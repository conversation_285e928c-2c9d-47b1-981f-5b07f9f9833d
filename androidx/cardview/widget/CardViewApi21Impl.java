package androidx.cardview.widget;

class CardViewApi21Impl {
    public static RoundRectDrawable a(CardViewDelegate cardViewDelegate0) {
        return (RoundRectDrawable)((androidx.cardview.widget.CardView.1)cardViewDelegate0).a;
    }

    public final void b(CardViewDelegate cardViewDelegate0, float f) {
        RoundRectDrawable roundRectDrawable0 = CardViewApi21Impl.a(cardViewDelegate0);
        boolean z = ((androidx.cardview.widget.CardView.1)cardViewDelegate0).b.getUseCompatPadding();
        CardView cardView0 = ((androidx.cardview.widget.CardView.1)cardViewDelegate0).b;
        boolean z1 = cardView0.getPreventCornerOverlap();
        if(f != roundRectDrawable0.e || roundRectDrawable0.f != z || roundRectDrawable0.g != z1) {
            roundRectDrawable0.e = f;
            roundRectDrawable0.f = z;
            roundRectDrawable0.g = z1;
            roundRectDrawable0.b(null);
            roundRectDrawable0.invalidateSelf();
        }
        if(!cardView0.getUseCompatPadding()) {
            ((androidx.cardview.widget.CardView.1)cardViewDelegate0).a(0, 0, 0, 0);
            return;
        }
        float f1 = CardViewApi21Impl.a(cardViewDelegate0).e;
        float f2 = CardViewApi21Impl.a(cardViewDelegate0).a;
        int v = (int)Math.ceil(RoundRectDrawableWithShadow.a(f1, f2, cardView0.getPreventCornerOverlap()));
        int v1 = (int)Math.ceil(RoundRectDrawableWithShadow.b(f1, f2, cardView0.getPreventCornerOverlap()));
        ((androidx.cardview.widget.CardView.1)cardViewDelegate0).a(v, v1, v, v1);
    }
}


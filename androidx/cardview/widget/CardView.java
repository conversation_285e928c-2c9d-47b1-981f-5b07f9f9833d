package androidx.cardview.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.FrameLayout;
import androidx.cardview.R.styleable;

public class CardView extends FrameLayout {
    public boolean a;
    public boolean b;
    public final Rect c;
    public final Rect d;
    public final androidx.cardview.widget.CardView.1 e;
    public static final int[] f;
    public static final CardViewApi21Impl g;

    static {
        CardView.f = new int[]{0x1010031};
        CardView.g = new CardViewApi21Impl();  // 初始化器: Ljava/lang/Object;-><init>()V
    }

    public CardView(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0, 0x7F0400F3);  // attr:cardViewStyle
        ColorStateList colorStateList0;
        Rect rect0 = new Rect();
        this.c = rect0;
        this.d = new Rect();
        androidx.cardview.widget.CardView.1 cardView$10 = new CardViewDelegate() {
            public Drawable a;
            public final CardView b;

            {
                this.b = cardView0;
            }

            public final void a(int v, int v1, int v2, int v3) {
                this.b.d.set(v, v1, v2, v3);
                this.b.super.setPadding(v + this.b.c.left, v1 + this.b.c.top, v2 + this.b.c.right, v3 + this.b.c.bottom);
            }
        };
        this.e = cardView$10;
        TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.a, 0x7F0400F3, 0x7F15012C);  // attr:cardViewStyle
        if(typedArray0.hasValue(2)) {
            colorStateList0 = typedArray0.getColorStateList(2);
        }
        else {
            TypedArray typedArray1 = this.getContext().obtainStyledAttributes(CardView.f);
            int v = typedArray1.getColor(0, 0);
            typedArray1.recycle();
            float[] arr_f = new float[3];
            Color.colorToHSV(v, arr_f);
            colorStateList0 = ColorStateList.valueOf((arr_f[2] > 0.5f ? this.getResources().getColor(0x7F060049) : this.getResources().getColor(0x7F060048)));  // color:cardview_light_background
        }
        float f = typedArray0.getDimension(3, 0.0f);
        float f1 = typedArray0.getDimension(4, 0.0f);
        float f2 = typedArray0.getDimension(5, 0.0f);
        this.a = typedArray0.getBoolean(7, false);
        this.b = typedArray0.getBoolean(6, true);
        int v1 = typedArray0.getDimensionPixelSize(8, 0);
        rect0.left = typedArray0.getDimensionPixelSize(10, v1);
        rect0.top = typedArray0.getDimensionPixelSize(12, v1);
        rect0.right = typedArray0.getDimensionPixelSize(11, v1);
        rect0.bottom = typedArray0.getDimensionPixelSize(9, v1);
        if(f1 > f2) {
            f2 = f1;
        }
        typedArray0.getDimensionPixelSize(0, 0);
        typedArray0.getDimensionPixelSize(1, 0);
        typedArray0.recycle();
        RoundRectDrawable roundRectDrawable0 = new RoundRectDrawable(f, colorStateList0);
        cardView$10.a = roundRectDrawable0;
        this.setBackgroundDrawable(roundRectDrawable0);
        this.setClipToOutline(true);
        this.setElevation(f1);
        CardView.g.b(cardView$10, f2);
    }

    public ColorStateList getCardBackgroundColor() {
        return ((RoundRectDrawable)this.e.a).h;
    }

    public float getCardElevation() {
        return this.e.b.getElevation();
    }

    public int getContentPaddingBottom() {
        return this.c.bottom;
    }

    public int getContentPaddingLeft() {
        return this.c.left;
    }

    public int getContentPaddingRight() {
        return this.c.right;
    }

    public int getContentPaddingTop() {
        return this.c.top;
    }

    public float getMaxCardElevation() {
        return ((RoundRectDrawable)this.e.a).e;
    }

    public boolean getPreventCornerOverlap() {
        return this.b;
    }

    public float getRadius() {
        return ((RoundRectDrawable)this.e.a).a;
    }

    public boolean getUseCompatPadding() {
        return this.a;
    }

    @Override  // android.widget.FrameLayout
    public final void onMeasure(int v, int v1) {
        super.onMeasure(v, v1);
    }

    public void setCardBackgroundColor(int v) {
        ColorStateList colorStateList0 = ColorStateList.valueOf(v);
        RoundRectDrawable roundRectDrawable0 = CardViewApi21Impl.a(this.e);
        if(colorStateList0 == null) {
            roundRectDrawable0.getClass();
            colorStateList0 = ColorStateList.valueOf(0);
        }
        roundRectDrawable0.h = colorStateList0;
        int v1 = colorStateList0.getColorForState(roundRectDrawable0.getState(), roundRectDrawable0.h.getDefaultColor());
        roundRectDrawable0.b.setColor(v1);
        roundRectDrawable0.invalidateSelf();
    }

    public void setCardBackgroundColor(ColorStateList colorStateList0) {
        RoundRectDrawable roundRectDrawable0 = CardViewApi21Impl.a(this.e);
        if(colorStateList0 == null) {
            roundRectDrawable0.getClass();
            colorStateList0 = ColorStateList.valueOf(0);
        }
        roundRectDrawable0.h = colorStateList0;
        int v = colorStateList0.getColorForState(roundRectDrawable0.getState(), roundRectDrawable0.h.getDefaultColor());
        roundRectDrawable0.b.setColor(v);
        roundRectDrawable0.invalidateSelf();
    }

    public void setCardElevation(float f) {
        this.e.b.setElevation(f);
    }

    public void setMaxCardElevation(float f) {
        CardView.g.b(this.e, f);
    }

    @Override  // android.view.View
    public void setMinimumHeight(int v) {
        super.setMinimumHeight(v);
    }

    @Override  // android.view.View
    public void setMinimumWidth(int v) {
        super.setMinimumWidth(v);
    }

    @Override  // android.view.View
    public final void setPadding(int v, int v1, int v2, int v3) {
    }

    @Override  // android.view.View
    public final void setPaddingRelative(int v, int v1, int v2, int v3) {
    }

    public void setPreventCornerOverlap(boolean z) {
        if(z != this.b) {
            this.b = z;
            CardView.g.b(this.e, ((RoundRectDrawable)this.e.a).e);
        }
    }

    public void setRadius(float f) {
        RoundRectDrawable roundRectDrawable0 = (RoundRectDrawable)this.e.a;
        if(f != roundRectDrawable0.a) {
            roundRectDrawable0.a = f;
            roundRectDrawable0.b(null);
            roundRectDrawable0.invalidateSelf();
        }
    }

    public void setUseCompatPadding(boolean z) {
        if(this.a != z) {
            this.a = z;
            CardView.g.b(this.e, ((RoundRectDrawable)this.e.a).e);
        }
    }
}


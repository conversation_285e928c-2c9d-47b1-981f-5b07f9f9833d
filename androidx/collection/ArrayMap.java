package androidx.collection;

import java.util.Collection;
import java.util.Map.Entry;
import java.util.Map;
import java.util.Set;

public class ArrayMap extends SimpleArrayMap implements Map {
    public ArrayMap.1 h;

    public ArrayMap(int v) {
        if(v == 0) {
            this.a = ContainerHelpers.a;
            this.b = ContainerHelpers.c;
        }
        else {
            this.b(v);
        }
        this.c = 0;
    }

    @Override
    public final Set entrySet() {
        if(this.h == null) {
            this.h = new ArrayMap.1(this);
        }
        ArrayMap.1 arrayMap$10 = this.h;
        if(arrayMap$10.a == null) {
            arrayMap$10.a = new EntrySet(arrayMap$10);
        }
        return arrayMap$10.a;
    }

    @Override
    public final Set keySet() {
        if(this.h == null) {
            this.h = new ArrayMap.1(this);
        }
        ArrayMap.1 arrayMap$10 = this.h;
        if(arrayMap$10.b == null) {
            arrayMap$10.b = new KeySet(arrayMap$10);
        }
        return arrayMap$10.b;
    }

    public final void n(Collection collection0) {
        MapCollections.k(this, collection0);
    }

    @Override
    public final void putAll(Map map0) {
        this.c(map0.size() + this.c);
        for(Object object0: map0.entrySet()) {
            this.put(((Map.Entry)object0).getKey(), ((Map.Entry)object0).getValue());
        }
    }

    @Override
    public final Collection values() {
        if(this.h == null) {
            this.h = new ArrayMap.1(this);
        }
        ArrayMap.1 arrayMap$10 = this.h;
        if(arrayMap$10.c == null) {
            arrayMap$10.c = new ValuesCollection(arrayMap$10);
        }
        return arrayMap$10.c;
    }
}


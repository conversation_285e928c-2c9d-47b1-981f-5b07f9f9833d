package androidx.collection;

import java.util.Map;

class ArrayMap.1 extends MapCollections {
    public final ArrayMap d;

    public ArrayMap.1(ArrayMap arrayMap0) {
        this.d = arrayMap0;
    }

    @Override  // androidx.collection.MapCollections
    public final void a() {
        this.d.clear();
    }

    @Override  // androidx.collection.MapCollections
    public final Object b(int v, int v1) {
        return this.d.b[(v << 1) + v1];
    }

    @Override  // androidx.collection.MapCollections
    public final Map c() {
        return this.d;
    }

    @Override  // androidx.collection.MapCollections
    public final int d() {
        return this.d.c;
    }

    @Override  // androidx.collection.MapCollections
    public final int e(Object object0) {
        return this.d.f(object0);
    }

    @Override  // androidx.collection.MapCollections
    public final int f(Object object0) {
        return this.d.h(object0);
    }

    @Override  // androidx.collection.MapCollections
    public final void g(Object object0, Object object1) {
        this.d.put(object0, object1);
    }

    @Override  // androidx.collection.MapCollections
    public final void h(int v) {
        this.d.k(v);
    }

    @Override  // androidx.collection.MapCollections
    public final Object i(int v, Object object0) {
        return this.d.l(v, object0);
    }
}


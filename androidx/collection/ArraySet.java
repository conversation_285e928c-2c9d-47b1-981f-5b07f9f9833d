package androidx.collection;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Iterator;
import java.util.Set;

public final class ArraySet implements Collection, Set {
    public int[] a;
    public Object[] b;
    public int c;
    public ArraySet.1 d;
    public static final int[] e;
    public static final Object[] f;
    public static Object[] g;
    public static int h;
    public static Object[] i;
    public static int j;

    static {
        ArraySet.e = new int[0];
        ArraySet.f = new Object[0];
    }

    public ArraySet(int v) {
        if(v == 0) {
            this.a = ArraySet.e;
            this.b = ArraySet.f;
        }
        else {
            this.a(v);
        }
        this.c = 0;
    }

    public final void a(int v) {
        switch(v) {
            case 4: {
                Class class0 = ArraySet.class;
                synchronized(class0) {
                    Object[] arr_object = ArraySet.g;
                    if(arr_object != null) {
                        this.b = arr_object;
                        ArraySet.g = (Object[])arr_object[0];
                        this.a = (int[])arr_object[1];
                        arr_object[1] = null;
                        arr_object[0] = null;
                        --ArraySet.h;
                        return;
                    }
                }
                break;
            }
            case 8: {
                Class class1 = ArraySet.class;
                synchronized(class1) {
                    Object[] arr_object1 = ArraySet.i;
                    if(arr_object1 != null) {
                        this.b = arr_object1;
                        ArraySet.i = (Object[])arr_object1[0];
                        this.a = (int[])arr_object1[1];
                        arr_object1[1] = null;
                        arr_object1[0] = null;
                        --ArraySet.j;
                        return;
                    }
                }
            }
        }
        this.a = new int[v];
        this.b = new Object[v];
    }

    @Override
    public final boolean add(Object object0) {
        int v1;
        int v;
        if(object0 == null) {
            v = this.f();
            v1 = 0;
        }
        else {
            int v2 = object0.hashCode();
            v1 = v2;
            v = this.e(object0, v2);
        }
        if(v >= 0) {
            return false;
        }
        int v3 = 8;
        int v4 = this.c;
        int[] arr_v = this.a;
        if(v4 >= arr_v.length) {
            if(v4 >= 8) {
                v3 = (v4 >> 1) + v4;
            }
            else if(v4 < 4) {
                v3 = 4;
            }
            Object[] arr_object = this.b;
            this.a(v3);
            int[] arr_v1 = this.a;
            if(arr_v1.length > 0) {
                System.arraycopy(arr_v, 0, arr_v1, 0, arr_v.length);
                System.arraycopy(arr_object, 0, this.b, 0, arr_object.length);
            }
            ArraySet.d(arr_v, arr_object, this.c);
        }
        int v5 = this.c;
        if(~v < v5) {
            System.arraycopy(this.a, ~v, this.a, -v, v5 - ~v);
            System.arraycopy(this.b, ~v, this.b, -v, this.c - ~v);
        }
        this.a[~v] = v1;
        this.b[~v] = object0;
        ++this.c;
        return true;
    }

    @Override
    public final boolean addAll(Collection collection0) {
        int v = collection0.size() + this.c;
        int[] arr_v = this.a;
        boolean z = false;
        if(arr_v.length < v) {
            Object[] arr_object = this.b;
            this.a(v);
            int v1 = this.c;
            if(v1 > 0) {
                System.arraycopy(arr_v, 0, this.a, 0, v1);
                System.arraycopy(arr_object, 0, this.b, 0, this.c);
            }
            ArraySet.d(arr_v, arr_object, this.c);
        }
        for(Object object0: collection0) {
            z |= this.add(object0);
        }
        return z;
    }

    @Override
    public final void clear() {
        int v = this.c;
        if(v != 0) {
            ArraySet.d(this.a, this.b, v);
            this.a = ArraySet.e;
            this.b = ArraySet.f;
            this.c = 0;
        }
    }

    @Override
    public final boolean contains(Object object0) {
        return this.indexOf(object0) >= 0;
    }

    @Override
    public final boolean containsAll(Collection collection0) {
        for(Object object0: collection0) {
            if(!this.contains(object0)) {
                return false;
            }
            if(false) {
                break;
            }
        }
        return true;
    }

    public static void d(int[] arr_v, Object[] arr_object, int v) {
        switch(arr_v.length) {
            case 4: {
                Class class0 = ArraySet.class;
                synchronized(class0) {
                    if(ArraySet.h < 10) {
                        arr_object[0] = ArraySet.g;
                        arr_object[1] = arr_v;
                        for(int v2 = v - 1; v2 >= 2; --v2) {
                            arr_object[v2] = null;
                        }
                        ArraySet.g = arr_object;
                        ++ArraySet.h;
                    }
                }
                return;
            }
            case 8: {
                Class class1 = ArraySet.class;
                synchronized(class1) {
                    if(ArraySet.j < 10) {
                        arr_object[0] = ArraySet.i;
                        arr_object[1] = arr_v;
                        for(int v4 = v - 1; v4 >= 2; --v4) {
                            arr_object[v4] = null;
                        }
                        ArraySet.i = arr_object;
                        ++ArraySet.j;
                    }
                }
            }
        }
    }

    public final int e(Object object0, int v) {
        int v1 = this.c;
        if(v1 == 0) {
            return -1;
        }
        int v2 = ContainerHelpers.a(this.a, v1, v);
        if(v2 < 0) {
            return v2;
        }
        if(object0.equals(this.b[v2])) {
            return v2;
        }
        int v3;
        for(v3 = v2 + 1; v3 < v1 && this.a[v3] == v; ++v3) {
            if(object0.equals(this.b[v3])) {
                return v3;
            }
        }
        for(int v4 = v2 - 1; v4 >= 0 && this.a[v4] == v; --v4) {
            if(object0.equals(this.b[v4])) {
                return v4;
            }
        }
        return ~v3;
    }

    @Override
    public final boolean equals(Object object0) {
        if(this == object0) {
            return true;
        }
        if(object0 instanceof Set) {
            Set set0 = (Set)object0;
            if(this.c != set0.size()) {
                return false;
            }
            try {
                for(int v = 0; true; ++v) {
                    if(v >= this.c) {
                        return true;
                    }
                    if(!set0.contains(this.b[v])) {
                        return false;
                    }
                }
            }
            catch(NullPointerException | ClassCastException unused_ex) {
            }
        }
        return false;
    }

    public final int f() {
        int v = this.c;
        if(v == 0) {
            return -1;
        }
        int v1 = ContainerHelpers.a(this.a, v, 0);
        if(v1 < 0) {
            return v1;
        }
        if(this.b[v1] == null) {
            return v1;
        }
        int v2;
        for(v2 = v1 + 1; v2 < v && this.a[v2] == 0; ++v2) {
            if(this.b[v2] == null) {
                return v2;
            }
        }
        for(int v3 = v1 - 1; v3 >= 0 && this.a[v3] == 0; --v3) {
            if(this.b[v3] == null) {
                return v3;
            }
        }
        return ~v2;
    }

    public final void g(int v) {
        Object[] arr_object = this.b;
        Object object0 = arr_object[v];
        int v1 = this.c;
        if(v1 <= 1) {
            ArraySet.d(this.a, arr_object, v1);
            this.a = ArraySet.e;
            this.b = ArraySet.f;
            this.c = 0;
            return;
        }
        int[] arr_v = this.a;
        int v2 = 8;
        if(arr_v.length <= 8 || v1 >= arr_v.length / 3) {
            this.c = v1 - 1;
            if(v < v1 - 1) {
                System.arraycopy(arr_v, v + 1, arr_v, v, v1 - 1 - v);
                System.arraycopy(this.b, v + 1, this.b, v, this.c - v);
            }
            this.b[this.c] = null;
        }
        else {
            if(v1 > 8) {
                v2 = v1 + (v1 >> 1);
            }
            this.a(v2);
            --this.c;
            if(v > 0) {
                System.arraycopy(arr_v, 0, this.a, 0, v);
                System.arraycopy(arr_object, 0, this.b, 0, v);
            }
            int v3 = this.c;
            if(v < v3) {
                System.arraycopy(arr_v, v + 1, this.a, v, v3 - v);
                System.arraycopy(arr_object, v + 1, this.b, v, this.c - v);
            }
        }
    }

    @Override
    public final int hashCode() {
        int[] arr_v = this.a;
        int v = this.c;
        int v2 = 0;
        for(int v1 = 0; v1 < v; ++v1) {
            v2 += arr_v[v1];
        }
        return v2;
    }

    public final int indexOf(Object object0) {
        return object0 == null ? this.f() : this.e(object0, object0.hashCode());
    }

    @Override
    public final boolean isEmpty() {
        return this.c <= 0;
    }

    @Override
    public final Iterator iterator() {
        if(this.d == null) {
            this.d = new ArraySet.1(this);
        }
        ArraySet.1 arraySet$10 = this.d;
        if(arraySet$10.b == null) {
            arraySet$10.b = new KeySet(arraySet$10);
        }
        return arraySet$10.b.iterator();
    }

    @Override
    public final boolean remove(Object object0) {
        int v = this.indexOf(object0);
        if(v >= 0) {
            this.g(v);
            return true;
        }
        return false;
    }

    @Override
    public final boolean removeAll(Collection collection0) {
        boolean z = false;
        for(Object object0: collection0) {
            z |= this.remove(object0);
        }
        return z;
    }

    @Override
    public final boolean retainAll(Collection collection0) {
        int v = this.c - 1;
        boolean z = false;
        while(v >= 0) {
            if(!collection0.contains(this.b[v])) {
                this.g(v);
                z = true;
            }
            --v;
        }
        return z;
    }

    @Override
    public final int size() {
        return this.c;
    }

    @Override
    public final Object[] toArray() {
        int v = this.c;
        Object[] arr_object = new Object[v];
        System.arraycopy(this.b, 0, arr_object, 0, v);
        return arr_object;
    }

    @Override
    public final Object[] toArray(Object[] arr_object) {
        if(arr_object.length < this.c) {
            arr_object = (Object[])Array.newInstance(arr_object.getClass().getComponentType(), this.c);
        }
        System.arraycopy(this.b, 0, arr_object, 0, this.c);
        int v = this.c;
        if(arr_object.length > v) {
            arr_object[v] = null;
        }
        return arr_object;
    }

    @Override
    public final String toString() {
        if(this.isEmpty()) {
            return "{}";
        }
        StringBuilder stringBuilder0 = new StringBuilder(this.c * 14);
        stringBuilder0.append('{');
        for(int v = 0; v < this.c; ++v) {
            if(v > 0) {
                stringBuilder0.append(", ");
            }
            Object object0 = this.b[v];
            if(object0 == this) {
                stringBuilder0.append("(this Set)");
            }
            else {
                stringBuilder0.append(object0);
            }
        }
        stringBuilder0.append('}');
        return stringBuilder0.toString();
    }
}


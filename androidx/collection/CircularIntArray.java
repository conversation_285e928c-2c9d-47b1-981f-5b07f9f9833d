package androidx.collection;

public final class CircularIntArray {
    public int[] a;
    public int b;
    public int c;
    public int d;

    public CircularIntArray() {
        this.d = 7;
        this.a = new int[8];
    }

    public final void a(int v) {
        int[] arr_v = this.a;
        int v1 = this.c;
        arr_v[v1] = v;
        int v2 = this.d & v1 + 1;
        this.c = v2;
        int v3 = this.b;
        if(v2 == v3) {
            int v4 = arr_v.length - v3;
            int v5 = arr_v.length << 1;
            if(v5 < 0) {
                throw new RuntimeException("Max array capacity exceeded");
            }
            int[] arr_v1 = new int[v5];
            System.arraycopy(arr_v, v3, arr_v1, 0, v4);
            System.arraycopy(this.a, 0, arr_v1, v4, this.b);
            this.a = arr_v1;
            this.b = 0;
            this.c = arr_v.length;
            this.d = v5 - 1;
        }
    }
}


package androidx.collection;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map.Entry;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;

abstract class MapCollections {
    final class ArrayIterator implements Iterator {
        public final int a;
        public int b;
        public int c;
        public boolean d;
        public final MapCollections e;

        public ArrayIterator(int v) {
            this.d = false;
            this.a = v;
            this.b = mapCollections0.d();
        }

        @Override
        public final boolean hasNext() {
            return this.c < this.b;
        }

        @Override
        public final Object next() {
            if(!this.hasNext()) {
                throw new NoSuchElementException();
            }
            Object object0 = this.e.b(this.c, this.a);
            ++this.c;
            this.d = true;
            return object0;
        }

        @Override
        public final void remove() {
            if(!this.d) {
                throw new IllegalStateException();
            }
            int v = this.c - 1;
            this.c = v;
            --this.b;
            this.d = false;
            this.e.h(v);
        }
    }

    final class EntrySet implements Set {
        public final MapCollections a;

        @Override
        public final boolean add(Object object0) {
            Map.Entry map$Entry0 = (Map.Entry)object0;
            throw new UnsupportedOperationException();
        }

        @Override
        public final boolean addAll(Collection collection0) {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.d();
            for(Object object0: collection0) {
                mapCollections0.g(((Map.Entry)object0).getKey(), ((Map.Entry)object0).getValue());
            }
            return v != mapCollections0.d();
        }

        @Override
        public final void clear() {
            this.a.a();
        }

        @Override
        public final boolean contains(Object object0) {
            if(!(object0 instanceof Map.Entry)) {
                return false;
            }
            Object object1 = ((Map.Entry)object0).getKey();
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.e(object1);
            if(v < 0) {
                return false;
            }
            Object object2 = mapCollections0.b(v, 1);
            Object object3 = ((Map.Entry)object0).getValue();
            return object2 == object3 || object2 != null && object2.equals(object3);
        }

        @Override
        public final boolean containsAll(Collection collection0) {
            for(Object object0: collection0) {
                if(!this.contains(object0)) {
                    return false;
                }
                if(false) {
                    break;
                }
            }
            return true;
        }

        @Override
        public final boolean equals(Object object0) {
            return MapCollections.j(this, object0);
        }

        @Override
        public final int hashCode() {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.d() - 1;
            int v1 = 0;
            while(v >= 0) {
                Object object0 = mapCollections0.b(v, 0);
                Object object1 = mapCollections0.b(v, 1);
                v1 += (object0 == null ? 0 : object0.hashCode()) ^ (object1 == null ? 0 : object1.hashCode());
                --v;
            }
            return v1;
        }

        @Override
        public final boolean isEmpty() {
            return this.a.d() == 0;
        }

        @Override
        public final Iterator iterator() {
            return new MapIterator(this.a);
        }

        @Override
        public final boolean remove(Object object0) {
            throw new UnsupportedOperationException();
        }

        @Override
        public final boolean removeAll(Collection collection0) {
            throw new UnsupportedOperationException();
        }

        @Override
        public final boolean retainAll(Collection collection0) {
            throw new UnsupportedOperationException();
        }

        @Override
        public final int size() {
            return this.a.d();
        }

        @Override
        public final Object[] toArray() {
            throw new UnsupportedOperationException();
        }

        @Override
        public final Object[] toArray(Object[] arr_object) {
            throw new UnsupportedOperationException();
        }
    }

    final class KeySet implements Set {
        public final MapCollections a;

        @Override
        public final boolean add(Object object0) {
            throw new UnsupportedOperationException();
        }

        @Override
        public final boolean addAll(Collection collection0) {
            throw new UnsupportedOperationException();
        }

        @Override
        public final void clear() {
            this.a.a();
        }

        @Override
        public final boolean contains(Object object0) {
            return this.a.e(object0) >= 0;
        }

        @Override
        public final boolean containsAll(Collection collection0) {
            Map map0 = this.a.c();
            for(Object object0: collection0) {
                if(!map0.containsKey(object0)) {
                    return false;
                }
                if(false) {
                    break;
                }
            }
            return true;
        }

        @Override
        public final boolean equals(Object object0) {
            return MapCollections.j(this, object0);
        }

        @Override
        public final int hashCode() {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.d() - 1;
            int v1 = 0;
            while(v >= 0) {
                Object object0 = mapCollections0.b(v, 0);
                v1 += (object0 == null ? 0 : object0.hashCode());
                --v;
            }
            return v1;
        }

        @Override
        public final boolean isEmpty() {
            return this.a.d() == 0;
        }

        @Override
        public final Iterator iterator() {
            return new ArrayIterator(this.a, 0);
        }

        @Override
        public final boolean remove(Object object0) {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.e(object0);
            if(v >= 0) {
                mapCollections0.h(v);
                return true;
            }
            return false;
        }

        @Override
        public final boolean removeAll(Collection collection0) {
            Map map0 = this.a.c();
            int v = map0.size();
            for(Object object0: collection0) {
                map0.remove(object0);
            }
            return v != map0.size();
        }

        @Override
        public final boolean retainAll(Collection collection0) {
            return MapCollections.k(this.a.c(), collection0);
        }

        @Override
        public final int size() {
            return this.a.d();
        }

        @Override
        public final Object[] toArray() {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.d();
            Object[] arr_object = new Object[v];
            for(int v1 = 0; v1 < v; ++v1) {
                arr_object[v1] = mapCollections0.b(v1, 0);
            }
            return arr_object;
        }

        @Override
        public final Object[] toArray(Object[] arr_object) {
            return this.a.l(arr_object, 0);
        }
    }

    final class MapIterator implements Iterator, Map.Entry {
        public int a;
        public int b;
        public boolean c;
        public final MapCollections d;

        public MapIterator() {
            this.c = false;
            this.a = mapCollections0.d() - 1;
            this.b = -1;
        }

        @Override
        public final boolean equals(Object object0) {
            if(!this.c) {
                throw new IllegalStateException("This container does not support retaining Map.Entry objects");
            }
            if(!(object0 instanceof Map.Entry)) {
                return false;
            }
            Object object1 = ((Map.Entry)object0).getKey();
            MapCollections mapCollections0 = this.d;
            Object object2 = mapCollections0.b(this.b, 0);
            if(object1 == object2 || object1 != null && object1.equals(object2)) {
                Object object3 = ((Map.Entry)object0).getValue();
                Object object4 = mapCollections0.b(this.b, 1);
                return object3 == object4 || object3 != null && object3.equals(object4);
            }
            return false;
        }

        @Override
        public final Object getKey() {
            if(!this.c) {
                throw new IllegalStateException("This container does not support retaining Map.Entry objects");
            }
            return this.d.b(this.b, 0);
        }

        @Override
        public final Object getValue() {
            if(!this.c) {
                throw new IllegalStateException("This container does not support retaining Map.Entry objects");
            }
            return this.d.b(this.b, 1);
        }

        @Override
        public final boolean hasNext() {
            return this.b < this.a;
        }

        @Override
        public final int hashCode() {
            if(!this.c) {
                throw new IllegalStateException("This container does not support retaining Map.Entry objects");
            }
            int v = 0;
            Object object0 = this.d.b(this.b, 0);
            Object object1 = this.d.b(this.b, 1);
            int v1 = object0 == null ? 0 : object0.hashCode();
            if(object1 != null) {
                v = object1.hashCode();
            }
            return v1 ^ v;
        }

        @Override
        public final Object next() {
            if(!this.hasNext()) {
                throw new NoSuchElementException();
            }
            ++this.b;
            this.c = true;
            return this;
        }

        @Override
        public final void remove() {
            if(!this.c) {
                throw new IllegalStateException();
            }
            this.d.h(this.b);
            --this.b;
            --this.a;
            this.c = false;
        }

        @Override
        public final Object setValue(Object object0) {
            if(!this.c) {
                throw new IllegalStateException("This container does not support retaining Map.Entry objects");
            }
            return this.d.i(this.b, object0);
        }

        @Override
        public final String toString() {
            return this.getKey() + "=" + this.getValue();
        }
    }

    final class ValuesCollection implements Collection {
        public final MapCollections a;

        @Override
        public final boolean add(Object object0) {
            throw new UnsupportedOperationException();
        }

        @Override
        public final boolean addAll(Collection collection0) {
            throw new UnsupportedOperationException();
        }

        @Override
        public final void clear() {
            this.a.a();
        }

        @Override
        public final boolean contains(Object object0) {
            return this.a.f(object0) >= 0;
        }

        @Override
        public final boolean containsAll(Collection collection0) {
            for(Object object0: collection0) {
                if(!this.contains(object0)) {
                    return false;
                }
                if(false) {
                    break;
                }
            }
            return true;
        }

        @Override
        public final boolean isEmpty() {
            return this.a.d() == 0;
        }

        @Override
        public final Iterator iterator() {
            return new ArrayIterator(this.a, 1);
        }

        @Override
        public final boolean remove(Object object0) {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.f(object0);
            if(v >= 0) {
                mapCollections0.h(v);
                return true;
            }
            return false;
        }

        @Override
        public final boolean removeAll(Collection collection0) {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.d();
            boolean z = false;
            for(int v1 = 0; v1 < v; ++v1) {
                if(collection0.contains(mapCollections0.b(v1, 1))) {
                    mapCollections0.h(v1);
                    --v1;
                    --v;
                    z = true;
                }
            }
            return z;
        }

        @Override
        public final boolean retainAll(Collection collection0) {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.d();
            boolean z = false;
            for(int v1 = 0; v1 < v; ++v1) {
                if(!collection0.contains(mapCollections0.b(v1, 1))) {
                    mapCollections0.h(v1);
                    --v1;
                    --v;
                    z = true;
                }
            }
            return z;
        }

        @Override
        public final int size() {
            return this.a.d();
        }

        @Override
        public final Object[] toArray() {
            MapCollections mapCollections0 = this.a;
            int v = mapCollections0.d();
            Object[] arr_object = new Object[v];
            for(int v1 = 0; v1 < v; ++v1) {
                arr_object[v1] = mapCollections0.b(v1, 1);
            }
            return arr_object;
        }

        @Override
        public final Object[] toArray(Object[] arr_object) {
            return this.a.l(arr_object, 1);
        }
    }

    public EntrySet a;
    public KeySet b;
    public ValuesCollection c;

    public abstract void a();

    public abstract Object b(int arg1, int arg2);

    public abstract Map c();

    public abstract int d();

    public abstract int e(Object arg1);

    public abstract int f(Object arg1);

    public abstract void g(Object arg1, Object arg2);

    public abstract void h(int arg1);

    public abstract Object i(int arg1, Object arg2);

    public static boolean j(Set set0, Object object0) {
        if(set0 == object0) {
            return true;
        }
        if(object0 instanceof Set) {
            Set set1 = (Set)object0;
            try {
                return set0.size() != set1.size() || !set0.containsAll(set1) ? false : true;
            }
            catch(NullPointerException | ClassCastException unused_ex) {
            }
        }
        return false;
    }

    public static boolean k(Map map0, Collection collection0) {
        int v = map0.size();
        Iterator iterator0 = map0.keySet().iterator();
        while(iterator0.hasNext()) {
            Object object0 = iterator0.next();
            if(!collection0.contains(object0)) {
                iterator0.remove();
            }
        }
        return v != map0.size();
    }

    public final Object[] l(Object[] arr_object, int v) {
        int v1 = this.d();
        if(arr_object.length < v1) {
            arr_object = (Object[])Array.newInstance(arr_object.getClass().getComponentType(), v1);
        }
        for(int v2 = 0; v2 < v1; ++v2) {
            arr_object[v2] = this.b(v2, v);
        }
        if(arr_object.length > v1) {
            arr_object[v1] = null;
        }
        return arr_object;
    }
}


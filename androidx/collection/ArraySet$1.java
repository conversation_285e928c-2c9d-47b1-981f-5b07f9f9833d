package androidx.collection;

import java.util.Map;

class ArraySet.1 extends MapCollections {
    public final ArraySet d;

    public ArraySet.1(ArraySet arraySet0) {
        this.d = arraySet0;
    }

    @Override  // androidx.collection.MapCollections
    public final void a() {
        this.d.clear();
    }

    @Override  // androidx.collection.MapCollections
    public final Object b(int v, int v1) {
        return this.d.b[v];
    }

    @Override  // androidx.collection.MapCollections
    public final Map c() {
        throw new UnsupportedOperationException("not a map");
    }

    @Override  // androidx.collection.MapCollections
    public final int d() {
        return this.d.c;
    }

    @Override  // androidx.collection.MapCollections
    public final int e(Object object0) {
        return this.d.indexOf(object0);
    }

    @Override  // androidx.collection.MapCollections
    public final int f(Object object0) {
        return this.d.indexOf(object0);
    }

    @Override  // androidx.collection.MapCollections
    public final void g(Object object0, Object object1) {
        this.d.add(object0);
    }

    @Override  // androidx.collection.MapCollections
    public final void h(int v) {
        this.d.g(v);
    }

    @Override  // androidx.collection.MapCollections
    public final Object i(int v, Object object0) {
        throw new UnsupportedOperationException("not a map");
    }
}


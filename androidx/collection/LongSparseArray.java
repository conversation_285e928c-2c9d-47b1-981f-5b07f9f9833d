package androidx.collection;

public class LongSparseArray implements Cloneable {
    public boolean a;
    public long[] b;
    public Object[] c;
    public int d;
    public static final Object e;

    static {
        LongSparseArray.e = new Object();
    }

    public LongSparseArray() {
        this(10);
    }

    public LongSparseArray(int v) {
        this.a = false;
        if(v == 0) {
            this.b = ContainerHelpers.b;
            this.c = ContainerHelpers.c;
            return;
        }
        int v1 = v * 8;
        for(int v2 = 4; v2 < 0x20; ++v2) {
            int v3 = (1 << v2) - 12;
            if(v1 <= v3) {
                v1 = v3;
                break;
            }
        }
        this.b = new long[v1 / 8];
        this.c = new Object[v1 / 8];
    }

    public final void a(long v, Long long0) {
        int v1 = this.d;
        if(v1 != 0 && v <= this.b[v1 - 1]) {
            this.h(v, long0);
            return;
        }
        if(this.a && v1 >= this.b.length) {
            this.d();
        }
        int v2 = this.d;
        if(v2 >= this.b.length) {
            int v3 = (v2 + 1) * 8;
            for(int v4 = 4; v4 < 0x20; ++v4) {
                int v5 = (1 << v4) - 12;
                if(v3 <= v5) {
                    v3 = v5;
                    break;
                }
            }
            long[] arr_v = new long[v3 / 8];
            Object[] arr_object = new Object[v3 / 8];
            System.arraycopy(this.b, 0, arr_v, 0, this.b.length);
            System.arraycopy(this.c, 0, arr_object, 0, this.c.length);
            this.b = arr_v;
            this.c = arr_object;
        }
        this.b[v2] = v;
        this.c[v2] = long0;
        this.d = v2 + 1;
    }

    public final void b() {
        int v = this.d;
        Object[] arr_object = this.c;
        for(int v1 = 0; v1 < v; ++v1) {
            arr_object[v1] = null;
        }
        this.d = 0;
        this.a = false;
    }

    public final LongSparseArray c() {
        try {
            LongSparseArray longSparseArray0 = (LongSparseArray)super.clone();
            longSparseArray0.b = (long[])this.b.clone();
            longSparseArray0.c = (Object[])this.c.clone();
            return longSparseArray0;
        }
        catch(CloneNotSupportedException cloneNotSupportedException0) {
            throw new AssertionError(cloneNotSupportedException0);
        }
    }

    @Override
    public final Object clone() {
        return this.c();
    }

    public final void d() {
        int v = this.d;
        long[] arr_v = this.b;
        Object[] arr_object = this.c;
        int v2 = 0;
        for(int v1 = 0; v1 < v; ++v1) {
            Object object0 = arr_object[v1];
            if(object0 != LongSparseArray.e) {
                if(v1 != v2) {
                    arr_v[v2] = arr_v[v1];
                    arr_object[v2] = object0;
                    arr_object[v1] = null;
                }
                ++v2;
            }
        }
        this.a = false;
        this.d = v2;
    }

    public final Object e(long v, Long long0) {
        int v1 = ContainerHelpers.b(this.b, this.d, v);
        if(v1 >= 0) {
            Object object0 = this.c[v1];
            if(object0 != LongSparseArray.e) {
                return object0;
            }
        }
        return long0;
    }

    public final int f(long v) {
        if(this.a) {
            this.d();
        }
        return ContainerHelpers.b(this.b, this.d, v);
    }

    public final long g(int v) {
        if(this.a) {
            this.d();
        }
        return this.b[v];
    }

    public final void h(long v, Object object0) {
        int v1 = ContainerHelpers.b(this.b, this.d, v);
        if(v1 >= 0) {
            this.c[v1] = object0;
            return;
        }
        int v2 = ~v1;
        int v3 = this.d;
        if(v2 < v3) {
            Object[] arr_object = this.c;
            if(arr_object[v2] == LongSparseArray.e) {
                this.b[v2] = v;
                arr_object[v2] = object0;
                return;
            }
        }
        if(this.a && v3 >= this.b.length) {
            this.d();
            v2 = ~ContainerHelpers.b(this.b, this.d, v);
        }
        int v4 = this.d;
        if(v4 >= this.b.length) {
            int v5 = (v4 + 1) * 8;
            for(int v6 = 4; v6 < 0x20; ++v6) {
                int v7 = (1 << v6) - 12;
                if(v5 <= v7) {
                    v5 = v7;
                    break;
                }
            }
            long[] arr_v = new long[v5 / 8];
            Object[] arr_object1 = new Object[v5 / 8];
            System.arraycopy(this.b, 0, arr_v, 0, this.b.length);
            System.arraycopy(this.c, 0, arr_object1, 0, this.c.length);
            this.b = arr_v;
            this.c = arr_object1;
        }
        int v8 = this.d - v2;
        if(v8 != 0) {
            System.arraycopy(this.b, v2, this.b, v2 + 1, v8);
            System.arraycopy(this.c, v2, this.c, v2 + 1, this.d - v2);
        }
        this.b[v2] = v;
        this.c[v2] = object0;
        ++this.d;
    }

    public final void i(long v) {
        int v1 = ContainerHelpers.b(this.b, this.d, v);
        if(v1 >= 0) {
            Object[] arr_object = this.c;
            Object object0 = LongSparseArray.e;
            if(arr_object[v1] != object0) {
                arr_object[v1] = object0;
                this.a = true;
            }
        }
    }

    public final int j() {
        if(this.a) {
            this.d();
        }
        return this.d;
    }

    public final Object k(int v) {
        if(this.a) {
            this.d();
        }
        return this.c[v];
    }

    @Override
    public final String toString() {
        if(this.j() <= 0) {
            return "{}";
        }
        StringBuilder stringBuilder0 = new StringBuilder(this.d * 28);
        stringBuilder0.append('{');
        for(int v = 0; v < this.d; ++v) {
            if(v > 0) {
                stringBuilder0.append(", ");
            }
            stringBuilder0.append(this.g(v));
            stringBuilder0.append('=');
            Object object0 = this.k(v);
            if(object0 == this) {
                stringBuilder0.append("(this Map)");
            }
            else {
                stringBuilder0.append(object0);
            }
        }
        stringBuilder0.append('}');
        return stringBuilder0.toString();
    }
}


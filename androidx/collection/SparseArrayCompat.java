package androidx.collection;

public class SparseArrayCompat implements Cloneable {
    public boolean a;
    public int[] b;
    public Object[] c;
    public int d;
    public static final Object e;

    static {
        SparseArrayCompat.e = new Object();
    }

    public SparseArrayCompat() {
        int v1;
        this.a = false;
        for(int v = 4; true; ++v) {
            v1 = 40;
            if(v >= 0x20) {
                break;
            }
            int v2 = (1 << v) - 12;
            if(40 <= v2) {
                v1 = v2;
                break;
            }
        }
        this.b = new int[v1 / 4];
        this.c = new Object[v1 / 4];
    }

    public final void a(int v, Object object0) {
        int v1 = this.d;
        if(v1 != 0 && v <= this.b[v1 - 1]) {
            this.e(v, object0);
            return;
        }
        if(this.a && v1 >= this.b.length) {
            this.c();
        }
        int v2 = this.d;
        if(v2 >= this.b.length) {
            int v3 = (v2 + 1) * 4;
            for(int v4 = 4; v4 < 0x20; ++v4) {
                int v5 = (1 << v4) - 12;
                if(v3 <= v5) {
                    v3 = v5;
                    break;
                }
            }
            int[] arr_v = new int[v3 / 4];
            Object[] arr_object = new Object[v3 / 4];
            System.arraycopy(this.b, 0, arr_v, 0, this.b.length);
            System.arraycopy(this.c, 0, arr_object, 0, this.c.length);
            this.b = arr_v;
            this.c = arr_object;
        }
        this.b[v2] = v;
        this.c[v2] = object0;
        this.d = v2 + 1;
    }

    public final SparseArrayCompat b() {
        try {
            SparseArrayCompat sparseArrayCompat0 = (SparseArrayCompat)super.clone();
            sparseArrayCompat0.b = (int[])this.b.clone();
            sparseArrayCompat0.c = (Object[])this.c.clone();
            return sparseArrayCompat0;
        }
        catch(CloneNotSupportedException cloneNotSupportedException0) {
            throw new AssertionError(cloneNotSupportedException0);
        }
    }

    public final void c() {
        int v = this.d;
        int[] arr_v = this.b;
        Object[] arr_object = this.c;
        int v2 = 0;
        for(int v1 = 0; v1 < v; ++v1) {
            Object object0 = arr_object[v1];
            if(object0 != SparseArrayCompat.e) {
                if(v1 != v2) {
                    arr_v[v2] = arr_v[v1];
                    arr_object[v2] = object0;
                    arr_object[v1] = null;
                }
                ++v2;
            }
        }
        this.a = false;
        this.d = v2;
    }

    @Override
    public final Object clone() {
        return this.b();
    }

    public final Object d(int v, Integer integer0) {
        int v1 = ContainerHelpers.a(this.b, this.d, v);
        if(v1 >= 0) {
            Object object0 = this.c[v1];
            if(object0 != SparseArrayCompat.e) {
                return object0;
            }
        }
        return integer0;
    }

    public final void e(int v, Object object0) {
        int v1 = ContainerHelpers.a(this.b, this.d, v);
        if(v1 >= 0) {
            this.c[v1] = object0;
            return;
        }
        int v2 = ~v1;
        int v3 = this.d;
        if(v2 < v3) {
            Object[] arr_object = this.c;
            if(arr_object[v2] == SparseArrayCompat.e) {
                this.b[v2] = v;
                arr_object[v2] = object0;
                return;
            }
        }
        if(this.a && v3 >= this.b.length) {
            this.c();
            v2 = ~ContainerHelpers.a(this.b, this.d, v);
        }
        int v4 = this.d;
        if(v4 >= this.b.length) {
            int v5 = (v4 + 1) * 4;
            for(int v6 = 4; v6 < 0x20; ++v6) {
                int v7 = (1 << v6) - 12;
                if(v5 <= v7) {
                    v5 = v7;
                    break;
                }
            }
            int[] arr_v = new int[v5 / 4];
            Object[] arr_object1 = new Object[v5 / 4];
            System.arraycopy(this.b, 0, arr_v, 0, this.b.length);
            System.arraycopy(this.c, 0, arr_object1, 0, this.c.length);
            this.b = arr_v;
            this.c = arr_object1;
        }
        int v8 = this.d - v2;
        if(v8 != 0) {
            System.arraycopy(this.b, v2, this.b, v2 + 1, v8);
            System.arraycopy(this.c, v2, this.c, v2 + 1, this.d - v2);
        }
        this.b[v2] = v;
        this.c[v2] = object0;
        ++this.d;
    }

    public final int f() {
        if(this.a) {
            this.c();
        }
        return this.d;
    }

    public final Object g(int v) {
        if(this.a) {
            this.c();
        }
        return this.c[v];
    }

    @Override
    public final String toString() {
        if(this.f() <= 0) {
            return "{}";
        }
        StringBuilder stringBuilder0 = new StringBuilder(this.d * 28);
        stringBuilder0.append('{');
        for(int v = 0; v < this.d; ++v) {
            if(v > 0) {
                stringBuilder0.append(", ");
            }
            if(this.a) {
                this.c();
            }
            stringBuilder0.append(this.b[v]);
            stringBuilder0.append('=');
            Object object0 = this.g(v);
            if(object0 == this) {
                stringBuilder0.append("(this Map)");
            }
            else {
                stringBuilder0.append(object0);
            }
        }
        stringBuilder0.append('}');
        return stringBuilder0.toString();
    }
}


package androidx.activity;

import android.window.OnBackInvokedDispatcher;
import androidx.lifecycle.Lifecycle.Event;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import kotlin.jvm.internal.Intrinsics;

public final class d implements LifecycleEventObserver {
    public final OnBackPressedDispatcher a;
    public final ComponentActivity b;

    public d(ComponentActivity componentActivity0, OnBackPressedDispatcher onBackPressedDispatcher0) {
        this.a = onBackPressedDispatcher0;
        this.b = componentActivity0;
    }

    @Override  // androidx.lifecycle.LifecycleEventObserver
    public final void onStateChanged(LifecycleOwner lifecycleOwner0, Event lifecycle$Event0) {
        OnBackPressedDispatcher onBackPressedDispatcher0 = this.a;
        Intrinsics.f(onBackPressedDispatcher0, "$dispatcher");
        ComponentActivity componentActivity0 = this.b;
        Intrinsics.f(componentActivity0, "this$0");
        if(lifecycle$Event0 == Event.ON_CREATE) {
            OnBackInvokedDispatcher onBackInvokedDispatcher0 = Api33Impl.a.a(componentActivity0);
            Intrinsics.f(onBackInvokedDispatcher0, "invoker");
            onBackPressedDispatcher0.e = onBackInvokedDispatcher0;
            onBackPressedDispatcher0.d(onBackPressedDispatcher0.g);
        }
    }
}


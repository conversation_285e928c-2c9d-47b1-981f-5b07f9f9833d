package androidx.activity;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Lambda;

final class OnBackPressedDispatcher.5 extends Lambda implements Function0 {
    public final OnBackPressedDispatcher a;

    public OnBackPressedDispatcher.5(OnBackPressedDispatcher onBackPressedDispatcher0) {
        this.a = onBackPressedDispatcher0;
        super(0);
    }

    @Override  // kotlin.jvm.functions.Function0
    public final Object invoke() {
        this.a.c();
        return Unit.a;
    }
}


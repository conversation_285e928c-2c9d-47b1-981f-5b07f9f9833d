package androidx.activity;

import android.os.Build.VERSION;
import android.os.Handler;
import android.os.Looper;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Lambda;

final class ComponentActivity.onBackPressedDispatcher.2 extends Lambda implements Function0 {
    public final ComponentActivity a;

    public ComponentActivity.onBackPressedDispatcher.2(ComponentActivity componentActivity0) {
        this.a = componentActivity0;
        super(0);
    }

    @Override  // kotlin.jvm.functions.Function0
    public final Object invoke() {
        ComponentActivity componentActivity0 = this.a;
        OnBackPressedDispatcher onBackPressedDispatcher0 = new OnBackPressedDispatcher(new g(componentActivity0, 0));
        if(Build.VERSION.SDK_INT >= 33) {
            if(!Intrinsics.a(Looper.myLooper(), Looper.getMainLooper())) {
                new Handler(Looper.getMainLooper()).post(new h(componentActivity0, onBackPressedDispatcher0));
                return onBackPressedDispatcher0;
            }
            ComponentActivity.access$addObserverForBackInvoker(componentActivity0, onBackPressedDispatcher0);
        }
        return onBackPressedDispatcher0;
    }
}


package androidx.activity;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Lambda;

final class ComponentActivity.fullyDrawnReporter.2.1 extends Lambda implements Function0 {
    public final ComponentActivity a;

    public ComponentActivity.fullyDrawnReporter.2.1(ComponentActivity componentActivity0) {
        this.a = componentActivity0;
        super(0);
    }

    @Override  // kotlin.jvm.functions.Function0
    public final Object invoke() {
        this.a.reportFullyDrawn();
        return Unit.a;
    }
}


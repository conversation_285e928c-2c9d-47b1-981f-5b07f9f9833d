package androidx.activity;

import java.util.concurrent.CopyOnWriteArrayList;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Intrinsics;

public abstract class OnBackPressedCallback {
    private final CopyOnWriteArrayList cancellables;
    private Function0 enabledChangedCallback;
    private boolean isEnabled;

    public OnBackPressedCallback(boolean z) {
        this.isEnabled = z;
        this.cancellables = new CopyOnWriteArrayList();
    }

    public final void addCancellable(Cancellable cancellable0) {
        Intrinsics.f(cancellable0, "cancellable");
        this.cancellables.add(cancellable0);
    }

    public final Function0 getEnabledChangedCallback$activity_release() {
        return this.enabledChangedCallback;
    }

    public void handleOnBackCancelled() {
    }

    public abstract void handleOnBackPressed();

    public void handleOnBackProgressed(BackEventCompat backEventCompat0) {
        Intrinsics.f(backEventCompat0, "backEvent");
    }

    public void handleOnBackStarted(BackEventCompat backEventCompat0) {
        Intrinsics.f(backEventCompat0, "backEvent");
    }

    public final boolean isEnabled() {
        return this.isEnabled;
    }

    public final void remove() {
        for(Object object0: this.cancellables) {
            ((Cancellable)object0).cancel();
        }
    }

    public final void removeCancellable(Cancellable cancellable0) {
        Intrinsics.f(cancellable0, "cancellable");
        this.cancellables.remove(cancellable0);
    }

    public final void setEnabled(boolean z) {
        this.isEnabled = z;
        Function0 function00 = this.enabledChangedCallback;
        if(function00 != null) {
            function00.invoke();
        }
    }

    public final void setEnabledChangedCallback$activity_release(Function0 function00) {
        this.enabledChangedCallback = function00;
    }
}


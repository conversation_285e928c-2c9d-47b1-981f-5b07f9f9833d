package androidx.activity;

import android.os.Build.VERSION;
import android.window.BackEvent;
import android.window.OnBackAnimationCallback;
import android.window.OnBackInvokedCallback;
import android.window.OnBackInvokedDispatcher;
import androidx.lifecycle.Lifecycle.Event;
import androidx.lifecycle.Lifecycle.State;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import java.util.Collection;
import java.util.ListIterator;
import kotlin.collections.ArrayDeque;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;

public final class OnBackPressedDispatcher {
    public static final class Api33Impl {
        public static final Api33Impl a;

        static {
            Api33Impl.a = new Api33Impl();  // 初始化器: Ljava/lang/Object;-><init>()V
        }

        public final OnBackInvokedCallback a(Function0 function00) {
            Intrinsics.f(function00, "onBackInvoked");
            return new j(function00);
        }

        public final void b(Object object0, int v, Object object1) {
            Intrinsics.f(object0, "dispatcher");
            Intrinsics.f(object1, "callback");
            ((OnBackInvokedDispatcher)object0).registerOnBackInvokedCallback(v, ((OnBackInvokedCallback)object1));
        }

        public final void c(Object object0, Object object1) {
            Intrinsics.f(object0, "dispatcher");
            Intrinsics.f(object1, "callback");
            ((OnBackInvokedDispatcher)object0).unregisterOnBackInvokedCallback(((OnBackInvokedCallback)object1));
        }
    }

    public static final class Api34Impl {
        public static final Api34Impl a;

        static {
            Api34Impl.a = new Api34Impl();  // 初始化器: Ljava/lang/Object;-><init>()V
        }

        public final OnBackInvokedCallback a(Function1 function10, Function1 function11, Function0 function00, Function0 function01) {
            Intrinsics.f(function10, "onBackStarted");
            Intrinsics.f(function11, "onBackProgressed");
            Intrinsics.f(function00, "onBackInvoked");
            Intrinsics.f(function01, "onBackCancelled");
            return new OnBackAnimationCallback() {
                public final Function1 a;
                public final Function1 b;
                public final Function0 c;
                public final Function0 d;

                {
                    Function1 function10 = function11;  // 捕获的参数 （可能与外部方法变量命名冲突；考虑手动重命名）
                    Function1 function11 = function00;  // 捕获的参数 （可能与外部方法变量命名冲突；考虑手动重命名）
                    Function0 function00 = function01;  // 捕获的参数 （可能与外部方法变量命名冲突；考虑手动重命名）
                    this.a = function10;
                    this.b = function11;
                    this.c = function00;
                    this.d = function01;
                }

                @Override  // android.window.OnBackAnimationCallback
                public final void onBackCancelled() {
                    this.d.invoke();
                }

                @Override  // android.window.OnBackInvokedCallback
                public final void onBackInvoked() {
                    this.c.invoke();
                }

                @Override  // android.window.OnBackAnimationCallback
                public final void onBackProgressed(BackEvent backEvent0) {
                    Intrinsics.f(backEvent0, "backEvent");
                    BackEventCompat backEventCompat0 = new BackEventCompat(backEvent0);
                    this.b.invoke(backEventCompat0);
                }

                @Override  // android.window.OnBackAnimationCallback
                public final void onBackStarted(BackEvent backEvent0) {
                    Intrinsics.f(backEvent0, "backEvent");
                    BackEventCompat backEventCompat0 = new BackEventCompat(backEvent0);
                    this.a.invoke(backEventCompat0);
                }
            };
        }
    }

    final class LifecycleOnBackPressedCancellable implements Cancellable, LifecycleEventObserver {
        public final Lifecycle a;
        public final OnBackPressedCallback b;
        public Cancellable c;
        public final OnBackPressedDispatcher d;

        public LifecycleOnBackPressedCancellable(Lifecycle lifecycle0, OnBackPressedCallback onBackPressedCallback0) {
            Intrinsics.f(onBackPressedCallback0, "onBackPressedCallback");
            this.d = onBackPressedDispatcher0;
            this.a = lifecycle0;
            this.b = onBackPressedCallback0;
            lifecycle0.a(this);
        }

        @Override  // androidx.activity.Cancellable
        public final void cancel() {
            this.a.b(this);
            this.b.removeCancellable(this);
            Cancellable cancellable0 = this.c;
            if(cancellable0 != null) {
                ((OnBackPressedCancellable)cancellable0).cancel();
            }
            this.c = null;
        }

        @Override  // androidx.lifecycle.LifecycleEventObserver
        public final void onStateChanged(LifecycleOwner lifecycleOwner0, Event lifecycle$Event0) {
            if(lifecycle$Event0 == Event.ON_START) {
                this.d.getClass();
                Intrinsics.f(this.b, "onBackPressedCallback");
                this.d.b.addLast(this.b);
                OnBackPressedCancellable onBackPressedDispatcher$OnBackPressedCancellable0 = new OnBackPressedCancellable(this.d, this.b);
                this.b.addCancellable(onBackPressedDispatcher$OnBackPressedCancellable0);
                this.d.e();
                OnBackPressedDispatcher.addCancellableCallback.1 onBackPressedDispatcher$addCancellableCallback$10 = new OnBackPressedDispatcher.addCancellableCallback.1(0, this.d, OnBackPressedDispatcher.class, "updateEnabledCallbacks", "updateEnabledCallbacks()V", 0);  // 初始化器: Lkotlin/jvm/internal/FunctionReference;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
                this.b.setEnabledChangedCallback$activity_release(onBackPressedDispatcher$addCancellableCallback$10);
                this.c = onBackPressedDispatcher$OnBackPressedCancellable0;
                return;
            }
            if(lifecycle$Event0 == Event.ON_STOP) {
                Cancellable cancellable0 = this.c;
                if(cancellable0 != null) {
                    ((OnBackPressedCancellable)cancellable0).cancel();
                }
            }
            else if(lifecycle$Event0 == Event.ON_DESTROY) {
                this.cancel();
            }
        }
    }

    final class OnBackPressedCancellable implements Cancellable {
        public final OnBackPressedCallback a;
        public final OnBackPressedDispatcher b;

        public OnBackPressedCancellable(OnBackPressedCallback onBackPressedCallback0) {
            this.a = onBackPressedCallback0;
        }

        @Override  // androidx.activity.Cancellable
        public final void cancel() {
            OnBackPressedDispatcher onBackPressedDispatcher0 = this.b;
            OnBackPressedCallback onBackPressedCallback0 = this.a;
            onBackPressedDispatcher0.b.remove(onBackPressedCallback0);
            if(Intrinsics.a(onBackPressedDispatcher0.c, onBackPressedCallback0)) {
                onBackPressedDispatcher0.c = null;
            }
            onBackPressedCallback0.removeCancellable(this);
            Function0 function00 = onBackPressedCallback0.getEnabledChangedCallback$activity_release();
            if(function00 != null) {
                function00.invoke();
            }
            onBackPressedCallback0.setEnabledChangedCallback$activity_release(null);
        }
    }

    public final Runnable a;
    public final ArrayDeque b;
    public OnBackPressedCallback c;
    public final OnBackInvokedCallback d;
    public OnBackInvokedDispatcher e;
    public boolean f;
    public boolean g;

    public OnBackPressedDispatcher(Runnable runnable0) {
        OnBackInvokedCallback onBackInvokedCallback0;
        this.a = runnable0;
        this.b = new ArrayDeque();
        int v = Build.VERSION.SDK_INT;
        if(v >= 33) {
            if(v >= 34) {
                OnBackPressedDispatcher.1 onBackPressedDispatcher$10 = new OnBackPressedDispatcher.1(this);
                OnBackPressedDispatcher.2 onBackPressedDispatcher$20 = new OnBackPressedDispatcher.2(this);
                OnBackPressedDispatcher.3 onBackPressedDispatcher$30 = new OnBackPressedDispatcher.3(this);
                OnBackPressedDispatcher.4 onBackPressedDispatcher$40 = new OnBackPressedDispatcher.4(this);
                onBackInvokedCallback0 = Api34Impl.a.a(onBackPressedDispatcher$10, onBackPressedDispatcher$20, onBackPressedDispatcher$30, onBackPressedDispatcher$40);
            }
            else {
                OnBackPressedDispatcher.5 onBackPressedDispatcher$50 = new OnBackPressedDispatcher.5(this);
                onBackInvokedCallback0 = Api33Impl.a.a(onBackPressedDispatcher$50);
            }
            this.d = onBackInvokedCallback0;
        }
    }

    public final void a(LifecycleOwner lifecycleOwner0, OnBackPressedCallback onBackPressedCallback0) {
        Intrinsics.f(lifecycleOwner0, "owner");
        Intrinsics.f(onBackPressedCallback0, "onBackPressedCallback");
        Lifecycle lifecycle0 = lifecycleOwner0.getLifecycle();
        if(((LifecycleRegistry)lifecycle0).c == State.a) {
            return;
        }
        onBackPressedCallback0.addCancellable(new LifecycleOnBackPressedCancellable(this, lifecycle0, onBackPressedCallback0));
        this.e();
        onBackPressedCallback0.setEnabledChangedCallback$activity_release(new OnBackPressedDispatcher.addCallback.1(0, this, OnBackPressedDispatcher.class, "updateEnabledCallbacks", "updateEnabledCallbacks()V", 0));  // 初始化器: Lkotlin/jvm/internal/FunctionReference;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
    }

    public final void b() {
        if(this.c == null) {
            Object object0 = null;
            ListIterator listIterator0 = this.b.listIterator(this.b.size());
            while(listIterator0.hasPrevious()) {
                Object object1 = listIterator0.previous();
                if(((OnBackPressedCallback)object1).isEnabled()) {
                    object0 = object1;
                    break;
                }
            }
            OnBackPressedCallback onBackPressedCallback0 = (OnBackPressedCallback)object0;
        }
        this.c = null;
    }

    public final void c() {
        OnBackPressedCallback onBackPressedCallback0 = this.c;
        if(onBackPressedCallback0 == null) {
            Object object0 = null;
            ListIterator listIterator0 = this.b.listIterator(this.b.size());
            while(listIterator0.hasPrevious()) {
                Object object1 = listIterator0.previous();
                if(((OnBackPressedCallback)object1).isEnabled()) {
                    object0 = object1;
                    break;
                }
            }
            onBackPressedCallback0 = (OnBackPressedCallback)object0;
        }
        this.c = null;
        if(onBackPressedCallback0 != null) {
            onBackPressedCallback0.handleOnBackPressed();
            return;
        }
        Runnable runnable0 = this.a;
        if(runnable0 != null) {
            runnable0.run();
        }
    }

    public final void d(boolean z) {
        OnBackInvokedDispatcher onBackInvokedDispatcher0 = this.e;
        if(onBackInvokedDispatcher0 != null) {
            OnBackInvokedCallback onBackInvokedCallback0 = this.d;
            if(onBackInvokedCallback0 != null) {
                Api33Impl onBackPressedDispatcher$Api33Impl0 = Api33Impl.a;
                if(z && !this.f) {
                    onBackPressedDispatcher$Api33Impl0.b(onBackInvokedDispatcher0, 0, onBackInvokedCallback0);
                    this.f = true;
                    return;
                }
                if(!z && this.f) {
                    onBackPressedDispatcher$Api33Impl0.c(onBackInvokedDispatcher0, onBackInvokedCallback0);
                    this.f = false;
                }
            }
        }
    }

    public final void e() {
        boolean z = this.g;
        ArrayDeque arrayDeque0 = this.b;
        boolean z1 = false;
        if(!(arrayDeque0 instanceof Collection) || !arrayDeque0.isEmpty()) {
            for(Object object0: arrayDeque0) {
                if(((OnBackPressedCallback)object0).isEnabled()) {
                    z1 = true;
                    break;
                }
                if(false) {
                    break;
                }
            }
        }
        this.g = z1;
        if(z1 != z && Build.VERSION.SDK_INT >= 33) {
            this.d(z1);
        }
    }
}


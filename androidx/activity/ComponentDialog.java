package androidx.activity;

import android.app.Dialog;
import android.content.Context;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.Window;
import android.window.OnBackInvokedDispatcher;
import androidx.lifecycle.Lifecycle.Event;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.savedstate.SavedStateRegistry;
import androidx.savedstate.SavedStateRegistryController.Companion;
import androidx.savedstate.SavedStateRegistryController;
import androidx.savedstate.SavedStateRegistryOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;
import kotlin.jvm.internal.Intrinsics;

public class ComponentDialog extends Dialog implements OnBackPressedDispatcherOwner, LifecycleOwner, SavedStateRegistryOwner {
    public LifecycleRegistry a;
    public final SavedStateRegistryController b;
    public final OnBackPressedDispatcher c;

    public ComponentDialog(Context context0, int v) {
        Intrinsics.f(context0, "context");
        super(context0, v);
        this.b = Companion.a(this);
        this.c = new OnBackPressedDispatcher(new e(this, 1));
    }

    public static void a(ComponentDialog componentDialog0) {
        Intrinsics.f(componentDialog0, "this$0");
        componentDialog0.super.onBackPressed();
    }

    @Override  // android.app.Dialog
    public void addContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        Intrinsics.f(view0, "view");
        this.c();
        super.addContentView(view0, viewGroup$LayoutParams0);
    }

    public final LifecycleRegistry b() {
        LifecycleRegistry lifecycleRegistry0 = this.a;
        if(lifecycleRegistry0 == null) {
            lifecycleRegistry0 = new LifecycleRegistry(this);
            this.a = lifecycleRegistry0;
        }
        return lifecycleRegistry0;
    }

    public final void c() {
        Window window0 = this.getWindow();
        Intrinsics.c(window0);
        View view0 = window0.getDecorView();
        Intrinsics.e(view0, "window!!.decorView");
        ViewTreeLifecycleOwner.a(view0, this);
        Window window1 = this.getWindow();
        Intrinsics.c(window1);
        View view1 = window1.getDecorView();
        Intrinsics.e(view1, "window!!.decorView");
        ViewTreeOnBackPressedDispatcherOwner.a(view1, this);
        Window window2 = this.getWindow();
        Intrinsics.c(window2);
        View view2 = window2.getDecorView();
        Intrinsics.e(view2, "window!!.decorView");
        ViewTreeSavedStateRegistryOwner.a(view2, this);
    }

    @Override  // androidx.lifecycle.LifecycleOwner
    public final Lifecycle getLifecycle() {
        return this.b();
    }

    @Override  // androidx.activity.OnBackPressedDispatcherOwner
    public final OnBackPressedDispatcher getOnBackPressedDispatcher() {
        return this.c;
    }

    @Override  // androidx.savedstate.SavedStateRegistryOwner
    public final SavedStateRegistry getSavedStateRegistry() {
        return this.b.b;
    }

    @Override  // android.app.Dialog
    public final void onBackPressed() {
        this.c.c();
    }

    @Override  // android.app.Dialog
    public void onCreate(Bundle bundle0) {
        super.onCreate(bundle0);
        if(Build.VERSION.SDK_INT >= 33) {
            OnBackInvokedDispatcher onBackInvokedDispatcher0 = this.getOnBackInvokedDispatcher();
            Intrinsics.e(onBackInvokedDispatcher0, "onBackInvokedDispatcher");
            this.c.getClass();
            this.c.e = onBackInvokedDispatcher0;
            this.c.d(this.c.g);
        }
        this.b.b(bundle0);
        this.b().e(Event.ON_CREATE);
    }

    @Override  // android.app.Dialog
    public final Bundle onSaveInstanceState() {
        Bundle bundle0 = super.onSaveInstanceState();
        Intrinsics.e(bundle0, "super.onSaveInstanceState()");
        this.b.c(bundle0);
        return bundle0;
    }

    @Override  // android.app.Dialog
    public final void onStart() {
        super.onStart();
        this.b().e(Event.ON_RESUME);
    }

    @Override  // android.app.Dialog
    public void onStop() {
        this.b().e(Event.ON_DESTROY);
        this.a = null;
        super.onStop();
    }

    @Override  // android.app.Dialog
    public void setContentView(int v) {
        this.c();
        super.setContentView(v);
    }

    @Override  // android.app.Dialog
    public void setContentView(View view0) {
        Intrinsics.f(view0, "view");
        this.c();
        super.setContentView(view0);
    }

    @Override  // android.app.Dialog
    public void setContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        Intrinsics.f(view0, "view");
        this.c();
        super.setContentView(view0, viewGroup$LayoutParams0);
    }
}


package androidx.activity;

import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Lambda;

final class ComponentActivity.fullyDrawnReporter.2 extends Lambda implements Function0 {
    public final ComponentActivity a;

    public ComponentActivity.fullyDrawnReporter.2(ComponentActivity componentActivity0) {
        this.a = componentActivity0;
        super(0);
    }

    @Override  // kotlin.jvm.functions.Function0
    public final Object invoke() {
        return new FullyDrawnReporter(this.a.reportFullyDrawnExecutor, new ComponentActivity.fullyDrawnReporter.2.1(this.a));
    }
}


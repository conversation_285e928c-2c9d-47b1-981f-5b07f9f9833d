package androidx.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender.SendIntentException;
import android.content.IntentSender;
import android.content.res.Configuration;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Looper;
import android.os.SystemClock;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewTreeObserver.OnDrawListener;
import android.window.OnBackInvokedDispatcher;
import androidx.activity.contextaware.ContextAwareHelper;
import androidx.activity.contextaware.OnContextAvailableListener;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultCaller;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.ActivityResultRegistry;
import androidx.activity.result.ActivityResultRegistryOwner;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.core.app.MultiWindowModeChangedInfo;
import androidx.core.app.OnMultiWindowModeChangedProvider;
import androidx.core.app.OnPictureInPictureModeChangedProvider;
import androidx.core.app.PictureInPictureModeChangedInfo;
import androidx.core.content.OnConfigurationChangedProvider;
import androidx.core.content.OnTrimMemoryProvider;
import androidx.core.util.Consumer;
import androidx.core.view.MenuHost;
import androidx.core.view.MenuHostHelper;
import androidx.core.view.MenuProvider;
import androidx.lifecycle.HasDefaultViewModelProviderFactory;
import androidx.lifecycle.Lifecycle.Event;
import androidx.lifecycle.Lifecycle.State;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import androidx.lifecycle.SavedStateHandleSupport;
import androidx.lifecycle.ViewModelProvider.AndroidViewModelFactory;
import androidx.lifecycle.ViewModelProvider.Factory;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.lifecycle.viewmodel.CreationExtras;
import androidx.lifecycle.viewmodel.MutableCreationExtras;
import androidx.savedstate.SavedStateRegistry;
import androidx.savedstate.SavedStateRegistryController;
import androidx.savedstate.SavedStateRegistryOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;
import androidx.tracing.Trace;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import kotlin.Lazy;
import kotlin.LazyKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.TypeIntrinsics;

public abstract class ComponentActivity extends androidx.core.app.ComponentActivity implements OnBackPressedDispatcherOwner, ActivityResultCaller, ActivityResultRegistryOwner, OnMultiWindowModeChangedProvider, OnPictureInPictureModeChangedProvider, OnConfigurationChangedProvider, OnTrimMemoryProvider, MenuHost, HasDefaultViewModelProviderFactory, LifecycleOwner, ViewModelStoreOwner, SavedStateRegistryOwner {
    static final class Api33Impl {
        public static final Api33Impl a;

        static {
            Api33Impl.a = new Api33Impl();  // 初始化器: Ljava/lang/Object;-><init>()V
        }

        public final OnBackInvokedDispatcher a(Activity activity0) {
            Intrinsics.f(activity0, "activity");
            OnBackInvokedDispatcher onBackInvokedDispatcher0 = activity0.getOnBackInvokedDispatcher();
            Intrinsics.e(onBackInvokedDispatcher0, "activity.getOnBackInvokedDispatcher()");
            return onBackInvokedDispatcher0;
        }
    }

    static final class Companion {
    }

    public static final class NonConfigurationInstances {
        public Object a;
        public ViewModelStore b;

    }

    interface ReportFullyDrawnExecutor extends Executor {
    }

    final class ReportFullyDrawnExecutorImpl implements ViewTreeObserver.OnDrawListener, ReportFullyDrawnExecutor, Runnable {
        public final long a;
        public Runnable b;
        public boolean c;
        public final ComponentActivity d;

        public ReportFullyDrawnExecutorImpl() {
            this.a = SystemClock.uptimeMillis() + 10000L;
        }

        public final void a(View view0) {
            if(!this.c) {
                this.c = true;
                view0.getViewTreeObserver().addOnDrawListener(this);
            }
        }

        @Override
        public final void execute(Runnable runnable0) {
            Intrinsics.f(runnable0, "runnable");
            this.b = runnable0;
            View view0 = this.d.getWindow().getDecorView();
            Intrinsics.e(view0, "window.decorView");
            if(this.c) {
                if(Intrinsics.a(Looper.myLooper(), Looper.getMainLooper())) {
                    view0.invalidate();
                    return;
                }
                view0.postInvalidate();
                return;
            }
            view0.postOnAnimation(new e(this, 0));
        }

        @Override  // android.view.ViewTreeObserver$OnDrawListener
        public final void onDraw() {
            Runnable runnable0 = this.b;
            if(runnable0 != null) {
                runnable0.run();
                this.b = null;
                if(this.d.getFullyDrawnReporter().c) {
                    this.c = false;
                    this.d.getWindow().getDecorView().post(this);
                }
            }
            else if(SystemClock.uptimeMillis() > this.a) {
                this.c = false;
                this.d.getWindow().getDecorView().post(this);
            }
        }

        @Override
        public final void run() {
            this.d.getWindow().getDecorView().getViewTreeObserver().removeOnDrawListener(this);
        }
    }

    private static final String ACTIVITY_RESULT_TAG = "android:support:activity-result";
    private static final Companion Companion;
    public static final int P;
    private ViewModelStore _viewModelStore;
    private final ActivityResultRegistry activityResultRegistry;
    private int contentLayoutId;
    private final ContextAwareHelper contextAwareHelper;
    private final Lazy defaultViewModelProviderFactory$delegate;
    private boolean dispatchingOnMultiWindowModeChanged;
    private boolean dispatchingOnPictureInPictureModeChanged;
    private final Lazy fullyDrawnReporter$delegate;
    private final MenuHostHelper menuHostHelper;
    private final AtomicInteger nextLocalRequestCode;
    private final Lazy onBackPressedDispatcher$delegate;
    private final CopyOnWriteArrayList onConfigurationChangedListeners;
    private final CopyOnWriteArrayList onMultiWindowModeChangedListeners;
    private final CopyOnWriteArrayList onNewIntentListeners;
    private final CopyOnWriteArrayList onPictureInPictureModeChangedListeners;
    private final CopyOnWriteArrayList onTrimMemoryListeners;
    private final CopyOnWriteArrayList onUserLeaveHintListeners;
    private final ReportFullyDrawnExecutor reportFullyDrawnExecutor;
    private final SavedStateRegistryController savedStateRegistryController;

    static {
        ComponentActivity.Companion = new Companion();  // 初始化器: Ljava/lang/Object;-><init>()V
    }

    public ComponentActivity() {
        this.contextAwareHelper = new ContextAwareHelper();
        this.menuHostHelper = new MenuHostHelper(new g(this, 1));
        SavedStateRegistryController savedStateRegistryController0 = androidx.savedstate.SavedStateRegistryController.Companion.a(this);
        this.savedStateRegistryController = savedStateRegistryController0;
        this.reportFullyDrawnExecutor = new ReportFullyDrawnExecutorImpl(this);
        this.fullyDrawnReporter$delegate = LazyKt.a(new ComponentActivity.fullyDrawnReporter.2(this));
        this.nextLocalRequestCode = new AtomicInteger();
        this.activityResultRegistry = new ComponentActivity.activityResultRegistry.1(this);
        this.onConfigurationChangedListeners = new CopyOnWriteArrayList();
        this.onTrimMemoryListeners = new CopyOnWriteArrayList();
        this.onNewIntentListeners = new CopyOnWriteArrayList();
        this.onMultiWindowModeChangedListeners = new CopyOnWriteArrayList();
        this.onPictureInPictureModeChangedListeners = new CopyOnWriteArrayList();
        this.onUserLeaveHintListeners = new CopyOnWriteArrayList();
        if(this.getLifecycle() == null) {
            throw new IllegalStateException("getLifecycle() returned null in ComponentActivity\'s constructor. Please make sure you are lazily constructing your Lifecycle in the first call to getLifecycle() rather than relying on field initialization.");
        }
        this.getLifecycle().a(new a(this, 0));
        this.getLifecycle().a(new a(this, 1));
        this.getLifecycle().a(new ComponentActivity.4(this));
        savedStateRegistryController0.a();
        SavedStateHandleSupport.b(this);
        if(Build.VERSION.SDK_INT <= 23) {
            this.getLifecycle().a(new ImmLeaksCleaner(this));
        }
        this.getSavedStateRegistry().c("android:support:activity-result", () -> {
            Intrinsics.f(this, "this$0");
            Bundle bundle0 = new Bundle();
            this.activityResultRegistry.getClass();
            bundle0.putIntegerArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_RCS", new ArrayList(this.activityResultRegistry.b.values()));
            bundle0.putStringArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS", new ArrayList(this.activityResultRegistry.b.keySet()));
            bundle0.putStringArrayList("KEY_COMPONENT_ACTIVITY_LAUNCHED_KEYS", new ArrayList(this.activityResultRegistry.d));
            bundle0.putBundle("KEY_COMPONENT_ACTIVITY_PENDING_RESULT", new Bundle(this.activityResultRegistry.g));
            return bundle0;
        });
        this.addOnContextAvailableListener((Context context0) -> {
            Intrinsics.f(this, "this$0");
            Intrinsics.f(context0, "it");
            Bundle bundle0 = this.getSavedStateRegistry().a("android:support:activity-result");
            if(bundle0 != null) {
                ActivityResultRegistry activityResultRegistry0 = this.activityResultRegistry;
                activityResultRegistry0.getClass();
                ArrayList arrayList0 = bundle0.getIntegerArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_RCS");
                ArrayList arrayList1 = bundle0.getStringArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS");
                if(arrayList1 != null && arrayList0 != null) {
                    ArrayList arrayList2 = bundle0.getStringArrayList("KEY_COMPONENT_ACTIVITY_LAUNCHED_KEYS");
                    if(arrayList2 != null) {
                        activityResultRegistry0.d.addAll(arrayList2);
                    }
                    Bundle bundle1 = bundle0.getBundle("KEY_COMPONENT_ACTIVITY_PENDING_RESULT");
                    Bundle bundle2 = activityResultRegistry0.g;
                    if(bundle1 != null) {
                        bundle2.putAll(bundle1);
                    }
                    int v = arrayList1.size();
                    for(int v1 = 0; v1 < v; ++v1) {
                        String s = (String)arrayList1.get(v1);
                        LinkedHashMap linkedHashMap0 = activityResultRegistry0.b;
                        boolean z = linkedHashMap0.containsKey(s);
                        LinkedHashMap linkedHashMap1 = activityResultRegistry0.a;
                        if(z) {
                            Integer integer0 = (Integer)linkedHashMap0.remove(s);
                            if(!bundle2.containsKey(s)) {
                                TypeIntrinsics.a(linkedHashMap1);
                                linkedHashMap1.remove(integer0);
                            }
                        }
                        Object object0 = arrayList0.get(v1);
                        Intrinsics.e(object0, "rcs[i]");
                        int v2 = ((Number)object0).intValue();
                        Object object1 = arrayList1.get(v1);
                        Intrinsics.e(object1, "keys[i]");
                        linkedHashMap1.put(v2, ((String)object1));
                        linkedHashMap0.put(((String)object1), v2);
                    }
                }
            }
        });
        this.defaultViewModelProviderFactory$delegate = LazyKt.a(new ComponentActivity.defaultViewModelProviderFactory.2(this));
        this.onBackPressedDispatcher$delegate = LazyKt.a(new ComponentActivity.onBackPressedDispatcher.2(this));
    }

    public ComponentActivity(int v) {
        this.contentLayoutId = v;
    }

    // 检测为 Lambda 实现
    public static void a(ComponentActivity componentActivity0, Context context0) [...]

    public static final void access$addObserverForBackInvoker(ComponentActivity componentActivity0, OnBackPressedDispatcher onBackPressedDispatcher0) {
        componentActivity0.getLifecycle().a(new d(componentActivity0, onBackPressedDispatcher0));
    }

    public static final void access$ensureViewModelStore(ComponentActivity componentActivity0) {
        if(componentActivity0._viewModelStore == null) {
            NonConfigurationInstances componentActivity$NonConfigurationInstances0 = (NonConfigurationInstances)componentActivity0.getLastNonConfigurationInstance();
            if(componentActivity$NonConfigurationInstances0 != null) {
                componentActivity0._viewModelStore = componentActivity$NonConfigurationInstances0.b;
            }
            if(componentActivity0._viewModelStore == null) {
                componentActivity0._viewModelStore = new ViewModelStore();
            }
        }
    }

    @Override  // android.app.Activity
    public void addContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        this.initializeViewTreeOwners();
        View view1 = this.getWindow().getDecorView();
        Intrinsics.e(view1, "window.decorView");
        ((ReportFullyDrawnExecutorImpl)this.reportFullyDrawnExecutor).a(view1);
        super.addContentView(view0, viewGroup$LayoutParams0);
    }

    @Override  // androidx.core.view.MenuHost
    public void addMenuProvider(MenuProvider menuProvider0) {
        Intrinsics.f(menuProvider0, "provider");
        this.menuHostHelper.b.add(menuProvider0);
        this.menuHostHelper.a.run();
    }

    public void addMenuProvider(MenuProvider menuProvider0, LifecycleOwner lifecycleOwner0) {
        Intrinsics.f(menuProvider0, "provider");
        Intrinsics.f(lifecycleOwner0, "owner");
        this.menuHostHelper.a(menuProvider0, lifecycleOwner0);
    }

    @SuppressLint({"LambdaLast"})
    public void addMenuProvider(MenuProvider menuProvider0, LifecycleOwner lifecycleOwner0, State lifecycle$State0) {
        Intrinsics.f(menuProvider0, "provider");
        Intrinsics.f(lifecycleOwner0, "owner");
        Intrinsics.f(lifecycle$State0, "state");
        this.menuHostHelper.b(menuProvider0, lifecycleOwner0, lifecycle$State0);
    }

    @Override  // androidx.core.content.OnConfigurationChangedProvider
    public final void addOnConfigurationChangedListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onConfigurationChangedListeners.add(consumer0);
    }

    public final void addOnContextAvailableListener(OnContextAvailableListener onContextAvailableListener0) {
        Intrinsics.f(onContextAvailableListener0, "listener");
        ContextAwareHelper contextAwareHelper0 = this.contextAwareHelper;
        contextAwareHelper0.getClass();
        Context context0 = contextAwareHelper0.b;
        if(context0 != null) {
            onContextAvailableListener0.onContextAvailable(context0);
        }
        contextAwareHelper0.a.add(onContextAvailableListener0);
    }

    @Override  // androidx.core.app.OnMultiWindowModeChangedProvider
    public final void addOnMultiWindowModeChangedListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onMultiWindowModeChangedListeners.add(consumer0);
    }

    public final void addOnNewIntentListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onNewIntentListeners.add(consumer0);
    }

    @Override  // androidx.core.app.OnPictureInPictureModeChangedProvider
    public final void addOnPictureInPictureModeChangedListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onPictureInPictureModeChangedListeners.add(consumer0);
    }

    @Override  // androidx.core.content.OnTrimMemoryProvider
    public final void addOnTrimMemoryListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onTrimMemoryListeners.add(consumer0);
    }

    public final void addOnUserLeaveHintListener(Runnable runnable0) {
        Intrinsics.f(runnable0, "listener");
        this.onUserLeaveHintListeners.add(runnable0);
    }

    public static void b(ComponentActivity componentActivity0, LifecycleOwner lifecycleOwner0, Event lifecycle$Event0) {
        Intrinsics.f(componentActivity0, "this$0");
        if(lifecycle$Event0 == Event.ON_DESTROY) {
            componentActivity0.contextAwareHelper.b = null;
            if(!componentActivity0.isChangingConfigurations()) {
                componentActivity0.getViewModelStore().a();
            }
            ((ReportFullyDrawnExecutorImpl)componentActivity0.reportFullyDrawnExecutor).d.getWindow().getDecorView().removeCallbacks(((ReportFullyDrawnExecutorImpl)componentActivity0.reportFullyDrawnExecutor));
            ((ReportFullyDrawnExecutorImpl)componentActivity0.reportFullyDrawnExecutor).d.getWindow().getDecorView().getViewTreeObserver().removeOnDrawListener(((ReportFullyDrawnExecutorImpl)componentActivity0.reportFullyDrawnExecutor));
        }
    }

    // 检测为 Lambda 实现
    public static Bundle c(ComponentActivity componentActivity0) [...]

    @Override  // androidx.activity.result.ActivityResultRegistryOwner
    public final ActivityResultRegistry getActivityResultRegistry() {
        return this.activityResultRegistry;
    }

    @Override  // androidx.lifecycle.HasDefaultViewModelProviderFactory
    public CreationExtras getDefaultViewModelCreationExtras() {
        CreationExtras creationExtras0 = new MutableCreationExtras(0);
        Application application0 = this.getApplication();
        LinkedHashMap linkedHashMap0 = creationExtras0.a;
        if(application0 != null) {
            Application application1 = this.getApplication();
            Intrinsics.e(application1, "application");
            linkedHashMap0.put(AndroidViewModelFactory.d, application1);
        }
        linkedHashMap0.put(SavedStateHandleSupport.a, this);
        linkedHashMap0.put(SavedStateHandleSupport.b, this);
        Intent intent0 = this.getIntent();
        Bundle bundle0 = intent0 == null ? null : intent0.getExtras();
        if(bundle0 != null) {
            linkedHashMap0.put(SavedStateHandleSupport.c, bundle0);
        }
        return creationExtras0;
    }

    @Override  // androidx.lifecycle.HasDefaultViewModelProviderFactory
    public Factory getDefaultViewModelProviderFactory() {
        return (Factory)this.defaultViewModelProviderFactory$delegate.getValue();
    }

    public FullyDrawnReporter getFullyDrawnReporter() {
        return (FullyDrawnReporter)this.fullyDrawnReporter$delegate.getValue();
    }

    public Object getLastCustomNonConfigurationInstance() {
        NonConfigurationInstances componentActivity$NonConfigurationInstances0 = (NonConfigurationInstances)this.getLastNonConfigurationInstance();
        return componentActivity$NonConfigurationInstances0 == null ? null : componentActivity$NonConfigurationInstances0.a;
    }

    @Override  // androidx.core.app.ComponentActivity, androidx.lifecycle.LifecycleOwner
    public Lifecycle getLifecycle() {
        return super.getLifecycle();
    }

    @Override  // androidx.activity.OnBackPressedDispatcherOwner
    public final OnBackPressedDispatcher getOnBackPressedDispatcher() {
        return (OnBackPressedDispatcher)this.onBackPressedDispatcher$delegate.getValue();
    }

    public static void getOnBackPressedDispatcher$annotations() {
    }

    @Override  // androidx.savedstate.SavedStateRegistryOwner
    public final SavedStateRegistry getSavedStateRegistry() {
        return this.savedStateRegistryController.b;
    }

    @Override  // androidx.lifecycle.ViewModelStoreOwner
    public ViewModelStore getViewModelStore() {
        if(this.getApplication() == null) {
            throw new IllegalStateException("Your activity is not yet attached to the Application instance. You can\'t request ViewModel before onCreate call.");
        }
        if(this._viewModelStore == null) {
            NonConfigurationInstances componentActivity$NonConfigurationInstances0 = (NonConfigurationInstances)this.getLastNonConfigurationInstance();
            if(componentActivity$NonConfigurationInstances0 != null) {
                this._viewModelStore = componentActivity$NonConfigurationInstances0.b;
            }
            if(this._viewModelStore == null) {
                this._viewModelStore = new ViewModelStore();
            }
        }
        ViewModelStore viewModelStore0 = this._viewModelStore;
        Intrinsics.c(viewModelStore0);
        return viewModelStore0;
    }

    public void initializeViewTreeOwners() {
        View view0 = this.getWindow().getDecorView();
        Intrinsics.e(view0, "window.decorView");
        ViewTreeLifecycleOwner.a(view0, this);
        View view1 = this.getWindow().getDecorView();
        Intrinsics.e(view1, "window.decorView");
        view1.setTag(0x7F0A0501, this);  // id:view_tree_view_model_store_owner
        View view2 = this.getWindow().getDecorView();
        Intrinsics.e(view2, "window.decorView");
        ViewTreeSavedStateRegistryOwner.a(view2, this);
        View view3 = this.getWindow().getDecorView();
        Intrinsics.e(view3, "window.decorView");
        ViewTreeOnBackPressedDispatcherOwner.a(view3, this);
        View view4 = this.getWindow().getDecorView();
        Intrinsics.e(view4, "window.decorView");
        view4.setTag(0x7F0A039F, this);  // id:report_drawn
    }

    public void invalidateMenu() {
        this.invalidateOptionsMenu();
    }

    @Override  // android.app.Activity
    public void onActivityResult(int v, int v1, Intent intent0) {
        if(!this.activityResultRegistry.b(v, v1, intent0)) {
            super.onActivityResult(v, v1, intent0);
        }
    }

    @Override  // android.app.Activity
    public void onBackPressed() {
        this.getOnBackPressedDispatcher().c();
    }

    @Override  // android.app.Activity
    public void onConfigurationChanged(Configuration configuration0) {
        Intrinsics.f(configuration0, "newConfig");
        super.onConfigurationChanged(configuration0);
        for(Object object0: this.onConfigurationChangedListeners) {
            ((Consumer)object0).accept(configuration0);
        }
    }

    @Override  // androidx.core.app.ComponentActivity
    public void onCreate(Bundle bundle0) {
        this.savedStateRegistryController.b(bundle0);
        this.contextAwareHelper.getClass();
        this.contextAwareHelper.b = this;
        for(Object object0: this.contextAwareHelper.a) {
            ((OnContextAvailableListener)object0).onContextAvailable(this);
        }
        super.onCreate(bundle0);
        androidx.lifecycle.ReportFragment.Companion.b(this);
        int v = this.contentLayoutId;
        if(v != 0) {
            this.setContentView(v);
        }
    }

    @Override  // android.app.Activity
    public boolean onCreatePanelMenu(int v, Menu menu0) {
        Intrinsics.f(menu0, "menu");
        if(v == 0) {
            super.onCreatePanelMenu(0, menu0);
            MenuInflater menuInflater0 = this.getMenuInflater();
            for(Object object0: this.menuHostHelper.b) {
                ((MenuProvider)object0).onCreateMenu(menu0, menuInflater0);
            }
        }
        return true;
    }

    @Override  // android.app.Activity
    public boolean onMenuItemSelected(int v, MenuItem menuItem0) {
        Intrinsics.f(menuItem0, "item");
        if(super.onMenuItemSelected(v, menuItem0)) {
            return true;
        }
        if(v == 0) {
            for(Object object0: this.menuHostHelper.b) {
                if(((MenuProvider)object0).onMenuItemSelected(menuItem0)) {
                    return true;
                }
                if(false) {
                    break;
                }
            }
            return false;
        }
        return false;
    }

    @Override  // android.app.Activity
    public void onMultiWindowModeChanged(boolean z) {
        if(this.dispatchingOnMultiWindowModeChanged) {
            return;
        }
        for(Object object0: this.onMultiWindowModeChangedListeners) {
            ((Consumer)object0).accept(new MultiWindowModeChangedInfo(z));
        }
    }

    @Override  // android.app.Activity
    public void onMultiWindowModeChanged(boolean z, Configuration configuration0) {
        Intrinsics.f(configuration0, "newConfig");
        this.dispatchingOnMultiWindowModeChanged = true;
        try {
            super.onMultiWindowModeChanged(z, configuration0);
        }
        finally {
            this.dispatchingOnMultiWindowModeChanged = false;
        }
        for(Object object0: this.onMultiWindowModeChangedListeners) {
            ((Consumer)object0).accept(new MultiWindowModeChangedInfo(z));
        }
    }

    @Override  // android.app.Activity
    public void onNewIntent(Intent intent0) {
        Intrinsics.f(intent0, "intent");
        super.onNewIntent(intent0);
        for(Object object0: this.onNewIntentListeners) {
            ((Consumer)object0).accept(intent0);
        }
    }

    @Override  // android.app.Activity
    public void onPanelClosed(int v, Menu menu0) {
        Intrinsics.f(menu0, "menu");
        for(Object object0: this.menuHostHelper.b) {
            ((MenuProvider)object0).onMenuClosed(menu0);
        }
        super.onPanelClosed(v, menu0);
    }

    @Override  // android.app.Activity
    public void onPictureInPictureModeChanged(boolean z) {
        if(this.dispatchingOnPictureInPictureModeChanged) {
            return;
        }
        for(Object object0: this.onPictureInPictureModeChangedListeners) {
            ((Consumer)object0).accept(new PictureInPictureModeChangedInfo(z));
        }
    }

    @Override  // android.app.Activity
    public void onPictureInPictureModeChanged(boolean z, Configuration configuration0) {
        Intrinsics.f(configuration0, "newConfig");
        this.dispatchingOnPictureInPictureModeChanged = true;
        try {
            super.onPictureInPictureModeChanged(z, configuration0);
        }
        finally {
            this.dispatchingOnPictureInPictureModeChanged = false;
        }
        for(Object object0: this.onPictureInPictureModeChangedListeners) {
            ((Consumer)object0).accept(new PictureInPictureModeChangedInfo(z));
        }
    }

    @Override  // android.app.Activity
    public boolean onPreparePanel(int v, View view0, Menu menu0) {
        Intrinsics.f(menu0, "menu");
        if(v == 0) {
            super.onPreparePanel(0, view0, menu0);
            for(Object object0: this.menuHostHelper.b) {
                ((MenuProvider)object0).onPrepareMenu(menu0);
            }
        }
        return true;
    }

    @Override  // android.app.Activity
    public void onRequestPermissionsResult(int v, String[] arr_s, int[] arr_v) {
        Intrinsics.f(arr_s, "permissions");
        Intrinsics.f(arr_v, "grantResults");
        Intent intent0 = new Intent().putExtra("androidx.activity.result.contract.extra.PERMISSIONS", arr_s).putExtra("androidx.activity.result.contract.extra.PERMISSION_GRANT_RESULTS", arr_v);
        if(!this.activityResultRegistry.b(v, -1, intent0) && Build.VERSION.SDK_INT >= 23) {
            super.onRequestPermissionsResult(v, arr_s, arr_v);
        }
    }

    public Object onRetainCustomNonConfigurationInstance() [...] // Inlined contents

    @Override  // android.app.Activity
    public final Object onRetainNonConfigurationInstance() {
        ViewModelStore viewModelStore0 = this._viewModelStore;
        if(viewModelStore0 == null) {
            NonConfigurationInstances componentActivity$NonConfigurationInstances0 = (NonConfigurationInstances)this.getLastNonConfigurationInstance();
            if(componentActivity$NonConfigurationInstances0 != null) {
                viewModelStore0 = componentActivity$NonConfigurationInstances0.b;
            }
        }
        if(viewModelStore0 == null) {
            return null;
        }
        NonConfigurationInstances componentActivity$NonConfigurationInstances1 = new NonConfigurationInstances();  // 初始化器: Ljava/lang/Object;-><init>()V
        componentActivity$NonConfigurationInstances1.a = null;
        componentActivity$NonConfigurationInstances1.b = viewModelStore0;
        return componentActivity$NonConfigurationInstances1;
    }

    @Override  // androidx.core.app.ComponentActivity
    public void onSaveInstanceState(Bundle bundle0) {
        Intrinsics.f(bundle0, "outState");
        if(this.getLifecycle() instanceof LifecycleRegistry) {
            Lifecycle lifecycle0 = this.getLifecycle();
            Intrinsics.d(lifecycle0, "null cannot be cast to non-null type androidx.lifecycle.LifecycleRegistry");
            ((LifecycleRegistry)lifecycle0).g(State.c);
        }
        super.onSaveInstanceState(bundle0);
        this.savedStateRegistryController.c(bundle0);
    }

    @Override  // android.app.Activity
    public void onTrimMemory(int v) {
        super.onTrimMemory(v);
        for(Object object0: this.onTrimMemoryListeners) {
            ((Consumer)object0).accept(v);
        }
    }

    @Override  // android.app.Activity
    public void onUserLeaveHint() {
        super.onUserLeaveHint();
        for(Object object0: this.onUserLeaveHintListeners) {
            ((Runnable)object0).run();
        }
    }

    public Context peekAvailableContext() {
        return this.contextAwareHelper.b;
    }

    @Override  // androidx.activity.result.ActivityResultCaller
    public final ActivityResultLauncher registerForActivityResult(ActivityResultContract activityResultContract0, ActivityResultCallback activityResultCallback0) {
        Intrinsics.f(activityResultContract0, "contract");
        Intrinsics.f(activityResultCallback0, "callback");
        return this.registerForActivityResult(activityResultContract0, this.activityResultRegistry, activityResultCallback0);
    }

    public final ActivityResultLauncher registerForActivityResult(ActivityResultContract activityResultContract0, ActivityResultRegistry activityResultRegistry0, ActivityResultCallback activityResultCallback0) {
        Intrinsics.f(activityResultContract0, "contract");
        Intrinsics.f(activityResultRegistry0, "registry");
        Intrinsics.f(activityResultCallback0, "callback");
        return activityResultRegistry0.d("activity_rq#" + this.nextLocalRequestCode.getAndIncrement(), this, activityResultContract0, activityResultCallback0);
    }

    @Override  // androidx.core.view.MenuHost
    public void removeMenuProvider(MenuProvider menuProvider0) {
        Intrinsics.f(menuProvider0, "provider");
        this.menuHostHelper.c(menuProvider0);
    }

    @Override  // androidx.core.content.OnConfigurationChangedProvider
    public final void removeOnConfigurationChangedListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onConfigurationChangedListeners.remove(consumer0);
    }

    public final void removeOnContextAvailableListener(OnContextAvailableListener onContextAvailableListener0) {
        Intrinsics.f(onContextAvailableListener0, "listener");
        this.contextAwareHelper.getClass();
        this.contextAwareHelper.a.remove(onContextAvailableListener0);
    }

    @Override  // androidx.core.app.OnMultiWindowModeChangedProvider
    public final void removeOnMultiWindowModeChangedListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onMultiWindowModeChangedListeners.remove(consumer0);
    }

    public final void removeOnNewIntentListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onNewIntentListeners.remove(consumer0);
    }

    @Override  // androidx.core.app.OnPictureInPictureModeChangedProvider
    public final void removeOnPictureInPictureModeChangedListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onPictureInPictureModeChangedListeners.remove(consumer0);
    }

    @Override  // androidx.core.content.OnTrimMemoryProvider
    public final void removeOnTrimMemoryListener(Consumer consumer0) {
        Intrinsics.f(consumer0, "listener");
        this.onTrimMemoryListeners.remove(consumer0);
    }

    public final void removeOnUserLeaveHintListener(Runnable runnable0) {
        Intrinsics.f(runnable0, "listener");
        this.onUserLeaveHintListeners.remove(runnable0);
    }

    @Override  // android.app.Activity
    public void reportFullyDrawn() {
        try {
            if(Trace.a()) {
                android.os.Trace.beginSection("reportFullyDrawn() for ComponentActivity");
            }
            super.reportFullyDrawn();
            this.getFullyDrawnReporter().a();
        }
        finally {
            android.os.Trace.endSection();
        }
    }

    @Override  // android.app.Activity
    public void setContentView(int v) {
        this.initializeViewTreeOwners();
        View view0 = this.getWindow().getDecorView();
        Intrinsics.e(view0, "window.decorView");
        ((ReportFullyDrawnExecutorImpl)this.reportFullyDrawnExecutor).a(view0);
        super.setContentView(v);
    }

    @Override  // android.app.Activity
    public void setContentView(View view0) {
        this.initializeViewTreeOwners();
        View view1 = this.getWindow().getDecorView();
        Intrinsics.e(view1, "window.decorView");
        ((ReportFullyDrawnExecutorImpl)this.reportFullyDrawnExecutor).a(view1);
        super.setContentView(view0);
    }

    @Override  // android.app.Activity
    public void setContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        this.initializeViewTreeOwners();
        View view1 = this.getWindow().getDecorView();
        Intrinsics.e(view1, "window.decorView");
        ((ReportFullyDrawnExecutorImpl)this.reportFullyDrawnExecutor).a(view1);
        super.setContentView(view0, viewGroup$LayoutParams0);
    }

    @Override  // android.app.Activity
    public void startActivityForResult(Intent intent0, int v) {
        Intrinsics.f(intent0, "intent");
        super.startActivityForResult(intent0, v);
    }

    @Override  // android.app.Activity
    public void startActivityForResult(Intent intent0, int v, Bundle bundle0) {
        Intrinsics.f(intent0, "intent");
        super.startActivityForResult(intent0, v, bundle0);
    }

    @Override  // android.app.Activity
    public void startIntentSenderForResult(IntentSender intentSender0, int v, Intent intent0, int v1, int v2, int v3) throws IntentSender.SendIntentException {
        Intrinsics.f(intentSender0, "intent");
        super.startIntentSenderForResult(intentSender0, v, intent0, v1, v2, v3);
    }

    @Override  // android.app.Activity
    public void startIntentSenderForResult(IntentSender intentSender0, int v, Intent intent0, int v1, int v2, int v3, Bundle bundle0) throws IntentSender.SendIntentException {
        Intrinsics.f(intentSender0, "intent");
        super.startIntentSenderForResult(intentSender0, v, intent0, v1, v2, v3, bundle0);
    }
}


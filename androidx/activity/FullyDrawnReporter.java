package androidx.activity;

import java.util.ArrayList;
import java.util.concurrent.Executor;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Intrinsics;

public final class FullyDrawnReporter {
    public final Executor a;
    public final Object b;
    public boolean c;
    public final ArrayList d;

    public FullyDrawnReporter(Executor executor0, Function0 function00) {
        Intrinsics.f(executor0, "executor");
        super();
        this.a = executor0;
        this.b = new Object();
        this.d = new ArrayList();
    }

    public final void a() {
        synchronized(this.b) {
            this.c = true;
            for(Object object1: this.d) {
                ((Function0)object1).invoke();
            }
            this.d.clear();
        }
    }
}


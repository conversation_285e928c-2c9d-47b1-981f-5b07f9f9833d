package androidx.activity;

import android.content.Intent;
import android.content.IntentSender.SendIntentException;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.activity.result.ActivityResultRegistry;
import androidx.activity.result.IntentSenderRequest;
import androidx.activity.result.contract.ActivityResultContract.SynchronousResult;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.core.app.ActivityCompat;
import kotlin.jvm.internal.Intrinsics;

public final class ComponentActivity.activityResultRegistry.1 extends ActivityResultRegistry {
    public final ComponentActivity h;

    public ComponentActivity.activityResultRegistry.1(ComponentActivity componentActivity0) {
        this.h = componentActivity0;
        super();
    }

    @Override  // androidx.activity.result.ActivityResultRegistry
    public final void c(int v, ActivityResultContract activityResultContract0, Object object0) {
        Bundle bundle2;
        Intrinsics.f(activityResultContract0, "contract");
        ComponentActivity componentActivity0 = this.h;
        SynchronousResult activityResultContract$SynchronousResult0 = activityResultContract0.getSynchronousResult(componentActivity0, object0);
        if(activityResultContract$SynchronousResult0 != null) {
            new Handler(Looper.getMainLooper()).post(new f(this, v, 0, activityResultContract$SynchronousResult0));
            return;
        }
        Intent intent0 = activityResultContract0.createIntent(componentActivity0, object0);
        if(intent0.getExtras() != null) {
            Bundle bundle0 = intent0.getExtras();
            Intrinsics.c(bundle0);
            if(bundle0.getClassLoader() == null) {
                intent0.setExtrasClassLoader(componentActivity0.getClassLoader());
            }
        }
        if(intent0.hasExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE")) {
            Bundle bundle1 = intent0.getBundleExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE");
            intent0.removeExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE");
            bundle2 = bundle1;
        }
        else {
            bundle2 = null;
        }
        if(Intrinsics.a("androidx.activity.result.contract.action.REQUEST_PERMISSIONS", intent0.getAction())) {
            String[] arr_s = intent0.getStringArrayExtra("androidx.activity.result.contract.extra.PERMISSIONS");
            if(arr_s == null) {
                arr_s = new String[0];
            }
            ActivityCompat.c(componentActivity0, arr_s, v);
            return;
        }
        if(Intrinsics.a("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST", intent0.getAction())) {
            IntentSenderRequest intentSenderRequest0 = (IntentSenderRequest)intent0.getParcelableExtra("androidx.activity.result.contract.extra.INTENT_SENDER_REQUEST");
            try {
                Intrinsics.c(intentSenderRequest0);
                componentActivity0.startIntentSenderForResult(intentSenderRequest0.a, v, intentSenderRequest0.b, intentSenderRequest0.c, intentSenderRequest0.d, 0, bundle2);
            }
            catch(IntentSender.SendIntentException intentSender$SendIntentException0) {
                new Handler(Looper.getMainLooper()).post(new f(this, v, 1, intentSender$SendIntentException0));
            }
            return;
        }
        componentActivity0.startActivityForResult(intent0, v, bundle2);
    }
}


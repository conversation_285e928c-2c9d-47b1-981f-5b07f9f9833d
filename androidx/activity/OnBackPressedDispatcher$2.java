package androidx.activity;

import java.util.ListIterator;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Lambda;

final class OnBackPressedDispatcher.2 extends Lambda implements Function1 {
    public final OnBackPressedDispatcher a;

    public OnBackPressedDispatcher.2(OnBackPressedDispatcher onBackPressedDispatcher0) {
        this.a = onBackPressedDispatcher0;
        super(1);
    }

    @Override  // kotlin.jvm.functions.Function1
    public final Object invoke(Object object0) {
        Intrinsics.f(((BackEventCompat)object0), "backEvent");
        OnBackPressedDispatcher onBackPressedDispatcher0 = this.a;
        OnBackPressedCallback onBackPressedCallback0 = onBackPressedDispatcher0.c;
        if(onBackPressedCallback0 == null) {
            Object object1 = null;
            ListIterator listIterator0 = onBackPressedDispatcher0.b.listIterator(onBackPressedDispatcher0.b.size());
            while(listIterator0.hasPrevious()) {
                Object object2 = listIterator0.previous();
                if(((OnBackPressedCallback)object2).isEnabled()) {
                    object1 = object2;
                    break;
                }
            }
            onBackPressedCallback0 = (OnBackPressedCallback)object1;
        }
        if(onBackPressedCallback0 != null) {
            onBackPressedCallback0.handleOnBackProgressed(((BackEventCompat)object0));
        }
        return Unit.a;
    }
}


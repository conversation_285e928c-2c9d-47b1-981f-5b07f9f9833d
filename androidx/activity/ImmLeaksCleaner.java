package androidx.activity;

import android.app.Activity;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import androidx.lifecycle.Lifecycle.Event;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import java.lang.reflect.Field;
import kotlin.Lazy;
import kotlin.LazyKt;
import kotlin.jvm.internal.Intrinsics;

public final class ImmLeaksCleaner implements LifecycleEventObserver {
    public static abstract class Cleaner {
        public abstract boolean a(InputMethodManager arg1);

        public abstract Object b(InputMethodManager arg1);

        public abstract View c(InputMethodManager arg1);
    }

    public static final class FailedInitialization extends Cleaner {
        public static final FailedInitialization a;

        static {
            FailedInitialization.a = new FailedInitialization();  // 初始化器: Ljava/lang/Object;-><init>()V
        }

        @Override  // androidx.activity.ImmLeaksCleaner$Cleaner
        public final boolean a(InputMethodManager inputMethodManager0) {
            return false;
        }

        @Override  // androidx.activity.ImmLeaksCleaner$Cleaner
        public final Object b(InputMethodManager inputMethodManager0) {
            return null;
        }

        @Override  // androidx.activity.ImmLeaksCleaner$Cleaner
        public final View c(InputMethodManager inputMethodManager0) {
            return null;
        }
    }

    public static final class ValidCleaner extends Cleaner {
        public final Field a;
        public final Field b;
        public final Field c;

        public ValidCleaner(Field field0, Field field1, Field field2) {
            this.a = field0;
            this.b = field1;
            this.c = field2;
        }

        @Override  // androidx.activity.ImmLeaksCleaner$Cleaner
        public final boolean a(InputMethodManager inputMethodManager0) {
            try {
                this.c.set(inputMethodManager0, null);
                return true;
            }
            catch(IllegalAccessException unused_ex) {
                return false;
            }
        }

        @Override  // androidx.activity.ImmLeaksCleaner$Cleaner
        public final Object b(InputMethodManager inputMethodManager0) {
            try {
                return this.a.get(inputMethodManager0);
            }
            catch(IllegalAccessException unused_ex) {
                return null;
            }
        }

        @Override  // androidx.activity.ImmLeaksCleaner$Cleaner
        public final View c(InputMethodManager inputMethodManager0) {
            try {
                return (View)this.b.get(inputMethodManager0);
            }
            catch(IllegalAccessException | ClassCastException unused_ex) {
                return null;
            }
        }
    }

    public final Activity a;
    public static final Lazy b;

    static {
        ImmLeaksCleaner.b = LazyKt.a(ImmLeaksCleaner.Companion.cleaner.2.a);
    }

    public ImmLeaksCleaner(Activity activity0) {
        Intrinsics.f(activity0, "activity");
        super();
        this.a = activity0;
    }

    @Override  // androidx.lifecycle.LifecycleEventObserver
    public final void onStateChanged(LifecycleOwner lifecycleOwner0, Event lifecycle$Event0) {
        if(lifecycle$Event0 != Event.ON_DESTROY) {
            return;
        }
        Object object0 = this.a.getSystemService("input_method");
        Intrinsics.d(object0, "null cannot be cast to non-null type android.view.inputmethod.InputMethodManager");
        Cleaner immLeaksCleaner$Cleaner0 = (Cleaner)ImmLeaksCleaner.b.getValue();
        Object object1 = immLeaksCleaner$Cleaner0.b(((InputMethodManager)object0));
        if(object1 == null) {
            return;
        }
        synchronized(object1) {
            View view0 = immLeaksCleaner$Cleaner0.c(((InputMethodManager)object0));
            if(view0 == null) {
                return;
            }
            if(view0.isAttachedToWindow()) {
                return;
            }
            boolean z = immLeaksCleaner$Cleaner0.a(((InputMethodManager)object0));
        }
        if(z) {
            ((InputMethodManager)object0).isActive();
        }
    }
}


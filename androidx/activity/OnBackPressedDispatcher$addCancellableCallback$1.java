package androidx.activity;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.FunctionReferenceImpl;

final class OnBackPressedDispatcher.addCancellableCallback.1 extends FunctionReferenceImpl implements Function0 {
    @Override  // kotlin.jvm.functions.Function0
    public final Object invoke() {
        ((OnBackPressedDispatcher)this.receiver).e();
        return Unit.a;
    }
}


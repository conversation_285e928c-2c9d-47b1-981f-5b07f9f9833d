package androidx.activity;

import android.window.BackEvent;
import kotlin.jvm.internal.Intrinsics;

public final class BackEventCompat {
    public final float a;
    public final float b;
    public final float c;
    public final int d;

    public BackEventCompat(BackEvent backEvent0) {
        Intrinsics.f(backEvent0, "backEvent");
        float f = Api34Impl.a.d(backEvent0);
        float f1 = Api34Impl.a.e(backEvent0);
        float f2 = Api34Impl.a.b(backEvent0);
        int v = Api34Impl.a.c(backEvent0);
        super();
        this.a = f;
        this.b = f1;
        this.c = f2;
        this.d = v;
    }

    @Override
    public final String toString() {
        return "BackEventCompat{touchX=" + this.a + ", touchY=" + this.b + ", progress=" + this.c + ", swipeEdge=" + this.d + '}';
    }
}


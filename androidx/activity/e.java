package androidx.activity;

import kotlin.jvm.internal.Intrinsics;

public final class e implements Runnable {
    public final int a;
    public final Object b;

    public e(Object object0, int v) {
        this.a = v;
        this.b = object0;
    }

    @Override
    public final void run() {
        if(this.a != 0) {
            ComponentDialog.a(((ComponentDialog)this.b));
            return;
        }
        ReportFullyDrawnExecutorImpl componentActivity$ReportFullyDrawnExecutorImpl0 = (ReportFullyDrawnExecutorImpl)this.b;
        Intrinsics.f(componentActivity$ReportFullyDrawnExecutorImpl0, "this$0");
        Runnable runnable0 = componentActivity$ReportFullyDrawnExecutorImpl0.b;
        if(runnable0 != null) {
            runnable0.run();
            componentActivity$ReportFullyDrawnExecutorImpl0.b = null;
        }
    }
}


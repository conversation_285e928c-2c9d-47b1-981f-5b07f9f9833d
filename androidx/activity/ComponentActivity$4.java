package androidx.activity;

import androidx.lifecycle.Lifecycle.Event;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

public final class ComponentActivity.4 implements LifecycleEventObserver {
    public final ComponentActivity a;

    public ComponentActivity.4(ComponentActivity componentActivity0) {
        this.a = componentActivity0;
    }

    @Override  // androidx.lifecycle.LifecycleEventObserver
    public final void onStateChanged(LifecycleOwner lifecycleOwner0, Event lifecycle$Event0) {
        ComponentActivity.access$ensureViewModelStore(this.a);
        this.a.getLifecycle().b(this);
    }
}


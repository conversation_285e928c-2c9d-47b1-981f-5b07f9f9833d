package androidx.activity;

import android.view.View;
import kotlin.jvm.internal.Intrinsics;

public abstract class ViewTreeOnBackPressedDispatcherOwner {
    public static final void a(View view0, OnBackPressedDispatcherOwner onBackPressedDispatcherOwner0) {
        Intrinsics.f(view0, "<this>");
        Intrinsics.f(onBackPressedDispatcherOwner0, "onBackPressedDispatcherOwner");
        view0.setTag(0x7F0A04FF, onBackPressedDispatcherOwner0);  // id:view_tree_on_back_pressed_dispatcher_owner
    }
}


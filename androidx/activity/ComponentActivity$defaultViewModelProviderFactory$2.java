package androidx.activity;

import android.app.Application;
import androidx.lifecycle.SavedStateViewModelFactory;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Lambda;

final class ComponentActivity.defaultViewModelProviderFactory.2 extends Lambda implements Function0 {
    public final ComponentActivity a;

    public ComponentActivity.defaultViewModelProviderFactory.2(ComponentActivity componentActivity0) {
        this.a = componentActivity0;
        super(0);
    }

    @Override  // kotlin.jvm.functions.Function0
    public final Object invoke() {
        Application application0 = this.a.getApplication();
        return this.a.getIntent() == null ? new SavedStateViewModelFactory(application0, this.a, null) : new SavedStateViewModelFactory(application0, this.a, this.a.getIntent().getExtras());
    }
}


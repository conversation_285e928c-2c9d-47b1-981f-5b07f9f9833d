package androidx.activity.result;

import android.content.Intent;
import android.os.Bundle;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.core.os.BundleCompat;
import androidx.lifecycle.Lifecycle.State;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.NoSuchElementException;
import java.util.Objects;
import kotlin.jvm.internal.Intrinsics;
import kotlin.sequences.SequencesKt;

public abstract class ActivityResultRegistry {
    static final class CallbackAndContract {
        public final ActivityResultCallback a;
        public final ActivityResultContract b;

        public CallbackAndContract(ActivityResultContract activityResultContract0, ActivityResultCallback activityResultCallback0) {
            this.a = activityResultCallback0;
            this.b = activityResultContract0;
        }
    }

    static final class LifecycleContainer {
        public final Lifecycle a;
        public final ArrayList b;

        public LifecycleContainer(Lifecycle lifecycle0) {
            this.a = lifecycle0;
            this.b = new ArrayList();
        }
    }

    public final LinkedHashMap a;
    public final LinkedHashMap b;
    public final LinkedHashMap c;
    public final ArrayList d;
    public final transient LinkedHashMap e;
    public final LinkedHashMap f;
    public final Bundle g;

    public ActivityResultRegistry() {
        this.a = new LinkedHashMap();
        this.b = new LinkedHashMap();
        this.c = new LinkedHashMap();
        this.d = new ArrayList();
        this.e = new LinkedHashMap();
        this.f = new LinkedHashMap();
        this.g = new Bundle();
    }

    public final void a(int v, Object object0) {
        String s = (String)this.a.get(v);
        if(s == null) {
            return;
        }
        CallbackAndContract activityResultRegistry$CallbackAndContract0 = (CallbackAndContract)this.e.get(s);
        if((activityResultRegistry$CallbackAndContract0 == null ? null : activityResultRegistry$CallbackAndContract0.a) == null) {
            this.g.remove(s);
            this.f.put(s, object0);
            return;
        }
        ActivityResultCallback activityResultCallback0 = activityResultRegistry$CallbackAndContract0.a;
        Intrinsics.d(activityResultCallback0, "null cannot be cast to non-null type androidx.activity.result.ActivityResultCallback<O of androidx.activity.result.ActivityResultRegistry.dispatchResult>");
        if(this.d.remove(s)) {
            activityResultCallback0.onActivityResult(object0);
        }
    }

    public final boolean b(int v, int v1, Intent intent0) {
        String s = (String)this.a.get(v);
        if(s == null) {
            return false;
        }
        CallbackAndContract activityResultRegistry$CallbackAndContract0 = (CallbackAndContract)this.e.get(s);
        if((activityResultRegistry$CallbackAndContract0 == null ? null : activityResultRegistry$CallbackAndContract0.a) != null) {
            ArrayList arrayList0 = this.d;
            if(arrayList0.contains(s)) {
                Object object0 = activityResultRegistry$CallbackAndContract0.b.parseResult(v1, intent0);
                activityResultRegistry$CallbackAndContract0.a.onActivityResult(object0);
                arrayList0.remove(s);
                return true;
            }
        }
        this.f.remove(s);
        ActivityResult activityResult0 = new ActivityResult(v1, intent0);
        this.g.putParcelable(s, activityResult0);
        return true;
    }

    public abstract void c(int arg1, ActivityResultContract arg2, Object arg3);

    public final ActivityResultRegistry.register.2 d(String s, LifecycleOwner lifecycleOwner0, ActivityResultContract activityResultContract0, ActivityResultCallback activityResultCallback0) {
        Intrinsics.f(s, "key");
        Intrinsics.f(lifecycleOwner0, "lifecycleOwner");
        Intrinsics.f(activityResultContract0, "contract");
        Intrinsics.f(activityResultCallback0, "callback");
        Lifecycle lifecycle0 = lifecycleOwner0.getLifecycle();
        if(!((LifecycleRegistry)lifecycle0).c.a(State.d) == 0) {
            throw new IllegalStateException(("LifecycleOwner " + lifecycleOwner0 + " is attempting to register while current state is " + ((LifecycleRegistry)lifecycle0).c + ". LifecycleOwners must call register before they are STARTED.").toString());
        }
        this.f(s);
        LinkedHashMap linkedHashMap0 = this.c;
        LifecycleContainer activityResultRegistry$LifecycleContainer0 = (LifecycleContainer)linkedHashMap0.get(s);
        if(activityResultRegistry$LifecycleContainer0 == null) {
            activityResultRegistry$LifecycleContainer0 = new LifecycleContainer(lifecycle0);
        }
        a a0 = new a(this, s, activityResultCallback0, activityResultContract0);
        activityResultRegistry$LifecycleContainer0.a.a(a0);
        activityResultRegistry$LifecycleContainer0.b.add(a0);
        linkedHashMap0.put(s, activityResultRegistry$LifecycleContainer0);
        return new ActivityResultRegistry.register.2(this, s, activityResultContract0);
    }

    public final ActivityResultRegistry.register.3 e(String s, ActivityResultContract activityResultContract0, ActivityResultCallback activityResultCallback0) {
        Intrinsics.f(s, "key");
        this.f(s);
        CallbackAndContract activityResultRegistry$CallbackAndContract0 = new CallbackAndContract(activityResultContract0, activityResultCallback0);
        this.e.put(s, activityResultRegistry$CallbackAndContract0);
        LinkedHashMap linkedHashMap0 = this.f;
        if(linkedHashMap0.containsKey(s)) {
            Object object0 = linkedHashMap0.get(s);
            linkedHashMap0.remove(s);
            activityResultCallback0.onActivityResult(object0);
        }
        Bundle bundle0 = this.g;
        ActivityResult activityResult0 = (ActivityResult)BundleCompat.a(bundle0, s);
        if(activityResult0 != null) {
            bundle0.remove(s);
            activityResultCallback0.onActivityResult(activityResultContract0.parseResult(activityResult0.a, activityResult0.b));
        }
        return new ActivityResultRegistry.register.3(this, s, activityResultContract0);
    }

    public final void f(String s) {
        LinkedHashMap linkedHashMap0 = this.b;
        if(((Integer)linkedHashMap0.get(s)) != null) {
            return;
        }
        for(Object object0: SequencesKt.c(ActivityResultRegistry.generateRandomNumber.1.a)) {
            Number number0 = (Number)object0;
            LinkedHashMap linkedHashMap1 = this.a;
            if(!linkedHashMap1.containsKey(number0.intValue()) != 0) {
                int v = number0.intValue();
                linkedHashMap1.put(v, s);
                linkedHashMap0.put(s, v);
                return;
            }
            if(false) {
                break;
            }
        }
        throw new NoSuchElementException("Sequence contains no element matching the predicate.");
    }

    public final void g(String s) {
        Intrinsics.f(s, "key");
        if(!this.d.contains(s)) {
            Integer integer0 = (Integer)this.b.remove(s);
            if(integer0 != null) {
                this.a.remove(integer0);
            }
        }
        this.e.remove(s);
        LinkedHashMap linkedHashMap0 = this.f;
        if(linkedHashMap0.containsKey(s)) {
            Objects.toString(linkedHashMap0.get(s));
            linkedHashMap0.remove(s);
        }
        Bundle bundle0 = this.g;
        if(bundle0.containsKey(s)) {
            Objects.toString(((ActivityResult)BundleCompat.a(bundle0, s)));
            bundle0.remove(s);
        }
        LinkedHashMap linkedHashMap1 = this.c;
        LifecycleContainer activityResultRegistry$LifecycleContainer0 = (LifecycleContainer)linkedHashMap1.get(s);
        if(activityResultRegistry$LifecycleContainer0 != null) {
            ArrayList arrayList0 = activityResultRegistry$LifecycleContainer0.b;
            for(Object object0: arrayList0) {
                activityResultRegistry$LifecycleContainer0.a.b(((LifecycleEventObserver)object0));
            }
            arrayList0.clear();
            linkedHashMap1.remove(s);
        }
    }
}


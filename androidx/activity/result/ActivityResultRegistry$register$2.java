package androidx.activity.result;

import androidx.activity.result.contract.ActivityResultContract;
import androidx.core.app.ActivityOptionsCompat;
import java.util.ArrayList;

public final class ActivityResultRegistry.register.2 extends ActivityResultLauncher {
    public final ActivityResultRegistry a;
    public final String b;
    public final ActivityResultContract c;

    public ActivityResultRegistry.register.2(ActivityResultRegistry activityResultRegistry0, String s, ActivityResultContract activityResultContract0) {
        this.a = activityResultRegistry0;
        this.b = s;
        this.c = activityResultContract0;
    }

    @Override  // androidx.activity.result.ActivityResultLauncher
    public final void launch(Object object0, ActivityOptionsCompat activityOptionsCompat0) {
        ActivityResultRegistry activityResultRegistry0 = this.a;
        String s = this.b;
        Object object1 = activityResultRegistry0.b.get(s);
        ActivityResultContract activityResultContract0 = this.c;
        if(object1 != null) {
            int v = ((Number)object1).intValue();
            ArrayList arrayList0 = activityResultRegistry0.d;
            arrayList0.add(s);
            try {
                activityResultRegistry0.c(v, activityResultContract0, object0);
                return;
            }
            catch(Exception exception0) {
                arrayList0.remove(s);
                throw exception0;
            }
        }
        throw new IllegalStateException(("Attempting to launch an unregistered ActivityResultLauncher with contract " + activityResultContract0 + " and input " + object0 + ". You must ensure the ActivityResultLauncher is registered before calling launch().").toString());
    }

    @Override  // androidx.activity.result.ActivityResultLauncher
    public final void unregister() {
        this.a.g(this.b);
    }
}


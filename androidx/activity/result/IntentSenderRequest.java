package androidx.activity.result;

import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.content.Intent;
import android.content.IntentSender;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import kotlin.jvm.internal.Intrinsics;

@SuppressLint({"BanParcelableUsage"})
public final class IntentSenderRequest implements Parcelable {
    public static final class Builder {
        public final IntentSender a;
        public Intent b;
        public int c;
        public int d;

        public Builder(PendingIntent pendingIntent0) {
            Intrinsics.f(pendingIntent0, "pendingIntent");
            IntentSender intentSender0 = pendingIntent0.getIntentSender();
            Intrinsics.e(intentSender0, "pendingIntent.intentSender");
            this(intentSender0);
        }

        public Builder(IntentSender intentSender0) {
            Intrinsics.f(intentSender0, "intentSender");
            super();
            this.a = intentSender0;
        }

        public final IntentSenderRequest a() {
            return new IntentSenderRequest(this.a, this.b, this.c, this.d);
        }
    }

    public static final Parcelable.Creator CREATOR;
    public final IntentSender a;
    public final Intent b;
    public final int c;
    public final int d;

    static {
        IntentSenderRequest.CREATOR = new IntentSenderRequest.Companion.CREATOR.1();  // 初始化器: Ljava/lang/Object;-><init>()V
    }

    public IntentSenderRequest(IntentSender intentSender0, Intent intent0, int v, int v1) {
        Intrinsics.f(intentSender0, "intentSender");
        super();
        this.a = intentSender0;
        this.b = intent0;
        this.c = v;
        this.d = v1;
    }

    @Override  // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override  // android.os.Parcelable
    public final void writeToParcel(Parcel parcel0, int v) {
        Intrinsics.f(parcel0, "dest");
        parcel0.writeParcelable(this.a, v);
        parcel0.writeParcelable(this.b, v);
        parcel0.writeInt(this.c);
        parcel0.writeInt(this.d);
    }
}


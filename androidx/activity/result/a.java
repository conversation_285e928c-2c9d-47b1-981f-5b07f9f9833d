package androidx.activity.result;

import android.os.Bundle;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.core.os.BundleCompat;
import androidx.lifecycle.Lifecycle.Event;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import java.util.LinkedHashMap;
import kotlin.jvm.internal.Intrinsics;

public final class a implements LifecycleEventObserver {
    public final ActivityResultRegistry a;
    public final String b;
    public final ActivityResultCallback c;
    public final ActivityResultContract d;

    public a(ActivityResultRegistry activityResultRegistry0, String s, ActivityResultCallback activityResultCallback0, ActivityResultContract activityResultContract0) {
        this.a = activityResultRegistry0;
        this.b = s;
        this.c = activityResultCallback0;
        this.d = activityResultContract0;
    }

    @Override  // androidx.lifecycle.LifecycleEventObserver
    public final void onStateChanged(LifecycleOwner lifecycleOwner0, Event lifecycle$Event0) {
        ActivityResultRegistry activityResultRegistry0 = this.a;
        Intrinsics.f(activityResultRegistry0, "this$0");
        String s = this.b;
        Intrinsics.f(s, "$key");
        ActivityResultCallback activityResultCallback0 = this.c;
        Intrinsics.f(activityResultCallback0, "$callback");
        ActivityResultContract activityResultContract0 = this.d;
        Intrinsics.f(activityResultContract0, "$contract");
        LinkedHashMap linkedHashMap0 = activityResultRegistry0.e;
        if(Event.ON_START == lifecycle$Event0) {
            linkedHashMap0.put(s, new CallbackAndContract(activityResultContract0, activityResultCallback0));
            LinkedHashMap linkedHashMap1 = activityResultRegistry0.f;
            if(linkedHashMap1.containsKey(s)) {
                Object object0 = linkedHashMap1.get(s);
                linkedHashMap1.remove(s);
                activityResultCallback0.onActivityResult(object0);
            }
            Bundle bundle0 = activityResultRegistry0.g;
            ActivityResult activityResult0 = (ActivityResult)BundleCompat.a(bundle0, s);
            if(activityResult0 != null) {
                bundle0.remove(s);
                activityResultCallback0.onActivityResult(activityResultContract0.parseResult(activityResult0.a, activityResult0.b));
            }
        }
        else {
            if(Event.ON_STOP == lifecycle$Event0) {
                linkedHashMap0.remove(s);
                return;
            }
            if(Event.ON_DESTROY == lifecycle$Event0) {
                activityResultRegistry0.g(s);
            }
        }
    }
}


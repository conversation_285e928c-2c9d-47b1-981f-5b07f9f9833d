package androidx.activity.result.contract;

import android.content.Context;
import android.content.Intent;
import androidx.core.content.ContextCompat;
import kotlin.jvm.internal.Intrinsics;

public final class ActivityResultContracts.RequestPermission extends ActivityResultContract {
    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final Intent createIntent(Context context0, Object object0) {
        Intrinsics.f(context0, "context");
        Intrinsics.f(((String)object0), "input");
        Intent intent0 = new Intent("androidx.activity.result.contract.action.REQUEST_PERMISSIONS").putExtra("androidx.activity.result.contract.extra.PERMISSIONS", new String[]{((String)object0)});
        Intrinsics.e(intent0, "Intent(ACTION_REQUEST_PE…EXTRA_PERMISSIONS, input)");
        return intent0;
    }

    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final SynchronousResult getSynchronousResult(Context context0, Object object0) {
        Intrinsics.f(context0, "context");
        Intrinsics.f(((String)object0), "input");
        return ContextCompat.checkSelfPermission(context0, ((String)object0)) == 0 ? new SynchronousResult(Boolean.TRUE) : null;
    }

    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final Object parseResult(int v, Intent intent0) {
        if(intent0 != null && v == -1) {
            int[] arr_v = intent0.getIntArrayExtra("androidx.activity.result.contract.extra.PERMISSION_GRANT_RESULTS");
            if(arr_v != null) {
                for(int v1 = 0; v1 < arr_v.length; ++v1) {
                    if(arr_v[v1] == 0) {
                        return true;
                    }
                }
            }
            return false;
        }
        return false;
    }
}


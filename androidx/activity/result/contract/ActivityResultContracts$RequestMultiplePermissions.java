package androidx.activity.result.contract;

import android.content.Context;
import android.content.Intent;
import androidx.core.content.ContextCompat;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import kotlin.Pair;
import kotlin.collections.CollectionsKt;
import kotlin.collections.EmptyMap;
import kotlin.collections.MapsKt;
import kotlin.jvm.internal.Intrinsics;

public final class ActivityResultContracts.RequestMultiplePermissions extends ActivityResultContract {
    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final Intent createIntent(Context context0, Object object0) {
        Intrinsics.f(context0, "context");
        Intrinsics.f(((String[])object0), "input");
        Intent intent0 = new Intent("androidx.activity.result.contract.action.REQUEST_PERMISSIONS").putExtra("androidx.activity.result.contract.extra.PERMISSIONS", ((String[])object0));
        Intrinsics.e(intent0, "Intent(ACTION_REQUEST_PE…EXTRA_PERMISSIONS, input)");
        return intent0;
    }

    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final SynchronousResult getSynchronousResult(Context context0, Object object0) {
        Intrinsics.f(context0, "context");
        Intrinsics.f(((String[])object0), "input");
        if(((String[])object0).length == 0) {
            return new SynchronousResult(((Serializable)EmptyMap.a));
        }
        int v1 = 0;
        while(v1 < ((String[])object0).length) {
            if(ContextCompat.checkSelfPermission(context0, ((String[])object0)[v1]) == 0) {
                ++v1;
                continue;
            }
            return null;
        }
        int v2 = MapsKt.g(((String[])object0).length);
        if(v2 < 16) {
            v2 = 16;
        }
        LinkedHashMap linkedHashMap0 = new LinkedHashMap(v2);
        for(int v = 0; v < ((String[])object0).length; ++v) {
            linkedHashMap0.put(((String[])object0)[v], Boolean.TRUE);
        }
        return new SynchronousResult(linkedHashMap0);
    }

    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final Object parseResult(int v, Intent intent0) {
        if(v != -1) {
            return EmptyMap.a;
        }
        if(intent0 == null) {
            return EmptyMap.a;
        }
        String[] arr_s = intent0.getStringArrayExtra("androidx.activity.result.contract.extra.PERMISSIONS");
        int[] arr_v = intent0.getIntArrayExtra("androidx.activity.result.contract.extra.PERMISSION_GRANT_RESULTS");
        if(arr_v != null && arr_s != null) {
            ArrayList arrayList0 = new ArrayList(arr_v.length);
            for(int v2 = 0; v2 < arr_v.length; ++v2) {
                arrayList0.add(Boolean.valueOf(arr_v[v2] == 0));
            }
            ArrayList arrayList1 = new ArrayList();
            for(int v1 = 0; v1 < arr_s.length; ++v1) {
                String s = arr_s[v1];
                if(s != null) {
                    arrayList1.add(s);
                }
            }
            Iterator iterator0 = arrayList1.iterator();
            Iterator iterator1 = arrayList0.iterator();
            ArrayList arrayList2 = new ArrayList(Math.min(CollectionsKt.f(arrayList1), CollectionsKt.f(arrayList0)));
            while(iterator0.hasNext() && iterator1.hasNext()) {
                Object object0 = iterator0.next();
                Object object1 = iterator1.next();
                arrayList2.add(new Pair(object0, object1));
            }
            return MapsKt.k(arrayList2);
        }
        return EmptyMap.a;
    }
}


package androidx.activity.result.contract;

import android.content.Context;
import android.content.Intent;
import java.io.Serializable;
import kotlin.jvm.internal.Intrinsics;

public abstract class ActivityResultContract {
    public static final class SynchronousResult {
        public final Object a;

        public SynchronousResult(Serializable serializable0) {
            this.a = serializable0;
        }
    }

    public abstract Intent createIntent(Context arg1, Object arg2);

    public SynchronousResult getSynchronousResult(Context context0, Object object0) {
        Intrinsics.f(context0, "context");
        return null;
    }

    public abstract Object parseResult(int arg1, Intent arg2);
}


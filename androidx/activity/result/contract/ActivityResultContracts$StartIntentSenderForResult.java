package androidx.activity.result.contract;

import android.content.Context;
import android.content.Intent;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.IntentSenderRequest;
import kotlin.jvm.internal.Intrinsics;

public final class ActivityResultContracts.StartIntentSenderForResult extends ActivityResultContract {
    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final Intent createIntent(Context context0, Object object0) {
        Intrinsics.f(context0, "context");
        Intrinsics.f(((IntentSenderRequest)object0), "input");
        Intent intent0 = new Intent("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST").putExtra("androidx.activity.result.contract.extra.INTENT_SENDER_REQUEST", ((IntentSenderRequest)object0));
        Intrinsics.e(intent0, "Intent(ACTION_INTENT_SEN…NT_SENDER_REQUEST, input)");
        return intent0;
    }

    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final Object parseResult(int v, Intent intent0) {
        return new ActivityResult(v, intent0);
    }
}


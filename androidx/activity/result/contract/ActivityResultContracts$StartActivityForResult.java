package androidx.activity.result.contract;

import android.content.Context;
import android.content.Intent;
import androidx.activity.result.ActivityResult;
import kotlin.jvm.internal.Intrinsics;

public final class ActivityResultContracts.StartActivityForResult extends ActivityResultContract {
    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final Intent createIntent(Context context0, Object object0) {
        Intrinsics.f(context0, "context");
        Intrinsics.f(((Intent)object0), "input");
        return (Intent)object0;
    }

    @Override  // androidx.activity.result.contract.ActivityResultContract
    public final Object parseResult(int v, Intent intent0) {
        return new ActivityResult(v, intent0);
    }
}


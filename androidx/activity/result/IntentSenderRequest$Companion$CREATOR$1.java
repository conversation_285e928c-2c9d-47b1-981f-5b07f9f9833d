package androidx.activity.result;

import android.content.Intent;
import android.content.IntentSender;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import kotlin.jvm.internal.Intrinsics;

public final class IntentSenderRequest.Companion.CREATOR.1 implements Parcelable.Creator {
    @Override  // android.os.Parcelable$Creator
    public final Object createFromParcel(Parcel parcel0) {
        Intrinsics.f(parcel0, "inParcel");
        Parcelable parcelable0 = parcel0.readParcelable(IntentSender.class.getClassLoader());
        Intrinsics.c(parcelable0);
        return new IntentSenderRequest(((IntentSender)parcelable0), ((Intent)parcel0.readParcelable(Intent.class.getClassLoader())), parcel0.readInt(), parcel0.readInt());
    }

    @Override  // android.os.Parcelable$Creator
    public final Object[] newArray(int v) {
        return new IntentSenderRequest[v];
    }
}


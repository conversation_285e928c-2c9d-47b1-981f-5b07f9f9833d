package androidx.activity.result;

import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Lambda;
import kotlin.random.Random;

final class ActivityResultRegistry.generateRandomNumber.1 extends Lambda implements Function0 {
    public static final ActivityResultRegistry.generateRandomNumber.1 a;

    static {
        ActivityResultRegistry.generateRandomNumber.1.a = new ActivityResultRegistry.generateRandomNumber.1(0);  // 初始化器: Lkotlin/jvm/internal/Lambda;-><init>(I)V
    }

    @Override  // kotlin.jvm.functions.Function0
    public final Object invoke() {
        Random.a.getClass();
        return (int)(Random.b.a().nextInt(0x7FFF0000) + 0x10000);
    }
}


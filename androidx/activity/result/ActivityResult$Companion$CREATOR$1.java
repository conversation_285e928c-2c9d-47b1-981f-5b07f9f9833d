package androidx.activity.result;

import android.content.Intent;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import kotlin.jvm.internal.Intrinsics;

public final class ActivityResult.Companion.CREATOR.1 implements Parcelable.Creator {
    @Override  // android.os.Parcelable$Creator
    public final Object createFromParcel(Parcel parcel0) {
        Intrinsics.f(parcel0, "parcel");
        int v = parcel0.readInt();
        return parcel0.readInt() == 0 ? new ActivityResult(v, null) : new ActivityResult(v, ((Intent)Intent.CREATOR.createFromParcel(parcel0)));
    }

    @Override  // android.os.Parcelable$Creator
    public final Object[] newArray(int v) {
        return new ActivityResult[v];
    }
}


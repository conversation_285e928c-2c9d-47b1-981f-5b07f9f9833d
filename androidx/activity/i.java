package androidx.activity;

import android.content.pm.PackageManager.ApplicationInfoFlags;
import android.content.pm.PackageManager.ResolveInfoFlags;
import android.window.OnBackInvokedCallback;
import android.window.OnBackInvokedDispatcher;
import androidx.appcompat.app.c;

public abstract class i {
    public static PackageManager.ApplicationInfoFlags b() {
        return PackageManager.ApplicationInfoFlags.of(0x80L);
    }

    public static PackageManager.ResolveInfoFlags c() {
        return PackageManager.ResolveInfoFlags.of(0L);
    }

    public static OnBackInvokedCallback e(Object object0) [...] // Inlined contents

    public static OnBackInvokedDispatcher h(Object object0) [...] // Inlined contents

    public static void l(OnBackInvokedDispatcher onBackInvokedDispatcher0, c c0) {
        onBackInvokedDispatcher0.registerOnBackInvokedCallback(1000000, c0);
    }
}


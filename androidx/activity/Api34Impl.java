package androidx.activity;

import android.window.BackEvent;
import kotlin.jvm.internal.Intrinsics;

public final class Api34Impl {
    public static final Api34Impl a;

    static {
        Api34Impl.a = new Api34Impl();  // 初始化器: Ljava/lang/Object;-><init>()V
    }

    public final BackEvent a(float f, float f1, float f2, int v) {
        return new BackEvent(f, f1, f2, v);
    }

    public final float b(BackEvent backEvent0) {
        Intrinsics.f(backEvent0, "backEvent");
        return backEvent0.getProgress();
    }

    public final int c(BackEvent backEvent0) {
        Intrinsics.f(backEvent0, "backEvent");
        return backEvent0.getSwipeEdge();
    }

    public final float d(BackEvent backEvent0) {
        Intrinsics.f(backEvent0, "backEvent");
        return backEvent0.getTouchX();
    }

    public final float e(BackEvent backEvent0) {
        Intrinsics.f(backEvent0, "backEvent");
        return backEvent0.getTouchY();
    }
}


package androidx.activity;

import kotlin.jvm.internal.Intrinsics;

public final class h implements Runnable {
    public final ComponentActivity a;
    public final OnBackPressedDispatcher b;

    public h(ComponentActivity componentActivity0, OnBackPressedDispatcher onBackPressedDispatcher0) {
        this.a = componentActivity0;
        this.b = onBackPressedDispatcher0;
    }

    @Override
    public final void run() {
        Intrinsics.f(this.a, "this$0");
        Intrinsics.f(this.b, "$dispatcher");
        ComponentActivity.access$addObserverForBackInvoker(this.a, this.b);
    }
}


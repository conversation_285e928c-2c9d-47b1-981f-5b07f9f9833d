package androidx.activity;

import android.content.Intent;
import android.content.IntentSender.SendIntentException;
import androidx.activity.result.contract.ActivityResultContract.SynchronousResult;
import androidx.profileinstaller.DeviceProfileWriter;
import androidx.profileinstaller.ProfileInstaller.DiagnosticsCallback;
import kotlin.jvm.internal.Intrinsics;

public final class f implements Runnable {
    public final int a;
    public final Object b;
    public final int c;
    public final Object d;

    public f(Object object0, int v, int v1, Object object1) {
        this.a = v1;
        this.b = object0;
        this.c = v;
        this.d = object1;
    }

    @Override
    public final void run() {
        Object object0 = this.d;
        int v = this.c;
        Object object1 = this.b;
        switch(this.a) {
            case 0: {
                Intrinsics.f(((ComponentActivity.activityResultRegistry.1)object1), "this$0");
                ((ComponentActivity.activityResultRegistry.1)object1).a(v, ((SynchronousResult)object0).a);
                return;
            }
            case 1: {
                Intrinsics.f(((ComponentActivity.activityResultRegistry.1)object1), "this$0");
                Intrinsics.f(((IntentSender.SendIntentException)object0), "$e");
                ((ComponentActivity.activityResultRegistry.1)object1).b(v, 0, new Intent().setAction("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST").putExtra("androidx.activity.result.contract.extra.SEND_INTENT_EXCEPTION", ((IntentSender.SendIntentException)object0)));
                return;
            }
            case 2: {
                ((DeviceProfileWriter)object1).b.a(v, object0);
                return;
            }
            default: {
                ((DiagnosticsCallback)object1).a(v, object0);
            }
        }
    }
}


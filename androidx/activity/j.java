package androidx.activity;

import android.window.OnBackInvokedCallback;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Intrinsics;

public final class j implements OnBackInvokedCallback {
    public final Function0 a;

    public j(Function0 function00) {
        this.a = function00;
    }

    @Override  // android.window.OnBackInvokedCallback
    public final void onBackInvoked() {
        Intrinsics.f(this.a, "$onBackInvoked");
        this.a.invoke();
    }
}


package androidx.activity;

import java.util.ListIterator;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Lambda;

final class OnBackPressedDispatcher.1 extends Lambda implements Function1 {
    public final OnBackPressedDispatcher a;

    public OnBackPressedDispatcher.1(OnBackPressedDispatcher onBackPressedDispatcher0) {
        this.a = onBackPressedDispatcher0;
        super(1);
    }

    @Override  // kotlin.jvm.functions.Function1
    public final Object invoke(Object object0) {
        Object object1 = null;
        Intrinsics.f(((BackEventCompat)object0), "backEvent");
        OnBackPressedDispatcher onBackPressedDispatcher0 = this.a;
        ListIterator listIterator0 = onBackPressedDispatcher0.b.listIterator(onBackPressedDispatcher0.b.size());
        while(listIterator0.hasPrevious()) {
            Object object2 = listIterator0.previous();
            if(((OnBackPressedCallback)object2).isEnabled()) {
                object1 = object2;
                break;
            }
        }
        if(onBackPressedDispatcher0.c != null) {
            onBackPressedDispatcher0.b();
        }
        onBackPressedDispatcher0.c = (OnBackPressedCallback)object1;
        if(((OnBackPressedCallback)object1) != null) {
            ((OnBackPressedCallback)object1).handleOnBackStarted(((BackEventCompat)object0));
        }
        return Unit.a;
    }
}


package androidx.arch.core.internal;

import java.util.Iterator;
import java.util.Map.Entry;
import java.util.WeakHashMap;

public class SafeIterableMap implements Iterable {
    static class AscendingIterator extends ListIterator {
        @Override  // androidx.arch.core.internal.SafeIterableMap$ListIterator
        public final Entry b(Entry safeIterableMap$Entry0) {
            return safeIterableMap$Entry0.d;
        }

        @Override  // androidx.arch.core.internal.SafeIterableMap$ListIterator
        public final Entry c(Entry safeIterableMap$Entry0) {
            return safeIterableMap$Entry0.c;
        }
    }

    static class DescendingIterator extends ListIterator {
        @Override  // androidx.arch.core.internal.SafeIterableMap$ListIterator
        public final Entry b(Entry safeIterableMap$Entry0) {
            return safeIterableMap$Entry0.c;
        }

        @Override  // androidx.arch.core.internal.SafeIterableMap$ListIterator
        public final Entry c(Entry safeIterableMap$Entry0) {
            return safeIterableMap$Entry0.d;
        }
    }

    static class Entry implements Map.Entry {
        public final Object a;
        public final Object b;
        public Entry c;
        public Entry d;

        public Entry(Object object0, Object object1) {
            this.a = object0;
            this.b = object1;
        }

        @Override
        public final boolean equals(Object object0) {
            if(object0 == this) {
                return true;
            }
            return object0 instanceof Entry ? this.a.equals(((Entry)object0).a) && this.b.equals(((Entry)object0).b) : false;
        }

        @Override
        public final Object getKey() {
            return this.a;
        }

        @Override
        public final Object getValue() {
            return this.b;
        }

        @Override
        public final int hashCode() {
            return this.a.hashCode() ^ this.b.hashCode();
        }

        @Override
        public final Object setValue(Object object0) {
            throw new UnsupportedOperationException("An entry modification is not supported");
        }

        @Override
        public final String toString() {
            return this.a + "=" + this.b;
        }
    }

    public class IteratorWithAdditions extends SupportRemove implements Iterator {
        public Entry a;
        public boolean b;
        public final SafeIterableMap c;

        public IteratorWithAdditions() {
            this.b = true;
        }

        @Override  // androidx.arch.core.internal.SafeIterableMap$SupportRemove
        public final void a(Entry safeIterableMap$Entry0) {
            Entry safeIterableMap$Entry1 = this.a;
            if(safeIterableMap$Entry0 == safeIterableMap$Entry1) {
                this.a = safeIterableMap$Entry1.d;
                this.b = safeIterableMap$Entry1.d == null;
            }
        }

        // 去混淆评级： 低(20)
        @Override
        public final boolean hasNext() {
            return this.b ? this.c.a != null : this.a != null && this.a.c != null;
        }

        @Override
        public final Object next() {
            if(this.b) {
                this.b = false;
                this.a = this.c.a;
                return this.a;
            }
            this.a = this.a == null ? null : this.a.c;
            return this.a;
        }
    }

    static abstract class ListIterator extends SupportRemove implements Iterator {
        public Entry a;
        public Entry b;

        public ListIterator(Entry safeIterableMap$Entry0, Entry safeIterableMap$Entry1) {
            this.a = safeIterableMap$Entry1;
            this.b = safeIterableMap$Entry0;
        }

        @Override  // androidx.arch.core.internal.SafeIterableMap$SupportRemove
        public final void a(Entry safeIterableMap$Entry0) {
            Entry safeIterableMap$Entry1 = null;
            if(this.a == safeIterableMap$Entry0 && safeIterableMap$Entry0 == this.b) {
                this.b = null;
                this.a = null;
            }
            Entry safeIterableMap$Entry2 = this.a;
            if(safeIterableMap$Entry2 == safeIterableMap$Entry0) {
                this.a = this.b(safeIterableMap$Entry2);
            }
            Entry safeIterableMap$Entry3 = this.b;
            if(safeIterableMap$Entry3 == safeIterableMap$Entry0) {
                if(safeIterableMap$Entry3 != this.a && this.a != null) {
                    safeIterableMap$Entry1 = this.c(safeIterableMap$Entry3);
                }
                this.b = safeIterableMap$Entry1;
            }
        }

        public abstract Entry b(Entry arg1);

        public abstract Entry c(Entry arg1);

        @Override
        public final boolean hasNext() {
            return this.b != null;
        }

        @Override
        public final Object next() {
            Entry safeIterableMap$Entry0 = this.b;
            this.b = safeIterableMap$Entry0 == this.a || this.a == null ? null : this.c(safeIterableMap$Entry0);
            return safeIterableMap$Entry0;
        }
    }

    public static abstract class SupportRemove {
        public abstract void a(Entry arg1);
    }

    public Entry a;
    public Entry b;
    public final WeakHashMap c;
    public int d;

    public SafeIterableMap() {
        this.c = new WeakHashMap();
        this.d = 0;
    }

    public Entry a(Object object0) {
        Entry safeIterableMap$Entry0;
        for(safeIterableMap$Entry0 = this.a; safeIterableMap$Entry0 != null && !safeIterableMap$Entry0.a.equals(object0); safeIterableMap$Entry0 = safeIterableMap$Entry0.c) {
        }
        return safeIterableMap$Entry0;
    }

    public Object d(Object object0, Object object1) {
        Entry safeIterableMap$Entry0 = this.a(object0);
        if(safeIterableMap$Entry0 != null) {
            return safeIterableMap$Entry0.b;
        }
        Entry safeIterableMap$Entry1 = new Entry(object0, object1);
        ++this.d;
        Entry safeIterableMap$Entry2 = this.b;
        if(safeIterableMap$Entry2 == null) {
            this.a = safeIterableMap$Entry1;
            this.b = safeIterableMap$Entry1;
            return null;
        }
        safeIterableMap$Entry2.c = safeIterableMap$Entry1;
        safeIterableMap$Entry1.d = safeIterableMap$Entry2;
        this.b = safeIterableMap$Entry1;
        return null;
    }

    public final Iterator descendingIterator() {
        Iterator iterator0 = new DescendingIterator(this.b, this.a);  // 初始化器: Landroidx/arch/core/internal/SafeIterableMap$ListIterator;-><init>(Landroidx/arch/core/internal/SafeIterableMap$Entry;Landroidx/arch/core/internal/SafeIterableMap$Entry;)V
        this.c.put(iterator0, Boolean.FALSE);
        return iterator0;
    }

    public Object e(Object object0) {
        Entry safeIterableMap$Entry0 = this.a(object0);
        if(safeIterableMap$Entry0 == null) {
            return null;
        }
        --this.d;
        WeakHashMap weakHashMap0 = this.c;
        if(!weakHashMap0.isEmpty()) {
            for(Object object1: weakHashMap0.keySet()) {
                ((SupportRemove)object1).a(safeIterableMap$Entry0);
            }
        }
        Entry safeIterableMap$Entry1 = safeIterableMap$Entry0.d;
        if(safeIterableMap$Entry1 == null) {
            this.a = safeIterableMap$Entry0.c;
        }
        else {
            safeIterableMap$Entry1.c = safeIterableMap$Entry0.c;
        }
        Entry safeIterableMap$Entry2 = safeIterableMap$Entry0.c;
        if(safeIterableMap$Entry2 == null) {
            this.b = safeIterableMap$Entry1;
        }
        else {
            safeIterableMap$Entry2.d = safeIterableMap$Entry1;
        }
        safeIterableMap$Entry0.c = null;
        safeIterableMap$Entry0.d = null;
        return safeIterableMap$Entry0.b;
    }

    @Override
    public final boolean equals(Object object0) {
        if(object0 == this) {
            return true;
        }
        if(!(object0 instanceof SafeIterableMap)) {
            return false;
        }
        if(this.d != ((SafeIterableMap)object0).d) {
            return false;
        }
        Iterator iterator0 = this.iterator();
        Iterator iterator1 = ((SafeIterableMap)object0).iterator();
        while(((ListIterator)iterator0).hasNext() && ((ListIterator)iterator1).hasNext()) {
            Map.Entry map$Entry0 = (Map.Entry)((ListIterator)iterator0).next();
            Object object1 = ((ListIterator)iterator1).next();
            if(map$Entry0 == null && object1 != null || map$Entry0 != null && !map$Entry0.equals(object1)) {
                return false;
            }
            if(false) {
                break;
            }
        }
        return !((ListIterator)iterator0).hasNext() && !((ListIterator)iterator1).hasNext();
    }

    @Override
    public final int hashCode() {
        Iterator iterator0 = this.iterator();
        int v;
        for(v = 0; ((ListIterator)iterator0).hasNext(); v += ((Map.Entry)((ListIterator)iterator0).next()).hashCode()) {
        }
        return v;
    }

    @Override
    public final Iterator iterator() {
        Iterator iterator0 = new AscendingIterator(this.a, this.b);  // 初始化器: Landroidx/arch/core/internal/SafeIterableMap$ListIterator;-><init>(Landroidx/arch/core/internal/SafeIterableMap$Entry;Landroidx/arch/core/internal/SafeIterableMap$Entry;)V
        this.c.put(iterator0, Boolean.FALSE);
        return iterator0;
    }

    @Override
    public final String toString() {
        StringBuilder stringBuilder0 = new StringBuilder("[");
        Iterator iterator0 = this.iterator();
        while(((ListIterator)iterator0).hasNext()) {
            stringBuilder0.append(((Map.Entry)((ListIterator)iterator0).next()).toString());
            if(((ListIterator)iterator0).hasNext()) {
                stringBuilder0.append(", ");
            }
        }
        stringBuilder0.append("]");
        return stringBuilder0.toString();
    }
}


package androidx.arch.core.internal;

import java.util.HashMap;
import java.util.Map.Entry;

public class FastSafeIterableMap extends SafeIterableMap {
    public final HashMap e;

    public FastSafeIterableMap() {
        this.e = new HashMap();
    }

    @Override  // androidx.arch.core.internal.SafeIterableMap
    public final Entry a(Object object0) {
        return (Entry)this.e.get(object0);
    }

    @Override  // androidx.arch.core.internal.SafeIterableMap
    public final Object d(Object object0, Object object1) {
        Entry safeIterableMap$Entry0 = this.a(object0);
        if(safeIterableMap$Entry0 != null) {
            return safeIterableMap$Entry0.b;
        }
        HashMap hashMap0 = this.e;
        Entry safeIterableMap$Entry1 = new Entry(object0, object1);
        ++this.d;
        Entry safeIterableMap$Entry2 = this.b;
        if(safeIterableMap$Entry2 == null) {
            this.a = safeIterableMap$Entry1;
        }
        else {
            safeIterableMap$Entry2.c = safeIterableMap$Entry1;
            safeIterableMap$Entry1.d = safeIterableMap$Entry2;
        }
        this.b = safeIterableMap$Entry1;
        hashMap0.put(object0, safeIterableMap$Entry1);
        return null;
    }

    @Override  // androidx.arch.core.internal.SafeIterableMap
    public final Object e(Object object0) {
        Object object1 = super.e(object0);
        this.e.remove(object0);
        return object1;
    }

    public final Map.Entry f(Object object0) {
        HashMap hashMap0 = this.e;
        return hashMap0.containsKey(object0) ? ((Entry)hashMap0.get(object0)).d : null;
    }
}


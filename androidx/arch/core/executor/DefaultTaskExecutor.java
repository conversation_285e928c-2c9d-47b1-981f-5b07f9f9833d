package androidx.arch.core.executor;

import android.os.Build.VERSION;
import android.os.Handler.Callback;
import android.os.Handler;
import android.os.Looper;
import java.lang.reflect.InvocationTargetException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

public class DefaultTaskExecutor extends TaskExecutor {
    static abstract class Api28Impl {
        public static Handler a(Looper looper0) {
            return Handler.createAsync(looper0);
        }
    }

    public final Object a;
    public final ExecutorService b;
    public volatile Handler c;

    public DefaultTaskExecutor() {
        this.a = new Object();
        this.b = Executors.newFixedThreadPool(4, new ThreadFactory() {
            public final AtomicInteger a;

            {
                this.a = new AtomicInteger(0);
            }

            @Override
            public final Thread newThread(Runnable runnable0) {
                Thread thread0 = new Thread(runnable0);
                thread0.setName("arch_disk_io_" + this.a.getAndIncrement());
                return thread0;
            }
        });
    }

    public static Handler a(Looper looper0) {
        if(Build.VERSION.SDK_INT >= 28) {
            return Api28Impl.a(looper0);
        }
        try {
            return (Handler)Handler.class.getDeclaredConstructor(Looper.class, Handler.Callback.class, Boolean.TYPE).newInstance(looper0, null, Boolean.TRUE);
        }
        catch(IllegalAccessException | InstantiationException | NoSuchMethodException unused_ex) {
            return new Handler(looper0);
        }
        catch(InvocationTargetException unused_ex) {
            return new Handler(looper0);
        }
    }
}


package androidx.arch.core.executor;

import android.os.Looper;
import m.a;

public class ArchTaskExecutor extends TaskExecutor {
    public final DefaultTaskExecutor a;
    public static volatile ArchTaskExecutor b;
    public static final a c;

    static {
        ArchTaskExecutor.c = new a(1);
    }

    public ArchTaskExecutor() {
        this.a = new DefaultTaskExecutor();
    }

    public static ArchTaskExecutor a() {
        if(ArchTaskExecutor.b != null) {
            return ArchTaskExecutor.b;
        }
        synchronized(ArchTaskExecutor.class) {
            if(ArchTaskExecutor.b == null) {
                ArchTaskExecutor.b = new ArchTaskExecutor();
            }
            return ArchTaskExecutor.b;
        }
    }

    public final boolean b() {
        this.a.getClass();
        return Looper.getMainLooper().getThread() == Thread.currentThread();
    }

    public final void c(Runnable runnable0) {
        DefaultTaskExecutor defaultTaskExecutor0 = this.a;
        if(defaultTaskExecutor0.c == null) {
            synchronized(defaultTaskExecutor0.a) {
                if(defaultTaskExecutor0.c == null) {
                    defaultTaskExecutor0.c = DefaultTaskExecutor.a(Looper.getMainLooper());
                }
            }
        }
        defaultTaskExecutor0.c.post(runnable0);
    }
}


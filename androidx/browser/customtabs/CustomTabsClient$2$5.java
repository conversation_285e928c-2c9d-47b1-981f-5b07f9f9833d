package androidx.browser.customtabs;

import android.net.Uri;
import android.os.Bundle;

class CustomTabsClient.2.5 implements Runnable {
    public final int a;
    public final Uri b;
    public final boolean c;
    public final Bundle d;
    public final CustomTabsClient.2 e;

    public CustomTabsClient.2.5(CustomTabsClient.2 customTabsClient$20, int v, Uri uri0, boolean z, Bundle bundle0) {
        this.e = customTabsClient$20;
        this.a = v;
        this.b = uri0;
        this.c = z;
        this.d = bundle0;
    }

    @Override
    public final void run() {
        this.e.b.onRelationshipValidationResult(this.a, this.b, this.c, this.d);
    }
}


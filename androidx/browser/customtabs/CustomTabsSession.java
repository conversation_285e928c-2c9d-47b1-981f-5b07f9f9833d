package androidx.browser.customtabs;

import android.app.PendingIntent;
import android.content.ComponentName;
import android.os.Bundle;
import android.os.RemoteException;
import android.support.customtabs.ICustomTabsCallback;
import android.support.customtabs.ICustomTabsService;

public final class CustomTabsSession {
    public final Object a;
    public final ICustomTabsService b;
    public final ICustomTabsCallback c;
    public final ComponentName d;
    public final PendingIntent e;

    public CustomTabsSession(ICustomTabsService iCustomTabsService0, ICustomTabsCallback iCustomTabsCallback0, ComponentName componentName0) {
        this.a = new Object();
        this.b = iCustomTabsService0;
        this.c = iCustomTabsCallback0;
        this.d = componentName0;
        this.e = null;
    }

    public final Bundle a() {
        Bundle bundle0 = new Bundle();
        PendingIntent pendingIntent0 = this.e;
        if(pendingIntent0 != null) {
            bundle0.putParcelable("android.support.customtabs.extra.SESSION_ID", pendingIntent0);
        }
        return bundle0;
    }

    public final void b(String s) {
        Bundle bundle0 = this.a();
        synchronized(this.a) {
            try {
                this.b.g(this.c, s, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }
    }
}


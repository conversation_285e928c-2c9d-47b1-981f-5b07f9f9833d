package androidx.browser.customtabs;

import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.customtabs.ICustomTabsCallback.Stub;
import com.google.android.gms.internal.ads.zzbdt;

class CustomTabsClient.2 extends Stub {
    public final Handler a;
    public final CustomTabsCallback b;

    public CustomTabsClient.2(zzbdt zzbdt0) {
        this.b = zzbdt0;
        super();
        this.attachInterface(this, "android.support.customtabs.ICustomTabsCallback");
        this.a = new Handler(Looper.getMainLooper());
    }

    @Override  // android.support.customtabs.ICustomTabsCallback
    public final Bundle d(String s, Bundle bundle0) {
        return this.b == null ? null : this.b.extraCallbackWithResult(s, bundle0);
    }

    @Override  // android.support.customtabs.ICustomTabsCallback
    public final void j(String s, Bundle bundle0) {
        if(this.b == null) {
            return;
        }
        CustomTabsClient.2.2 customTabsClient$2$20 = new CustomTabsClient.2.2(this, s, bundle0);
        this.a.post(customTabsClient$2$20);
    }

    @Override  // android.support.customtabs.ICustomTabsCallback
    public final void k(int v, Bundle bundle0) {
        if(this.b == null) {
            return;
        }
        CustomTabsClient.2.1 customTabsClient$2$10 = new CustomTabsClient.2.1(this, v, bundle0);
        this.a.post(customTabsClient$2$10);
    }

    @Override  // android.support.customtabs.ICustomTabsCallback
    public final void m(String s, Bundle bundle0) {
        if(this.b == null) {
            return;
        }
        CustomTabsClient.2.4 customTabsClient$2$40 = new CustomTabsClient.2.4(this, s, bundle0);
        this.a.post(customTabsClient$2$40);
    }

    @Override  // android.support.customtabs.ICustomTabsCallback
    public final void n(Bundle bundle0) {
        if(this.b == null) {
            return;
        }
        CustomTabsClient.2.3 customTabsClient$2$30 = new CustomTabsClient.2.3(this, bundle0);
        this.a.post(customTabsClient$2$30);
    }

    @Override  // android.support.customtabs.ICustomTabsCallback
    public final void p(int v, Uri uri0, boolean z, Bundle bundle0) {
        if(this.b == null) {
            return;
        }
        CustomTabsClient.2.5 customTabsClient$2$50 = new CustomTabsClient.2.5(this, v, uri0, z, bundle0);
        this.a.post(customTabsClient$2$50);
    }
}


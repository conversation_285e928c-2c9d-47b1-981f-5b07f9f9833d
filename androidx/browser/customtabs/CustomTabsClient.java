package androidx.browser.customtabs;

import android.content.ComponentName;
import android.os.RemoteException;
import android.support.customtabs.ICustomTabsService;
import com.google.android.gms.internal.ads.zzbdt;

public abstract class CustomTabsClient {
    public final ICustomTabsService a;
    public final ComponentName b;

    public CustomTabsClient(ICustomTabsService iCustomTabsService0, ComponentName componentName0) {
        this.a = iCustomTabsService0;
        this.b = componentName0;
    }

    public final CustomTabsSession a(zzbdt zzbdt0) {
        CustomTabsClient.2 customTabsClient$20 = new CustomTabsClient.2(zzbdt0);
        ICustomTabsService iCustomTabsService0 = this.a;
        try {
            if(iCustomTabsService0.h(customTabsClient$20)) {
                return new CustomTabsSession(iCustomTabsService0, customTabsClient$20, this.b);
            }
        }
        catch(RemoteException unused_ex) {
        }
        return null;
    }
}


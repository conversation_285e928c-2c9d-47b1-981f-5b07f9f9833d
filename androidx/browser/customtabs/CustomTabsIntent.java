package androidx.browser.customtabs;

import android.app.PendingIntent;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;

public final class CustomTabsIntent {
    public static final class Builder {
        public final Intent a;
        public final CustomTabColorSchemeParams.Builder b;
        public final boolean c;

        public Builder() {
            this.a = new Intent("android.intent.action.VIEW");
            this.b = new CustomTabColorSchemeParams.Builder();  // 初始化器: Ljava/lang/Object;-><init>()V
            this.c = true;
        }

        public Builder(CustomTabsSession customTabsSession0) {
            Intent intent0 = new Intent("android.intent.action.VIEW");
            this.a = intent0;
            this.b = new CustomTabColorSchemeParams.Builder();  // 初始化器: Ljava/lang/Object;-><init>()V
            this.c = true;
            if(customTabsSession0 != null) {
                intent0.setPackage(customTabsSession0.d.getPackageName());
                IBinder iBinder0 = customTabsSession0.c.asBinder();
                Bundle bundle0 = new Bundle();
                bundle0.putBinder("android.support.customtabs.extra.SESSION", iBinder0);
                PendingIntent pendingIntent0 = customTabsSession0.e;
                if(pendingIntent0 != null) {
                    bundle0.putParcelable("android.support.customtabs.extra.SESSION_ID", pendingIntent0);
                }
                intent0.putExtras(bundle0);
            }
        }

        public final CustomTabsIntent a() {
            Intent intent0 = this.a;
            if(!intent0.hasExtra("android.support.customtabs.extra.SESSION")) {
                Bundle bundle0 = new Bundle();
                bundle0.putBinder("android.support.customtabs.extra.SESSION", null);
                intent0.putExtras(bundle0);
            }
            intent0.putExtra("android.support.customtabs.extra.EXTRA_ENABLE_INSTANT_APPS", this.c);
            this.b.getClass();
            intent0.putExtras(new Bundle());
            intent0.putExtra("androidx.browser.customtabs.extra.SHARE_STATE", 0);
            return new CustomTabsIntent(intent0);
        }
    }

    public final Intent a;

    public CustomTabsIntent(Intent intent0) {
        this.a = intent0;
    }
}


package androidx.core.app;

import android.graphics.Bitmap.Config;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.widget.RemoteViews;
import androidx.core.graphics.drawable.IconCompat;

public abstract class NotificationCompat.Style {
    public NotificationCompat.Builder a;

    public void a(Bundle bundle0) {
        bundle0.putString("androidx.core.app.extra.COMPAT_TEMPLATE", this.e());
    }

    public abstract void b(NotificationBuilderWithBuilderAccessor arg1);

    public final Bitmap c(IconCompat iconCompat0, int v, int v1) {
        Drawable drawable0 = iconCompat0.k(this.a.a);
        int v2 = v1 == 0 ? drawable0.getIntrinsicWidth() : v1;
        if(v1 == 0) {
            v1 = drawable0.getIntrinsicHeight();
        }
        Bitmap bitmap0 = Bitmap.createBitmap(v2, v1, Bitmap.Config.ARGB_8888);
        drawable0.setBounds(0, 0, v2, v1);
        if(v != 0) {
            drawable0.mutate().setColorFilter(new PorterDuffColorFilter(v, PorterDuff.Mode.SRC_IN));
        }
        drawable0.draw(new Canvas(bitmap0));
        return bitmap0;
    }

    public final Bitmap d(int v, int v1, int v2, int v3) {
        if(v3 == 0) {
            v3 = 0;
        }
        Bitmap bitmap0 = this.c(IconCompat.b(0x7F0802D6, this.a.a), v3, v1);  // drawable:notification_icon_background
        Canvas canvas0 = new Canvas(bitmap0);
        Drawable drawable0 = this.a.a.getResources().getDrawable(v).mutate();
        drawable0.setFilterBitmap(true);
        int v4 = (v1 - v2) / 2;
        drawable0.setBounds(v4, v4, v2 + v4, v2 + v4);
        drawable0.setColorFilter(new PorterDuffColorFilter(-1, PorterDuff.Mode.SRC_ATOP));
        drawable0.draw(canvas0);
        return bitmap0;
    }

    public abstract String e();

    public RemoteViews f() {
        return null;
    }

    public RemoteViews g() {
        return null;
    }

    public void h() {
    }
}


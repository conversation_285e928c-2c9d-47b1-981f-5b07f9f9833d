package androidx.core.app;

import android.app.Notification.Builder;
import android.app.Notification;
import android.app.PendingIntent;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.media.AudioAttributes.Builder;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.widget.RemoteViews;
import androidx.core.graphics.drawable.IconCompat;
import java.util.ArrayList;

public class NotificationCompat.Builder {
    static abstract class Api21Impl {
        public static AudioAttributes a(AudioAttributes.Builder audioAttributes$Builder0) {
            return audioAttributes$Builder0.build();
        }

        public static AudioAttributes.Builder b() {
            return new AudioAttributes.Builder();
        }

        public static AudioAttributes.Builder c(AudioAttributes.Builder audioAttributes$Builder0, int v) {
            return audioAttributes$Builder0.setContentType(v);
        }

        public static AudioAttributes.Builder d(AudioAttributes.Builder audioAttributes$Builder0, int v) {
            return audioAttributes$Builder0.setLegacyStreamType(v);
        }

        public static AudioAttributes.Builder e(AudioAttributes.Builder audioAttributes$Builder0, int v) {
            return audioAttributes$Builder0.setUsage(v);
        }
    }

    public final Context a;
    public final ArrayList b;
    public final ArrayList c;
    public final ArrayList d;
    public CharSequence e;
    public CharSequence f;
    public PendingIntent g;
    public IconCompat h;
    public int i;
    public int j;
    public boolean k;
    public NotificationCompat.Style l;
    public String m;
    public boolean n;
    public Bundle o;
    public int p;
    public int q;
    public RemoteViews r;
    public RemoteViews s;
    public String t;
    public final boolean u;
    public final Notification v;
    public final ArrayList w;

    public NotificationCompat.Builder(Context context0, String s) {
        this.b = new ArrayList();
        this.c = new ArrayList();
        this.d = new ArrayList();
        this.k = true;
        this.n = false;
        this.p = 0;
        this.q = 0;
        Notification notification0 = new Notification();
        this.v = notification0;
        this.a = context0;
        this.t = s;
        notification0.when = System.currentTimeMillis();
        notification0.audioStreamType = -1;
        this.j = 0;
        this.w = new ArrayList();
        this.u = true;
    }

    public final Notification a() {
        Notification notification0;
        NotificationCompatBuilder notificationCompatBuilder0 = new NotificationCompatBuilder(this);
        NotificationCompat.Builder notificationCompat$Builder0 = notificationCompatBuilder0.c;
        NotificationCompat.Style notificationCompat$Style0 = notificationCompat$Builder0.l;
        if(notificationCompat$Style0 != null) {
            notificationCompat$Style0.b(notificationCompatBuilder0);
        }
        RemoteViews remoteViews0 = notificationCompat$Style0 == null ? null : notificationCompat$Style0.g();
        int v = Build.VERSION.SDK_INT;
        Notification.Builder notification$Builder0 = notificationCompatBuilder0.b;
        if(v >= 26) {
            notification0 = notification$Builder0.build();
        }
        else if(v >= 24) {
            notification0 = notification$Builder0.build();
        }
        else {
            notification$Builder0.setExtras(notificationCompatBuilder0.f);
            Notification notification1 = notification$Builder0.build();
            RemoteViews remoteViews1 = notificationCompatBuilder0.d;
            if(remoteViews1 != null) {
                notification1.contentView = remoteViews1;
            }
            RemoteViews remoteViews2 = notificationCompatBuilder0.e;
            if(remoteViews2 != null) {
                notification1.bigContentView = remoteViews2;
            }
            notification0 = notification1;
        }
        if(remoteViews0 == null) {
            RemoteViews remoteViews3 = notificationCompat$Builder0.r;
            if(remoteViews3 != null) {
                notification0.contentView = remoteViews3;
            }
        }
        else {
            notification0.contentView = remoteViews0;
        }
        if(notificationCompat$Style0 != null) {
            RemoteViews remoteViews4 = notificationCompat$Style0.f();
            if(remoteViews4 != null) {
                notification0.bigContentView = remoteViews4;
            }
        }
        if(notificationCompat$Style0 != null) {
            notificationCompat$Builder0.l.h();
        }
        if(notificationCompat$Style0 != null) {
            Bundle bundle0 = notification0.extras;
            if(bundle0 != null) {
                notificationCompat$Style0.a(bundle0);
            }
        }
        return notification0;
    }

    public static CharSequence b(CharSequence charSequence0) {
        if(charSequence0 == null) {
            return null;
        }
        return charSequence0.length() <= 0x1400 ? charSequence0 : charSequence0.subSequence(0, 0x1400);
    }

    public final void c(int v, boolean z) {
        Notification notification0 = this.v;
        if(z) {
            notification0.flags |= v;
            return;
        }
        notification0.flags &= ~v;
    }

    public final void d(Bitmap bitmap0) {
        IconCompat iconCompat0;
        if(bitmap0 == null) {
            iconCompat0 = null;
        }
        else {
            if(Build.VERSION.SDK_INT < 27) {
                Resources resources0 = this.a.getResources();
                int v = resources0.getDimensionPixelSize(0x7F07037E);  // dimen:compat_notification_large_icon_max_width
                int v1 = resources0.getDimensionPixelSize(0x7F07037D);  // dimen:compat_notification_large_icon_max_height
                if(bitmap0.getWidth() > v || bitmap0.getHeight() > v1) {
                    double f = Math.min(((double)v) / ((double)Math.max(1, bitmap0.getWidth())), ((double)v1) / ((double)Math.max(1, bitmap0.getHeight())));
                    bitmap0 = Bitmap.createScaledBitmap(bitmap0, ((int)Math.ceil(((double)bitmap0.getWidth()) * f)), ((int)Math.ceil(((double)bitmap0.getHeight()) * f)), true);
                }
            }
            bitmap0.getClass();
            IconCompat iconCompat1 = new IconCompat(1);
            iconCompat1.b = bitmap0;
            iconCompat0 = iconCompat1;
        }
        this.h = iconCompat0;
    }

    public final void e(Uri uri0) {
        this.v.sound = uri0;
        this.v.audioStreamType = -1;
        this.v.audioAttributes = Api21Impl.a(Api21Impl.e(Api21Impl.c(Api21Impl.b(), 4), 5));
    }

    public final void f(NotificationCompat.Style notificationCompat$Style0) {
        if(this.l != notificationCompat$Style0) {
            this.l = notificationCompat$Style0;
            if(notificationCompat$Style0.a != this) {
                notificationCompat$Style0.a = this;
                this.f(notificationCompat$Style0);
            }
        }
    }
}


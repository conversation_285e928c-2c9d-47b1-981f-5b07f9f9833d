package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.widgets.analyzer.BasicMeasure.Measure;
import androidx.constraintlayout.core.widgets.analyzer.BasicMeasure.Measurer;

public abstract class VirtualLayout extends HelperWidget {
    public int A0;
    public Measure B0;
    public Measurer C0;
    public int s0;
    public int t0;
    public int u0;
    public int v0;
    public int w0;
    public int x0;
    public boolean y0;
    public int z0;

    public abstract void T(int arg1, int arg2, int arg3, int arg4);

    public final void U(ConstraintWidget constraintWidget0, DimensionBehaviour constraintWidget$DimensionBehaviour0, int v, DimensionBehaviour constraintWidget$DimensionBehaviour1, int v1) {
        Measurer basicMeasure$Measurer0;
        while((basicMeasure$Measurer0 = this.C0) == null) {
            ConstraintWidget constraintWidget1 = this.U;
            if(constraintWidget1 == null) {
                break;
            }
            this.C0 = ((ConstraintWidgetContainer)constraintWidget1).u0;
        }
        Measure basicMeasure$Measure0 = this.B0;
        basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour0;
        basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour1;
        basicMeasure$Measure0.c = v;
        basicMeasure$Measure0.d = v1;
        basicMeasure$Measurer0.a(constraintWidget0, basicMeasure$Measure0);
        constraintWidget0.P(basicMeasure$Measure0.e);
        constraintWidget0.M(basicMeasure$Measure0.f);
        constraintWidget0.E = basicMeasure$Measure0.h;
        constraintWidget0.J(basicMeasure$Measure0.g);
    }

    @Override  // androidx.constraintlayout.core.widgets.HelperWidget
    public final void a() {
        for(int v = 0; v < this.r0; ++v) {
            ConstraintWidget constraintWidget0 = this.q0[v];
            if(constraintWidget0 != null) {
                constraintWidget0.F = true;
            }
        }
    }
}


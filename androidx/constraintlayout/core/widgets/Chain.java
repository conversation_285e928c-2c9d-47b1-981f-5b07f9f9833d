package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.ArrayRow;
import androidx.constraintlayout.core.LinearSystem;
import androidx.constraintlayout.core.SolverVariable;
import java.util.ArrayList;

public abstract class Chain {
    public static void a(ConstraintWidgetContainer constraintWidgetContainer0, LinearSystem linearSystem0, ArrayList arrayList0, int v) {
        int v11;
        ChainHead[] arr_chainHead1;
        int v27;
        ConstraintWidget constraintWidget30;
        SolverVariable solverVariable13;
        ConstraintWidget constraintWidget29;
        SolverVariable solverVariable12;
        ConstraintAnchor constraintAnchor15;
        ConstraintWidget constraintWidget24;
        ConstraintWidget constraintWidget23;
        ConstraintWidget constraintWidget22;
        SolverVariable solverVariable8;
        ConstraintAnchor constraintAnchor13;
        int v19;
        ConstraintWidget constraintWidget17;
        int v18;
        ChainHead chainHead3;
        ConstraintWidget constraintWidget14;
        ChainHead chainHead1;
        ConstraintWidget constraintWidget13;
        boolean z4;
        ConstraintWidget constraintWidget12;
        ConstraintAnchor[] arr_constraintAnchor1;
        int v13;
        ConstraintWidget constraintWidget11;
        float f2;
        boolean z2;
        int v12;
        int v10;
        int v9;
        ConstraintWidget constraintWidget4;
        int v8;
        int v7;
        int v2;
        ChainHead[] arr_chainHead;
        int v1;
        if(v == 0) {
            v1 = constraintWidgetContainer0.z0;
            arr_chainHead = constraintWidgetContainer0.C0;
            v2 = 0;
        }
        else {
            v1 = constraintWidgetContainer0.A0;
            arr_chainHead = constraintWidgetContainer0.B0;
            v2 = 2;
        }
        int v3 = 0;
        while(v3 < v1) {
            ChainHead chainHead0 = arr_chainHead[v3];
            DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.c;
            ConstraintWidget constraintWidget0 = chainHead0.a;
            SolverVariable solverVariable0 = null;
            if(chainHead0.q) {
                v9 = v3;
                v10 = v1;
            }
            else {
                int v4 = chainHead0.l;
                int v5 = v4 * 2;
                ConstraintWidget constraintWidget1 = constraintWidget0;
                ConstraintWidget constraintWidget2 = constraintWidget1;
                boolean z = false;
                while(!z) {
                    ++chainHead0.i;
                    constraintWidget1.n0[v4] = null;
                    constraintWidget1.m0[v4] = null;
                    ConstraintAnchor[] arr_constraintAnchor = constraintWidget1.Q;
                    if(constraintWidget1.h0 == 8) {
                        v7 = v3;
                        v8 = v1;
                    }
                    else {
                        constraintWidget1.k(v4);
                        arr_constraintAnchor[v5].e();
                        arr_constraintAnchor[v5 + 1].e();
                        arr_constraintAnchor[v5].e();
                        arr_constraintAnchor[v5 + 1].e();
                        if(chainHead0.b == null) {
                            chainHead0.b = constraintWidget1;
                        }
                        chainHead0.d = constraintWidget1;
                        DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidget1.T[v4];
                        if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour0) {
                            int v6 = constraintWidget1.t[v4];
                            if(v6 == 0 || v6 == 3 || v6 == 2) {
                                ++chainHead0.j;
                                float f = constraintWidget1.l0[v4];
                                if(f > 0.0f) {
                                    v7 = v3;
                                    chainHead0.k += f;
                                }
                                else {
                                    v7 = v3;
                                }
                                v8 = v1;
                                if(constraintWidget1.h0 != 8 && constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour0 && (v6 == 0 || v6 == 3)) {
                                    if(f < 0.0f) {
                                        chainHead0.n = true;
                                    }
                                    else {
                                        chainHead0.o = true;
                                    }
                                    if(chainHead0.h == null) {
                                        chainHead0.h = new ArrayList();
                                    }
                                    chainHead0.h.add(constraintWidget1);
                                }
                                if(chainHead0.f == null) {
                                    chainHead0.f = constraintWidget1;
                                }
                                ConstraintWidget constraintWidget3 = chainHead0.g;
                                if(constraintWidget3 != null) {
                                    constraintWidget3.m0[v4] = constraintWidget1;
                                }
                                chainHead0.g = constraintWidget1;
                            }
                            else {
                                v7 = v3;
                                v8 = v1;
                            }
                        }
                    }
                    if(constraintWidget2 != constraintWidget1) {
                        constraintWidget2.n0[v4] = constraintWidget1;
                    }
                    ConstraintAnchor constraintAnchor0 = arr_constraintAnchor[v5 + 1].f;
                    if(constraintAnchor0 == null) {
                        constraintWidget4 = null;
                    }
                    else {
                        constraintWidget4 = constraintAnchor0.d;
                        ConstraintAnchor constraintAnchor1 = constraintWidget4.Q[v5].f;
                        if(constraintAnchor1 == null || constraintAnchor1.d != constraintWidget1) {
                            constraintWidget4 = null;
                        }
                    }
                    if(constraintWidget4 == null) {
                        constraintWidget4 = constraintWidget1;
                        z = true;
                    }
                    constraintWidget2 = constraintWidget1;
                    v3 = v7;
                    v1 = v8;
                    constraintWidget1 = constraintWidget4;
                }
                v9 = v3;
                v10 = v1;
                ConstraintWidget constraintWidget5 = chainHead0.b;
                if(constraintWidget5 != null) {
                    constraintWidget5.Q[v5].e();
                }
                ConstraintWidget constraintWidget6 = chainHead0.d;
                if(constraintWidget6 != null) {
                    constraintWidget6.Q[v5 + 1].e();
                }
                chainHead0.c = constraintWidget1;
                chainHead0.e = v4 != 0 || !chainHead0.m ? constraintWidget0 : constraintWidget1;
                chainHead0.p = chainHead0.o && chainHead0.n;
            }
            chainHead0.q = true;
            if(arrayList0 == null || arrayList0.contains(constraintWidget0)) {
                ConstraintWidget constraintWidget7 = chainHead0.c;
                ConstraintWidget constraintWidget8 = chainHead0.b;
                ConstraintWidget constraintWidget9 = chainHead0.d;
                ConstraintWidget constraintWidget10 = chainHead0.e;
                float f1 = chainHead0.k;
                boolean z1 = constraintWidgetContainer0.T[v] == DimensionBehaviour.b;
                if(v == 0) {
                    v12 = constraintWidget10.j0 == 1 ? 1 : 0;
                    z2 = constraintWidget10.j0 == 2;
                    f2 = f1;
                    constraintWidget11 = constraintWidget0;
                    v13 = constraintWidget10.j0 == 0 ? 1 : 0;
                }
                else {
                    z2 = constraintWidget10.k0 == 2;
                    f2 = f1;
                    constraintWidget11 = constraintWidget0;
                    v13 = constraintWidget10.k0 == 0 ? 1 : 0;
                    v12 = constraintWidget10.k0 == 1 ? 1 : 0;
                }
                boolean z3 = false;
                while(true) {
                    arr_constraintAnchor1 = constraintWidgetContainer0.Q;
                    if(z3) {
                        break;
                    }
                    ConstraintAnchor constraintAnchor2 = constraintWidget11.Q[v2];
                    int v14 = z2 ? 1 : 4;
                    int v15 = constraintAnchor2.e();
                    DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = constraintWidget11.T;
                    if(arr_constraintWidget$DimensionBehaviour[v] != constraintWidget$DimensionBehaviour0 || constraintWidget11.t[v] != 0) {
                        constraintWidget12 = constraintWidget10;
                        z4 = false;
                    }
                    else {
                        constraintWidget12 = constraintWidget10;
                        z4 = true;
                    }
                    ConstraintAnchor constraintAnchor3 = constraintAnchor2.f;
                    if(constraintAnchor3 != null && constraintWidget11 != constraintWidget0) {
                        v15 = constraintAnchor3.e() + v15;
                    }
                    if(!z2 || constraintWidget11 == constraintWidget0 || constraintWidget11 == constraintWidget8) {
                        constraintWidget13 = constraintWidget0;
                    }
                    else {
                        constraintWidget13 = constraintWidget0;
                        v14 = 8;
                    }
                    ConstraintAnchor constraintAnchor4 = constraintAnchor2.f;
                    if(constraintAnchor4 == null) {
                        chainHead1 = chainHead0;
                    }
                    else {
                        if(constraintWidget11 == constraintWidget8) {
                            chainHead1 = chainHead0;
                            linearSystem0.f(constraintAnchor2.i, constraintAnchor4.i, v15, 6);
                        }
                        else {
                            chainHead1 = chainHead0;
                            linearSystem0.f(constraintAnchor2.i, constraintAnchor4.i, v15, 8);
                        }
                        if(z4 && !z2) {
                            v14 = 5;
                        }
                        linearSystem0.e(constraintAnchor2.i, constraintAnchor2.f.i, v15, (constraintWidget11 != constraintWidget8 || !z2 || !constraintWidget11.S[v] ? v14 : 5));
                    }
                    ConstraintAnchor[] arr_constraintAnchor2 = constraintWidget11.Q;
                    if(z1) {
                        if(constraintWidget11.h0 != 8 && arr_constraintWidget$DimensionBehaviour[v] == constraintWidget$DimensionBehaviour0) {
                            linearSystem0.f(arr_constraintAnchor2[v2 + 1].i, arr_constraintAnchor2[v2].i, 0, 5);
                        }
                        linearSystem0.f(arr_constraintAnchor2[v2].i, arr_constraintAnchor1[v2].i, 0, 8);
                    }
                    ConstraintAnchor constraintAnchor5 = arr_constraintAnchor2[v2 + 1].f;
                    if(constraintAnchor5 == null) {
                        constraintWidget14 = null;
                    }
                    else {
                        constraintWidget14 = constraintAnchor5.d;
                        ConstraintAnchor constraintAnchor6 = constraintWidget14.Q[v2].f;
                        if(constraintAnchor6 == null || constraintAnchor6.d != constraintWidget11) {
                            constraintWidget14 = null;
                        }
                    }
                    if(constraintWidget14 == null) {
                        z3 = true;
                    }
                    else {
                        constraintWidget11 = constraintWidget14;
                    }
                    constraintWidget0 = constraintWidget13;
                    constraintWidget10 = constraintWidget12;
                    chainHead0 = chainHead1;
                }
                arr_chainHead1 = arr_chainHead;
                if(constraintWidget9 != null && constraintWidget7.Q[v2 + 1].f != null) {
                    ConstraintAnchor constraintAnchor7 = constraintWidget9.Q[v2 + 1];
                    if(constraintWidget9.T[v] == constraintWidget$DimensionBehaviour0 && constraintWidget9.t[v] == 0 && !z2) {
                        ConstraintAnchor constraintAnchor8 = constraintAnchor7.f;
                        if(constraintAnchor8.d == constraintWidgetContainer0) {
                            linearSystem0.e(constraintAnchor7.i, constraintAnchor8.i, -constraintAnchor7.e(), 5);
                        }
                    }
                    else if(z2) {
                        ConstraintAnchor constraintAnchor9 = constraintAnchor7.f;
                        if(constraintAnchor9.d == constraintWidgetContainer0) {
                            linearSystem0.e(constraintAnchor7.i, constraintAnchor9.i, -constraintAnchor7.e(), 4);
                        }
                    }
                    linearSystem0.g(constraintAnchor7.i, constraintWidget7.Q[v2 + 1].f.i, -constraintAnchor7.e(), 6);
                }
                if(z1) {
                    SolverVariable solverVariable1 = arr_constraintAnchor1[v2 + 1].i;
                    ConstraintAnchor constraintAnchor10 = constraintWidget7.Q[v2 + 1];
                    linearSystem0.f(solverVariable1, constraintAnchor10.i, constraintAnchor10.e(), 8);
                }
                ChainHead chainHead2 = chainHead0;
                ArrayList arrayList1 = chainHead2.h;
                if(arrayList1 != null) {
                    int v16 = arrayList1.size();
                    if(v16 > 1) {
                        float f3 = !chainHead2.n || chainHead2.p ? f2 : ((float)chainHead2.j);
                        ConstraintWidget constraintWidget15 = null;
                        float f4 = 0.0f;
                        int v17 = 0;
                        while(v17 < v16) {
                            ConstraintWidget constraintWidget16 = (ConstraintWidget)arrayList1.get(v17);
                            float f5 = constraintWidget16.l0[v];
                            ConstraintAnchor[] arr_constraintAnchor3 = constraintWidget16.Q;
                            if(f5 < 0.0f) {
                                if(chainHead2.p) {
                                    linearSystem0.e(arr_constraintAnchor3[v2 + 1].i, arr_constraintAnchor3[v2].i, 0, 4);
                                    chainHead3 = chainHead2;
                                    v18 = v16;
                                    goto label_253;
                                }
                                else {
                                    f5 = 1.0f;
                                }
                            }
                            if(Float.compare(f5, 0.0f) == 0) {
                                linearSystem0.e(arr_constraintAnchor3[v2 + 1].i, arr_constraintAnchor3[v2].i, 0, 8);
                                chainHead3 = chainHead2;
                                v18 = v16;
                            }
                            else {
                                if(constraintWidget15 == null) {
                                    chainHead3 = chainHead2;
                                    v18 = v16;
                                    constraintWidget17 = constraintWidget16;
                                }
                                else {
                                    SolverVariable solverVariable2 = constraintWidget15.Q[v2].i;
                                    SolverVariable solverVariable3 = constraintWidget15.Q[v2 + 1].i;
                                    v18 = v16;
                                    SolverVariable solverVariable4 = arr_constraintAnchor3[v2].i;
                                    SolverVariable solverVariable5 = arr_constraintAnchor3[v2 + 1].i;
                                    constraintWidget17 = constraintWidget16;
                                    ArrayRow arrayRow0 = linearSystem0.l();
                                    arrayRow0.b = 0.0f;
                                    chainHead3 = chainHead2;
                                    if(f3 == 0.0f || f4 == f5) {
                                        arrayRow0.d.d(solverVariable2, 1.0f);
                                        arrayRow0.d.d(solverVariable3, -1.0f);
                                        arrayRow0.d.d(solverVariable5, 1.0f);
                                        arrayRow0.d.d(solverVariable4, -1.0f);
                                    }
                                    else if(f4 == 0.0f) {
                                        arrayRow0.d.d(solverVariable2, 1.0f);
                                        arrayRow0.d.d(solverVariable3, -1.0f);
                                    }
                                    else {
                                        float f6 = f4 / f3 / (f5 / f3);
                                        arrayRow0.d.d(solverVariable2, 1.0f);
                                        arrayRow0.d.d(solverVariable3, -1.0f);
                                        arrayRow0.d.d(solverVariable5, f6);
                                        arrayRow0.d.d(solverVariable4, -f6);
                                    }
                                    linearSystem0.c(arrayRow0);
                                }
                                f4 = f5;
                                constraintWidget15 = constraintWidget17;
                            }
                        label_253:
                            ++v17;
                            chainHead2 = chainHead3;
                            v16 = v18;
                        }
                    }
                }
                if(constraintWidget8 != null && (constraintWidget8 == constraintWidget9 || z2)) {
                    ConstraintAnchor constraintAnchor22 = constraintWidget0.Q[v2];
                    ConstraintAnchor constraintAnchor23 = constraintWidget7.Q[v2 + 1];
                    SolverVariable solverVariable14 = constraintAnchor22.f == null ? null : constraintAnchor22.f.i;
                    SolverVariable solverVariable15 = constraintAnchor23.f == null ? null : constraintAnchor23.f.i;
                    ConstraintAnchor constraintAnchor24 = constraintWidget8.Q[v2];
                    if(constraintWidget9 != null) {
                        constraintAnchor23 = constraintWidget9.Q[v2 + 1];
                    }
                    if(solverVariable14 == null || solverVariable15 == null) {
                        v19 = v9;
                    }
                    else {
                        float f7 = v == 0 ? constraintWidget10.e0 : constraintWidget10.f0;
                        int v28 = constraintAnchor24.e();
                        int v29 = constraintAnchor23.e();
                        v19 = v9;
                        linearSystem0.b(constraintAnchor24.i, solverVariable14, v28, f7, solverVariable15, constraintAnchor23.i, v29, 7);
                    }
                    v11 = v19;
                }
                else {
                    v19 = v9;
                    ConstraintWidget constraintWidget18 = constraintWidget0;
                    if(v13 != 0 && constraintWidget8 != null) {
                        boolean z5 = chainHead2.j > 0 && chainHead2.i == chainHead2.j;
                        ConstraintWidget constraintWidget19 = constraintWidget8;
                        for(ConstraintWidget constraintWidget20 = constraintWidget19; constraintWidget20 != null; constraintWidget20 = constraintWidget23) {
                            ConstraintWidget constraintWidget21;
                            for(constraintWidget21 = constraintWidget20.n0[v]; constraintWidget21 != null && constraintWidget21.h0 == 8; constraintWidget21 = constraintWidget21.n0[v]) {
                            }
                            if(constraintWidget21 == null && constraintWidget20 != constraintWidget9) {
                                constraintWidget23 = constraintWidget21;
                                constraintWidget22 = constraintWidget19;
                                constraintWidget24 = constraintWidget20;
                            }
                            else {
                                ConstraintAnchor[] arr_constraintAnchor4 = constraintWidget20.Q;
                                ConstraintAnchor constraintAnchor11 = arr_constraintAnchor4[v2];
                                SolverVariable solverVariable6 = constraintAnchor11.i;
                                SolverVariable solverVariable7 = constraintAnchor11.f == null ? null : constraintAnchor11.f.i;
                                if(constraintWidget19 != constraintWidget20) {
                                    solverVariable7 = constraintWidget19.Q[v2 + 1].i;
                                }
                                else if(constraintWidget20 == constraintWidget8) {
                                    ConstraintAnchor constraintAnchor12 = constraintWidget18.Q[v2].f;
                                    solverVariable7 = constraintAnchor12 == null ? null : constraintAnchor12.i;
                                }
                                int v20 = constraintAnchor11.e();
                                int v21 = arr_constraintAnchor4[v2 + 1].e();
                                if(constraintWidget21 == null) {
                                    constraintAnchor13 = constraintWidget7.Q[v2 + 1].f;
                                    solverVariable8 = constraintAnchor13 == null ? null : constraintAnchor13.i;
                                }
                                else {
                                    constraintAnchor13 = constraintWidget21.Q[v2];
                                    solverVariable8 = constraintAnchor13.i;
                                }
                                SolverVariable solverVariable9 = arr_constraintAnchor4[v2 + 1].i;
                                if(constraintAnchor13 != null) {
                                    v21 = constraintAnchor13.e() + v21;
                                }
                                int v22 = constraintWidget19.Q[v2 + 1].e() + v20;
                                if(solverVariable6 != null && solverVariable7 != null && solverVariable8 != null && solverVariable9 != null) {
                                    if(constraintWidget20 == constraintWidget8) {
                                        v22 = constraintWidget8.Q[v2].e();
                                    }
                                    if(constraintWidget20 == constraintWidget9) {
                                        v21 = constraintWidget9.Q[v2 + 1].e();
                                    }
                                    constraintWidget22 = constraintWidget19;
                                    constraintWidget23 = constraintWidget21;
                                    constraintWidget24 = constraintWidget20;
                                    linearSystem0.b(solverVariable6, solverVariable7, v22, 0.5f, solverVariable8, solverVariable9, v21, (z5 ? 8 : 5));
                                }
                            }
                            constraintWidget19 = constraintWidget24.h0 == 8 ? constraintWidget22 : constraintWidget24;
                        }
                        v11 = v19;
                    }
                    else if(v12 == 0 || constraintWidget8 == null) {
                        v11 = v19;
                    }
                    else {
                        boolean z6 = chainHead2.j > 0 && chainHead2.i == chainHead2.j;
                        ConstraintWidget constraintWidget25 = constraintWidget8;
                        ConstraintWidget constraintWidget26 = constraintWidget25;
                        while(constraintWidget26 != null) {
                            ConstraintWidget constraintWidget27;
                            for(constraintWidget27 = constraintWidget26.n0[v]; constraintWidget27 != null && constraintWidget27.h0 == 8; constraintWidget27 = constraintWidget27.n0[v]) {
                            }
                            if(constraintWidget26 == constraintWidget8 || constraintWidget26 == constraintWidget9 || constraintWidget27 == null) {
                                constraintWidget30 = constraintWidget25;
                                v27 = v19;
                            }
                            else {
                                ConstraintWidget constraintWidget28 = constraintWidget27 == constraintWidget9 ? null : constraintWidget27;
                                ConstraintAnchor[] arr_constraintAnchor5 = constraintWidget26.Q;
                                ConstraintAnchor constraintAnchor14 = arr_constraintAnchor5[v2];
                                SolverVariable solverVariable10 = constraintAnchor14.i;
                                SolverVariable solverVariable11 = constraintWidget25.Q[v2 + 1].i;
                                int v23 = constraintAnchor14.e();
                                int v24 = arr_constraintAnchor5[v2 + 1].e();
                                if(constraintWidget28 == null) {
                                    constraintWidget29 = null;
                                    ConstraintAnchor constraintAnchor17 = constraintWidget9.Q[v2];
                                    solverVariable12 = constraintAnchor17 == null ? null : constraintAnchor17.i;
                                    solverVariable13 = arr_constraintAnchor5[v2 + 1].i;
                                    constraintAnchor15 = constraintAnchor17;
                                }
                                else {
                                    constraintAnchor15 = constraintWidget28.Q[v2];
                                    solverVariable12 = constraintAnchor15.i;
                                    constraintWidget29 = constraintWidget28;
                                    ConstraintAnchor constraintAnchor16 = constraintAnchor15.f;
                                    solverVariable13 = constraintAnchor16 == null ? null : constraintAnchor16.i;
                                }
                                int v25 = constraintAnchor15 == null ? v24 : constraintAnchor15.e() + v24;
                                int v26 = constraintWidget25.Q[v2 + 1].e() + v23;
                                if(solverVariable10 == null || solverVariable11 == null || solverVariable12 == null || solverVariable13 == null) {
                                    constraintWidget30 = constraintWidget25;
                                    v27 = v19;
                                }
                                else {
                                    constraintWidget30 = constraintWidget25;
                                    v27 = v19;
                                    linearSystem0.b(solverVariable10, solverVariable11, v26, 0.5f, solverVariable12, solverVariable13, v25, (z6 ? 8 : 4));
                                }
                                constraintWidget27 = constraintWidget29;
                            }
                            constraintWidget25 = constraintWidget26.h0 == 8 ? constraintWidget30 : constraintWidget26;
                            constraintWidget26 = constraintWidget27;
                            v19 = v27;
                        }
                        v11 = v19;
                        ConstraintAnchor constraintAnchor18 = constraintWidget8.Q[v2];
                        ConstraintAnchor constraintAnchor19 = constraintWidget18.Q[v2].f;
                        ConstraintAnchor constraintAnchor20 = constraintWidget9.Q[v2 + 1];
                        ConstraintAnchor constraintAnchor21 = constraintWidget7.Q[v2 + 1].f;
                        if(constraintAnchor19 != null) {
                            if(constraintWidget8 != constraintWidget9) {
                                linearSystem0.e(constraintAnchor18.i, constraintAnchor19.i, constraintAnchor18.e(), 5);
                            }
                            else if(constraintAnchor21 != null) {
                                linearSystem0.b(constraintAnchor18.i, constraintAnchor19.i, constraintAnchor18.e(), 0.5f, constraintAnchor20.i, constraintAnchor21.i, constraintAnchor20.e(), 5);
                            }
                        }
                        if(constraintAnchor21 != null && constraintWidget8 != constraintWidget9) {
                            linearSystem0.e(constraintAnchor20.i, constraintAnchor21.i, -constraintAnchor20.e(), 5);
                        }
                    }
                }
                if((v13 != 0 || v12 != 0) && constraintWidget8 != null && constraintWidget8 != constraintWidget9) {
                    ConstraintAnchor[] arr_constraintAnchor6 = constraintWidget8.Q;
                    ConstraintAnchor constraintAnchor25 = arr_constraintAnchor6[v2];
                    if(constraintWidget9 == null) {
                        constraintWidget9 = constraintWidget8;
                    }
                    ConstraintAnchor[] arr_constraintAnchor7 = constraintWidget9.Q;
                    ConstraintAnchor constraintAnchor26 = arr_constraintAnchor7[v2 + 1];
                    SolverVariable solverVariable16 = constraintAnchor25.f == null ? null : constraintAnchor25.f.i;
                    SolverVariable solverVariable17 = constraintAnchor26.f == null ? null : constraintAnchor26.f.i;
                    if(constraintWidget7 != constraintWidget9) {
                        ConstraintAnchor constraintAnchor27 = constraintWidget7.Q[v2 + 1].f;
                        if(constraintAnchor27 != null) {
                            solverVariable0 = constraintAnchor27.i;
                        }
                        solverVariable17 = solverVariable0;
                    }
                    if(constraintWidget8 == constraintWidget9) {
                        constraintAnchor26 = arr_constraintAnchor6[v2 + 1];
                    }
                    if(solverVariable16 != null && solverVariable17 != null) {
                        int v30 = constraintAnchor25.e();
                        int v31 = arr_constraintAnchor7[v2 + 1].e();
                        linearSystem0.b(constraintAnchor25.i, solverVariable16, v30, 0.5f, solverVariable17, constraintAnchor26.i, v31, 5);
                    }
                }
            }
            else {
                arr_chainHead1 = arr_chainHead;
                v11 = v9;
            }
            v3 = v11 + 1;
            v1 = v10;
            arr_chainHead = arr_chainHead1;
        }
    }
}


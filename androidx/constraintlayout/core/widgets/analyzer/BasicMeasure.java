package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.ConstraintWidget.DimensionBehaviour;
import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.ConstraintWidgetContainer;
import java.util.ArrayList;

public class BasicMeasure {
    public static class Measure {
        public DimensionBehaviour a;
        public DimensionBehaviour b;
        public int c;
        public int d;
        public int e;
        public int f;
        public int g;
        public boolean h;
        public boolean i;
        public int j;

    }

    public interface Measurer {
        void a(ConstraintWidget arg1, Measure arg2);
    }

    public final ArrayList a;
    public final Measure b;
    public final ConstraintWidgetContainer c;

    public BasicMeasure(ConstraintWidgetContainer constraintWidgetContainer0) {
        this.a = new ArrayList();
        this.b = new Measure();  // 初始化器: Ljava/lang/Object;-><init>()V
        this.c = constraintWidgetContainer0;
    }

    public final boolean a(int v, ConstraintWidget constraintWidget0, Measurer basicMeasure$Measurer0) {
        Measure basicMeasure$Measure0 = this.b;
        basicMeasure$Measure0.a = constraintWidget0.T[0];
        basicMeasure$Measure0.b = constraintWidget0.T[1];
        basicMeasure$Measure0.c = constraintWidget0.r();
        basicMeasure$Measure0.d = constraintWidget0.l();
        basicMeasure$Measure0.i = false;
        basicMeasure$Measure0.j = v;
        boolean z = basicMeasure$Measure0.b == DimensionBehaviour.c && constraintWidget0.X > 0.0f;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.a;
        int[] arr_v = constraintWidget0.t;
        if(basicMeasure$Measure0.a == DimensionBehaviour.c && constraintWidget0.X > 0.0f && arr_v[0] == 4) {
            basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour0;
        }
        if(z && arr_v[1] == 4) {
            basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour0;
        }
        basicMeasure$Measurer0.a(constraintWidget0, basicMeasure$Measure0);
        constraintWidget0.P(basicMeasure$Measure0.e);
        constraintWidget0.M(basicMeasure$Measure0.f);
        constraintWidget0.E = basicMeasure$Measure0.h;
        constraintWidget0.J(basicMeasure$Measure0.g);
        basicMeasure$Measure0.j = 0;
        return basicMeasure$Measure0.i;
    }

    public final void b(ConstraintWidgetContainer constraintWidgetContainer0, int v, int v1, int v2) {
        int v3 = constraintWidgetContainer0.c0;
        int v4 = constraintWidgetContainer0.d0;
        constraintWidgetContainer0.c0 = 0;
        constraintWidgetContainer0.d0 = 0;
        constraintWidgetContainer0.P(v1);
        constraintWidgetContainer0.M(v2);
        constraintWidgetContainer0.c0 = v3 < 0 ? 0 : v3;
        constraintWidgetContainer0.d0 = v4 < 0 ? 0 : v4;
        this.c.t0 = v;
        this.c.S();
    }

    public final void c(ConstraintWidgetContainer constraintWidgetContainer0) {
        ArrayList arrayList0 = this.a;
        arrayList0.clear();
        int v = constraintWidgetContainer0.q0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ConstraintWidget constraintWidget0 = (ConstraintWidget)constraintWidgetContainer0.q0.get(v1);
            if(constraintWidget0.T[0] == DimensionBehaviour.c || constraintWidget0.T[1] == DimensionBehaviour.c) {
                arrayList0.add(constraintWidget0);
            }
        }
        constraintWidgetContainer0.s0.b = true;
    }
}


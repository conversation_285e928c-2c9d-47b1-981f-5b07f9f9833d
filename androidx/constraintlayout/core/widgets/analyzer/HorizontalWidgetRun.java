package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.ConstraintAnchor;
import androidx.constraintlayout.core.widgets.ConstraintWidget.DimensionBehaviour;
import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.Helper;
import java.util.ArrayList;

public class HorizontalWidgetRun extends WidgetRun {
    public static final int[] k;

    static {
        HorizontalWidgetRun.k = new int[2];
    }

    public HorizontalWidgetRun(ConstraintWidget constraintWidget0) {
        super(constraintWidget0);
        this.h.e = Type.d;
        this.i.e = Type.e;
        this.f = 0;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.Dependency
    public final void a(Dependency dependency0) {
        int v;
        if(this.j.ordinal() != 3) {
            DimensionDependency dimensionDependency0 = this.e;
            DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.c;
            DependencyNode dependencyNode0 = this.h;
            DependencyNode dependencyNode1 = this.i;
            if(!dimensionDependency0.j && this.d == constraintWidget$DimensionBehaviour0) {
                ConstraintWidget constraintWidget0 = this.b;
                switch(constraintWidget0.r) {
                    case 2: {
                        ConstraintWidget constraintWidget1 = constraintWidget0.U;
                        if(constraintWidget1 != null) {
                            DimensionDependency dimensionDependency1 = constraintWidget1.d.e;
                            if(dimensionDependency1.j) {
                                dimensionDependency0.d(((int)(((float)dimensionDependency1.g) * constraintWidget0.w + 0.5f)));
                            }
                        }
                        break;
                    }
                    case 3: {
                        if(constraintWidget0.s == 0 || constraintWidget0.s == 3) {
                            DependencyNode dependencyNode2 = constraintWidget0.e.h;
                            DependencyNode dependencyNode3 = constraintWidget0.e.i;
                            boolean z = constraintWidget0.I.f != null;
                            boolean z1 = constraintWidget0.J.f != null;
                            boolean z2 = constraintWidget0.K.f != null;
                            boolean z3 = constraintWidget0.L.f != null;
                            int v1 = constraintWidget0.Y;
                            if(z && z1 && z2 && z3) {
                                float f = constraintWidget0.X;
                                int[] arr_v = HorizontalWidgetRun.k;
                                if(dependencyNode2.j && dependencyNode3.j) {
                                    if(dependencyNode0.c && dependencyNode1.c) {
                                        HorizontalWidgetRun.m(arr_v, ((DependencyNode)dependencyNode0.l.get(0)).g + dependencyNode0.f, ((DependencyNode)dependencyNode1.l.get(0)).g - dependencyNode1.f, dependencyNode2.g + dependencyNode2.f, dependencyNode3.g - dependencyNode3.f, f, v1);
                                        dimensionDependency0.d(arr_v[0]);
                                        this.b.e.e.d(arr_v[1]);
                                    }
                                    return;
                                }
                                ArrayList arrayList0 = dependencyNode2.l;
                                if(dependencyNode0.j && dependencyNode1.j) {
                                    if(dependencyNode2.c && dependencyNode3.c) {
                                        HorizontalWidgetRun.m(arr_v, dependencyNode0.g + dependencyNode0.f, dependencyNode1.g - dependencyNode1.f, ((DependencyNode)arrayList0.get(0)).g + dependencyNode2.f, ((DependencyNode)dependencyNode3.l.get(0)).g - dependencyNode3.f, f, v1);
                                        dimensionDependency0.d(arr_v[0]);
                                        this.b.e.e.d(arr_v[1]);
                                        goto label_50;
                                    }
                                    return;
                                }
                            label_50:
                                if(dependencyNode0.c && dependencyNode1.c && dependencyNode2.c && dependencyNode3.c) {
                                    HorizontalWidgetRun.m(arr_v, ((DependencyNode)dependencyNode0.l.get(0)).g + dependencyNode0.f, ((DependencyNode)dependencyNode1.l.get(0)).g - dependencyNode1.f, ((DependencyNode)arrayList0.get(0)).g + dependencyNode2.f, ((DependencyNode)dependencyNode3.l.get(0)).g - dependencyNode3.f, f, v1);
                                    dimensionDependency0.d(arr_v[0]);
                                    this.b.e.e.d(arr_v[1]);
                                    break;
                                }
                                return;
                            }
                            else if(z && z2) {
                                if(!dependencyNode0.c || !dependencyNode1.c) {
                                    return;
                                }
                                float f1 = constraintWidget0.X;
                                int v2 = ((DependencyNode)dependencyNode0.l.get(0)).g + dependencyNode0.f;
                                int v3 = ((DependencyNode)dependencyNode1.l.get(0)).g - dependencyNode1.f;
                                switch(v1) {
                                    case -1: 
                                    case 0: {
                                        int v4 = this.g(v3 - v2, 0);
                                        int v5 = (int)(((float)v4) * f1 + 0.5f);
                                        int v6 = this.g(v5, 1);
                                        if(v5 != v6) {
                                            v4 = (int)(((float)v6) / f1 + 0.5f);
                                        }
                                        dimensionDependency0.d(v4);
                                        this.b.e.e.d(v6);
                                        break;
                                    }
                                    case 1: {
                                        int v7 = this.g(v3 - v2, 0);
                                        int v8 = (int)(((float)v7) / f1 + 0.5f);
                                        int v9 = this.g(v8, 1);
                                        if(v8 != v9) {
                                            v7 = (int)(((float)v9) * f1 + 0.5f);
                                        }
                                        dimensionDependency0.d(v7);
                                        this.b.e.e.d(v9);
                                        break;
                                    }
                                }
                            }
                            else if(z1 && z3) {
                                if(!dependencyNode2.c || !dependencyNode3.c) {
                                    return;
                                }
                                float f2 = constraintWidget0.X;
                                int v10 = ((DependencyNode)dependencyNode2.l.get(0)).g + dependencyNode2.f;
                                int v11 = ((DependencyNode)dependencyNode3.l.get(0)).g - dependencyNode3.f;
                                switch(v1) {
                                    case 0: {
                                        int v15 = this.g(v11 - v10, 1);
                                        int v16 = (int)(((float)v15) * f2 + 0.5f);
                                        int v17 = this.g(v16, 0);
                                        if(v16 != v17) {
                                            v15 = (int)(((float)v17) / f2 + 0.5f);
                                        }
                                        dimensionDependency0.d(v17);
                                        this.b.e.e.d(v15);
                                        break;
                                    }
                                    case -1: 
                                    case 1: {
                                        int v12 = this.g(v11 - v10, 1);
                                        int v13 = (int)(((float)v12) / f2 + 0.5f);
                                        int v14 = this.g(v13, 0);
                                        if(v13 != v14) {
                                            v12 = (int)(((float)v14) * f2 + 0.5f);
                                        }
                                        dimensionDependency0.d(v14);
                                        this.b.e.e.d(v12);
                                    }
                                }
                            }
                        }
                        else {
                            switch(constraintWidget0.Y) {
                                case -1: {
                                    v = (int)(((float)constraintWidget0.e.e.g) * constraintWidget0.X + 0.5f);
                                    break;
                                }
                                case 0: {
                                    v = (int)(((float)constraintWidget0.e.e.g) / constraintWidget0.X + 0.5f);
                                    break;
                                }
                                case 1: {
                                    v = (int)(((float)constraintWidget0.e.e.g) * constraintWidget0.X + 0.5f);
                                    break;
                                }
                                default: {
                                    v = 0;
                                }
                            }
                            dimensionDependency0.d(v);
                        }
                    }
                }
            }
            if(!dependencyNode0.c || !dependencyNode1.c || dependencyNode0.j && dependencyNode1.j && dimensionDependency0.j) {
                return;
            }
            if(!dimensionDependency0.j && this.d == constraintWidget$DimensionBehaviour0 && (this.b.r == 0 && !this.b.y())) {
                DependencyNode dependencyNode4 = (DependencyNode)dependencyNode0.l.get(0);
                DependencyNode dependencyNode5 = (DependencyNode)dependencyNode1.l.get(0);
                int v18 = dependencyNode4.g + dependencyNode0.f;
                int v19 = dependencyNode5.g + dependencyNode1.f;
                dependencyNode0.d(v18);
                dependencyNode1.d(v19);
                dimensionDependency0.d(v19 - v18);
                return;
            }
            if(!dimensionDependency0.j && this.d == constraintWidget$DimensionBehaviour0 && this.a == 1 && dependencyNode0.l.size() > 0 && dependencyNode1.l.size() > 0) {
                DependencyNode dependencyNode6 = (DependencyNode)dependencyNode0.l.get(0);
                int v20 = Math.min(((DependencyNode)dependencyNode1.l.get(0)).g + dependencyNode1.f - (dependencyNode6.g + dependencyNode0.f), dimensionDependency0.m);
                int v21 = this.b.v;
                dimensionDependency0.d((v21 <= 0 ? Math.max(this.b.u, v20) : Math.min(v21, Math.max(this.b.u, v20))));
            }
            if(!dimensionDependency0.j) {
                return;
            }
            DependencyNode dependencyNode7 = (DependencyNode)dependencyNode0.l.get(0);
            DependencyNode dependencyNode8 = (DependencyNode)dependencyNode1.l.get(0);
            int v22 = dependencyNode7.g;
            int v23 = dependencyNode0.f + v22;
            int v24 = dependencyNode8.g;
            int v25 = dependencyNode1.f + v24;
            float f3 = this.b.e0;
            if(dependencyNode7 == dependencyNode8) {
                f3 = 0.5f;
            }
            else {
                v22 = v23;
                v24 = v25;
            }
            dependencyNode0.d(((int)(((float)(v24 - v22 - dimensionDependency0.g)) * f3 + (((float)v22) + 0.5f))));
            dependencyNode1.d(dependencyNode0.g + dimensionDependency0.g);
            return;
        }
        this.l(this.b.I, this.b.K, 0);
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void d() {
        ConstraintWidget constraintWidget0 = this.b;
        DimensionDependency dimensionDependency0 = this.e;
        if(constraintWidget0.a) {
            dimensionDependency0.d(constraintWidget0.r());
        }
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.d;
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = DimensionBehaviour.c;
        DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.a;
        DependencyNode dependencyNode0 = this.i;
        DependencyNode dependencyNode1 = this.h;
        if(!dimensionDependency0.j) {
            ConstraintWidget constraintWidget1 = this.b;
            DimensionBehaviour constraintWidget$DimensionBehaviour3 = constraintWidget1.T[0];
            this.d = constraintWidget$DimensionBehaviour3;
            if(constraintWidget$DimensionBehaviour3 != constraintWidget$DimensionBehaviour1) {
                if(constraintWidget$DimensionBehaviour3 == constraintWidget$DimensionBehaviour0) {
                    ConstraintWidget constraintWidget2 = constraintWidget1.U;
                    if(constraintWidget2 != null) {
                        DimensionBehaviour constraintWidget$DimensionBehaviour4 = constraintWidget2.T[0];
                        if(constraintWidget$DimensionBehaviour4 == constraintWidget$DimensionBehaviour2 || constraintWidget$DimensionBehaviour4 == constraintWidget$DimensionBehaviour0) {
                            int v = constraintWidget2.r();
                            int v1 = this.b.I.e();
                            int v2 = this.b.K.e();
                            WidgetRun.b(dependencyNode1, constraintWidget2.d.h, this.b.I.e());
                            WidgetRun.b(dependencyNode0, constraintWidget2.d.i, -this.b.K.e());
                            dimensionDependency0.d(v - v1 - v2);
                            return;
                        }
                    }
                }
                if(constraintWidget$DimensionBehaviour3 == constraintWidget$DimensionBehaviour2) {
                    dimensionDependency0.d(constraintWidget1.r());
                }
            }
        }
        else if(this.d == constraintWidget$DimensionBehaviour0) {
            ConstraintWidget constraintWidget3 = this.b;
            ConstraintWidget constraintWidget4 = constraintWidget3.U;
            if(constraintWidget4 != null) {
                DimensionBehaviour constraintWidget$DimensionBehaviour5 = constraintWidget4.T[0];
                if(constraintWidget$DimensionBehaviour5 == constraintWidget$DimensionBehaviour2 || constraintWidget$DimensionBehaviour5 == constraintWidget$DimensionBehaviour0) {
                    WidgetRun.b(dependencyNode1, constraintWidget4.d.h, constraintWidget3.I.e());
                    WidgetRun.b(dependencyNode0, constraintWidget4.d.i, -this.b.K.e());
                    return;
                }
            }
        }
        if(dimensionDependency0.j) {
            ConstraintWidget constraintWidget5 = this.b;
            if(constraintWidget5.a) {
                ConstraintAnchor[] arr_constraintAnchor = constraintWidget5.Q;
                ConstraintAnchor constraintAnchor0 = arr_constraintAnchor[0];
                ConstraintAnchor constraintAnchor1 = constraintAnchor0.f;
                if(constraintAnchor1 != null && arr_constraintAnchor[1].f != null) {
                    if(constraintWidget5.y()) {
                        dependencyNode1.f = this.b.Q[0].e();
                        dependencyNode0.f = -this.b.Q[1].e();
                        return;
                    }
                    DependencyNode dependencyNode2 = WidgetRun.h(this.b.Q[0]);
                    if(dependencyNode2 != null) {
                        WidgetRun.b(dependencyNode1, dependencyNode2, this.b.Q[0].e());
                    }
                    DependencyNode dependencyNode3 = WidgetRun.h(this.b.Q[1]);
                    if(dependencyNode3 != null) {
                        WidgetRun.b(dependencyNode0, dependencyNode3, -this.b.Q[1].e());
                    }
                    dependencyNode1.b = true;
                    dependencyNode0.b = true;
                    return;
                }
                if(constraintAnchor1 == null) {
                    ConstraintAnchor constraintAnchor2 = arr_constraintAnchor[1];
                    if(constraintAnchor2.f != null) {
                        DependencyNode dependencyNode5 = WidgetRun.h(constraintAnchor2);
                        if(dependencyNode5 != null) {
                            WidgetRun.b(dependencyNode0, dependencyNode5, -this.b.Q[1].e());
                            WidgetRun.b(dependencyNode1, dependencyNode0, -dimensionDependency0.g);
                        }
                    }
                    else if(!(constraintWidget5 instanceof Helper) && constraintWidget5.U != null && constraintWidget5.j(androidx.constraintlayout.core.widgets.ConstraintAnchor.Type.f).f == null) {
                        WidgetRun.b(dependencyNode1, this.b.U.d.h, this.b.s());
                        WidgetRun.b(dependencyNode0, dependencyNode1, dimensionDependency0.g);
                    }
                }
                else {
                    DependencyNode dependencyNode4 = WidgetRun.h(constraintAnchor0);
                    if(dependencyNode4 != null) {
                        WidgetRun.b(dependencyNode1, dependencyNode4, this.b.Q[0].e());
                        WidgetRun.b(dependencyNode0, dependencyNode1, dimensionDependency0.g);
                    }
                }
            }
            else {
                goto label_75;
            }
        }
        else {
        label_75:
            if(this.d == constraintWidget$DimensionBehaviour1) {
                ConstraintWidget constraintWidget6 = this.b;
                switch(constraintWidget6.r) {
                    case 2: {
                        ConstraintWidget constraintWidget7 = constraintWidget6.U;
                        if(constraintWidget7 != null) {
                            DimensionDependency dimensionDependency1 = constraintWidget7.e.e;
                            dimensionDependency0.l.add(dimensionDependency1);
                            dimensionDependency1.k.add(dimensionDependency0);
                            dimensionDependency0.b = true;
                            dimensionDependency0.k.add(dependencyNode1);
                            dimensionDependency0.k.add(dependencyNode0);
                        }
                        break;
                    }
                    case 3: {
                        if(constraintWidget6.s == 3) {
                            dependencyNode1.a = this;
                            dependencyNode0.a = this;
                            constraintWidget6.e.h.a = this;
                            constraintWidget6.e.i.a = this;
                            dimensionDependency0.a = this;
                            if(constraintWidget6.z()) {
                                dimensionDependency0.l.add(this.b.e.e);
                                this.b.e.e.k.add(dimensionDependency0);
                                this.b.e.e.a = this;
                                dimensionDependency0.l.add(this.b.e.h);
                                dimensionDependency0.l.add(this.b.e.i);
                                this.b.e.h.k.add(dimensionDependency0);
                                this.b.e.i.k.add(dimensionDependency0);
                            }
                            else if(this.b.y()) {
                                this.b.e.e.l.add(dimensionDependency0);
                                dimensionDependency0.k.add(this.b.e.e);
                            }
                            else {
                                this.b.e.e.l.add(dimensionDependency0);
                            }
                        }
                        else {
                            DimensionDependency dimensionDependency2 = constraintWidget6.e.e;
                            dimensionDependency0.l.add(dimensionDependency2);
                            dimensionDependency2.k.add(dimensionDependency0);
                            this.b.e.h.k.add(dimensionDependency0);
                            this.b.e.i.k.add(dimensionDependency0);
                            dimensionDependency0.b = true;
                            dimensionDependency0.k.add(dependencyNode1);
                            dimensionDependency0.k.add(dependencyNode0);
                            dependencyNode1.l.add(dimensionDependency0);
                            dependencyNode0.l.add(dimensionDependency0);
                        }
                    }
                }
            }
            ConstraintWidget constraintWidget8 = this.b;
            ConstraintAnchor[] arr_constraintAnchor1 = constraintWidget8.Q;
            ConstraintAnchor constraintAnchor3 = arr_constraintAnchor1[0];
            ConstraintAnchor constraintAnchor4 = constraintAnchor3.f;
            if(constraintAnchor4 != null && arr_constraintAnchor1[1].f != null) {
                if(constraintWidget8.y()) {
                    dependencyNode1.f = this.b.Q[0].e();
                    dependencyNode0.f = -this.b.Q[1].e();
                    return;
                }
                DependencyNode dependencyNode6 = WidgetRun.h(this.b.Q[0]);
                DependencyNode dependencyNode7 = WidgetRun.h(this.b.Q[1]);
                if(dependencyNode6 != null) {
                    dependencyNode6.b(this);
                }
                if(dependencyNode7 != null) {
                    dependencyNode7.b(this);
                }
                this.j = RunType.b;
                return;
            }
            if(constraintAnchor4 == null) {
                ConstraintAnchor constraintAnchor5 = arr_constraintAnchor1[1];
                if(constraintAnchor5.f != null) {
                    DependencyNode dependencyNode9 = WidgetRun.h(constraintAnchor5);
                    if(dependencyNode9 != null) {
                        WidgetRun.b(dependencyNode0, dependencyNode9, -this.b.Q[1].e());
                        this.c(dependencyNode1, dependencyNode0, -1, dimensionDependency0);
                    }
                }
                else if(!(constraintWidget8 instanceof Helper)) {
                    ConstraintWidget constraintWidget9 = constraintWidget8.U;
                    if(constraintWidget9 != null) {
                        WidgetRun.b(dependencyNode1, constraintWidget9.d.h, constraintWidget8.s());
                        this.c(dependencyNode0, dependencyNode1, 1, dimensionDependency0);
                    }
                }
            }
            else {
                DependencyNode dependencyNode8 = WidgetRun.h(constraintAnchor3);
                if(dependencyNode8 != null) {
                    WidgetRun.b(dependencyNode1, dependencyNode8, this.b.Q[0].e());
                    this.c(dependencyNode0, dependencyNode1, 1, dimensionDependency0);
                }
            }
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void e() {
        DependencyNode dependencyNode0 = this.h;
        if(dependencyNode0.j) {
            this.b.Z = dependencyNode0.g;
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void f() {
        this.c = null;
        this.h.c();
        this.i.c();
        this.e.c();
        this.g = false;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final boolean k() {
        return this.d != DimensionBehaviour.c || this.b.r == 0;
    }

    public static void m(int[] arr_v, int v, int v1, int v2, int v3, float f, int v4) {
        int v5 = v1 - v;
        int v6 = v3 - v2;
        switch(v4) {
            case -1: {
                int v7 = (int)(((float)v6) * f + 0.5f);
                int v8 = (int)(((float)v5) / f + 0.5f);
                if(v7 <= v5) {
                    arr_v[0] = v7;
                    arr_v[1] = v6;
                    return;
                }
                if(v8 <= v6) {
                    arr_v[0] = v5;
                    arr_v[1] = v8;
                }
                return;
            }
            case 0: {
                arr_v[0] = (int)(((float)v6) * f + 0.5f);
                arr_v[1] = v6;
                return;
            }
            case 1: {
                arr_v[0] = v5;
                arr_v[1] = (int)(((float)v5) * f + 0.5f);
            }
        }
    }

    public final void n() {
        this.g = false;
        this.h.c();
        this.h.j = false;
        this.i.c();
        this.i.j = false;
        this.e.j = false;
    }

    @Override
    public final String toString() {
        return "HorizontalRun " + this.b.i0;
    }
}


package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.ConstraintAnchor;
import androidx.constraintlayout.core.widgets.ConstraintWidget.DimensionBehaviour;
import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.ConstraintWidgetContainer;
import androidx.constraintlayout.core.widgets.Guideline;
import androidx.constraintlayout.core.widgets.HelperWidget;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;

public class DependencyGraph {
    public ConstraintWidgetContainer a;
    public boolean b;
    public boolean c;
    public ConstraintWidgetContainer d;
    public ArrayList e;
    public Measurer f;
    public Measure g;
    public ArrayList h;

    public final void a(DependencyNode dependencyNode0, int v, ArrayList arrayList0, RunGroup runGroup0) {
        WidgetRun widgetRun0 = dependencyNode0.d;
        if(widgetRun0.c == null && (widgetRun0 != this.a.d && widgetRun0 != this.a.e)) {
            if(runGroup0 == null) {
                runGroup0 = new RunGroup();  // 初始化器: Ljava/lang/Object;-><init>()V
                runGroup0.a = null;
                runGroup0.b = new ArrayList();
                runGroup0.a = widgetRun0;
                arrayList0.add(runGroup0);
            }
            widgetRun0.c = runGroup0;
            runGroup0.b.add(widgetRun0);
            DependencyNode dependencyNode1 = widgetRun0.h;
            for(Object object0: dependencyNode1.k) {
                Dependency dependency0 = (Dependency)object0;
                if(dependency0 instanceof DependencyNode) {
                    this.a(((DependencyNode)dependency0), v, arrayList0, runGroup0);
                }
            }
            DependencyNode dependencyNode2 = widgetRun0.i;
            for(Object object1: dependencyNode2.k) {
                Dependency dependency1 = (Dependency)object1;
                if(dependency1 instanceof DependencyNode) {
                    this.a(((DependencyNode)dependency1), v, arrayList0, runGroup0);
                }
            }
            if(v == 1 && widgetRun0 instanceof VerticalWidgetRun) {
                for(Object object2: ((VerticalWidgetRun)widgetRun0).k.k) {
                    Dependency dependency2 = (Dependency)object2;
                    if(dependency2 instanceof DependencyNode) {
                        this.a(((DependencyNode)dependency2), 1, arrayList0, runGroup0);
                    }
                }
            }
            for(Object object3: dependencyNode1.l) {
                this.a(((DependencyNode)object3), v, arrayList0, runGroup0);
            }
            for(Object object4: dependencyNode2.l) {
                this.a(((DependencyNode)object4), v, arrayList0, runGroup0);
            }
            if(v == 1 && widgetRun0 instanceof VerticalWidgetRun) {
                for(Object object5: ((VerticalWidgetRun)widgetRun0).k.l) {
                    this.a(((DependencyNode)object5), 1, arrayList0, runGroup0);
                }
            }
        }
    }

    public final void b(ConstraintWidgetContainer constraintWidgetContainer0) {
        DimensionBehaviour constraintWidget$DimensionBehaviour9;
        int v7;
        int v5;
        for(Object object0: constraintWidgetContainer0.q0) {
            ConstraintWidget constraintWidget0 = (ConstraintWidget)object0;
            DimensionBehaviour constraintWidget$DimensionBehaviour0 = constraintWidget0.T[0];
            DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidget0.T[1];
            if(constraintWidget0.h0 == 8) {
                constraintWidget0.a = true;
            }
            else {
                float f = constraintWidget0.w;
                DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.c;
                if(f < 1.0f && constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2) {
                    constraintWidget0.r = 2;
                }
                float f1 = constraintWidget0.z;
                if(f1 < 1.0f && constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2) {
                    constraintWidget0.s = 2;
                }
                DimensionBehaviour constraintWidget$DimensionBehaviour3 = DimensionBehaviour.b;
                DimensionBehaviour constraintWidget$DimensionBehaviour4 = DimensionBehaviour.a;
                if(constraintWidget0.X > 0.0f) {
                    if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2 && (constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour3 || constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour4)) {
                        constraintWidget0.r = 3;
                    }
                    else if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2 && (constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour3 || constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour4)) {
                        constraintWidget0.s = 3;
                    }
                    else if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2 && constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2) {
                        if(constraintWidget0.r == 0) {
                            constraintWidget0.r = 3;
                        }
                        if(constraintWidget0.s == 0) {
                            constraintWidget0.s = 3;
                        }
                    }
                }
                ConstraintAnchor constraintAnchor0 = constraintWidget0.K;
                ConstraintAnchor constraintAnchor1 = constraintWidget0.I;
                if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2 && constraintWidget0.r == 1 && (constraintAnchor1.f == null || constraintAnchor0.f == null)) {
                    constraintWidget$DimensionBehaviour0 = constraintWidget$DimensionBehaviour3;
                }
                ConstraintAnchor constraintAnchor2 = constraintWidget0.L;
                ConstraintAnchor constraintAnchor3 = constraintWidget0.J;
                DimensionBehaviour constraintWidget$DimensionBehaviour5 = constraintWidget$DimensionBehaviour1 != constraintWidget$DimensionBehaviour2 || constraintWidget0.s != 1 || constraintAnchor3.f != null && constraintAnchor2.f != null ? constraintWidget$DimensionBehaviour1 : constraintWidget$DimensionBehaviour3;
                HorizontalWidgetRun horizontalWidgetRun0 = constraintWidget0.d;
                horizontalWidgetRun0.d = constraintWidget$DimensionBehaviour0;
                int v = constraintWidget0.r;
                horizontalWidgetRun0.a = v;
                constraintWidget0.e.d = constraintWidget$DimensionBehaviour5;
                int v1 = constraintWidget0.s;
                constraintWidget0.e.a = v1;
                DimensionBehaviour constraintWidget$DimensionBehaviour6 = DimensionBehaviour.d;
                if(constraintWidget$DimensionBehaviour0 != constraintWidget$DimensionBehaviour6 && constraintWidget$DimensionBehaviour0 != constraintWidget$DimensionBehaviour4 && constraintWidget$DimensionBehaviour0 != constraintWidget$DimensionBehaviour3 || constraintWidget$DimensionBehaviour5 != constraintWidget$DimensionBehaviour6 && constraintWidget$DimensionBehaviour5 != constraintWidget$DimensionBehaviour4 && constraintWidget$DimensionBehaviour5 != constraintWidget$DimensionBehaviour3) {
                    DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = constraintWidgetContainer0.T;
                    ConstraintAnchor[] arr_constraintAnchor = constraintWidget0.Q;
                    if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2 && (constraintWidget$DimensionBehaviour5 == constraintWidget$DimensionBehaviour3 || constraintWidget$DimensionBehaviour5 == constraintWidget$DimensionBehaviour4)) {
                        switch(v) {
                            case 1: {
                                this.h(constraintWidget0, constraintWidget$DimensionBehaviour3, 0, constraintWidget$DimensionBehaviour5, 0);
                                constraintWidget0.d.e.m = constraintWidget0.r();
                                continue;
                            }
                            case 2: {
                                DimensionBehaviour constraintWidget$DimensionBehaviour7 = arr_constraintWidget$DimensionBehaviour[0];
                                if(constraintWidget$DimensionBehaviour7 == constraintWidget$DimensionBehaviour4 || constraintWidget$DimensionBehaviour7 == constraintWidget$DimensionBehaviour6) {
                                    this.h(constraintWidget0, constraintWidget$DimensionBehaviour4, ((int)(f * ((float)constraintWidgetContainer0.r()) + 0.5f)), constraintWidget$DimensionBehaviour5, constraintWidget0.l());
                                    constraintWidget0.d.e.d(constraintWidget0.r());
                                    constraintWidget0.e.e.d(constraintWidget0.l());
                                    constraintWidget0.a = true;
                                    continue;
                                }
                                break;
                            }
                            case 3: {
                                if(constraintWidget$DimensionBehaviour5 == constraintWidget$DimensionBehaviour3) {
                                    this.h(constraintWidget0, constraintWidget$DimensionBehaviour3, 0, constraintWidget$DimensionBehaviour3, 0);
                                }
                                int v2 = constraintWidget0.l();
                                this.h(constraintWidget0, constraintWidget$DimensionBehaviour4, ((int)(((float)v2) * constraintWidget0.X + 0.5f)), constraintWidget$DimensionBehaviour4, v2);
                                constraintWidget0.d.e.d(constraintWidget0.r());
                                constraintWidget0.e.e.d(constraintWidget0.l());
                                constraintWidget0.a = true;
                                continue;
                            }
                            default: {
                                if(arr_constraintAnchor[0].f == null || arr_constraintAnchor[1].f == null) {
                                    this.h(constraintWidget0, constraintWidget$DimensionBehaviour3, 0, constraintWidget$DimensionBehaviour5, 0);
                                    constraintWidget0.d.e.d(constraintWidget0.r());
                                    constraintWidget0.e.e.d(constraintWidget0.l());
                                    constraintWidget0.a = true;
                                    continue;
                                }
                            }
                        }
                    }
                    if(constraintWidget$DimensionBehaviour5 == constraintWidget$DimensionBehaviour2 && (constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour3 || constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour4)) {
                        if(v1 == 3) {
                            if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour3) {
                                this.h(constraintWidget0, constraintWidget$DimensionBehaviour3, 0, constraintWidget$DimensionBehaviour3, 0);
                            }
                            int v3 = constraintWidget0.r();
                            this.h(constraintWidget0, constraintWidget$DimensionBehaviour4, v3, constraintWidget$DimensionBehaviour4, ((int)(((float)v3) * (constraintWidget0.Y == -1 ? 1.0f / constraintWidget0.X : constraintWidget0.X) + 0.5f)));
                            constraintWidget0.d.e.d(constraintWidget0.r());
                            constraintWidget0.e.e.d(constraintWidget0.l());
                            constraintWidget0.a = true;
                            continue;
                        }
                        else {
                            switch(v1) {
                                case 1: {
                                    this.h(constraintWidget0, constraintWidget$DimensionBehaviour0, 0, constraintWidget$DimensionBehaviour3, 0);
                                    constraintWidget0.e.e.m = constraintWidget0.l();
                                    continue;
                                }
                                case 2: {
                                    DimensionBehaviour constraintWidget$DimensionBehaviour8 = arr_constraintWidget$DimensionBehaviour[1];
                                    if(constraintWidget$DimensionBehaviour8 == constraintWidget$DimensionBehaviour4 || constraintWidget$DimensionBehaviour8 == constraintWidget$DimensionBehaviour6) {
                                        this.h(constraintWidget0, constraintWidget$DimensionBehaviour0, constraintWidget0.r(), constraintWidget$DimensionBehaviour4, ((int)(f1 * ((float)constraintWidgetContainer0.l()) + 0.5f)));
                                        constraintWidget0.d.e.d(constraintWidget0.r());
                                        constraintWidget0.e.e.d(constraintWidget0.l());
                                        constraintWidget0.a = true;
                                        continue;
                                    }
                                    break;
                                }
                                default: {
                                    if(arr_constraintAnchor[2].f == null || arr_constraintAnchor[3].f == null) {
                                        this.h(constraintWidget0, constraintWidget$DimensionBehaviour3, 0, constraintWidget$DimensionBehaviour5, 0);
                                        constraintWidget0.d.e.d(constraintWidget0.r());
                                        constraintWidget0.e.e.d(constraintWidget0.l());
                                        constraintWidget0.a = true;
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                    if(constraintWidget$DimensionBehaviour0 != constraintWidget$DimensionBehaviour2 || constraintWidget$DimensionBehaviour5 != constraintWidget$DimensionBehaviour2) {
                        continue;
                    }
                    if(v == 1 || v1 == 1) {
                        this.h(constraintWidget0, constraintWidget$DimensionBehaviour3, 0, constraintWidget$DimensionBehaviour3, 0);
                        constraintWidget0.d.e.m = constraintWidget0.r();
                        constraintWidget0.e.e.m = constraintWidget0.l();
                    }
                    else {
                        if(v1 != 2 || v != 2 || arr_constraintWidget$DimensionBehaviour[0] != constraintWidget$DimensionBehaviour4 || arr_constraintWidget$DimensionBehaviour[1] != constraintWidget$DimensionBehaviour4) {
                            continue;
                        }
                        this.h(constraintWidget0, constraintWidget$DimensionBehaviour4, ((int)(f * ((float)constraintWidgetContainer0.r()) + 0.5f)), constraintWidget$DimensionBehaviour4, ((int)(f1 * ((float)constraintWidgetContainer0.l()) + 0.5f)));
                        constraintWidget0.d.e.d(constraintWidget0.r());
                        constraintWidget0.e.e.d(constraintWidget0.l());
                        constraintWidget0.a = true;
                    }
                }
                else {
                    int v4 = constraintWidget0.r();
                    if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour6) {
                        v5 = constraintWidgetContainer0.r() - constraintAnchor1.g - constraintAnchor0.g;
                        constraintWidget$DimensionBehaviour0 = constraintWidget$DimensionBehaviour4;
                    }
                    else {
                        v5 = v4;
                    }
                    int v6 = constraintWidget0.l();
                    if(constraintWidget$DimensionBehaviour5 == constraintWidget$DimensionBehaviour6) {
                        v7 = constraintWidgetContainer0.l() - constraintAnchor3.g - constraintAnchor2.g;
                        constraintWidget$DimensionBehaviour9 = constraintWidget$DimensionBehaviour4;
                    }
                    else {
                        v7 = v6;
                        constraintWidget$DimensionBehaviour9 = constraintWidget$DimensionBehaviour5;
                    }
                    this.h(constraintWidget0, constraintWidget$DimensionBehaviour0, v5, constraintWidget$DimensionBehaviour9, v7);
                    constraintWidget0.d.e.d(constraintWidget0.r());
                    constraintWidget0.e.e.d(constraintWidget0.l());
                    constraintWidget0.a = true;
                }
            }
        }
    }

    public final void c() {
        ArrayList arrayList0 = this.e;
        arrayList0.clear();
        ConstraintWidgetContainer constraintWidgetContainer0 = this.d;
        constraintWidgetContainer0.d.f();
        constraintWidgetContainer0.e.f();
        arrayList0.add(constraintWidgetContainer0.d);
        arrayList0.add(constraintWidgetContainer0.e);
        Collection collection0 = null;
        for(Object object0: constraintWidgetContainer0.q0) {
            ConstraintWidget constraintWidget0 = (ConstraintWidget)object0;
            if(constraintWidget0 instanceof Guideline) {
                GuidelineReference guidelineReference0 = new GuidelineReference(constraintWidget0);  // 初始化器: Landroidx/constraintlayout/core/widgets/analyzer/WidgetRun;-><init>(Landroidx/constraintlayout/core/widgets/ConstraintWidget;)V
                constraintWidget0.d.f();
                constraintWidget0.e.f();
                guidelineReference0.f = ((Guideline)constraintWidget0).u0;
                arrayList0.add(guidelineReference0);
            }
            else {
                if(constraintWidget0.y()) {
                    if(constraintWidget0.b == null) {
                        constraintWidget0.b = new ChainRun(0, constraintWidget0);
                    }
                    if(collection0 == null) {
                        collection0 = new HashSet();
                    }
                    ((HashSet)collection0).add(constraintWidget0.b);
                }
                else {
                    arrayList0.add(constraintWidget0.d);
                }
                if(constraintWidget0.z()) {
                    if(constraintWidget0.c == null) {
                        constraintWidget0.c = new ChainRun(1, constraintWidget0);
                    }
                    if(collection0 == null) {
                        collection0 = new HashSet();
                    }
                    ((HashSet)collection0).add(constraintWidget0.c);
                }
                else {
                    arrayList0.add(constraintWidget0.e);
                }
                if(constraintWidget0 instanceof HelperWidget) {
                    arrayList0.add(new HelperReferences(constraintWidget0));  // 初始化器: Landroidx/constraintlayout/core/widgets/analyzer/WidgetRun;-><init>(Landroidx/constraintlayout/core/widgets/ConstraintWidget;)V
                }
            }
        }
        if(collection0 != null) {
            arrayList0.addAll(collection0);
        }
        for(Object object1: arrayList0) {
            ((WidgetRun)object1).f();
        }
        for(Object object2: arrayList0) {
            WidgetRun widgetRun0 = (WidgetRun)object2;
            if(widgetRun0.b != constraintWidgetContainer0) {
                widgetRun0.d();
            }
        }
        ArrayList arrayList1 = this.h;
        arrayList1.clear();
        ConstraintWidgetContainer constraintWidgetContainer1 = this.a;
        this.g(constraintWidgetContainer1.d, 0, arrayList1);
        this.g(constraintWidgetContainer1.e, 1, arrayList1);
        this.b = false;
    }

    public final int d(ConstraintWidgetContainer constraintWidgetContainer0, int v) {
        long v13;
        float f;
        int v9;
        ArrayList arrayList0 = this.h;
        int v1 = arrayList0.size();
        int v2 = 0;
        long v3 = 0L;
        while(v2 < v1) {
            WidgetRun widgetRun0 = ((RunGroup)arrayList0.get(v2)).a;
            if(widgetRun0 instanceof ChainRun) {
                if(((ChainRun)widgetRun0).f == v) {
                    goto label_13;
                }
                else {
                    goto label_59;
                }
                goto label_9;
            }
            else {
            label_9:
                if(v == 0) {
                    if(widgetRun0 instanceof HorizontalWidgetRun) {
                        goto label_13;
                    }
                    else {
                        goto label_59;
                    }
                    goto label_12;
                }
                else {
                label_12:
                    if(widgetRun0 instanceof VerticalWidgetRun) {
                    label_13:
                        HorizontalWidgetRun horizontalWidgetRun0 = v == 0 ? constraintWidgetContainer0.d : constraintWidgetContainer0.e;
                        DependencyNode dependencyNode0 = horizontalWidgetRun0.h;
                        HorizontalWidgetRun horizontalWidgetRun1 = v == 0 ? constraintWidgetContainer0.d : constraintWidgetContainer0.e;
                        boolean z = widgetRun0.h.l.contains(dependencyNode0);
                        DependencyNode dependencyNode1 = widgetRun0.i;
                        boolean z1 = dependencyNode1.l.contains(horizontalWidgetRun1.i);
                        long v4 = widgetRun0.j();
                        DependencyNode dependencyNode2 = widgetRun0.h;
                        if(!z || !z1) {
                            v9 = v2;
                            if(z) {
                                v13 = Math.max(RunGroup.b(dependencyNode2, ((long)dependencyNode2.f)), ((long)dependencyNode2.f) + v4);
                            }
                            else if(z1) {
                                v13 = Math.max(-RunGroup.a(dependencyNode1, ((long)dependencyNode1.f)), ((long)(-dependencyNode1.f)) + v4);
                            }
                            else {
                                long v14 = (long)dependencyNode2.f;
                                v13 = widgetRun0.j() + v14 - ((long)dependencyNode1.f);
                            }
                        }
                        else {
                            long v5 = RunGroup.b(dependencyNode2, 0L);
                            long v6 = RunGroup.a(dependencyNode1, 0L);
                            long v7 = v5 - v4;
                            int v8 = dependencyNode1.f;
                            v9 = v2;
                            if(v7 >= ((long)(-v8))) {
                                v7 += (long)v8;
                            }
                            long v10 = (long)dependencyNode2.f;
                            long v11 = -v6 - v4 - v10;
                            ConstraintWidget constraintWidget0 = widgetRun0.b;
                            if(v == 0) {
                                f = constraintWidget0.e0;
                            }
                            else if(v == 1) {
                                f = constraintWidget0.f0;
                            }
                            else {
                                constraintWidget0.getClass();
                                f = -1.0f;
                            }
                            long v12 = f > 0.0f ? ((long)(((float)v7) / (1.0f - f) + ((float)(v11 < v10 ? -v6 - v4 - v10 : v11 - v10)) / f)) : 0L;
                            v13 = ((long)dependencyNode2.f) + (((long)(((float)v12) * f + 0.5f)) + v4 + ((long)((1.0f - f) * ((float)v12) + 0.5f))) - ((long)dependencyNode1.f);
                        }
                        goto label_61;
                    }
                }
            }
        label_59:
            v9 = v2;
            v13 = 0L;
        label_61:
            v3 = Math.max(v3, v13);
            v2 = v9 + 1;
        }
        return (int)v3;
    }

    public final boolean e(boolean z) {
        boolean z2;
        boolean z1 = true;
        int v = z & true;
        ConstraintWidgetContainer constraintWidgetContainer0 = this.a;
        if(this.b || this.c) {
            for(Object object0: constraintWidgetContainer0.q0) {
                ((ConstraintWidget)object0).i();
                ((ConstraintWidget)object0).a = false;
                ((ConstraintWidget)object0).d.n();
                ((ConstraintWidget)object0).e.m();
            }
            constraintWidgetContainer0.i();
            constraintWidgetContainer0.a = false;
            constraintWidgetContainer0.d.n();
            constraintWidgetContainer0.e.m();
            this.c = false;
        }
        this.b(this.d);
        constraintWidgetContainer0.Z = 0;
        constraintWidgetContainer0.a0 = 0;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = constraintWidgetContainer0.k(0);
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidgetContainer0.k(1);
        if(this.b) {
            this.c();
        }
        int v1 = constraintWidgetContainer0.s();
        int v2 = constraintWidgetContainer0.t();
        constraintWidgetContainer0.d.h.d(v1);
        constraintWidgetContainer0.e.h.d(v2);
        this.i();
        DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.b;
        DimensionBehaviour constraintWidget$DimensionBehaviour3 = DimensionBehaviour.a;
        ArrayList arrayList0 = this.e;
        if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2 || constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2) {
            if(v != 0) {
                for(Object object1: arrayList0) {
                    if(!((WidgetRun)object1).k()) {
                        v = 0;
                        break;
                    }
                    if(false) {
                        break;
                    }
                }
            }
            if(v != 0 && constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2) {
                constraintWidgetContainer0.N(constraintWidget$DimensionBehaviour3);
                constraintWidgetContainer0.P(this.d(constraintWidgetContainer0, 0));
                constraintWidgetContainer0.d.e.d(constraintWidgetContainer0.r());
            }
            if(v != 0 && constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2) {
                constraintWidgetContainer0.O(constraintWidget$DimensionBehaviour3);
                constraintWidgetContainer0.M(this.d(constraintWidgetContainer0, 1));
                constraintWidgetContainer0.e.e.d(constraintWidgetContainer0.l());
            }
        }
        DimensionBehaviour constraintWidget$DimensionBehaviour4 = constraintWidgetContainer0.T[0];
        DimensionBehaviour constraintWidget$DimensionBehaviour5 = DimensionBehaviour.d;
        if(constraintWidget$DimensionBehaviour4 == constraintWidget$DimensionBehaviour3 || constraintWidget$DimensionBehaviour4 == constraintWidget$DimensionBehaviour5) {
            int v3 = constraintWidgetContainer0.r() + v1;
            constraintWidgetContainer0.d.i.d(v3);
            constraintWidgetContainer0.d.e.d(v3 - v1);
            this.i();
            DimensionBehaviour constraintWidget$DimensionBehaviour6 = constraintWidgetContainer0.T[1];
            if(constraintWidget$DimensionBehaviour6 == constraintWidget$DimensionBehaviour3 || constraintWidget$DimensionBehaviour6 == constraintWidget$DimensionBehaviour5) {
                int v4 = constraintWidgetContainer0.l() + v2;
                constraintWidgetContainer0.e.i.d(v4);
                constraintWidgetContainer0.e.e.d(v4 - v2);
            }
            this.i();
            z2 = true;
        }
        else {
            z2 = false;
        }
        for(Object object2: arrayList0) {
            WidgetRun widgetRun0 = (WidgetRun)object2;
            if(widgetRun0.b != constraintWidgetContainer0 || widgetRun0.g) {
                widgetRun0.e();
            }
        }
        for(Object object3: arrayList0) {
            WidgetRun widgetRun1 = (WidgetRun)object3;
            if(!z2 && widgetRun1.b == constraintWidgetContainer0) {
            }
            else if(!widgetRun1.h.j || (!widgetRun1.i.j && !(widgetRun1 instanceof GuidelineReference) || !widgetRun1.e.j && !(widgetRun1 instanceof ChainRun) && !(widgetRun1 instanceof GuidelineReference))) {
                z1 = false;
                break;
            }
        }
        constraintWidgetContainer0.N(constraintWidget$DimensionBehaviour0);
        constraintWidgetContainer0.O(constraintWidget$DimensionBehaviour1);
        return z1;
    }

    public final boolean f(int v, boolean z) {
        boolean z2;
        boolean z1 = true;
        int v1 = z & true;
        ConstraintWidgetContainer constraintWidgetContainer0 = this.a;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = constraintWidgetContainer0.k(0);
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidgetContainer0.k(1);
        int v2 = constraintWidgetContainer0.s();
        int v3 = constraintWidgetContainer0.t();
        ArrayList arrayList0 = this.e;
        DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.a;
        if(v1 != 0) {
            DimensionBehaviour constraintWidget$DimensionBehaviour3 = DimensionBehaviour.b;
            if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour3 || constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour3) {
                for(Object object0: arrayList0) {
                    if(((WidgetRun)object0).f == v && !((WidgetRun)object0).k()) {
                        v1 = 0;
                        break;
                    }
                    if(false) {
                        break;
                    }
                }
                if(v != 0) {
                    if(v1 != 0 && constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour3) {
                        constraintWidgetContainer0.O(constraintWidget$DimensionBehaviour2);
                        constraintWidgetContainer0.M(this.d(constraintWidgetContainer0, 1));
                        constraintWidgetContainer0.e.e.d(constraintWidgetContainer0.l());
                    }
                }
                else if(v1 != 0 && constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour3) {
                    constraintWidgetContainer0.N(constraintWidget$DimensionBehaviour2);
                    constraintWidgetContainer0.P(this.d(constraintWidgetContainer0, 0));
                    constraintWidgetContainer0.d.e.d(constraintWidgetContainer0.r());
                }
            }
        }
        DimensionBehaviour constraintWidget$DimensionBehaviour4 = DimensionBehaviour.d;
        if(v == 0) {
            DimensionBehaviour constraintWidget$DimensionBehaviour5 = constraintWidgetContainer0.T[0];
            if(constraintWidget$DimensionBehaviour5 != constraintWidget$DimensionBehaviour2 && constraintWidget$DimensionBehaviour5 != constraintWidget$DimensionBehaviour4) {
                z2 = false;
            }
            else {
                int v4 = constraintWidgetContainer0.r() + v2;
                constraintWidgetContainer0.d.i.d(v4);
                constraintWidgetContainer0.d.e.d(v4 - v2);
                z2 = true;
            }
        }
        else {
            DimensionBehaviour constraintWidget$DimensionBehaviour6 = constraintWidgetContainer0.T[1];
            if(constraintWidget$DimensionBehaviour6 == constraintWidget$DimensionBehaviour2 || constraintWidget$DimensionBehaviour6 == constraintWidget$DimensionBehaviour4) {
                int v5 = constraintWidgetContainer0.l() + v3;
                constraintWidgetContainer0.e.i.d(v5);
                constraintWidgetContainer0.e.e.d(v5 - v3);
                z2 = true;
            }
            else {
                z2 = false;
            }
        }
        this.i();
        for(Object object1: arrayList0) {
            WidgetRun widgetRun0 = (WidgetRun)object1;
            if(widgetRun0.f == v && (widgetRun0.b != constraintWidgetContainer0 || widgetRun0.g)) {
                widgetRun0.e();
            }
        }
        for(Object object2: arrayList0) {
            WidgetRun widgetRun1 = (WidgetRun)object2;
            if(widgetRun1.f != v || !z2 && widgetRun1.b == constraintWidgetContainer0) {
            }
            else if(!widgetRun1.h.j || (!widgetRun1.i.j || !(widgetRun1 instanceof ChainRun) && !widgetRun1.e.j)) {
                z1 = false;
                break;
            }
        }
        constraintWidgetContainer0.N(constraintWidget$DimensionBehaviour0);
        constraintWidgetContainer0.O(constraintWidget$DimensionBehaviour1);
        return z1;
    }

    public final void g(WidgetRun widgetRun0, int v, ArrayList arrayList0) {
        Iterator iterator0 = widgetRun0.h.k.iterator();
        while(true) {
            boolean z = iterator0.hasNext();
            DependencyNode dependencyNode0 = widgetRun0.i;
            if(!z) {
                break;
            }
            Object object0 = iterator0.next();
            Dependency dependency0 = (Dependency)object0;
            if(dependency0 instanceof DependencyNode) {
                this.a(((DependencyNode)dependency0), v, arrayList0, null);
            }
            else if(dependency0 instanceof WidgetRun) {
                this.a(((WidgetRun)dependency0).h, v, arrayList0, null);
            }
        }
        for(Object object1: dependencyNode0.k) {
            Dependency dependency1 = (Dependency)object1;
            if(dependency1 instanceof DependencyNode) {
                this.a(((DependencyNode)dependency1), v, arrayList0, null);
            }
            else if(dependency1 instanceof WidgetRun) {
                this.a(((WidgetRun)dependency1).i, v, arrayList0, null);
            }
        }
        if(v == 1) {
            for(Object object2: ((VerticalWidgetRun)widgetRun0).k.k) {
                Dependency dependency2 = (Dependency)object2;
                if(dependency2 instanceof DependencyNode) {
                    this.a(((DependencyNode)dependency2), 1, arrayList0, null);
                }
            }
        }
    }

    public final void h(ConstraintWidget constraintWidget0, DimensionBehaviour constraintWidget$DimensionBehaviour0, int v, DimensionBehaviour constraintWidget$DimensionBehaviour1, int v1) {
        Measure basicMeasure$Measure0 = this.g;
        basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour0;
        basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour1;
        basicMeasure$Measure0.c = v;
        basicMeasure$Measure0.d = v1;
        this.f.a(constraintWidget0, basicMeasure$Measure0);
        constraintWidget0.P(basicMeasure$Measure0.e);
        constraintWidget0.M(basicMeasure$Measure0.f);
        constraintWidget0.E = basicMeasure$Measure0.h;
        constraintWidget0.J(basicMeasure$Measure0.g);
    }

    public final void i() {
        for(Object object0: this.a.q0) {
            ConstraintWidget constraintWidget0 = (ConstraintWidget)object0;
            if(!constraintWidget0.a) {
                boolean z = false;
                DimensionBehaviour constraintWidget$DimensionBehaviour0 = constraintWidget0.T[0];
                DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidget0.T[1];
                DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.b;
                DimensionBehaviour constraintWidget$DimensionBehaviour3 = DimensionBehaviour.c;
                boolean z1 = constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2 || constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour3 && constraintWidget0.r == 1;
                if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2 || constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour3 && constraintWidget0.s == 1) {
                    z = true;
                }
                DimensionDependency dimensionDependency0 = constraintWidget0.d.e;
                boolean z2 = dimensionDependency0.j;
                DimensionDependency dimensionDependency1 = constraintWidget0.e.e;
                boolean z3 = dimensionDependency1.j;
                DimensionBehaviour constraintWidget$DimensionBehaviour4 = DimensionBehaviour.a;
                if(z2 && z3) {
                    this.h(constraintWidget0, constraintWidget$DimensionBehaviour4, dimensionDependency0.g, constraintWidget$DimensionBehaviour4, dimensionDependency1.g);
                    constraintWidget0.a = true;
                }
                else if(z2 && z) {
                    this.h(constraintWidget0, constraintWidget$DimensionBehaviour4, dimensionDependency0.g, constraintWidget$DimensionBehaviour2, dimensionDependency1.g);
                    if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour3) {
                        constraintWidget0.e.e.m = constraintWidget0.l();
                    }
                    else {
                        constraintWidget0.e.e.d(constraintWidget0.l());
                        constraintWidget0.a = true;
                    }
                }
                else if(z3 && z1) {
                    this.h(constraintWidget0, constraintWidget$DimensionBehaviour2, dimensionDependency0.g, constraintWidget$DimensionBehaviour4, dimensionDependency1.g);
                    if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour3) {
                        constraintWidget0.d.e.m = constraintWidget0.r();
                    }
                    else {
                        constraintWidget0.d.e.d(constraintWidget0.r());
                        constraintWidget0.a = true;
                    }
                }
                if(constraintWidget0.a) {
                    BaselineDimensionDependency baselineDimensionDependency0 = constraintWidget0.e.l;
                    if(baselineDimensionDependency0 != null) {
                        baselineDimensionDependency0.d(constraintWidget0.b0);
                    }
                }
            }
        }
    }
}


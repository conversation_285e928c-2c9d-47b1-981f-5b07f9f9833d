package androidx.constraintlayout.core.widgets.analyzer;

import a.a;
import androidx.constraintlayout.core.widgets.ConstraintAnchor;
import androidx.constraintlayout.core.widgets.ConstraintWidget.DimensionBehaviour;
import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.ConstraintWidgetContainer;
import java.util.ArrayList;

public class ChainRun extends WidgetRun {
    public final ArrayList k;
    public int l;

    public ChainRun(int v, ConstraintWidget constraintWidget0) {
        super(constraintWidget0);
        HorizontalWidgetRun horizontalWidgetRun1;
        HorizontalWidgetRun horizontalWidgetRun0;
        ConstraintWidget constraintWidget4;
        this.k = new ArrayList();
        this.f = v;
        ConstraintWidget constraintWidget1 = this.b;
        for(ConstraintWidget constraintWidget2 = constraintWidget1.n(v); true; constraintWidget2 = constraintWidget1.n(this.f)) {
            ConstraintWidget constraintWidget3 = constraintWidget1;
            constraintWidget1 = constraintWidget2;
            constraintWidget4 = constraintWidget3;
            if(constraintWidget1 == null) {
                break;
            }
        }
        this.b = constraintWidget4;
        int v1 = this.f;
        if(v1 == 0) {
            horizontalWidgetRun0 = constraintWidget4.d;
        }
        else if(v1 == 1) {
            horizontalWidgetRun0 = constraintWidget4.e;
        }
        else {
            horizontalWidgetRun0 = null;
        }
        ArrayList arrayList0 = this.k;
        arrayList0.add(horizontalWidgetRun0);
        for(ConstraintWidget constraintWidget5 = constraintWidget4.m(this.f); constraintWidget5 != null; constraintWidget5 = constraintWidget5.m(this.f)) {
            int v2 = this.f;
            if(v2 == 0) {
                horizontalWidgetRun1 = constraintWidget5.d;
            }
            else if(v2 == 1) {
                horizontalWidgetRun1 = constraintWidget5.e;
            }
            else {
                horizontalWidgetRun1 = null;
            }
            arrayList0.add(horizontalWidgetRun1);
        }
        for(Object object0: arrayList0) {
            WidgetRun widgetRun0 = (WidgetRun)object0;
            int v3 = this.f;
            if(v3 == 0) {
                widgetRun0.b.b = this;
            }
            else if(v3 == 1) {
                widgetRun0.b.c = this;
            }
        }
        if(this.f == 0 && ((ConstraintWidgetContainer)this.b.U).v0 && arrayList0.size() > 1) {
            this.b = ((WidgetRun)a.h(arrayList0, 1)).b;
        }
        this.l = this.f == 0 ? this.b.j0 : this.b.k0;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.Dependency
    public final void a(Dependency dependency0) {
        DimensionBehaviour constraintWidget$DimensionBehaviour3;
        int v39;
        int v36;
        int v34;
        int v33;
        ArrayList arrayList2;
        DimensionBehaviour constraintWidget$DimensionBehaviour2;
        boolean z1;
        int v31;
        float f3;
        int v28;
        int v27;
        int v26;
        DimensionBehaviour constraintWidget$DimensionBehaviour1;
        int v23;
        int v18;
        int v17;
        ArrayList arrayList1;
        int v15;
        int v16;
        int v11;
        float f;
        int v10;
        int v9;
        int v7;
        DimensionBehaviour constraintWidget$DimensionBehaviour0;
        int v3;
        DependencyNode dependencyNode0 = this.h;
        if(dependencyNode0.j) {
            DependencyNode dependencyNode1 = this.i;
            if(dependencyNode1.j) {
                ConstraintWidget constraintWidget0 = this.b.U;
                boolean z = constraintWidget0 instanceof ConstraintWidgetContainer ? ((ConstraintWidgetContainer)constraintWidget0).v0 : false;
                int v = dependencyNode1.g - dependencyNode0.g;
                ArrayList arrayList0 = this.k;
                int v1 = arrayList0.size();
                int v2;
                for(v2 = 0; true; ++v2) {
                    v3 = -1;
                    if(v2 >= v1) {
                        v2 = -1;
                        break;
                    }
                    if(((WidgetRun)arrayList0.get(v2)).b.h0 != 8) {
                        break;
                    }
                }
                int v4 = v1 - 1;
                int v5 = v4;
                while(v5 >= 0) {
                    if(((WidgetRun)arrayList0.get(v5)).b.h0 == 8) {
                        --v5;
                    }
                    else {
                        v3 = v5;
                        if(true) {
                            break;
                        }
                    }
                }
                int v6 = 0;
                while(true) {
                    constraintWidget$DimensionBehaviour0 = DimensionBehaviour.c;
                    if(v6 >= 2) {
                        goto label_90;
                    }
                    v7 = 0;
                    int v8 = 0;
                    v9 = 0;
                    v10 = 0;
                    f = 0.0f;
                    while(v8 < v1) {
                        WidgetRun widgetRun0 = (WidgetRun)arrayList0.get(v8);
                        ConstraintWidget constraintWidget1 = widgetRun0.b;
                        if(constraintWidget1.h0 == 8) {
                            v11 = v2;
                        }
                        else {
                            ++v10;
                            if(v8 > 0 && v8 >= v2) {
                                v7 += widgetRun0.h.f;
                            }
                            DimensionDependency dimensionDependency0 = widgetRun0.e;
                            int v12 = dimensionDependency0.g;
                            int v13 = widgetRun0.d == constraintWidget$DimensionBehaviour0 ? 0 : 1;
                            if(v13 == 0) {
                                v15 = 0;
                                v11 = v2;
                                if(widgetRun0.a == 1 && v6 == 0) {
                                    v16 = dimensionDependency0.m;
                                    ++v9;
                                    v15 = 1;
                                    goto label_66;
                                }
                                else if(dimensionDependency0.j) {
                                    v16 = v12;
                                    v15 = 1;
                                    goto label_66;
                                }
                            }
                            else {
                                int v14 = this.f;
                                v15 = v13;
                                if(v14 == 0 && !constraintWidget1.d.e.j) {
                                    return;
                                }
                                if(v14 == 1 && !constraintWidget1.e.e.j) {
                                    return;
                                }
                                v11 = v2;
                            }
                            v16 = v12;
                        label_66:
                            if(v15 == 0) {
                                ++v9;
                                float f1 = constraintWidget1.l0[this.f];
                                if(f1 >= 0.0f) {
                                    f += f1;
                                }
                            }
                            else {
                                v7 += v16;
                            }
                            if(v8 < v4 && v8 < v3) {
                                v7 -= widgetRun0.i.f;
                            }
                        }
                        ++v8;
                        v2 = v11;
                    }
                    arrayList1 = arrayList0;
                    v17 = v1;
                    v18 = v2;
                    if(v7 < v || v9 == 0) {
                        break;
                    }
                    ++v6;
                    arrayList0 = arrayList1;
                    v1 = v17;
                    v2 = v18;
                }
                int v19 = v9;
                int v20 = v10;
                goto label_97;
            label_90:
                arrayList1 = arrayList0;
                v17 = v1;
                v18 = v2;
                v19 = 0;
                v20 = 0;
                v7 = 0;
                f = 0.0f;
            label_97:
                int v21 = z ? dependencyNode1.g : dependencyNode0.g;
                if(v7 > v) {
                    v21 = z ? v21 + ((int)(((float)(v7 - v)) / 2.0f + 0.5f)) : v21 - ((int)(((float)(v7 - v)) / 2.0f + 0.5f));
                }
                if(v19 > 0) {
                    float f2 = (float)(v - v7);
                    int v22 = (int)(f2 / ((float)v19) + 0.5f);
                    v23 = v17;
                    int v24 = 0;
                    int v25 = 0;
                    while(v24 < v23) {
                        WidgetRun widgetRun1 = (WidgetRun)arrayList1.get(v24);
                        ConstraintWidget constraintWidget2 = widgetRun1.b;
                        if(constraintWidget2.h0 == 8 || widgetRun1.d != constraintWidget$DimensionBehaviour0) {
                            f3 = f2;
                            constraintWidget$DimensionBehaviour1 = constraintWidget$DimensionBehaviour0;
                        }
                        else {
                            DimensionDependency dimensionDependency1 = widgetRun1.e;
                            if(!dimensionDependency1.j) {
                                if(f > 0.0f) {
                                    constraintWidget$DimensionBehaviour1 = constraintWidget$DimensionBehaviour0;
                                    v26 = (int)(constraintWidget2.l0[this.f] * f2 / f + 0.5f);
                                }
                                else {
                                    constraintWidget$DimensionBehaviour1 = constraintWidget$DimensionBehaviour0;
                                    v26 = v22;
                                }
                                if(this.f == 0) {
                                    v27 = constraintWidget2.v;
                                    v28 = constraintWidget2.u;
                                }
                                else {
                                    v27 = constraintWidget2.y;
                                    v28 = constraintWidget2.x;
                                }
                                f3 = f2;
                                int v29 = widgetRun1.a == 1 ? Math.min(v26, dimensionDependency1.m) : v26;
                                int v30 = v27 <= 0 ? Math.max(v28, v29) : Math.min(v27, Math.max(v28, v29));
                                if(v30 != v26) {
                                    ++v25;
                                    v26 = v30;
                                }
                                dimensionDependency1.d(v26);
                            }
                        }
                        ++v24;
                        constraintWidget$DimensionBehaviour0 = constraintWidget$DimensionBehaviour1;
                        f2 = f3;
                    }
                    v31 = v21;
                    z1 = z;
                    constraintWidget$DimensionBehaviour2 = constraintWidget$DimensionBehaviour0;
                    arrayList2 = arrayList1;
                    if(v25 > 0) {
                        v19 -= v25;
                        int v32 = 0;
                        v7 = 0;
                        while(v32 < v23) {
                            WidgetRun widgetRun2 = (WidgetRun)arrayList2.get(v32);
                            if(widgetRun2.b.h0 == 8) {
                                v33 = v18;
                            }
                            else {
                                v33 = v18;
                                if(v32 > 0 && v32 >= v33) {
                                    v7 += widgetRun2.h.f;
                                }
                                v7 += widgetRun2.e.g;
                                if(v32 < v4 && v32 < v3) {
                                    v7 -= widgetRun2.i.f;
                                }
                            }
                            ++v32;
                            v18 = v33;
                        }
                    }
                    v34 = v18;
                    if(this.l == 2 && v25 == 0) {
                        this.l = 0;
                    }
                }
                else {
                    z1 = z;
                    constraintWidget$DimensionBehaviour2 = constraintWidget$DimensionBehaviour0;
                    arrayList2 = arrayList1;
                    v23 = v17;
                    v34 = v18;
                    v31 = v21;
                }
                if(v7 > v) {
                    this.l = 2;
                }
                if(v20 > 0 && v19 == 0 && v34 == v3) {
                    this.l = 2;
                }
                int v35 = this.l;
                if(v35 == 1) {
                    if(v20 > 1) {
                        v36 = (v - v7) / (v20 - 1);
                    }
                    else {
                        v36 = v20 == 1 ? (v - v7) / 2 : 0;
                    }
                    if(v19 > 0) {
                        v36 = 0;
                    }
                    int v37 = v31;
                    int v38 = 0;
                    while(v38 < v23) {
                        WidgetRun widgetRun3 = (WidgetRun)arrayList2.get((z1 ? v23 - (v38 + 1) : v38));
                        DependencyNode dependencyNode2 = widgetRun3.i;
                        DependencyNode dependencyNode3 = widgetRun3.h;
                        if(widgetRun3.b.h0 == 8) {
                            dependencyNode3.d(v37);
                            dependencyNode2.d(v37);
                            v39 = v36;
                            constraintWidget$DimensionBehaviour3 = constraintWidget$DimensionBehaviour2;
                        }
                        else {
                            if(v38 > 0) {
                                v37 = z1 ? v37 - v36 : v37 + v36;
                            }
                            if(v38 > 0 && v38 >= v34) {
                                v37 = z1 ? v37 - dependencyNode3.f : v37 + dependencyNode3.f;
                            }
                            if(z1) {
                                dependencyNode2.d(v37);
                            }
                            else {
                                dependencyNode3.d(v37);
                            }
                            DimensionDependency dimensionDependency2 = widgetRun3.e;
                            int v40 = dimensionDependency2.g;
                            constraintWidget$DimensionBehaviour3 = constraintWidget$DimensionBehaviour2;
                            if(widgetRun3.d == constraintWidget$DimensionBehaviour3) {
                                v39 = v36;
                                if(widgetRun3.a == 1) {
                                    v40 = dimensionDependency2.m;
                                }
                            }
                            else {
                                v39 = v36;
                            }
                            v37 = z1 ? v37 - v40 : v37 + v40;
                            if(z1) {
                                dependencyNode3.d(v37);
                            }
                            else {
                                dependencyNode2.d(v37);
                            }
                            widgetRun3.g = true;
                            if(v38 < v4 && v38 < v3) {
                                v37 = z1 ? v37 - -dependencyNode2.f : v37 - dependencyNode2.f;
                            }
                        }
                        ++v38;
                        v36 = v39;
                        constraintWidget$DimensionBehaviour2 = constraintWidget$DimensionBehaviour3;
                    }
                    return;
                }
            alab1:
                switch(v35) {
                    case 0: {
                        int v41 = (v - v7) / (v20 + 1);
                        if(v19 > 0) {
                            v41 = 0;
                        }
                        int v42 = v31;
                        for(int v43 = 0; v43 < v23; ++v43) {
                            WidgetRun widgetRun4 = (WidgetRun)arrayList2.get((z1 ? v23 - (v43 + 1) : v43));
                            DependencyNode dependencyNode4 = widgetRun4.i;
                            DependencyNode dependencyNode5 = widgetRun4.h;
                            if(widgetRun4.b.h0 == 8) {
                                dependencyNode5.d(v42);
                                dependencyNode4.d(v42);
                            }
                            else {
                                int v44 = z1 ? v42 - v41 : v42 + v41;
                                if(v43 > 0 && v43 >= v34) {
                                    v44 = z1 ? v44 - dependencyNode5.f : v44 + dependencyNode5.f;
                                }
                                if(z1) {
                                    dependencyNode4.d(v44);
                                }
                                else {
                                    dependencyNode5.d(v44);
                                }
                                int v45 = widgetRun4.d != constraintWidget$DimensionBehaviour2 || widgetRun4.a != 1 ? widgetRun4.e.g : Math.min(widgetRun4.e.g, widgetRun4.e.m);
                                v42 = z1 ? v44 - v45 : v44 + v45;
                                if(z1) {
                                    dependencyNode5.d(v42);
                                }
                                else {
                                    dependencyNode4.d(v42);
                                }
                                if(v43 < v4 && v43 < v3) {
                                    v42 = z1 ? v42 - -dependencyNode4.f : v42 - dependencyNode4.f;
                                }
                            }
                        }
                        return;
                    }
                    case 2: {
                        float f4 = this.f == 0 ? this.b.e0 : this.b.f0;
                        if(z1) {
                            f4 = 1.0f - f4;
                        }
                        int v46 = ((int)(((float)(v - v7)) * f4 + 0.5f)) >= 0 && v19 <= 0 ? ((int)(((float)(v - v7)) * f4 + 0.5f)) : 0;
                        int v47 = z1 ? v31 - v46 : v31 + v46;
                        for(int v48 = 0; true; ++v48) {
                            if(v48 >= v23) {
                                break alab1;
                            }
                            WidgetRun widgetRun5 = (WidgetRun)arrayList2.get((z1 ? v23 - (v48 + 1) : v48));
                            DependencyNode dependencyNode6 = widgetRun5.i;
                            DependencyNode dependencyNode7 = widgetRun5.h;
                            if(widgetRun5.b.h0 == 8) {
                                dependencyNode7.d(v47);
                                dependencyNode6.d(v47);
                            }
                            else {
                                if(v48 > 0 && v48 >= v34) {
                                    v47 = z1 ? v47 - dependencyNode7.f : v47 + dependencyNode7.f;
                                }
                                if(z1) {
                                    dependencyNode6.d(v47);
                                }
                                else {
                                    dependencyNode7.d(v47);
                                }
                                int v49 = widgetRun5.d != constraintWidget$DimensionBehaviour2 || widgetRun5.a != 1 ? widgetRun5.e.g : widgetRun5.e.m;
                                v47 = z1 ? v47 - v49 : v47 + v49;
                                if(z1) {
                                    dependencyNode7.d(v47);
                                }
                                else {
                                    dependencyNode6.d(v47);
                                }
                                if(v48 < v4 && v48 < v3) {
                                    v47 = z1 ? v47 - -dependencyNode6.f : v47 - dependencyNode6.f;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void d() {
        ArrayList arrayList0 = this.k;
        for(Object object0: arrayList0) {
            ((WidgetRun)object0).d();
        }
        int v = arrayList0.size();
        if(v < 1) {
            return;
        }
        ConstraintWidget constraintWidget0 = ((WidgetRun)arrayList0.get(0)).b;
        ConstraintWidget constraintWidget1 = ((WidgetRun)arrayList0.get(v - 1)).b;
        DependencyNode dependencyNode0 = this.i;
        DependencyNode dependencyNode1 = this.h;
        if(this.f == 0) {
            ConstraintAnchor constraintAnchor0 = constraintWidget1.K;
            DependencyNode dependencyNode2 = WidgetRun.i(constraintWidget0.I, 0);
            int v1 = constraintWidget0.I.e();
            ConstraintWidget constraintWidget2 = this.m();
            if(constraintWidget2 != null) {
                v1 = constraintWidget2.I.e();
            }
            if(dependencyNode2 != null) {
                WidgetRun.b(dependencyNode1, dependencyNode2, v1);
            }
            DependencyNode dependencyNode3 = WidgetRun.i(constraintAnchor0, 0);
            int v2 = constraintAnchor0.e();
            ConstraintWidget constraintWidget3 = this.n();
            if(constraintWidget3 != null) {
                v2 = constraintWidget3.K.e();
            }
            if(dependencyNode3 != null) {
                WidgetRun.b(dependencyNode0, dependencyNode3, -v2);
            }
        }
        else {
            ConstraintAnchor constraintAnchor1 = constraintWidget1.L;
            DependencyNode dependencyNode4 = WidgetRun.i(constraintWidget0.J, 1);
            int v3 = constraintWidget0.J.e();
            ConstraintWidget constraintWidget4 = this.m();
            if(constraintWidget4 != null) {
                v3 = constraintWidget4.J.e();
            }
            if(dependencyNode4 != null) {
                WidgetRun.b(dependencyNode1, dependencyNode4, v3);
            }
            DependencyNode dependencyNode5 = WidgetRun.i(constraintAnchor1, 1);
            int v4 = constraintAnchor1.e();
            ConstraintWidget constraintWidget5 = this.n();
            if(constraintWidget5 != null) {
                v4 = constraintWidget5.L.e();
            }
            if(dependencyNode5 != null) {
                WidgetRun.b(dependencyNode0, dependencyNode5, -v4);
            }
        }
        dependencyNode1.a = this;
        dependencyNode0.a = this;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void e() {
        for(int v = 0; true; ++v) {
            ArrayList arrayList0 = this.k;
            if(v >= arrayList0.size()) {
                break;
            }
            ((WidgetRun)arrayList0.get(v)).e();
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void f() {
        this.c = null;
        for(Object object0: this.k) {
            ((WidgetRun)object0).f();
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final long j() {
        ArrayList arrayList0 = this.k;
        int v = arrayList0.size();
        long v1 = 0L;
        for(int v2 = 0; v2 < v; ++v2) {
            WidgetRun widgetRun0 = (WidgetRun)arrayList0.get(v2);
            long v3 = v1 + ((long)widgetRun0.h.f);
            long v4 = widgetRun0.j();
            v1 = ((long)widgetRun0.i.f) + (v4 + v3);
        }
        return v1;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final boolean k() {
        ArrayList arrayList0 = this.k;
        int v = arrayList0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            if(!((WidgetRun)arrayList0.get(v1)).k()) {
                return false;
            }
        }
        return true;
    }

    public final ConstraintWidget m() {
        for(int v = 0; true; ++v) {
            ArrayList arrayList0 = this.k;
            if(v >= arrayList0.size()) {
                break;
            }
            ConstraintWidget constraintWidget0 = ((WidgetRun)arrayList0.get(v)).b;
            if(constraintWidget0.h0 != 8) {
                return constraintWidget0;
            }
        }
        return null;
    }

    public final ConstraintWidget n() {
        ArrayList arrayList0 = this.k;
        for(int v = arrayList0.size() - 1; v >= 0; --v) {
            ConstraintWidget constraintWidget0 = ((WidgetRun)arrayList0.get(v)).b;
            if(constraintWidget0.h0 != 8) {
                return constraintWidget0;
            }
        }
        return null;
    }

    @Override
    public final String toString() {
        StringBuilder stringBuilder0 = new StringBuilder("ChainRun ");
        stringBuilder0.append((this.f == 0 ? "horizontal : " : "vertical : "));
        for(Object object0: this.k) {
            stringBuilder0.append("<");
            stringBuilder0.append(((WidgetRun)object0));
            stringBuilder0.append("> ");
        }
        return stringBuilder0.toString();
    }
}


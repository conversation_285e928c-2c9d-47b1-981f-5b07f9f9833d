package androidx.constraintlayout.core.widgets.analyzer;

import java.util.ArrayList;

class RunGroup {
    public WidgetRun a;
    public ArrayList b;

    public static long a(DependencyNode dependencyNode0, long v) {
        WidgetRun widgetRun0 = dependencyNode0.d;
        if(widgetRun0 instanceof HelperReferences) {
            return v;
        }
        ArrayList arrayList0 = dependencyNode0.k;
        int v1 = arrayList0.size();
        long v3 = v;
        for(int v2 = 0; v2 < v1; ++v2) {
            Dependency dependency0 = (Dependency)arrayList0.get(v2);
            if(dependency0 instanceof DependencyNode && ((DependencyNode)dependency0).d != widgetRun0) {
                v3 = Math.min(v3, RunGroup.a(((DependencyNode)dependency0), ((long)((DependencyNode)dependency0).f) + v));
            }
        }
        if(dependencyNode0 == widgetRun0.i) {
            long v4 = v - widgetRun0.j();
            return Math.min(Math.min(v3, RunGroup.a(widgetRun0.h, v4)), v4 - ((long)widgetRun0.h.f));
        }
        return v3;
    }

    public static long b(DependencyNode dependencyNode0, long v) {
        WidgetRun widgetRun0 = dependencyNode0.d;
        if(widgetRun0 instanceof HelperReferences) {
            return v;
        }
        ArrayList arrayList0 = dependencyNode0.k;
        int v1 = arrayList0.size();
        long v3 = v;
        for(int v2 = 0; v2 < v1; ++v2) {
            Dependency dependency0 = (Dependency)arrayList0.get(v2);
            if(dependency0 instanceof DependencyNode && ((DependencyNode)dependency0).d != widgetRun0) {
                v3 = Math.max(v3, RunGroup.b(((DependencyNode)dependency0), ((long)((DependencyNode)dependency0).f) + v));
            }
        }
        if(dependencyNode0 == widgetRun0.h) {
            long v4 = v + widgetRun0.j();
            return Math.max(Math.max(v3, RunGroup.b(widgetRun0.i, v4)), v4 - ((long)widgetRun0.i.f));
        }
        return v3;
    }
}


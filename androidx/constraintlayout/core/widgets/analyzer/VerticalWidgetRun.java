package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.ConstraintAnchor;
import androidx.constraintlayout.core.widgets.ConstraintWidget.DimensionBehaviour;
import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.Helper;

public class VerticalWidgetRun extends WidgetRun {
    public final DependencyNode k;
    public BaselineDimensionDependency l;

    public VerticalWidgetRun(ConstraintWidget constraintWidget0) {
        super(constraintWidget0);
        DependencyNode dependencyNode0 = new DependencyNode(this);
        this.k = dependencyNode0;
        this.l = null;
        this.h.e = Type.f;
        this.i.e = Type.g;
        dependencyNode0.e = Type.h;
        this.f = 1;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.Dependency
    public final void a(Dependency dependency0) {
        int v;
        if(this.j.ordinal() != 3) {
            DimensionDependency dimensionDependency0 = this.e;
            DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.c;
            if(dimensionDependency0.c && !dimensionDependency0.j && this.d == constraintWidget$DimensionBehaviour0) {
                ConstraintWidget constraintWidget0 = this.b;
                switch(constraintWidget0.s) {
                    case 2: {
                        ConstraintWidget constraintWidget1 = constraintWidget0.U;
                        if(constraintWidget1 != null) {
                            DimensionDependency dimensionDependency1 = constraintWidget1.e.e;
                            if(dimensionDependency1.j) {
                                dimensionDependency0.d(((int)(((float)dimensionDependency1.g) * constraintWidget0.z + 0.5f)));
                            }
                        }
                        break;
                    }
                    case 3: {
                        DimensionDependency dimensionDependency2 = constraintWidget0.d.e;
                        if(dimensionDependency2.j) {
                            switch(constraintWidget0.Y) {
                                case -1: {
                                    v = (int)(((float)dimensionDependency2.g) / constraintWidget0.X + 0.5f);
                                    break;
                                }
                                case 0: {
                                    v = (int)(((float)dimensionDependency2.g) * constraintWidget0.X + 0.5f);
                                    break;
                                }
                                case 1: {
                                    v = (int)(((float)dimensionDependency2.g) / constraintWidget0.X + 0.5f);
                                    break;
                                }
                                default: {
                                    v = 0;
                                }
                            }
                            dimensionDependency0.d(v);
                        }
                    }
                }
            }
            DependencyNode dependencyNode0 = this.h;
            if(dependencyNode0.c) {
                DependencyNode dependencyNode1 = this.i;
                if(!dependencyNode1.c || dependencyNode0.j && dependencyNode1.j && dimensionDependency0.j) {
                    return;
                }
                if(!dimensionDependency0.j && this.d == constraintWidget$DimensionBehaviour0 && (this.b.r == 0 && !this.b.z())) {
                    DependencyNode dependencyNode2 = (DependencyNode)dependencyNode0.l.get(0);
                    DependencyNode dependencyNode3 = (DependencyNode)dependencyNode1.l.get(0);
                    int v1 = dependencyNode2.g + dependencyNode0.f;
                    int v2 = dependencyNode3.g + dependencyNode1.f;
                    dependencyNode0.d(v1);
                    dependencyNode1.d(v2);
                    dimensionDependency0.d(v2 - v1);
                    return;
                }
                if(!dimensionDependency0.j && this.d == constraintWidget$DimensionBehaviour0 && this.a == 1 && dependencyNode0.l.size() > 0 && dependencyNode1.l.size() > 0) {
                    DependencyNode dependencyNode4 = (DependencyNode)dependencyNode0.l.get(0);
                    int v3 = ((DependencyNode)dependencyNode1.l.get(0)).g + dependencyNode1.f - (dependencyNode4.g + dependencyNode0.f);
                    int v4 = dimensionDependency0.m;
                    if(v3 < v4) {
                        dimensionDependency0.d(v3);
                    }
                    else {
                        dimensionDependency0.d(v4);
                    }
                }
                if(!dimensionDependency0.j) {
                    return;
                }
                if(dependencyNode0.l.size() > 0 && dependencyNode1.l.size() > 0) {
                    DependencyNode dependencyNode5 = (DependencyNode)dependencyNode0.l.get(0);
                    DependencyNode dependencyNode6 = (DependencyNode)dependencyNode1.l.get(0);
                    int v5 = dependencyNode5.g;
                    int v6 = dependencyNode0.f + v5;
                    int v7 = dependencyNode6.g;
                    int v8 = dependencyNode1.f + v7;
                    float f = this.b.f0;
                    if(dependencyNode5 == dependencyNode6) {
                        f = 0.5f;
                    }
                    else {
                        v5 = v6;
                        v7 = v8;
                    }
                    dependencyNode0.d(((int)(((float)(v7 - v5 - dimensionDependency0.g)) * f + (((float)v5) + 0.5f))));
                    dependencyNode1.d(dependencyNode0.g + dimensionDependency0.g);
                }
            }
            return;
        }
        this.l(this.b.J, this.b.L, 1);
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void d() {
        ConstraintWidget constraintWidget0 = this.b;
        DimensionDependency dimensionDependency0 = this.e;
        if(constraintWidget0.a) {
            dimensionDependency0.d(constraintWidget0.l());
        }
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.d;
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = DimensionBehaviour.a;
        DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.c;
        DependencyNode dependencyNode0 = this.i;
        DependencyNode dependencyNode1 = this.h;
        if(!dimensionDependency0.j) {
            ConstraintWidget constraintWidget1 = this.b;
            this.d = constraintWidget1.T[1];
            if(constraintWidget1.E) {
                this.l = new BaselineDimensionDependency(this);  // 初始化器: Landroidx/constraintlayout/core/widgets/analyzer/DimensionDependency;-><init>(Landroidx/constraintlayout/core/widgets/analyzer/WidgetRun;)V
            }
            DimensionBehaviour constraintWidget$DimensionBehaviour3 = this.d;
            if(constraintWidget$DimensionBehaviour3 != constraintWidget$DimensionBehaviour2) {
                if(constraintWidget$DimensionBehaviour3 == constraintWidget$DimensionBehaviour0) {
                    ConstraintWidget constraintWidget2 = this.b.U;
                    if(constraintWidget2 != null && constraintWidget2.T[1] == constraintWidget$DimensionBehaviour1) {
                        int v = constraintWidget2.l();
                        int v1 = this.b.J.e();
                        int v2 = this.b.L.e();
                        WidgetRun.b(dependencyNode1, constraintWidget2.e.h, this.b.J.e());
                        WidgetRun.b(dependencyNode0, constraintWidget2.e.i, -this.b.L.e());
                        dimensionDependency0.d(v - v1 - v2);
                        return;
                    }
                }
                if(constraintWidget$DimensionBehaviour3 == constraintWidget$DimensionBehaviour1) {
                    dimensionDependency0.d(this.b.l());
                }
            }
        }
        else if(this.d == constraintWidget$DimensionBehaviour0) {
            ConstraintWidget constraintWidget3 = this.b;
            ConstraintWidget constraintWidget4 = constraintWidget3.U;
            if(constraintWidget4 != null && constraintWidget4.T[1] == constraintWidget$DimensionBehaviour1) {
                WidgetRun.b(dependencyNode1, constraintWidget4.e.h, constraintWidget3.J.e());
                WidgetRun.b(dependencyNode0, constraintWidget4.e.i, -this.b.L.e());
                return;
            }
        }
        boolean z = dimensionDependency0.j;
        DependencyNode dependencyNode2 = this.k;
        if(z) {
            ConstraintWidget constraintWidget5 = this.b;
            if(constraintWidget5.a) {
                ConstraintAnchor[] arr_constraintAnchor = constraintWidget5.Q;
                ConstraintAnchor constraintAnchor0 = arr_constraintAnchor[2];
                ConstraintAnchor constraintAnchor1 = constraintAnchor0.f;
                if(constraintAnchor1 != null && arr_constraintAnchor[3].f != null) {
                    if(constraintWidget5.z()) {
                        dependencyNode1.f = this.b.Q[2].e();
                        dependencyNode0.f = -this.b.Q[3].e();
                    }
                    else {
                        DependencyNode dependencyNode3 = WidgetRun.h(this.b.Q[2]);
                        if(dependencyNode3 != null) {
                            WidgetRun.b(dependencyNode1, dependencyNode3, this.b.Q[2].e());
                        }
                        DependencyNode dependencyNode4 = WidgetRun.h(this.b.Q[3]);
                        if(dependencyNode4 != null) {
                            WidgetRun.b(dependencyNode0, dependencyNode4, -this.b.Q[3].e());
                        }
                        dependencyNode1.b = true;
                        dependencyNode0.b = true;
                    }
                    ConstraintWidget constraintWidget6 = this.b;
                    if(constraintWidget6.E) {
                        WidgetRun.b(dependencyNode2, dependencyNode1, constraintWidget6.b0);
                    }
                }
                else if(constraintAnchor1 != null) {
                    DependencyNode dependencyNode5 = WidgetRun.h(constraintAnchor0);
                    if(dependencyNode5 != null) {
                        WidgetRun.b(dependencyNode1, dependencyNode5, this.b.Q[2].e());
                        WidgetRun.b(dependencyNode0, dependencyNode1, dimensionDependency0.g);
                        ConstraintWidget constraintWidget7 = this.b;
                        if(constraintWidget7.E) {
                            WidgetRun.b(dependencyNode2, dependencyNode1, constraintWidget7.b0);
                        }
                    }
                }
                else {
                    ConstraintAnchor constraintAnchor2 = arr_constraintAnchor[3];
                    if(constraintAnchor2.f == null) {
                        ConstraintAnchor constraintAnchor3 = arr_constraintAnchor[4];
                        if(constraintAnchor3.f != null) {
                            DependencyNode dependencyNode7 = WidgetRun.h(constraintAnchor3);
                            if(dependencyNode7 != null) {
                                WidgetRun.b(dependencyNode2, dependencyNode7, 0);
                                WidgetRun.b(dependencyNode1, dependencyNode2, -this.b.b0);
                                WidgetRun.b(dependencyNode0, dependencyNode1, dimensionDependency0.g);
                            }
                        }
                        else if(!(constraintWidget5 instanceof Helper) && constraintWidget5.U != null && constraintWidget5.j(androidx.constraintlayout.core.widgets.ConstraintAnchor.Type.f).f == null) {
                            WidgetRun.b(dependencyNode1, this.b.U.e.h, this.b.t());
                            WidgetRun.b(dependencyNode0, dependencyNode1, dimensionDependency0.g);
                            ConstraintWidget constraintWidget9 = this.b;
                            if(constraintWidget9.E) {
                                WidgetRun.b(dependencyNode2, dependencyNode1, constraintWidget9.b0);
                            }
                        }
                    }
                    else {
                        DependencyNode dependencyNode6 = WidgetRun.h(constraintAnchor2);
                        if(dependencyNode6 != null) {
                            WidgetRun.b(dependencyNode0, dependencyNode6, -this.b.Q[3].e());
                            WidgetRun.b(dependencyNode1, dependencyNode0, -dimensionDependency0.g);
                        }
                        ConstraintWidget constraintWidget8 = this.b;
                        if(constraintWidget8.E) {
                            WidgetRun.b(dependencyNode2, dependencyNode1, constraintWidget8.b0);
                        }
                    }
                }
            }
            else {
                goto label_95;
            }
        }
        else {
        label_95:
            if(z || this.d != constraintWidget$DimensionBehaviour2) {
                dimensionDependency0.b(this);
            }
            else {
                ConstraintWidget constraintWidget10 = this.b;
                switch(constraintWidget10.s) {
                    case 2: {
                        ConstraintWidget constraintWidget11 = constraintWidget10.U;
                        if(constraintWidget11 != null) {
                            DimensionDependency dimensionDependency1 = constraintWidget11.e.e;
                            dimensionDependency0.l.add(dimensionDependency1);
                            dimensionDependency1.k.add(dimensionDependency0);
                            dimensionDependency0.b = true;
                            dimensionDependency0.k.add(dependencyNode1);
                            dimensionDependency0.k.add(dependencyNode0);
                        }
                        break;
                    }
                    case 3: {
                        if(!constraintWidget10.z()) {
                            ConstraintWidget constraintWidget12 = this.b;
                            if(constraintWidget12.r != 3) {
                                DimensionDependency dimensionDependency2 = constraintWidget12.d.e;
                                dimensionDependency0.l.add(dimensionDependency2);
                                dimensionDependency2.k.add(dimensionDependency0);
                                dimensionDependency0.b = true;
                                dimensionDependency0.k.add(dependencyNode1);
                                dimensionDependency0.k.add(dependencyNode0);
                            }
                        }
                    }
                }
            }
            ConstraintWidget constraintWidget13 = this.b;
            ConstraintAnchor[] arr_constraintAnchor1 = constraintWidget13.Q;
            ConstraintAnchor constraintAnchor4 = arr_constraintAnchor1[2];
            ConstraintAnchor constraintAnchor5 = constraintAnchor4.f;
            if(constraintAnchor5 != null && arr_constraintAnchor1[3].f != null) {
                if(constraintWidget13.z()) {
                    dependencyNode1.f = this.b.Q[2].e();
                    dependencyNode0.f = -this.b.Q[3].e();
                }
                else {
                    DependencyNode dependencyNode8 = WidgetRun.h(this.b.Q[2]);
                    DependencyNode dependencyNode9 = WidgetRun.h(this.b.Q[3]);
                    if(dependencyNode8 != null) {
                        dependencyNode8.b(this);
                    }
                    if(dependencyNode9 != null) {
                        dependencyNode9.b(this);
                    }
                    this.j = RunType.b;
                }
                if(this.b.E) {
                    this.c(dependencyNode2, dependencyNode1, 1, this.l);
                }
            }
            else if(constraintAnchor5 == null) {
                ConstraintAnchor constraintAnchor6 = arr_constraintAnchor1[3];
                if(constraintAnchor6.f == null) {
                    ConstraintAnchor constraintAnchor7 = arr_constraintAnchor1[4];
                    if(constraintAnchor7.f != null) {
                        DependencyNode dependencyNode12 = WidgetRun.h(constraintAnchor7);
                        if(dependencyNode12 != null) {
                            WidgetRun.b(dependencyNode2, dependencyNode12, 0);
                            this.c(dependencyNode1, dependencyNode2, -1, this.l);
                            this.c(dependencyNode0, dependencyNode1, 1, dimensionDependency0);
                        }
                    }
                    else if(!(constraintWidget13 instanceof Helper)) {
                        ConstraintWidget constraintWidget15 = constraintWidget13.U;
                        if(constraintWidget15 != null) {
                            WidgetRun.b(dependencyNode1, constraintWidget15.e.h, constraintWidget13.t());
                            this.c(dependencyNode0, dependencyNode1, 1, dimensionDependency0);
                            if(this.b.E) {
                                this.c(dependencyNode2, dependencyNode1, 1, this.l);
                            }
                            if(this.d == constraintWidget$DimensionBehaviour2) {
                                ConstraintWidget constraintWidget16 = this.b;
                                if(constraintWidget16.X > 0.0f) {
                                    HorizontalWidgetRun horizontalWidgetRun1 = constraintWidget16.d;
                                    if(horizontalWidgetRun1.d == constraintWidget$DimensionBehaviour2) {
                                        horizontalWidgetRun1.e.k.add(dimensionDependency0);
                                        dimensionDependency0.l.add(this.b.d.e);
                                        dimensionDependency0.a = this;
                                    }
                                }
                            }
                        }
                    }
                }
                else {
                    DependencyNode dependencyNode11 = WidgetRun.h(constraintAnchor6);
                    if(dependencyNode11 != null) {
                        WidgetRun.b(dependencyNode0, dependencyNode11, -this.b.Q[3].e());
                        this.c(dependencyNode1, dependencyNode0, -1, dimensionDependency0);
                        if(this.b.E) {
                            this.c(dependencyNode2, dependencyNode1, 1, this.l);
                        }
                    }
                }
            }
            else {
                DependencyNode dependencyNode10 = WidgetRun.h(constraintAnchor4);
                if(dependencyNode10 != null) {
                    WidgetRun.b(dependencyNode1, dependencyNode10, this.b.Q[2].e());
                    this.c(dependencyNode0, dependencyNode1, 1, dimensionDependency0);
                    if(this.b.E) {
                        this.c(dependencyNode2, dependencyNode1, 1, this.l);
                    }
                    if(this.d == constraintWidget$DimensionBehaviour2) {
                        ConstraintWidget constraintWidget14 = this.b;
                        if(constraintWidget14.X > 0.0f) {
                            HorizontalWidgetRun horizontalWidgetRun0 = constraintWidget14.d;
                            if(horizontalWidgetRun0.d == constraintWidget$DimensionBehaviour2) {
                                horizontalWidgetRun0.e.k.add(dimensionDependency0);
                                dimensionDependency0.l.add(this.b.d.e);
                                dimensionDependency0.a = this;
                            }
                        }
                    }
                }
            }
            if(dimensionDependency0.l.size() == 0) {
                dimensionDependency0.c = true;
            }
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void e() {
        DependencyNode dependencyNode0 = this.h;
        if(dependencyNode0.j) {
            this.b.a0 = dependencyNode0.g;
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void f() {
        this.c = null;
        this.h.c();
        this.i.c();
        this.k.c();
        this.e.c();
        this.g = false;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final boolean k() {
        return this.d != DimensionBehaviour.c || this.b.s == 0;
    }

    public final void m() {
        this.g = false;
        this.h.c();
        this.h.j = false;
        this.i.c();
        this.i.j = false;
        this.k.c();
        this.k.j = false;
        this.e.j = false;
    }

    @Override
    public final String toString() {
        return "VerticalRun " + this.b.i0;
    }
}


package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.ConstraintAnchor;
import androidx.constraintlayout.core.widgets.ConstraintWidget.DimensionBehaviour;
import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.Guideline;
import androidx.constraintlayout.core.widgets.HelperWidget;
import java.util.ArrayList;

public abstract class Grouping {
    public static WidgetGroup a(ConstraintWidget constraintWidget0, int v, ArrayList arrayList0, WidgetGroup widgetGroup0) {
        int v1 = v == 0 ? constraintWidget0.o0 : constraintWidget0.p0;
        int v2 = 0;
        if(v1 != -1 && (widgetGroup0 == null || v1 != widgetGroup0.b)) {
            for(int v3 = 0; v3 < arrayList0.size(); ++v3) {
                WidgetGroup widgetGroup1 = (WidgetGroup)arrayList0.get(v3);
                if(widgetGroup1.b == v1) {
                    if(widgetGroup0 != null) {
                        widgetGroup0.c(v, widgetGroup1);
                        arrayList0.remove(widgetGroup0);
                    }
                    widgetGroup0 = widgetGroup1;
                    break;
                }
            }
        }
        else if(v1 != -1) {
            return widgetGroup0;
        }
        if(widgetGroup0 == null) {
            if(constraintWidget0 instanceof HelperWidget) {
                for(int v4 = 0; true; ++v4) {
                    int v5 = -1;
                    if(v4 >= ((HelperWidget)constraintWidget0).r0) {
                        break;
                    }
                    ConstraintWidget constraintWidget1 = ((HelperWidget)constraintWidget0).q0[v4];
                    if(v == 0) {
                        int v6 = constraintWidget1.o0;
                        if(v6 != -1) {
                            v5 = v6;
                            break;
                        }
                    }
                    if(v == 1) {
                        int v7 = constraintWidget1.p0;
                        if(v7 != -1) {
                            v5 = v7;
                            break;
                        }
                    }
                }
                if(v5 != -1) {
                    for(int v8 = 0; v8 < arrayList0.size(); ++v8) {
                        WidgetGroup widgetGroup2 = (WidgetGroup)arrayList0.get(v8);
                        if(widgetGroup2.b == v5) {
                            widgetGroup0 = widgetGroup2;
                            break;
                        }
                    }
                }
            }
            if(widgetGroup0 == null) {
                widgetGroup0 = new WidgetGroup();  // 初始化器: Ljava/lang/Object;-><init>()V
                widgetGroup0.a = new ArrayList();
                widgetGroup0.d = null;
                widgetGroup0.e = -1;
                int v9 = WidgetGroup.f;
                WidgetGroup.f = v9 + 1;
                widgetGroup0.b = v9;
                widgetGroup0.c = v;
            }
            arrayList0.add(widgetGroup0);
        }
        ArrayList arrayList1 = widgetGroup0.a;
        if(!arrayList1.contains(constraintWidget0)) {
            arrayList1.add(constraintWidget0);
            if(constraintWidget0 instanceof Guideline) {
                ConstraintAnchor constraintAnchor0 = ((Guideline)constraintWidget0).t0;
                if(((Guideline)constraintWidget0).u0 == 0) {
                    v2 = 1;
                }
                constraintAnchor0.c(v2, widgetGroup0, arrayList0);
            }
            int v10 = widgetGroup0.b;
            if(v == 0) {
                constraintWidget0.o0 = v10;
                constraintWidget0.I.c(0, widgetGroup0, arrayList0);
                constraintWidget0.K.c(0, widgetGroup0, arrayList0);
            }
            else {
                constraintWidget0.p0 = v10;
                constraintWidget0.J.c(v, widgetGroup0, arrayList0);
                constraintWidget0.M.c(v, widgetGroup0, arrayList0);
                constraintWidget0.L.c(v, widgetGroup0, arrayList0);
            }
            constraintWidget0.P.c(v, widgetGroup0, arrayList0);
        }
        return widgetGroup0;
    }

    // 去混淆评级： 低(40)
    public static boolean b(DimensionBehaviour constraintWidget$DimensionBehaviour0, DimensionBehaviour constraintWidget$DimensionBehaviour1, DimensionBehaviour constraintWidget$DimensionBehaviour2, DimensionBehaviour constraintWidget$DimensionBehaviour3) {
        return constraintWidget$DimensionBehaviour2 == DimensionBehaviour.a || constraintWidget$DimensionBehaviour2 == DimensionBehaviour.b || constraintWidget$DimensionBehaviour2 == DimensionBehaviour.d && constraintWidget$DimensionBehaviour0 != DimensionBehaviour.b || (constraintWidget$DimensionBehaviour3 == DimensionBehaviour.a || constraintWidget$DimensionBehaviour3 == DimensionBehaviour.b || constraintWidget$DimensionBehaviour3 == DimensionBehaviour.d && constraintWidget$DimensionBehaviour1 != DimensionBehaviour.b);
    }
}


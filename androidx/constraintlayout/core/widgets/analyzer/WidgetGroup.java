package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.LinearSystem;
import androidx.constraintlayout.core.widgets.Chain;
import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.ConstraintWidgetContainer;
import androidx.work.impl.model.c;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;

public class WidgetGroup {
    static class MeasureResult {
    }

    public ArrayList a;
    public int b;
    public int c;
    public ArrayList d;
    public int e;
    public static int f;

    public final void a(ArrayList arrayList0) {
        int v = this.a.size();
        if(this.e != -1 && v > 0) {
            for(int v1 = 0; v1 < arrayList0.size(); ++v1) {
                WidgetGroup widgetGroup0 = (WidgetGroup)arrayList0.get(v1);
                if(this.e == widgetGroup0.b) {
                    this.c(this.c, widgetGroup0);
                }
            }
        }
        if(v == 0) {
            arrayList0.remove(this);
        }
    }

    public final int b(LinearSystem linearSystem0, int v) {
        int v4;
        int v3;
        ArrayList arrayList0 = this.a;
        if(arrayList0.size() == 0) {
            return 0;
        }
        ConstraintWidgetContainer constraintWidgetContainer0 = (ConstraintWidgetContainer)((ConstraintWidget)arrayList0.get(0)).U;
        linearSystem0.t();
        constraintWidgetContainer0.c(linearSystem0, false);
        for(int v2 = 0; v2 < arrayList0.size(); ++v2) {
            ((ConstraintWidget)arrayList0.get(v2)).c(linearSystem0, false);
        }
        if(v == 0 && constraintWidgetContainer0.z0 > 0) {
            Chain.a(constraintWidgetContainer0, linearSystem0, arrayList0, 0);
        }
        if(v == 1 && constraintWidgetContainer0.A0 > 0) {
            Chain.a(constraintWidgetContainer0, linearSystem0, arrayList0, 1);
        }
        try {
            linearSystem0.p();
        }
        catch(Exception exception0) {
            System.err.println(exception0.toString() + "\n" + Arrays.toString(exception0.getStackTrace()).replace("[", "   at ").replace(",", "\n   at").replace("]", ""));
        }
        this.d = new ArrayList();
        for(int v1 = 0; v1 < arrayList0.size(); ++v1) {
            ConstraintWidget constraintWidget0 = (ConstraintWidget)arrayList0.get(v1);
            MeasureResult widgetGroup$MeasureResult0 = new MeasureResult();  // 初始化器: Ljava/lang/Object;-><init>()V
            new WeakReference(constraintWidget0);
            this.d.add(widgetGroup$MeasureResult0);
        }
        if(v == 0) {
            v3 = LinearSystem.n(constraintWidgetContainer0.I);
            v4 = LinearSystem.n(constraintWidgetContainer0.K);
            linearSystem0.t();
            return v4 - v3;
        }
        v3 = LinearSystem.n(constraintWidgetContainer0.J);
        v4 = LinearSystem.n(constraintWidgetContainer0.L);
        linearSystem0.t();
        return v4 - v3;
    }

    public final void c(int v, WidgetGroup widgetGroup0) {
        for(Object object0: this.a) {
            ConstraintWidget constraintWidget0 = (ConstraintWidget)object0;
            ArrayList arrayList0 = widgetGroup0.a;
            if(!arrayList0.contains(constraintWidget0)) {
                arrayList0.add(constraintWidget0);
            }
            int v1 = widgetGroup0.b;
            if(v == 0) {
                constraintWidget0.o0 = v1;
            }
            else {
                constraintWidget0.p0 = v1;
            }
        }
        this.e = widgetGroup0.b;
    }

    @Override
    public final String toString() {
        String s;
        StringBuilder stringBuilder0 = new StringBuilder();
        int v = this.c;
        if(v == 0) {
            s = "Horizontal";
        }
        else {
            switch(v) {
                case 1: {
                    s = "Vertical";
                    break;
                }
                case 2: {
                    s = "Both";
                    break;
                }
                default: {
                    s = "Unknown";
                }
            }
        }
        stringBuilder0.append(s);
        stringBuilder0.append(" [");
        String s1 = c.l(stringBuilder0, this.b, "] <");
        for(Object object0: this.a) {
            StringBuilder stringBuilder1 = c.o(s1, " ");
            stringBuilder1.append(((ConstraintWidget)object0).i0);
            s1 = stringBuilder1.toString();
        }
        return s1 + " >";
    }
}


package androidx.constraintlayout.core.widgets.analyzer;

import java.util.ArrayList;

public class DependencyNode implements Dependency {
    static enum Type {
        UNKNOWN,
        HORIZONTAL_DIMENSION,
        VERTICAL_DIMENSION,
        LEFT,
        RIGHT,
        TOP,
        BOTTOM,
        BASELINE;

    }

    public WidgetRun a;
    public boolean b;
    public boolean c;
    public final WidgetRun d;
    public Type e;
    public int f;
    public int g;
    public int h;
    public DimensionDependency i;
    public boolean j;
    public final ArrayList k;
    public final ArrayList l;

    public DependencyNode(WidgetRun widgetRun0) {
        this.a = null;
        this.b = false;
        this.c = false;
        this.e = Type.a;
        this.h = 1;
        this.i = null;
        this.j = false;
        this.k = new ArrayList();
        this.l = new ArrayList();
        this.d = widgetRun0;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.Dependency
    public final void a(Dependency dependency0) {
        ArrayList arrayList0 = this.l;
        for(Object object0: arrayList0) {
            if(!((DependencyNode)object0).j) {
                return;
            }
            if(false) {
                break;
            }
        }
        this.c = true;
        WidgetRun widgetRun0 = this.a;
        if(widgetRun0 != null) {
            widgetRun0.a(this);
        }
        if(this.b) {
            this.d.a(this);
            return;
        }
        DependencyNode dependencyNode0 = null;
        int v = 0;
        for(Object object1: arrayList0) {
            DependencyNode dependencyNode1 = (DependencyNode)object1;
            if(!(dependencyNode1 instanceof DimensionDependency)) {
                ++v;
                dependencyNode0 = dependencyNode1;
            }
        }
        if(dependencyNode0 != null && v == 1 && dependencyNode0.j) {
            DimensionDependency dimensionDependency0 = this.i;
            if(dimensionDependency0 != null) {
                if(dimensionDependency0.j) {
                    this.f = this.h * dimensionDependency0.g;
                    goto label_31;
                }
                return;
            }
        label_31:
            this.d(dependencyNode0.g + this.f);
        }
        WidgetRun widgetRun1 = this.a;
        if(widgetRun1 != null) {
            widgetRun1.a(this);
        }
    }

    public final void b(Dependency dependency0) {
        this.k.add(dependency0);
        if(this.j) {
            dependency0.a(dependency0);
        }
    }

    public final void c() {
        this.l.clear();
        this.k.clear();
        this.j = false;
        this.g = 0;
        this.c = false;
        this.b = false;
    }

    public void d(int v) {
        if(this.j) {
            return;
        }
        this.j = true;
        this.g = v;
        for(Object object0: this.k) {
            ((Dependency)object0).a(((Dependency)object0));
        }
    }

    @Override
    public final String toString() {
        StringBuilder stringBuilder0 = new StringBuilder();
        stringBuilder0.append(this.d.b.i0);
        stringBuilder0.append(":");
        stringBuilder0.append(this.e);
        stringBuilder0.append("(");
        Integer integer0 = this.j ? this.g : "unresolved";
        stringBuilder0.append(integer0);
        stringBuilder0.append(") <t=");
        stringBuilder0.append(this.l.size());
        stringBuilder0.append(":d=");
        stringBuilder0.append(this.k.size());
        stringBuilder0.append(">");
        return stringBuilder0.toString();
    }
}


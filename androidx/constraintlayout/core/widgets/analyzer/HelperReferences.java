package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.Barrier;
import androidx.constraintlayout.core.widgets.ConstraintWidget;

class HelperReferences extends WidgetRun {
    @Override  // androidx.constraintlayout.core.widgets.analyzer.Dependency
    public final void a(Dependency dependency0) {
        Barrier barrier0 = (Barrier)this.b;
        int v = barrier0.s0;
        DependencyNode dependencyNode0 = this.h;
        int v1 = 0;
        int v2 = -1;
        for(Object object0: dependencyNode0.l) {
            int v3 = ((DependencyNode)object0).g;
            if(v2 == -1 || v3 < v2) {
                v2 = v3;
            }
            if(v1 < v3) {
                v1 = v3;
            }
        }
        if(v != 0 && v != 2) {
            dependencyNode0.d(v1 + barrier0.u0);
            return;
        }
        dependencyNode0.d(v2 + barrier0.u0);
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void d() {
        ConstraintWidget constraintWidget0 = this.b;
        if(constraintWidget0 instanceof Barrier) {
            DependencyNode dependencyNode0 = this.h;
            dependencyNode0.b = true;
            int v = ((Barrier)constraintWidget0).s0;
            boolean z = ((Barrier)constraintWidget0).t0;
            int v1 = 0;
            switch(v) {
                case 0: {
                    dependencyNode0.e = Type.d;
                    while(v1 < ((Barrier)constraintWidget0).r0) {
                        ConstraintWidget constraintWidget4 = ((Barrier)constraintWidget0).q0[v1];
                        if(z || constraintWidget4.h0 != 8) {
                            DependencyNode dependencyNode4 = constraintWidget4.d.h;
                            dependencyNode4.k.add(dependencyNode0);
                            dependencyNode0.l.add(dependencyNode4);
                        }
                        ++v1;
                    }
                    this.m(this.b.d.h);
                    this.m(this.b.d.i);
                    break;
                }
                case 1: {
                    dependencyNode0.e = Type.e;
                    while(v1 < ((Barrier)constraintWidget0).r0) {
                        ConstraintWidget constraintWidget1 = ((Barrier)constraintWidget0).q0[v1];
                        if(z || constraintWidget1.h0 != 8) {
                            DependencyNode dependencyNode1 = constraintWidget1.d.i;
                            dependencyNode1.k.add(dependencyNode0);
                            dependencyNode0.l.add(dependencyNode1);
                        }
                        ++v1;
                    }
                    this.m(this.b.d.h);
                    this.m(this.b.d.i);
                    return;
                }
                case 2: {
                    dependencyNode0.e = Type.f;
                    while(v1 < ((Barrier)constraintWidget0).r0) {
                        ConstraintWidget constraintWidget2 = ((Barrier)constraintWidget0).q0[v1];
                        if(z || constraintWidget2.h0 != 8) {
                            DependencyNode dependencyNode2 = constraintWidget2.e.h;
                            dependencyNode2.k.add(dependencyNode0);
                            dependencyNode0.l.add(dependencyNode2);
                        }
                        ++v1;
                    }
                    this.m(this.b.e.h);
                    this.m(this.b.e.i);
                    return;
                }
                case 3: {
                    dependencyNode0.e = Type.g;
                    while(v1 < ((Barrier)constraintWidget0).r0) {
                        ConstraintWidget constraintWidget3 = ((Barrier)constraintWidget0).q0[v1];
                        if(z || constraintWidget3.h0 != 8) {
                            DependencyNode dependencyNode3 = constraintWidget3.e.i;
                            dependencyNode3.k.add(dependencyNode0);
                            dependencyNode0.l.add(dependencyNode3);
                        }
                        ++v1;
                    }
                    this.m(this.b.e.h);
                    this.m(this.b.e.i);
                }
            }
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void e() {
        ConstraintWidget constraintWidget0 = this.b;
        if(constraintWidget0 instanceof Barrier) {
            DependencyNode dependencyNode0 = this.h;
            switch(((Barrier)constraintWidget0).s0) {
                case 0: 
                case 1: {
                    constraintWidget0.Z = dependencyNode0.g;
                    break;
                }
                default: {
                    constraintWidget0.a0 = dependencyNode0.g;
                }
            }
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void f() {
        this.c = null;
        this.h.c();
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final boolean k() {
        return false;
    }

    public final void m(DependencyNode dependencyNode0) {
        this.h.k.add(dependencyNode0);
        dependencyNode0.l.add(this.h);
    }
}


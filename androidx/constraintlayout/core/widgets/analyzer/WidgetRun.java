package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.ConstraintAnchor;
import androidx.constraintlayout.core.widgets.ConstraintWidget.DimensionBehaviour;
import androidx.constraintlayout.core.widgets.ConstraintWidget;

public abstract class WidgetRun implements Dependency {
    static enum RunType {
        NONE,
        START,  // 此枚举字段已被移除
        END,  // 此枚举字段已被移除
        CENTER;

    }

    public int a;
    public ConstraintWidget b;
    public RunGroup c;
    public DimensionBehaviour d;
    public final DimensionDependency e;
    public int f;
    public boolean g;
    public final DependencyNode h;
    public final DependencyNode i;
    public RunType j;

    public WidgetRun(ConstraintWidget constraintWidget0) {
        this.e = new DimensionDependency(this);
        this.f = 0;
        this.g = false;
        this.h = new DependencyNode(this);
        this.i = new DependencyNode(this);
        this.j = RunType.a;
        this.b = constraintWidget0;
    }

    public static void b(DependencyNode dependencyNode0, DependencyNode dependencyNode1, int v) {
        dependencyNode0.l.add(dependencyNode1);
        dependencyNode0.f = v;
        dependencyNode1.k.add(dependencyNode0);
    }

    public final void c(DependencyNode dependencyNode0, DependencyNode dependencyNode1, int v, DimensionDependency dimensionDependency0) {
        dependencyNode0.l.add(dependencyNode1);
        dependencyNode0.l.add(this.e);
        dependencyNode0.h = v;
        dependencyNode0.i = dimensionDependency0;
        dependencyNode1.k.add(dependencyNode0);
        dimensionDependency0.k.add(dependencyNode0);
    }

    public abstract void d();

    public abstract void e();

    public abstract void f();

    public final int g(int v, int v1) {
        int v3;
        if(v1 == 0) {
            int v2 = this.b.v;
            v3 = v2 <= 0 ? Math.max(this.b.u, v) : Math.min(v2, v);
            return v3 == v ? v : v3;
        }
        int v4 = this.b.y;
        v3 = v4 <= 0 ? Math.max(this.b.x, v) : Math.min(v4, v);
        return v3 == v ? v : v3;
    }

    public static DependencyNode h(ConstraintAnchor constraintAnchor0) {
        ConstraintAnchor constraintAnchor1 = constraintAnchor0.f;
        if(constraintAnchor1 == null) {
            return null;
        }
        ConstraintWidget constraintWidget0 = constraintAnchor1.d;
        switch(constraintAnchor1.e.ordinal()) {
            case 1: {
                return constraintWidget0.d.h;
            }
            case 2: {
                return constraintWidget0.e.h;
            }
            case 3: {
                return constraintWidget0.d.i;
            }
            case 4: {
                return constraintWidget0.e.i;
            }
            case 5: {
                return constraintWidget0.e.k;
            }
            default: {
                return null;
            }
        }
    }

    public static DependencyNode i(ConstraintAnchor constraintAnchor0, int v) {
        ConstraintAnchor constraintAnchor1 = constraintAnchor0.f;
        if(constraintAnchor1 == null) {
            return null;
        }
        ConstraintWidget constraintWidget0 = constraintAnchor1.d;
        HorizontalWidgetRun horizontalWidgetRun0 = v == 0 ? constraintWidget0.d : constraintWidget0.e;
        switch(constraintAnchor1.e.ordinal()) {
            case 1: 
            case 2: {
                return horizontalWidgetRun0.h;
            }
            case 3: 
            case 4: {
                return horizontalWidgetRun0.i;
            }
            default: {
                return null;
            }
        }
    }

    // 去混淆评级： 低(20)
    public long j() {
        return this.e.j ? ((long)this.e.g) : 0L;
    }

    public abstract boolean k();

    public final void l(ConstraintAnchor constraintAnchor0, ConstraintAnchor constraintAnchor1, int v) {
        DependencyNode dependencyNode0 = WidgetRun.h(constraintAnchor0);
        DependencyNode dependencyNode1 = WidgetRun.h(constraintAnchor1);
        if(dependencyNode0.j && dependencyNode1.j) {
            int v1 = constraintAnchor0.e() + dependencyNode0.g;
            int v2 = dependencyNode1.g - constraintAnchor1.e();
            int v3 = v2 - v1;
            DimensionDependency dimensionDependency0 = this.e;
            if(!dimensionDependency0.j) {
                DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.c;
                if(this.d == constraintWidget$DimensionBehaviour0) {
                    switch(this.a) {
                        case 0: {
                            dimensionDependency0.d(this.g(v3, v));
                            break;
                        }
                        case 1: {
                            dimensionDependency0.d(Math.min(this.g(dimensionDependency0.m, v), v3));
                            break;
                        }
                        case 2: {
                            ConstraintWidget constraintWidget0 = this.b;
                            ConstraintWidget constraintWidget1 = constraintWidget0.U;
                            if(constraintWidget1 != null) {
                                HorizontalWidgetRun horizontalWidgetRun0 = v == 0 ? constraintWidget1.d : constraintWidget1.e;
                                DimensionDependency dimensionDependency1 = horizontalWidgetRun0.e;
                                if(dimensionDependency1.j) {
                                    dimensionDependency0.d(this.g(((int)(((float)dimensionDependency1.g) * (v == 0 ? constraintWidget0.w : constraintWidget0.z) + 0.5f)), v));
                                }
                            }
                            break;
                        }
                        case 3: {
                            ConstraintWidget constraintWidget2 = this.b;
                            HorizontalWidgetRun horizontalWidgetRun1 = constraintWidget2.d;
                            if(horizontalWidgetRun1.d != constraintWidget$DimensionBehaviour0 || horizontalWidgetRun1.a != 3 || (constraintWidget2.e.d != constraintWidget$DimensionBehaviour0 || constraintWidget2.e.a != 3)) {
                                if(v == 0) {
                                    horizontalWidgetRun1 = constraintWidget2.e;
                                }
                                DimensionDependency dimensionDependency2 = horizontalWidgetRun1.e;
                                if(dimensionDependency2.j) {
                                    dimensionDependency0.d((v == 1 ? ((int)(((float)dimensionDependency2.g) / constraintWidget2.X + 0.5f)) : ((int)(constraintWidget2.X * ((float)dimensionDependency2.g) + 0.5f))));
                                }
                            }
                        }
                    }
                }
            }
            if(!dimensionDependency0.j) {
                return;
            }
            int v4 = dimensionDependency0.g;
            DependencyNode dependencyNode2 = this.i;
            DependencyNode dependencyNode3 = this.h;
            if(v4 == v3) {
                dependencyNode3.d(v1);
                dependencyNode2.d(v2);
                return;
            }
            float f = v == 0 ? this.b.e0 : this.b.f0;
            if(dependencyNode0 == dependencyNode1) {
                v1 = dependencyNode0.g;
                v2 = dependencyNode1.g;
                f = 0.5f;
            }
            dependencyNode3.d(((int)(((float)(v2 - v1 - v4)) * f + (((float)v1) + 0.5f))));
            dependencyNode2.d(dependencyNode3.g + dimensionDependency0.g);
        }
    }
}


package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.ConstraintAnchor.Type;
import androidx.constraintlayout.core.widgets.ConstraintAnchor;
import androidx.constraintlayout.core.widgets.ConstraintWidget.DimensionBehaviour;
import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.ConstraintWidgetContainer;
import androidx.constraintlayout.core.widgets.Guideline;
import java.util.HashSet;
import java.util.Iterator;

public abstract class Direct {
    public static final Measure a;

    static {
        Direct.a = new Measure();  // 初始化器: Ljava/lang/Object;-><init>()V
    }

    public static boolean a(ConstraintWidget constraintWidget0) {
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = constraintWidget0.T[0];
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidget0.T[1];
        ConstraintWidgetContainer constraintWidgetContainer0 = constraintWidget0.U == null ? null : ((ConstraintWidgetContainer)constraintWidget0.U);
        DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.a;
        if(constraintWidgetContainer0 != null) {
            DimensionBehaviour constraintWidget$DimensionBehaviour3 = constraintWidgetContainer0.T[0];
        }
        if(constraintWidgetContainer0 != null) {
            DimensionBehaviour constraintWidget$DimensionBehaviour4 = constraintWidgetContainer0.T[1];
        }
        boolean z = constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2 || constraintWidget0.B() || constraintWidget$DimensionBehaviour0 == DimensionBehaviour.b || constraintWidget$DimensionBehaviour0 == DimensionBehaviour.c && constraintWidget0.r == 0 && constraintWidget0.X == 0.0f && constraintWidget0.u(0) || constraintWidget$DimensionBehaviour0 == DimensionBehaviour.c && constraintWidget0.r == 1 && constraintWidget0.v(0, constraintWidget0.r());
        boolean z1 = constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2 || constraintWidget0.C() || constraintWidget$DimensionBehaviour1 == DimensionBehaviour.b || constraintWidget$DimensionBehaviour1 == DimensionBehaviour.c && constraintWidget0.s == 0 && constraintWidget0.X == 0.0f && constraintWidget0.u(1) || constraintWidget$DimensionBehaviour1 == DimensionBehaviour.c && constraintWidget0.s == 1 && constraintWidget0.v(1, constraintWidget0.l());
        return constraintWidget0.X <= 0.0f || !z && !z1 ? z && z1 : true;
    }

    public static void b(int v, ConstraintWidget constraintWidget0, Measurer basicMeasure$Measurer0, boolean z) {
        boolean z2;
        Iterator iterator1;
        if(constraintWidget0.m) {
            return;
        }
        if(!(constraintWidget0 instanceof ConstraintWidgetContainer) && constraintWidget0.A() && Direct.a(constraintWidget0)) {
            ConstraintWidgetContainer.V(constraintWidget0, basicMeasure$Measurer0, new Measure());  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        ConstraintAnchor constraintAnchor0 = constraintWidget0.j(Type.a);
        ConstraintAnchor constraintAnchor1 = constraintWidget0.j(Type.c);
        int v1 = constraintAnchor0.d();
        int v2 = constraintAnchor1.d();
        HashSet hashSet0 = constraintAnchor0.a;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.c;
        if(hashSet0 != null && constraintAnchor0.c) {
            for(Iterator iterator0 = hashSet0.iterator(); iterator0.hasNext(); iterator0 = iterator1) {
                Object object0 = iterator0.next();
                ConstraintAnchor constraintAnchor2 = (ConstraintAnchor)object0;
                ConstraintWidget constraintWidget1 = constraintAnchor2.d;
                boolean z1 = Direct.a(constraintWidget1);
                if(constraintWidget1.A() && z1) {
                    ConstraintWidgetContainer.V(constraintWidget1, basicMeasure$Measurer0, new Measure());  // 初始化器: Ljava/lang/Object;-><init>()V
                }
                ConstraintAnchor constraintAnchor3 = constraintWidget1.I;
                ConstraintAnchor constraintAnchor4 = constraintWidget1.K;
                if((constraintAnchor2 != constraintAnchor3 || (constraintAnchor4.f == null || !constraintAnchor4.f.c)) && (constraintAnchor2 != constraintAnchor4 || (constraintAnchor3.f == null || !constraintAnchor3.f.c))) {
                    iterator1 = iterator0;
                    z2 = false;
                }
                else {
                    iterator1 = iterator0;
                    z2 = true;
                }
                DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidget1.T[0];
                if(constraintWidget$DimensionBehaviour1 != constraintWidget$DimensionBehaviour0 || z1) {
                    if(!constraintWidget1.A()) {
                        if(constraintAnchor2 == constraintAnchor3 && constraintAnchor4.f == null) {
                            int v3 = constraintAnchor3.e();
                            constraintWidget1.K(v3 + v1, constraintWidget1.r() + (v3 + v1));
                            Direct.b(v + 1, constraintWidget1, basicMeasure$Measurer0, z);
                        }
                        else if(constraintAnchor2 == constraintAnchor4 && constraintAnchor3.f == null) {
                            int v4 = constraintAnchor4.e();
                            constraintWidget1.K(v1 - v4 - constraintWidget1.r(), v1 - v4);
                            Direct.b(v + 1, constraintWidget1, basicMeasure$Measurer0, z);
                        }
                        else if(z2 && !constraintWidget1.y()) {
                            Direct.c(v + 1, constraintWidget1, basicMeasure$Measurer0, z);
                        }
                    }
                }
                else if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour0 && constraintWidget1.v >= 0 && constraintWidget1.u >= 0 && (constraintWidget1.h0 == 8 || constraintWidget1.r == 0 && constraintWidget1.X == 0.0f) && !constraintWidget1.y() && !constraintWidget1.F && z2 && !constraintWidget1.y()) {
                    Direct.d(v + 1, constraintWidget0, basicMeasure$Measurer0, constraintWidget1, z);
                }
            }
        }
        if(constraintWidget0 instanceof Guideline) {
            return;
        }
        HashSet hashSet1 = constraintAnchor1.a;
        if(hashSet1 != null && constraintAnchor1.c) {
            for(Object object1: hashSet1) {
                ConstraintAnchor constraintAnchor5 = (ConstraintAnchor)object1;
                ConstraintWidget constraintWidget2 = constraintAnchor5.d;
                boolean z3 = Direct.a(constraintWidget2);
                if(constraintWidget2.A() && z3) {
                    ConstraintWidgetContainer.V(constraintWidget2, basicMeasure$Measurer0, new Measure());  // 初始化器: Ljava/lang/Object;-><init>()V
                }
                ConstraintAnchor constraintAnchor6 = constraintWidget2.I;
                ConstraintAnchor constraintAnchor7 = constraintWidget2.K;
                boolean z4 = constraintAnchor5 == constraintAnchor6 && (constraintAnchor7.f != null && constraintAnchor7.f.c) || constraintAnchor5 == constraintAnchor7 && (constraintAnchor6.f != null && constraintAnchor6.f.c);
                DimensionBehaviour constraintWidget$DimensionBehaviour2 = constraintWidget2.T[0];
                if(constraintWidget$DimensionBehaviour2 == constraintWidget$DimensionBehaviour0 && !z3) {
                    if(constraintWidget$DimensionBehaviour2 != constraintWidget$DimensionBehaviour0 || constraintWidget2.v < 0 || constraintWidget2.u < 0 || constraintWidget2.h0 != 8 && (constraintWidget2.r != 0 || constraintWidget2.X != 0.0f) || constraintWidget2.y() || constraintWidget2.F || !z4 || constraintWidget2.y()) {
                        continue;
                    }
                    Direct.d(v + 1, constraintWidget0, basicMeasure$Measurer0, constraintWidget2, z);
                }
                else if(constraintWidget2.A()) {
                }
                else if(constraintAnchor5 == constraintAnchor6 && constraintAnchor7.f == null) {
                    int v5 = constraintAnchor6.e();
                    constraintWidget2.K(v5 + v2, constraintWidget2.r() + (v5 + v2));
                    Direct.b(v + 1, constraintWidget2, basicMeasure$Measurer0, z);
                }
                else if(constraintAnchor5 == constraintAnchor7 && constraintAnchor6.f == null) {
                    int v6 = constraintAnchor7.e();
                    constraintWidget2.K(v2 - v6 - constraintWidget2.r(), v2 - v6);
                    Direct.b(v + 1, constraintWidget2, basicMeasure$Measurer0, z);
                }
                else if(z4 && !constraintWidget2.y()) {
                    Direct.c(v + 1, constraintWidget2, basicMeasure$Measurer0, z);
                }
            }
        }
        constraintWidget0.m = true;
    }

    public static void c(int v, ConstraintWidget constraintWidget0, Measurer basicMeasure$Measurer0, boolean z) {
        float f = constraintWidget0.e0;
        int v1 = constraintWidget0.I.f.d();
        int v2 = constraintWidget0.K.f.d();
        int v3 = constraintWidget0.I.e();
        int v4 = constraintWidget0.K.e();
        if(v1 == v2) {
            f = 0.5f;
        }
        else {
            v1 = v3 + v1;
            v2 -= v4;
        }
        int v5 = constraintWidget0.r();
        int v6 = v1 <= v2 ? v2 - v1 - v5 : v1 - v2 - v5;
        int v7 = ((int)(v6 <= 0 ? f * ((float)v6) : f * ((float)v6) + 0.5f)) + v1;
        constraintWidget0.K(v7, (v1 <= v2 ? v7 + v5 : v7 - v5));
        Direct.b(v + 1, constraintWidget0, basicMeasure$Measurer0, z);
    }

    public static void d(int v, ConstraintWidget constraintWidget0, Measurer basicMeasure$Measurer0, ConstraintWidget constraintWidget1, boolean z) {
        float f = constraintWidget1.e0;
        int v1 = constraintWidget1.I.f.d();
        int v2 = constraintWidget1.I.e() + v1;
        int v3 = constraintWidget1.K.f.d() - constraintWidget1.K.e();
        if(v3 >= v2) {
            int v4 = constraintWidget1.r();
            if(constraintWidget1.h0 != 8) {
                int v5 = constraintWidget1.r;
                if(v5 == 2) {
                    int v6 = constraintWidget0 instanceof ConstraintWidgetContainer ? constraintWidget0.r() : constraintWidget0.U.r();
                    v4 = (int)(constraintWidget1.e0 * 0.5f * ((float)v6));
                }
                else if(v5 == 0) {
                    v4 = v3 - v2;
                }
                v4 = Math.max(constraintWidget1.u, v4);
                int v7 = constraintWidget1.v;
                if(v7 > 0) {
                    v4 = Math.min(v7, v4);
                }
            }
            int v8 = v2 + ((int)(f * ((float)(v3 - v2 - v4)) + 0.5f));
            constraintWidget1.K(v8, v4 + v8);
            Direct.b(v + 1, constraintWidget1, basicMeasure$Measurer0, z);
        }
    }

    public static void e(int v, ConstraintWidget constraintWidget0, Measurer basicMeasure$Measurer0) {
        float f = constraintWidget0.f0;
        int v1 = constraintWidget0.J.f.d();
        int v2 = constraintWidget0.L.f.d();
        int v3 = constraintWidget0.J.e();
        int v4 = constraintWidget0.L.e();
        if(v1 == v2) {
            f = 0.5f;
        }
        else {
            v1 = v3 + v1;
            v2 -= v4;
        }
        int v5 = constraintWidget0.l();
        int v6 = v1 <= v2 ? v2 - v1 - v5 : v1 - v2 - v5;
        float f1 = v6 <= 0 ? f * ((float)v6) : f * ((float)v6) + 0.5f;
        int v7 = v1 + ((int)f1);
        int v8 = v7 + v5;
        if(v1 > v2) {
            v7 = v1 - ((int)f1);
            v8 = v7 - v5;
        }
        constraintWidget0.L(v7, v8);
        Direct.g(v + 1, constraintWidget0, basicMeasure$Measurer0);
    }

    public static void f(int v, ConstraintWidget constraintWidget0, Measurer basicMeasure$Measurer0, ConstraintWidget constraintWidget1) {
        float f = constraintWidget1.f0;
        int v1 = constraintWidget1.J.f.d();
        int v2 = constraintWidget1.J.e() + v1;
        int v3 = constraintWidget1.L.f.d() - constraintWidget1.L.e();
        if(v3 >= v2) {
            int v4 = constraintWidget1.l();
            if(constraintWidget1.h0 != 8) {
                int v5 = constraintWidget1.s;
                if(v5 == 2) {
                    v4 = (int)(f * 0.5f * ((float)(constraintWidget0 instanceof ConstraintWidgetContainer ? constraintWidget0.l() : constraintWidget0.U.l())));
                }
                else if(v5 == 0) {
                    v4 = v3 - v2;
                }
                v4 = Math.max(constraintWidget1.x, v4);
                int v6 = constraintWidget1.y;
                if(v6 > 0) {
                    v4 = Math.min(v6, v4);
                }
            }
            int v7 = v2 + ((int)(f * ((float)(v3 - v2 - v4)) + 0.5f));
            constraintWidget1.L(v7, v4 + v7);
            Direct.g(v + 1, constraintWidget1, basicMeasure$Measurer0);
        }
    }

    public static void g(int v, ConstraintWidget constraintWidget0, Measurer basicMeasure$Measurer0) {
        if(constraintWidget0.n) {
            return;
        }
        if(!(constraintWidget0 instanceof ConstraintWidgetContainer) && constraintWidget0.A() && Direct.a(constraintWidget0)) {
            ConstraintWidgetContainer.V(constraintWidget0, basicMeasure$Measurer0, new Measure());  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        ConstraintAnchor constraintAnchor0 = constraintWidget0.j(Type.b);
        ConstraintAnchor constraintAnchor1 = constraintWidget0.j(Type.d);
        int v1 = constraintAnchor0.d();
        int v2 = constraintAnchor1.d();
        HashSet hashSet0 = constraintAnchor0.a;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.c;
        if(hashSet0 != null && constraintAnchor0.c) {
            for(Object object0: hashSet0) {
                ConstraintAnchor constraintAnchor2 = (ConstraintAnchor)object0;
                ConstraintWidget constraintWidget1 = constraintAnchor2.d;
                boolean z = Direct.a(constraintWidget1);
                if(constraintWidget1.A() && z) {
                    ConstraintWidgetContainer.V(constraintWidget1, basicMeasure$Measurer0, new Measure());  // 初始化器: Ljava/lang/Object;-><init>()V
                }
                ConstraintAnchor constraintAnchor3 = constraintWidget1.J;
                ConstraintAnchor constraintAnchor4 = constraintWidget1.L;
                boolean z1 = constraintAnchor2 == constraintAnchor3 && (constraintAnchor4.f != null && constraintAnchor4.f.c) || constraintAnchor2 == constraintAnchor4 && (constraintAnchor3.f != null && constraintAnchor3.f.c);
                DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidget1.T[1];
                if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour0 && !z) {
                    if(constraintWidget$DimensionBehaviour1 != constraintWidget$DimensionBehaviour0 || constraintWidget1.y < 0 || constraintWidget1.x < 0 || constraintWidget1.h0 != 8 && (constraintWidget1.s != 0 || constraintWidget1.X != 0.0f) || constraintWidget1.z() || constraintWidget1.F || !z1 || constraintWidget1.z()) {
                        continue;
                    }
                    Direct.f(v + 1, constraintWidget0, basicMeasure$Measurer0, constraintWidget1);
                }
                else if(constraintWidget1.A()) {
                }
                else if(constraintAnchor2 == constraintAnchor3 && constraintAnchor4.f == null) {
                    int v3 = constraintAnchor3.e();
                    constraintWidget1.L(v3 + v1, constraintWidget1.l() + (v3 + v1));
                    Direct.g(v + 1, constraintWidget1, basicMeasure$Measurer0);
                }
                else if(constraintAnchor2 == constraintAnchor4 && constraintAnchor3.f == null) {
                    int v4 = constraintAnchor4.e();
                    constraintWidget1.L(v1 - v4 - constraintWidget1.l(), v1 - v4);
                    Direct.g(v + 1, constraintWidget1, basicMeasure$Measurer0);
                }
                else if(z1 && !constraintWidget1.z()) {
                    Direct.e(v + 1, constraintWidget1, basicMeasure$Measurer0);
                }
            }
        }
        if(constraintWidget0 instanceof Guideline) {
            return;
        }
        HashSet hashSet1 = constraintAnchor1.a;
        if(hashSet1 != null && constraintAnchor1.c) {
            for(Object object1: hashSet1) {
                ConstraintAnchor constraintAnchor5 = (ConstraintAnchor)object1;
                ConstraintWidget constraintWidget2 = constraintAnchor5.d;
                boolean z2 = Direct.a(constraintWidget2);
                if(constraintWidget2.A() && z2) {
                    ConstraintWidgetContainer.V(constraintWidget2, basicMeasure$Measurer0, new Measure());  // 初始化器: Ljava/lang/Object;-><init>()V
                }
                ConstraintAnchor constraintAnchor6 = constraintWidget2.J;
                ConstraintAnchor constraintAnchor7 = constraintWidget2.L;
                boolean z3 = constraintAnchor5 == constraintAnchor6 && (constraintAnchor7.f != null && constraintAnchor7.f.c) || constraintAnchor5 == constraintAnchor7 && (constraintAnchor6.f != null && constraintAnchor6.f.c);
                DimensionBehaviour constraintWidget$DimensionBehaviour2 = constraintWidget2.T[1];
                if(constraintWidget$DimensionBehaviour2 == constraintWidget$DimensionBehaviour0 && !z2) {
                    if(constraintWidget$DimensionBehaviour2 != constraintWidget$DimensionBehaviour0 || constraintWidget2.y < 0 || constraintWidget2.x < 0 || constraintWidget2.h0 != 8 && (constraintWidget2.s != 0 || constraintWidget2.X != 0.0f) || constraintWidget2.z() || constraintWidget2.F || !z3 || constraintWidget2.z()) {
                        continue;
                    }
                    Direct.f(v + 1, constraintWidget0, basicMeasure$Measurer0, constraintWidget2);
                }
                else if(constraintWidget2.A()) {
                }
                else if(constraintAnchor5 == constraintAnchor6 && constraintAnchor7.f == null) {
                    int v5 = constraintAnchor6.e();
                    constraintWidget2.L(v5 + v2, constraintWidget2.l() + (v5 + v2));
                    Direct.g(v + 1, constraintWidget2, basicMeasure$Measurer0);
                }
                else if(constraintAnchor5 == constraintAnchor7 && constraintAnchor6.f == null) {
                    int v6 = constraintAnchor7.e();
                    constraintWidget2.L(v2 - v6 - constraintWidget2.l(), v2 - v6);
                    Direct.g(v + 1, constraintWidget2, basicMeasure$Measurer0);
                }
                else if(z3 && !constraintWidget2.z()) {
                    Direct.e(v + 1, constraintWidget2, basicMeasure$Measurer0);
                }
            }
        }
        ConstraintAnchor constraintAnchor8 = constraintWidget0.j(Type.e);
        if(constraintAnchor8.a != null && constraintAnchor8.c) {
            int v7 = constraintAnchor8.d();
            for(Object object2: constraintAnchor8.a) {
                ConstraintAnchor constraintAnchor9 = (ConstraintAnchor)object2;
                ConstraintWidget constraintWidget3 = constraintAnchor9.d;
                boolean z4 = Direct.a(constraintWidget3);
                if(constraintWidget3.A() && z4) {
                    ConstraintWidgetContainer.V(constraintWidget3, basicMeasure$Measurer0, new Measure());  // 初始化器: Ljava/lang/Object;-><init>()V
                }
                if((constraintWidget3.T[1] != constraintWidget$DimensionBehaviour0 || z4) && !constraintWidget3.A()) {
                    ConstraintAnchor constraintAnchor10 = constraintWidget3.M;
                    if(constraintAnchor9 == constraintAnchor10) {
                        int v8 = constraintAnchor9.e() + v7;
                        if(constraintWidget3.E) {
                            int v9 = v8 - constraintWidget3.b0;
                            int v10 = constraintWidget3.W + v9;
                            constraintWidget3.a0 = v9;
                            constraintWidget3.J.l(v9);
                            constraintWidget3.L.l(v10);
                            constraintAnchor10.l(v8);
                            constraintWidget3.l = true;
                        }
                        Direct.g(v + 1, constraintWidget3, basicMeasure$Measurer0);
                    }
                }
            }
        }
        constraintWidget0.n = true;
    }
}


package androidx.constraintlayout.core.widgets.analyzer;

import androidx.constraintlayout.core.widgets.ConstraintWidget;
import androidx.constraintlayout.core.widgets.Guideline;

class GuidelineReference extends WidgetRun {
    @Override  // androidx.constraintlayout.core.widgets.analyzer.Dependency
    public final void a(Dependency dependency0) {
        DependencyNode dependencyNode0 = this.h;
        if(!dependencyNode0.c) {
            return;
        }
        if(dependencyNode0.j) {
            return;
        }
        dependencyNode0.d(((int)(((float)((DependencyNode)dependencyNode0.l.get(0)).g) * ((Guideline)this.b).q0 + 0.5f)));
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void d() {
        ConstraintWidget constraintWidget0 = this.b;
        int v = ((Guideline)constraintWidget0).r0;
        int v1 = ((Guideline)constraintWidget0).s0;
        DependencyNode dependencyNode0 = this.h;
        if(((Guideline)constraintWidget0).u0 == 1) {
            if(v != -1) {
                dependencyNode0.l.add(constraintWidget0.U.d.h);
                this.b.U.d.h.k.add(dependencyNode0);
                dependencyNode0.f = v;
            }
            else if(v1 == -1) {
                dependencyNode0.b = true;
                dependencyNode0.l.add(constraintWidget0.U.d.i);
                this.b.U.d.i.k.add(dependencyNode0);
            }
            else {
                dependencyNode0.l.add(constraintWidget0.U.d.i);
                this.b.U.d.i.k.add(dependencyNode0);
                dependencyNode0.f = -v1;
            }
            this.m(this.b.d.h);
            this.m(this.b.d.i);
            return;
        }
        if(v != -1) {
            dependencyNode0.l.add(constraintWidget0.U.e.h);
            this.b.U.e.h.k.add(dependencyNode0);
            dependencyNode0.f = v;
        }
        else if(v1 == -1) {
            dependencyNode0.b = true;
            dependencyNode0.l.add(constraintWidget0.U.e.i);
            this.b.U.e.i.k.add(dependencyNode0);
        }
        else {
            dependencyNode0.l.add(constraintWidget0.U.e.i);
            this.b.U.e.i.k.add(dependencyNode0);
            dependencyNode0.f = -v1;
        }
        this.m(this.b.e.h);
        this.m(this.b.e.i);
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void e() {
        ConstraintWidget constraintWidget0 = this.b;
        DependencyNode dependencyNode0 = this.h;
        if(((Guideline)constraintWidget0).u0 == 1) {
            constraintWidget0.Z = dependencyNode0.g;
            return;
        }
        constraintWidget0.a0 = dependencyNode0.g;
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final void f() {
        this.h.c();
    }

    @Override  // androidx.constraintlayout.core.widgets.analyzer.WidgetRun
    public final boolean k() {
        return false;
    }

    public final void m(DependencyNode dependencyNode0) {
        this.h.k.add(dependencyNode0);
        dependencyNode0.l.add(this.h);
    }
}


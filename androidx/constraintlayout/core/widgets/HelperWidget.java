package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.widgets.analyzer.Grouping;
import androidx.constraintlayout.core.widgets.analyzer.WidgetGroup;
import java.util.ArrayList;

public abstract class HelperWidget extends ConstraintWidget implements Helper {
    public ConstraintWidget[] q0;
    public int r0;

    public HelperWidget() {
        this.q0 = new ConstraintWidget[4];
        this.r0 = 0;
    }

    public final void S(int v, WidgetGroup widgetGroup0, ArrayList arrayList0) {
        for(int v2 = 0; v2 < this.r0; ++v2) {
            ConstraintWidget constraintWidget0 = this.q0[v2];
            ArrayList arrayList1 = widgetGroup0.a;
            if(!arrayList1.contains(constraintWidget0)) {
                arrayList1.add(constraintWidget0);
            }
        }
        for(int v1 = 0; v1 < this.r0; ++v1) {
            Grouping.a(this.q0[v1], v, arrayList0, widgetGroup0);
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.Helper
    public void a() {
    }
}


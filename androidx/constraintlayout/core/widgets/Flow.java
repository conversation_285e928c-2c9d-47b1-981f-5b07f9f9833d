package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.LinearSystem;
import androidx.constraintlayout.core.widgets.analyzer.BasicMeasure.Measure;
import androidx.constraintlayout.core.widgets.analyzer.BasicMeasure.Measurer;
import java.util.ArrayList;
import java.util.Arrays;

public class Flow extends VirtualLayout {
    class WidgetsList {
        public int a;
        public ConstraintWidget b;
        public int c;
        public ConstraintAnchor d;
        public ConstraintAnchor e;
        public ConstraintAnchor f;
        public ConstraintAnchor g;
        public int h;
        public int i;
        public int j;
        public int k;
        public int l;
        public int m;
        public int n;
        public int o;
        public int p;
        public int q;
        public final Flow r;

        public WidgetsList(int v, ConstraintAnchor constraintAnchor0, ConstraintAnchor constraintAnchor1, ConstraintAnchor constraintAnchor2, ConstraintAnchor constraintAnchor3, int v1) {
            this.b = null;
            this.c = 0;
            this.l = 0;
            this.m = 0;
            this.n = 0;
            this.o = 0;
            this.p = 0;
            this.a = v;
            this.d = constraintAnchor0;
            this.e = constraintAnchor1;
            this.f = constraintAnchor2;
            this.g = constraintAnchor3;
            this.h = flow0.w0;
            this.i = flow0.s0;
            this.j = flow0.x0;
            this.k = flow0.t0;
            this.q = v1;
        }

        public final void a(ConstraintWidget constraintWidget0) {
            DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.c;
            int v = 0;
            Flow flow0 = this.r;
            if(this.a == 0) {
                int v1 = flow0.W(this.q, constraintWidget0);
                if(constraintWidget0.T[0] == constraintWidget$DimensionBehaviour0) {
                    ++this.p;
                    v1 = 0;
                }
                int v2 = flow0.P0;
                if(constraintWidget0.h0 != 8) {
                    v = v2;
                }
                this.l = v1 + v + this.l;
                int v3 = flow0.V(this.q, constraintWidget0);
                if(this.b == null || this.c < v3) {
                    this.b = constraintWidget0;
                    this.c = v3;
                    this.m = v3;
                }
            }
            else {
                int v4 = flow0.W(this.q, constraintWidget0);
                int v5 = flow0.V(this.q, constraintWidget0);
                if(constraintWidget0.T[1] == constraintWidget$DimensionBehaviour0) {
                    ++this.p;
                    v5 = 0;
                }
                int v6 = flow0.Q0;
                if(constraintWidget0.h0 != 8) {
                    v = v6;
                }
                this.m = v5 + v + this.m;
                if(this.b == null || this.c < v4) {
                    this.b = constraintWidget0;
                    this.c = v4;
                    this.l = v4;
                }
            }
            ++this.o;
        }

        public final void b(int v, boolean z, boolean z1) {
            int v25;
            int v24;
            int v16;
            float f2;
            float f1;
            float f;
            int v14;
            ConstraintWidget constraintWidget4;
            Flow flow0;
            int v1 = this.o;
            for(int v2 = 0; true; ++v2) {
                flow0 = this.r;
                if(v2 >= v1) {
                    break;
                }
                int v3 = this.n + v2;
                if(v3 >= flow0.b1) {
                    break;
                }
                ConstraintWidget constraintWidget0 = flow0.a1[v3];
                if(constraintWidget0 != null) {
                    constraintWidget0.E();
                }
            }
            if(v1 != 0 && this.b != null) {
                boolean z2 = z1 && v == 0;
                int v5 = -1;
                int v6 = -1;
                for(int v4 = 0; v4 < v1; ++v4) {
                    int v7 = this.n + (z ? v1 - 1 - v4 : v4);
                    if(v7 >= flow0.b1) {
                        break;
                    }
                    ConstraintWidget constraintWidget1 = flow0.a1[v7];
                    if(constraintWidget1 != null && constraintWidget1.h0 == 0) {
                        if(v5 == -1) {
                            v5 = v4;
                        }
                        v6 = v4;
                    }
                }
                if(this.a == 0) {
                    ConstraintWidget constraintWidget2 = this.b;
                    constraintWidget2.k0 = flow0.E0;
                    ConstraintAnchor constraintAnchor0 = constraintWidget2.J;
                    constraintAnchor0.a(this.e, (v <= 0 ? this.i : this.i + flow0.Q0));
                    ConstraintAnchor constraintAnchor1 = constraintWidget2.L;
                    if(z1) {
                        constraintAnchor1.a(this.g, this.k);
                    }
                    if(v > 0) {
                        this.e.d.L.a(constraintAnchor0, 0);
                    }
                    if(flow0.S0 == 3 && !constraintWidget2.E) {
                        int v8 = 0;
                        while(v8 < v1) {
                            int v9 = this.n + (z ? v1 - 1 - v8 : v8);
                            if(v9 >= flow0.b1) {
                                break;
                            }
                            ConstraintWidget constraintWidget3 = flow0.a1[v9];
                            if(constraintWidget3.E) {
                                constraintWidget4 = constraintWidget3;
                                goto label_48;
                            }
                            ++v8;
                        }
                    }
                    constraintWidget4 = constraintWidget2;
                label_48:
                    ConstraintWidget constraintWidget5 = null;
                    int v10 = 0;
                    while(v10 < v1) {
                        int v11 = z ? v1 - 1 - v10 : v10;
                        int v12 = this.n + v11;
                        if(v12 >= flow0.b1) {
                            return;
                        }
                        ConstraintWidget constraintWidget6 = flow0.a1[v12];
                        if(constraintWidget6 != null) {
                            ConstraintAnchor constraintAnchor2 = constraintWidget6.I;
                            if(v10 == 0) {
                                constraintWidget6.g(constraintAnchor2, this.d, this.h);
                            }
                            if(v11 == 0) {
                                int v13 = flow0.D0;
                                if(z) {
                                    v14 = v13;
                                    f = 1.0f - flow0.J0;
                                }
                                else {
                                    v14 = v13;
                                    f = flow0.J0;
                                }
                                if(this.n == 0) {
                                    int v15 = flow0.F0;
                                    if(v15 == -1) {
                                        f1 = f;
                                        goto label_76;
                                    }
                                    else {
                                        f2 = z ? 1.0f - flow0.L0 : flow0.L0;
                                        v16 = v15;
                                        goto label_86;
                                    }
                                    goto label_75;
                                }
                                else {
                                label_75:
                                    f1 = f;
                                }
                            label_76:
                                if(z1) {
                                    v16 = flow0.H0;
                                    if(v16 != -1) {
                                        f2 = z ? 1.0f - flow0.N0 : flow0.N0;
                                    }
                                }
                                else {
                                    v16 = v14;
                                    f2 = f1;
                                }
                            label_86:
                                constraintWidget6.j0 = v16;
                                constraintWidget6.e0 = f2;
                            }
                            if(v10 == v1 - 1) {
                                constraintWidget6.g(constraintWidget6.K, this.f, this.j);
                            }
                            if(constraintWidget5 != null) {
                                ConstraintAnchor constraintAnchor3 = constraintWidget5.K;
                                constraintAnchor2.a(constraintAnchor3, flow0.P0);
                                if(v10 == v5) {
                                    int v17 = this.h;
                                    if(constraintAnchor2.h()) {
                                        constraintAnchor2.h = v17;
                                    }
                                }
                                constraintAnchor3.a(constraintAnchor2, 0);
                                if(v10 == v6 + 1) {
                                    int v18 = this.j;
                                    if(constraintAnchor3.h()) {
                                        constraintAnchor3.h = v18;
                                    }
                                }
                            }
                            if(constraintWidget6 != constraintWidget2) {
                                int v19 = flow0.S0;
                                if(v19 != 3 || !constraintWidget4.E || constraintWidget6 == constraintWidget4 || !constraintWidget6.E) {
                                    ConstraintAnchor constraintAnchor4 = constraintWidget6.J;
                                    if(v19 == 0) {
                                        constraintAnchor4.a(constraintAnchor0, 0);
                                    }
                                    else {
                                        ConstraintAnchor constraintAnchor5 = constraintWidget6.L;
                                        if(v19 == 1) {
                                            constraintAnchor5.a(constraintAnchor1, 0);
                                        }
                                        else if(z2) {
                                            constraintAnchor4.a(this.e, this.i);
                                            constraintAnchor5.a(this.g, this.k);
                                        }
                                        else {
                                            constraintAnchor4.a(constraintAnchor0, 0);
                                            constraintAnchor5.a(constraintAnchor1, 0);
                                        }
                                    }
                                }
                                else {
                                    constraintWidget6.M.a(constraintWidget4.M, 0);
                                }
                            }
                            constraintWidget5 = constraintWidget6;
                        }
                        ++v10;
                    }
                }
                else {
                    ConstraintWidget constraintWidget7 = this.b;
                    constraintWidget7.j0 = flow0.D0;
                    int v20 = v <= 0 ? this.h : this.h + flow0.P0;
                    ConstraintAnchor constraintAnchor6 = constraintWidget7.I;
                    ConstraintAnchor constraintAnchor7 = constraintWidget7.K;
                    if(z) {
                        constraintAnchor7.a(this.f, v20);
                        if(z1) {
                            constraintAnchor6.a(this.d, this.j);
                        }
                        if(v > 0) {
                            this.f.d.I.a(constraintAnchor7, 0);
                        }
                    }
                    else {
                        constraintAnchor6.a(this.d, v20);
                        if(z1) {
                            constraintAnchor7.a(this.f, this.j);
                        }
                        if(v > 0) {
                            this.d.d.K.a(constraintAnchor6, 0);
                        }
                    }
                    int v21 = 0;
                    ConstraintWidget constraintWidget8 = null;
                    while(v21 < v1) {
                        int v22 = this.n + v21;
                        if(v22 >= flow0.b1) {
                            break;
                        }
                        ConstraintWidget constraintWidget9 = flow0.a1[v22];
                        if(constraintWidget9 != null) {
                            ConstraintAnchor constraintAnchor8 = constraintWidget9.J;
                            if(v21 == 0) {
                                constraintWidget9.g(constraintAnchor8, this.e, this.i);
                                int v23 = flow0.E0;
                                float f3 = flow0.K0;
                                if(this.n == 0) {
                                    v24 = flow0.G0;
                                    if(v24 == -1) {
                                        v25 = v23;
                                        goto label_162;
                                    }
                                    else {
                                        f3 = flow0.M0;
                                        goto label_168;
                                    }
                                    goto label_161;
                                }
                                else {
                                label_161:
                                    v25 = v23;
                                }
                            label_162:
                                if(z1) {
                                    v24 = flow0.I0;
                                    if(v24 != -1) {
                                        f3 = flow0.O0;
                                    }
                                }
                                else {
                                    v24 = v25;
                                }
                            label_168:
                                constraintWidget9.k0 = v24;
                                constraintWidget9.f0 = f3;
                            }
                            if(v21 == v1 - 1) {
                                constraintWidget9.g(constraintWidget9.L, this.g, this.k);
                            }
                            if(constraintWidget8 != null) {
                                ConstraintAnchor constraintAnchor9 = constraintWidget8.L;
                                constraintAnchor8.a(constraintAnchor9, flow0.Q0);
                                if(v21 == v5) {
                                    int v26 = this.i;
                                    if(constraintAnchor8.h()) {
                                        constraintAnchor8.h = v26;
                                    }
                                }
                                constraintAnchor9.a(constraintAnchor8, 0);
                                if(v21 == v6 + 1) {
                                    int v27 = this.k;
                                    if(constraintAnchor9.h()) {
                                        constraintAnchor9.h = v27;
                                    }
                                }
                            }
                            if(constraintWidget9 != constraintWidget7) {
                                ConstraintAnchor constraintAnchor10 = constraintWidget9.K;
                                ConstraintAnchor constraintAnchor11 = constraintWidget9.I;
                                if(z) {
                                    int v28 = flow0.R0;
                                    if(v28 == 0) {
                                        constraintAnchor10.a(constraintAnchor7, 0);
                                    }
                                    else {
                                        switch(v28) {
                                            case 1: {
                                                constraintAnchor11.a(constraintAnchor6, 0);
                                                break;
                                            }
                                            case 2: {
                                                constraintAnchor11.a(constraintAnchor6, 0);
                                                constraintAnchor10.a(constraintAnchor7, 0);
                                            }
                                        }
                                    }
                                }
                                else {
                                    int v29 = flow0.R0;
                                    if(v29 == 0) {
                                        constraintAnchor11.a(constraintAnchor6, 0);
                                    }
                                    else {
                                        switch(v29) {
                                            case 1: {
                                                constraintAnchor10.a(constraintAnchor7, 0);
                                                break;
                                            }
                                            case 2: {
                                                if(z2) {
                                                    constraintAnchor11.a(this.d, this.h);
                                                    constraintAnchor10.a(this.f, this.j);
                                                }
                                                else {
                                                    constraintAnchor11.a(constraintAnchor6, 0);
                                                    constraintAnchor10.a(constraintAnchor7, 0);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            constraintWidget8 = constraintWidget9;
                        }
                        ++v21;
                    }
                }
            }
        }

        public final int c() {
            return this.a == 1 ? this.m - this.r.Q0 : this.m;
        }

        public final int d() {
            return this.a == 0 ? this.l - this.r.P0 : this.l;
        }

        public final void e(int v) {
            int v1 = this.p;
            if(v1 == 0) {
                return;
            }
            int v2 = this.o;
            int v3 = v / v1;
            for(int v4 = 0; v4 < v2; ++v4) {
                int v5 = this.n;
                Flow flow0 = this.r;
                if(v5 + v4 >= flow0.b1) {
                    break;
                }
                ConstraintWidget constraintWidget0 = flow0.a1[v5 + v4];
                DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.a;
                DimensionBehaviour constraintWidget$DimensionBehaviour1 = DimensionBehaviour.c;
                if(this.a != 0) {
                    if(constraintWidget0 != null) {
                        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour1 = constraintWidget0.T;
                        if(arr_constraintWidget$DimensionBehaviour1[1] == constraintWidget$DimensionBehaviour1 && constraintWidget0.s == 0) {
                            flow0.U(constraintWidget0, arr_constraintWidget$DimensionBehaviour1[0], constraintWidget0.r(), constraintWidget$DimensionBehaviour0, v3);
                        }
                    }
                }
                else if(constraintWidget0 != null) {
                    DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = constraintWidget0.T;
                    if(arr_constraintWidget$DimensionBehaviour[0] == constraintWidget$DimensionBehaviour1 && constraintWidget0.r == 0) {
                        flow0.U(constraintWidget0, constraintWidget$DimensionBehaviour0, v3, arr_constraintWidget$DimensionBehaviour[1], constraintWidget0.l());
                    }
                }
            }
            this.l = 0;
            this.m = 0;
            this.b = null;
            this.c = 0;
            int v6 = this.o;
            for(int v7 = 0; v7 < v6; ++v7) {
                int v8 = this.n + v7;
                Flow flow1 = this.r;
                if(v8 >= flow1.b1) {
                    break;
                }
                ConstraintWidget constraintWidget1 = flow1.a1[v8];
                if(this.a == 0) {
                    this.l = constraintWidget1.r() + (constraintWidget1.h0 == 8 ? 0 : flow1.P0) + this.l;
                    int v9 = flow1.V(this.q, constraintWidget1);
                    if(this.b == null || this.c < v9) {
                        this.b = constraintWidget1;
                        this.c = v9;
                        this.m = v9;
                    }
                }
                else {
                    int v10 = flow1.W(this.q, constraintWidget1);
                    this.m = flow1.V(this.q, constraintWidget1) + (constraintWidget1.h0 == 8 ? 0 : flow1.Q0) + this.m;
                    if(this.b == null || this.c < v10) {
                        this.b = constraintWidget1;
                        this.c = v10;
                        this.l = v10;
                    }
                }
            }
        }

        public final void f(int v, ConstraintAnchor constraintAnchor0, ConstraintAnchor constraintAnchor1, ConstraintAnchor constraintAnchor2, ConstraintAnchor constraintAnchor3, int v1, int v2, int v3, int v4, int v5) {
            this.a = v;
            this.d = constraintAnchor0;
            this.e = constraintAnchor1;
            this.f = constraintAnchor2;
            this.g = constraintAnchor3;
            this.h = v1;
            this.i = v2;
            this.j = v3;
            this.k = v4;
            this.q = v5;
        }
    }

    public int D0;
    public int E0;
    public int F0;
    public int G0;
    public int H0;
    public int I0;
    public float J0;
    public float K0;
    public float L0;
    public float M0;
    public float N0;
    public float O0;
    public int P0;
    public int Q0;
    public int R0;
    public int S0;
    public int T0;
    public int U0;
    public int V0;
    public ArrayList W0;
    public ConstraintWidget[] X0;
    public ConstraintWidget[] Y0;
    public int[] Z0;
    public ConstraintWidget[] a1;
    public int b1;

    @Override  // androidx.constraintlayout.core.widgets.VirtualLayout
    public final void T(int v, int v1, int v2, int v3) {
        int v91;
        DimensionBehaviour constraintWidget$DimensionBehaviour8;
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour2;
        int v70;
        int v69;
        ConstraintWidget constraintWidget13;
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour1;
        DimensionBehaviour constraintWidget$DimensionBehaviour7;
        int v65;
        boolean z2;
        int v49;
        int v47;
        int v28;
        DimensionBehaviour constraintWidget$DimensionBehaviour6;
        int v27;
        ConstraintWidget constraintWidget3;
        DimensionBehaviour constraintWidget$DimensionBehaviour5;
        int v23;
        int v21;
        int v20;
        int v19;
        int v18;
        WidgetsList flow$WidgetsList8;
        int v15;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.b;
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = DimensionBehaviour.c;
        if(this.r0 > 0) {
            Measurer basicMeasure$Measurer0 = this.U == null ? null : ((ConstraintWidgetContainer)this.U).u0;
            if(basicMeasure$Measurer0 == null) {
                this.z0 = 0;
                this.A0 = 0;
                this.y0 = false;
                return;
            }
            for(int v4 = 0; v4 < this.r0; ++v4) {
                ConstraintWidget constraintWidget0 = this.q0[v4];
                if(constraintWidget0 != null && !(constraintWidget0 instanceof Guideline)) {
                    DimensionBehaviour constraintWidget$DimensionBehaviour2 = constraintWidget0.k(0);
                    DimensionBehaviour constraintWidget$DimensionBehaviour3 = constraintWidget0.k(1);
                    if(constraintWidget$DimensionBehaviour2 != constraintWidget$DimensionBehaviour1 || constraintWidget0.r == 1 || constraintWidget$DimensionBehaviour3 != constraintWidget$DimensionBehaviour1 || constraintWidget0.s == 1) {
                        if(constraintWidget$DimensionBehaviour2 == constraintWidget$DimensionBehaviour1) {
                            constraintWidget$DimensionBehaviour2 = constraintWidget$DimensionBehaviour0;
                        }
                        if(constraintWidget$DimensionBehaviour3 == constraintWidget$DimensionBehaviour1) {
                            constraintWidget$DimensionBehaviour3 = constraintWidget$DimensionBehaviour0;
                        }
                        Measure basicMeasure$Measure0 = this.B0;
                        basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour2;
                        basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour3;
                        basicMeasure$Measure0.c = constraintWidget0.r();
                        basicMeasure$Measure0.d = constraintWidget0.l();
                        basicMeasure$Measurer0.a(constraintWidget0, basicMeasure$Measure0);
                        constraintWidget0.P(basicMeasure$Measure0.e);
                        constraintWidget0.M(basicMeasure$Measure0.f);
                        constraintWidget0.J(basicMeasure$Measure0.g);
                    }
                }
            }
        }
        int v5 = this.w0;
        int v6 = this.x0;
        int v7 = this.s0;
        int v8 = this.t0;
        int v9 = 0;
        int v10 = 0;
        int v11 = v1 - v5 - v6;
        int v12 = this.V0;
        if(v12 == 1) {
            v11 = v3 - v7 - v8;
        }
        if(v12 == 0) {
            if(this.D0 == -1) {
                this.D0 = 0;
            }
            if(this.E0 == -1) {
                this.E0 = 0;
            }
        }
        else {
            if(this.D0 == -1) {
                this.D0 = 0;
            }
            if(this.E0 == -1) {
                this.E0 = 0;
            }
        }
        ConstraintWidget[] arr_constraintWidget = this.q0;
        int v14 = 0;
        for(int v13 = 0; true; ++v13) {
            v15 = this.r0;
            if(v13 >= v15) {
                break;
            }
            if(this.q0[v13].h0 == 8) {
                ++v14;
            }
        }
        if(v14 > 0) {
            arr_constraintWidget = new ConstraintWidget[v15 - v14];
            v15 = 0;
            for(int v16 = 0; v16 < this.r0; ++v16) {
                ConstraintWidget constraintWidget1 = this.q0[v16];
                if(constraintWidget1.h0 != 8) {
                    arr_constraintWidget[v15] = constraintWidget1;
                    ++v15;
                }
            }
        }
        this.a1 = arr_constraintWidget;
        this.b1 = v15;
        int v17 = this.T0;
        ArrayList arrayList0 = this.W0;
        if(v17 == 0) {
            v18 = v7;
            v19 = v6;
            v20 = v5;
            v21 = v8;
            int v87 = this.V0;
            if(v15 != 0) {
                if(arrayList0.size() == 0) {
                    flow$WidgetsList8 = new WidgetsList(this, v87, this.I, this.J, this.K, this.L, v11);
                    arrayList0.add(flow$WidgetsList8);
                }
                else {
                    flow$WidgetsList8 = (WidgetsList)arrayList0.get(0);
                    flow$WidgetsList8.c = 0;
                    flow$WidgetsList8.b = null;
                    flow$WidgetsList8.l = 0;
                    flow$WidgetsList8.m = 0;
                    flow$WidgetsList8.n = 0;
                    flow$WidgetsList8.o = 0;
                    flow$WidgetsList8.p = 0;
                    flow$WidgetsList8.f(v87, this.I, this.J, this.K, this.L, this.w0, this.s0, this.x0, this.t0, v11);
                }
                for(int v88 = 0; v88 < v15; ++v88) {
                    flow$WidgetsList8.a(arr_constraintWidget[v88]);
                }
                v9 = flow$WidgetsList8.d();
                v10 = flow$WidgetsList8.c();
            }
        }
        else {
            ConstraintAnchor constraintAnchor0 = this.J;
            ConstraintAnchor constraintAnchor1 = this.I;
            ConstraintAnchor constraintAnchor2 = this.K;
            ConstraintAnchor constraintAnchor3 = this.L;
            DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = this.T;
            switch(v17) {
                case 1: {
                    ConstraintAnchor constraintAnchor4 = constraintAnchor1;
                    v18 = v7;
                    v19 = v6;
                    v20 = v5;
                    DimensionBehaviour constraintWidget$DimensionBehaviour4 = constraintWidget$DimensionBehaviour0;
                    v21 = v8;
                    int v22 = this.V0;
                    if(v15 != 0) {
                        arrayList0.clear();
                        WidgetsList flow$WidgetsList0 = new WidgetsList(this, v22, this.I, this.J, this.K, this.L, v11);
                        arrayList0.add(flow$WidgetsList0);
                        if(v22 == 0) {
                            v23 = 0;
                            int v24 = 0;
                            int v25 = 0;
                            while(v25 < v15) {
                                ConstraintWidget constraintWidget2 = arr_constraintWidget[v25];
                                int v26 = this.W(v11, constraintWidget2);
                                if(constraintWidget2.T[0] == constraintWidget$DimensionBehaviour1) {
                                    ++v23;
                                }
                                boolean z = (v24 == v11 || this.P0 + v24 + v26 > v11) && flow$WidgetsList0.b != null;
                                if((z || v25 <= 0 || (this.U0 <= 0 || v25 % this.U0 != 0)) && !z) {
                                    constraintWidget$DimensionBehaviour5 = constraintWidget$DimensionBehaviour4;
                                    constraintWidget3 = constraintWidget2;
                                    v27 = v25;
                                    if(v27 > 0) {
                                        v24 = this.P0 + v26 + v24;
                                        goto label_119;
                                    }
                                }
                                else {
                                    constraintWidget$DimensionBehaviour5 = constraintWidget$DimensionBehaviour4;
                                    constraintWidget3 = constraintWidget2;
                                    v27 = v25;
                                    flow$WidgetsList0 = new WidgetsList(this, 0, this.I, this.J, this.K, this.L, v11);
                                    flow$WidgetsList0.n = v27;
                                    arrayList0.add(flow$WidgetsList0);
                                }
                                v24 = v26;
                            label_119:
                                flow$WidgetsList0.a(constraintWidget3);
                                v25 = v27 + 1;
                                constraintWidget$DimensionBehaviour4 = constraintWidget$DimensionBehaviour5;
                            }
                            constraintWidget$DimensionBehaviour6 = constraintWidget$DimensionBehaviour4;
                            v28 = 0;
                        }
                        else {
                            constraintWidget$DimensionBehaviour6 = constraintWidget$DimensionBehaviour4;
                            v28 = v22;
                            v23 = 0;
                            int v29 = 0;
                            int v30 = 0;
                            while(v30 < v15) {
                                ConstraintWidget constraintWidget4 = arr_constraintWidget[v30];
                                int v31 = this.V(v11, constraintWidget4);
                                if(constraintWidget4.T[1] == constraintWidget$DimensionBehaviour1) {
                                    ++v23;
                                }
                                boolean z1 = (v29 == v11 || this.Q0 + v29 + v31 > v11) && flow$WidgetsList0.b != null;
                                if(!z1 && v30 > 0 && (this.U0 > 0 && v30 % this.U0 == 0) || z1) {
                                    flow$WidgetsList0 = new WidgetsList(this, v28, this.I, this.J, this.K, this.L, v11);
                                    flow$WidgetsList0.n = v30;
                                    arrayList0.add(flow$WidgetsList0);
                                }
                                else if(v30 > 0) {
                                    v29 = this.Q0 + v31 + v29;
                                    goto label_146;
                                }
                                v29 = v31;
                            label_146:
                                flow$WidgetsList0.a(constraintWidget4);
                                ++v30;
                            }
                        }
                        int v32 = arrayList0.size();
                        int v33 = this.w0;
                        int v34 = this.s0;
                        int v35 = this.x0;
                        int v36 = this.t0;
                        if(v23 > 0 && (arr_constraintWidget$DimensionBehaviour[0] == constraintWidget$DimensionBehaviour6 || arr_constraintWidget$DimensionBehaviour[1] == constraintWidget$DimensionBehaviour6)) {
                            for(int v37 = 0; v37 < v32; ++v37) {
                                WidgetsList flow$WidgetsList1 = (WidgetsList)arrayList0.get(v37);
                                if(v28 == 0) {
                                    flow$WidgetsList1.e(v11 - flow$WidgetsList1.d());
                                }
                                else {
                                    flow$WidgetsList1.e(v11 - flow$WidgetsList1.c());
                                }
                            }
                        }
                        ConstraintAnchor constraintAnchor5 = constraintAnchor0;
                        ConstraintAnchor constraintAnchor6 = constraintAnchor2;
                        ConstraintAnchor constraintAnchor7 = constraintAnchor3;
                        int v39 = 0;
                        int v40 = 0;
                        for(int v38 = 0; v38 < v32; ++v38) {
                            WidgetsList flow$WidgetsList2 = (WidgetsList)arrayList0.get(v38);
                            if(v28 == 0) {
                                if(v38 < v32 - 1) {
                                    constraintAnchor7 = ((WidgetsList)arrayList0.get(v38 + 1)).b.J;
                                    v36 = 0;
                                }
                                else {
                                    v36 = this.t0;
                                    constraintAnchor7 = constraintAnchor3;
                                }
                                ConstraintAnchor constraintAnchor8 = flow$WidgetsList2.b.L;
                                flow$WidgetsList2.f(0, constraintAnchor4, constraintAnchor5, constraintAnchor6, constraintAnchor7, v33, v34, v35, v36, v11);
                                int v41 = Math.max(v39, flow$WidgetsList2.d());
                                v40 += flow$WidgetsList2.c();
                                if(v38 > 0) {
                                    v40 += this.Q0;
                                }
                                v39 = v41;
                                constraintAnchor5 = constraintAnchor8;
                                v34 = 0;
                            }
                            else {
                                if(v38 < v32 - 1) {
                                    constraintAnchor6 = ((WidgetsList)arrayList0.get(v38 + 1)).b.I;
                                    v35 = 0;
                                }
                                else {
                                    v35 = this.x0;
                                    constraintAnchor6 = constraintAnchor2;
                                }
                                ConstraintAnchor constraintAnchor9 = flow$WidgetsList2.b.K;
                                flow$WidgetsList2.f(v28, constraintAnchor4, constraintAnchor5, constraintAnchor6, constraintAnchor7, v33, v34, v35, v36, v11);
                                v39 += flow$WidgetsList2.d();
                                int v42 = Math.max(v40, flow$WidgetsList2.c());
                                if(v38 > 0) {
                                    v39 += this.P0;
                                }
                                v40 = v42;
                                constraintAnchor4 = constraintAnchor9;
                                v33 = 0;
                            }
                        }
                        v9 = v39;
                        v10 = v40;
                    }
                    break;
                }
                case 2: {
                    v18 = v7;
                    v19 = v6;
                    v20 = v5;
                    v21 = v8;
                    int v43 = this.V0;
                    if(v43 == 0) {
                        int v44 = this.U0;
                        if(v44 <= 0) {
                            int v46 = 0;
                            v47 = 0;
                            for(int v45 = 0; v45 < v15; ++v45) {
                                if(v45 > 0) {
                                    v46 += this.P0;
                                }
                                ConstraintWidget constraintWidget5 = arr_constraintWidget[v45];
                                if(constraintWidget5 != null) {
                                    int v48 = this.W(v11, constraintWidget5) + v46;
                                    if(v48 > v11) {
                                        break;
                                    }
                                    ++v47;
                                    v46 = v48;
                                }
                            }
                        }
                        else {
                            v47 = v44;
                        }
                        v49 = 0;
                    }
                    else {
                        v49 = this.U0;
                        if(v49 <= 0) {
                            int v51 = 0;
                            int v52 = 0;
                            for(int v50 = 0; v50 < v15; ++v50) {
                                if(v50 > 0) {
                                    v51 += this.Q0;
                                }
                                ConstraintWidget constraintWidget6 = arr_constraintWidget[v50];
                                if(constraintWidget6 != null) {
                                    int v53 = this.V(v11, constraintWidget6) + v51;
                                    if(v53 > v11) {
                                        break;
                                    }
                                    ++v52;
                                    v51 = v53;
                                }
                            }
                            v49 = v52;
                        }
                        v47 = 0;
                    }
                    if(this.Z0 == null) {
                        this.Z0 = new int[2];
                    }
                    if((v49 != 0 || v43 != 1) && (v47 != 0 || v43 != 0)) {
                        z2 = false;
                        goto label_259;
                    }
                alab1:
                    while(true) {
                        while(true) {
                        label_258:
                            z2 = true;
                        label_259:
                            if(z2) {
                                break alab1;
                            }
                            if(v43 == 0) {
                                v49 = (int)Math.ceil(((float)v15) / ((float)v47));
                            }
                            else {
                                v47 = (int)Math.ceil(((float)v15) / ((float)v49));
                            }
                            ConstraintWidget[] arr_constraintWidget1 = this.Y0;
                            if(arr_constraintWidget1 == null || arr_constraintWidget1.length < v47) {
                                this.Y0 = new ConstraintWidget[v47];
                            }
                            else {
                                Arrays.fill(arr_constraintWidget1, null);
                            }
                            ConstraintWidget[] arr_constraintWidget2 = this.X0;
                            if(arr_constraintWidget2 == null || arr_constraintWidget2.length < v49) {
                                this.X0 = new ConstraintWidget[v49];
                            }
                            else {
                                Arrays.fill(arr_constraintWidget2, null);
                            }
                            for(int v54 = 0; v54 < v47; ++v54) {
                                for(int v55 = 0; v55 < v49; ++v55) {
                                    int v56 = v43 == 1 ? v54 * v49 + v55 : v55 * v47 + v54;
                                    if(v56 < arr_constraintWidget.length) {
                                        ConstraintWidget constraintWidget7 = arr_constraintWidget[v56];
                                        if(constraintWidget7 != null) {
                                            int v57 = this.W(v11, constraintWidget7);
                                            ConstraintWidget constraintWidget8 = this.Y0[v54];
                                            if(constraintWidget8 == null || constraintWidget8.r() < v57) {
                                                this.Y0[v54] = constraintWidget7;
                                            }
                                            int v58 = this.V(v11, constraintWidget7);
                                            ConstraintWidget constraintWidget9 = this.X0[v55];
                                            if(constraintWidget9 == null || constraintWidget9.l() < v58) {
                                                this.X0[v55] = constraintWidget7;
                                            }
                                        }
                                    }
                                }
                            }
                            int v60 = 0;
                            for(int v59 = 0; v59 < v47; ++v59) {
                                ConstraintWidget constraintWidget10 = this.Y0[v59];
                                if(constraintWidget10 != null) {
                                    if(v59 > 0) {
                                        v60 += this.P0;
                                    }
                                    v60 = this.W(v11, constraintWidget10) + v60;
                                }
                            }
                            int v62 = 0;
                            for(int v61 = 0; v61 < v49; ++v61) {
                                ConstraintWidget constraintWidget11 = this.X0[v61];
                                if(constraintWidget11 != null) {
                                    if(v61 > 0) {
                                        v62 += this.Q0;
                                    }
                                    v62 = this.V(v11, constraintWidget11) + v62;
                                }
                            }
                            v9 = v60;
                            v10 = v62;
                            if(v43 == 0) {
                                if(v60 <= v11 || v47 <= 1) {
                                    continue;
                                }
                                break;
                            }
                            else {
                                goto label_320;
                            }
                            break alab1;
                        }
                        --v47;
                        goto label_259;
                    label_320:
                        if(v62 <= v11 || v49 <= 1) {
                            goto label_258;
                        }
                        --v49;
                        goto label_259;
                    }
                    int[] arr_v = this.Z0;
                    arr_v[0] = v47;
                    arr_v[1] = v49;
                    break;
                }
                case 3: {
                    int v63 = this.V0;
                    if(v15 == 0) {
                    label_466:
                        v18 = v7;
                        v19 = v6;
                        v20 = v5;
                        v21 = v8;
                    }
                    else {
                        arrayList0.clear();
                        ConstraintAnchor constraintAnchor10 = constraintAnchor1;
                        v21 = v8;
                        v18 = v7;
                        v19 = v6;
                        v20 = v5;
                        WidgetsList flow$WidgetsList3 = new WidgetsList(this, v63, this.I, this.J, this.K, this.L, v11);
                        arrayList0.add(flow$WidgetsList3);
                        if(v63 == 0) {
                            int v64 = 0;
                            v65 = 0;
                            int v66 = 0;
                            int v67 = 0;
                            while(v67 < v15) {
                                ++v64;
                                ConstraintWidget constraintWidget12 = arr_constraintWidget[v67];
                                int v68 = this.W(v11, constraintWidget12);
                                if(constraintWidget12.T[0] == constraintWidget$DimensionBehaviour1) {
                                    ++v65;
                                }
                                boolean z3 = (v66 == v11 || this.P0 + v66 + v68 > v11) && flow$WidgetsList3.b != null;
                                if((z3 || v67 <= 0 || (this.U0 <= 0 || v64 <= this.U0)) && !z3) {
                                    arr_constraintWidget$DimensionBehaviour1 = arr_constraintWidget$DimensionBehaviour;
                                    constraintWidget$DimensionBehaviour7 = constraintWidget$DimensionBehaviour0;
                                    constraintWidget13 = constraintWidget12;
                                    v69 = v67;
                                    v66 = v69 <= 0 ? v68 : this.P0 + v68 + v66;
                                }
                                else {
                                    constraintWidget$DimensionBehaviour7 = constraintWidget$DimensionBehaviour0;
                                    arr_constraintWidget$DimensionBehaviour1 = arr_constraintWidget$DimensionBehaviour;
                                    constraintWidget13 = constraintWidget12;
                                    v69 = v67;
                                    WidgetsList flow$WidgetsList4 = new WidgetsList(this, 0, this.I, this.J, this.K, this.L, v11);
                                    flow$WidgetsList4.n = v69;
                                    arrayList0.add(flow$WidgetsList4);
                                    flow$WidgetsList3 = flow$WidgetsList4;
                                    v66 = v68;
                                    v64 = 1;
                                }
                                flow$WidgetsList3.a(constraintWidget13);
                                v67 = v69 + 1;
                                arr_constraintWidget$DimensionBehaviour = arr_constraintWidget$DimensionBehaviour1;
                                constraintWidget$DimensionBehaviour0 = constraintWidget$DimensionBehaviour7;
                            }
                            v70 = 0;
                            arr_constraintWidget$DimensionBehaviour2 = arr_constraintWidget$DimensionBehaviour;
                            constraintWidget$DimensionBehaviour8 = constraintWidget$DimensionBehaviour0;
                        }
                        else {
                            v70 = v63;
                            arr_constraintWidget$DimensionBehaviour2 = arr_constraintWidget$DimensionBehaviour;
                            constraintWidget$DimensionBehaviour8 = constraintWidget$DimensionBehaviour0;
                            int v71 = 0;
                            v65 = 0;
                            int v72 = 0;
                            for(int v73 = 0; v73 < v15; ++v73) {
                                ConstraintWidget constraintWidget14 = arr_constraintWidget[v73];
                                int v74 = this.V(v11, constraintWidget14);
                                if(constraintWidget14.T[1] == constraintWidget$DimensionBehaviour1) {
                                    ++v65;
                                }
                                boolean z4 = (v72 == v11 || this.Q0 + v72 + v74 > v11) && flow$WidgetsList3.b != null;
                                if(!z4 && v73 > 0 && (this.U0 > 0 && v71 + 1 > this.U0) || z4) {
                                    WidgetsList flow$WidgetsList5 = new WidgetsList(this, v70, this.I, this.J, this.K, this.L, v11);
                                    flow$WidgetsList5.n = v73;
                                    arrayList0.add(flow$WidgetsList5);
                                    flow$WidgetsList3 = flow$WidgetsList5;
                                    v72 = v74;
                                    v71 = 1;
                                }
                                else if(v73 > 0) {
                                    v72 = this.Q0 + v74 + v72;
                                    ++v71;
                                }
                                else {
                                    ++v71;
                                    v72 = v74;
                                }
                                flow$WidgetsList3.a(constraintWidget14);
                            }
                        }
                        int v75 = arrayList0.size();
                        int v76 = this.w0;
                        int v77 = this.s0;
                        int v78 = this.x0;
                        int v79 = this.t0;
                        if(v65 > 0 && (arr_constraintWidget$DimensionBehaviour2[0] == constraintWidget$DimensionBehaviour8 || arr_constraintWidget$DimensionBehaviour2[1] == constraintWidget$DimensionBehaviour8)) {
                            for(int v80 = 0; v80 < v75; ++v80) {
                                WidgetsList flow$WidgetsList6 = (WidgetsList)arrayList0.get(v80);
                                if(v70 == 0) {
                                    flow$WidgetsList6.e(v11 - flow$WidgetsList6.d());
                                }
                                else {
                                    flow$WidgetsList6.e(v11 - flow$WidgetsList6.c());
                                }
                            }
                        }
                        int v81 = v76;
                        ConstraintAnchor constraintAnchor11 = constraintAnchor0;
                        ConstraintAnchor constraintAnchor12 = constraintAnchor2;
                        ConstraintAnchor constraintAnchor13 = constraintAnchor3;
                        int v83 = 0;
                        int v84 = 0;
                        for(int v82 = 0; v82 < v75; ++v82) {
                            WidgetsList flow$WidgetsList7 = (WidgetsList)arrayList0.get(v82);
                            if(v70 == 0) {
                                if(v82 < v75 - 1) {
                                    constraintAnchor13 = ((WidgetsList)arrayList0.get(v82 + 1)).b.J;
                                    v79 = 0;
                                }
                                else {
                                    v79 = this.t0;
                                    constraintAnchor13 = constraintAnchor3;
                                }
                                ConstraintAnchor constraintAnchor14 = flow$WidgetsList7.b.L;
                                flow$WidgetsList7.f(0, constraintAnchor10, constraintAnchor11, constraintAnchor12, constraintAnchor13, v81, v77, v78, v79, v11);
                                int v85 = Math.max(v83, flow$WidgetsList7.d());
                                v84 += flow$WidgetsList7.c();
                                if(v82 > 0) {
                                    v84 += this.Q0;
                                }
                                v83 = v85;
                                constraintAnchor11 = constraintAnchor14;
                                v77 = 0;
                            }
                            else {
                                if(v82 < v75 - 1) {
                                    constraintAnchor12 = ((WidgetsList)arrayList0.get(v82 + 1)).b.I;
                                    v78 = 0;
                                }
                                else {
                                    v78 = this.x0;
                                    constraintAnchor12 = constraintAnchor2;
                                }
                                ConstraintAnchor constraintAnchor15 = flow$WidgetsList7.b.K;
                                flow$WidgetsList7.f(v70, constraintAnchor10, constraintAnchor11, constraintAnchor12, constraintAnchor13, v81, v77, v78, v79, v11);
                                v83 += flow$WidgetsList7.d();
                                int v86 = Math.max(v84, flow$WidgetsList7.c());
                                if(v82 > 0) {
                                    v83 += this.P0;
                                }
                                v84 = v86;
                                constraintAnchor10 = constraintAnchor15;
                                v81 = 0;
                            }
                        }
                        v9 = v83;
                        v10 = v84;
                    }
                    break;
                }
                default: {
                    goto label_466;
                }
            }
        }
        int v89 = v9 + v20 + v19;
        int v90 = v10 + v18 + v21;
        if(v == 0x40000000) {
            v89 = v1;
        }
        else if(v == 0x80000000) {
            v89 = Math.min(v89, v1);
        }
        else if(v != 0) {
            v89 = 0;
        }
        if(v2 == 0x40000000) {
            v91 = v3;
        }
        else if(v2 == 0x80000000) {
            v91 = Math.min(v90, v3);
        }
        else {
            v91 = v2 == 0 ? v90 : 0;
        }
        this.z0 = v89;
        this.A0 = v91;
        this.P(v89);
        this.M(v91);
        this.y0 = this.r0 > 0;
    }

    public final int V(int v, ConstraintWidget constraintWidget0) {
        if(constraintWidget0 == null) {
            return 0;
        }
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = constraintWidget0.T;
        if(arr_constraintWidget$DimensionBehaviour[1] == DimensionBehaviour.c) {
            int v1 = constraintWidget0.s;
            if(v1 == 0) {
                return 0;
            }
            switch(v1) {
                case 1: {
                    return constraintWidget0.l();
                }
                case 2: {
                    int v2 = (int)(constraintWidget0.z * ((float)v));
                    if(v2 != constraintWidget0.l()) {
                        constraintWidget0.g = true;
                        this.U(constraintWidget0, arr_constraintWidget$DimensionBehaviour[0], constraintWidget0.r(), DimensionBehaviour.a, v2);
                    }
                    return v2;
                }
                case 3: {
                    return (int)(((float)constraintWidget0.r()) * constraintWidget0.X + 0.5f);
                }
                default: {
                    return constraintWidget0.l();
                }
            }
        }
        return constraintWidget0.l();
    }

    public final int W(int v, ConstraintWidget constraintWidget0) {
        if(constraintWidget0 == null) {
            return 0;
        }
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = constraintWidget0.T;
        if(arr_constraintWidget$DimensionBehaviour[0] == DimensionBehaviour.c) {
            int v1 = constraintWidget0.r;
            if(v1 == 0) {
                return 0;
            }
            switch(v1) {
                case 1: {
                    return constraintWidget0.r();
                }
                case 2: {
                    int v2 = (int)(constraintWidget0.w * ((float)v));
                    if(v2 != constraintWidget0.r()) {
                        constraintWidget0.g = true;
                        DimensionBehaviour constraintWidget$DimensionBehaviour0 = arr_constraintWidget$DimensionBehaviour[1];
                        int v3 = constraintWidget0.l();
                        this.U(constraintWidget0, DimensionBehaviour.a, v2, constraintWidget$DimensionBehaviour0, v3);
                    }
                    return v2;
                }
                case 3: {
                    return (int)(((float)constraintWidget0.l()) * constraintWidget0.X + 0.5f);
                }
                default: {
                    return constraintWidget0.r();
                }
            }
        }
        return constraintWidget0.r();
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final void c(LinearSystem linearSystem0, boolean z) {
        float f1;
        int v6;
        super.c(linearSystem0, z);
        boolean z1 = this.U != null && ((ConstraintWidgetContainer)this.U).v0;
        ArrayList arrayList0 = this.W0;
        switch(this.T0) {
            case 0: {
                if(arrayList0.size() > 0) {
                    ((WidgetsList)arrayList0.get(0)).b(0, z1, true);
                }
                break;
            }
            case 1: {
                int v = arrayList0.size();
                for(int v1 = 0; v1 < v; ++v1) {
                    ((WidgetsList)arrayList0.get(v1)).b(v1, z1, v1 == v - 1);
                }
                break;
            }
            case 2: {
                if(this.Z0 != null && this.Y0 != null && this.X0 != null) {
                    for(int v2 = 0; v2 < this.b1; ++v2) {
                        this.a1[v2].E();
                    }
                    int[] arr_v = this.Z0;
                    int v3 = arr_v[0];
                    int v4 = arr_v[1];
                    float f = this.J0;
                    ConstraintWidget constraintWidget0 = null;
                    int v5 = 0;
                    while(v5 < v3) {
                        if(z1) {
                            v6 = v3 - v5 - 1;
                            f1 = 1.0f - this.J0;
                        }
                        else {
                            f1 = f;
                            v6 = v5;
                        }
                        ConstraintWidget constraintWidget1 = this.Y0[v6];
                        if(constraintWidget1 != null && constraintWidget1.h0 != 8) {
                            ConstraintAnchor constraintAnchor0 = constraintWidget1.I;
                            if(v5 == 0) {
                                constraintWidget1.g(constraintAnchor0, this.I, this.w0);
                                constraintWidget1.j0 = this.D0;
                                constraintWidget1.e0 = f1;
                            }
                            if(v5 == v3 - 1) {
                                constraintWidget1.g(constraintWidget1.K, this.K, this.x0);
                            }
                            if(v5 > 0 && constraintWidget0 != null) {
                                constraintWidget1.g(constraintAnchor0, constraintWidget0.K, this.P0);
                                constraintWidget0.g(constraintWidget0.K, constraintAnchor0, 0);
                            }
                            constraintWidget0 = constraintWidget1;
                        }
                        ++v5;
                        f = f1;
                    }
                    for(int v7 = 0; v7 < v4; ++v7) {
                        ConstraintWidget constraintWidget2 = this.X0[v7];
                        if(constraintWidget2 != null && constraintWidget2.h0 != 8) {
                            ConstraintAnchor constraintAnchor1 = constraintWidget2.J;
                            if(v7 == 0) {
                                constraintWidget2.g(constraintAnchor1, this.J, this.s0);
                                constraintWidget2.k0 = this.E0;
                                constraintWidget2.f0 = this.K0;
                            }
                            if(v7 == v4 - 1) {
                                constraintWidget2.g(constraintWidget2.L, this.L, this.t0);
                            }
                            if(v7 > 0 && constraintWidget0 != null) {
                                constraintWidget2.g(constraintAnchor1, constraintWidget0.L, this.Q0);
                                constraintWidget0.g(constraintWidget0.L, constraintAnchor1, 0);
                            }
                            constraintWidget0 = constraintWidget2;
                        }
                    }
                    for(int v8 = 0; v8 < v3; ++v8) {
                        for(int v9 = 0; v9 < v4; ++v9) {
                            int v10 = this.V0 == 1 ? v8 * v4 + v9 : v9 * v3 + v8;
                            ConstraintWidget[] arr_constraintWidget = this.a1;
                            if(v10 < arr_constraintWidget.length) {
                                ConstraintWidget constraintWidget3 = arr_constraintWidget[v10];
                                if(constraintWidget3 != null && constraintWidget3.h0 != 8) {
                                    ConstraintWidget constraintWidget4 = this.Y0[v8];
                                    ConstraintWidget constraintWidget5 = this.X0[v9];
                                    if(constraintWidget3 != constraintWidget4) {
                                        constraintWidget3.g(constraintWidget3.I, constraintWidget4.I, 0);
                                        constraintWidget3.g(constraintWidget3.K, constraintWidget4.K, 0);
                                    }
                                    if(constraintWidget3 != constraintWidget5) {
                                        constraintWidget3.g(constraintWidget3.J, constraintWidget5.J, 0);
                                        constraintWidget3.g(constraintWidget3.L, constraintWidget5.L, 0);
                                    }
                                }
                            }
                        }
                    }
                }
                break;
            }
            case 3: {
                int v11 = arrayList0.size();
                for(int v12 = 0; v12 < v11; ++v12) {
                    ((WidgetsList)arrayList0.get(v12)).b(v12, z1, v12 == v11 - 1);
                }
            }
        }
        this.y0 = false;
    }
}


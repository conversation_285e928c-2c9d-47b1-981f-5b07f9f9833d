package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.ArrayRow;
import androidx.constraintlayout.core.LinearSystem;
import androidx.constraintlayout.core.SolverVariable;
import java.util.ArrayList;

public class Guideline extends ConstraintWidget {
    public float q0;
    public int r0;
    public int s0;
    public ConstraintAnchor t0;
    public int u0;
    public boolean v0;

    public Guideline() {
        this.q0 = -1.0f;
        this.r0 = -1;
        this.s0 = -1;
        this.t0 = this.J;
        this.u0 = 0;
        this.R.clear();
        this.R.add(this.t0);
        for(int v = 0; v < this.Q.length; ++v) {
            this.Q[v] = this.t0;
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final boolean B() {
        return this.v0;
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final boolean C() {
        return this.v0;
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final void R(LinearSystem linearSystem0, boolean z) {
        if(this.U == null) {
            return;
        }
        ConstraintAnchor constraintAnchor0 = this.t0;
        linearSystem0.getClass();
        int v = LinearSystem.n(constraintAnchor0);
        if(this.u0 == 1) {
            this.Z = v;
            this.a0 = 0;
            this.M(this.U.l());
            this.P(0);
            return;
        }
        this.Z = 0;
        this.a0 = v;
        this.P(this.U.r());
        this.M(0);
    }

    public final void S(int v) {
        this.t0.l(v);
        this.v0 = true;
    }

    public final void T(int v) {
        if(this.u0 == v) {
            return;
        }
        this.u0 = v;
        ArrayList arrayList0 = this.R;
        arrayList0.clear();
        this.t0 = this.u0 == 1 ? this.I : this.J;
        arrayList0.add(this.t0);
        ConstraintAnchor[] arr_constraintAnchor = this.Q;
        for(int v1 = 0; v1 < arr_constraintAnchor.length; ++v1) {
            arr_constraintAnchor[v1] = this.t0;
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final void c(LinearSystem linearSystem0, boolean z) {
        ConstraintWidgetContainer constraintWidgetContainer0 = (ConstraintWidgetContainer)this.U;
        if(constraintWidgetContainer0 == null) {
            return;
        }
        ConstraintAnchor constraintAnchor0 = constraintWidgetContainer0.j(Type.a);
        ConstraintAnchor constraintAnchor1 = constraintWidgetContainer0.j(Type.c);
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.b;
        int v = 1;
        int v1 = this.U == null || this.U.T[0] != constraintWidget$DimensionBehaviour0 ? 0 : 1;
        if(this.u0 == 0) {
            constraintAnchor0 = constraintWidgetContainer0.j(Type.b);
            constraintAnchor1 = constraintWidgetContainer0.j(Type.d);
            if(this.U == null || this.U.T[1] != constraintWidget$DimensionBehaviour0) {
                v = 0;
            }
            v1 = v;
        }
        if(this.v0) {
            ConstraintAnchor constraintAnchor2 = this.t0;
            if(constraintAnchor2.c) {
                SolverVariable solverVariable0 = linearSystem0.k(constraintAnchor2);
                linearSystem0.d(solverVariable0, this.t0.d());
                if(this.r0 == -1) {
                    if(this.s0 != -1 && v1 != 0) {
                        SolverVariable solverVariable1 = linearSystem0.k(constraintAnchor1);
                        linearSystem0.f(solverVariable0, linearSystem0.k(constraintAnchor0), 0, 5);
                        linearSystem0.f(solverVariable1, solverVariable0, 0, 5);
                    }
                }
                else if(v1 != 0) {
                    linearSystem0.f(linearSystem0.k(constraintAnchor1), solverVariable0, 0, 5);
                }
                this.v0 = false;
                return;
            }
        }
        if(this.r0 != -1) {
            SolverVariable solverVariable2 = linearSystem0.k(this.t0);
            linearSystem0.e(solverVariable2, linearSystem0.k(constraintAnchor0), this.r0, 8);
            if(v1 != 0) {
                linearSystem0.f(linearSystem0.k(constraintAnchor1), solverVariable2, 0, 5);
            }
        }
        else if(this.s0 != -1) {
            SolverVariable solverVariable3 = linearSystem0.k(this.t0);
            SolverVariable solverVariable4 = linearSystem0.k(constraintAnchor1);
            linearSystem0.e(solverVariable3, solverVariable4, -this.s0, 8);
            if(v1 != 0) {
                linearSystem0.f(solverVariable3, linearSystem0.k(constraintAnchor0), 0, 5);
                linearSystem0.f(solverVariable4, solverVariable3, 0, 5);
            }
        }
        else if(this.q0 != -1.0f) {
            SolverVariable solverVariable5 = linearSystem0.k(this.t0);
            SolverVariable solverVariable6 = linearSystem0.k(constraintAnchor1);
            float f = this.q0;
            ArrayRow arrayRow0 = linearSystem0.l();
            arrayRow0.d.d(solverVariable5, -1.0f);
            arrayRow0.d.d(solverVariable6, f);
            linearSystem0.c(arrayRow0);
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final boolean d() {
        return true;
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final ConstraintAnchor j(Type constraintAnchor$Type0) {
        switch(constraintAnchor$Type0.ordinal()) {
            case 1: 
            case 3: {
                return this.u0 == 1 ? this.t0 : null;
            }
            case 2: 
            case 4: {
                return this.u0 == 0 ? this.t0 : null;
            }
            default: {
                return null;
            }
        }
    }
}


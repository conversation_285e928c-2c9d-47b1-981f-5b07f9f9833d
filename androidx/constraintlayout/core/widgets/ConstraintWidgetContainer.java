package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.LinearSystem;
import androidx.constraintlayout.core.SolverVariable;
import androidx.constraintlayout.core.widgets.analyzer.BasicMeasure.Measure;
import androidx.constraintlayout.core.widgets.analyzer.BasicMeasure.Measurer;
import androidx.constraintlayout.core.widgets.analyzer.BasicMeasure;
import androidx.constraintlayout.core.widgets.analyzer.DependencyGraph;
import androidx.constraintlayout.core.widgets.analyzer.Direct;
import androidx.constraintlayout.core.widgets.analyzer.Grouping;
import androidx.constraintlayout.core.widgets.analyzer.WidgetGroup;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;

public class ConstraintWidgetContainer extends WidgetContainer {
    public int A0;
    public ChainHead[] B0;
    public ChainHead[] C0;
    public int D0;
    public boolean E0;
    public boolean F0;
    public WeakReference G0;
    public WeakReference H0;
    public WeakReference I0;
    public WeakReference J0;
    public final HashSet K0;
    public final Measure L0;
    public final BasicMeasure r0;
    public final DependencyGraph s0;
    public int t0;
    public Measurer u0;
    public boolean v0;
    public final LinearSystem w0;
    public int x0;
    public int y0;
    public int z0;

    public ConstraintWidgetContainer() {
        this.q0 = new ArrayList();
        this.r0 = new BasicMeasure(this);
        DependencyGraph dependencyGraph0 = new DependencyGraph();  // 初始化器: Ljava/lang/Object;-><init>()V
        dependencyGraph0.b = true;
        dependencyGraph0.c = true;
        dependencyGraph0.e = new ArrayList();
        new ArrayList();
        dependencyGraph0.f = null;
        dependencyGraph0.g = new Measure();  // 初始化器: Ljava/lang/Object;-><init>()V
        dependencyGraph0.h = new ArrayList();
        dependencyGraph0.a = this;
        dependencyGraph0.d = this;
        this.s0 = dependencyGraph0;
        this.u0 = null;
        this.v0 = false;
        this.w0 = new LinearSystem();
        this.z0 = 0;
        this.A0 = 0;
        this.B0 = new ChainHead[4];
        this.C0 = new ChainHead[4];
        this.D0 = 0x101;
        this.E0 = false;
        this.F0 = false;
        this.G0 = null;
        this.H0 = null;
        this.I0 = null;
        this.J0 = null;
        this.K0 = new HashSet();
        this.L0 = new Measure();  // 初始化器: Ljava/lang/Object;-><init>()V
    }

    @Override  // androidx.constraintlayout.core.widgets.WidgetContainer
    public final void D() {
        this.w0.t();
        this.x0 = 0;
        this.y0 = 0;
        super.D();
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final void Q(boolean z, boolean z1) {
        super.Q(z, z1);
        int v = this.q0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((ConstraintWidget)this.q0.get(v1)).Q(z, z1);
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.WidgetContainer
    public final void S() {
        int v53;
        int v43;
        SolverVariable solverVariable0;
        ConstraintAnchor constraintAnchor4;
        int v41;
        ConstraintAnchor constraintAnchor3;
        ArrayList arrayList10;
        int v39;
        int v38;
        DimensionBehaviour constraintWidget$DimensionBehaviour12;
        int v30;
        int v29;
        int v33;
        int v32;
        int v31;
        WidgetGroup widgetGroup8;
        WidgetGroup widgetGroup6;
        WidgetGroup widgetGroup4;
        WidgetGroup widgetGroup2;
        DimensionBehaviour constraintWidget$DimensionBehaviour11;
        LinearSystem linearSystem1;
        ConstraintAnchor constraintAnchor2;
        DimensionBehaviour constraintWidget$DimensionBehaviour7;
        DimensionBehaviour constraintWidget$DimensionBehaviour6;
        boolean[] arr_z = Optimizer.a;
        this.Z = 0;
        this.a0 = 0;
        this.E0 = false;
        this.F0 = false;
        int v = this.q0.size();
        int v1 = Math.max(0, this.r());
        int v2 = Math.max(0, this.l());
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = this.T;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = arr_constraintWidget$DimensionBehaviour[1];
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = arr_constraintWidget$DimensionBehaviour[0];
        DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.c;
        ConstraintAnchor constraintAnchor0 = this.J;
        ConstraintAnchor constraintAnchor1 = this.I;
        DimensionBehaviour constraintWidget$DimensionBehaviour3 = DimensionBehaviour.a;
        if(this.t0 != 0 || !Optimizer.b(this.D0, 1)) {
            constraintWidget$DimensionBehaviour7 = constraintWidget$DimensionBehaviour0;
        }
        else {
            Measurer basicMeasure$Measurer0 = this.u0;
            DimensionBehaviour constraintWidget$DimensionBehaviour4 = arr_constraintWidget$DimensionBehaviour[0];
            DimensionBehaviour constraintWidget$DimensionBehaviour5 = arr_constraintWidget$DimensionBehaviour[1];
            this.F();
            ArrayList arrayList0 = this.q0;
            int v3 = arrayList0.size();
            for(int v4 = 0; v4 < v3; ++v4) {
                ((ConstraintWidget)arrayList0.get(v4)).F();
            }
            boolean z = this.v0;
            if(constraintWidget$DimensionBehaviour4 == constraintWidget$DimensionBehaviour3) {
                this.K(0, this.r());
            }
            else {
                constraintAnchor1.l(0);
                this.Z = 0;
            }
            int v5 = 0;
            boolean z1 = false;
            boolean z2 = false;
            while(v5 < v3) {
                ConstraintWidget constraintWidget0 = (ConstraintWidget)arrayList0.get(v5);
                if(constraintWidget0 instanceof Guideline) {
                    constraintWidget$DimensionBehaviour6 = constraintWidget$DimensionBehaviour0;
                    if(((Guideline)constraintWidget0).u0 == 1) {
                        int v6 = ((Guideline)constraintWidget0).r0;
                        if(v6 != -1) {
                            ((Guideline)constraintWidget0).S(v6);
                        }
                        else if(((Guideline)constraintWidget0).s0 != -1 && this.B()) {
                            ((Guideline)constraintWidget0).S(this.r() - ((Guideline)constraintWidget0).s0);
                        }
                        else if(this.B()) {
                            ((Guideline)constraintWidget0).S(((int)(((Guideline)constraintWidget0).q0 * ((float)this.r()) + 0.5f)));
                        }
                        z1 = true;
                    }
                }
                else {
                    constraintWidget$DimensionBehaviour6 = constraintWidget$DimensionBehaviour0;
                    if(constraintWidget0 instanceof Barrier && ((Barrier)constraintWidget0).U() == 0) {
                        z2 = true;
                    }
                }
                ++v5;
                constraintWidget$DimensionBehaviour0 = constraintWidget$DimensionBehaviour6;
            }
            constraintWidget$DimensionBehaviour7 = constraintWidget$DimensionBehaviour0;
            if(z1) {
                for(int v7 = 0; v7 < v3; ++v7) {
                    ConstraintWidget constraintWidget1 = (ConstraintWidget)arrayList0.get(v7);
                    if(constraintWidget1 instanceof Guideline && ((Guideline)constraintWidget1).u0 == 1) {
                        Direct.b(0, ((Guideline)constraintWidget1), basicMeasure$Measurer0, z);
                    }
                }
            }
            Direct.b(0, this, basicMeasure$Measurer0, z);
            if(z2) {
                for(int v8 = 0; v8 < v3; ++v8) {
                    ConstraintWidget constraintWidget2 = (ConstraintWidget)arrayList0.get(v8);
                    if(constraintWidget2 instanceof Barrier && ((Barrier)constraintWidget2).U() == 0 && ((Barrier)constraintWidget2).T()) {
                        Direct.b(1, ((Barrier)constraintWidget2), basicMeasure$Measurer0, z);
                    }
                }
            }
            if(constraintWidget$DimensionBehaviour5 == constraintWidget$DimensionBehaviour3) {
                this.L(0, this.l());
            }
            else {
                constraintAnchor0.l(0);
                this.a0 = 0;
            }
            boolean z3 = false;
            boolean z4 = false;
            for(int v9 = 0; v9 < v3; ++v9) {
                ConstraintWidget constraintWidget3 = (ConstraintWidget)arrayList0.get(v9);
                if(!(constraintWidget3 instanceof Guideline)) {
                    if(constraintWidget3 instanceof Barrier && ((Barrier)constraintWidget3).U() == 1) {
                        z4 = true;
                    }
                }
                else if(((Guideline)constraintWidget3).u0 == 0) {
                    int v10 = ((Guideline)constraintWidget3).r0;
                    if(v10 != -1) {
                        ((Guideline)constraintWidget3).S(v10);
                    }
                    else if(((Guideline)constraintWidget3).s0 != -1 && this.C()) {
                        ((Guideline)constraintWidget3).S(this.l() - ((Guideline)constraintWidget3).s0);
                    }
                    else if(this.C()) {
                        ((Guideline)constraintWidget3).S(((int)(((Guideline)constraintWidget3).q0 * ((float)this.l()) + 0.5f)));
                    }
                    z3 = true;
                }
            }
            if(z3) {
                for(int v11 = 0; v11 < v3; ++v11) {
                    ConstraintWidget constraintWidget4 = (ConstraintWidget)arrayList0.get(v11);
                    if(constraintWidget4 instanceof Guideline && ((Guideline)constraintWidget4).u0 == 0) {
                        Direct.g(1, ((Guideline)constraintWidget4), basicMeasure$Measurer0);
                    }
                }
            }
            Direct.g(0, this, basicMeasure$Measurer0);
            if(z4) {
                for(int v12 = 0; v12 < v3; ++v12) {
                    ConstraintWidget constraintWidget5 = (ConstraintWidget)arrayList0.get(v12);
                    if(constraintWidget5 instanceof Barrier && ((Barrier)constraintWidget5).U() == 1 && ((Barrier)constraintWidget5).T()) {
                        Direct.g(1, ((Barrier)constraintWidget5), basicMeasure$Measurer0);
                    }
                }
            }
            for(int v13 = 0; v13 < v3; ++v13) {
                ConstraintWidget constraintWidget6 = (ConstraintWidget)arrayList0.get(v13);
                if(constraintWidget6.A() && Direct.a(constraintWidget6)) {
                    ConstraintWidgetContainer.V(constraintWidget6, basicMeasure$Measurer0, Direct.a);
                    if(!(constraintWidget6 instanceof Guideline)) {
                        Direct.b(0, constraintWidget6, basicMeasure$Measurer0, z);
                        Direct.g(0, constraintWidget6, basicMeasure$Measurer0);
                    }
                    else if(((Guideline)constraintWidget6).u0 == 0) {
                        Direct.g(0, constraintWidget6, basicMeasure$Measurer0);
                    }
                    else {
                        Direct.b(0, constraintWidget6, basicMeasure$Measurer0, z);
                    }
                }
            }
            for(int v14 = 0; v14 < v; ++v14) {
                ConstraintWidget constraintWidget7 = (ConstraintWidget)this.q0.get(v14);
                if(constraintWidget7.A() && !(constraintWidget7 instanceof Guideline) && !(constraintWidget7 instanceof Barrier) && !(constraintWidget7 instanceof VirtualLayout) && !constraintWidget7.F) {
                    DimensionBehaviour constraintWidget$DimensionBehaviour8 = constraintWidget7.k(0);
                    DimensionBehaviour constraintWidget$DimensionBehaviour9 = constraintWidget7.k(1);
                    if(constraintWidget$DimensionBehaviour8 != constraintWidget$DimensionBehaviour2 || constraintWidget7.r == 1 || constraintWidget$DimensionBehaviour9 != constraintWidget$DimensionBehaviour2 || constraintWidget7.s == 1) {
                        Measure basicMeasure$Measure0 = new Measure();  // 初始化器: Ljava/lang/Object;-><init>()V
                        ConstraintWidgetContainer.V(constraintWidget7, this.u0, basicMeasure$Measure0);
                    }
                }
            }
        }
        DimensionBehaviour constraintWidget$DimensionBehaviour10 = DimensionBehaviour.b;
        LinearSystem linearSystem0 = this.w0;
        if(v <= 2) {
            linearSystem1 = linearSystem0;
            v29 = v2;
            v30 = v1;
            constraintWidget$DimensionBehaviour12 = constraintWidget$DimensionBehaviour7;
            constraintAnchor2 = constraintAnchor0;
        label_424:
            v33 = 0;
        }
        else if((constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour10 || constraintWidget$DimensionBehaviour7 == constraintWidget$DimensionBehaviour10) && Optimizer.b(this.D0, 0x400)) {
            Measurer basicMeasure$Measurer1 = this.u0;
            ArrayList arrayList1 = this.q0;
            int v15 = arrayList1.size();
            int v16 = 0;
            while(v16 < v15) {
                ConstraintWidget constraintWidget8 = (ConstraintWidget)arrayList1.get(v16);
                constraintAnchor2 = constraintAnchor0;
                if(Grouping.b(arr_constraintWidget$DimensionBehaviour[0], arr_constraintWidget$DimensionBehaviour[1], constraintWidget8.T[0], constraintWidget8.T[1]) && !(constraintWidget8 instanceof Flow)) {
                    ++v16;
                    constraintAnchor0 = constraintAnchor2;
                    continue;
                }
                linearSystem1 = linearSystem0;
                constraintWidget$DimensionBehaviour11 = constraintWidget$DimensionBehaviour7;
                v29 = v2;
                v30 = v1;
                constraintWidget$DimensionBehaviour12 = constraintWidget$DimensionBehaviour11;
                goto label_424;
            }
            constraintAnchor2 = constraintAnchor0;
            ArrayList arrayList2 = null;
            ArrayList arrayList3 = null;
            ArrayList arrayList4 = null;
            ArrayList arrayList5 = null;
            ArrayList arrayList6 = null;
            ArrayList arrayList7 = null;
            for(int v17 = 0; v17 < v15; ++v17) {
                ConstraintWidget constraintWidget9 = (ConstraintWidget)arrayList1.get(v17);
                if(!Grouping.b(arr_constraintWidget$DimensionBehaviour[0], arr_constraintWidget$DimensionBehaviour[1], constraintWidget9.T[0], constraintWidget9.T[1])) {
                    ConstraintWidgetContainer.V(constraintWidget9, basicMeasure$Measurer1, this.L0);
                }
                if(constraintWidget9 instanceof Guideline) {
                    if(((Guideline)constraintWidget9).u0 == 0) {
                        if(arrayList4 == null) {
                            arrayList4 = new ArrayList();
                        }
                        arrayList4.add(((Guideline)constraintWidget9));
                    }
                    if(((Guideline)constraintWidget9).u0 == 1) {
                        if(arrayList2 == null) {
                            arrayList2 = new ArrayList();
                        }
                        arrayList2.add(((Guideline)constraintWidget9));
                    }
                }
                if(constraintWidget9 instanceof HelperWidget) {
                    if(constraintWidget9 instanceof Barrier) {
                        if(((Barrier)constraintWidget9).U() == 0) {
                            if(arrayList3 == null) {
                                arrayList3 = new ArrayList();
                            }
                            arrayList3.add(((Barrier)constraintWidget9));
                        }
                        if(((Barrier)constraintWidget9).U() == 1) {
                            if(arrayList5 == null) {
                                arrayList5 = new ArrayList();
                            }
                            arrayList5.add(((Barrier)constraintWidget9));
                        }
                    }
                    else {
                        if(arrayList3 == null) {
                            arrayList3 = new ArrayList();
                        }
                        arrayList3.add(((HelperWidget)constraintWidget9));
                        if(arrayList5 == null) {
                            arrayList5 = new ArrayList();
                        }
                        arrayList5.add(((HelperWidget)constraintWidget9));
                    }
                }
                if(constraintWidget9.I.f == null && constraintWidget9.K.f == null && !(constraintWidget9 instanceof Guideline) && !(constraintWidget9 instanceof Barrier)) {
                    if(arrayList6 == null) {
                        arrayList6 = new ArrayList();
                    }
                    arrayList6.add(constraintWidget9);
                }
                if(constraintWidget9.J.f == null && constraintWidget9.L.f == null && constraintWidget9.M.f == null && !(constraintWidget9 instanceof Guideline) && !(constraintWidget9 instanceof Barrier)) {
                    if(arrayList7 == null) {
                        arrayList7 = new ArrayList();
                    }
                    arrayList7.add(constraintWidget9);
                }
            }
            constraintWidget$DimensionBehaviour11 = constraintWidget$DimensionBehaviour7;
            ArrayList arrayList8 = new ArrayList();
            if(arrayList2 != null) {
                for(Object object0: arrayList2) {
                    Grouping.a(((Guideline)object0), 0, arrayList8, null);
                }
            }
            if(arrayList3 != null) {
                for(Object object1: arrayList3) {
                    WidgetGroup widgetGroup0 = Grouping.a(((HelperWidget)object1), 0, arrayList8, null);
                    ((HelperWidget)object1).S(0, widgetGroup0, arrayList8);
                    widgetGroup0.a(arrayList8);
                }
            }
            HashSet hashSet0 = this.j(Type.a).a;
            if(hashSet0 != null) {
                for(Object object2: hashSet0) {
                    Grouping.a(((ConstraintAnchor)object2).d, 0, arrayList8, null);
                }
            }
            HashSet hashSet1 = this.j(Type.c).a;
            if(hashSet1 != null) {
                for(Object object3: hashSet1) {
                    Grouping.a(((ConstraintAnchor)object3).d, 0, arrayList8, null);
                }
            }
            Type constraintAnchor$Type0 = Type.f;
            HashSet hashSet2 = this.j(constraintAnchor$Type0).a;
            if(hashSet2 != null) {
                for(Object object4: hashSet2) {
                    Grouping.a(((ConstraintAnchor)object4).d, 0, arrayList8, null);
                }
            }
            if(arrayList6 != null) {
                for(Object object5: arrayList6) {
                    Grouping.a(((ConstraintWidget)object5), 0, arrayList8, null);
                }
            }
            if(arrayList4 != null) {
                for(Object object6: arrayList4) {
                    Grouping.a(((Guideline)object6), 1, arrayList8, null);
                }
            }
            if(arrayList5 != null) {
                for(Object object7: arrayList5) {
                    WidgetGroup widgetGroup1 = Grouping.a(((HelperWidget)object7), 1, arrayList8, null);
                    ((HelperWidget)object7).S(1, widgetGroup1, arrayList8);
                    widgetGroup1.a(arrayList8);
                }
            }
            HashSet hashSet3 = this.j(Type.b).a;
            if(hashSet3 != null) {
                for(Object object8: hashSet3) {
                    Grouping.a(((ConstraintAnchor)object8).d, 1, arrayList8, null);
                }
            }
            HashSet hashSet4 = this.j(Type.e).a;
            if(hashSet4 != null) {
                for(Object object9: hashSet4) {
                    Grouping.a(((ConstraintAnchor)object9).d, 1, arrayList8, null);
                }
            }
            HashSet hashSet5 = this.j(Type.d).a;
            if(hashSet5 != null) {
                for(Object object10: hashSet5) {
                    Grouping.a(((ConstraintAnchor)object10).d, 1, arrayList8, null);
                }
            }
            HashSet hashSet6 = this.j(constraintAnchor$Type0).a;
            if(hashSet6 != null) {
                for(Object object11: hashSet6) {
                    Grouping.a(((ConstraintAnchor)object11).d, 1, arrayList8, null);
                }
            }
            if(arrayList7 != null) {
                for(Object object12: arrayList7) {
                    Grouping.a(((ConstraintWidget)object12), 1, arrayList8, null);
                }
            }
            int v18 = 0;
            while(v18 < v15) {
                ConstraintWidget constraintWidget10 = (ConstraintWidget)arrayList1.get(v18);
                if(constraintWidget10.T[0] == constraintWidget$DimensionBehaviour2 && constraintWidget10.T[1] == constraintWidget$DimensionBehaviour2) {
                    int v19 = constraintWidget10.o0;
                    int v20 = arrayList8.size();
                    int v21 = 0;
                    while(true) {
                        widgetGroup2 = null;
                        if(v21 < v20) {
                            WidgetGroup widgetGroup3 = (WidgetGroup)arrayList8.get(v21);
                            if(v19 == widgetGroup3.b) {
                                widgetGroup2 = widgetGroup3;
                            }
                            else {
                                ++v21;
                                continue;
                            }
                        }
                        break;
                    }
                    int v22 = constraintWidget10.p0;
                    int v23 = arrayList8.size();
                    int v24 = 0;
                    while(true) {
                        widgetGroup4 = null;
                        if(v24 < v23) {
                            WidgetGroup widgetGroup5 = (WidgetGroup)arrayList8.get(v24);
                            if(v22 == widgetGroup5.b) {
                                widgetGroup4 = widgetGroup5;
                            }
                            else {
                                ++v24;
                                continue;
                            }
                        }
                        break;
                    }
                    if(widgetGroup2 != null && widgetGroup4 != null) {
                        widgetGroup2.c(0, widgetGroup4);
                        widgetGroup4.c = 2;
                        arrayList8.remove(widgetGroup2);
                    }
                }
                ++v18;
            }
            if(arrayList8.size() <= 1) {
                linearSystem1 = linearSystem0;
                v29 = v2;
                v30 = v1;
                constraintWidget$DimensionBehaviour12 = constraintWidget$DimensionBehaviour11;
                goto label_424;
            }
            else {
                if(arr_constraintWidget$DimensionBehaviour[0] == constraintWidget$DimensionBehaviour10) {
                    int v25 = 0;
                    widgetGroup6 = null;
                    for(Object object13: arrayList8) {
                        WidgetGroup widgetGroup7 = (WidgetGroup)object13;
                        if(widgetGroup7.c != 1) {
                            int v26 = widgetGroup7.b(linearSystem0, 0);
                            if(v26 > v25) {
                                widgetGroup6 = widgetGroup7;
                                v25 = v26;
                            }
                        }
                    }
                    linearSystem1 = linearSystem0;
                    if(widgetGroup6 == null) {
                        widgetGroup6 = null;
                    }
                    else {
                        this.N(constraintWidget$DimensionBehaviour3);
                        this.P(v25);
                    }
                }
                else {
                    linearSystem1 = linearSystem0;
                    widgetGroup6 = null;
                }
                if(arr_constraintWidget$DimensionBehaviour[1] == constraintWidget$DimensionBehaviour10) {
                    int v27 = 0;
                    widgetGroup8 = null;
                    for(Object object14: arrayList8) {
                        WidgetGroup widgetGroup9 = (WidgetGroup)object14;
                        if(widgetGroup9.c != 0) {
                            int v28 = widgetGroup9.b(linearSystem1, 1);
                            if(v28 > v27) {
                                widgetGroup8 = widgetGroup9;
                                v27 = v28;
                            }
                        }
                    }
                    if(widgetGroup8 == null) {
                        widgetGroup8 = null;
                    }
                    else {
                        this.O(constraintWidget$DimensionBehaviour3);
                        this.M(v27);
                    }
                }
                else {
                    widgetGroup8 = null;
                }
                if(widgetGroup6 != null || widgetGroup8 != null) {
                    if(constraintWidget$DimensionBehaviour1 != constraintWidget$DimensionBehaviour10) {
                        v31 = v1;
                    }
                    else if(v1 < this.r() && v1 > 0) {
                        this.P(v1);
                        this.E0 = true;
                        v31 = v1;
                    }
                    else {
                        v31 = this.r();
                    }
                    constraintWidget$DimensionBehaviour12 = constraintWidget$DimensionBehaviour11;
                    if(constraintWidget$DimensionBehaviour12 != constraintWidget$DimensionBehaviour10) {
                        v32 = v2;
                    }
                    else if(v2 < this.l() && v2 > 0) {
                        this.M(v2);
                        this.F0 = true;
                        v32 = v2;
                    }
                    else {
                        v32 = this.l();
                    }
                    v29 = v32;
                    v30 = v31;
                    v33 = 1;
                }
                else {
                    v29 = v2;
                    v30 = v1;
                    constraintWidget$DimensionBehaviour12 = constraintWidget$DimensionBehaviour11;
                    goto label_424;
                }
            }
        }
        else {
            linearSystem1 = linearSystem0;
            constraintWidget$DimensionBehaviour12 = constraintWidget$DimensionBehaviour7;
            constraintAnchor2 = constraintAnchor0;
            v29 = v2;
            v30 = v1;
            goto label_424;
        }
        boolean z5 = this.W(0x40) || this.W(0x80);
        linearSystem1.getClass();
        linearSystem1.h = false;
        if(this.D0 != 0 && z5) {
            linearSystem1.h = true;
        }
        ArrayList arrayList9 = this.q0;
        boolean z6 = arr_constraintWidget$DimensionBehaviour[0] == constraintWidget$DimensionBehaviour10 || arr_constraintWidget$DimensionBehaviour[1] == constraintWidget$DimensionBehaviour10;
        this.z0 = 0;
        this.A0 = 0;
        for(int v34 = 0; v34 < v; ++v34) {
            ConstraintWidget constraintWidget11 = (ConstraintWidget)this.q0.get(v34);
            if(constraintWidget11 instanceof WidgetContainer) {
                ((WidgetContainer)constraintWidget11).S();
            }
        }
        boolean z7 = this.W(0x40);
        int v35 = v33;
        int v36 = 0;
        int v37 = 1;
        while(v37 != 0) {
            try {
                linearSystem1.t();
                v38 = v37;
            }
            catch(Exception exception0) {
                v39 = v29;
                arrayList10 = arrayList9;
                v38 = v37;
                constraintAnchor3 = constraintAnchor2;
                v41 = v35;
                goto label_524;
            }
            try {
                this.z0 = 0;
                this.A0 = 0;
                this.h(linearSystem1);
                for(int v40 = 0; v40 < v; ++v40) {
                    ((ConstraintWidget)this.q0.get(v40)).h(linearSystem1);
                }
                this.U(linearSystem1);
                WeakReference weakReference0 = this.G0;
                goto label_471;
            label_466:
                v39 = v29;
                arrayList10 = arrayList9;
                constraintAnchor3 = constraintAnchor2;
                v41 = v35;
                goto label_524;
            }
            catch(Exception exception0) {
                goto label_466;
            }
        label_471:
            if(weakReference0 == null) {
                goto label_498;
            }
            else {
                try {
                    if(weakReference0.get() != null) {
                        constraintAnchor4 = (ConstraintAnchor)this.G0.get();
                        v41 = v35;
                        solverVariable0 = linearSystem1.k(constraintAnchor2);
                        constraintAnchor3 = constraintAnchor2;
                        goto label_490;
                    }
                    goto label_498;
                }
                catch(Exception exception0) {
                    try {
                        v39 = v29;
                        arrayList10 = arrayList9;
                        constraintAnchor3 = constraintAnchor2;
                        v41 = v35;
                    }
                    catch(Exception exception0) {
                    }
                    v38 = 1;
                    goto label_524;
                }
                try {
                    solverVariable0 = linearSystem1.k(constraintAnchor2);
                    constraintAnchor3 = constraintAnchor2;
                    goto label_490;
                }
                catch(Exception exception0) {
                    try {
                        v39 = v29;
                        arrayList10 = arrayList9;
                        constraintAnchor3 = constraintAnchor2;
                        v38 = 1;
                        goto label_524;
                    label_490:
                        v39 = v29;
                        arrayList10 = arrayList9;
                        SolverVariable solverVariable1 = this.w0.k(constraintAnchor4);
                        v39 = v29;
                        arrayList10 = arrayList9;
                        this.w0.f(solverVariable1, solverVariable0, 0, 5);
                        this.G0 = null;
                        goto label_502;
                    label_498:
                        v39 = v29;
                        arrayList10 = arrayList9;
                        constraintAnchor3 = constraintAnchor2;
                        v41 = v35;
                    label_502:
                        if(this.I0 != null && this.I0.get() != null) {
                            ConstraintAnchor constraintAnchor5 = (ConstraintAnchor)this.I0.get();
                            SolverVariable solverVariable2 = linearSystem1.k(this.L);
                            SolverVariable solverVariable3 = this.w0.k(constraintAnchor5);
                            this.w0.f(solverVariable2, solverVariable3, 0, 5);
                            this.I0 = null;
                        }
                        if(this.H0 != null && this.H0.get() != null) {
                            ConstraintAnchor constraintAnchor6 = (ConstraintAnchor)this.H0.get();
                            SolverVariable solverVariable4 = linearSystem1.k(constraintAnchor1);
                            SolverVariable solverVariable5 = this.w0.k(constraintAnchor6);
                            this.w0.f(solverVariable5, solverVariable4, 0, 5);
                            this.H0 = null;
                        }
                        if(this.J0 != null && this.J0.get() != null) {
                            ConstraintAnchor constraintAnchor7 = (ConstraintAnchor)this.J0.get();
                            SolverVariable solverVariable6 = linearSystem1.k(this.K);
                            SolverVariable solverVariable7 = this.w0.k(constraintAnchor7);
                            this.w0.f(solverVariable6, solverVariable7, 0, 5);
                            this.J0 = null;
                        }
                        linearSystem1.p();
                        goto label_535;
                    }
                    catch(Exception exception0) {
                    }
                    v38 = 1;
                }
            }
        label_524:
            exception0.printStackTrace();
            System.out.println("EXCEPTION : " + exception0);
            if(v38 == 0) {
                this.R(linearSystem1, z7);
                for(int v42 = 0; v42 < v; ++v42) {
                    ((ConstraintWidget)this.q0.get(v42)).R(linearSystem1, z7);
                }
                v43 = 0;
            }
            else {
            label_535:
                arr_z[2] = false;
                boolean z8 = this.W(0x40);
                this.R(linearSystem1, z8);
                int v44 = this.q0.size();
                v43 = 0;
                for(int v45 = 0; v45 < v44; ++v45) {
                    ConstraintWidget constraintWidget12 = (ConstraintWidget)this.q0.get(v45);
                    constraintWidget12.R(linearSystem1, z8);
                    if(constraintWidget12.h != -1 || constraintWidget12.i != -1) {
                        v43 = 1;
                    }
                }
            }
            if(z6 && v36 + 1 < 8 && arr_z[2]) {
                int v46 = 0;
                int v48 = 0;
                for(int v47 = 0; v47 < v; ++v47) {
                    ConstraintWidget constraintWidget13 = (ConstraintWidget)this.q0.get(v47);
                    v48 = Math.max(v48, constraintWidget13.r() + constraintWidget13.Z);
                    v46 = Math.max(v46, constraintWidget13.l() + constraintWidget13.a0);
                }
                int v49 = Math.max(this.c0, v48);
                int v50 = Math.max(this.d0, v46);
                if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour10 && this.r() < v49) {
                    this.P(v49);
                    arr_constraintWidget$DimensionBehaviour[0] = constraintWidget$DimensionBehaviour10;
                    v43 = 1;
                    v41 = 1;
                }
                if(constraintWidget$DimensionBehaviour12 == constraintWidget$DimensionBehaviour10 && this.l() < v50) {
                    this.M(v50);
                    arr_constraintWidget$DimensionBehaviour[1] = constraintWidget$DimensionBehaviour10;
                    v43 = 1;
                    v41 = 1;
                }
            }
            int v51 = Math.max(this.c0, this.r());
            if(v51 > this.r()) {
                this.P(v51);
                arr_constraintWidget$DimensionBehaviour[0] = constraintWidget$DimensionBehaviour3;
                v43 = 1;
                v41 = 1;
            }
            int v52 = Math.max(this.d0, this.l());
            if(v52 > this.l()) {
                this.M(v52);
                arr_constraintWidget$DimensionBehaviour[1] = constraintWidget$DimensionBehaviour3;
                v43 = 1;
                v41 = 1;
            }
            if(v41 == 0) {
                if(arr_constraintWidget$DimensionBehaviour[0] == constraintWidget$DimensionBehaviour10 && v30 > 0 && this.r() > v30) {
                    this.E0 = true;
                    arr_constraintWidget$DimensionBehaviour[0] = constraintWidget$DimensionBehaviour3;
                    this.P(v30);
                    v43 = 1;
                    v41 = 1;
                }
                if(arr_constraintWidget$DimensionBehaviour[1] != constraintWidget$DimensionBehaviour10 || v39 <= 0) {
                    v53 = v39;
                }
                else {
                    v53 = v39;
                    if(this.l() > v53) {
                        this.F0 = true;
                        arr_constraintWidget$DimensionBehaviour[1] = constraintWidget$DimensionBehaviour3;
                        this.M(v53);
                        v35 = 1;
                        v43 = 1;
                        goto label_602;
                    }
                }
            }
            else {
                v53 = v39;
            }
            v35 = v41;
        label_602:
            v37 = v36 + 1 <= 8 ? v43 : 0;
            ++v36;
            v29 = v53;
            constraintAnchor2 = constraintAnchor3;
            arrayList9 = arrayList10;
        }
        this.q0 = arrayList9;
        if(v35 != 0) {
            arr_constraintWidget$DimensionBehaviour[0] = constraintWidget$DimensionBehaviour1;
            arr_constraintWidget$DimensionBehaviour[1] = constraintWidget$DimensionBehaviour12;
        }
        this.G(linearSystem1.m);
    }

    public final void T(int v, ConstraintWidget constraintWidget0) {
        switch(v) {
            case 0: {
                ChainHead[] arr_chainHead = this.C0;
                if(this.z0 + 1 >= arr_chainHead.length) {
                    this.C0 = (ChainHead[])Arrays.copyOf(arr_chainHead, arr_chainHead.length * 2);
                }
                ChainHead[] arr_chainHead1 = this.C0;
                int v1 = this.z0;
                arr_chainHead1[v1] = new ChainHead(constraintWidget0, 0, this.v0);
                this.z0 = v1 + 1;
                return;
            }
            case 1: {
                ChainHead[] arr_chainHead2 = this.B0;
                if(this.A0 + 1 >= arr_chainHead2.length) {
                    this.B0 = (ChainHead[])Arrays.copyOf(arr_chainHead2, arr_chainHead2.length * 2);
                }
                ChainHead[] arr_chainHead3 = this.B0;
                int v2 = this.A0;
                arr_chainHead3[v2] = new ChainHead(constraintWidget0, 1, this.v0);
                this.A0 = v2 + 1;
            }
        }
    }

    public final void U(LinearSystem linearSystem0) {
        boolean z = this.W(0x40);
        this.c(linearSystem0, z);
        int v = this.q0.size();
        boolean z1 = false;
        for(int v1 = 0; v1 < v; ++v1) {
            ConstraintWidget constraintWidget0 = (ConstraintWidget)this.q0.get(v1);
            constraintWidget0.S[0] = false;
            constraintWidget0.S[1] = false;
            if(constraintWidget0 instanceof Barrier) {
                z1 = true;
            }
        }
        if(z1) {
            for(int v2 = 0; v2 < v; ++v2) {
                ConstraintWidget constraintWidget1 = (ConstraintWidget)this.q0.get(v2);
                if(constraintWidget1 instanceof Barrier) {
                    Barrier barrier0 = (Barrier)constraintWidget1;
                    for(int v3 = 0; v3 < barrier0.r0; ++v3) {
                        ConstraintWidget constraintWidget2 = barrier0.q0[v3];
                        if(barrier0.t0 || constraintWidget2.d()) {
                            int v4 = barrier0.s0;
                            if(v4 == 0 || v4 == 1) {
                                constraintWidget2.S[0] = true;
                            }
                            else if(v4 == 2 || v4 == 3) {
                                constraintWidget2.S[1] = true;
                            }
                        }
                    }
                }
            }
        }
        HashSet hashSet0 = this.K0;
        hashSet0.clear();
        for(int v5 = 0; v5 < v; ++v5) {
            ConstraintWidget constraintWidget3 = (ConstraintWidget)this.q0.get(v5);
            constraintWidget3.getClass();
            boolean z2 = constraintWidget3 instanceof VirtualLayout;
            if(z2 || constraintWidget3 instanceof Guideline) {
                if(z2) {
                    hashSet0.add(constraintWidget3);
                }
                else {
                    constraintWidget3.c(linearSystem0, z);
                }
            }
        }
        while(hashSet0.size() > 0) {
            int v6 = hashSet0.size();
        alab1:
            for(Object object0: hashSet0) {
                VirtualLayout virtualLayout0 = (VirtualLayout)(((ConstraintWidget)object0));
                for(int v7 = 0; v7 < virtualLayout0.r0; ++v7) {
                    if(hashSet0.contains(virtualLayout0.q0[v7])) {
                        virtualLayout0.c(linearSystem0, z);
                        hashSet0.remove(virtualLayout0);
                        break alab1;
                    }
                }
            }
            if(v6 == hashSet0.size()) {
                for(Object object1: hashSet0) {
                    ((ConstraintWidget)object1).c(linearSystem0, z);
                }
                hashSet0.clear();
            }
        }
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.b;
        if(LinearSystem.q) {
            HashSet hashSet1 = new HashSet();
            for(int v8 = 0; v8 < v; ++v8) {
                ConstraintWidget constraintWidget4 = (ConstraintWidget)this.q0.get(v8);
                constraintWidget4.getClass();
                if(!(constraintWidget4 instanceof VirtualLayout) && !(constraintWidget4 instanceof Guideline)) {
                    hashSet1.add(constraintWidget4);
                }
            }
            this.b(this, linearSystem0, hashSet1, (this.T[0] == constraintWidget$DimensionBehaviour0 ? 0 : 1), false);
            for(Object object2: hashSet1) {
                Optimizer.a(this, linearSystem0, ((ConstraintWidget)object2));
                ((ConstraintWidget)object2).c(linearSystem0, z);
            }
        }
        else {
            for(int v9 = 0; v9 < v; ++v9) {
                ConstraintWidget constraintWidget5 = (ConstraintWidget)this.q0.get(v9);
                if(constraintWidget5 instanceof ConstraintWidgetContainer) {
                    DimensionBehaviour constraintWidget$DimensionBehaviour1 = constraintWidget5.T[0];
                    DimensionBehaviour constraintWidget$DimensionBehaviour2 = constraintWidget5.T[1];
                    DimensionBehaviour constraintWidget$DimensionBehaviour3 = DimensionBehaviour.a;
                    if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour0) {
                        constraintWidget5.N(constraintWidget$DimensionBehaviour3);
                    }
                    if(constraintWidget$DimensionBehaviour2 == constraintWidget$DimensionBehaviour0) {
                        constraintWidget5.O(constraintWidget$DimensionBehaviour3);
                    }
                    constraintWidget5.c(linearSystem0, z);
                    if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour0) {
                        constraintWidget5.N(constraintWidget$DimensionBehaviour1);
                    }
                    if(constraintWidget$DimensionBehaviour2 == constraintWidget$DimensionBehaviour0) {
                        constraintWidget5.O(constraintWidget$DimensionBehaviour2);
                    }
                }
                else {
                    Optimizer.a(this, linearSystem0, constraintWidget5);
                    if(!(constraintWidget5 instanceof VirtualLayout) && !(constraintWidget5 instanceof Guideline)) {
                        constraintWidget5.c(linearSystem0, z);
                    }
                }
            }
        }
        if(this.z0 > 0) {
            Chain.a(this, linearSystem0, null, 0);
        }
        if(this.A0 > 0) {
            Chain.a(this, linearSystem0, null, 1);
        }
    }

    public static void V(ConstraintWidget constraintWidget0, Measurer basicMeasure$Measurer0, Measure basicMeasure$Measure0) {
        int v1;
        int v;
        if(basicMeasure$Measurer0 == null) {
            return;
        }
        if(constraintWidget0.h0 != 8 && !(constraintWidget0 instanceof Guideline) && !(constraintWidget0 instanceof Barrier)) {
            basicMeasure$Measure0.a = constraintWidget0.T[0];
            basicMeasure$Measure0.b = constraintWidget0.T[1];
            basicMeasure$Measure0.c = constraintWidget0.r();
            basicMeasure$Measure0.d = constraintWidget0.l();
            basicMeasure$Measure0.i = false;
            basicMeasure$Measure0.j = 0;
            boolean z = basicMeasure$Measure0.a == DimensionBehaviour.c;
            boolean z1 = basicMeasure$Measure0.b == DimensionBehaviour.c;
            boolean z2 = z && constraintWidget0.X > 0.0f;
            boolean z3 = z1 && constraintWidget0.X > 0.0f;
            DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.b;
            DimensionBehaviour constraintWidget$DimensionBehaviour1 = DimensionBehaviour.a;
            if(z && constraintWidget0.u(0) && constraintWidget0.r == 0 && !z2) {
                basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour0;
                if(z1 && constraintWidget0.s == 0) {
                    basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour1;
                }
                z = false;
            }
            if(z1 && constraintWidget0.u(1) && constraintWidget0.s == 0 && !z3) {
                basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour0;
                if(z && constraintWidget0.r == 0) {
                    basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour1;
                }
                z1 = false;
            }
            if(constraintWidget0.B()) {
                basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour1;
                z = false;
            }
            if(constraintWidget0.C()) {
                basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour1;
                z1 = false;
            }
            int[] arr_v = constraintWidget0.t;
            if(z2) {
                if(arr_v[0] == 4) {
                    basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour1;
                }
                else if(!z1) {
                    if(basicMeasure$Measure0.b == constraintWidget$DimensionBehaviour1) {
                        v = basicMeasure$Measure0.d;
                    }
                    else {
                        basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour0;
                        basicMeasure$Measurer0.a(constraintWidget0, basicMeasure$Measure0);
                        v = basicMeasure$Measure0.f;
                    }
                    basicMeasure$Measure0.a = constraintWidget$DimensionBehaviour1;
                    basicMeasure$Measure0.c = (int)(constraintWidget0.X * ((float)v));
                }
            }
            if(z3) {
                if(arr_v[1] == 4) {
                    basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour1;
                }
                else if(!z) {
                    if(basicMeasure$Measure0.a == constraintWidget$DimensionBehaviour1) {
                        v1 = basicMeasure$Measure0.c;
                    }
                    else {
                        basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour0;
                        basicMeasure$Measurer0.a(constraintWidget0, basicMeasure$Measure0);
                        v1 = basicMeasure$Measure0.e;
                    }
                    basicMeasure$Measure0.b = constraintWidget$DimensionBehaviour1;
                    basicMeasure$Measure0.d = constraintWidget0.Y == -1 ? ((int)(((float)v1) / constraintWidget0.X)) : ((int)(constraintWidget0.X * ((float)v1)));
                }
            }
            basicMeasure$Measurer0.a(constraintWidget0, basicMeasure$Measure0);
            constraintWidget0.P(basicMeasure$Measure0.e);
            constraintWidget0.M(basicMeasure$Measure0.f);
            constraintWidget0.E = basicMeasure$Measure0.h;
            constraintWidget0.J(basicMeasure$Measure0.g);
            basicMeasure$Measure0.j = 0;
            return;
        }
        basicMeasure$Measure0.e = 0;
        basicMeasure$Measure0.f = 0;
    }

    public final boolean W(int v) {
        return (this.D0 & v) == v;
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final void o(StringBuilder stringBuilder0) {
        stringBuilder0.append(this.j + ":{\n");
        stringBuilder0.append("  actualWidth:" + this.V);
        stringBuilder0.append("\n");
        stringBuilder0.append("  actualHeight:" + this.W);
        stringBuilder0.append("\n");
        for(Object object0: this.q0) {
            ((ConstraintWidget)object0).o(stringBuilder0);
            stringBuilder0.append(",\n");
        }
        stringBuilder0.append("}");
    }
}


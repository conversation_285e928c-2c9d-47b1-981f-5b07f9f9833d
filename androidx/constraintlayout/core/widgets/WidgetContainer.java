package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.Cache;
import java.util.ArrayList;

public abstract class WidgetContainer extends ConstraintWidget {
    public ArrayList q0;

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public void D() {
        this.q0.clear();
        super.D();
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final void G(Cache cache0) {
        super.G(cache0);
        int v = this.q0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((ConstraintWidget)this.q0.get(v1)).G(cache0);
        }
    }

    public abstract void S();
}


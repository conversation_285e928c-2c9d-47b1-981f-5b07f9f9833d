package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.LinearSystem;

public abstract class Optimizer {
    public static final boolean[] a;

    static {
        Optimizer.a = new boolean[3];
    }

    public static void a(ConstraintWidgetContainer constraintWidgetContainer0, LinearSystem linearSystem0, ConstraintWidget constraintWidget0) {
        constraintWidget0.o = -1;
        constraintWidget0.p = -1;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.b;
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = DimensionBehaviour.d;
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = constraintWidget0.T;
        if(constraintWidgetContainer0.T[0] != constraintWidget$DimensionBehaviour0 && arr_constraintWidget$DimensionBehaviour[0] == constraintWidget$DimensionBehaviour1) {
            int v = constraintWidget0.I.g;
            int v1 = constraintWidgetContainer0.r() - constraintWidget0.K.g;
            constraintWidget0.I.i = linearSystem0.k(constraintWidget0.I);
            constraintWidget0.K.i = linearSystem0.k(constraintWidget0.K);
            linearSystem0.d(constraintWidget0.I.i, v);
            linearSystem0.d(constraintWidget0.K.i, v1);
            constraintWidget0.o = 2;
            constraintWidget0.Z = v;
            int v2 = v1 - v;
            constraintWidget0.V = v2;
            int v3 = constraintWidget0.c0;
            if(v2 < v3) {
                constraintWidget0.V = v3;
            }
        }
        if(constraintWidgetContainer0.T[1] != constraintWidget$DimensionBehaviour0 && arr_constraintWidget$DimensionBehaviour[1] == constraintWidget$DimensionBehaviour1) {
            int v4 = constraintWidget0.J.g;
            int v5 = constraintWidgetContainer0.l() - constraintWidget0.L.g;
            constraintWidget0.J.i = linearSystem0.k(constraintWidget0.J);
            constraintWidget0.L.i = linearSystem0.k(constraintWidget0.L);
            linearSystem0.d(constraintWidget0.J.i, v4);
            linearSystem0.d(constraintWidget0.L.i, v5);
            if(constraintWidget0.b0 > 0 || constraintWidget0.h0 == 8) {
                constraintWidget0.M.i = linearSystem0.k(constraintWidget0.M);
                linearSystem0.d(constraintWidget0.M.i, constraintWidget0.b0 + v4);
            }
            constraintWidget0.p = 2;
            constraintWidget0.a0 = v4;
            int v6 = v5 - v4;
            constraintWidget0.W = v6;
            int v7 = constraintWidget0.d0;
            if(v6 < v7) {
                constraintWidget0.W = v7;
            }
        }
    }

    public static final boolean b(int v, int v1) {
        return (v & v1) == v1;
    }
}


package androidx.constraintlayout.core.widgets;

import androidx.constraintlayout.core.SolverVariable;
import androidx.constraintlayout.core.widgets.analyzer.Grouping;
import androidx.constraintlayout.core.widgets.analyzer.WidgetGroup;
import java.util.ArrayList;
import java.util.HashSet;

public class ConstraintAnchor {
    public static enum Type {
        NONE,  // 此枚举字段已被移除
        LEFT,
        TOP,
        RIGHT,
        BOTTOM,
        BASELINE,
        CENTER,
        CENTER_X,
        CENTER_Y;

    }

    public HashSet a;
    public int b;
    public boolean c;
    public final ConstraintWidget d;
    public final Type e;
    public ConstraintAnchor f;
    public int g;
    public int h;
    public SolverVariable i;

    public ConstraintAnchor(ConstraintWidget constraintWidget0, Type constraintAnchor$Type0) {
        this.a = null;
        this.g = 0;
        this.h = 0x80000000;
        this.d = constraintWidget0;
        this.e = constraintAnchor$Type0;
    }

    public final void a(ConstraintAnchor constraintAnchor0, int v) {
        this.b(constraintAnchor0, v, 0x80000000, false);
    }

    public final boolean b(ConstraintAnchor constraintAnchor0, int v, int v1, boolean z) {
        if(constraintAnchor0 == null) {
            this.j();
            return true;
        }
        if(!z && !this.i(constraintAnchor0)) {
            return false;
        }
        this.f = constraintAnchor0;
        if(constraintAnchor0.a == null) {
            constraintAnchor0.a = new HashSet();
        }
        HashSet hashSet0 = this.f.a;
        if(hashSet0 != null) {
            hashSet0.add(this);
        }
        this.g = v;
        this.h = v1;
        return true;
    }

    public final void c(int v, WidgetGroup widgetGroup0, ArrayList arrayList0) {
        HashSet hashSet0 = this.a;
        if(hashSet0 != null) {
            for(Object object0: hashSet0) {
                Grouping.a(((ConstraintAnchor)object0).d, v, arrayList0, widgetGroup0);
            }
        }
    }

    public final int d() {
        return this.c ? this.b : 0;
    }

    public final int e() {
        if(this.d.h0 == 8) {
            return 0;
        }
        return this.h == 0x80000000 || (this.f == null || this.f.d.h0 != 8) ? this.g : this.h;
    }

    public final ConstraintAnchor f() {
        Type constraintAnchor$Type0 = this.e;
        ConstraintWidget constraintWidget0 = this.d;
        switch(constraintAnchor$Type0.ordinal()) {
            case 1: {
                return constraintWidget0.K;
            }
            case 2: {
                return constraintWidget0.L;
            }
            case 3: {
                return constraintWidget0.I;
            }
            case 4: {
                return constraintWidget0.J;
            }
            case 0: 
            case 5: 
            case 6: 
            case 7: 
            case 8: {
                return null;
            }
            default: {
                throw new AssertionError(constraintAnchor$Type0.name());
            }
        }
    }

    public final boolean g() {
        HashSet hashSet0 = this.a;
        if(hashSet0 == null) {
            return false;
        }
        for(Object object0: hashSet0) {
            if(((ConstraintAnchor)object0).f().h()) {
                return true;
            }
            if(false) {
                break;
            }
        }
        return false;
    }

    public final boolean h() {
        return this.f != null;
    }

    public final boolean i(ConstraintAnchor constraintAnchor0) {
        if(constraintAnchor0 == null) {
            return false;
        }
        Type constraintAnchor$Type0 = Type.e;
        Type constraintAnchor$Type1 = this.e;
        ConstraintWidget constraintWidget0 = constraintAnchor0.d;
        Type constraintAnchor$Type2 = constraintAnchor0.e;
        if(constraintAnchor$Type2 == constraintAnchor$Type1) {
            return constraintAnchor$Type1 != constraintAnchor$Type0 || constraintWidget0.E && this.d.E;
        }
        Type constraintAnchor$Type3 = Type.c;
        Type constraintAnchor$Type4 = Type.a;
        Type constraintAnchor$Type5 = Type.h;
        Type constraintAnchor$Type6 = Type.g;
        switch(constraintAnchor$Type1.ordinal()) {
            case 1: 
            case 3: {
                boolean z = constraintAnchor$Type2 == constraintAnchor$Type4 || constraintAnchor$Type2 == constraintAnchor$Type3;
                return constraintWidget0 instanceof Guideline ? z || constraintAnchor$Type2 == constraintAnchor$Type6 : z;
            }
            case 2: 
            case 4: {
                boolean z1 = constraintAnchor$Type2 == Type.b || constraintAnchor$Type2 == Type.d;
                return constraintWidget0 instanceof Guideline ? z1 || constraintAnchor$Type2 == constraintAnchor$Type5 : z1;
            }
            case 5: {
                return constraintAnchor$Type2 != constraintAnchor$Type4 && constraintAnchor$Type2 != constraintAnchor$Type3;
            }
            case 6: {
                return constraintAnchor$Type2 != constraintAnchor$Type0 && constraintAnchor$Type2 != constraintAnchor$Type6 && constraintAnchor$Type2 != constraintAnchor$Type5;
            }
            case 0: 
            case 7: 
            case 8: {
                return false;
            }
            default: {
                throw new AssertionError(constraintAnchor$Type1.name());
            }
        }
    }

    public final void j() {
        ConstraintAnchor constraintAnchor0 = this.f;
        if(constraintAnchor0 != null) {
            HashSet hashSet0 = constraintAnchor0.a;
            if(hashSet0 != null) {
                hashSet0.remove(this);
                if(this.f.a.size() == 0) {
                    this.f.a = null;
                }
            }
        }
        this.a = null;
        this.f = null;
        this.g = 0;
        this.h = 0x80000000;
        this.c = false;
        this.b = 0;
    }

    public final void k() {
        SolverVariable solverVariable0 = this.i;
        if(solverVariable0 == null) {
            this.i = new SolverVariable(androidx.constraintlayout.core.SolverVariable.Type.a);
            return;
        }
        solverVariable0.c();
    }

    public final void l(int v) {
        this.b = v;
        this.c = true;
    }

    @Override
    public final String toString() {
        return this.d.i0 + ":" + this.e.toString();
    }
}


package androidx.constraintlayout.core.widgets;

import a.a;
import androidx.constraintlayout.core.ArrayRow;
import androidx.constraintlayout.core.LinearSystem;
import androidx.constraintlayout.core.SolverVariable;
import androidx.work.impl.model.c;

public class Barrier extends HelperWidget {
    public int s0;
    public boolean t0;
    public int u0;
    public boolean v0;

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final boolean B() {
        return this.v0;
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final boolean C() {
        return this.v0;
    }

    public final boolean T() {
        boolean z = true;
        for(int v1 = 0; true; ++v1) {
            int v2 = this.r0;
            if(v1 >= v2) {
                break;
            }
            ConstraintWidget constraintWidget0 = this.q0[v1];
            if(this.t0 || constraintWidget0.d()) {
                if((this.s0 == 0 || this.s0 == 1) && !constraintWidget0.B()) {
                    z = false;
                }
                else if((this.s0 == 2 || this.s0 == 3) && !constraintWidget0.C()) {
                    z = false;
                }
            }
        }
        if(z && v2 > 0) {
            int v3 = 0;
            boolean z1 = false;
            for(int v = 0; v < this.r0; ++v) {
                ConstraintWidget constraintWidget1 = this.q0[v];
                if(this.t0 || constraintWidget1.d()) {
                    Type constraintAnchor$Type0 = Type.d;
                    Type constraintAnchor$Type1 = Type.b;
                    Type constraintAnchor$Type2 = Type.c;
                    Type constraintAnchor$Type3 = Type.a;
                    if(!z1) {
                        int v4 = this.s0;
                        if(v4 == 0) {
                            v3 = constraintWidget1.j(constraintAnchor$Type3).d();
                        }
                        else if(v4 == 1) {
                            v3 = constraintWidget1.j(constraintAnchor$Type2).d();
                        }
                        else if(v4 == 2) {
                            v3 = constraintWidget1.j(constraintAnchor$Type1).d();
                        }
                        else if(v4 == 3) {
                            v3 = constraintWidget1.j(constraintAnchor$Type0).d();
                        }
                        z1 = true;
                    }
                    int v5 = this.s0;
                    if(v5 == 0) {
                        v3 = Math.min(v3, constraintWidget1.j(constraintAnchor$Type3).d());
                    }
                    else if(v5 == 1) {
                        v3 = Math.max(v3, constraintWidget1.j(constraintAnchor$Type2).d());
                    }
                    else if(v5 == 2) {
                        v3 = Math.min(v3, constraintWidget1.j(constraintAnchor$Type1).d());
                    }
                    else if(v5 == 3) {
                        v3 = Math.max(v3, constraintWidget1.j(constraintAnchor$Type0).d());
                    }
                }
            }
            int v6 = v3 + this.u0;
            if(this.s0 == 0 || this.s0 == 1) {
                this.K(v6, v6);
            }
            else {
                this.L(v6, v6);
            }
            this.v0 = true;
            return true;
        }
        return false;
    }

    public final int U() {
        switch(this.s0) {
            case 0: 
            case 1: {
                return 0;
            }
            case 2: 
            case 3: {
                return 1;
            }
            default: {
                return -1;
            }
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final void c(LinearSystem linearSystem0, boolean z) {
        boolean z1;
        ConstraintAnchor[] arr_constraintAnchor = this.Q;
        ConstraintAnchor constraintAnchor0 = this.I;
        arr_constraintAnchor[0] = constraintAnchor0;
        ConstraintAnchor constraintAnchor1 = this.J;
        arr_constraintAnchor[2] = constraintAnchor1;
        ConstraintAnchor constraintAnchor2 = this.K;
        arr_constraintAnchor[1] = constraintAnchor2;
        ConstraintAnchor constraintAnchor3 = this.L;
        arr_constraintAnchor[3] = constraintAnchor3;
        for(int v = 0; v < arr_constraintAnchor.length; ++v) {
            ConstraintAnchor constraintAnchor4 = arr_constraintAnchor[v];
            constraintAnchor4.i = linearSystem0.k(constraintAnchor4);
        }
        int v1 = this.s0;
        if(v1 >= 0 && v1 < 4) {
            ConstraintAnchor constraintAnchor5 = arr_constraintAnchor[v1];
            if(!this.v0) {
                this.T();
            }
            if(this.v0) {
                this.v0 = false;
                int v2 = this.s0;
                if(v2 == 0 || v2 == 1) {
                    linearSystem0.d(constraintAnchor0.i, this.Z);
                    linearSystem0.d(constraintAnchor2.i, this.Z);
                }
                else if(v2 == 2 || v2 == 3) {
                    linearSystem0.d(constraintAnchor1.i, this.a0);
                    linearSystem0.d(constraintAnchor3.i, this.a0);
                    return;
                }
                return;
            }
            for(int v3 = 0; true; ++v3) {
                z1 = false;
                if(v3 >= this.r0) {
                    break;
                }
                ConstraintWidget constraintWidget0 = this.q0[v3];
                if((this.t0 || constraintWidget0.d()) && ((this.s0 == 0 || this.s0 == 1) && constraintWidget0.T[0] == DimensionBehaviour.c && constraintWidget0.I.f != null && constraintWidget0.K.f != null || (this.s0 == 2 || this.s0 == 3) && constraintWidget0.T[1] == DimensionBehaviour.c && constraintWidget0.J.f != null && constraintWidget0.L.f != null)) {
                    z1 = true;
                    break;
                }
            }
            boolean z2 = constraintAnchor0.g() || constraintAnchor2.g();
            boolean z3 = constraintAnchor1.g() || constraintAnchor3.g();
            int v4 = z1 || (this.s0 != 0 || !z2) && (this.s0 != 2 || !z3) && (this.s0 != 1 || !z2) && (this.s0 != 3 || !z3) ? 4 : 5;
            for(int v5 = 0; v5 < this.r0; ++v5) {
                ConstraintWidget constraintWidget1 = this.q0[v5];
                if(this.t0 || constraintWidget1.d()) {
                    SolverVariable solverVariable0 = linearSystem0.k(constraintWidget1.Q[this.s0]);
                    int v6 = this.s0;
                    ConstraintAnchor constraintAnchor6 = constraintWidget1.Q[v6];
                    constraintAnchor6.i = solverVariable0;
                    int v7 = constraintAnchor6.f == null || constraintAnchor6.f.d != this ? 0 : constraintAnchor6.g;
                    if(v6 == 0 || v6 == 2) {
                        SolverVariable solverVariable3 = constraintAnchor5.i;
                        int v9 = this.u0 - v7;
                        ArrayRow arrayRow1 = linearSystem0.l();
                        SolverVariable solverVariable4 = linearSystem0.m();
                        solverVariable4.d = 0;
                        arrayRow1.d(solverVariable3, solverVariable0, solverVariable4, v9);
                        linearSystem0.c(arrayRow1);
                    }
                    else {
                        SolverVariable solverVariable1 = constraintAnchor5.i;
                        int v8 = this.u0 + v7;
                        ArrayRow arrayRow0 = linearSystem0.l();
                        SolverVariable solverVariable2 = linearSystem0.m();
                        solverVariable2.d = 0;
                        arrayRow0.c(solverVariable1, solverVariable0, solverVariable2, v8);
                        linearSystem0.c(arrayRow0);
                    }
                    linearSystem0.e(constraintAnchor5.i, solverVariable0, this.u0 + v7, v4);
                }
            }
            int v10 = this.s0;
            if(v10 == 0) {
                linearSystem0.e(constraintAnchor2.i, constraintAnchor0.i, 0, 8);
                linearSystem0.e(constraintAnchor0.i, this.U.K.i, 0, 4);
                linearSystem0.e(constraintAnchor0.i, this.U.I.i, 0, 0);
                return;
            }
            switch(v10) {
                case 1: {
                    linearSystem0.e(constraintAnchor0.i, constraintAnchor2.i, 0, 8);
                    linearSystem0.e(constraintAnchor0.i, this.U.I.i, 0, 4);
                    linearSystem0.e(constraintAnchor0.i, this.U.K.i, 0, 0);
                    return;
                }
                case 2: {
                    linearSystem0.e(constraintAnchor3.i, constraintAnchor1.i, 0, 8);
                    linearSystem0.e(constraintAnchor1.i, this.U.L.i, 0, 4);
                    linearSystem0.e(constraintAnchor1.i, this.U.J.i, 0, 0);
                    return;
                }
                case 3: {
                    linearSystem0.e(constraintAnchor1.i, constraintAnchor3.i, 0, 8);
                    linearSystem0.e(constraintAnchor1.i, this.U.J.i, 0, 4);
                    linearSystem0.e(constraintAnchor1.i, this.U.L.i, 0, 0);
                    break;
                }
            }
        }
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final boolean d() {
        return true;
    }

    @Override  // androidx.constraintlayout.core.widgets.ConstraintWidget
    public final String toString() {
        String s = a.s(new StringBuilder("[Barrier] "), this.i0, " {");
        for(int v = 0; v < this.r0; ++v) {
            ConstraintWidget constraintWidget0 = this.q0[v];
            if(v > 0) {
                s = s + ", ";
            }
            StringBuilder stringBuilder0 = c.n(s);
            stringBuilder0.append(constraintWidget0.i0);
            s = stringBuilder0.toString();
        }
        return s + "}";
    }
}


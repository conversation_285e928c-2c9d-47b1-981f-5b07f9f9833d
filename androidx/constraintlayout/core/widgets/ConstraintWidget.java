package androidx.constraintlayout.core.widgets;

import a.a;
import androidx.constraintlayout.core.ArrayRow;
import androidx.constraintlayout.core.Cache;
import androidx.constraintlayout.core.LinearSystem;
import androidx.constraintlayout.core.SolverVariable;
import androidx.constraintlayout.core.widgets.analyzer.ChainRun;
import androidx.constraintlayout.core.widgets.analyzer.DependencyNode;
import androidx.constraintlayout.core.widgets.analyzer.HorizontalWidgetRun;
import androidx.constraintlayout.core.widgets.analyzer.VerticalWidgetRun;
import androidx.work.impl.model.c;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;

public class ConstraintWidget {
    public static enum DimensionBehaviour {
        FIXED,
        WRAP_CONTENT,
        MATCH_CONSTRAINT,
        MATCH_PARENT;

    }

    public int A;
    public float B;
    public final int[] C;
    public float D;
    public boolean E;
    public boolean F;
    public int G;
    public int H;
    public final ConstraintAnchor I;
    public final ConstraintAnchor J;
    public final ConstraintAnchor K;
    public final ConstraintAnchor L;
    public final ConstraintAnchor M;
    public final ConstraintAnchor N;
    public final ConstraintAnchor O;
    public final ConstraintAnchor P;
    public final ConstraintAnchor[] Q;
    public final ArrayList R;
    public final boolean[] S;
    public final DimensionBehaviour[] T;
    public ConstraintWidget U;
    public int V;
    public int W;
    public float X;
    public int Y;
    public int Z;
    public boolean a;
    public int a0;
    public ChainRun b;
    public int b0;
    public ChainRun c;
    public int c0;
    public HorizontalWidgetRun d;
    public int d0;
    public VerticalWidgetRun e;
    public float e0;
    public final boolean[] f;
    public float f0;
    public boolean g;
    public Object g0;
    public int h;
    public int h0;
    public int i;
    public String i0;
    public String j;
    public int j0;
    public boolean k;
    public int k0;
    public boolean l;
    public final float[] l0;
    public boolean m;
    public final ConstraintWidget[] m0;
    public boolean n;
    public final ConstraintWidget[] n0;
    public int o;
    public int o0;
    public int p;
    public int p0;
    public int q;
    public int r;
    public int s;
    public final int[] t;
    public int u;
    public int v;
    public float w;
    public int x;
    public int y;
    public float z;

    public ConstraintWidget() {
        this.a = false;
        this.d = null;
        this.e = null;
        this.f = new boolean[]{true, true};
        this.g = true;
        this.h = -1;
        this.i = -1;
        new HashMap();
        this.k = false;
        this.l = false;
        this.m = false;
        this.n = false;
        this.o = -1;
        this.p = -1;
        this.q = 0;
        this.r = 0;
        this.s = 0;
        this.t = new int[2];
        this.u = 0;
        this.v = 0;
        this.w = 1.0f;
        this.x = 0;
        this.y = 0;
        this.z = 1.0f;
        this.A = -1;
        this.B = 1.0f;
        this.C = new int[]{0x7FFFFFFF, 0x7FFFFFFF};
        this.D = NaNf;
        this.E = false;
        this.F = false;
        this.G = 0;
        this.H = 0;
        ConstraintAnchor constraintAnchor0 = new ConstraintAnchor(this, Type.a);
        this.I = constraintAnchor0;
        ConstraintAnchor constraintAnchor1 = new ConstraintAnchor(this, Type.b);
        this.J = constraintAnchor1;
        ConstraintAnchor constraintAnchor2 = new ConstraintAnchor(this, Type.c);
        this.K = constraintAnchor2;
        ConstraintAnchor constraintAnchor3 = new ConstraintAnchor(this, Type.d);
        this.L = constraintAnchor3;
        ConstraintAnchor constraintAnchor4 = new ConstraintAnchor(this, Type.e);
        this.M = constraintAnchor4;
        ConstraintAnchor constraintAnchor5 = new ConstraintAnchor(this, Type.g);
        this.N = constraintAnchor5;
        ConstraintAnchor constraintAnchor6 = new ConstraintAnchor(this, Type.h);
        this.O = constraintAnchor6;
        ConstraintAnchor constraintAnchor7 = new ConstraintAnchor(this, Type.f);
        this.P = constraintAnchor7;
        this.Q = new ConstraintAnchor[]{constraintAnchor0, constraintAnchor2, constraintAnchor1, constraintAnchor3, constraintAnchor4, constraintAnchor7};
        ArrayList arrayList0 = new ArrayList();
        this.R = arrayList0;
        this.S = new boolean[2];
        this.T = new DimensionBehaviour[]{DimensionBehaviour.a, DimensionBehaviour.a};
        this.U = null;
        this.V = 0;
        this.W = 0;
        this.X = 0.0f;
        this.Y = -1;
        this.Z = 0;
        this.a0 = 0;
        this.b0 = 0;
        this.e0 = 0.5f;
        this.f0 = 0.5f;
        this.h0 = 0;
        this.i0 = null;
        this.j0 = 0;
        this.k0 = 0;
        this.l0 = new float[]{-1.0f, -1.0f};
        this.m0 = new ConstraintWidget[]{null, null};
        this.n0 = new ConstraintWidget[]{null, null};
        this.o0 = -1;
        this.p0 = -1;
        arrayList0.add(constraintAnchor0);
        arrayList0.add(constraintAnchor1);
        arrayList0.add(constraintAnchor2);
        arrayList0.add(constraintAnchor3);
        arrayList0.add(constraintAnchor5);
        arrayList0.add(constraintAnchor6);
        arrayList0.add(constraintAnchor7);
        arrayList0.add(constraintAnchor4);
    }

    public final boolean A() {
        return this.g && this.h0 != 8;
    }

    // 去混淆评级： 低(30)
    public boolean B() {
        return this.k || this.I.c && this.K.c;
    }

    // 去混淆评级： 低(30)
    public boolean C() {
        return this.l || this.J.c && this.L.c;
    }

    public void D() {
        this.I.j();
        this.J.j();
        this.K.j();
        this.L.j();
        this.M.j();
        this.N.j();
        this.O.j();
        this.P.j();
        this.U = null;
        this.D = NaNf;
        this.V = 0;
        this.W = 0;
        this.X = 0.0f;
        this.Y = -1;
        this.Z = 0;
        this.a0 = 0;
        this.b0 = 0;
        this.c0 = 0;
        this.d0 = 0;
        this.e0 = 0.5f;
        this.f0 = 0.5f;
        this.T[0] = DimensionBehaviour.a;
        this.T[1] = DimensionBehaviour.a;
        this.g0 = null;
        this.h0 = 0;
        this.j0 = 0;
        this.k0 = 0;
        this.l0[0] = -1.0f;
        this.l0[1] = -1.0f;
        this.o = -1;
        this.p = -1;
        this.C[0] = 0x7FFFFFFF;
        this.C[1] = 0x7FFFFFFF;
        this.r = 0;
        this.s = 0;
        this.w = 1.0f;
        this.z = 1.0f;
        this.v = 0x7FFFFFFF;
        this.y = 0x7FFFFFFF;
        this.u = 0;
        this.x = 0;
        this.A = -1;
        this.B = 1.0f;
        this.f[0] = true;
        this.f[1] = true;
        this.F = false;
        this.S[0] = false;
        this.S[1] = false;
        this.g = true;
        this.t[0] = 0;
        this.t[1] = 0;
        this.h = -1;
        this.i = -1;
    }

    public final void E() {
        ConstraintWidget constraintWidget0 = this.U;
        if(constraintWidget0 != null && constraintWidget0 instanceof ConstraintWidgetContainer) {
            ((ConstraintWidgetContainer)constraintWidget0).getClass();
        }
        ArrayList arrayList0 = this.R;
        int v = arrayList0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((ConstraintAnchor)arrayList0.get(v1)).j();
        }
    }

    public final void F() {
        this.k = false;
        this.l = false;
        this.m = false;
        this.n = false;
        ArrayList arrayList0 = this.R;
        int v = arrayList0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ConstraintAnchor constraintAnchor0 = (ConstraintAnchor)arrayList0.get(v1);
            constraintAnchor0.c = false;
            constraintAnchor0.b = 0;
        }
    }

    public void G(Cache cache0) {
        this.I.k();
        this.J.k();
        this.K.k();
        this.L.k();
        this.M.k();
        this.P.k();
        this.N.k();
        this.O.k();
    }

    public static void H(int v, int v1, String s, StringBuilder stringBuilder0) {
        if(v == v1) {
            return;
        }
        stringBuilder0.append(s);
        stringBuilder0.append(" :   ");
        stringBuilder0.append(v);
        stringBuilder0.append(",\n");
    }

    public static void I(StringBuilder stringBuilder0, String s, float f, float f1) {
        if(f == f1) {
            return;
        }
        stringBuilder0.append(s);
        stringBuilder0.append(" :   ");
        stringBuilder0.append(f);
        stringBuilder0.append(",\n");
    }

    public final void J(int v) {
        this.b0 = v;
        this.E = v > 0;
    }

    public final void K(int v, int v1) {
        if(this.k) {
            return;
        }
        this.I.l(v);
        this.K.l(v1);
        this.Z = v;
        this.V = v1 - v;
        this.k = true;
    }

    public final void L(int v, int v1) {
        if(this.l) {
            return;
        }
        this.J.l(v);
        this.L.l(v1);
        this.a0 = v;
        this.W = v1 - v;
        if(this.E) {
            this.M.l(v + this.b0);
        }
        this.l = true;
    }

    public final void M(int v) {
        this.W = v;
        int v1 = this.d0;
        if(v < v1) {
            this.W = v1;
        }
    }

    public final void N(DimensionBehaviour constraintWidget$DimensionBehaviour0) {
        this.T[0] = constraintWidget$DimensionBehaviour0;
    }

    public final void O(DimensionBehaviour constraintWidget$DimensionBehaviour0) {
        this.T[1] = constraintWidget$DimensionBehaviour0;
    }

    public final void P(int v) {
        this.V = v;
        int v1 = this.c0;
        if(v < v1) {
            this.V = v1;
        }
    }

    public void Q(boolean z, boolean z1) {
        int v = z & this.d.g;
        int v1 = z1 & this.e.g;
        int v2 = this.d.h.g;
        int v3 = this.e.h.g;
        int v4 = this.d.i.g;
        int v5 = this.e.i.g;
        if(v4 - v2 < 0 || v5 - v3 < 0 || (v2 == 0x80000000 || v2 == 0x7FFFFFFF) || (v3 == 0x80000000 || v3 == 0x7FFFFFFF) || (v4 == 0x80000000 || v4 == 0x7FFFFFFF) || (v5 == 0x80000000 || v5 == 0x7FFFFFFF)) {
            v4 = 0;
            v5 = 0;
            v2 = 0;
            v3 = 0;
        }
        int v6 = v4 - v2;
        int v7 = v5 - v3;
        if(v != 0) {
            this.Z = v2;
        }
        if(v1 != 0) {
            this.a0 = v3;
        }
        if(this.h0 == 8) {
            this.V = 0;
            this.W = 0;
            return;
        }
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.a;
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = this.T;
        if(v != 0) {
            if(arr_constraintWidget$DimensionBehaviour[0] == constraintWidget$DimensionBehaviour0) {
                int v8 = this.V;
                if(v6 < v8) {
                    v6 = v8;
                }
            }
            this.V = v6;
            int v9 = this.c0;
            if(v6 < v9) {
                this.V = v9;
            }
        }
        if(v1 != 0) {
            if(arr_constraintWidget$DimensionBehaviour[1] == constraintWidget$DimensionBehaviour0) {
                int v10 = this.W;
                if(v7 < v10) {
                    v7 = v10;
                }
            }
            this.W = v7;
            int v11 = this.d0;
            if(v7 < v11) {
                this.W = v11;
            }
        }
    }

    public void R(LinearSystem linearSystem0, boolean z) {
        linearSystem0.getClass();
        int v = LinearSystem.n(this.I);
        int v1 = LinearSystem.n(this.J);
        int v2 = LinearSystem.n(this.K);
        int v3 = LinearSystem.n(this.L);
        if(z) {
            HorizontalWidgetRun horizontalWidgetRun0 = this.d;
            if(horizontalWidgetRun0 != null) {
                DependencyNode dependencyNode0 = horizontalWidgetRun0.h;
                if(dependencyNode0.j) {
                    DependencyNode dependencyNode1 = horizontalWidgetRun0.i;
                    if(dependencyNode1.j) {
                        v = dependencyNode0.g;
                        v2 = dependencyNode1.g;
                    }
                }
            }
        }
        if(z) {
            VerticalWidgetRun verticalWidgetRun0 = this.e;
            if(verticalWidgetRun0 != null) {
                DependencyNode dependencyNode2 = verticalWidgetRun0.h;
                if(dependencyNode2.j) {
                    DependencyNode dependencyNode3 = verticalWidgetRun0.i;
                    if(dependencyNode3.j) {
                        v1 = dependencyNode2.g;
                        v3 = dependencyNode3.g;
                    }
                }
            }
        }
        if(v2 - v < 0 || v3 - v1 < 0 || (v == 0x80000000 || v == 0x7FFFFFFF) || (v1 == 0x80000000 || v1 == 0x7FFFFFFF) || (v2 == 0x80000000 || v2 == 0x7FFFFFFF) || (v3 == 0x80000000 || v3 == 0x7FFFFFFF)) {
            v = 0;
            v1 = 0;
            v2 = 0;
            v3 = 0;
        }
        int v4 = v2 - v;
        int v5 = v3 - v1;
        this.Z = v;
        this.a0 = v1;
        if(this.h0 == 8) {
            this.V = 0;
            this.W = 0;
            return;
        }
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = this.T;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = arr_constraintWidget$DimensionBehaviour[0];
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = DimensionBehaviour.a;
        if(constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour1) {
            int v6 = this.V;
            if(v4 < v6) {
                v4 = v6;
            }
        }
        if(arr_constraintWidget$DimensionBehaviour[1] == constraintWidget$DimensionBehaviour1) {
            int v7 = this.W;
            if(v5 < v7) {
                v5 = v7;
            }
        }
        this.V = v4;
        this.W = v5;
        int v8 = this.d0;
        if(v5 < v8) {
            this.W = v8;
        }
        int v9 = this.c0;
        if(v4 < v9) {
            this.V = v9;
        }
        int v10 = this.v;
        DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.c;
        if(v10 > 0 && constraintWidget$DimensionBehaviour0 == constraintWidget$DimensionBehaviour2) {
            this.V = Math.min(this.V, v10);
        }
        int v11 = this.y;
        if(v11 > 0 && arr_constraintWidget$DimensionBehaviour[1] == constraintWidget$DimensionBehaviour2) {
            this.W = Math.min(this.W, v11);
        }
        int v12 = this.V;
        if(v4 != v12) {
            this.h = v12;
        }
        int v13 = this.W;
        if(v5 != v13) {
            this.i = v13;
        }
    }

    public final void b(ConstraintWidgetContainer constraintWidgetContainer0, LinearSystem linearSystem0, HashSet hashSet0, int v, boolean z) {
        if(z) {
            if(!hashSet0.contains(this)) {
                return;
            }
            Optimizer.a(constraintWidgetContainer0, linearSystem0, this);
            hashSet0.remove(this);
            this.c(linearSystem0, constraintWidgetContainer0.W(0x40));
        }
        if(v == 0) {
            HashSet hashSet1 = this.I.a;
            if(hashSet1 != null) {
                for(Object object0: hashSet1) {
                    ((ConstraintAnchor)object0).d.b(constraintWidgetContainer0, linearSystem0, hashSet0, 0, true);
                }
            }
            HashSet hashSet2 = this.K.a;
            if(hashSet2 != null) {
                for(Object object1: hashSet2) {
                    ((ConstraintAnchor)object1).d.b(constraintWidgetContainer0, linearSystem0, hashSet0, 0, true);
                }
            }
        }
        else {
            HashSet hashSet3 = this.J.a;
            if(hashSet3 != null) {
                for(Object object2: hashSet3) {
                    ((ConstraintAnchor)object2).d.b(constraintWidgetContainer0, linearSystem0, hashSet0, v, true);
                }
            }
            HashSet hashSet4 = this.L.a;
            if(hashSet4 != null) {
                for(Object object3: hashSet4) {
                    ((ConstraintAnchor)object3).d.b(constraintWidgetContainer0, linearSystem0, hashSet0, v, true);
                }
            }
            HashSet hashSet5 = this.M.a;
            if(hashSet5 != null) {
                for(Object object4: hashSet5) {
                    ((ConstraintAnchor)object4).d.b(constraintWidgetContainer0, linearSystem0, hashSet0, v, true);
                }
            }
        }
    }

    public void c(LinearSystem linearSystem0, boolean z) {
        int v16;
        boolean z20;
        ConstraintAnchor constraintAnchor6;
        int[] arr_v1;
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour2;
        DimensionBehaviour constraintWidget$DimensionBehaviour4;
        int v14;
        boolean z13;
        int v13;
        int v12;
        int v11;
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour1;
        boolean z10;
        boolean z9;
        boolean z8;
        boolean z7;
        boolean z6;
        boolean[] arr_z2;
        boolean z4;
        boolean z3;
        ConstraintAnchor constraintAnchor0 = this.I;
        SolverVariable solverVariable0 = linearSystem0.k(constraintAnchor0);
        ConstraintAnchor constraintAnchor1 = this.K;
        SolverVariable solverVariable1 = linearSystem0.k(constraintAnchor1);
        ConstraintAnchor constraintAnchor2 = this.J;
        SolverVariable solverVariable2 = linearSystem0.k(constraintAnchor2);
        ConstraintAnchor constraintAnchor3 = this.L;
        SolverVariable solverVariable3 = linearSystem0.k(constraintAnchor3);
        ConstraintAnchor constraintAnchor4 = this.M;
        SolverVariable solverVariable4 = linearSystem0.k(constraintAnchor4);
        ConstraintWidget constraintWidget0 = this.U;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = DimensionBehaviour.b;
        if(constraintWidget0 == null) {
            z3 = false;
        label_27:
            z4 = false;
        }
        else {
            boolean z1 = constraintWidget0.T[0] == constraintWidget$DimensionBehaviour0;
            boolean z2 = constraintWidget0.T[1] == constraintWidget$DimensionBehaviour0;
            switch(this.q) {
                case 1: {
                    z4 = z1;
                    z3 = false;
                    break;
                }
                case 2: {
                    z3 = z2;
                    goto label_27;
                }
                case 3: {
                    z3 = false;
                    goto label_27;
                }
                default: {
                    z3 = z2;
                    z4 = z1;
                }
            }
        }
        boolean[] arr_z = this.S;
        if(this.h0 == 8) {
            ArrayList arrayList0 = this.R;
            int v = arrayList0.size();
            int v1 = 0;
            while(v1 < v) {
                HashSet hashSet0 = ((ConstraintAnchor)arrayList0.get(v1)).a;
                if(hashSet0 != null && hashSet0.size() > 0) {
                    goto label_40;
                }
                ++v1;
            }
            if(!arr_z[0] && !arr_z[1]) {
                return;
            }
        }
    label_40:
        boolean z5 = this.k;
        if(z5 || this.l) {
            if(z5) {
                linearSystem0.d(solverVariable0, this.Z);
                linearSystem0.d(solverVariable1, this.Z + this.V);
                if(z4) {
                    ConstraintWidget constraintWidget1 = this.U;
                    if(constraintWidget1 != null) {
                        WeakReference weakReference0 = ((ConstraintWidgetContainer)constraintWidget1).H0;
                        if(weakReference0 == null || weakReference0.get() == null || constraintAnchor0.d() > ((ConstraintAnchor)((ConstraintWidgetContainer)constraintWidget1).H0.get()).d()) {
                            ((ConstraintWidgetContainer)constraintWidget1).H0 = new WeakReference(constraintAnchor0);
                        }
                        WeakReference weakReference1 = ((ConstraintWidgetContainer)constraintWidget1).J0;
                        if(weakReference1 == null || weakReference1.get() == null || constraintAnchor1.d() > ((ConstraintAnchor)((ConstraintWidgetContainer)constraintWidget1).J0.get()).d()) {
                            ((ConstraintWidgetContainer)constraintWidget1).J0 = new WeakReference(constraintAnchor1);
                        }
                    }
                }
            }
            if(this.l) {
                linearSystem0.d(solverVariable2, this.a0);
                linearSystem0.d(solverVariable3, this.a0 + this.W);
                if(constraintAnchor4.a != null && constraintAnchor4.a.size() > 0) {
                    linearSystem0.d(solverVariable4, this.a0 + this.b0);
                }
                if(z3) {
                    ConstraintWidget constraintWidget2 = this.U;
                    if(constraintWidget2 != null) {
                        WeakReference weakReference2 = ((ConstraintWidgetContainer)constraintWidget2).G0;
                        if(weakReference2 == null || weakReference2.get() == null || constraintAnchor2.d() > ((ConstraintAnchor)((ConstraintWidgetContainer)constraintWidget2).G0.get()).d()) {
                            ((ConstraintWidgetContainer)constraintWidget2).G0 = new WeakReference(constraintAnchor2);
                        }
                        WeakReference weakReference3 = ((ConstraintWidgetContainer)constraintWidget2).I0;
                        if(weakReference3 == null || weakReference3.get() == null || constraintAnchor3.d() > ((ConstraintAnchor)((ConstraintWidgetContainer)constraintWidget2).I0.get()).d()) {
                            ((ConstraintWidgetContainer)constraintWidget2).I0 = new WeakReference(constraintAnchor3);
                        }
                    }
                }
            }
            if(this.k && this.l) {
                this.k = false;
                this.l = false;
                return;
            }
        }
        boolean[] arr_z1 = this.f;
        if(z) {
            HorizontalWidgetRun horizontalWidgetRun0 = this.d;
            if(horizontalWidgetRun0 == null) {
                arr_z2 = arr_z;
            }
            else {
                VerticalWidgetRun verticalWidgetRun0 = this.e;
                if(verticalWidgetRun0 == null) {
                    arr_z2 = arr_z;
                }
                else {
                    arr_z2 = arr_z;
                    DependencyNode dependencyNode0 = horizontalWidgetRun0.h;
                    if(dependencyNode0.j && horizontalWidgetRun0.i.j && verticalWidgetRun0.h.j && verticalWidgetRun0.i.j) {
                        linearSystem0.d(solverVariable0, dependencyNode0.g);
                        linearSystem0.d(solverVariable1, this.d.i.g);
                        linearSystem0.d(solverVariable2, this.e.h.g);
                        linearSystem0.d(solverVariable3, this.e.i.g);
                        linearSystem0.d(solverVariable4, this.e.k.g);
                        if(this.U != null) {
                            if(z4 && arr_z1[0] && !this.y()) {
                                linearSystem0.f(linearSystem0.k(this.U.K), solverVariable1, 0, 8);
                            }
                            if(z3 && arr_z1[1] && !this.z()) {
                                linearSystem0.f(linearSystem0.k(this.U.L), solverVariable3, 0, 8);
                            }
                        }
                        this.k = false;
                        this.l = false;
                        return;
                    }
                }
            }
        }
        else {
            arr_z2 = arr_z;
        }
        if(this.U == null) {
            z9 = false;
            z10 = false;
        }
        else {
            if(this.x(0)) {
                ((ConstraintWidgetContainer)this.U).T(0, this);
                z6 = true;
            }
            else {
                z6 = this.y();
            }
            if(this.x(1)) {
                ((ConstraintWidgetContainer)this.U).T(1, this);
                z7 = true;
            }
            else {
                z7 = this.z();
            }
            if(z6 || !z4 || this.h0 == 8 || constraintAnchor0.f != null || constraintAnchor1.f != null) {
                z8 = z6;
            }
            else {
                z8 = false;
                linearSystem0.f(linearSystem0.k(this.U.K), solverVariable1, 0, 1);
            }
            if(!z7 && z3 && this.h0 != 8 && constraintAnchor2.f == null && constraintAnchor3.f == null && constraintAnchor4 == null) {
                linearSystem0.f(linearSystem0.k(this.U.L), solverVariable3, 0, 1);
            }
            z9 = z7;
            z10 = z8;
        }
        int v2 = this.V;
        int v3 = v2 < this.c0 ? this.c0 : v2;
        int v4 = this.W;
        int v5 = v4 < this.d0 ? this.d0 : v4;
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = this.T;
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = arr_constraintWidget$DimensionBehaviour[0];
        DimensionBehaviour constraintWidget$DimensionBehaviour2 = DimensionBehaviour.c;
        boolean z11 = constraintWidget$DimensionBehaviour1 != constraintWidget$DimensionBehaviour2;
        DimensionBehaviour constraintWidget$DimensionBehaviour3 = arr_constraintWidget$DimensionBehaviour[1];
        boolean z12 = constraintWidget$DimensionBehaviour3 != constraintWidget$DimensionBehaviour2;
        int v6 = this.Y;
        this.A = v6;
        float f = this.X;
        this.B = f;
        int v7 = this.r;
        int v8 = this.s;
        if(f > 0.0f) {
            arr_constraintWidget$DimensionBehaviour1 = arr_constraintWidget$DimensionBehaviour;
            if(this.h0 == 8) {
                goto label_209;
            }
            else {
                if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2 && v7 == 0) {
                    v7 = 3;
                }
                if(constraintWidget$DimensionBehaviour3 == constraintWidget$DimensionBehaviour2 && v8 == 0) {
                    v8 = 3;
                }
                if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2 && constraintWidget$DimensionBehaviour3 == constraintWidget$DimensionBehaviour2 && v7 == 3 && v8 == 3) {
                    if(v6 == -1) {
                        if(z11 && !z12) {
                            this.A = 0;
                        }
                        else if(!z11 && z12) {
                            this.A = 1;
                            this.B = 1.0f / f;
                        }
                    }
                    if(this.A == 0 && (!constraintAnchor2.h() || !constraintAnchor3.h())) {
                        this.A = 1;
                    }
                    else if(this.A == 1 && (!constraintAnchor0.h() || !constraintAnchor1.h())) {
                        this.A = 0;
                    }
                    if(this.A == -1 && (!constraintAnchor2.h() || !constraintAnchor3.h() || !constraintAnchor0.h() || !constraintAnchor1.h())) {
                        if(constraintAnchor2.h() && constraintAnchor3.h()) {
                            this.A = 0;
                        }
                        else if(constraintAnchor0.h() && constraintAnchor1.h()) {
                            this.B = 1.0f / this.B;
                            this.A = 1;
                        }
                    }
                    if(this.A == -1) {
                        int v9 = this.u;
                        if(v9 > 0 && this.x == 0) {
                            this.A = 0;
                        }
                        else if(v9 != 0 || this.x <= 0) {
                        }
                        else {
                            this.B = 1.0f / this.B;
                            this.A = 1;
                        }
                    }
                    goto label_202;
                }
                else if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2 && v7 == 3) {
                    this.A = 0;
                    int v10 = (int)(f * ((float)v4));
                    if(constraintWidget$DimensionBehaviour3 == constraintWidget$DimensionBehaviour2) {
                        v11 = v10;
                        v14 = 3;
                        v12 = v8;
                        v13 = v5;
                        z13 = true;
                    }
                    else {
                        v11 = v10;
                        v12 = v8;
                        v13 = v5;
                        z13 = false;
                        v14 = 4;
                    }
                }
                else if(constraintWidget$DimensionBehaviour3 != constraintWidget$DimensionBehaviour2 || v8 != 3) {
                label_202:
                    v14 = v7;
                    v12 = v8;
                    v11 = v3;
                    v13 = v5;
                label_206:
                    z13 = true;
                }
                else {
                    this.A = 1;
                    if(v6 == -1) {
                        this.B = 1.0f / f;
                    }
                    v13 = (int)(this.B * ((float)v2));
                    v14 = v7;
                    if(constraintWidget$DimensionBehaviour1 == constraintWidget$DimensionBehaviour2) {
                        v12 = 3;
                        v11 = v3;
                        goto label_206;
                    }
                    else {
                        v11 = v3;
                        z13 = false;
                        v12 = 4;
                    }
                }
            }
        }
        else {
            arr_constraintWidget$DimensionBehaviour1 = arr_constraintWidget$DimensionBehaviour;
        label_209:
            v14 = v7;
            v12 = v8;
            v11 = v3;
            v13 = v5;
            z13 = false;
        }
        this.t[0] = v14;
        this.t[1] = v12;
        boolean z14 = z13 && (this.A == -1 || this.A == 0);
        boolean z15 = z13 && (this.A == -1 || this.A == 1);
        boolean z16 = arr_constraintWidget$DimensionBehaviour1[0] == constraintWidget$DimensionBehaviour0 && this instanceof ConstraintWidgetContainer;
        ConstraintAnchor constraintAnchor5 = this.P;
        boolean z17 = constraintAnchor5.h();
        boolean z18 = arr_z2[0];
        boolean z19 = arr_z2[1];
        int[] arr_v = this.C;
        if(this.o == 2 || this.k) {
            constraintWidget$DimensionBehaviour4 = constraintWidget$DimensionBehaviour2;
        label_245:
            arr_constraintWidget$DimensionBehaviour2 = arr_constraintWidget$DimensionBehaviour1;
            arr_v1 = arr_v;
            constraintAnchor6 = constraintAnchor5;
        }
        else if(z) {
            HorizontalWidgetRun horizontalWidgetRun1 = this.d;
            if(horizontalWidgetRun1 != null) {
                DependencyNode dependencyNode1 = horizontalWidgetRun1.h;
                if(dependencyNode1.j && horizontalWidgetRun1.i.j) {
                    linearSystem0.d(solverVariable0, dependencyNode1.g);
                    linearSystem0.d(solverVariable1, this.d.i.g);
                    if(this.U != null && z4 && arr_z1[0] && !this.y()) {
                        linearSystem0.f(linearSystem0.k(this.U.K), solverVariable1, 0, 8);
                    }
                    constraintWidget$DimensionBehaviour4 = constraintWidget$DimensionBehaviour2;
                    goto label_245;
                }
            }
            goto label_236;
        }
        else {
        label_236:
            SolverVariable solverVariable5 = this.U == null ? null : linearSystem0.k(this.U.K);
            SolverVariable solverVariable6 = this.U == null ? null : linearSystem0.k(this.U.I);
            constraintWidget$DimensionBehaviour4 = constraintWidget$DimensionBehaviour2;
            arr_constraintWidget$DimensionBehaviour2 = arr_constraintWidget$DimensionBehaviour1;
            arr_v1 = arr_v;
            constraintAnchor6 = constraintAnchor5;
            this.e(linearSystem0, true, z4, z3, arr_z1[0], solverVariable6, solverVariable5, arr_constraintWidget$DimensionBehaviour1[0], z16, this.I, this.K, this.Z, (z16 ? 0 : v11), this.c0, arr_v[0], this.e0, z14, arr_constraintWidget$DimensionBehaviour1[1] == constraintWidget$DimensionBehaviour2, z10, z9, z18, v14, v12, this.u, this.v, this.w, !z17);
        }
        if(z) {
            VerticalWidgetRun verticalWidgetRun1 = this.e;
            if(verticalWidgetRun1 == null) {
                z20 = true;
            }
            else {
                DependencyNode dependencyNode2 = verticalWidgetRun1.h;
                if(!dependencyNode2.j || !verticalWidgetRun1.i.j) {
                    z20 = true;
                }
                else {
                    linearSystem0.d(solverVariable2, dependencyNode2.g);
                    linearSystem0.d(solverVariable3, this.e.i.g);
                    linearSystem0.d(solverVariable4, this.e.k.g);
                    ConstraintWidget constraintWidget3 = this.U;
                    if(constraintWidget3 != null && !z9 && z3 && arr_z1[1]) {
                        linearSystem0.f(linearSystem0.k(constraintWidget3.L), solverVariable3, 0, 8);
                    }
                    z20 = false;
                }
            }
        }
        else {
            z20 = true;
        }
        if(this.p != 2 && z20 && !this.l) {
            boolean z21 = arr_constraintWidget$DimensionBehaviour2[1] == constraintWidget$DimensionBehaviour0 && this instanceof ConstraintWidgetContainer;
            if(z21) {
                v13 = 0;
            }
            SolverVariable solverVariable7 = this.U == null ? null : linearSystem0.k(this.U.L);
            SolverVariable solverVariable8 = this.U == null ? null : linearSystem0.k(this.U.J);
            int v15 = this.b0;
            if(v15 <= 0 && this.h0 != 8) {
            label_283:
                v16 = !z17;
            }
            else if(constraintAnchor4.f != null) {
                linearSystem0.e(solverVariable4, solverVariable2, v15, 8);
                linearSystem0.e(solverVariable4, linearSystem0.k(constraintAnchor4.f), constraintAnchor4.e(), 8);
                if(z3) {
                    linearSystem0.f(solverVariable7, linearSystem0.k(constraintAnchor3), 0, 5);
                }
                v16 = 0;
            }
            else {
                if(this.h0 == 8) {
                    linearSystem0.e(solverVariable4, solverVariable2, constraintAnchor4.e(), 8);
                }
                else {
                    linearSystem0.e(solverVariable4, solverVariable2, v15, 8);
                }
                goto label_283;
            }
            this.e(linearSystem0, false, z3, z4, arr_z1[1], solverVariable8, solverVariable7, arr_constraintWidget$DimensionBehaviour2[1], z21, this.J, this.L, this.a0, v13, this.d0, arr_v1[1], this.f0, z15, arr_constraintWidget$DimensionBehaviour2[0] == constraintWidget$DimensionBehaviour4, z9, z10, z19, v12, v14, this.x, this.y, this.z, ((boolean)v16));
        }
        if(z13) {
            if(this.A == 1) {
                float f1 = this.B;
                ArrayRow arrayRow0 = linearSystem0.l();
                arrayRow0.d.d(solverVariable3, -1.0f);
                arrayRow0.d.d(solverVariable2, 1.0f);
                arrayRow0.d.d(solverVariable1, f1);
                arrayRow0.d.d(solverVariable0, -f1);
                linearSystem0.c(arrayRow0);
            }
            else {
                float f2 = this.B;
                ArrayRow arrayRow1 = linearSystem0.l();
                arrayRow1.d.d(solverVariable1, -1.0f);
                arrayRow1.d.d(solverVariable0, 1.0f);
                arrayRow1.d.d(solverVariable3, f2);
                arrayRow1.d.d(solverVariable2, -f2);
                linearSystem0.c(arrayRow1);
            }
        }
        if(constraintAnchor6.h()) {
            ConstraintWidget constraintWidget4 = constraintAnchor6.f.d;
            float f3 = (float)Math.toRadians(this.D + 90.0f);
            int v17 = constraintAnchor6.e();
            SolverVariable solverVariable9 = linearSystem0.k(this.j(Type.a));
            SolverVariable solverVariable10 = linearSystem0.k(this.j(Type.b));
            SolverVariable solverVariable11 = linearSystem0.k(this.j(Type.c));
            SolverVariable solverVariable12 = linearSystem0.k(this.j(Type.d));
            SolverVariable solverVariable13 = linearSystem0.k(constraintWidget4.j(Type.a));
            SolverVariable solverVariable14 = linearSystem0.k(constraintWidget4.j(Type.b));
            SolverVariable solverVariable15 = linearSystem0.k(constraintWidget4.j(Type.c));
            SolverVariable solverVariable16 = linearSystem0.k(constraintWidget4.j(Type.d));
            ArrayRow arrayRow2 = linearSystem0.l();
            arrayRow2.d.d(solverVariable14, 0.5f);
            arrayRow2.d.d(solverVariable16, 0.5f);
            arrayRow2.d.d(solverVariable10, -0.5f);
            arrayRow2.d.d(solverVariable12, -0.5f);
            arrayRow2.b = -((float)(Math.sin(f3) * ((double)v17)));
            linearSystem0.c(arrayRow2);
            ArrayRow arrayRow3 = linearSystem0.l();
            arrayRow3.d.d(solverVariable13, 0.5f);
            arrayRow3.d.d(solverVariable15, 0.5f);
            arrayRow3.d.d(solverVariable9, -0.5f);
            arrayRow3.d.d(solverVariable11, -0.5f);
            arrayRow3.b = -((float)(Math.cos(f3) * ((double)v17)));
            linearSystem0.c(arrayRow3);
        }
        this.k = false;
        this.l = false;
    }

    public boolean d() {
        return this.h0 != 8;
    }

    public final void e(LinearSystem linearSystem0, boolean z, boolean z1, boolean z2, boolean z3, SolverVariable solverVariable0, SolverVariable solverVariable1, DimensionBehaviour constraintWidget$DimensionBehaviour0, boolean z4, ConstraintAnchor constraintAnchor0, ConstraintAnchor constraintAnchor1, int v, int v1, int v2, int v3, float f, boolean z5, boolean z6, boolean z7, boolean z8, boolean z9, int v4, int v5, int v6, int v7, float f1, boolean z10) {
        int v39;
        int v38;
        ConstraintWidget constraintWidget4;
        int v35;
        ConstraintWidget constraintWidget3;
        int v34;
        int v32;
        int v31;
        int v30;
        int v29;
        int v28;
        int v27;
        int v26;
        int v25;
        int v24;
        int v23;
        boolean z17;
        int v22;
        int v21;
        int v20;
        int v19;
        boolean z16;
        boolean z15;
        int v18;
        boolean z14;
        int v17;
        boolean z13;
        SolverVariable solverVariable7;
        SolverVariable solverVariable6;
        int v16;
        int v12;
        int v8 = v6;
        SolverVariable solverVariable2 = linearSystem0.k(constraintAnchor0);
        SolverVariable solverVariable3 = linearSystem0.k(constraintAnchor1);
        SolverVariable solverVariable4 = linearSystem0.k(constraintAnchor0.f);
        SolverVariable solverVariable5 = linearSystem0.k(constraintAnchor1.f);
        int v9 = constraintAnchor0.h();
        boolean z11 = constraintAnchor1.h();
        boolean z12 = this.P.h();
        int v10 = z11 ? v9 + 1 : v9;
        if(z12) {
            ++v10;
        }
        int v11 = z5 ? 3 : v4;
        if(constraintWidget$DimensionBehaviour0.ordinal() != 2) {
            v12 = 0;
        }
        else if(v11 != 4) {
            v12 = 1;
        }
        else {
            v12 = 0;
        }
        int v13 = this.h;
        int v14 = v12;
        if(v13 != -1 && z) {
            this.h = -1;
            v1 = v13;
            v14 = 0;
        }
        int v15 = this.i;
        if(v15 == -1 || z) {
            v15 = v1;
        }
        else {
            this.i = -1;
            v14 = 0;
        }
        if(this.h0 == 8) {
            v16 = 0;
            v14 = 0;
        }
        else {
            v16 = v15;
        }
        if(z10) {
            if(v9 == 0 && !z11 && !z12) {
                linearSystem0.d(solverVariable2, v);
            }
            else if(v9 != 0 && !z11) {
                linearSystem0.e(solverVariable2, solverVariable4, constraintAnchor0.e(), 8);
            }
        }
        if(v14 != 0) {
            if(v10 == 2 || z5 || v11 != 0 && v11 != 1) {
                if(v6 == -2) {
                    v6 = v16;
                }
                if(v7 == -2) {
                    v7 = v16;
                }
                if(v16 > 0 && v11 != 1) {
                    v16 = 0;
                }
                if(v6 > 0) {
                    linearSystem0.f(solverVariable3, solverVariable2, v6, 8);
                    v16 = Math.max(v16, v6);
                }
                if(v7 > 0) {
                    if(!z1 || v11 != 1) {
                        linearSystem0.g(solverVariable3, solverVariable2, v7, 8);
                    }
                    v16 = Math.min(v16, v7);
                }
                if(v11 == 1) {
                    if(z1) {
                        linearSystem0.e(solverVariable3, solverVariable2, v16, 8);
                    }
                    else {
                        if(!z7) {
                        }
                        linearSystem0.e(solverVariable3, solverVariable2, v16, 5);
                        linearSystem0.g(solverVariable3, solverVariable2, v16, 8);
                    }
                    z13 = z3;
                    v17 = v10;
                }
                else if(v11 == 2) {
                    Type constraintAnchor$Type0 = Type.b;
                    Type constraintAnchor$Type1 = Type.d;
                    if(constraintAnchor0.e == constraintAnchor$Type0 || constraintAnchor0.e == constraintAnchor$Type1) {
                        solverVariable6 = linearSystem0.k(this.U.j(constraintAnchor$Type0));
                        solverVariable7 = linearSystem0.k(this.U.j(constraintAnchor$Type1));
                    }
                    else {
                        solverVariable6 = linearSystem0.k(this.U.j(Type.a));
                        solverVariable7 = linearSystem0.k(this.U.j(Type.c));
                    }
                    ArrayRow arrayRow0 = linearSystem0.l();
                    v17 = v10;
                    arrayRow0.d.d(solverVariable3, -1.0f);
                    arrayRow0.d.d(solverVariable2, 1.0f);
                    arrayRow0.d.d(solverVariable7, f1);
                    arrayRow0.d.d(solverVariable6, -f1);
                    linearSystem0.c(arrayRow0);
                    if(z1) {
                        v14 = 0;
                    }
                    z13 = z3;
                }
                else {
                    v17 = v10;
                    z13 = true;
                }
            }
            else {
                linearSystem0.e(solverVariable3, solverVariable2, (v7 <= 0 ? Math.max(v6, v16) : Math.min(v7, Math.max(v6, v16))), 8);
                z13 = z3;
                v17 = v10;
                v14 = 0;
            }
        }
        else if(z4) {
            linearSystem0.e(solverVariable3, solverVariable2, 0, 3);
            if(v2 > 0) {
                linearSystem0.f(solverVariable3, solverVariable2, v2, 8);
            }
            if(v3 < 0x7FFFFFFF) {
                linearSystem0.g(solverVariable3, solverVariable2, v3, 8);
            }
            z13 = z3;
            v17 = v10;
        }
        else {
            linearSystem0.e(solverVariable3, solverVariable2, v16, 8);
            z13 = z3;
            v17 = v10;
        }
        if(z10) {
            if(z7) {
                z14 = z13;
                v18 = v17;
                goto label_353;
            }
            if(v9 == 0 && !z11 && !z12) {
                z15 = z13;
                v19 = 5;
                v20 = 0;
                z16 = z1;
            }
            else if(v9 != 0 && !z11) {
                z16 = z1;
                v19 = !z1 || !(constraintAnchor0.f.d instanceof Barrier) ? 5 : 8;
                z15 = z13;
                v20 = 0;
            }
            else if(v9 == 0 && z11) {
                linearSystem0.e(solverVariable3, solverVariable5, -constraintAnchor1.e(), 8);
                if(z1) {
                    v19 = 5;
                    linearSystem0.f(solverVariable2, solverVariable0, 0, 5);
                    z15 = z13;
                }
                else {
                    z15 = z13;
                    v19 = 5;
                }
                v20 = 0;
                z16 = z1;
            }
            else if(v9 == 0 || !z11) {
                z15 = z13;
                v19 = 5;
                v20 = 0;
                z16 = z1;
            }
            else {
                ConstraintWidget constraintWidget0 = constraintAnchor0.f.d;
                ConstraintWidget constraintWidget1 = constraintAnchor1.f.d;
                ConstraintWidget constraintWidget2 = this.U;
                if(v14 == 0) {
                    v30 = v11;
                    if(solverVariable4.f && solverVariable5.f) {
                        linearSystem0.b(solverVariable2, solverVariable4, constraintAnchor0.e(), f, solverVariable5, solverVariable3, constraintAnchor1.e(), 8);
                        if(z1 && z13) {
                            int v33 = constraintAnchor1.f == null ? 0 : constraintAnchor1.e();
                            if(solverVariable5 != solverVariable1) {
                                linearSystem0.f(solverVariable1, solverVariable3, v33, 5);
                            }
                        }
                        return;
                    }
                    z17 = true;
                    v28 = 1;
                    v29 = 0;
                label_263:
                    v27 = 6;
                    v26 = 4;
                    v25 = 5;
                }
                else if(v11 == 0) {
                    if(v7 != 0 || v8 != 0) {
                        v21 = 1;
                        v22 = 0;
                        z17 = true;
                        v23 = 5;
                        v24 = 5;
                    }
                    else {
                        if(solverVariable4.f && solverVariable5.f) {
                            linearSystem0.e(solverVariable2, solverVariable4, constraintAnchor0.e(), 8);
                            linearSystem0.e(solverVariable3, solverVariable5, -constraintAnchor1.e(), 8);
                            return;
                        }
                        v21 = 0;
                        v22 = 1;
                        z17 = false;
                        v23 = 8;
                        v24 = 8;
                    }
                    if(constraintWidget0 instanceof Barrier || constraintWidget1 instanceof Barrier) {
                        v29 = v22;
                        v25 = v23;
                        v27 = 6;
                        v26 = 4;
                        v30 = 0;
                        v28 = v21;
                    }
                    else {
                        v25 = v23;
                        v26 = v24;
                        v27 = 6;
                        v28 = v21;
                        v29 = v22;
                        v30 = 0;
                    }
                }
                else {
                    v30 = 3;
                    switch(v11) {
                        case 1: {
                            v30 = 1;
                            z17 = true;
                            v28 = 1;
                            v29 = 0;
                            v27 = 6;
                            v26 = 4;
                            v25 = 8;
                            break;
                        }
                        case 2: {
                            if(constraintWidget0 instanceof Barrier || constraintWidget1 instanceof Barrier) {
                                v30 = 2;
                                z17 = true;
                                v28 = 1;
                                v29 = 0;
                                v27 = 6;
                                v26 = 4;
                            }
                            else {
                                v30 = 2;
                                z17 = true;
                                v28 = 1;
                                v29 = 0;
                                v27 = 6;
                                v26 = 5;
                            }
                            v25 = 5;
                            break;
                        }
                        case 3: {
                            if(this.A == -1) {
                                if(z8) {
                                    z17 = true;
                                    v28 = 1;
                                    v29 = 1;
                                    v27 = z1 ? 5 : 4;
                                }
                                else {
                                    z17 = true;
                                    v28 = 1;
                                    v29 = 1;
                                    v27 = 8;
                                }
                                v26 = 5;
                                v25 = 8;
                            }
                            else if(z5) {
                                if(v5 == 1 || v5 == 2) {
                                    v31 = 5;
                                    v32 = 4;
                                }
                                else {
                                    v31 = 8;
                                    v32 = 5;
                                }
                                v25 = v31;
                                v26 = v32;
                                z17 = true;
                                v28 = 1;
                                v29 = 1;
                                v27 = 6;
                            }
                            else if(v7 > 0) {
                                z17 = true;
                                v28 = 1;
                                v29 = 1;
                                v27 = 6;
                                v26 = 5;
                                v25 = 5;
                                break;
                            }
                            else if(v7 != 0 || v8 != 0) {
                                z17 = true;
                                v28 = 1;
                                v29 = 1;
                                goto label_263;
                            }
                            else if(!z8) {
                                z17 = true;
                                v28 = 1;
                                v29 = 1;
                                v27 = 6;
                                v26 = 8;
                                v25 = 5;
                            }
                            else {
                                v25 = constraintWidget0 == constraintWidget2 || constraintWidget1 == constraintWidget2 ? 5 : 4;
                                z17 = true;
                                v28 = 1;
                                v29 = 1;
                                v27 = 6;
                                v26 = 4;
                            }
                            break;
                        }
                        default: {
                            v30 = v11;
                            z17 = false;
                            v28 = 0;
                            v29 = 0;
                            goto label_263;
                        }
                    }
                }
                if(!z17 || solverVariable4 != solverVariable5 || constraintWidget0 == constraintWidget2) {
                    v34 = 1;
                }
                else {
                    z17 = false;
                    v34 = 0;
                }
                if(v28 == 0) {
                    constraintWidget3 = constraintWidget0;
                    z15 = z13;
                    v35 = v8;
                    constraintWidget4 = constraintWidget1;
                    z16 = z1;
                }
                else {
                    if(v14 != 0 || z6 || z8 || solverVariable4 != solverVariable0 || solverVariable5 != solverVariable1) {
                        z16 = z1;
                    }
                    else {
                        z16 = false;
                        v27 = 8;
                        v25 = 8;
                        v34 = 0;
                    }
                    constraintWidget3 = constraintWidget0;
                    z15 = z13;
                    v35 = v8;
                    constraintWidget4 = constraintWidget1;
                    linearSystem0.b(solverVariable2, solverVariable4, constraintAnchor0.e(), f, solverVariable5, solverVariable3, constraintAnchor1.e(), v27);
                }
                int v36 = v34;
                if(this.h0 == 8 && (constraintAnchor1.a == null || constraintAnchor1.a.size() <= 0)) {
                    return;
                }
                if(z17) {
                    int v37 = !z16 || solverVariable4 == solverVariable5 || v14 != 0 || !(constraintWidget3 instanceof Barrier) && !(constraintWidget4 instanceof Barrier) ? v25 : 6;
                    linearSystem0.f(solverVariable2, solverVariable4, constraintAnchor0.e(), v37);
                    linearSystem0.g(solverVariable3, solverVariable5, -constraintAnchor1.e(), v37);
                    v25 = v37;
                }
                if(!z16 || !z9 || constraintWidget3 instanceof Barrier || constraintWidget4 instanceof Barrier || constraintWidget4 == constraintWidget2) {
                    v38 = v26;
                    v39 = v25;
                }
                else {
                    v38 = 6;
                    v39 = 6;
                    v36 = 1;
                }
                if(v36 != 0) {
                    if(v29 != 0 && (!z8 || z2)) {
                        int v40 = constraintWidget3 == constraintWidget2 || constraintWidget4 == constraintWidget2 ? 6 : v38;
                        if(constraintWidget3 instanceof Guideline || constraintWidget4 instanceof Guideline) {
                            v40 = 5;
                        }
                        if(constraintWidget3 instanceof Barrier || constraintWidget4 instanceof Barrier) {
                            v40 = 5;
                        }
                        if(z8) {
                            v40 = 5;
                        }
                        v38 = Math.max(v40, v38);
                    }
                    int v41 = v38;
                    if(z16) {
                        v41 = Math.min(v39, v41);
                        if(z5 && !z8 && (constraintWidget3 == constraintWidget2 || constraintWidget4 == constraintWidget2)) {
                            v41 = 4;
                        }
                    }
                    linearSystem0.e(solverVariable2, solverVariable4, constraintAnchor0.e(), v41);
                    linearSystem0.e(solverVariable3, solverVariable5, -constraintAnchor1.e(), v41);
                }
                if(z16) {
                    int v42 = solverVariable0 == solverVariable4 ? constraintAnchor0.e() : 0;
                    if(solverVariable4 == solverVariable0) {
                        v19 = 5;
                    }
                    else {
                        v19 = 5;
                        linearSystem0.f(solverVariable2, solverVariable0, v42, 5);
                    }
                }
                else {
                    v19 = 5;
                }
                if(!z16 || v14 == 0 || v2 != 0 || v35 != 0) {
                    v20 = 0;
                }
                else if(v14 != 0 && v30 == 3) {
                    v20 = 0;
                    linearSystem0.f(solverVariable3, solverVariable2, 0, 8);
                }
                else {
                    v20 = 0;
                    linearSystem0.f(solverVariable3, solverVariable2, 0, 5);
                }
            }
            if(z16 && z15) {
                if(constraintAnchor1.f != null) {
                    v20 = constraintAnchor1.e();
                }
                if(solverVariable5 != solverVariable1) {
                    linearSystem0.f(solverVariable1, solverVariable3, v20, v19);
                }
            }
            return;
        }
        else {
            z14 = z13;
            v18 = v17;
        }
    label_353:
        if(v18 < 2 && z1 && z14) {
            linearSystem0.f(solverVariable2, solverVariable0, 0, 8);
            ConstraintAnchor constraintAnchor2 = this.M;
            boolean z18 = z || constraintAnchor2.f == null;
            if(z) {
            label_363:
                if(z18) {
                    linearSystem0.f(solverVariable1, solverVariable3, 0, 8);
                }
            }
            else {
                ConstraintAnchor constraintAnchor3 = constraintAnchor2.f;
                if(constraintAnchor3 == null) {
                    goto label_363;
                }
                else if(constraintAnchor3.d.X != 0.0f && (constraintAnchor3.d.T[0] == DimensionBehaviour.c && constraintAnchor3.d.T[1] == DimensionBehaviour.c)) {
                    linearSystem0.f(solverVariable1, solverVariable3, 0, 8);
                }
            }
        }
    }

    public final void f(Type constraintAnchor$Type0, ConstraintWidget constraintWidget0, Type constraintAnchor$Type1, int v) {
        boolean z1;
        Type constraintAnchor$Type2 = Type.f;
        Type constraintAnchor$Type3 = Type.h;
        Type constraintAnchor$Type4 = Type.g;
        Type constraintAnchor$Type5 = Type.a;
        Type constraintAnchor$Type6 = Type.b;
        Type constraintAnchor$Type7 = Type.c;
        Type constraintAnchor$Type8 = Type.d;
        if(constraintAnchor$Type0 != constraintAnchor$Type2) {
            if(constraintAnchor$Type0 == constraintAnchor$Type4 && (constraintAnchor$Type1 == constraintAnchor$Type5 || constraintAnchor$Type1 == constraintAnchor$Type7)) {
                ConstraintAnchor constraintAnchor4 = this.j(constraintAnchor$Type5);
                ConstraintAnchor constraintAnchor5 = constraintWidget0.j(constraintAnchor$Type1);
                ConstraintAnchor constraintAnchor6 = this.j(constraintAnchor$Type7);
                constraintAnchor4.a(constraintAnchor5, 0);
                constraintAnchor6.a(constraintAnchor5, 0);
                this.j(constraintAnchor$Type4).a(constraintAnchor5, 0);
                return;
            }
            if(constraintAnchor$Type0 == constraintAnchor$Type3 && (constraintAnchor$Type1 == constraintAnchor$Type6 || constraintAnchor$Type1 == constraintAnchor$Type8)) {
                ConstraintAnchor constraintAnchor7 = constraintWidget0.j(constraintAnchor$Type1);
                this.j(constraintAnchor$Type6).a(constraintAnchor7, 0);
                this.j(constraintAnchor$Type8).a(constraintAnchor7, 0);
                this.j(constraintAnchor$Type3).a(constraintAnchor7, 0);
                return;
            }
            if(constraintAnchor$Type0 == constraintAnchor$Type4 && constraintAnchor$Type1 == constraintAnchor$Type4) {
                this.j(constraintAnchor$Type5).a(constraintWidget0.j(constraintAnchor$Type5), 0);
                this.j(constraintAnchor$Type7).a(constraintWidget0.j(constraintAnchor$Type7), 0);
                this.j(constraintAnchor$Type4).a(constraintWidget0.j(constraintAnchor$Type1), 0);
                return;
            }
            if(constraintAnchor$Type0 == constraintAnchor$Type3 && constraintAnchor$Type1 == constraintAnchor$Type3) {
                this.j(constraintAnchor$Type6).a(constraintWidget0.j(constraintAnchor$Type6), 0);
                this.j(constraintAnchor$Type8).a(constraintWidget0.j(constraintAnchor$Type8), 0);
                this.j(constraintAnchor$Type3).a(constraintWidget0.j(constraintAnchor$Type1), 0);
                return;
            }
            ConstraintAnchor constraintAnchor8 = this.j(constraintAnchor$Type0);
            ConstraintAnchor constraintAnchor9 = constraintWidget0.j(constraintAnchor$Type1);
            if(constraintAnchor8.i(constraintAnchor9)) {
                Type constraintAnchor$Type9 = Type.e;
                if(constraintAnchor$Type0 == constraintAnchor$Type9) {
                    ConstraintAnchor constraintAnchor10 = this.j(constraintAnchor$Type6);
                    ConstraintAnchor constraintAnchor11 = this.j(constraintAnchor$Type8);
                    if(constraintAnchor10 != null) {
                        constraintAnchor10.j();
                    }
                    if(constraintAnchor11 != null) {
                        constraintAnchor11.j();
                    }
                }
                else if(constraintAnchor$Type0 == constraintAnchor$Type6 || constraintAnchor$Type0 == constraintAnchor$Type8) {
                    ConstraintAnchor constraintAnchor15 = this.j(constraintAnchor$Type9);
                    if(constraintAnchor15 != null) {
                        constraintAnchor15.j();
                    }
                    ConstraintAnchor constraintAnchor16 = this.j(constraintAnchor$Type2);
                    if(constraintAnchor16.f != constraintAnchor9) {
                        constraintAnchor16.j();
                    }
                    ConstraintAnchor constraintAnchor17 = this.j(constraintAnchor$Type0).f();
                    ConstraintAnchor constraintAnchor18 = this.j(constraintAnchor$Type3);
                    if(constraintAnchor18.h()) {
                        constraintAnchor17.j();
                        constraintAnchor18.j();
                    }
                }
                else if(constraintAnchor$Type0 == constraintAnchor$Type5 || constraintAnchor$Type0 == constraintAnchor$Type7) {
                    ConstraintAnchor constraintAnchor12 = this.j(constraintAnchor$Type2);
                    if(constraintAnchor12.f != constraintAnchor9) {
                        constraintAnchor12.j();
                    }
                    ConstraintAnchor constraintAnchor13 = this.j(constraintAnchor$Type0).f();
                    ConstraintAnchor constraintAnchor14 = this.j(constraintAnchor$Type4);
                    if(constraintAnchor14.h()) {
                        constraintAnchor13.j();
                        constraintAnchor14.j();
                    }
                }
                constraintAnchor8.a(constraintAnchor9, v);
            }
        }
        else if(constraintAnchor$Type1 == constraintAnchor$Type2) {
            ConstraintAnchor constraintAnchor0 = this.j(constraintAnchor$Type5);
            ConstraintAnchor constraintAnchor1 = this.j(constraintAnchor$Type7);
            ConstraintAnchor constraintAnchor2 = this.j(constraintAnchor$Type6);
            ConstraintAnchor constraintAnchor3 = this.j(constraintAnchor$Type8);
            boolean z = true;
            if((constraintAnchor0 == null || !constraintAnchor0.h()) && (constraintAnchor1 == null || !constraintAnchor1.h())) {
                this.f(constraintAnchor$Type5, constraintWidget0, constraintAnchor$Type5, 0);
                this.f(constraintAnchor$Type7, constraintWidget0, constraintAnchor$Type7, 0);
                z1 = true;
            }
            else {
                z1 = false;
            }
            if((constraintAnchor2 == null || !constraintAnchor2.h()) && (constraintAnchor3 == null || !constraintAnchor3.h())) {
                this.f(constraintAnchor$Type6, constraintWidget0, constraintAnchor$Type6, 0);
                this.f(constraintAnchor$Type8, constraintWidget0, constraintAnchor$Type8, 0);
            }
            else {
                z = false;
            }
            if(z1 && z) {
                this.j(constraintAnchor$Type2).a(constraintWidget0.j(constraintAnchor$Type2), 0);
                return;
            }
            if(z1) {
                this.j(constraintAnchor$Type4).a(constraintWidget0.j(constraintAnchor$Type4), 0);
                return;
            }
            if(z) {
                this.j(constraintAnchor$Type3).a(constraintWidget0.j(constraintAnchor$Type3), 0);
            }
        }
        else if(constraintAnchor$Type1 == constraintAnchor$Type5 || constraintAnchor$Type1 == constraintAnchor$Type7) {
            this.f(constraintAnchor$Type5, constraintWidget0, constraintAnchor$Type1, 0);
            this.f(constraintAnchor$Type7, constraintWidget0, constraintAnchor$Type1, 0);
            this.j(constraintAnchor$Type2).a(constraintWidget0.j(constraintAnchor$Type1), 0);
        }
        else if(constraintAnchor$Type1 == constraintAnchor$Type6 || constraintAnchor$Type1 == constraintAnchor$Type8) {
            this.f(constraintAnchor$Type6, constraintWidget0, constraintAnchor$Type1, 0);
            this.f(constraintAnchor$Type8, constraintWidget0, constraintAnchor$Type1, 0);
            this.j(constraintAnchor$Type2).a(constraintWidget0.j(constraintAnchor$Type1), 0);
        }
    }

    public final void g(ConstraintAnchor constraintAnchor0, ConstraintAnchor constraintAnchor1, int v) {
        if(constraintAnchor0.d == this) {
            this.f(constraintAnchor0.e, constraintAnchor1.d, constraintAnchor1.e, v);
        }
    }

    public final void h(LinearSystem linearSystem0) {
        linearSystem0.k(this.I);
        linearSystem0.k(this.J);
        linearSystem0.k(this.K);
        linearSystem0.k(this.L);
        if(this.b0 > 0) {
            linearSystem0.k(this.M);
        }
    }

    public final void i() {
        if(this.d == null) {
            this.d = new HorizontalWidgetRun(this);
        }
        if(this.e == null) {
            this.e = new VerticalWidgetRun(this);
        }
    }

    public ConstraintAnchor j(Type constraintAnchor$Type0) {
        switch(constraintAnchor$Type0.ordinal()) {
            case 0: {
                return null;
            }
            case 1: {
                return this.I;
            }
            case 2: {
                return this.J;
            }
            case 3: {
                return this.K;
            }
            case 4: {
                return this.L;
            }
            case 5: {
                return this.M;
            }
            case 6: {
                return this.P;
            }
            case 7: {
                return this.N;
            }
            case 8: {
                return this.O;
            }
            default: {
                throw new AssertionError(constraintAnchor$Type0.name());
            }
        }
    }

    public final DimensionBehaviour k(int v) {
        DimensionBehaviour[] arr_constraintWidget$DimensionBehaviour = this.T;
        if(v == 0) {
            return arr_constraintWidget$DimensionBehaviour[0];
        }
        return v == 1 ? arr_constraintWidget$DimensionBehaviour[1] : null;
    }

    public final int l() {
        return this.h0 == 8 ? 0 : this.W;
    }

    public final ConstraintWidget m(int v) {
        if(v == 0) {
            ConstraintAnchor constraintAnchor0 = this.K.f;
            return constraintAnchor0 == null || constraintAnchor0.f != this.K ? null : constraintAnchor0.d;
        }
        if(v == 1) {
            ConstraintAnchor constraintAnchor1 = this.L.f;
            return constraintAnchor1 == null || constraintAnchor1.f != this.L ? null : constraintAnchor1.d;
        }
        return null;
    }

    public final ConstraintWidget n(int v) {
        if(v == 0) {
            ConstraintAnchor constraintAnchor0 = this.I.f;
            return constraintAnchor0 == null || constraintAnchor0.f != this.I ? null : constraintAnchor0.d;
        }
        if(v == 1) {
            ConstraintAnchor constraintAnchor1 = this.J.f;
            return constraintAnchor1 == null || constraintAnchor1.f != this.J ? null : constraintAnchor1.d;
        }
        return null;
    }

    public void o(StringBuilder stringBuilder0) {
        stringBuilder0.append("  " + this.j + ":{\n");
        stringBuilder0.append("    actualWidth:" + this.V);
        stringBuilder0.append("\n");
        stringBuilder0.append("    actualHeight:" + this.W);
        stringBuilder0.append("\n");
        stringBuilder0.append("    actualLeft:" + this.Z);
        stringBuilder0.append("\n");
        stringBuilder0.append("    actualTop:" + this.a0);
        stringBuilder0.append("\n");
        ConstraintWidget.q(stringBuilder0, "left", this.I);
        ConstraintWidget.q(stringBuilder0, "top", this.J);
        ConstraintWidget.q(stringBuilder0, "right", this.K);
        ConstraintWidget.q(stringBuilder0, "bottom", this.L);
        ConstraintWidget.q(stringBuilder0, "baseline", this.M);
        ConstraintWidget.q(stringBuilder0, "centerX", this.N);
        ConstraintWidget.q(stringBuilder0, "centerY", this.O);
        int v = this.V;
        int v1 = this.c0;
        int v2 = this.C[0];
        int v3 = this.u;
        int v4 = this.r;
        float f = this.w;
        DimensionBehaviour constraintWidget$DimensionBehaviour0 = this.T[0];
        float f1 = this.l0[0];
        ConstraintWidget.p(stringBuilder0, "    width", v, v1, v2, v3, v4, f, constraintWidget$DimensionBehaviour0);
        int v5 = this.W;
        int v6 = this.d0;
        int v7 = this.C[1];
        int v8 = this.x;
        int v9 = this.s;
        float f2 = this.z;
        DimensionBehaviour constraintWidget$DimensionBehaviour1 = this.T[1];
        float f3 = this.l0[1];
        ConstraintWidget.p(stringBuilder0, "    height", v5, v6, v7, v8, v9, f2, constraintWidget$DimensionBehaviour1);
        float f4 = this.X;
        int v10 = this.Y;
        if(f4 != 0.0f) {
            stringBuilder0.append("    dimensionRatio");
            stringBuilder0.append(" :  [");
            stringBuilder0.append(f4);
            stringBuilder0.append(",");
            stringBuilder0.append(v10);
            stringBuilder0.append("");
            stringBuilder0.append("],\n");
        }
        ConstraintWidget.I(stringBuilder0, "    horizontalBias", this.e0, 0.5f);
        ConstraintWidget.I(stringBuilder0, "    verticalBias", this.f0, 0.5f);
        ConstraintWidget.H(this.j0, 0, "    horizontalChainStyle", stringBuilder0);
        ConstraintWidget.H(this.k0, 0, "    verticalChainStyle", stringBuilder0);
        stringBuilder0.append("  }");
    }

    public static void p(StringBuilder stringBuilder0, String s, int v, int v1, int v2, int v3, int v4, float f, DimensionBehaviour constraintWidget$DimensionBehaviour0) {
        stringBuilder0.append(s);
        stringBuilder0.append(" :  {\n");
        String s1 = constraintWidget$DimensionBehaviour0.toString();
        if(!"FIXED".equals(s1)) {
            stringBuilder0.append("      behavior");
            stringBuilder0.append(" :   ");
            stringBuilder0.append(s1);
            stringBuilder0.append(",\n");
        }
        ConstraintWidget.H(v, 0, "      size", stringBuilder0);
        ConstraintWidget.H(v1, 0, "      min", stringBuilder0);
        ConstraintWidget.H(v2, 0x7FFFFFFF, "      max", stringBuilder0);
        ConstraintWidget.H(v3, 0, "      matchMin", stringBuilder0);
        ConstraintWidget.H(v4, 0, "      matchDef", stringBuilder0);
        ConstraintWidget.I(stringBuilder0, "      matchPercent", f, 1.0f);
        stringBuilder0.append("    },\n");
    }

    public static void q(StringBuilder stringBuilder0, String s, ConstraintAnchor constraintAnchor0) {
        if(constraintAnchor0.f == null) {
            return;
        }
        stringBuilder0.append("    ");
        stringBuilder0.append(s);
        stringBuilder0.append(" : [ \'");
        stringBuilder0.append(constraintAnchor0.f);
        stringBuilder0.append("\'");
        if(constraintAnchor0.h != 0x80000000 || constraintAnchor0.g != 0) {
            stringBuilder0.append(",");
            stringBuilder0.append(constraintAnchor0.g);
            if(constraintAnchor0.h != 0x80000000) {
                stringBuilder0.append(",");
                stringBuilder0.append(constraintAnchor0.h);
                stringBuilder0.append(",");
            }
        }
        stringBuilder0.append(" ] ,\n");
    }

    public final int r() {
        return this.h0 == 8 ? 0 : this.V;
    }

    public final int s() {
        return this.U == null || !(this.U instanceof ConstraintWidgetContainer) ? this.Z : ((ConstraintWidgetContainer)this.U).x0 + this.Z;
    }

    public final int t() {
        return this.U == null || !(this.U instanceof ConstraintWidgetContainer) ? this.a0 : ((ConstraintWidgetContainer)this.U).y0 + this.a0;
    }

    @Override
    public String toString() {
        String s = "";
        StringBuilder stringBuilder0 = c.n("");
        if(this.i0 != null) {
            s = a.s(new StringBuilder("id: "), this.i0, " ");
        }
        stringBuilder0.append(s);
        stringBuilder0.append("(");
        stringBuilder0.append(this.Z);
        stringBuilder0.append(", ");
        stringBuilder0.append(this.a0);
        stringBuilder0.append(") - (");
        stringBuilder0.append(this.V);
        stringBuilder0.append(" x ");
        return c.l(stringBuilder0, this.W, ")");
    }

    public final boolean u(int v) {
        return v == 0 ? (this.I.f == null ? 0 : 1) + (this.K.f == null ? 0 : 1) < 2 : (this.J.f == null ? 0 : 1) + (this.L.f == null ? 0 : 1) + (this.M.f == null ? 0 : 1) < 2;
    }

    public final boolean v(int v, int v1) {
        if(v == 0) {
            ConstraintAnchor constraintAnchor0 = this.I;
            if(constraintAnchor0.f != null && constraintAnchor0.f.c) {
                ConstraintAnchor constraintAnchor1 = this.K.f;
                return constraintAnchor1 != null && constraintAnchor1.c && constraintAnchor1.d() - this.K.e() - (constraintAnchor0.e() + constraintAnchor0.f.d()) >= v1;
            }
        }
        else {
            ConstraintAnchor constraintAnchor2 = this.J;
            if(constraintAnchor2.f != null && constraintAnchor2.f.c) {
                ConstraintAnchor constraintAnchor3 = this.L.f;
                return constraintAnchor3 != null && constraintAnchor3.c && constraintAnchor3.d() - this.L.e() - (constraintAnchor2.e() + constraintAnchor2.f.d()) >= v1;
            }
        }
        return false;
    }

    public final void w(Type constraintAnchor$Type0, ConstraintWidget constraintWidget0, Type constraintAnchor$Type1, int v, int v1) {
        this.j(constraintAnchor$Type0).b(constraintWidget0.j(constraintAnchor$Type1), v, v1, true);
    }

    public final boolean x(int v) {
        ConstraintAnchor[] arr_constraintAnchor = this.Q;
        ConstraintAnchor constraintAnchor0 = arr_constraintAnchor[v * 2];
        if(constraintAnchor0.f != null && constraintAnchor0.f.f != constraintAnchor0) {
            ConstraintAnchor constraintAnchor1 = arr_constraintAnchor[v * 2 + 1];
            return constraintAnchor1.f != null && constraintAnchor1.f.f == constraintAnchor1;
        }
        return false;
    }

    public final boolean y() {
        ConstraintAnchor constraintAnchor0 = this.I.f;
        if(constraintAnchor0 == null || constraintAnchor0.f != this.I) {
            ConstraintAnchor constraintAnchor1 = this.K.f;
            return constraintAnchor1 != null && constraintAnchor1.f == this.K;
        }
        return true;
    }

    public final boolean z() {
        ConstraintAnchor constraintAnchor0 = this.J.f;
        if(constraintAnchor0 == null || constraintAnchor0.f != this.J) {
            ConstraintAnchor constraintAnchor1 = this.L.f;
            return constraintAnchor1 != null && constraintAnchor1.f == this.L;
        }
        return true;
    }
}


package androidx.constraintlayout.core;

import java.util.Arrays;

public class SolverVariable implements Comparable {
    public static enum Type {
        UNRESTRICTED,
        CONSTANT,  // 此枚举字段已被移除
        SLACK,
        ERROR,
        UNKNOWN;

    }

    public boolean a;
    public int b;
    public int c;
    public int d;
    public float e;
    public boolean f;
    public final float[] g;
    public final float[] h;
    public Type i;
    public ArrayRow[] j;
    public int k;
    public int l;

    public SolverVariable(Type solverVariable$Type0) {
        this.b = -1;
        this.c = -1;
        this.d = 0;
        this.f = false;
        this.g = new float[9];
        this.h = new float[9];
        this.j = new ArrayRow[16];
        this.k = 0;
        this.l = 0;
        this.i = solverVariable$Type0;
    }

    public final void a(ArrayRow arrayRow0) {
        for(int v = 0; true; ++v) {
            int v1 = this.k;
            if(v >= v1) {
                break;
            }
            if(this.j[v] == arrayRow0) {
                return;
            }
        }
        ArrayRow[] arr_arrayRow = this.j;
        if(v1 >= arr_arrayRow.length) {
            this.j = (ArrayRow[])Arrays.copyOf(arr_arrayRow, arr_arrayRow.length * 2);
        }
        int v2 = this.k;
        this.j[v2] = arrayRow0;
        this.k = v2 + 1;
    }

    public final void b(ArrayRow arrayRow0) {
        int v = this.k;
        for(int v1 = 0; v1 < v; ++v1) {
            if(this.j[v1] == arrayRow0) {
                while(v1 < v - 1) {
                    this.j[v1] = this.j[v1 + 1];
                    ++v1;
                }
                --this.k;
                return;
            }
        }
    }

    public final void c() {
        this.i = Type.d;
        this.d = 0;
        this.b = -1;
        this.c = -1;
        this.e = 0.0f;
        this.f = false;
        int v = this.k;
        for(int v1 = 0; v1 < v; ++v1) {
            this.j[v1] = null;
        }
        this.k = 0;
        this.l = 0;
        this.a = false;
        Arrays.fill(this.h, 0.0f);
    }

    @Override
    public final int compareTo(Object object0) {
        return this.b - ((SolverVariable)object0).b;
    }

    public final void d(LinearSystem linearSystem0, float f) {
        this.e = f;
        this.f = true;
        int v = this.k;
        this.c = -1;
        for(int v1 = 0; v1 < v; ++v1) {
            this.j[v1].h(linearSystem0, this, false);
        }
        this.k = 0;
    }

    public final void e(LinearSystem linearSystem0, ArrayRow arrayRow0) {
        int v = this.k;
        for(int v1 = 0; v1 < v; ++v1) {
            this.j[v1].i(linearSystem0, arrayRow0, false);
        }
        this.k = 0;
    }

    @Override
    public final String toString() {
        return "" + this.b;
    }
}


package androidx.concurrent.futures;

import a.a;
import com.google.common.util.concurrent.ListenableFuture;
import java.util.Locale;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import java.util.concurrent.locks.LockSupport;
import java.util.logging.Level;
import java.util.logging.Logger;

public abstract class AbstractResolvableFuture implements ListenableFuture {
    static abstract class AtomicHelper {
        public abstract boolean a(AbstractResolvableFuture arg1, Listener arg2, Listener arg3);

        public abstract boolean b(AbstractResolvableFuture arg1, Object arg2, Object arg3);

        public abstract boolean c(AbstractResolvableFuture arg1, Waiter arg2, Waiter arg3);

        public abstract void d(<PERSON>er arg1, <PERSON>er arg2);

        public abstract void e(Waiter arg1, Thread arg2);
    }

    static final class Cancellation {
        public final boolean a;
        public final Throwable b;
        public static final Cancellation c;
        public static final Cancellation d;

        static {
            if(AbstractResolvableFuture.GENERATE_CANCELLATION_CAUSES) {
                Cancellation.d = null;
                Cancellation.c = null;
                return;
            }
            Cancellation.d = new Cancellation(false, null);
            Cancellation.c = new Cancellation(true, null);
        }

        public Cancellation(boolean z, Throwable throwable0) {
            this.a = z;
            this.b = throwable0;
        }
    }

    static final class Failure {
        public final Throwable a;
        public static final Failure b;

        static {
            Failure.b = new Failure(new Throwable("Failure occurred while trying to finish a future.") {  // 初始化器: Ljava/lang/Throwable;-><init>(Ljava/lang/String;)V
                @Override
                public final Throwable fillInStackTrace() {
                    synchronized(this) {
                    }
                    return this;
                }
            });
        }

        public Failure(Throwable throwable0) {
            this.a = (Throwable)AbstractResolvableFuture.checkNotNull(throwable0);
        }
    }

    static final class Listener {
        public final Runnable a;
        public final Executor b;
        public Listener c;
        public static final Listener d;

        static {
            Listener.d = new Listener(null, null);
        }

        public Listener(Runnable runnable0, Executor executor0) {
            this.a = runnable0;
            this.b = executor0;
        }
    }

    static final class SafeAtomicHelper extends AtomicHelper {
        public final AtomicReferenceFieldUpdater a;
        public final AtomicReferenceFieldUpdater b;
        public final AtomicReferenceFieldUpdater c;
        public final AtomicReferenceFieldUpdater d;
        public final AtomicReferenceFieldUpdater e;

        public SafeAtomicHelper(AtomicReferenceFieldUpdater atomicReferenceFieldUpdater0, AtomicReferenceFieldUpdater atomicReferenceFieldUpdater1, AtomicReferenceFieldUpdater atomicReferenceFieldUpdater2, AtomicReferenceFieldUpdater atomicReferenceFieldUpdater3, AtomicReferenceFieldUpdater atomicReferenceFieldUpdater4) {
            this.a = atomicReferenceFieldUpdater0;
            this.b = atomicReferenceFieldUpdater1;
            this.c = atomicReferenceFieldUpdater2;
            this.d = atomicReferenceFieldUpdater3;
            this.e = atomicReferenceFieldUpdater4;
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final boolean a(AbstractResolvableFuture abstractResolvableFuture0, Listener abstractResolvableFuture$Listener0, Listener abstractResolvableFuture$Listener1) {
            do {
                AtomicReferenceFieldUpdater atomicReferenceFieldUpdater0 = this.d;
                if(atomicReferenceFieldUpdater0.compareAndSet(abstractResolvableFuture0, abstractResolvableFuture$Listener0, abstractResolvableFuture$Listener1)) {
                    return true;
                }
            }
            while(atomicReferenceFieldUpdater0.get(abstractResolvableFuture0) == abstractResolvableFuture$Listener0);
            return false;
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final boolean b(AbstractResolvableFuture abstractResolvableFuture0, Object object0, Object object1) {
            do {
                AtomicReferenceFieldUpdater atomicReferenceFieldUpdater0 = this.e;
                if(atomicReferenceFieldUpdater0.compareAndSet(abstractResolvableFuture0, object0, object1)) {
                    return true;
                }
            }
            while(atomicReferenceFieldUpdater0.get(abstractResolvableFuture0) == object0);
            return false;
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final boolean c(AbstractResolvableFuture abstractResolvableFuture0, Waiter abstractResolvableFuture$Waiter0, Waiter abstractResolvableFuture$Waiter1) {
            do {
                AtomicReferenceFieldUpdater atomicReferenceFieldUpdater0 = this.c;
                if(atomicReferenceFieldUpdater0.compareAndSet(abstractResolvableFuture0, abstractResolvableFuture$Waiter0, abstractResolvableFuture$Waiter1)) {
                    return true;
                }
            }
            while(atomicReferenceFieldUpdater0.get(abstractResolvableFuture0) == abstractResolvableFuture$Waiter0);
            return false;
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final void d(Waiter abstractResolvableFuture$Waiter0, Waiter abstractResolvableFuture$Waiter1) {
            this.b.lazySet(abstractResolvableFuture$Waiter0, abstractResolvableFuture$Waiter1);
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final void e(Waiter abstractResolvableFuture$Waiter0, Thread thread0) {
            this.a.lazySet(abstractResolvableFuture$Waiter0, thread0);
        }
    }

    static final class SetFuture implements Runnable {
        public final AbstractResolvableFuture a;
        public final ListenableFuture b;

        public SetFuture(AbstractResolvableFuture abstractResolvableFuture0, ListenableFuture listenableFuture0) {
            this.a = abstractResolvableFuture0;
            this.b = listenableFuture0;
        }

        @Override
        public final void run() {
            if(this.a.value != this) {
                return;
            }
            Object object0 = AbstractResolvableFuture.getFutureValue(this.b);
            if(AbstractResolvableFuture.ATOMIC_HELPER.b(this.a, this, object0)) {
                AbstractResolvableFuture.complete(this.a);
            }
        }
    }

    static final class SynchronizedHelper extends AtomicHelper {
        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final boolean a(AbstractResolvableFuture abstractResolvableFuture0, Listener abstractResolvableFuture$Listener0, Listener abstractResolvableFuture$Listener1) {
            synchronized(abstractResolvableFuture0) {
                if(abstractResolvableFuture0.listeners == abstractResolvableFuture$Listener0) {
                    abstractResolvableFuture0.listeners = abstractResolvableFuture$Listener1;
                    return true;
                }
            }
            return false;
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final boolean b(AbstractResolvableFuture abstractResolvableFuture0, Object object0, Object object1) {
            synchronized(abstractResolvableFuture0) {
                if(abstractResolvableFuture0.value == object0) {
                    abstractResolvableFuture0.value = object1;
                    return true;
                }
            }
            return false;
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final boolean c(AbstractResolvableFuture abstractResolvableFuture0, Waiter abstractResolvableFuture$Waiter0, Waiter abstractResolvableFuture$Waiter1) {
            synchronized(abstractResolvableFuture0) {
                if(abstractResolvableFuture0.waiters == abstractResolvableFuture$Waiter0) {
                    abstractResolvableFuture0.waiters = abstractResolvableFuture$Waiter1;
                    return true;
                }
            }
            return false;
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final void d(Waiter abstractResolvableFuture$Waiter0, Waiter abstractResolvableFuture$Waiter1) {
            abstractResolvableFuture$Waiter0.b = abstractResolvableFuture$Waiter1;
        }

        @Override  // androidx.concurrent.futures.AbstractResolvableFuture$AtomicHelper
        public final void e(Waiter abstractResolvableFuture$Waiter0, Thread thread0) {
            abstractResolvableFuture$Waiter0.a = thread0;
        }
    }

    static final class Waiter {
        public volatile Thread a;
        public volatile Waiter b;
        public static final Waiter c;

        static {
            Waiter.c = new Waiter();  // 初始化器: Ljava/lang/Object;-><init>()V
        }

        public Waiter() {
            AbstractResolvableFuture.ATOMIC_HELPER.e(this, Thread.currentThread());
        }
    }

    static final AtomicHelper ATOMIC_HELPER = null;
    static final boolean GENERATE_CANCELLATION_CAUSES = false;
    private static final Object NULL = null;
    private static final long SPIN_THRESHOLD_NANOS = 1000L;
    volatile Listener listeners;
    private static final Logger log;
    volatile Object value;
    volatile Waiter waiters;

    static {
        SafeAtomicHelper abstractResolvableFuture$SafeAtomicHelper0;
        AbstractResolvableFuture.GENERATE_CANCELLATION_CAUSES = Boolean.parseBoolean(System.getProperty("guava.concurrent.generate_cancellation_cause", "false"));
        AbstractResolvableFuture.log = Logger.getLogger("androidx.concurrent.futures.AbstractResolvableFuture");
        try {
            abstractResolvableFuture$SafeAtomicHelper0 = new SafeAtomicHelper(AtomicReferenceFieldUpdater.newUpdater(Waiter.class, Thread.class, "a"), AtomicReferenceFieldUpdater.newUpdater(Waiter.class, Waiter.class, "b"), AtomicReferenceFieldUpdater.newUpdater(AbstractResolvableFuture.class, Waiter.class, "waiters"), AtomicReferenceFieldUpdater.newUpdater(AbstractResolvableFuture.class, Listener.class, "listeners"), AtomicReferenceFieldUpdater.newUpdater(AbstractResolvableFuture.class, Object.class, "value"));
            throwable0 = null;
        }
        catch(Throwable throwable0) {
            abstractResolvableFuture$SafeAtomicHelper0 = new SynchronizedHelper();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        AbstractResolvableFuture.ATOMIC_HELPER = abstractResolvableFuture$SafeAtomicHelper0;
        if(throwable0 != null) {
            AbstractResolvableFuture.log.log(Level.SEVERE, "SafeAtomicHelper is broken!", throwable0);
        }
        AbstractResolvableFuture.NULL = new Object();
    }

    public final void a(StringBuilder stringBuilder0) {
        try {
            Object object0 = AbstractResolvableFuture.getUninterruptibly(this);
            stringBuilder0.append("SUCCESS, result=[");
            stringBuilder0.append((object0 == this ? "this future" : String.valueOf(object0)));
            stringBuilder0.append("]");
        }
        catch(ExecutionException executionException0) {
            stringBuilder0.append("FAILURE, cause=[");
            stringBuilder0.append(executionException0.getCause());
            stringBuilder0.append("]");
        }
        catch(CancellationException unused_ex) {
            stringBuilder0.append("CANCELLED");
        }
        catch(RuntimeException runtimeException0) {
            stringBuilder0.append("UNKNOWN, cause=[");
            stringBuilder0.append(runtimeException0.getClass());
            stringBuilder0.append(" thrown from get()]");
        }
    }

    @Override  // com.google.common.util.concurrent.ListenableFuture
    public final void addListener(Runnable runnable0, Executor executor0) {
        AbstractResolvableFuture.checkNotNull(runnable0);
        AbstractResolvableFuture.checkNotNull(executor0);
        Listener abstractResolvableFuture$Listener0 = this.listeners;
        Listener abstractResolvableFuture$Listener1 = Listener.d;
        if(abstractResolvableFuture$Listener0 != abstractResolvableFuture$Listener1) {
            Listener abstractResolvableFuture$Listener2 = new Listener(runnable0, executor0);
            while(true) {
                abstractResolvableFuture$Listener2.c = abstractResolvableFuture$Listener0;
                if(AbstractResolvableFuture.ATOMIC_HELPER.a(this, abstractResolvableFuture$Listener0, abstractResolvableFuture$Listener2)) {
                    return;
                }
                abstractResolvableFuture$Listener0 = this.listeners;
                if(abstractResolvableFuture$Listener0 == abstractResolvableFuture$Listener1) {
                    break;
                }
            }
        }
        AbstractResolvableFuture.b(runnable0, executor0);
    }

    public void afterDone() {
    }

    public static void b(Runnable runnable0, Executor executor0) {
        try {
            executor0.execute(runnable0);
        }
        catch(RuntimeException runtimeException0) {
            AbstractResolvableFuture.log.log(Level.SEVERE, "RuntimeException while executing runnable " + runnable0 + " with executor " + executor0, runtimeException0);
        }
    }

    public static Object c(Object object0) {
        if(!(object0 instanceof Cancellation)) {
            if(object0 instanceof Failure) {
                throw new ExecutionException(((Failure)object0).a);
            }
            return object0 == AbstractResolvableFuture.NULL ? null : object0;
        }
        CancellationException cancellationException0 = new CancellationException("Task was cancelled.");
        cancellationException0.initCause(((Cancellation)object0).b);
        throw cancellationException0;
    }

    @Override
    public final boolean cancel(boolean z) {
        Cancellation abstractResolvableFuture$Cancellation0;
        Object object0 = this.value;
        if((object0 == null | object0 instanceof SetFuture) != 0) {
            if(AbstractResolvableFuture.GENERATE_CANCELLATION_CAUSES) {
                abstractResolvableFuture$Cancellation0 = new Cancellation(z, new CancellationException("Future.cancel() was called."));
            }
            else {
                abstractResolvableFuture$Cancellation0 = z ? Cancellation.c : Cancellation.d;
            }
            boolean z1 = false;
            AbstractResolvableFuture abstractResolvableFuture0 = this;
            while(true) {
                if(AbstractResolvableFuture.ATOMIC_HELPER.b(abstractResolvableFuture0, object0, abstractResolvableFuture$Cancellation0)) {
                    AbstractResolvableFuture.complete(abstractResolvableFuture0);
                    if(object0 instanceof SetFuture) {
                        ListenableFuture listenableFuture0 = ((SetFuture)object0).b;
                        if(!(listenableFuture0 instanceof AbstractResolvableFuture)) {
                            listenableFuture0.cancel(z);
                            return true;
                        }
                        abstractResolvableFuture0 = (AbstractResolvableFuture)listenableFuture0;
                        object0 = abstractResolvableFuture0.value;
                        if((object0 == null | object0 instanceof SetFuture) != 0) {
                            z1 = true;
                            continue;
                        }
                    }
                    return true;
                }
                object0 = abstractResolvableFuture0.value;
                if(!(object0 instanceof SetFuture)) {
                    break;
                }
            }
            return z1;
        }
        return false;
    }

    public static Object checkNotNull(Object object0) {
        object0.getClass();
        return object0;
    }

    public static void complete(AbstractResolvableFuture abstractResolvableFuture0) {
        Runnable runnable0;
        Listener abstractResolvableFuture$Listener2;
        Listener abstractResolvableFuture$Listener1;
        Listener abstractResolvableFuture$Listener0 = null;
        while(true) {
            Waiter abstractResolvableFuture$Waiter0 = abstractResolvableFuture0.waiters;
            if(!AbstractResolvableFuture.ATOMIC_HELPER.c(abstractResolvableFuture0, abstractResolvableFuture$Waiter0, Waiter.c)) {
                continue;
            }
            while(abstractResolvableFuture$Waiter0 != null) {
                Thread thread0 = abstractResolvableFuture$Waiter0.a;
                if(thread0 != null) {
                    abstractResolvableFuture$Waiter0.a = null;
                    LockSupport.unpark(thread0);
                }
                abstractResolvableFuture$Waiter0 = abstractResolvableFuture$Waiter0.b;
            }
            abstractResolvableFuture0.afterDone();
            do {
                abstractResolvableFuture$Listener1 = abstractResolvableFuture0.listeners;
            }
            while(!AbstractResolvableFuture.ATOMIC_HELPER.a(abstractResolvableFuture0, abstractResolvableFuture$Listener1, Listener.d));
            while(true) {
                abstractResolvableFuture$Listener2 = abstractResolvableFuture$Listener0;
                abstractResolvableFuture$Listener0 = abstractResolvableFuture$Listener1;
                if(abstractResolvableFuture$Listener0 == null) {
                    break;
                }
                abstractResolvableFuture$Listener1 = abstractResolvableFuture$Listener0.c;
                abstractResolvableFuture$Listener0.c = abstractResolvableFuture$Listener2;
            }
        label_19:
            if(abstractResolvableFuture$Listener2 != null) {
                abstractResolvableFuture$Listener0 = abstractResolvableFuture$Listener2.c;
                runnable0 = abstractResolvableFuture$Listener2.a;
                if(runnable0 instanceof SetFuture) {
                    abstractResolvableFuture0 = ((SetFuture)runnable0).a;
                    if(abstractResolvableFuture0.value == ((SetFuture)runnable0)) {
                        Object object0 = AbstractResolvableFuture.getFutureValue(((SetFuture)runnable0).b);
                        if(AbstractResolvableFuture.ATOMIC_HELPER.b(abstractResolvableFuture0, ((SetFuture)runnable0), object0)) {
                            continue;
                        }
                    }
                }
                else {
                    break;
                }
                abstractResolvableFuture$Listener2 = abstractResolvableFuture$Listener0;
                goto label_19;
            }
            return;
        }
        AbstractResolvableFuture.b(runnable0, abstractResolvableFuture$Listener2.b);
        abstractResolvableFuture$Listener2 = abstractResolvableFuture$Listener0;
        goto label_19;
    }

    public final void d(Waiter abstractResolvableFuture$Waiter0) {
        abstractResolvableFuture$Waiter0.a = null;
    alab1:
        while(true) {
            Waiter abstractResolvableFuture$Waiter1 = this.waiters;
            if(abstractResolvableFuture$Waiter1 == Waiter.c) {
                return;
            }
            Waiter abstractResolvableFuture$Waiter2 = null;
            while(true) {
                if(abstractResolvableFuture$Waiter1 == null) {
                    break alab1;
                }
                Waiter abstractResolvableFuture$Waiter3 = abstractResolvableFuture$Waiter1.b;
                if(abstractResolvableFuture$Waiter1.a != null) {
                    abstractResolvableFuture$Waiter2 = abstractResolvableFuture$Waiter1;
                }
                else if(abstractResolvableFuture$Waiter2 == null) {
                    if(AbstractResolvableFuture.ATOMIC_HELPER.c(this, abstractResolvableFuture$Waiter1, abstractResolvableFuture$Waiter3)) {
                        abstractResolvableFuture$Waiter1 = abstractResolvableFuture$Waiter3;
                        continue;
                    }
                    break;
                }
                else {
                    abstractResolvableFuture$Waiter2.b = abstractResolvableFuture$Waiter3;
                    if(abstractResolvableFuture$Waiter2.a == null) {
                        break;
                    }
                }
                abstractResolvableFuture$Waiter1 = abstractResolvableFuture$Waiter3;
            }
        }
    }

    @Override
    public final Object get() throws InterruptedException, ExecutionException {
        if(Thread.interrupted()) {
            throw new InterruptedException();
        }
        Object object0 = this.value;
        if(((object0 == null ? 0 : 1) & !(object0 instanceof SetFuture)) != 0) {
            return AbstractResolvableFuture.c(object0);
        }
        Waiter abstractResolvableFuture$Waiter0 = this.waiters;
        Waiter abstractResolvableFuture$Waiter1 = Waiter.c;
        if(abstractResolvableFuture$Waiter0 != abstractResolvableFuture$Waiter1) {
            Waiter abstractResolvableFuture$Waiter2 = new Waiter();
            while(true) {
                AbstractResolvableFuture.ATOMIC_HELPER.d(abstractResolvableFuture$Waiter2, abstractResolvableFuture$Waiter0);
                if(AbstractResolvableFuture.ATOMIC_HELPER.c(this, abstractResolvableFuture$Waiter0, abstractResolvableFuture$Waiter2)) {
                    while(true) {
                        LockSupport.park(this);
                        if(Thread.interrupted()) {
                            break;
                        }
                        Object object1 = this.value;
                        if(((object1 == null ? 0 : 1) & !(object1 instanceof SetFuture)) != 0) {
                            return AbstractResolvableFuture.c(object1);
                        }
                    }
                    this.d(abstractResolvableFuture$Waiter2);
                    throw new InterruptedException();
                }
                abstractResolvableFuture$Waiter0 = this.waiters;
                if(abstractResolvableFuture$Waiter0 == abstractResolvableFuture$Waiter1) {
                    break;
                }
            }
        }
        return AbstractResolvableFuture.c(this.value);
    }

    @Override
    public final Object get(long v, TimeUnit timeUnit0) throws InterruptedException, TimeoutException, ExecutionException {
        long v1 = timeUnit0.toNanos(v);
        if(Thread.interrupted()) {
            throw new InterruptedException();
        }
        Object object0 = this.value;
        if(((object0 == null ? 0 : 1) & !(object0 instanceof SetFuture)) != 0) {
            return AbstractResolvableFuture.c(object0);
        }
        long v2 = v1 <= 0L ? 0L : System.nanoTime() + v1;
        if(v1 >= 1000L) {
            Waiter abstractResolvableFuture$Waiter0 = this.waiters;
            Waiter abstractResolvableFuture$Waiter1 = Waiter.c;
            if(abstractResolvableFuture$Waiter0 != abstractResolvableFuture$Waiter1) {
                Waiter abstractResolvableFuture$Waiter2 = new Waiter();
                while(true) {
                    AbstractResolvableFuture.ATOMIC_HELPER.d(abstractResolvableFuture$Waiter2, abstractResolvableFuture$Waiter0);
                    if(AbstractResolvableFuture.ATOMIC_HELPER.c(this, abstractResolvableFuture$Waiter0, abstractResolvableFuture$Waiter2)) {
                        do {
                            LockSupport.parkNanos(this, v1);
                            if(Thread.interrupted()) {
                                this.d(abstractResolvableFuture$Waiter2);
                                throw new InterruptedException();
                            }
                            Object object1 = this.value;
                            if(((object1 == null ? 0 : 1) & !(object1 instanceof SetFuture)) != 0) {
                                return AbstractResolvableFuture.c(object1);
                            }
                            v1 = v2 - System.nanoTime();
                        }
                        while(v1 >= 1000L);
                        this.d(abstractResolvableFuture$Waiter2);
                        goto label_27;
                    }
                    abstractResolvableFuture$Waiter0 = this.waiters;
                    if(abstractResolvableFuture$Waiter0 == abstractResolvableFuture$Waiter1) {
                        break;
                    }
                }
            }
            return AbstractResolvableFuture.c(this.value);
        }
    label_27:
        while(v1 > 0L) {
            Object object2 = this.value;
            if(((object2 == null ? 0 : 1) & !(object2 instanceof SetFuture)) != 0) {
                return AbstractResolvableFuture.c(object2);
            }
            if(Thread.interrupted()) {
                throw new InterruptedException();
            }
            v1 = v2 - System.nanoTime();
        }
        String s = this.toString();
        Locale locale0 = Locale.ROOT;
        String s1 = timeUnit0.toString().toLowerCase(locale0);
        StringBuilder stringBuilder0 = a.w("Waited ", v, " ");
        stringBuilder0.append(timeUnit0.toString().toLowerCase(locale0));
        String s2 = stringBuilder0.toString();
        if(v1 + 1000L < 0L) {
            String s3 = s2 + " (plus ";
            long v3 = timeUnit0.convert(-v1, TimeUnit.NANOSECONDS);
            long v4 = -v1 - timeUnit0.toNanos(v3);
            int v5 = Long.compare(v3, 0L);
            boolean z = v5 == 0 || v4 > 1000L;
            if(v5 > 0) {
                s3 = (z ? s3 + v3 + " " + s1 + "," : s3 + v3 + " " + s1) + " ";
            }
            if(z) {
                s3 = s3 + v4 + " nanoseconds ";
            }
            s2 = s3 + "delay)";
        }
        throw this.isDone() ? new TimeoutException(s2 + " but future completed as timeout expired") : new TimeoutException(s2 + " for " + s);
    }

    public static Object getFutureValue(ListenableFuture listenableFuture0) {
        if(listenableFuture0 instanceof AbstractResolvableFuture) {
            Cancellation abstractResolvableFuture$Cancellation0 = ((AbstractResolvableFuture)listenableFuture0).value;
            if(abstractResolvableFuture$Cancellation0 instanceof Cancellation && abstractResolvableFuture$Cancellation0.a) {
                return abstractResolvableFuture$Cancellation0.b == null ? Cancellation.d : new Cancellation(false, abstractResolvableFuture$Cancellation0.b);
            }
            return abstractResolvableFuture$Cancellation0;
        }
        boolean z = listenableFuture0.isCancelled();
        if((!AbstractResolvableFuture.GENERATE_CANCELLATION_CAUSES & z) != 0) {
            return Cancellation.d;
        }
        try {
            Object object0 = AbstractResolvableFuture.getUninterruptibly(listenableFuture0);
            return object0 == null ? AbstractResolvableFuture.NULL : object0;
        }
        catch(ExecutionException executionException0) {
            return new Failure(executionException0.getCause());
        }
        catch(CancellationException cancellationException0) {
            return !z ? new Failure(new IllegalArgumentException("get() threw CancellationException, despite reporting isCancelled() == false: " + listenableFuture0, cancellationException0)) : new Cancellation(false, cancellationException0);
        }
        catch(Throwable throwable0) {
            return new Failure(throwable0);
        }
    }

    public static Object getUninterruptibly(Future future0) throws ExecutionException {
        Object object0;
        boolean z = false;
        while(true) {
            try {
                object0 = future0.get();
                break;
            }
            catch(InterruptedException unused_ex) {
                z = true;
            }
            catch(Throwable throwable0) {
                if(z) {
                    Thread.currentThread().interrupt();
                }
                throw throwable0;
            }
        }
        if(z) {
            Thread.currentThread().interrupt();
        }
        return object0;
    }

    public void interruptTask() {
    }

    @Override
    public final boolean isCancelled() {
        return this.value instanceof Cancellation;
    }

    @Override
    public final boolean isDone() {
        return this.value == null ? 0 : !(this.value instanceof SetFuture) & 1;
    }

    public final void maybePropagateCancellationTo(Future future0) {
        if((future0 != null & this.isCancelled()) != 0) {
            future0.cancel(this.wasInterrupted());
        }
    }

    public String pendingToString() {
        Object object0 = this.value;
        if(object0 instanceof SetFuture) {
            StringBuilder stringBuilder0 = new StringBuilder("setFuture=[");
            ListenableFuture listenableFuture0 = ((SetFuture)object0).b;
            return listenableFuture0 == this ? a.s(stringBuilder0, "this future", "]") : a.s(stringBuilder0, String.valueOf(listenableFuture0), "]");
        }
        return this instanceof ScheduledFuture ? "remaining delay=[" + ((ScheduledFuture)this).getDelay(TimeUnit.MILLISECONDS) + " ms]" : null;
    }

    public boolean set(Object object0) {
        if(object0 == null) {
            object0 = AbstractResolvableFuture.NULL;
        }
        if(AbstractResolvableFuture.ATOMIC_HELPER.b(this, null, object0)) {
            AbstractResolvableFuture.complete(this);
            return true;
        }
        return false;
    }

    public boolean setException(Throwable throwable0) {
        Failure abstractResolvableFuture$Failure0 = new Failure(((Throwable)AbstractResolvableFuture.checkNotNull(throwable0)));
        if(AbstractResolvableFuture.ATOMIC_HELPER.b(this, null, abstractResolvableFuture$Failure0)) {
            AbstractResolvableFuture.complete(this);
            return true;
        }
        return false;
    }

    public boolean setFuture(ListenableFuture listenableFuture0) {
        Failure abstractResolvableFuture$Failure0;
        AbstractResolvableFuture.checkNotNull(listenableFuture0);
        Object object0 = this.value;
        if(object0 == null) {
            if(listenableFuture0.isDone()) {
                Object object1 = AbstractResolvableFuture.getFutureValue(listenableFuture0);
                if(AbstractResolvableFuture.ATOMIC_HELPER.b(this, null, object1)) {
                    AbstractResolvableFuture.complete(this);
                    return true;
                }
                return false;
            }
            SetFuture abstractResolvableFuture$SetFuture0 = new SetFuture(this, listenableFuture0);
            if(AbstractResolvableFuture.ATOMIC_HELPER.b(this, null, abstractResolvableFuture$SetFuture0)) {
                try {
                    listenableFuture0.addListener(abstractResolvableFuture$SetFuture0, DirectExecutor.a);
                }
                catch(Throwable throwable0) {
                    try {
                        abstractResolvableFuture$Failure0 = new Failure(throwable0);
                    }
                    catch(Throwable unused_ex) {
                        abstractResolvableFuture$Failure0 = Failure.b;
                    }
                    AbstractResolvableFuture.ATOMIC_HELPER.b(this, abstractResolvableFuture$SetFuture0, abstractResolvableFuture$Failure0);
                }
                return true;
            }
            object0 = this.value;
        }
        if(object0 instanceof Cancellation) {
            listenableFuture0.cancel(((Cancellation)object0).a);
        }
        return false;
    }

    @Override
    public String toString() {
        String s;
        StringBuilder stringBuilder0 = new StringBuilder();
        stringBuilder0.append(super.toString());
        stringBuilder0.append("[status=");
        if(this.isCancelled()) {
            stringBuilder0.append("CANCELLED");
        }
        else if(this.isDone()) {
            this.a(stringBuilder0);
        }
        else {
            try {
                s = this.pendingToString();
            }
            catch(RuntimeException runtimeException0) {
                s = "Exception thrown from implementation: " + runtimeException0.getClass();
            }
            if(s != null && !s.isEmpty()) {
                stringBuilder0.append("PENDING, info=[");
                stringBuilder0.append(s);
                stringBuilder0.append("]");
            }
            else if(this.isDone()) {
                this.a(stringBuilder0);
            }
            else {
                stringBuilder0.append("PENDING");
            }
        }
        stringBuilder0.append("]");
        return stringBuilder0.toString();
    }

    // 去混淆评级： 低(20)
    public final boolean wasInterrupted() {
        return this.value instanceof Cancellation && ((Cancellation)this.value).a;
    }
}


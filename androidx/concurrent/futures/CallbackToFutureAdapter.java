package androidx.concurrent.futures;

import androidx.privacysandbox.ads.adservices.java.internal.a;
import com.google.common.util.concurrent.ListenableFuture;
import java.lang.ref.WeakReference;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

public abstract class CallbackToFutureAdapter {
    public static final class Completer {
        public Object a;
        public SafeFuture b;
        public ResolvableFuture c;
        public boolean d;

        public final void a(Object object0) {
            this.d = true;
            if(this.b != null && this.b.b.set(object0)) {
                this.a = null;
                this.b = null;
                this.c = null;
            }
        }

        public final void b() {
            this.d = true;
            if(this.b != null && this.b.b.cancel(true)) {
                this.a = null;
                this.b = null;
                this.c = null;
            }
        }

        public final void c(Throwable throwable0) {
            this.d = true;
            if(this.b != null && this.b.b.setException(throwable0)) {
                this.a = null;
                this.b = null;
                this.c = null;
            }
        }

        @Override
        public final void finalize() {
            SafeFuture callbackToFutureAdapter$SafeFuture0 = this.b;
            if(callbackToFutureAdapter$SafeFuture0 != null) {
                AbstractResolvableFuture abstractResolvableFuture0 = callbackToFutureAdapter$SafeFuture0.b;
                if(!abstractResolvableFuture0.isDone()) {
                    abstractResolvableFuture0.setException(new FutureGarbageCollectedException("The completer object was garbage collected - this future would otherwise never complete. The tag was: " + this.a));  // 初始化器: Ljava/lang/Throwable;-><init>(Ljava/lang/String;)V
                }
            }
            if(!this.d) {
                ResolvableFuture resolvableFuture0 = this.c;
                if(resolvableFuture0 != null) {
                    resolvableFuture0.set(null);
                }
            }
        }
    }

    static final class FutureGarbageCollectedException extends Throwable {
        @Override
        public final Throwable fillInStackTrace() {
            synchronized(this) {
            }
            return this;
        }
    }

    static final class SafeFuture implements ListenableFuture {
        public final WeakReference a;
        public final AbstractResolvableFuture b;

        public SafeFuture(Completer callbackToFutureAdapter$Completer0) {
            this.b = new AbstractResolvableFuture() {
                public final SafeFuture a;

                {
                    this.a = callbackToFutureAdapter$SafeFuture0;
                }

                @Override  // androidx.concurrent.futures.AbstractResolvableFuture
                public final String pendingToString() {
                    Completer callbackToFutureAdapter$Completer0 = (Completer)this.a.a.get();
                    return callbackToFutureAdapter$Completer0 == null ? "Completer object has been garbage collected, future will fail soon" : "tag=[" + callbackToFutureAdapter$Completer0.a + "]";
                }
            };
            this.a = new WeakReference(callbackToFutureAdapter$Completer0);
        }

        @Override  // com.google.common.util.concurrent.ListenableFuture
        public final void addListener(Runnable runnable0, Executor executor0) {
            this.b.addListener(runnable0, executor0);
        }

        @Override
        public final boolean cancel(boolean z) {
            Completer callbackToFutureAdapter$Completer0 = (Completer)this.a.get();
            boolean z1 = this.b.cancel(z);
            if(z1 && callbackToFutureAdapter$Completer0 != null) {
                callbackToFutureAdapter$Completer0.a = null;
                callbackToFutureAdapter$Completer0.b = null;
                callbackToFutureAdapter$Completer0.c.set(null);
            }
            return z1;
        }

        @Override
        public final Object get() {
            return this.b.get();
        }

        @Override
        public final Object get(long v, TimeUnit timeUnit0) {
            return this.b.get(v, timeUnit0);
        }

        @Override
        public final boolean isCancelled() {
            return this.b.isCancelled();
        }

        @Override
        public final boolean isDone() {
            return this.b.isDone();
        }

        @Override
        public final String toString() {
            return this.b.toString();
        }
    }

    public static ListenableFuture a(a a0) {
        Completer callbackToFutureAdapter$Completer0 = new Completer();  // 初始化器: Ljava/lang/Object;-><init>()V
        callbackToFutureAdapter$Completer0.c = new ResolvableFuture();  // 初始化器: Ljava/lang/Object;-><init>()V
        SafeFuture callbackToFutureAdapter$SafeFuture0 = new SafeFuture(callbackToFutureAdapter$Completer0);
        callbackToFutureAdapter$Completer0.b = callbackToFutureAdapter$SafeFuture0;
        callbackToFutureAdapter$Completer0.a = a.class;
        try {
            Object object0 = a0.c(callbackToFutureAdapter$Completer0);
            if(object0 != null) {
                callbackToFutureAdapter$Completer0.a = object0;
                return callbackToFutureAdapter$SafeFuture0;
            }
        }
        catch(Exception exception0) {
            callbackToFutureAdapter$SafeFuture0.b.setException(exception0);
        }
        return callbackToFutureAdapter$SafeFuture0;
    }
}


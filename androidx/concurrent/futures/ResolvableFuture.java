package androidx.concurrent.futures;

import com.google.common.util.concurrent.ListenableFuture;

public final class ResolvableFuture extends AbstractResolvableFuture {
    @Override  // androidx.concurrent.futures.AbstractResolvableFuture
    public final boolean set(Object object0) {
        return super.set(object0);
    }

    @Override  // androidx.concurrent.futures.AbstractResolvableFuture
    public final boolean setException(Throwable throwable0) {
        return super.setException(throwable0);
    }

    @Override  // androidx.concurrent.futures.AbstractResolvableFuture
    public final boolean setFuture(ListenableFuture listenableFuture0) {
        return super.setFuture(listenableFuture0);
    }
}


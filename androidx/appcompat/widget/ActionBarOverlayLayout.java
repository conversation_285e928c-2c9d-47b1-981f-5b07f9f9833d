package androidx.appcompat.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.ViewGroup;
import android.view.ViewPropertyAnimator;
import android.view.Window.Callback;
import android.view.WindowInsets;
import android.widget.FrameLayout;
import android.widget.OverScroller;
import androidx.appcompat.app.WindowDecorActionBar;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.appcompat.view.ViewPropertyAnimatorCompatSet;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuPresenter.Callback;
import androidx.core.graphics.Insets;
import androidx.core.view.NestedScrollingParent2;
import androidx.core.view.NestedScrollingParent3;
import androidx.core.view.NestedScrollingParentHelper;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat.Builder;
import androidx.core.view.WindowInsetsCompat;

@SuppressLint({"UnknownNullness"})
public class ActionBarOverlayLayout extends ViewGroup implements DecorContentParent, NestedScrollingParent2, NestedScrollingParent3 {
    public interface ActionBarVisibilityCallback {
    }

    public static class LayoutParams extends ViewGroup.MarginLayoutParams {
    }

    static final class NoSystemUiLayoutFlagView extends View {
        @Override  // android.view.View
        public final int getWindowSystemUiVisibility() {
            return 0;
        }
    }

    public final NestedScrollingParentHelper A;
    public final NoSystemUiLayoutFlagView B;
    public static final int[] C;
    public static final WindowInsetsCompat D;
    public static final Rect E;
    public int a;
    public int b;
    public ContentFrameLayout c;
    public ActionBarContainer d;
    public DecorToolbar e;
    public Drawable f;
    public boolean g;
    public boolean h;
    public boolean i;
    public boolean j;
    public int k;
    public int l;
    public final Rect m;
    public final Rect n;
    public final Rect o;
    public final Rect p;
    public WindowInsetsCompat q;
    public WindowInsetsCompat r;
    public WindowInsetsCompat s;
    public WindowInsetsCompat t;
    public ActionBarVisibilityCallback u;
    public OverScroller v;
    public ViewPropertyAnimator w;
    public final AnimatorListenerAdapter x;
    public final Runnable y;
    public final Runnable z;

    static {
        ActionBarOverlayLayout.C = new int[]{0x7F04000C, 0x1010059};  // attr:actionBarSize
        Builder windowInsetsCompat$Builder0 = new Builder();
        windowInsetsCompat$Builder0.b(Insets.b(0, 1, 0, 1));
        ActionBarOverlayLayout.D = windowInsetsCompat$Builder0.a();
        ActionBarOverlayLayout.E = new Rect();
    }

    public ActionBarOverlayLayout(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0);
        this.b = 0;
        this.m = new Rect();
        this.n = new Rect();
        this.o = new Rect();
        this.p = new Rect();
        new Rect();
        new Rect();
        new Rect();
        new Rect();
        this.q = WindowInsetsCompat.b;
        this.r = WindowInsetsCompat.b;
        this.s = WindowInsetsCompat.b;
        this.t = WindowInsetsCompat.b;
        this.x = new AnimatorListenerAdapter() {
            public final ActionBarOverlayLayout a;

            @Override  // android.animation.AnimatorListenerAdapter
            public final void onAnimationCancel(Animator animator0) {
                ActionBarOverlayLayout.this.w = null;
                ActionBarOverlayLayout.this.j = false;
            }

            @Override  // android.animation.AnimatorListenerAdapter
            public final void onAnimationEnd(Animator animator0) {
                ActionBarOverlayLayout.this.w = null;
                ActionBarOverlayLayout.this.j = false;
            }
        };
        this.y = new Runnable() {
            public final ActionBarOverlayLayout a;

            {
                this.a = actionBarOverlayLayout0;
            }

            @Override
            public final void run() {
                this.a.d();
                this.a.w = this.a.d.animate().translationY(0.0f).setListener(this.a.x);
            }
        };
        this.z = new Runnable() {
            public final ActionBarOverlayLayout a;

            {
                this.a = actionBarOverlayLayout0;
            }

            @Override
            public final void run() {
                this.a.d();
                this.a.w = this.a.d.animate().translationY(((float)(-this.a.d.getHeight()))).setListener(this.a.x);
            }
        };
        this.e(context0);
        this.A = new NestedScrollingParentHelper();  // 初始化器: Ljava/lang/Object;-><init>()V
        NoSystemUiLayoutFlagView actionBarOverlayLayout$NoSystemUiLayoutFlagView0 = new NoSystemUiLayoutFlagView(context0);  // 初始化器: Landroid/view/View;-><init>(Landroid/content/Context;)V
        actionBarOverlayLayout$NoSystemUiLayoutFlagView0.setWillNotDraw(true);
        this.B = actionBarOverlayLayout$NoSystemUiLayoutFlagView0;
        this.addView(actionBarOverlayLayout$NoSystemUiLayoutFlagView0);
    }

    @Override  // androidx.core.view.NestedScrollingParent3
    public final void a(View view0, int v, int v1, int v2, int v3, int[] arr_v, int v4) {
        this.onNestedScroll(view0, v, v1, v2, v3, v4);
    }

    public static boolean b(FrameLayout frameLayout0, Rect rect0, boolean z) {
        boolean z1;
        LayoutParams actionBarOverlayLayout$LayoutParams0 = (LayoutParams)frameLayout0.getLayoutParams();
        int v = rect0.left;
        if(actionBarOverlayLayout$LayoutParams0.leftMargin == v) {
            z1 = false;
        }
        else {
            actionBarOverlayLayout$LayoutParams0.leftMargin = v;
            z1 = true;
        }
        int v1 = rect0.top;
        if(actionBarOverlayLayout$LayoutParams0.topMargin != v1) {
            actionBarOverlayLayout$LayoutParams0.topMargin = v1;
            z1 = true;
        }
        int v2 = rect0.right;
        if(actionBarOverlayLayout$LayoutParams0.rightMargin != v2) {
            actionBarOverlayLayout$LayoutParams0.rightMargin = v2;
            z1 = true;
        }
        if(z) {
            int v3 = rect0.bottom;
            if(actionBarOverlayLayout$LayoutParams0.bottomMargin != v3) {
                actionBarOverlayLayout$LayoutParams0.bottomMargin = v3;
                return true;
            }
        }
        return z1;
    }

    public final void c() {
        this.h();
        ActionMenuView actionMenuView0 = ((ToolbarWidgetWrapper)this.e).a.a;
        if(actionMenuView0 != null) {
            ActionMenuPresenter actionMenuPresenter0 = actionMenuView0.t;
            if(actionMenuPresenter0 != null) {
                actionMenuPresenter0.n();
                ActionButtonSubmenu actionMenuPresenter$ActionButtonSubmenu0 = actionMenuPresenter0.u;
                if(actionMenuPresenter$ActionButtonSubmenu0 != null && actionMenuPresenter$ActionButtonSubmenu0.b()) {
                    actionMenuPresenter$ActionButtonSubmenu0.j.dismiss();
                }
            }
        }
    }

    @Override  // android.view.ViewGroup
    public final boolean checkLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return viewGroup$LayoutParams0 instanceof LayoutParams;
    }

    public final void d() {
        this.removeCallbacks(this.y);
        this.removeCallbacks(this.z);
        ViewPropertyAnimator viewPropertyAnimator0 = this.w;
        if(viewPropertyAnimator0 != null) {
            viewPropertyAnimator0.cancel();
        }
    }

    @Override  // android.view.View
    public final void draw(Canvas canvas0) {
        int v;
        super.draw(canvas0);
        if(this.f != null) {
            if(this.d.getVisibility() == 0) {
                float f = (float)this.d.getBottom();
                v = (int)(this.d.getTranslationY() + f + 0.5f);
            }
            else {
                v = 0;
            }
            this.f.setBounds(0, v, this.getWidth(), this.f.getIntrinsicHeight() + v);
            this.f.draw(canvas0);
        }
    }

    public final void e(Context context0) {
        TypedArray typedArray0 = this.getContext().getTheme().obtainStyledAttributes(ActionBarOverlayLayout.C);
        boolean z = false;
        this.a = typedArray0.getDimensionPixelSize(0, 0);
        Drawable drawable0 = typedArray0.getDrawable(1);
        this.f = drawable0;
        if(drawable0 == null) {
            z = true;
        }
        this.setWillNotDraw(z);
        typedArray0.recycle();
        this.v = new OverScroller(context0);
    }

    public final void f(int v) {
        this.h();
        switch(v) {
            case 2: {
                this.e.getClass();
                return;
            }
            case 5: {
                this.e.getClass();
                return;
            }
            case 109: {
                this.setOverlayMode(true);
            }
        }
    }

    @Override  // android.view.View
    public final boolean fitSystemWindows(Rect rect0) {
        return super.fitSystemWindows(rect0);
    }

    public final boolean g() {
        this.h();
        ActionMenuView actionMenuView0 = ((ToolbarWidgetWrapper)this.e).a.a;
        return actionMenuView0 != null && (actionMenuView0.t != null && (actionMenuView0.t.v != null || actionMenuView0.t.o()));
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams(-1, -1);  // 初始化器: Landroid/view/ViewGroup$MarginLayoutParams;-><init>(II)V
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet0) {
        return new LayoutParams(this.getContext(), attributeSet0);  // 初始化器: Landroid/view/ViewGroup$MarginLayoutParams;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return new LayoutParams(viewGroup$LayoutParams0);  // 初始化器: Landroid/view/ViewGroup$MarginLayoutParams;-><init>(Landroid/view/ViewGroup$LayoutParams;)V
    }

    public int getActionBarHideOffset() {
        return this.d == null ? 0 : -((int)this.d.getTranslationY());
    }

    @Override  // android.view.ViewGroup
    public int getNestedScrollAxes() {
        return this.A.b | this.A.a;
    }

    public CharSequence getTitle() {
        this.h();
        return ((ToolbarWidgetWrapper)this.e).a.getTitle();
    }

    public final void h() {
        DecorToolbar decorToolbar0;
        if(this.c == null) {
            this.c = (ContentFrameLayout)this.findViewById(0x7F0A0052);  // id:action_bar_activity_content
            this.d = (ActionBarContainer)this.findViewById(0x7F0A0053);  // id:action_bar_container
            View view0 = this.findViewById(0x7F0A0051);  // id:action_bar
            if(view0 instanceof DecorToolbar) {
                decorToolbar0 = (DecorToolbar)view0;
            }
            else if(view0 instanceof Toolbar) {
                decorToolbar0 = ((Toolbar)view0).getWrapper();
            }
            else {
                throw new IllegalStateException("Can\'t make a decor toolbar out of " + view0.getClass().getSimpleName());
            }
            this.e = decorToolbar0;
        }
    }

    public final void i(MenuBuilder menuBuilder0, Callback menuPresenter$Callback0) {
        this.h();
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = (ToolbarWidgetWrapper)this.e;
        Toolbar toolbar0 = toolbarWidgetWrapper0.a;
        if(toolbarWidgetWrapper0.m == null) {
            ActionMenuPresenter actionMenuPresenter0 = new ActionMenuPresenter(toolbar0.getContext());
            toolbarWidgetWrapper0.m = actionMenuPresenter0;
            actionMenuPresenter0.i = 0x7F0A005E;  // id:action_menu_presenter
        }
        ActionMenuPresenter actionMenuPresenter1 = toolbarWidgetWrapper0.m;
        actionMenuPresenter1.e = menuPresenter$Callback0;
        if(menuBuilder0 != null || toolbar0.a != null) {
            toolbar0.f();
            MenuBuilder menuBuilder1 = toolbar0.a.p;
            if(menuBuilder1 != menuBuilder0) {
                if(menuBuilder1 != null) {
                    menuBuilder1.r(toolbar0.L);
                    menuBuilder1.r(toolbar0.M);
                }
                if(toolbar0.M == null) {
                    toolbar0.M = new ExpandedActionViewMenuPresenter(toolbar0);
                }
                actionMenuPresenter1.r = true;
                if(menuBuilder0 == null) {
                    actionMenuPresenter1.g(toolbar0.j, null);
                    toolbar0.M.g(toolbar0.j, null);
                    actionMenuPresenter1.d(true);
                    toolbar0.M.d(true);
                }
                else {
                    menuBuilder0.b(actionMenuPresenter1, toolbar0.j);
                    menuBuilder0.b(toolbar0.M, toolbar0.j);
                }
                toolbar0.a.setPopupTheme(toolbar0.k);
                toolbar0.a.setPresenter(actionMenuPresenter1);
                toolbar0.L = actionMenuPresenter1;
                toolbar0.x();
            }
        }
    }

    @Override  // android.view.View
    public final WindowInsets onApplyWindowInsets(WindowInsets windowInsets0) {
        this.h();
        WindowInsetsCompat windowInsetsCompat0 = WindowInsetsCompat.n(this, windowInsets0);
        Rect rect0 = new Rect(windowInsetsCompat0.f(), windowInsetsCompat0.h(), windowInsetsCompat0.g(), windowInsetsCompat0.e());
        boolean z = ActionBarOverlayLayout.b(this.d, rect0, false);
        Rect rect1 = this.m;
        ViewCompat.b(this, windowInsetsCompat0, rect1);
        WindowInsetsCompat windowInsetsCompat1 = windowInsetsCompat0.i(rect1.left, rect1.top, rect1.right, rect1.bottom);
        this.q = windowInsetsCompat1;
        if(!this.r.equals(windowInsetsCompat1)) {
            this.r = this.q;
            z = true;
        }
        Rect rect2 = this.n;
        if(!rect2.equals(rect1)) {
            rect2.set(rect1);
            this.requestLayout();
            return windowInsetsCompat0.a().c().b().m();
        }
        if(z) {
            this.requestLayout();
        }
        return windowInsetsCompat0.a().c().b().m();
    }

    @Override  // android.view.View
    public final void onConfigurationChanged(Configuration configuration0) {
        super.onConfigurationChanged(configuration0);
        this.e(this.getContext());
        ViewCompat.A(this);
    }

    @Override  // android.view.ViewGroup
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        this.d();
    }

    @Override  // android.view.ViewGroup
    public final void onLayout(boolean z, int v, int v1, int v2, int v3) {
        int v4 = this.getChildCount();
        int v5 = this.getPaddingLeft();
        int v6 = this.getPaddingTop();
        for(int v7 = 0; v7 < v4; ++v7) {
            View view0 = this.getChildAt(v7);
            if(view0.getVisibility() != 8) {
                LayoutParams actionBarOverlayLayout$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                int v8 = view0.getMeasuredWidth();
                int v9 = view0.getMeasuredHeight();
                int v10 = actionBarOverlayLayout$LayoutParams0.leftMargin + v5;
                int v11 = actionBarOverlayLayout$LayoutParams0.topMargin + v6;
                view0.layout(v10, v11, v8 + v10, v9 + v11);
            }
        }
    }

    @Override  // android.view.View
    public final void onMeasure(int v, int v1) {
        int v5;
        this.h();
        this.measureChildWithMargins(this.d, v, 0, v1, 0);
        LayoutParams actionBarOverlayLayout$LayoutParams0 = (LayoutParams)this.d.getLayoutParams();
        int v2 = Math.max(0, this.d.getMeasuredWidth() + actionBarOverlayLayout$LayoutParams0.leftMargin + actionBarOverlayLayout$LayoutParams0.rightMargin);
        int v3 = Math.max(0, this.d.getMeasuredHeight() + actionBarOverlayLayout$LayoutParams0.topMargin + actionBarOverlayLayout$LayoutParams0.bottomMargin);
        int v4 = View.combineMeasuredStates(0, this.d.getMeasuredState());
        boolean z = (this.getWindowSystemUiVisibility() & 0x100) != 0;
        if(z) {
            v5 = this.a;
        }
        else {
            v5 = this.d.getVisibility() == 8 ? 0 : this.d.getMeasuredHeight();
        }
        Rect rect0 = this.o;
        rect0.set(this.m);
        this.s = this.q;
        if(this.g || z) {
        label_21:
            Insets insets0 = Insets.b(this.s.f(), this.s.h() + v5, this.s.g(), this.s.e());
            Builder windowInsetsCompat$Builder0 = new Builder(this.s);
            windowInsetsCompat$Builder0.b(insets0);
            this.s = windowInsetsCompat$Builder0.a();
        }
        else {
            ViewCompat.b(this.B, ActionBarOverlayLayout.D, this.p);
            if(!this.p.equals(ActionBarOverlayLayout.E) != 0) {
                rect0.top += v5;
                rect0.bottom = rect0.bottom;
                this.s = this.s.i(0, v5, 0, 0);
                goto label_25;
            }
            goto label_21;
        }
    label_25:
        ActionBarOverlayLayout.b(this.c, rect0, true);
        if(!this.t.equals(this.s)) {
            this.t = this.s;
            ViewCompat.c(this.c, this.s);
        }
        this.measureChildWithMargins(this.c, v, 0, v1, 0);
        LayoutParams actionBarOverlayLayout$LayoutParams1 = (LayoutParams)this.c.getLayoutParams();
        int v6 = Math.max(v2, this.c.getMeasuredWidth() + actionBarOverlayLayout$LayoutParams1.leftMargin + actionBarOverlayLayout$LayoutParams1.rightMargin);
        int v7 = Math.max(v3, this.c.getMeasuredHeight() + actionBarOverlayLayout$LayoutParams1.topMargin + actionBarOverlayLayout$LayoutParams1.bottomMargin);
        int v8 = View.combineMeasuredStates(v4, this.c.getMeasuredState());
        int v9 = this.getPaddingLeft();
        int v10 = this.getPaddingRight();
        int v11 = this.getPaddingTop();
        int v12 = Math.max(this.getPaddingBottom() + v11 + v7, this.getSuggestedMinimumHeight());
        this.setMeasuredDimension(View.resolveSizeAndState(Math.max(v10 + v9 + v6, this.getSuggestedMinimumWidth()), v, v8), View.resolveSizeAndState(v12, v1, v8 << 16));
    }

    @Override  // android.view.ViewGroup
    public final boolean onNestedFling(View view0, float f, float f1, boolean z) {
        if(this.i && z) {
            this.v.fling(0, 0, 0, ((int)f1), 0, 0, 0x80000000, 0x7FFFFFFF);
            if(this.v.getFinalY() > this.d.getHeight()) {
                this.d();
                ((androidx.appcompat.widget.ActionBarOverlayLayout.3)this.z).run();
            }
            else {
                this.d();
                ((androidx.appcompat.widget.ActionBarOverlayLayout.2)this.y).run();
            }
            this.j = true;
            return true;
        }
        return false;
    }

    @Override  // android.view.ViewGroup
    public final boolean onNestedPreFling(View view0, float f, float f1) {
        return false;
    }

    @Override  // android.view.ViewGroup
    public final void onNestedPreScroll(View view0, int v, int v1, int[] arr_v) {
    }

    @Override  // androidx.core.view.NestedScrollingParent2
    public final void onNestedPreScroll(View view0, int v, int v1, int[] arr_v, int v2) {
    }

    @Override  // android.view.ViewGroup
    public final void onNestedScroll(View view0, int v, int v1, int v2, int v3) {
        int v4 = this.k + v1;
        this.k = v4;
        this.setActionBarHideOffset(v4);
    }

    @Override  // androidx.core.view.NestedScrollingParent2
    public final void onNestedScroll(View view0, int v, int v1, int v2, int v3, int v4) {
        if(v4 == 0) {
            this.onNestedScroll(view0, v, v1, v2, v3);
        }
    }

    @Override  // android.view.ViewGroup
    public final void onNestedScrollAccepted(View view0, View view1, int v) {
        this.A.a = v;
        this.k = this.getActionBarHideOffset();
        this.d();
        ActionBarVisibilityCallback actionBarOverlayLayout$ActionBarVisibilityCallback0 = this.u;
        if(actionBarOverlayLayout$ActionBarVisibilityCallback0 != null) {
            ViewPropertyAnimatorCompatSet viewPropertyAnimatorCompatSet0 = ((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).s;
            if(viewPropertyAnimatorCompatSet0 != null) {
                viewPropertyAnimatorCompatSet0.a();
                ((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).s = null;
            }
        }
    }

    @Override  // androidx.core.view.NestedScrollingParent2
    public final void onNestedScrollAccepted(View view0, View view1, int v, int v1) {
        if(v1 == 0) {
            this.onNestedScrollAccepted(view0, view1, v);
        }
    }

    @Override  // android.view.ViewGroup
    public final boolean onStartNestedScroll(View view0, View view1, int v) {
        return (v & 2) == 0 || this.d.getVisibility() != 0 ? false : this.i;
    }

    @Override  // androidx.core.view.NestedScrollingParent2
    public final boolean onStartNestedScroll(View view0, View view1, int v, int v1) {
        return v1 == 0 && this.onStartNestedScroll(view0, view1, v);
    }

    @Override  // android.view.ViewGroup
    public final void onStopNestedScroll(View view0) {
        if(this.i && !this.j) {
            if(this.k <= this.d.getHeight()) {
                this.d();
                this.postDelayed(this.y, 600L);
            }
            else {
                this.d();
                this.postDelayed(this.z, 600L);
            }
        }
        ActionBarVisibilityCallback actionBarOverlayLayout$ActionBarVisibilityCallback0 = this.u;
        if(actionBarOverlayLayout$ActionBarVisibilityCallback0 != null) {
            actionBarOverlayLayout$ActionBarVisibilityCallback0.getClass();
        }
    }

    @Override  // androidx.core.view.NestedScrollingParent2
    public final void onStopNestedScroll(View view0, int v) {
        if(v == 0) {
            this.onStopNestedScroll(view0);
        }
    }

    @Override  // android.view.View
    public final void onWindowSystemUiVisibilityChanged(int v) {
        super.onWindowSystemUiVisibilityChanged(v);
        this.h();
        int v1 = this.l ^ v;
        this.l = v;
        int v2 = (v & 0x100) == 0 ? 0 : 1;
        ActionBarVisibilityCallback actionBarOverlayLayout$ActionBarVisibilityCallback0 = this.u;
        if(actionBarOverlayLayout$ActionBarVisibilityCallback0 != null) {
            ((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).o = v2 ^ 1;
            if((v & 4) == 0 || v2 == 0) {
                if(((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).p) {
                    ((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).p = false;
                    ((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).t(true);
                }
            }
            else if(!((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).p) {
                ((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).p = true;
                ((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).t(true);
            }
        }
        if((v1 & 0x100) != 0 && this.u != null) {
            ViewCompat.A(this);
        }
    }

    @Override  // android.view.View
    public final void onWindowVisibilityChanged(int v) {
        super.onWindowVisibilityChanged(v);
        this.b = v;
        ActionBarVisibilityCallback actionBarOverlayLayout$ActionBarVisibilityCallback0 = this.u;
        if(actionBarOverlayLayout$ActionBarVisibilityCallback0 != null) {
            ((WindowDecorActionBar)actionBarOverlayLayout$ActionBarVisibilityCallback0).n = v;
        }
    }

    public void setActionBarHideOffset(int v) {
        this.d();
        int v1 = Math.max(0, Math.min(v, this.d.getHeight()));
        this.d.setTranslationY(((float)(-v1)));
    }

    public void setActionBarVisibilityCallback(ActionBarVisibilityCallback actionBarOverlayLayout$ActionBarVisibilityCallback0) {
        this.u = actionBarOverlayLayout$ActionBarVisibilityCallback0;
        if(this.getWindowToken() != null) {
            ((WindowDecorActionBar)this.u).n = this.b;
            int v = this.l;
            if(v != 0) {
                this.onWindowSystemUiVisibilityChanged(v);
                ViewCompat.A(this);
            }
        }
    }

    public void setHasNonEmbeddedTabs(boolean z) {
        this.h = z;
    }

    public void setHideOnContentScrollEnabled(boolean z) {
        if(z != this.i) {
            this.i = z;
            if(!z) {
                this.d();
                this.setActionBarHideOffset(0);
            }
        }
    }

    public void setIcon(int v) {
        this.h();
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = (ToolbarWidgetWrapper)this.e;
        toolbarWidgetWrapper0.d = v == 0 ? null : AppCompatResources.a(toolbarWidgetWrapper0.a.getContext(), v);
        toolbarWidgetWrapper0.g();
    }

    public void setIcon(Drawable drawable0) {
        this.h();
        ((ToolbarWidgetWrapper)this.e).d = drawable0;
        ((ToolbarWidgetWrapper)this.e).g();
    }

    public void setLogo(int v) {
        this.h();
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = (ToolbarWidgetWrapper)this.e;
        toolbarWidgetWrapper0.e = v == 0 ? null : AppCompatResources.a(toolbarWidgetWrapper0.a.getContext(), v);
        toolbarWidgetWrapper0.g();
    }

    public void setOverlayMode(boolean z) {
        this.g = z;
    }

    public void setShowingForActionMode(boolean z) {
    }

    public void setUiOptions(int v) {
    }

    @Override  // androidx.appcompat.widget.DecorContentParent
    public void setWindowCallback(Window.Callback window$Callback0) {
        this.h();
        ((ToolbarWidgetWrapper)this.e).k = window$Callback0;
    }

    @Override  // androidx.appcompat.widget.DecorContentParent
    public void setWindowTitle(CharSequence charSequence0) {
        this.h();
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = (ToolbarWidgetWrapper)this.e;
        if(!toolbarWidgetWrapper0.g) {
            toolbarWidgetWrapper0.h = charSequence0;
            if((toolbarWidgetWrapper0.b & 8) != 0) {
                Toolbar toolbar0 = toolbarWidgetWrapper0.a;
                toolbar0.setTitle(charSequence0);
                if(toolbarWidgetWrapper0.g) {
                    ViewCompat.E(toolbar0.getRootView(), charSequence0);
                }
            }
        }
    }

    @Override  // android.view.ViewGroup
    public final boolean shouldDelayChildPressedState() {
        return false;
    }
}


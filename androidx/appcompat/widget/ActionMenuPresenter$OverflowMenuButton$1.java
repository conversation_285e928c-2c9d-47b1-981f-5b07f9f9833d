package androidx.appcompat.widget;

import android.view.View;
import androidx.appcompat.view.menu.ShowableListMenu;

class ActionMenuPresenter.OverflowMenuButton.1 extends ForwardingListener {
    public final OverflowMenuButton j;

    public ActionMenuPresenter.OverflowMenuButton.1(OverflowMenuButton actionMenuPresenter$OverflowMenuButton0, View view0) {
        this.j = actionMenuPresenter$OverflowMenuButton0;
        super(view0);
    }

    @Override  // androidx.appcompat.widget.ForwardingListener
    public final ShowableListMenu b() {
        OverflowPopup actionMenuPresenter$OverflowPopup0 = ActionMenuPresenter.this.t;
        return actionMenuPresenter$OverflowPopup0 == null ? null : actionMenuPresenter$OverflowPopup0.a();
    }

    @Override  // androidx.appcompat.widget.ForwardingListener
    public final boolean c() {
        ActionMenuPresenter.this.p();
        return true;
    }

    @Override  // androidx.appcompat.widget.ForwardingListener
    public final boolean d() {
        ActionMenuPresenter actionMenuPresenter0 = ActionMenuPresenter.this;
        if(actionMenuPresenter0.v != null) {
            return false;
        }
        actionMenuPresenter0.n();
        return true;
    }
}


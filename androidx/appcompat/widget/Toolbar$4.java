package androidx.appcompat.widget;

import android.view.View.OnClickListener;
import android.view.View;
import androidx.appcompat.view.menu.MenuItemImpl;

class Toolbar.4 implements View.OnClickListener {
    public final Toolbar a;

    public Toolbar.4(Toolbar toolbar0) {
        this.a = toolbar0;
    }

    @Override  // android.view.View$OnClickListener
    public final void onClick(View view0) {
        ExpandedActionViewMenuPresenter toolbar$ExpandedActionViewMenuPresenter0 = this.a.M;
        MenuItemImpl menuItemImpl0 = toolbar$ExpandedActionViewMenuPresenter0 == null ? null : toolbar$ExpandedActionViewMenuPresenter0.b;
        if(menuItemImpl0 != null) {
            menuItemImpl0.collapseActionView();
        }
    }
}


package androidx.appcompat.widget;

import android.view.MenuItem;
import androidx.appcompat.view.menu.MenuBuilder.Callback;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.core.view.MenuProvider;

class Toolbar.3 implements Callback {
    public final Toolbar a;

    public Toolbar.3(Toolbar toolbar0) {
        this.a = toolbar0;
    }

    @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
    public final boolean a(MenuBuilder menuBuilder0, MenuItem menuItem0) {
        Callback menuBuilder$Callback0 = this.a.O;
        return menuBuilder$Callback0 != null && menuBuilder$Callback0.a(menuBuilder0, menuItem0);
    }

    @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
    public final void b(MenuBuilder menuBuilder0) {
        Toolbar toolbar0 = this.a;
        ActionMenuPresenter actionMenuPresenter0 = toolbar0.a.t;
        if(actionMenuPresenter0 == null || !actionMenuPresenter0.o()) {
            for(Object object0: toolbar0.G.b) {
                ((MenuProvider)object0).onPrepareMenu(menuBuilder0);
            }
        }
        Callback menuBuilder$Callback0 = toolbar0.O;
        if(menuBuilder$Callback0 != null) {
            menuBuilder$Callback0.b(menuBuilder0);
        }
    }
}


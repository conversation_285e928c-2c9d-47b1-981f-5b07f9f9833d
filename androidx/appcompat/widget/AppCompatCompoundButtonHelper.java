package androidx.appcompat.widget;

import android.content.res.ColorStateList;
import android.content.res.Resources.NotFoundException;
import android.content.res.TypedArray;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.CompoundButton;
import androidx.appcompat.R.styleable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.core.view.ViewCompat;
import androidx.core.widget.CompoundButtonCompat;

class AppCompatCompoundButtonHelper {
    public final CompoundButton a;
    public ColorStateList b;
    public PorterDuff.Mode c;
    public boolean d;
    public boolean e;
    public boolean f;

    public AppCompatCompoundButtonHelper(CompoundButton compoundButton0) {
        this.b = null;
        this.c = null;
        this.d = false;
        this.e = false;
        this.a = compoundButton0;
    }

    public final void a() {
        CompoundButton compoundButton0 = this.a;
        Drawable drawable0 = CompoundButtonCompat.a(compoundButton0);
        if(drawable0 != null && (this.d || this.e)) {
            Drawable drawable1 = DrawableCompat.n(drawable0).mutate();
            if(this.d) {
                DrawableCompat.k(drawable1, this.b);
            }
            if(this.e) {
                DrawableCompat.l(drawable1, this.c);
            }
            if(drawable1.isStateful()) {
                drawable1.setState(compoundButton0.getDrawableState());
            }
            compoundButton0.setButtonDrawable(drawable1);
        }
    }

    public final void b(AttributeSet attributeSet0, int v) {
        CompoundButton compoundButton0 = this.a;
        TintTypedArray tintTypedArray0 = TintTypedArray.e(compoundButton0.getContext(), attributeSet0, R.styleable.m, v, 0);
        TypedArray typedArray0 = tintTypedArray0.b;
        ViewCompat.B(compoundButton0, compoundButton0.getContext(), R.styleable.m, attributeSet0, tintTypedArray0.b, v);
        try {
            if(typedArray0.hasValue(1)) {
                int v2 = typedArray0.getResourceId(1, 0);
                if(v2 != 0) {
                    try {
                        compoundButton0.setButtonDrawable(AppCompatResources.a(compoundButton0.getContext(), v2));
                        goto label_14;
                    }
                    catch(Resources.NotFoundException unused_ex) {
                    }
                }
                goto label_10;
            }
            else {
            label_10:
                if(typedArray0.hasValue(0)) {
                    int v3 = typedArray0.getResourceId(0, 0);
                    if(v3 != 0) {
                        compoundButton0.setButtonDrawable(AppCompatResources.a(compoundButton0.getContext(), v3));
                    }
                }
            }
        label_14:
            if(typedArray0.hasValue(2)) {
                CompoundButtonCompat.d(compoundButton0, tintTypedArray0.a(2));
            }
            if(typedArray0.hasValue(3)) {
                CompoundButtonCompat.e(compoundButton0, DrawableUtils.c(typedArray0.getInt(3, -1), null));
            }
        }
        finally {
            tintTypedArray0.f();
        }
    }
}


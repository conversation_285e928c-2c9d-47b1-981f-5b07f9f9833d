package androidx.appcompat.widget;

import android.app.Activity;
import android.text.Selection;
import android.text.Spannable;
import android.view.DragEvent;
import android.view.View;
import android.widget.TextView;
import androidx.core.view.ContentInfoCompat.Builder;
import androidx.core.view.ViewCompat;

abstract class AppCompatReceiveContentHelper.OnDropApi24Impl {
    public static boolean a(DragEvent dragEvent0, TextView textView0, Activity activity0) {
        activity0.requestDragAndDropPermissions(dragEvent0);
        int v = textView0.getOffsetForPosition(dragEvent0.getX(), dragEvent0.getY());
        textView0.beginBatchEdit();
        try {
            Selection.setSelection(((Spannable)textView0.getText()), v);
            ViewCompat.x(textView0, new Builder(dragEvent0.getClipData(), 3).a());
            return true;
        }
        finally {
            textView0.endBatchEdit();
        }
    }

    public static boolean b(Drag<PERSON>vent dragEvent0, View view0, Activity activity0) {
        activity0.requestDragAndDropPermissions(dragEvent0);
        ViewCompat.x(view0, new Builder(dragEvent0.getClipData(), 3).a());
        return true;
    }
}


package androidx.appcompat.widget;

import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.drawable.Drawable;

class ActionBarBackgroundDrawable extends Drawable {
    public final ActionBarContainer a;

    public ActionBarBackgroundDrawable(ActionBarContainer actionBarContainer0) {
        this.a = actionBarContainer0;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void draw(Canvas canvas0) {
        ActionBarContainer actionBarContainer0 = this.a;
        if(actionBarContainer0.g) {
            Drawable drawable0 = actionBarContainer0.f;
            if(drawable0 != null) {
                drawable0.draw(canvas0);
            }
        }
        else {
            Drawable drawable1 = actionBarContainer0.d;
            if(drawable1 != null) {
                drawable1.draw(canvas0);
            }
            Drawable drawable2 = actionBarContainer0.e;
            if(drawable2 != null && actionBarContainer0.h) {
                drawable2.draw(canvas0);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getOpacity() {
        return 0;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void getOutline(Outline outline0) {
        ActionBarContainer actionBarContainer0 = this.a;
        if(!actionBarContainer0.g) {
            Drawable drawable0 = actionBarContainer0.d;
            if(drawable0 != null) {
                drawable0.getOutline(outline0);
            }
        }
        else if(actionBarContainer0.f != null) {
            actionBarContainer0.d.getOutline(outline0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setAlpha(int v) {
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setColorFilter(ColorFilter colorFilter0) {
    }
}


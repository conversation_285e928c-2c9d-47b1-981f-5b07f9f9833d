package androidx.appcompat.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;

public class ActivityChooserView.InnerLayout extends LinearLayout {
    public static final int[] a;

    static {
        ActivityChooserView.InnerLayout.a = new int[]{0x10100D4};
    }

    public ActivityChooserView.InnerLayout(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0);
        TintTypedArray tintTypedArray0 = new TintTypedArray(context0, context0.obtainStyledAttributes(attributeSet0, ActivityChooserView.InnerLayout.a));
        this.setBackgroundDrawable(tintTypedArray0.b(0));
        tintTypedArray0.f();
    }
}


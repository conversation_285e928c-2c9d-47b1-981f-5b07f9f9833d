package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.util.AttributeSet;
import android.view.View;
import androidx.appcompat.R.styleable;
import androidx.core.view.ViewCompat;

class AppCompatBackgroundHelper {
    public final View a;
    public final AppCompatDrawableManager b;
    public int c;
    public TintInfo d;
    public TintInfo e;
    public TintInfo f;

    public AppCompatBackgroundHelper(View view0) {
        this.c = -1;
        this.a = view0;
        this.b = AppCompatDrawableManager.a();
    }

    public final void a() {
        View view0 = this.a;
        Drawable drawable0 = view0.getBackground();
        if(drawable0 != null) {
            int v = Build.VERSION.SDK_INT;
            if(v <= 21) {
                if(v == 21) {
                label_8:
                    if(this.f == null) {
                        this.f = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
                    }
                    TintInfo tintInfo0 = this.f;
                    tintInfo0.a = null;
                    tintInfo0.d = false;
                    tintInfo0.b = null;
                    tintInfo0.c = false;
                    ColorStateList colorStateList0 = ViewCompat.i(view0);
                    if(colorStateList0 != null) {
                        tintInfo0.d = true;
                        tintInfo0.a = colorStateList0;
                    }
                    PorterDuff.Mode porterDuff$Mode0 = ViewCompat.j(view0);
                    if(porterDuff$Mode0 != null) {
                        tintInfo0.c = true;
                        tintInfo0.b = porterDuff$Mode0;
                    }
                    if(tintInfo0.d || tintInfo0.c) {
                        AppCompatDrawableManager.e(drawable0, tintInfo0, view0.getDrawableState());
                        return;
                    }
                }
            }
            else if(this.d != null) {
                goto label_8;
            }
            TintInfo tintInfo1 = this.e;
            if(tintInfo1 != null) {
                AppCompatDrawableManager.e(drawable0, tintInfo1, view0.getDrawableState());
                return;
            }
            TintInfo tintInfo2 = this.d;
            if(tintInfo2 != null) {
                AppCompatDrawableManager.e(drawable0, tintInfo2, view0.getDrawableState());
            }
        }
    }

    public final ColorStateList b() {
        return this.e == null ? null : this.e.a;
    }

    public final PorterDuff.Mode c() {
        return this.e == null ? null : this.e.b;
    }

    public final void d(AttributeSet attributeSet0, int v) {
        ColorStateList colorStateList0;
        View view0 = this.a;
        TintTypedArray tintTypedArray0 = TintTypedArray.e(view0.getContext(), attributeSet0, R.styleable.z, v, 0);
        TypedArray typedArray0 = tintTypedArray0.b;
        Context context0 = this.a.getContext();
        ViewCompat.B(this.a, context0, R.styleable.z, attributeSet0, tintTypedArray0.b, v);
        try {
            if(typedArray0.hasValue(0)) {
                this.c = typedArray0.getResourceId(0, -1);
                AppCompatDrawableManager appCompatDrawableManager0 = this.b;
                Context context1 = view0.getContext();
                synchronized(appCompatDrawableManager0) {
                    colorStateList0 = appCompatDrawableManager0.a.i(this.c, context1);
                }
                if(colorStateList0 != null) {
                    this.g(colorStateList0);
                }
            }
            if(typedArray0.hasValue(1)) {
                ViewCompat.F(view0, tintTypedArray0.a(1));
            }
            if(typedArray0.hasValue(2)) {
                ViewCompat.G(view0, DrawableUtils.c(typedArray0.getInt(2, -1), null));
            }
        }
        finally {
            tintTypedArray0.f();
        }
    }

    public final void e() {
        this.c = -1;
        this.g(null);
        this.a();
    }

    public final void f(int v) {
        ColorStateList colorStateList0;
        this.c = v;
        AppCompatDrawableManager appCompatDrawableManager0 = this.b;
        if(appCompatDrawableManager0 == null) {
            colorStateList0 = null;
        }
        else {
            Context context0 = this.a.getContext();
            synchronized(appCompatDrawableManager0) {
                colorStateList0 = appCompatDrawableManager0.a.i(v, context0);
            }
        }
        this.g(colorStateList0);
        this.a();
    }

    public final void g(ColorStateList colorStateList0) {
        if(colorStateList0 == null) {
            this.d = null;
        }
        else {
            if(this.d == null) {
                this.d = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
            }
            this.d.a = colorStateList0;
            this.d.d = true;
        }
        this.a();
    }

    public final void h(ColorStateList colorStateList0) {
        if(this.e == null) {
            this.e = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        this.e.a = colorStateList0;
        this.e.d = true;
        this.a();
    }

    public final void i(PorterDuff.Mode porterDuff$Mode0) {
        if(this.e == null) {
            this.e = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        this.e.b = porterDuff$Mode0;
        this.e.c = true;
        this.a();
    }
}


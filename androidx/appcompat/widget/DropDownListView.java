package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import androidx.appcompat.graphics.drawable.DrawableWrapperCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.core.widget.ListViewAutoScrollHelper;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

class DropDownListView extends ListView {
    static abstract class Api21Impl {
        public static void a(View view0, float f, float f1) {
            view0.drawableHotspotChanged(f, f1);
        }
    }

    static abstract class Api30Impl {
        public static final Method a;
        public static final Method b;
        public static final Method c;
        public static final boolean d;

        static {
            try {
                Class[] arr_class = new Class[5];
                Class class0 = Integer.TYPE;
                arr_class[0] = class0;
                arr_class[1] = View.class;
                arr_class[2] = Boolean.TYPE;
                arr_class[3] = Float.TYPE;
                arr_class[4] = Float.TYPE;
                Method method0 = AbsListView.class.getDeclaredMethod("positionSelector", arr_class);
                Api30Impl.a = method0;
                method0.setAccessible(true);
                Method method1 = AdapterView.class.getDeclaredMethod("setSelectedPositionInt", class0);
                Api30Impl.b = method1;
                method1.setAccessible(true);
                Method method2 = AdapterView.class.getDeclaredMethod("setNextSelectedPositionInt", class0);
                Api30Impl.c = method2;
                method2.setAccessible(true);
                Api30Impl.d = true;
            }
            catch(NoSuchMethodException noSuchMethodException0) {
                noSuchMethodException0.printStackTrace();
            }
        }
    }

    static abstract class Api33Impl {
        public static boolean a(AbsListView absListView0) {
            return absListView0.isSelectedChildViewEnabled();
        }

        public static void b(AbsListView absListView0, boolean z) {
            absListView0.setSelectedChildViewEnabled(z);
        }
    }

    static class GateKeeperDrawable extends DrawableWrapperCompat {
        public boolean b;

        @Override  // androidx.appcompat.graphics.drawable.DrawableWrapperCompat
        public final void draw(Canvas canvas0) {
            if(this.b) {
                super.draw(canvas0);
            }
        }

        @Override  // androidx.appcompat.graphics.drawable.DrawableWrapperCompat
        public final void setHotspot(float f, float f1) {
            if(this.b) {
                super.setHotspot(f, f1);
            }
        }

        @Override  // androidx.appcompat.graphics.drawable.DrawableWrapperCompat
        public final void setHotspotBounds(int v, int v1, int v2, int v3) {
            if(this.b) {
                super.setHotspotBounds(v, v1, v2, v3);
            }
        }

        // 去混淆评级： 低(20)
        @Override  // android.graphics.drawable.Drawable
        public final boolean setState(int[] arr_v) {
            return this.b ? this.a.setState(arr_v) : false;
        }

        // 去混淆评级： 低(20)
        @Override  // androidx.appcompat.graphics.drawable.DrawableWrapperCompat
        public final boolean setVisible(boolean z, boolean z1) {
            return this.b ? super.setVisible(z, z1) : false;
        }
    }

    static abstract class PreApi33Impl {
        public static final Field a;

        static {
            Field field0 = null;
            try {
                field0 = AbsListView.class.getDeclaredField("mIsChildViewEnabled");
                field0.setAccessible(true);
            }
            catch(NoSuchFieldException noSuchFieldException0) {
                noSuchFieldException0.printStackTrace();
            }
            PreApi33Impl.a = field0;
        }
    }

    class ResolveHoverRunnable implements Runnable {
        public final DropDownListView a;

        @Override
        public final void run() {
            this.a.l = null;
            this.a.drawableStateChanged();
        }
    }

    public final Rect a;
    public int b;
    public int c;
    public int d;
    public int e;
    public int f;
    public GateKeeperDrawable g;
    public boolean h;
    public final boolean i;
    public boolean j;
    public ListViewAutoScrollHelper k;
    public ResolveHoverRunnable l;

    public DropDownListView(Context context0, boolean z) {
        super(context0, null, 0x7F04020B);  // attr:dropDownListViewStyle
        this.a = new Rect();
        this.b = 0;
        this.c = 0;
        this.d = 0;
        this.e = 0;
        this.i = z;
        this.setCacheColorHint(0);
    }

    public int a(int v, int v1) {
        int v2 = this.getListPaddingTop();
        int v3 = this.getListPaddingBottom();
        int v4 = this.getDividerHeight();
        Drawable drawable0 = this.getDivider();
        ListAdapter listAdapter0 = this.getAdapter();
        if(listAdapter0 == null) {
            return v2 + v3;
        }
        int v5 = v2 + v3;
        if(v4 <= 0 || drawable0 == null) {
            v4 = 0;
        }
        int v6 = listAdapter0.getCount();
        View view0 = null;
        int v8 = 0;
        for(int v7 = 0; v7 < v6; ++v7) {
            int v9 = listAdapter0.getItemViewType(v7);
            if(v9 != v8) {
                view0 = null;
                v8 = v9;
            }
            view0 = listAdapter0.getView(v7, view0, this);
            ViewGroup.LayoutParams viewGroup$LayoutParams0 = view0.getLayoutParams();
            if(viewGroup$LayoutParams0 == null) {
                viewGroup$LayoutParams0 = this.generateDefaultLayoutParams();
                view0.setLayoutParams(viewGroup$LayoutParams0);
            }
            view0.measure(v, (viewGroup$LayoutParams0.height <= 0 ? 0 : View.MeasureSpec.makeMeasureSpec(viewGroup$LayoutParams0.height, 0x40000000)));
            view0.forceLayout();
            if(v7 > 0) {
                v5 += v4;
            }
            v5 += view0.getMeasuredHeight();
            if(v5 >= v1) {
                return v1;
            }
        }
        return v5;
    }

    public boolean b(MotionEvent motionEvent0, int v) {
        boolean z1;
        boolean z3;
        int v1 = motionEvent0.getActionMasked();
        boolean z = false;
        switch(v1) {
            case 1: {
                z1 = false;
                goto label_8;
            }
            case 2: {
                z1 = true;
            label_8:
                int v2 = motionEvent0.findPointerIndex(v);
                if(v2 >= 0) {
                    int v3 = (int)motionEvent0.getX(v2);
                    int v4 = (int)motionEvent0.getY(v2);
                    int v5 = this.pointToPosition(v3, v4);
                    if(v5 == -1) {
                        z = true;
                    }
                    else {
                        View view0 = this.getChildAt(v5 - this.getFirstVisiblePosition());
                        this.j = true;
                        int v6 = Build.VERSION.SDK_INT;
                        Api21Impl.a(this, ((float)v3), ((float)v4));
                        if(!this.isPressed()) {
                            this.setPressed(true);
                        }
                        this.layoutChildren();
                        int v7 = this.f;
                        if(v7 != -1) {
                            View view1 = this.getChildAt(v7 - this.getFirstVisiblePosition());
                            if(view1 != null && view1 != view0 && view1.isPressed()) {
                                view1.setPressed(false);
                            }
                        }
                        this.f = v5;
                        Api21Impl.a(view0, ((float)v3) - ((float)view0.getLeft()), ((float)v4) - ((float)view0.getTop()));
                        if(!view0.isPressed()) {
                            view0.setPressed(true);
                        }
                        Drawable drawable0 = this.getSelector();
                        boolean z2 = drawable0 != null && v5 != -1;
                        if(z2) {
                            drawable0.setVisible(false, false);
                        }
                        int v8 = view0.getLeft();
                        int v9 = view0.getTop();
                        int v10 = view0.getRight();
                        int v11 = view0.getBottom();
                        Rect rect0 = this.a;
                        rect0.set(v8, v9, v10, v11);
                        rect0.left -= this.b;
                        rect0.top -= this.c;
                        rect0.right += this.d;
                        rect0.bottom += this.e;
                        if(v6 >= 33) {
                            z3 = Api33Impl.a(this);
                        }
                        else {
                            Field field0 = PreApi33Impl.a;
                            if(field0 == null) {
                                z3 = false;
                            }
                            else {
                                try {
                                    z3 = field0.getBoolean(this);
                                }
                                catch(IllegalAccessException illegalAccessException0) {
                                    illegalAccessException0.printStackTrace();
                                    z3 = false;
                                }
                            }
                        }
                        if(view0.isEnabled() != z3) {
                            if(Build.VERSION.SDK_INT >= 33) {
                                Api33Impl.b(this, !z3);
                            }
                            else {
                                Field field1 = PreApi33Impl.a;
                                if(field1 != null) {
                                    try {
                                        field1.set(this, Boolean.valueOf(!z3));
                                    }
                                    catch(IllegalAccessException illegalAccessException1) {
                                        illegalAccessException1.printStackTrace();
                                    }
                                }
                            }
                            this.refreshDrawableState();
                        }
                        if(z2) {
                            float f = rect0.exactCenterX();
                            float f1 = rect0.exactCenterY();
                            drawable0.setVisible(this.getVisibility() == 0, false);
                            DrawableCompat.g(drawable0, f, f1);
                        }
                        Drawable drawable1 = this.getSelector();
                        if(drawable1 != null && v5 != -1) {
                            DrawableCompat.g(drawable1, ((float)v3), ((float)v4));
                        }
                        GateKeeperDrawable dropDownListView$GateKeeperDrawable0 = this.g;
                        if(dropDownListView$GateKeeperDrawable0 != null) {
                            dropDownListView$GateKeeperDrawable0.b = false;
                        }
                        this.refreshDrawableState();
                        if(v1 == 1) {
                            this.performItemClick(view0, v5, this.getItemIdAtPosition(v5));
                        }
                        z1 = true;
                    }
                }
                else {
                    z1 = false;
                }
                break;
            }
            case 3: {
                z1 = false;
                break;
            }
            default: {
                z1 = true;
            }
        }
        if(!z1 || z) {
            this.j = false;
            this.setPressed(false);
            this.drawableStateChanged();
            View view2 = this.getChildAt(this.f - this.getFirstVisiblePosition());
            if(view2 != null) {
                view2.setPressed(false);
            }
        }
        if(z1) {
            if(this.k == null) {
                this.k = new ListViewAutoScrollHelper(this);
            }
            this.k.p = true;
            this.k.onTouch(this, motionEvent0);
            return true;
        }
        ListViewAutoScrollHelper listViewAutoScrollHelper0 = this.k;
        if(listViewAutoScrollHelper0 != null) {
            if(listViewAutoScrollHelper0.p) {
                listViewAutoScrollHelper0.d();
            }
            listViewAutoScrollHelper0.p = false;
        }
        return false;
    }

    @Override  // android.widget.ListView
    public final void dispatchDraw(Canvas canvas0) {
        Rect rect0 = this.a;
        if(!rect0.isEmpty()) {
            Drawable drawable0 = this.getSelector();
            if(drawable0 != null) {
                drawable0.setBounds(rect0);
                drawable0.draw(canvas0);
            }
        }
        super.dispatchDraw(canvas0);
    }

    @Override  // android.widget.AbsListView
    public final void drawableStateChanged() {
        if(this.l != null) {
            return;
        }
        super.drawableStateChanged();
        GateKeeperDrawable dropDownListView$GateKeeperDrawable0 = this.g;
        if(dropDownListView$GateKeeperDrawable0 != null) {
            dropDownListView$GateKeeperDrawable0.b = true;
        }
        Drawable drawable0 = this.getSelector();
        if(drawable0 != null && this.j && this.isPressed()) {
            drawable0.setState(this.getDrawableState());
        }
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.ViewGroup
    public boolean hasFocus() {
        return this.i || super.hasFocus();
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.View
    public boolean hasWindowFocus() {
        return this.i || super.hasWindowFocus();
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.View
    public boolean isFocused() {
        return this.i || super.isFocused();
    }

    // 去混淆评级： 低(30)
    @Override  // android.view.View
    public boolean isInTouchMode() {
        return this.i && this.h || super.isInTouchMode();
    }

    @Override  // android.widget.ListView
    public final void onDetachedFromWindow() {
        this.l = null;
        super.onDetachedFromWindow();
    }

    @Override  // android.view.View
    public boolean onHoverEvent(MotionEvent motionEvent0) {
        int v = Build.VERSION.SDK_INT;
        if(v < 26) {
            return super.onHoverEvent(motionEvent0);
        }
        int v1 = motionEvent0.getActionMasked();
        if(v1 == 10 && this.l == null) {
            ResolveHoverRunnable dropDownListView$ResolveHoverRunnable0 = new ResolveHoverRunnable(this);
            this.l = dropDownListView$ResolveHoverRunnable0;
            this.post(dropDownListView$ResolveHoverRunnable0);
        }
        boolean z = super.onHoverEvent(motionEvent0);
        if(v1 != 7 && v1 != 9) {
            this.setSelection(-1);
            return z;
        }
        int v2 = this.pointToPosition(((int)motionEvent0.getX()), ((int)motionEvent0.getY()));
        if(v2 != -1 && v2 != this.getSelectedItemPosition()) {
            View view0 = this.getChildAt(v2 - this.getFirstVisiblePosition());
            if(view0.isEnabled()) {
                this.requestFocus();
                if(v < 30 || !Api30Impl.d) {
                    this.setSelectionFromTop(v2, view0.getTop() - this.getTop());
                }
                else {
                    try {
                        Api30Impl.a.invoke(this, v2, view0, Boolean.FALSE, -1, -1);
                        Api30Impl.b.invoke(this, v2);
                        Api30Impl.c.invoke(this, v2);
                    }
                    catch(IllegalAccessException illegalAccessException0) {
                        illegalAccessException0.printStackTrace();
                    }
                    catch(InvocationTargetException invocationTargetException0) {
                        invocationTargetException0.printStackTrace();
                    }
                }
            }
            Drawable drawable0 = this.getSelector();
            if(drawable0 != null && this.j && this.isPressed()) {
                drawable0.setState(this.getDrawableState());
            }
        }
        return z;
    }

    @Override  // android.widget.AbsListView
    public boolean onTouchEvent(MotionEvent motionEvent0) {
        if(motionEvent0.getAction() == 0) {
            this.f = this.pointToPosition(((int)motionEvent0.getX()), ((int)motionEvent0.getY()));
        }
        ResolveHoverRunnable dropDownListView$ResolveHoverRunnable0 = this.l;
        if(dropDownListView$ResolveHoverRunnable0 != null) {
            dropDownListView$ResolveHoverRunnable0.a.l = null;
            dropDownListView$ResolveHoverRunnable0.a.removeCallbacks(dropDownListView$ResolveHoverRunnable0);
        }
        return super.onTouchEvent(motionEvent0);
    }

    public void setListSelectionHidden(boolean z) {
        this.h = z;
    }

    @Override  // android.widget.AbsListView
    public void setSelector(Drawable drawable0) {
        GateKeeperDrawable dropDownListView$GateKeeperDrawable0;
        if(drawable0 == null) {
            dropDownListView$GateKeeperDrawable0 = null;
        }
        else {
            dropDownListView$GateKeeperDrawable0 = new GateKeeperDrawable(drawable0);  // 初始化器: Landroidx/appcompat/graphics/drawable/DrawableWrapperCompat;-><init>(Landroid/graphics/drawable/Drawable;)V
            dropDownListView$GateKeeperDrawable0.b = true;
        }
        this.g = dropDownListView$GateKeeperDrawable0;
        super.setSelector(dropDownListView$GateKeeperDrawable0);
        Rect rect0 = new Rect();
        if(drawable0 != null) {
            drawable0.getPadding(rect0);
        }
        this.b = rect0.left;
        this.c = rect0.top;
        this.d = rect0.right;
        this.e = rect0.bottom;
    }
}


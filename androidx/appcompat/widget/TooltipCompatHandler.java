package androidx.appcompat.widget;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.Resources;
import android.graphics.Rect;
import android.util.DisplayMetrics;
import android.view.MotionEvent;
import android.view.View.OnAttachStateChangeListener;
import android.view.View.OnHoverListener;
import android.view.View.OnLongClickListener;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewParent;
import android.view.WindowManager.LayoutParams;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityManager;
import androidx.core.view.ViewConfigurationCompat;

class TooltipCompatHandler implements View.OnAttachStateChangeListener, View.OnHoverListener, View.OnLongClickListener {
    public final View a;
    public final CharSequence b;
    public final int c;
    public final b d;
    public final b e;
    public int f;
    public int g;
    public TooltipPopup h;
    public boolean i;
    public boolean j;
    public static TooltipCompatHandler k;
    public static TooltipCompatHandler l;

    public TooltipCompatHandler(View view0, CharSequence charSequence0) {
        this.d = new b(this, 0);
        this.e = new b(this, 1);
        this.a = view0;
        this.b = charSequence0;
        this.c = ViewConfigurationCompat.c(ViewConfiguration.get(view0.getContext()));
        this.j = true;
        view0.setOnLongClickListener(this);
        view0.setOnHoverListener(this);
    }

    public final void a() {
        View view0 = this.a;
        if(TooltipCompatHandler.l == this) {
            TooltipCompatHandler.l = null;
            TooltipPopup tooltipPopup0 = this.h;
            if(tooltipPopup0 != null) {
                View view1 = tooltipPopup0.b;
                if(view1.getParent() != null) {
                    ((WindowManager)tooltipPopup0.a.getSystemService("window")).removeView(view1);
                }
                this.h = null;
                this.j = true;
                view0.removeOnAttachStateChangeListener(this);
            }
        }
        if(TooltipCompatHandler.k == this) {
            TooltipCompatHandler.b(null);
        }
        view0.removeCallbacks(this.e);
    }

    public static void b(TooltipCompatHandler tooltipCompatHandler0) {
        TooltipCompatHandler tooltipCompatHandler1 = TooltipCompatHandler.k;
        if(tooltipCompatHandler1 != null) {
            tooltipCompatHandler1.a.removeCallbacks(tooltipCompatHandler1.d);
        }
        TooltipCompatHandler.k = tooltipCompatHandler0;
        if(tooltipCompatHandler0 != null) {
            long v = (long)ViewConfiguration.getLongPressTimeout();
            tooltipCompatHandler0.a.postDelayed(tooltipCompatHandler0.d, v);
        }
    }

    public final void c(boolean z) {
        long v15;
        long v14;
        long v13;
        int v5;
        int v4;
        View view0 = this.a;
        if(!view0.isAttachedToWindow()) {
            return;
        }
        TooltipCompatHandler.b(null);
        TooltipCompatHandler tooltipCompatHandler0 = TooltipCompatHandler.l;
        if(tooltipCompatHandler0 != null) {
            tooltipCompatHandler0.a();
        }
        TooltipCompatHandler.l = this;
        this.i = z;
        TooltipPopup tooltipPopup0 = new TooltipPopup(view0.getContext());
        this.h = tooltipPopup0;
        int v = this.f;
        int v1 = this.g;
        boolean z1 = this.i;
        View view1 = tooltipPopup0.b;
        ViewParent viewParent0 = view1.getParent();
        Context context0 = tooltipPopup0.a;
        if(viewParent0 != null && view1.getParent() != null) {
            ((WindowManager)context0.getSystemService("window")).removeView(view1);
        }
        tooltipPopup0.c.setText(this.b);
        WindowManager.LayoutParams windowManager$LayoutParams0 = tooltipPopup0.d;
        windowManager$LayoutParams0.token = view0.getApplicationWindowToken();
        int v2 = context0.getResources().getDimensionPixelOffset(0x7F0706B7);  // dimen:tooltip_precise_anchor_threshold
        if(view0.getWidth() < v2) {
            v = view0.getWidth() / 2;
        }
        if(view0.getHeight() >= v2) {
            int v3 = context0.getResources().getDimensionPixelOffset(0x7F0706B6);  // dimen:tooltip_precise_anchor_extra_offset
            v4 = v1 + v3;
            v5 = v1 - v3;
        }
        else {
            v4 = view0.getHeight();
            v5 = 0;
        }
        windowManager$LayoutParams0.gravity = 49;
        int v6 = context0.getResources().getDimensionPixelOffset((z1 ? 0x7F0706BA : 0x7F0706B9));  // dimen:tooltip_y_offset_touch
        View view2 = view0.getRootView();
        ViewGroup.LayoutParams viewGroup$LayoutParams0 = view2.getLayoutParams();
        if(!(viewGroup$LayoutParams0 instanceof WindowManager.LayoutParams) || ((WindowManager.LayoutParams)viewGroup$LayoutParams0).type != 2) {
            for(Context context1 = view0.getContext(); context1 instanceof ContextWrapper; context1 = ((ContextWrapper)context1).getBaseContext()) {
                if(context1 instanceof Activity) {
                    view2 = ((Activity)context1).getWindow().getDecorView();
                    break;
                }
            }
        }
        if(view2 != null) {
            Rect rect0 = tooltipPopup0.e;
            view2.getWindowVisibleDisplayFrame(rect0);
            if(rect0.left < 0 && rect0.top < 0) {
                Resources resources0 = context0.getResources();
                int v7 = resources0.getIdentifier("status_bar_height", "dimen", "android");
                int v8 = v7 == 0 ? 0 : resources0.getDimensionPixelSize(v7);
                DisplayMetrics displayMetrics0 = resources0.getDisplayMetrics();
                rect0.set(0, v8, displayMetrics0.widthPixels, displayMetrics0.heightPixels);
            }
            view2.getLocationOnScreen(tooltipPopup0.g);
            view0.getLocationOnScreen(tooltipPopup0.f);
            int v9 = tooltipPopup0.f[0] - tooltipPopup0.g[0];
            tooltipPopup0.f[0] = v9;
            tooltipPopup0.f[1] -= tooltipPopup0.g[1];
            windowManager$LayoutParams0.x = v9 + v - view2.getWidth() / 2;
            view1.measure(0, 0);
            int v10 = view1.getMeasuredHeight();
            int v11 = v5 + tooltipPopup0.f[1] - v6 - v10;
            int v12 = tooltipPopup0.f[1] + v4 + v6;
            if(!z1) {
                windowManager$LayoutParams0.y = v10 + v12 <= rect0.height() ? v12 : v11;
            }
            else if(v11 >= 0) {
                windowManager$LayoutParams0.y = v11;
            }
            else {
                windowManager$LayoutParams0.y = v12;
            }
        }
        ((WindowManager)context0.getSystemService("window")).addView(view1, windowManager$LayoutParams0);
        view0.addOnAttachStateChangeListener(this);
        if(this.i) {
            v13 = 2500L;
        }
        else {
            if((view0.getWindowSystemUiVisibility() & 1) == 1) {
                v14 = (long)ViewConfiguration.getLongPressTimeout();
                v15 = 3000L;
            }
            else {
                v14 = (long)ViewConfiguration.getLongPressTimeout();
                v15 = 15000L;
            }
            v13 = v15 - v14;
        }
        view0.removeCallbacks(this.e);
        view0.postDelayed(this.e, v13);
    }

    @Override  // android.view.View$OnHoverListener
    public final boolean onHover(View view0, MotionEvent motionEvent0) {
        if(this.h != null && this.i) {
            return false;
        }
        View view1 = this.a;
        AccessibilityManager accessibilityManager0 = (AccessibilityManager)view1.getContext().getSystemService("accessibility");
        if(accessibilityManager0.isEnabled() && accessibilityManager0.isTouchExplorationEnabled()) {
            return false;
        }
        switch(motionEvent0.getAction()) {
            case 7: {
                if(view1.isEnabled() && this.h == null) {
                    int v = (int)motionEvent0.getX();
                    int v1 = (int)motionEvent0.getY();
                    if(this.j || (Math.abs(v - this.f) > this.c || Math.abs(v1 - this.g) > this.c)) {
                        this.f = v;
                        this.g = v1;
                        this.j = false;
                        TooltipCompatHandler.b(this);
                    }
                }
                return false;
            }
            case 10: {
                this.j = true;
                this.a();
                return false;
            }
            default: {
                return false;
            }
        }
    }

    @Override  // android.view.View$OnLongClickListener
    public final boolean onLongClick(View view0) {
        this.f = view0.getWidth() / 2;
        this.g = view0.getHeight() / 2;
        this.c(true);
        return true;
    }

    @Override  // android.view.View$OnAttachStateChangeListener
    public final void onViewAttachedToWindow(View view0) {
    }

    @Override  // android.view.View$OnAttachStateChangeListener
    public final void onViewDetachedFromWindow(View view0) {
        this.a();
    }
}


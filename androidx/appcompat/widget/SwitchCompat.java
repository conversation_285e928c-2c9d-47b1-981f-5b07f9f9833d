package androidx.appcompat.widget;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.PorterDuff.Mode;
import android.graphics.Rect;
import android.graphics.Region.Op;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.text.InputFilter;
import android.text.Layout.Alignment;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.TransformationMethod;
import android.util.AttributeSet;
import android.util.Property;
import android.view.ActionMode.Callback;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.CompoundButton;
import androidx.appcompat.R.styleable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.appcompat.text.AllCapsTransformationMethod;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.core.view.ViewCompat;
import androidx.core.widget.TextViewCompat;
import androidx.emoji2.text.EmojiCompat.InitCallback;
import androidx.emoji2.text.EmojiCompat;
import java.lang.ref.WeakReference;

public class SwitchCompat extends CompoundButton {
    static class EmojiCompatInitCallback extends InitCallback {
        public final WeakReference a;

        public EmojiCompatInitCallback(SwitchCompat switchCompat0) {
            this.a = new WeakReference(switchCompat0);
        }

        @Override  // androidx.emoji2.text.EmojiCompat$InitCallback
        public final void a() {
            SwitchCompat switchCompat0 = (SwitchCompat)this.a.get();
            if(switchCompat0 != null) {
                switchCompat0.c();
            }
        }

        @Override  // androidx.emoji2.text.EmojiCompat$InitCallback
        public final void b() {
            SwitchCompat switchCompat0 = (SwitchCompat)this.a.get();
            if(switchCompat0 != null) {
                switchCompat0.c();
            }
        }
    }

    public int A;
    public int B;
    public int C;
    public int D;
    public int E;
    public int F;
    public int G;
    public boolean H;
    public final TextPaint I;
    public final ColorStateList J;
    public StaticLayout K;
    public StaticLayout L;
    public final AllCapsTransformationMethod M;
    public ObjectAnimator N;
    public AppCompatEmojiTextHelper O;
    public EmojiCompatInitCallback P;
    public final Rect Q;
    public static final Property R;
    public static final int[] S;
    public Drawable a;
    public ColorStateList b;
    public PorterDuff.Mode c;
    public boolean d;
    public boolean e;
    public Drawable f;
    public ColorStateList g;
    public PorterDuff.Mode h;
    public boolean i;
    public boolean j;
    public int k;
    public int l;
    public int m;
    public boolean n;
    public CharSequence o;
    public CharSequence p;
    public CharSequence q;
    public CharSequence r;
    public boolean s;
    public int t;
    public final int u;
    public float v;
    public float w;
    public final VelocityTracker x;
    public final int y;
    public float z;

    static {
        SwitchCompat.R = new Property(Float.class, "thumbPos") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            @Override  // android.util.Property
            public final Object get(Object object0) {
                return ((SwitchCompat)object0).z;
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                ((SwitchCompat)object0).setThumbPosition(((float)(((Float)object1))));
            }
        };
        SwitchCompat.S = new int[]{0x10100A0};
    }

    public SwitchCompat(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0, 0x7F04057A);  // attr:switchStyle
        Typeface typeface0;
        this.b = null;
        this.c = null;
        this.d = false;
        this.e = false;
        this.g = null;
        this.h = null;
        this.i = false;
        this.j = false;
        this.x = VelocityTracker.obtain();
        boolean z = true;
        this.H = true;
        this.Q = new Rect();
        ThemeUtils.a(this, this.getContext());
        TextPaint textPaint0 = new TextPaint(1);
        this.I = textPaint0;
        textPaint0.density = this.getResources().getDisplayMetrics().density;
        TintTypedArray tintTypedArray0 = TintTypedArray.e(context0, attributeSet0, R.styleable.v, 0x7F04057A, 0);  // attr:switchStyle
        ViewCompat.B(this, context0, R.styleable.v, attributeSet0, tintTypedArray0.b, 0x7F04057A);  // attr:switchStyle
        Drawable drawable0 = tintTypedArray0.b(2);
        this.a = drawable0;
        if(drawable0 != null) {
            drawable0.setCallback(this);
        }
        Drawable drawable1 = tintTypedArray0.b(11);
        this.f = drawable1;
        if(drawable1 != null) {
            drawable1.setCallback(this);
        }
        TypedArray typedArray0 = tintTypedArray0.b;
        this.setTextOnInternal(typedArray0.getText(0));
        this.setTextOffInternal(typedArray0.getText(1));
        this.s = typedArray0.getBoolean(3, true);
        this.k = typedArray0.getDimensionPixelSize(8, 0);
        this.l = typedArray0.getDimensionPixelSize(5, 0);
        this.m = typedArray0.getDimensionPixelSize(6, 0);
        this.n = typedArray0.getBoolean(4, false);
        ColorStateList colorStateList0 = tintTypedArray0.a(9);
        if(colorStateList0 != null) {
            this.b = colorStateList0;
            this.d = true;
        }
        PorterDuff.Mode porterDuff$Mode0 = DrawableUtils.c(typedArray0.getInt(10, -1), null);
        if(this.c != porterDuff$Mode0) {
            this.c = porterDuff$Mode0;
            this.e = true;
        }
        if(this.d || this.e) {
            this.a();
        }
        ColorStateList colorStateList1 = tintTypedArray0.a(12);
        if(colorStateList1 != null) {
            this.g = colorStateList1;
            this.i = true;
        }
        PorterDuff.Mode porterDuff$Mode1 = DrawableUtils.c(typedArray0.getInt(13, -1), null);
        if(this.h != porterDuff$Mode1) {
            this.h = porterDuff$Mode1;
            this.j = true;
        }
        if(this.i || this.j) {
            this.b();
        }
        int v = typedArray0.getResourceId(7, 0);
        if(v != 0) {
            TypedArray typedArray1 = context0.obtainStyledAttributes(v, R.styleable.w);
            TintTypedArray tintTypedArray1 = new TintTypedArray(context0, typedArray1);
            ColorStateList colorStateList2 = tintTypedArray1.a(3);
            this.J = colorStateList2 == null ? this.getTextColors() : colorStateList2;
            int v1 = typedArray1.getDimensionPixelSize(0, 0);
            if(v1 != 0 && ((float)v1) != textPaint0.getTextSize()) {
                textPaint0.setTextSize(((float)v1));
                this.requestLayout();
            }
            int v2 = typedArray1.getInt(1, -1);
            int v3 = typedArray1.getInt(2, -1);
            switch(v2) {
                case 1: {
                    typeface0 = Typeface.SANS_SERIF;
                    break;
                }
                case 2: {
                    typeface0 = Typeface.SERIF;
                    break;
                }
                case 3: {
                    typeface0 = Typeface.MONOSPACE;
                    break;
                }
                default: {
                    typeface0 = null;
                }
            }
            float f = 0.0f;
            if(v3 > 0) {
                Typeface typeface1 = typeface0 == null ? Typeface.defaultFromStyle(v3) : Typeface.create(typeface0, v3);
                this.setSwitchTypeface(typeface1);
                int v4 = ~(typeface1 == null ? 0 : typeface1.getStyle()) & v3;
                if((v4 & 1) == 0) {
                    z = false;
                }
                textPaint0.setFakeBoldText(z);
                if((2 & v4) != 0) {
                    f = -0.25f;
                }
                textPaint0.setTextSkewX(f);
            }
            else {
                textPaint0.setFakeBoldText(false);
                textPaint0.setTextSkewX(0.0f);
                this.setSwitchTypeface(typeface0);
            }
            if(typedArray1.getBoolean(14, false)) {
                Context context1 = this.getContext();
                AllCapsTransformationMethod allCapsTransformationMethod0 = new AllCapsTransformationMethod();  // 初始化器: Ljava/lang/Object;-><init>()V
                allCapsTransformationMethod0.a = context1.getResources().getConfiguration().locale;
                this.M = allCapsTransformationMethod0;
            }
            else {
                this.M = null;
            }
            this.setTextOnInternal(this.o);
            this.setTextOffInternal(this.q);
            tintTypedArray1.f();
        }
        new AppCompatTextHelper(this).f(attributeSet0, 0x7F04057A);  // attr:switchStyle
        tintTypedArray0.f();
        ViewConfiguration viewConfiguration0 = ViewConfiguration.get(context0);
        this.u = viewConfiguration0.getScaledTouchSlop();
        this.y = viewConfiguration0.getScaledMinimumFlingVelocity();
        this.getEmojiTextViewHelper().a(attributeSet0, 0x7F04057A);  // attr:switchStyle
        this.refreshDrawableState();
        this.setChecked(this.isChecked());
    }

    public final void a() {
        Drawable drawable0 = this.a;
        if(drawable0 != null && (this.d || this.e)) {
            Drawable drawable1 = DrawableCompat.n(drawable0).mutate();
            this.a = drawable1;
            if(this.d) {
                DrawableCompat.k(drawable1, this.b);
            }
            if(this.e) {
                DrawableCompat.l(this.a, this.c);
            }
            if(this.a.isStateful()) {
                this.a.setState(this.getDrawableState());
            }
        }
    }

    public final void b() {
        Drawable drawable0 = this.f;
        if(drawable0 != null && (this.i || this.j)) {
            Drawable drawable1 = DrawableCompat.n(drawable0).mutate();
            this.f = drawable1;
            if(this.i) {
                DrawableCompat.k(drawable1, this.g);
            }
            if(this.j) {
                DrawableCompat.l(this.f, this.h);
            }
            if(this.f.isStateful()) {
                this.f.setState(this.getDrawableState());
            }
        }
    }

    public final void c() {
        this.setTextOnInternal(this.o);
        this.setTextOffInternal(this.q);
        this.requestLayout();
    }

    public final void d() {
        if(this.P == null && this.O.b.b() && EmojiCompat.k != null) {
            EmojiCompat emojiCompat0 = EmojiCompat.a();
            switch(emojiCompat0.b()) {
                case 0: 
                case 3: {
                    EmojiCompatInitCallback switchCompat$EmojiCompatInitCallback0 = new EmojiCompatInitCallback(this);
                    this.P = switchCompat$EmojiCompatInitCallback0;
                    emojiCompat0.i(switchCompat$EmojiCompatInitCallback0);
                    break;
                }
            }
        }
    }

    @Override  // android.view.View
    public final void draw(Canvas canvas0) {
        int v12;
        int v7;
        int v = this.D;
        int v1 = this.E;
        int v2 = this.F;
        int v3 = this.G;
        int v4 = this.getThumbOffset() + v;
        Rect rect0 = this.a == null ? DrawableUtils.c : DrawableUtils.b(this.a);
        Drawable drawable0 = this.f;
        Rect rect1 = this.Q;
        if(drawable0 != null) {
            drawable0.getPadding(rect1);
            int v5 = rect1.left;
            v4 += v5;
            if(rect0 == null) {
                v7 = v1;
                v12 = v3;
            }
            else {
                int v6 = rect0.left;
                if(v6 > v5) {
                    v += v6 - v5;
                }
                v7 = rect0.top <= rect1.top ? v1 : rect0.top - rect1.top + v1;
                int v8 = rect0.right;
                int v9 = rect1.right;
                if(v8 > v9) {
                    v2 -= v8 - v9;
                }
                int v10 = rect0.bottom;
                int v11 = rect1.bottom;
                v12 = v10 > v11 ? v3 - (v10 - v11) : v3;
            }
            this.f.setBounds(v, v7, v2, v12);
        }
        Drawable drawable1 = this.a;
        if(drawable1 != null) {
            drawable1.getPadding(rect1);
            int v13 = v4 - rect1.left;
            int v14 = v4 + this.C + rect1.right;
            this.a.setBounds(v13, v1, v14, v3);
            Drawable drawable2 = this.getBackground();
            if(drawable2 != null) {
                DrawableCompat.h(drawable2, v13, v1, v14, v3);
            }
        }
        super.draw(canvas0);
    }

    @Override  // android.widget.CompoundButton
    public final void drawableHotspotChanged(float f, float f1) {
        super.drawableHotspotChanged(f, f1);
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.g(drawable0, f, f1);
        }
        Drawable drawable1 = this.f;
        if(drawable1 != null) {
            DrawableCompat.g(drawable1, f, f1);
        }
    }

    @Override  // android.widget.CompoundButton
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        int[] arr_v = this.getDrawableState();
        Drawable drawable0 = this.a;
        boolean z = drawable0 == null || !drawable0.isStateful() ? false : drawable0.setState(arr_v);
        Drawable drawable1 = this.f;
        if(drawable1 != null && drawable1.isStateful()) {
            z |= drawable1.setState(arr_v);
        }
        if(z) {
            this.invalidate();
        }
    }

    @Override  // android.widget.CompoundButton
    public int getCompoundPaddingLeft() {
        if(this.getLayoutDirection() == 1) {
            int v = super.getCompoundPaddingLeft() + this.A;
            return TextUtils.isEmpty(this.getText()) ? v : v + this.m;
        }
        return super.getCompoundPaddingLeft();
    }

    @Override  // android.widget.CompoundButton
    public int getCompoundPaddingRight() {
        if(this.getLayoutDirection() == 1) {
            return super.getCompoundPaddingRight();
        }
        int v = super.getCompoundPaddingRight() + this.A;
        return TextUtils.isEmpty(this.getText()) ? v : v + this.m;
    }

    @Override  // android.widget.TextView
    public ActionMode.Callback getCustomSelectionActionModeCallback() {
        return TextViewCompat.j(super.getCustomSelectionActionModeCallback());
    }

    private AppCompatEmojiTextHelper getEmojiTextViewHelper() {
        if(this.O == null) {
            this.O = new AppCompatEmojiTextHelper(this);
        }
        return this.O;
    }

    public boolean getShowText() {
        return this.s;
    }

    public boolean getSplitTrack() {
        return this.n;
    }

    public int getSwitchMinWidth() {
        return this.l;
    }

    public int getSwitchPadding() {
        return this.m;
    }

    private boolean getTargetCheckedState() {
        return this.z > 0.5f;
    }

    public CharSequence getTextOff() {
        return this.q;
    }

    public CharSequence getTextOn() {
        return this.o;
    }

    public Drawable getThumbDrawable() {
        return this.a;
    }

    private int getThumbOffset() {
        return this.getLayoutDirection() == 1 ? ((int)((1.0f - this.z) * ((float)this.getThumbScrollRange()) + 0.5f)) : ((int)(this.z * ((float)this.getThumbScrollRange()) + 0.5f));
    }

    public final float getThumbPosition() {
        return this.z;
    }

    private int getThumbScrollRange() {
        Drawable drawable0 = this.f;
        if(drawable0 != null) {
            Rect rect0 = this.Q;
            drawable0.getPadding(rect0);
            Drawable drawable1 = this.a;
            if(drawable1 != null) {
                Rect rect1 = DrawableUtils.b(drawable1);
                return this.A - this.C - rect0.left - rect0.right - rect1.left - rect1.right;
            }
            return this.A - this.C - rect0.left - rect0.right - DrawableUtils.c.left - DrawableUtils.c.right;
        }
        return 0;
    }

    public int getThumbTextPadding() {
        return this.k;
    }

    public ColorStateList getThumbTintList() {
        return this.b;
    }

    public PorterDuff.Mode getThumbTintMode() {
        return this.c;
    }

    public Drawable getTrackDrawable() {
        return this.f;
    }

    public ColorStateList getTrackTintList() {
        return this.g;
    }

    public PorterDuff.Mode getTrackTintMode() {
        return this.h;
    }

    @Override  // android.widget.CompoundButton
    public final void jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState();
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.jumpToCurrentState();
        }
        Drawable drawable1 = this.f;
        if(drawable1 != null) {
            drawable1.jumpToCurrentState();
        }
        if(this.N != null && this.N.isStarted()) {
            this.N.end();
            this.N = null;
        }
    }

    @Override  // android.widget.CompoundButton
    public final int[] onCreateDrawableState(int v) {
        int[] arr_v = super.onCreateDrawableState(v + 1);
        if(this.isChecked()) {
            View.mergeDrawableStates(arr_v, SwitchCompat.S);
        }
        return arr_v;
    }

    @Override  // android.widget.CompoundButton
    public final void onDraw(Canvas canvas0) {
        int v4;
        super.onDraw(canvas0);
        Drawable drawable0 = this.f;
        Rect rect0 = this.Q;
        if(drawable0 == null) {
            rect0.setEmpty();
        }
        else {
            drawable0.getPadding(rect0);
        }
        int v = this.E + rect0.top;
        int v1 = this.G - rect0.bottom;
        Drawable drawable1 = this.a;
        if(drawable0 != null) {
            if(!this.n || drawable1 == null) {
                drawable0.draw(canvas0);
            }
            else {
                Rect rect1 = DrawableUtils.b(drawable1);
                drawable1.copyBounds(rect0);
                rect0.left += rect1.left;
                rect0.right -= rect1.right;
                int v2 = canvas0.save();
                canvas0.clipRect(rect0, Region.Op.DIFFERENCE);
                drawable0.draw(canvas0);
                canvas0.restoreToCount(v2);
            }
        }
        int v3 = canvas0.save();
        if(drawable1 != null) {
            drawable1.draw(canvas0);
        }
        StaticLayout staticLayout0 = this.getTargetCheckedState() ? this.K : this.L;
        if(staticLayout0 != null) {
            int[] arr_v = this.getDrawableState();
            ColorStateList colorStateList0 = this.J;
            TextPaint textPaint0 = this.I;
            if(colorStateList0 != null) {
                textPaint0.setColor(colorStateList0.getColorForState(arr_v, 0));
            }
            textPaint0.drawableState = arr_v;
            if(drawable1 == null) {
                v4 = this.getWidth();
            }
            else {
                Rect rect2 = drawable1.getBounds();
                v4 = rect2.left + rect2.right;
            }
            canvas0.translate(((float)(v4 / 2 - staticLayout0.getWidth() / 2)), ((float)((v + v1) / 2 - staticLayout0.getHeight() / 2)));
            staticLayout0.draw(canvas0);
        }
        canvas0.restoreToCount(v3);
    }

    @Override  // android.view.View
    public final void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent0) {
        super.onInitializeAccessibilityEvent(accessibilityEvent0);
        accessibilityEvent0.setClassName("android.widget.Switch");
    }

    @Override  // android.view.View
    public final void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo accessibilityNodeInfo0) {
        super.onInitializeAccessibilityNodeInfo(accessibilityNodeInfo0);
        accessibilityNodeInfo0.setClassName("android.widget.Switch");
        if(Build.VERSION.SDK_INT < 30) {
            CharSequence charSequence0 = this.isChecked() ? this.o : this.q;
            if(!TextUtils.isEmpty(charSequence0)) {
                CharSequence charSequence1 = accessibilityNodeInfo0.getText();
                if(TextUtils.isEmpty(charSequence1)) {
                    accessibilityNodeInfo0.setText(charSequence0);
                    return;
                }
                StringBuilder stringBuilder0 = new StringBuilder();
                stringBuilder0.append(charSequence1);
                stringBuilder0.append(' ');
                stringBuilder0.append(charSequence0);
                accessibilityNodeInfo0.setText(stringBuilder0);
            }
        }
    }

    @Override  // android.widget.TextView
    public final void onLayout(boolean z, int v, int v1, int v2, int v3) {
        int v9;
        int v8;
        int v7;
        int v6;
        int v5;
        super.onLayout(z, v, v1, v2, v3);
        int v4 = 0;
        if(this.a == null) {
            v5 = 0;
        }
        else {
            Drawable drawable0 = this.f;
            Rect rect0 = this.Q;
            if(drawable0 == null) {
                rect0.setEmpty();
            }
            else {
                drawable0.getPadding(rect0);
            }
            Rect rect1 = DrawableUtils.b(this.a);
            v5 = Math.max(0, rect1.left - rect0.left);
            v4 = Math.max(0, rect1.right - rect0.right);
        }
        if(this.getLayoutDirection() == 1) {
            v6 = this.getPaddingLeft() + v5;
            v7 = this.A + v6 - v5 - v4;
        }
        else {
            v7 = this.getWidth() - this.getPaddingRight() - v4;
            v6 = v7 - this.A + v5 + v4;
        }
        switch(this.getGravity() & 0x70) {
            case 16: {
                int v10 = this.getPaddingTop();
                int v11 = (this.getHeight() + v10 - this.getPaddingBottom()) / 2 - this.B / 2;
                v9 = this.B + v11;
                v8 = v11;
                break;
            }
            case 80: {
                v9 = this.getHeight() - this.getPaddingBottom();
                v8 = v9 - this.B;
                break;
            }
            default: {
                v8 = this.getPaddingTop();
                v9 = this.B + v8;
            }
        }
        this.D = v6;
        this.E = v8;
        this.G = v9;
        this.F = v7;
    }

    @Override  // android.widget.TextView
    public final void onMeasure(int v, int v1) {
        int v6;
        int v4;
        int v3;
        int v2 = 0;
        if(this.s) {
            TextPaint textPaint0 = this.I;
            if(this.K == null) {
                this.K = new StaticLayout(this.p, textPaint0, (this.p == null ? 0 : ((int)Math.ceil(Layout.getDesiredWidth(this.p, textPaint0)))), Layout.Alignment.ALIGN_NORMAL, 1.0f, 0.0f, true);
            }
            if(this.L == null) {
                this.L = new StaticLayout(this.r, textPaint0, (this.r == null ? 0 : ((int)Math.ceil(Layout.getDesiredWidth(this.r, textPaint0)))), Layout.Alignment.ALIGN_NORMAL, 1.0f, 0.0f, true);
            }
        }
        Drawable drawable0 = this.a;
        Rect rect0 = this.Q;
        if(drawable0 == null) {
            v3 = 0;
            v4 = 0;
        }
        else {
            drawable0.getPadding(rect0);
            v3 = this.a.getIntrinsicWidth() - rect0.left - rect0.right;
            v4 = this.a.getIntrinsicHeight();
        }
        if(this.s) {
            int v5 = Math.max(this.K.getWidth(), this.L.getWidth());
            v6 = this.k * 2 + v5;
        }
        else {
            v6 = 0;
        }
        this.C = Math.max(v6, v3);
        Drawable drawable1 = this.f;
        if(drawable1 == null) {
            rect0.setEmpty();
        }
        else {
            drawable1.getPadding(rect0);
            v2 = this.f.getIntrinsicHeight();
        }
        int v7 = rect0.left;
        int v8 = rect0.right;
        Drawable drawable2 = this.a;
        if(drawable2 != null) {
            Rect rect1 = DrawableUtils.b(drawable2);
            v7 = Math.max(v7, rect1.left);
            v8 = Math.max(v8, rect1.right);
        }
        int v9 = Math.max(v2, v4);
        this.A = this.H ? Math.max(this.l, this.C * 2 + v7 + v8) : this.l;
        this.B = v9;
        super.onMeasure(v, v1);
        if(this.getMeasuredHeight() < v9) {
            this.setMeasuredDimension(this.getMeasuredWidthAndState(), v9);
        }
    }

    @Override  // android.view.View
    public final void onPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent0) {
        super.onPopulateAccessibilityEvent(accessibilityEvent0);
        CharSequence charSequence0 = this.isChecked() ? this.o : this.q;
        if(charSequence0 != null) {
            accessibilityEvent0.getText().add(charSequence0);
        }
    }

    @Override  // android.widget.TextView
    public final boolean onTouchEvent(MotionEvent motionEvent0) {
        boolean z2;
        float f4;
        VelocityTracker velocityTracker0 = this.x;
        velocityTracker0.addMovement(motionEvent0);
        int v = motionEvent0.getActionMasked();
        int v1 = this.u;
        if(v == 0) {
            float f9 = motionEvent0.getX();
            float f10 = motionEvent0.getY();
            if(this.isEnabled() && this.a != null) {
                int v3 = this.getThumbOffset();
                this.a.getPadding(this.Q);
                int v4 = this.D + v3 - v1;
                if(f9 > ((float)v4) && f9 < ((float)(this.C + v4 + this.Q.left + this.Q.right + v1)) && f10 > ((float)(this.E - v1)) && f10 < ((float)(this.G + v1))) {
                    this.t = 1;
                    this.v = f9;
                    this.w = f10;
                }
            }
        }
        else {
            float f = 0.0f;
        alab1:
            switch(v) {
                case 2: {
                    switch(this.t) {
                        case 1: {
                            break alab1;
                        }
                        case 2: {
                            goto label_44;
                        }
                    }
                    return super.onTouchEvent(motionEvent0);
                label_44:
                    float f2 = motionEvent0.getX();
                    int v2 = this.getThumbScrollRange();
                    float f3 = f2 - this.v;
                    if(v2 == 0) {
                        f4 = f3 > 0.0f ? 1.0f : -1.0f;
                    }
                    else {
                        f4 = f3 / ((float)v2);
                    }
                    if(this.getLayoutDirection() == 1) {
                        f4 = -f4;
                    }
                    float f5 = this.z;
                    float f6 = f4 + f5;
                    if(f6 >= 0.0f) {
                        f = f6 > 1.0f ? 1.0f : f6;
                    }
                    if(f != f5) {
                        this.v = f2;
                        this.setThumbPosition(f);
                    }
                    return true;
                }
                case 1: 
                case 3: {
                    if(this.t == 2) {
                        this.t = 0;
                        boolean z = motionEvent0.getAction() == 1 && this.isEnabled();
                        boolean z1 = this.isChecked();
                        if(z) {
                            velocityTracker0.computeCurrentVelocity(1000);
                            float f1 = velocityTracker0.getXVelocity();
                            if(Math.abs(f1) <= ((float)this.y)) {
                                z2 = this.getTargetCheckedState();
                            }
                            else if(this.getLayoutDirection() != 1) {
                                z2 = f1 > 0.0f;
                            }
                            else if(f1 < 0.0f) {
                                z2 = true;
                            }
                            else {
                                z2 = false;
                            }
                        }
                        else {
                            z2 = z1;
                        }
                        if(z2 != z1) {
                            this.playSoundEffect(0);
                        }
                        this.setChecked(z2);
                        MotionEvent motionEvent1 = MotionEvent.obtain(motionEvent0);
                        motionEvent1.setAction(3);
                        super.onTouchEvent(motionEvent1);
                        motionEvent1.recycle();
                        super.onTouchEvent(motionEvent0);
                        return true;
                    }
                    this.t = 0;
                    velocityTracker0.clear();
                    return super.onTouchEvent(motionEvent0);
                }
                default: {
                    return super.onTouchEvent(motionEvent0);
                }
            }
            float f7 = motionEvent0.getX();
            float f8 = motionEvent0.getY();
            if(Math.abs(f7 - this.v) > ((float)v1) || Math.abs(f8 - this.w) > ((float)v1)) {
                this.t = 2;
                this.getParent().requestDisallowInterceptTouchEvent(true);
                this.v = f7;
                this.w = f8;
                return true;
            }
        }
        return super.onTouchEvent(motionEvent0);
    }

    @Override  // android.widget.TextView
    public void setAllCaps(boolean z) {
        super.setAllCaps(z);
        this.getEmojiTextViewHelper().b(z);
    }

    @Override  // android.widget.CompoundButton
    public void setChecked(boolean z) {
        super.setChecked(z);
        boolean z1 = this.isChecked();
        if(!z1) {
            if(Build.VERSION.SDK_INT >= 30) {
                CharSequence charSequence1 = this.q;
                if(charSequence1 == null) {
                    charSequence1 = "OFF";
                }
                ViewCompat.N(this, charSequence1);
            }
        }
        else if(Build.VERSION.SDK_INT >= 30) {
            CharSequence charSequence0 = this.o;
            if(charSequence0 == null) {
                charSequence0 = "ON";
            }
            ViewCompat.N(this, charSequence0);
        }
        float f = 0.0f;
        if(this.getWindowToken() != null && this.isLaidOut()) {
            if(z1) {
                f = 1.0f;
            }
            ObjectAnimator objectAnimator0 = ObjectAnimator.ofFloat(this, SwitchCompat.R, new float[]{f});
            this.N = objectAnimator0;
            objectAnimator0.setDuration(0xFAL);
            this.N.setAutoCancel(true);
            this.N.start();
            return;
        }
        ObjectAnimator objectAnimator1 = this.N;
        if(objectAnimator1 != null) {
            objectAnimator1.cancel();
        }
        if(z1) {
            f = 1.0f;
        }
        this.setThumbPosition(f);
    }

    @Override  // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback actionMode$Callback0) {
        super.setCustomSelectionActionModeCallback(TextViewCompat.k(this, actionMode$Callback0));
    }

    public void setEmojiCompatEnabled(boolean z) {
        this.getEmojiTextViewHelper().c(z);
        this.setTextOnInternal(this.o);
        this.setTextOffInternal(this.q);
        this.requestLayout();
    }

    public final void setEnforceSwitchWidth(boolean z) {
        this.H = z;
        this.invalidate();
    }

    @Override  // android.widget.TextView
    public void setFilters(InputFilter[] arr_inputFilter) {
        super.setFilters(this.getEmojiTextViewHelper().b.a(arr_inputFilter));
    }

    public void setShowText(boolean z) {
        if(this.s != z) {
            this.s = z;
            this.requestLayout();
            if(z) {
                this.d();
            }
        }
    }

    public void setSplitTrack(boolean z) {
        this.n = z;
        this.invalidate();
    }

    public void setSwitchMinWidth(int v) {
        this.l = v;
        this.requestLayout();
    }

    public void setSwitchPadding(int v) {
        this.m = v;
        this.requestLayout();
    }

    public void setSwitchTypeface(Typeface typeface0) {
        TextPaint textPaint0 = this.I;
        if(textPaint0.getTypeface() != null && !textPaint0.getTypeface().equals(typeface0) || textPaint0.getTypeface() == null && typeface0 != null) {
            textPaint0.setTypeface(typeface0);
            this.requestLayout();
            this.invalidate();
        }
    }

    public void setTextOff(CharSequence charSequence0) {
        this.setTextOffInternal(charSequence0);
        this.requestLayout();
        if(!this.isChecked() && Build.VERSION.SDK_INT >= 30) {
            CharSequence charSequence1 = this.q;
            if(charSequence1 == null) {
                charSequence1 = "OFF";
            }
            ViewCompat.N(this, charSequence1);
        }
    }

    private void setTextOffInternal(CharSequence charSequence0) {
        this.q = charSequence0;
        TransformationMethod transformationMethod0 = this.getEmojiTextViewHelper().b.e(this.M);
        if(transformationMethod0 != null) {
            charSequence0 = transformationMethod0.getTransformation(charSequence0, this);
        }
        this.r = charSequence0;
        this.L = null;
        if(this.s) {
            this.d();
        }
    }

    public void setTextOn(CharSequence charSequence0) {
        this.setTextOnInternal(charSequence0);
        this.requestLayout();
        if(this.isChecked() && Build.VERSION.SDK_INT >= 30) {
            CharSequence charSequence1 = this.o;
            if(charSequence1 == null) {
                charSequence1 = "ON";
            }
            ViewCompat.N(this, charSequence1);
        }
    }

    private void setTextOnInternal(CharSequence charSequence0) {
        this.o = charSequence0;
        TransformationMethod transformationMethod0 = this.getEmojiTextViewHelper().b.e(this.M);
        if(transformationMethod0 != null) {
            charSequence0 = transformationMethod0.getTransformation(charSequence0, this);
        }
        this.p = charSequence0;
        this.K = null;
        if(this.s) {
            this.d();
        }
    }

    public void setThumbDrawable(Drawable drawable0) {
        Drawable drawable1 = this.a;
        if(drawable1 != null) {
            drawable1.setCallback(null);
        }
        this.a = drawable0;
        if(drawable0 != null) {
            drawable0.setCallback(this);
        }
        this.requestLayout();
    }

    public void setThumbPosition(float f) {
        this.z = f;
        this.invalidate();
    }

    public void setThumbResource(int v) {
        this.setThumbDrawable(AppCompatResources.a(this.getContext(), v));
    }

    public void setThumbTextPadding(int v) {
        this.k = v;
        this.requestLayout();
    }

    public void setThumbTintList(ColorStateList colorStateList0) {
        this.b = colorStateList0;
        this.d = true;
        this.a();
    }

    public void setThumbTintMode(PorterDuff.Mode porterDuff$Mode0) {
        this.c = porterDuff$Mode0;
        this.e = true;
        this.a();
    }

    public void setTrackDrawable(Drawable drawable0) {
        Drawable drawable1 = this.f;
        if(drawable1 != null) {
            drawable1.setCallback(null);
        }
        this.f = drawable0;
        if(drawable0 != null) {
            drawable0.setCallback(this);
        }
        this.requestLayout();
    }

    public void setTrackResource(int v) {
        this.setTrackDrawable(AppCompatResources.a(this.getContext(), v));
    }

    public void setTrackTintList(ColorStateList colorStateList0) {
        this.g = colorStateList0;
        this.i = true;
        this.b();
    }

    public void setTrackTintMode(PorterDuff.Mode porterDuff$Mode0) {
        this.h = porterDuff$Mode0;
        this.j = true;
        this.b();
    }

    @Override  // android.widget.CompoundButton
    public final void toggle() {
        this.setChecked(!this.isChecked());
    }

    @Override  // android.widget.CompoundButton
    public final boolean verifyDrawable(Drawable drawable0) {
        return super.verifyDrawable(drawable0) || drawable0 == this.a || drawable0 == this.f;
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RatingBar;

public class AppCompatRatingBar extends RatingBar {
    public final AppCompatProgressBarHelper a;

    public AppCompatRatingBar(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0, 0x7F040460);  // attr:ratingBarStyle
        ThemeUtils.a(this, this.getContext());
        AppCompatProgressBarHelper appCompatProgressBarHelper0 = new AppCompatProgressBarHelper(this);
        this.a = appCompatProgressBarHelper0;
        appCompatProgressBarHelper0.a(attributeSet0, 0x7F040460);  // attr:ratingBarStyle
    }

    @Override  // android.widget.RatingBar
    public final void onMeasure(int v, int v1) {
        synchronized(this) {
            super.onMeasure(v, v1);
            Bitmap bitmap0 = this.a.b;
            if(bitmap0 != null) {
                this.setMeasuredDimension(View.resolveSizeAndState(bitmap0.getWidth() * this.getNumStars(), v, 0), this.getMeasuredHeight());
            }
        }
    }
}


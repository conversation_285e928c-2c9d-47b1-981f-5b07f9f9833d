package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.RectF;
import android.os.Build.VERSION;
import android.text.Layout.Alignment;
import android.text.StaticLayout.Builder;
import android.text.StaticLayout;
import android.text.TextDirectionHeuristic;
import android.text.TextDirectionHeuristics;
import android.text.TextPaint;
import android.text.method.TransformationMethod;
import android.util.TypedValue;
import android.widget.TextView;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;

class AppCompatTextViewAutoSizeHelper {
    static abstract class Api23Impl {
        public static StaticLayout a(CharSequence charSequence0, Layout.Alignment layout$Alignment0, int v, int v1, TextView textView0, TextPaint textPaint0, Impl appCompatTextViewAutoSizeHelper$Impl0) {
            StaticLayout.Builder staticLayout$Builder0 = StaticLayout.Builder.obtain(charSequence0, 0, charSequence0.length(), textPaint0, v);
            StaticLayout.Builder staticLayout$Builder1 = staticLayout$Builder0.setAlignment(layout$Alignment0).setLineSpacing(textView0.getLineSpacingExtra(), textView0.getLineSpacingMultiplier()).setIncludePad(textView0.getIncludeFontPadding()).setBreakStrategy(textView0.getBreakStrategy()).setHyphenationFrequency(textView0.getHyphenationFrequency());
            if(v1 == -1) {
                v1 = 0x7FFFFFFF;
            }
            staticLayout$Builder1.setMaxLines(v1);
            try {
                appCompatTextViewAutoSizeHelper$Impl0.a(staticLayout$Builder0, textView0);
            }
            catch(ClassCastException unused_ex) {
            }
            return staticLayout$Builder0.build();
        }
    }

    static class Impl23 extends Impl {
        @Override  // androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl
        public void a(StaticLayout.Builder staticLayout$Builder0, TextView textView0) {
            staticLayout$Builder0.setTextDirection(((TextDirectionHeuristic)AppCompatTextViewAutoSizeHelper.e(textView0, "getTextDirectionHeuristic", TextDirectionHeuristics.FIRSTSTRONG_LTR)));
        }
    }

    static class Impl29 extends Impl23 {
        @Override  // androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23
        public void a(StaticLayout.Builder staticLayout$Builder0, TextView textView0) {
            staticLayout$Builder0.setTextDirection(textView0.getTextDirectionHeuristic());
        }

        @Override  // androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl
        public boolean b(TextView textView0) {
            return textView0.isHorizontallyScrollable();
        }
    }

    static class Impl {
        public void a(StaticLayout.Builder staticLayout$Builder0, TextView textView0) {
        }

        public boolean b(TextView textView0) {
            return ((Boolean)AppCompatTextViewAutoSizeHelper.e(textView0, "getHorizontallyScrolling", Boolean.FALSE)).booleanValue();
        }
    }

    public int a;
    public boolean b;
    public float c;
    public float d;
    public float e;
    public int[] f;
    public boolean g;
    public TextPaint h;
    public final TextView i;
    public final Context j;
    public final Impl k;
    public static final RectF l;
    public static final ConcurrentHashMap m;

    static {
        AppCompatTextViewAutoSizeHelper.l = new RectF();
        AppCompatTextViewAutoSizeHelper.m = new ConcurrentHashMap();
    }

    public AppCompatTextViewAutoSizeHelper(TextView textView0) {
        this.a = 0;
        this.b = false;
        this.c = -1.0f;
        this.d = -1.0f;
        this.e = -1.0f;
        this.f = new int[0];
        this.g = false;
        this.i = textView0;
        this.j = textView0.getContext();
        int v = Build.VERSION.SDK_INT;
        if(v >= 29) {
            this.k = new Impl29();
            return;
        }
        if(v >= 23) {
            this.k = new Impl23();
            return;
        }
        this.k = new Impl();
    }

    public final void a() {
        if(!this.f()) {
            return;
        }
        if(this.b) {
            if(this.i.getMeasuredHeight() > 0 && this.i.getMeasuredWidth() > 0) {
                int v = this.k.b(this.i) ? 0x100000 : this.i.getMeasuredWidth() - this.i.getTotalPaddingLeft() - this.i.getTotalPaddingRight();
                int v1 = this.i.getHeight() - this.i.getCompoundPaddingBottom() - this.i.getCompoundPaddingTop();
                if(v > 0 && v1 > 0) {
                    RectF rectF0 = AppCompatTextViewAutoSizeHelper.l;
                    synchronized(rectF0) {
                        rectF0.setEmpty();
                        rectF0.right = (float)v;
                        rectF0.bottom = (float)v1;
                        float f = (float)this.c(rectF0);
                        if(f != this.i.getTextSize()) {
                            this.g(f, 0);
                        }
                    }
                    this.b = true;
                    return;
                }
            }
            return;
        }
        this.b = true;
    }

    public static int[] b(int[] arr_v) {
        if(arr_v.length == 0) {
            return arr_v;
        }
        Arrays.sort(arr_v);
        ArrayList arrayList0 = new ArrayList();
        for(int v1 = 0; v1 < arr_v.length; ++v1) {
            int v2 = arr_v[v1];
            if(v2 > 0 && Collections.binarySearch(arrayList0, v2) < 0) {
                arrayList0.add(v2);
            }
        }
        if(arr_v.length == arrayList0.size()) {
            return arr_v;
        }
        int v3 = arrayList0.size();
        int[] arr_v1 = new int[v3];
        for(int v = 0; v < v3; ++v) {
            arr_v1[v] = (int)(((Integer)arrayList0.get(v)));
        }
        return arr_v1;
    }

    public final int c(RectF rectF0) {
        StaticLayout staticLayout0;
        int v = this.f.length;
        if(v == 0) {
            throw new IllegalStateException("No available text sizes to choose from.");
        }
        int v1 = v - 1;
        int v2 = 1;
        int v3 = 0;
        while(v2 <= v1) {
            int v4 = (v2 + v1) / 2;
            int v5 = this.f[v4];
            TextView textView0 = this.i;
            CharSequence charSequence0 = textView0.getText();
            TransformationMethod transformationMethod0 = textView0.getTransformationMethod();
            if(transformationMethod0 != null) {
                CharSequence charSequence1 = transformationMethod0.getTransformation(charSequence0, textView0);
                if(charSequence1 != null) {
                    charSequence0 = charSequence1;
                }
            }
            int v6 = textView0.getMaxLines();
            TextPaint textPaint0 = this.h;
            if(textPaint0 == null) {
                this.h = new TextPaint();
            }
            else {
                textPaint0.reset();
            }
            this.h.set(textView0.getPaint());
            this.h.setTextSize(((float)v5));
            Layout.Alignment layout$Alignment0 = (Layout.Alignment)AppCompatTextViewAutoSizeHelper.e(textView0, "getLayoutAlignment", Layout.Alignment.ALIGN_NORMAL);
            int v7 = Math.round(rectF0.right);
            if(Build.VERSION.SDK_INT >= 23) {
                staticLayout0 = Api23Impl.a(charSequence0, layout$Alignment0, v7, v6, this.i, this.h, this.k);
            }
            else {
                float f = textView0.getLineSpacingMultiplier();
                float f1 = textView0.getLineSpacingExtra();
                boolean z = textView0.getIncludeFontPadding();
                staticLayout0 = new StaticLayout(charSequence0, this.h, v7, layout$Alignment0, f, f1, z);
            }
            if(v6 != -1 && (staticLayout0.getLineCount() > v6 || staticLayout0.getLineEnd(staticLayout0.getLineCount() - 1) != charSequence0.length()) || ((float)staticLayout0.getHeight()) > rectF0.bottom) {
                v3 = v4 - 1;
                v1 = v3;
            }
            else {
                v3 = v2;
                v2 = v4 + 1;
            }
        }
        return this.f[v3];
    }

    public static Method d(String s) {
        try {
            ConcurrentHashMap concurrentHashMap0 = AppCompatTextViewAutoSizeHelper.m;
            Method method0 = (Method)concurrentHashMap0.get(s);
            if(method0 == null) {
                method0 = TextView.class.getDeclaredMethod(s);
                if(method0 != null) {
                    method0.setAccessible(true);
                    concurrentHashMap0.put(s, method0);
                }
            }
            return method0;
        }
        catch(Exception unused_ex) {
            return null;
        }
    }

    public static Object e(Object object0, String s, Object object1) {
        try {
            return AppCompatTextViewAutoSizeHelper.d(s).invoke(object0);
        }
        catch(Exception unused_ex) {
            return object1;
        }
    }

    public final boolean f() {
        return this.j() && this.a != 0;
    }

    public final void g(float f, int v) {
        float f1 = TypedValue.applyDimension(v, f, (this.j == null ? Resources.getSystem() : this.j.getResources()).getDisplayMetrics());
        TextView textView0 = this.i;
        if(f1 != textView0.getPaint().getTextSize()) {
            textView0.getPaint().setTextSize(f1);
            boolean z = textView0.isInLayout();
            if(textView0.getLayout() != null) {
                try {
                    this.b = false;
                    Method method0 = AppCompatTextViewAutoSizeHelper.d("nullLayouts");
                    if(method0 != null) {
                        method0.invoke(textView0);
                    }
                }
                catch(Exception unused_ex) {
                }
                if(z) {
                    textView0.forceLayout();
                }
                else {
                    textView0.requestLayout();
                }
                textView0.invalidate();
            }
        }
    }

    public final boolean h() {
        if(this.j() && this.a == 1) {
            if(!this.g || this.f.length == 0) {
                int v1 = ((int)Math.floor((this.e - this.d) / this.c)) + 1;
                int[] arr_v = new int[v1];
                for(int v = 0; v < v1; ++v) {
                    arr_v[v] = Math.round(((float)v) * this.c + this.d);
                }
                this.f = AppCompatTextViewAutoSizeHelper.b(arr_v);
            }
            this.b = true;
            return true;
        }
        this.b = false;
        return false;
    }

    public final boolean i() {
        int[] arr_v = this.f;
        boolean z = arr_v.length > 0;
        this.g = z;
        if(z) {
            this.a = 1;
            this.d = (float)arr_v[0];
            this.e = (float)arr_v[arr_v.length - 1];
            this.c = -1.0f;
        }
        return z;
    }

    public final boolean j() {
        return !(this.i instanceof AppCompatEditText);
    }

    public final void k(float f, float f1, float f2) {
        if(f <= 0.0f) {
            throw new IllegalArgumentException("Minimum auto-size text size (" + f + "px) is less or equal to (0px)");
        }
        if(f1 <= f) {
            throw new IllegalArgumentException("Maximum auto-size text size (" + f1 + "px) is less or equal to minimum auto-size text size (" + f + "px)");
        }
        if(f2 <= 0.0f) {
            throw new IllegalArgumentException("The auto-size step granularity (" + f2 + "px) is less or equal to (0px)");
        }
        this.a = 1;
        this.d = f;
        this.e = f1;
        this.c = f2;
        this.g = false;
    }
}


package androidx.appcompat.widget;

import android.content.res.TypedArray;
import android.text.method.KeyListener;
import android.text.method.NumberKeyListener;
import android.util.AttributeSet;
import android.widget.EditText;
import androidx.appcompat.R.styleable;
import androidx.emoji2.viewsintegration.EmojiEditTextHelper;

class AppCompatEmojiEditTextHelper {
    public final EditText a;
    public final EmojiEditTextHelper b;

    public AppCompatEmojiEditTextHelper(EditText editText0) {
        this.a = editText0;
        this.b = new EmojiEditTextHelper(editText0);
    }

    public final KeyListener a(KeyListener keyListener0) {
        return !(keyListener0 instanceof NumberKeyListener) == 0 ? keyListener0 : this.b.a(keyListener0);
    }

    public final void b(AttributeSet attributeSet0, int v) {
        boolean z = true;
        TypedArray typedArray0 = this.a.getContext().obtainStyledAttributes(attributeSet0, R.styleable.i, v, 0);
        try {
            if(typedArray0.hasValue(14)) {
                z = typedArray0.getBoolean(14, true);
            }
        }
        finally {
            typedArray0.recycle();
        }
        this.b.c(z);
    }
}


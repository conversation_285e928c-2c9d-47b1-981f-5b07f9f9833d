package androidx.appcompat.widget;

import android.view.View.OnClickListener;
import android.view.View;
import android.view.Window.Callback;
import androidx.appcompat.view.menu.ActionMenuItem;

class ToolbarWidgetWrapper.1 implements View.OnClickListener {
    public final ActionMenuItem a;
    public final ToolbarWidgetWrapper b;

    public ToolbarWidgetWrapper.1(ToolbarWidgetWrapper toolbarWidgetWrapper0) {
        this.b = toolbarWidgetWrapper0;
        this.a = new ActionMenuItem(toolbarWidgetWrapper0.a.getContext(), toolbarWidgetWrapper0.h);
    }

    @Override  // android.view.View$OnClickListener
    public final void onClick(View view0) {
        Window.Callback window$Callback0 = this.b.k;
        if(window$Callback0 != null && this.b.l) {
            window$Callback0.onMenuItemSelected(0, this.a);
        }
    }
}


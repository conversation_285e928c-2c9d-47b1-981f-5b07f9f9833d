package androidx.appcompat.widget;

import android.view.textclassifier.TextClassificationManager;
import android.view.textclassifier.TextClassifier;
import android.widget.TextView;

final class AppCompatTextClassifierHelper {
    static abstract class Api26Impl {
        public static TextClassifier a(TextView textView0) {
            TextClassificationManager textClassificationManager0 = (TextClassificationManager)textView0.getContext().getSystemService(TextClassificationManager.class);
            return textClassificationManager0 == null ? TextClassifier.NO_OP : textClassificationManager0.getTextClassifier();
        }
    }

    public TextView a;
    public TextClassifier b;

}


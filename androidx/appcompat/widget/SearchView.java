package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.KeyEvent.DispatcherState;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputMethodManager;
import androidx.appcompat.view.CollapsibleActionView;

public abstract class SearchView extends LinearLayoutCompat implements CollapsibleActionView {
    public static class SearchAutoComplete extends AppCompatAutoCompleteTextView {
        public int e;
        public boolean f;
        public final Runnable g;

        public SearchAutoComplete(Context context0, AttributeSet attributeSet0) {
            super(context0, attributeSet0);
            this.g = new Runnable() {
                public final SearchAutoComplete a;

                {
                    this.a = searchView$SearchAutoComplete0;
                }

                @Override
                public final void run() {
                    SearchAutoComplete searchView$SearchAutoComplete0 = this.a;
                    if(searchView$SearchAutoComplete0.f) {
                        ((InputMethodManager)searchView$SearchAutoComplete0.getContext().getSystemService("input_method")).showSoftInput(searchView$SearchAutoComplete0, 0);
                        searchView$SearchAutoComplete0.f = false;
                    }
                }
            };
            this.e = this.getThreshold();
        }

        @Override  // android.widget.AutoCompleteTextView
        public final boolean enoughToFilter() {
            return this.e <= 0 || super.enoughToFilter();
        }

        private int getSearchViewTextMinWidthDp() {
            Configuration configuration0 = this.getResources().getConfiguration();
            int v = configuration0.screenWidthDp;
            int v1 = configuration0.screenHeightDp;
            if(v >= 960 && v1 >= 720 && configuration0.orientation == 2) {
                return 0x100;
            }
            return v >= 600 || v >= 640 && v1 >= 480 ? 0xC0 : 0xA0;
        }

        @Override  // androidx.appcompat.widget.AppCompatAutoCompleteTextView
        public final InputConnection onCreateInputConnection(EditorInfo editorInfo0) {
            InputConnection inputConnection0 = super.onCreateInputConnection(editorInfo0);
            if(this.f) {
                this.removeCallbacks(this.g);
                this.post(this.g);
            }
            return inputConnection0;
        }

        @Override  // android.view.View
        public final void onFinishInflate() {
            super.onFinishInflate();
            DisplayMetrics displayMetrics0 = this.getResources().getDisplayMetrics();
            this.setMinWidth(((int)TypedValue.applyDimension(1, ((float)this.getSearchViewTextMinWidthDp()), displayMetrics0)));
        }

        @Override  // android.widget.AutoCompleteTextView
        public final void onFocusChanged(boolean z, int v, Rect rect0) {
            super.onFocusChanged(z, v, rect0);
            throw null;
        }

        @Override  // android.widget.AutoCompleteTextView
        public final boolean onKeyPreIme(int v, KeyEvent keyEvent0) {
            if(v == 4) {
                if(keyEvent0.getAction() == 0 && keyEvent0.getRepeatCount() == 0) {
                    KeyEvent.DispatcherState keyEvent$DispatcherState0 = this.getKeyDispatcherState();
                    if(keyEvent$DispatcherState0 != null) {
                        keyEvent$DispatcherState0.startTracking(keyEvent0, this);
                    }
                    return true;
                }
                if(keyEvent0.getAction() == 1) {
                    KeyEvent.DispatcherState keyEvent$DispatcherState1 = this.getKeyDispatcherState();
                    if(keyEvent$DispatcherState1 != null) {
                        keyEvent$DispatcherState1.handleUpEvent(keyEvent0);
                    }
                    if(keyEvent0.isTracking() && !keyEvent0.isCanceled()) {
                        throw null;
                    }
                }
            }
            return super.onKeyPreIme(v, keyEvent0);
        }

        @Override  // android.widget.AutoCompleteTextView
        public final void onWindowFocusChanged(boolean z) {
            super.onWindowFocusChanged(z);
            if(z) {
                throw null;
            }
        }

        @Override  // android.widget.AutoCompleteTextView
        public final void performCompletion() {
        }

        @Override  // android.widget.AutoCompleteTextView
        public final void replaceText(CharSequence charSequence0) {
        }

        public void setImeVisibility(boolean z) {
            InputMethodManager inputMethodManager0 = (InputMethodManager)this.getContext().getSystemService("input_method");
            Runnable runnable0 = this.g;
            if(!z) {
                this.f = false;
                this.removeCallbacks(runnable0);
                inputMethodManager0.hideSoftInputFromWindow(this.getWindowToken(), 0);
                return;
            }
            if(inputMethodManager0.isActive(this)) {
                this.f = false;
                this.removeCallbacks(runnable0);
                inputMethodManager0.showSoftInput(this, 0);
                return;
            }
            this.f = true;
        }

        public void setSearchView(SearchView searchView0) {
        }

        @Override  // android.widget.AutoCompleteTextView
        public void setThreshold(int v) {
            super.setThreshold(v);
            this.e = v;
        }
    }

}


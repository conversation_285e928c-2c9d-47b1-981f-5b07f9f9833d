package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.util.SparseBooleanArray;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.view.ViewParent;
import androidx.appcompat.view.ActionBarPolicy;
import androidx.appcompat.view.menu.ActionMenuItemView.PopupCallback;
import androidx.appcompat.view.menu.ActionMenuItemView;
import androidx.appcompat.view.menu.BaseMenuPresenter;
import androidx.appcompat.view.menu.MenuBuilder.Callback;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuItemImpl;
import androidx.appcompat.view.menu.MenuPopup;
import androidx.appcompat.view.menu.MenuPopupHelper;
import androidx.appcompat.view.menu.MenuView.ItemView;
import androidx.appcompat.view.menu.MenuView;
import androidx.appcompat.view.menu.ShowableListMenu;
import androidx.appcompat.view.menu.SubMenuBuilder;
import androidx.core.graphics.drawable.DrawableCompat;
import java.util.ArrayList;

class ActionMenuPresenter extends BaseMenuPresenter {
    class ActionButtonSubmenu extends MenuPopupHelper {
        public final ActionMenuPresenter m;

        public ActionButtonSubmenu(Context context0, SubMenuBuilder subMenuBuilder0, View view0) {
            super(0x7F040029, 0, context0, view0, subMenuBuilder0, false);  // attr:actionOverflowMenuStyle
            if(!subMenuBuilder0.A.e()) {
                View view1 = actionMenuPresenter0.j;
                if(view1 == null) {
                    view1 = (View)actionMenuPresenter0.h;
                }
                this.f = view1;
            }
            PopupPresenterCallback actionMenuPresenter$PopupPresenterCallback0 = actionMenuPresenter0.x;
            this.i = actionMenuPresenter$PopupPresenterCallback0;
            MenuPopup menuPopup0 = this.j;
            if(menuPopup0 != null) {
                menuPopup0.e(actionMenuPresenter$PopupPresenterCallback0);
            }
        }

        @Override  // androidx.appcompat.view.menu.MenuPopupHelper
        public final void c() {
            ActionMenuPresenter.this.u = null;
            ActionMenuPresenter.this.y = 0;
            super.c();
        }
    }

    class ActionMenuPopupCallback extends PopupCallback {
        public final ActionMenuPresenter a;

        @Override  // androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback
        public final ShowableListMenu a() {
            ActionButtonSubmenu actionMenuPresenter$ActionButtonSubmenu0 = this.a.u;
            return actionMenuPresenter$ActionButtonSubmenu0 != null ? actionMenuPresenter$ActionButtonSubmenu0.a() : null;
        }
    }

    class OpenOverflowRunnable implements Runnable {
        public final OverflowPopup a;
        public final ActionMenuPresenter b;

        public OpenOverflowRunnable(OverflowPopup actionMenuPresenter$OverflowPopup0) {
            this.a = actionMenuPresenter$OverflowPopup0;
        }

        @Override
        public final void run() {
            ActionMenuPresenter actionMenuPresenter0 = this.b;
            MenuBuilder menuBuilder0 = actionMenuPresenter0.c;
            if(menuBuilder0 != null) {
                Callback menuBuilder$Callback0 = menuBuilder0.e;
                if(menuBuilder$Callback0 != null) {
                    menuBuilder$Callback0.b(menuBuilder0);
                }
            }
            View view0 = (View)actionMenuPresenter0.h;
            if(view0 != null && view0.getWindowToken() != null) {
                OverflowPopup actionMenuPresenter$OverflowPopup0 = this.a;
                if(actionMenuPresenter$OverflowPopup0.b()) {
                    actionMenuPresenter0.t = actionMenuPresenter$OverflowPopup0;
                }
                else if(actionMenuPresenter$OverflowPopup0.f != null) {
                    actionMenuPresenter$OverflowPopup0.e(0, 0, false, false);
                    actionMenuPresenter0.t = actionMenuPresenter$OverflowPopup0;
                }
            }
            actionMenuPresenter0.v = null;
        }
    }

    class OverflowMenuButton extends AppCompatImageView implements ActionMenuChildView {
        public final ActionMenuPresenter d;

        public OverflowMenuButton(Context context0) {
            super(context0, null, 0x7F040028);  // attr:actionOverflowButtonStyle
            this.setClickable(true);
            this.setFocusable(true);
            this.setVisibility(0);
            this.setEnabled(true);
            TooltipCompat.a(this, this.getContentDescription());
            this.setOnTouchListener(new ActionMenuPresenter.OverflowMenuButton.1(this, this));
        }

        @Override  // androidx.appcompat.widget.ActionMenuView$ActionMenuChildView
        public final boolean a() {
            return false;
        }

        @Override  // androidx.appcompat.widget.ActionMenuView$ActionMenuChildView
        public final boolean b() {
            return false;
        }

        @Override  // android.view.View
        public final boolean performClick() {
            if(super.performClick()) {
                return true;
            }
            this.playSoundEffect(0);
            ActionMenuPresenter.this.p();
            return true;
        }

        @Override  // android.widget.ImageView
        public final boolean setFrame(int v, int v1, int v2, int v3) {
            boolean z = super.setFrame(v, v1, v2, v3);
            Drawable drawable0 = this.getDrawable();
            Drawable drawable1 = this.getBackground();
            if(drawable0 != null && drawable1 != null) {
                int v4 = this.getWidth();
                int v5 = this.getHeight();
                int v6 = Math.max(v4, v5);
                int v7 = (v4 + (this.getPaddingLeft() - this.getPaddingRight())) / 2;
                int v8 = (v5 + (this.getPaddingTop() - this.getPaddingBottom())) / 2;
                DrawableCompat.h(drawable1, v7 - v6 / 2, v8 - v6 / 2, v7 + v6 / 2, v8 + v6 / 2);
            }
            return z;
        }
    }

    class OverflowPopup extends MenuPopupHelper {
        public final ActionMenuPresenter m;

        public OverflowPopup(Context context0, MenuBuilder menuBuilder0, View view0) {
            super(0x7F040029, 0, context0, view0, menuBuilder0, true);  // attr:actionOverflowMenuStyle
            this.g = 0x800005;
            PopupPresenterCallback actionMenuPresenter$PopupPresenterCallback0 = actionMenuPresenter0.x;
            this.i = actionMenuPresenter$PopupPresenterCallback0;
            MenuPopup menuPopup0 = this.j;
            if(menuPopup0 != null) {
                menuPopup0.e(actionMenuPresenter$PopupPresenterCallback0);
            }
        }

        @Override  // androidx.appcompat.view.menu.MenuPopupHelper
        public final void c() {
            ActionMenuPresenter actionMenuPresenter0 = ActionMenuPresenter.this;
            MenuBuilder menuBuilder0 = actionMenuPresenter0.c;
            if(menuBuilder0 != null) {
                menuBuilder0.c(true);
            }
            actionMenuPresenter0.t = null;
            super.c();
        }
    }

    class PopupPresenterCallback implements androidx.appcompat.view.menu.MenuPresenter.Callback {
        public final ActionMenuPresenter a;

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final void c(MenuBuilder menuBuilder0, boolean z) {
            if(menuBuilder0 instanceof SubMenuBuilder) {
                menuBuilder0.k().c(false);
            }
            androidx.appcompat.view.menu.MenuPresenter.Callback menuPresenter$Callback0 = this.a.e;
            if(menuPresenter$Callback0 != null) {
                menuPresenter$Callback0.c(menuBuilder0, z);
            }
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final boolean d(MenuBuilder menuBuilder0) {
            ActionMenuPresenter actionMenuPresenter0 = this.a;
            if(menuBuilder0 == actionMenuPresenter0.c) {
                return false;
            }
            actionMenuPresenter0.y = ((SubMenuBuilder)menuBuilder0).A.a;
            return actionMenuPresenter0.e == null ? false : actionMenuPresenter0.e.d(menuBuilder0);
        }
    }

    @SuppressLint({"BanParcelableUsage"})
    static class SavedState implements Parcelable {
        public static final Parcelable.Creator CREATOR;
        public int a;

        static {
            SavedState.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
                @Override  // android.os.Parcelable$Creator
                public final Object createFromParcel(Parcel parcel0) {
                    SavedState actionMenuPresenter$SavedState0 = new SavedState();  // 初始化器: Ljava/lang/Object;-><init>()V
                    actionMenuPresenter$SavedState0.a = parcel0.readInt();
                    return actionMenuPresenter$SavedState0;
                }

                @Override  // android.os.Parcelable$Creator
                public final Object[] newArray(int v) {
                    return new SavedState[v];
                }
            };
        }

        @Override  // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override  // android.os.Parcelable
        public final void writeToParcel(Parcel parcel0, int v) {
            parcel0.writeInt(this.a);
        }
    }

    public OverflowMenuButton j;
    public Drawable k;
    public boolean l;
    public boolean m;
    public boolean n;
    public int o;
    public int p;
    public int q;
    public boolean r;
    public final SparseBooleanArray s;
    public OverflowPopup t;
    public ActionButtonSubmenu u;
    public OpenOverflowRunnable v;
    public ActionMenuPopupCallback w;
    public final PopupPresenterCallback x;
    public int y;

    public ActionMenuPresenter(Context context0) {
        this.a = context0;
        this.d = LayoutInflater.from(context0);
        this.f = 0x7F0D0003;  // layout:abc_action_menu_layout
        this.g = 0x7F0D0002;  // layout:abc_action_menu_item_layout
        this.s = new SparseBooleanArray();
        this.x = new PopupPresenterCallback(this);
    }

    public final void a(MenuItemImpl menuItemImpl0, ItemView menuView$ItemView0) {
        menuView$ItemView0.d(menuItemImpl0);
        ((ActionMenuItemView)menuView$ItemView0).setItemInvoker(((ActionMenuView)this.h));
        if(this.w == null) {
            this.w = new ActionMenuPopupCallback(this);
        }
        ((ActionMenuItemView)menuView$ItemView0).setPopupCallback(this.w);
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean b() {
        int v;
        ArrayList arrayList0;
        MenuBuilder menuBuilder0 = this.c;
        if(menuBuilder0 == null) {
            arrayList0 = null;
            v = 0;
        }
        else {
            arrayList0 = menuBuilder0.l();
            v = arrayList0.size();
        }
        int v1 = this.q;
        int v2 = this.p;
        ViewGroup viewGroup0 = (ViewGroup)this.h;
        boolean z = false;
        int v4 = 0;
        int v5 = 0;
        for(int v3 = 0; v3 < v; ++v3) {
            MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList0.get(v3);
            int v6 = menuItemImpl0.y;
            if((v6 & 2) == 2) {
                ++v4;
            }
            else if((v6 & 1) == 1) {
                ++v5;
            }
            else {
                z = true;
            }
            if(this.r && menuItemImpl0.C) {
                v1 = 0;
            }
        }
        if(this.m && (z || v5 + v4 > v1)) {
            --v1;
        }
        int v7 = v1 - v4;
        SparseBooleanArray sparseBooleanArray0 = this.s;
        sparseBooleanArray0.clear();
        int v9 = 0;
        for(int v8 = 0; v8 < v; ++v8) {
            MenuItemImpl menuItemImpl1 = (MenuItemImpl)arrayList0.get(v8);
            int v10 = menuItemImpl1.y;
            int v11 = menuItemImpl1.b;
            if((v10 & 2) == 2) {
                View view0 = this.m(menuItemImpl1, null, viewGroup0);
                view0.measure(0, 0);
                int v12 = view0.getMeasuredWidth();
                v2 -= v12;
                if(v9 == 0) {
                    v9 = v12;
                }
                if(v11 != 0) {
                    sparseBooleanArray0.put(v11, true);
                }
                menuItemImpl1.g(true);
            }
            else if((v10 & 1) == 1) {
                boolean z1 = sparseBooleanArray0.get(v11);
                int v13 = v7 <= 0 && !z1 || v2 <= 0 ? 0 : 1;
                if(v13 != 0) {
                    View view1 = this.m(menuItemImpl1, null, viewGroup0);
                    view1.measure(0, 0);
                    int v14 = view1.getMeasuredWidth();
                    v2 -= v14;
                    if(v9 == 0) {
                        v9 = v14;
                    }
                    v13 &= (v2 + v9 <= 0 ? 0 : 1);
                }
                if(v13 != 0 && v11 != 0) {
                    sparseBooleanArray0.put(v11, true);
                }
                else if(z1) {
                    sparseBooleanArray0.put(v11, false);
                    for(int v15 = 0; v15 < v8; ++v15) {
                        MenuItemImpl menuItemImpl2 = (MenuItemImpl)arrayList0.get(v15);
                        if(menuItemImpl2.b == v11) {
                            if(menuItemImpl2.e()) {
                                ++v7;
                            }
                            menuItemImpl2.g(false);
                        }
                    }
                }
                if(v13 != 0) {
                    --v7;
                }
                menuItemImpl1.g(((boolean)v13));
            }
            else {
                menuItemImpl1.g(false);
            }
        }
        return true;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void c(MenuBuilder menuBuilder0, boolean z) {
        this.n();
        ActionButtonSubmenu actionMenuPresenter$ActionButtonSubmenu0 = this.u;
        if(actionMenuPresenter$ActionButtonSubmenu0 != null && actionMenuPresenter$ActionButtonSubmenu0.b()) {
            actionMenuPresenter$ActionButtonSubmenu0.j.dismiss();
        }
        androidx.appcompat.view.menu.MenuPresenter.Callback menuPresenter$Callback0 = this.e;
        if(menuPresenter$Callback0 != null) {
            menuPresenter$Callback0.c(menuBuilder0, z);
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void d(boolean z) {
        int v2;
        ViewGroup viewGroup0 = (ViewGroup)this.h;
        ArrayList arrayList0 = null;
        if(viewGroup0 != null) {
            MenuBuilder menuBuilder0 = this.c;
            if(menuBuilder0 == null) {
                v2 = 0;
            }
            else {
                menuBuilder0.i();
                ArrayList arrayList1 = this.c.l();
                int v = arrayList1.size();
                v2 = 0;
                for(int v1 = 0; v1 < v; ++v1) {
                    MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList1.get(v1);
                    if(menuItemImpl0.e()) {
                        View view0 = viewGroup0.getChildAt(v2);
                        MenuItemImpl menuItemImpl1 = view0 instanceof ItemView ? ((ItemView)view0).getItemData() : null;
                        View view1 = this.m(menuItemImpl0, view0, viewGroup0);
                        if(menuItemImpl0 != menuItemImpl1) {
                            view1.setPressed(false);
                            view1.jumpDrawablesToCurrentState();
                        }
                        if(view1 != view0) {
                            ViewGroup viewGroup1 = (ViewGroup)view1.getParent();
                            if(viewGroup1 != null) {
                                viewGroup1.removeView(view1);
                            }
                            ((ViewGroup)this.h).addView(view1, v2);
                        }
                        ++v2;
                    }
                }
            }
            while(v2 < viewGroup0.getChildCount()) {
                if(!this.i(viewGroup0, v2)) {
                    ++v2;
                }
            }
        }
        ((View)this.h).requestLayout();
        MenuBuilder menuBuilder1 = this.c;
        if(menuBuilder1 != null) {
            menuBuilder1.i();
            ArrayList arrayList2 = menuBuilder1.i;
            int v3 = arrayList2.size();
            for(int v4 = 0; v4 < v3; ++v4) {
                MenuItemImpl menuItemImpl2 = (MenuItemImpl)arrayList2.get(v4);
            }
        }
        MenuBuilder menuBuilder2 = this.c;
        if(menuBuilder2 != null) {
            menuBuilder2.i();
            arrayList0 = menuBuilder2.j;
        }
        if(!this.m || arrayList0 == null) {
        label_67:
            OverflowMenuButton actionMenuPresenter$OverflowMenuButton1 = this.j;
            if(actionMenuPresenter$OverflowMenuButton1 != null) {
                ViewParent viewParent0 = actionMenuPresenter$OverflowMenuButton1.getParent();
                MenuView menuView0 = this.h;
                if(viewParent0 == menuView0) {
                    ((ViewGroup)menuView0).removeView(this.j);
                }
            }
        }
        else {
            int v5 = arrayList0.size();
            if(v5 != 1) {
                if(v5 > 0) {
                label_54:
                    if(this.j == null) {
                        this.j = new OverflowMenuButton(this, this.a);
                    }
                    ViewGroup viewGroup2 = (ViewGroup)this.j.getParent();
                    if(viewGroup2 != this.h) {
                        if(viewGroup2 != null) {
                            viewGroup2.removeView(this.j);
                        }
                        ActionMenuView actionMenuView0 = (ActionMenuView)this.h;
                        OverflowMenuButton actionMenuPresenter$OverflowMenuButton0 = this.j;
                        actionMenuView0.getClass();
                        LayoutParams actionMenuView$LayoutParams0 = ActionMenuView.l();
                        actionMenuView$LayoutParams0.a = true;
                        actionMenuView0.addView(actionMenuPresenter$OverflowMenuButton0, actionMenuView$LayoutParams0);
                    }
                }
                else {
                    goto label_67;
                }
            }
            else if(!((MenuItemImpl)arrayList0.get(0)).C != 0) {
                goto label_54;
            }
            else {
                goto label_67;
            }
        }
        ((ActionMenuView)this.h).setOverflowReserved(this.m);
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void g(Context context0, MenuBuilder menuBuilder0) {
        this.b = context0;
        LayoutInflater.from(context0);
        this.c = menuBuilder0;
        Resources resources0 = context0.getResources();
        ActionBarPolicy actionBarPolicy0 = new ActionBarPolicy();  // 初始化器: Ljava/lang/Object;-><init>()V
        actionBarPolicy0.a = context0;
        if(!this.n) {
            this.m = true;
        }
        this.o = context0.getResources().getDisplayMetrics().widthPixels / 2;
        this.q = actionBarPolicy0.a();
        int v = this.o;
        if(this.m) {
            if(this.j == null) {
                OverflowMenuButton actionMenuPresenter$OverflowMenuButton0 = new OverflowMenuButton(this, this.a);
                this.j = actionMenuPresenter$OverflowMenuButton0;
                if(this.l) {
                    actionMenuPresenter$OverflowMenuButton0.setImageDrawable(this.k);
                    this.k = null;
                    this.l = false;
                }
                this.j.measure(0, 0);
            }
            v -= this.j.getMeasuredWidth();
        }
        else {
            this.j = null;
        }
        this.p = v;
        resources0.getDisplayMetrics();
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void h(Parcelable parcelable0) {
        if(!(parcelable0 instanceof SavedState)) {
            return;
        }
        int v = ((SavedState)parcelable0).a;
        if(v > 0) {
            MenuItem menuItem0 = this.c.findItem(v);
            if(menuItem0 != null) {
                this.j(((SubMenuBuilder)menuItem0.getSubMenu()));
            }
        }
    }

    public final boolean i(ViewGroup viewGroup0, int v) {
        if(viewGroup0.getChildAt(v) == this.j) {
            return false;
        }
        viewGroup0.removeViewAt(v);
        return true;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean j(SubMenuBuilder subMenuBuilder0) {
        boolean z;
        if(!subMenuBuilder0.hasVisibleItems()) {
            return false;
        }
        SubMenuBuilder subMenuBuilder1 = subMenuBuilder0;
        MenuBuilder menuBuilder0;
        while((menuBuilder0 = subMenuBuilder1.z) != this.c) {
            subMenuBuilder1 = (SubMenuBuilder)menuBuilder0;
        }
        ViewGroup viewGroup0 = (ViewGroup)this.h;
        View view0 = null;
        if(viewGroup0 != null) {
            int v = viewGroup0.getChildCount();
            for(int v1 = 0; v1 < v; ++v1) {
                View view1 = viewGroup0.getChildAt(v1);
                if(view1 instanceof ItemView && ((ItemView)view1).getItemData() == subMenuBuilder1.A) {
                    view0 = view1;
                    break;
                }
            }
        }
        if(view0 == null) {
            return false;
        }
        this.y = subMenuBuilder0.A.a;
        int v2 = subMenuBuilder0.f.size();
        for(int v3 = 0; true; ++v3) {
            z = false;
            if(v3 >= v2) {
                break;
            }
            MenuItem menuItem0 = subMenuBuilder0.getItem(v3);
            if(menuItem0.isVisible() && menuItem0.getIcon() != null) {
                z = true;
                break;
            }
        }
        ActionButtonSubmenu actionMenuPresenter$ActionButtonSubmenu0 = new ActionButtonSubmenu(this, this.b, subMenuBuilder0, view0);
        this.u = actionMenuPresenter$ActionButtonSubmenu0;
        actionMenuPresenter$ActionButtonSubmenu0.d(z);
        ActionButtonSubmenu actionMenuPresenter$ActionButtonSubmenu1 = this.u;
        if(!actionMenuPresenter$ActionButtonSubmenu1.b()) {
            if(actionMenuPresenter$ActionButtonSubmenu1.f == null) {
                throw new IllegalStateException("MenuPopupHelper cannot be used without an anchor");
            }
            actionMenuPresenter$ActionButtonSubmenu1.e(0, 0, false, false);
        }
        androidx.appcompat.view.menu.MenuPresenter.Callback menuPresenter$Callback0 = this.e;
        if(menuPresenter$Callback0 != null) {
            menuPresenter$Callback0.d(subMenuBuilder0);
        }
        return true;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final Parcelable k() {
        Parcelable parcelable0 = new SavedState();  // 初始化器: Ljava/lang/Object;-><init>()V
        parcelable0.a = this.y;
        return parcelable0;
    }

    public final View m(MenuItemImpl menuItemImpl0, View view0, ViewGroup viewGroup0) {
        View view1 = menuItemImpl0.getActionView();
        int v = 0;
        if(view1 == null || menuItemImpl0.d()) {
            ItemView menuView$ItemView0 = view0 instanceof ItemView ? ((ItemView)view0) : ((ItemView)this.d.inflate(this.g, viewGroup0, false));
            this.a(menuItemImpl0, menuView$ItemView0);
            view1 = (View)menuView$ItemView0;
        }
        if(menuItemImpl0.C) {
            v = 8;
        }
        view1.setVisibility(v);
        ViewGroup.LayoutParams viewGroup$LayoutParams0 = view1.getLayoutParams();
        ((ActionMenuView)viewGroup0).getClass();
        if(!(viewGroup$LayoutParams0 instanceof LayoutParams)) {
            view1.setLayoutParams(ActionMenuView.m(viewGroup$LayoutParams0));
        }
        return view1;
    }

    public final boolean n() {
        OpenOverflowRunnable actionMenuPresenter$OpenOverflowRunnable0 = this.v;
        if(actionMenuPresenter$OpenOverflowRunnable0 != null) {
            MenuView menuView0 = this.h;
            if(menuView0 != null) {
                ((View)menuView0).removeCallbacks(actionMenuPresenter$OpenOverflowRunnable0);
                this.v = null;
                return true;
            }
        }
        OverflowPopup actionMenuPresenter$OverflowPopup0 = this.t;
        if(actionMenuPresenter$OverflowPopup0 != null) {
            if(actionMenuPresenter$OverflowPopup0.b()) {
                actionMenuPresenter$OverflowPopup0.j.dismiss();
            }
            return true;
        }
        return false;
    }

    public final boolean o() {
        return this.t != null && this.t.b();
    }

    public final boolean p() {
        if(this.m && !this.o()) {
            MenuBuilder menuBuilder0 = this.c;
            if(menuBuilder0 != null && this.h != null && this.v == null) {
                menuBuilder0.i();
                if(!menuBuilder0.j.isEmpty()) {
                    OpenOverflowRunnable actionMenuPresenter$OpenOverflowRunnable0 = new OpenOverflowRunnable(this, new OverflowPopup(this, this.b, this.c, this.j));
                    this.v = actionMenuPresenter$OpenOverflowRunnable0;
                    ((View)this.h).post(actionMenuPresenter$OpenOverflowRunnable0);
                    return true;
                }
            }
        }
        return false;
    }
}


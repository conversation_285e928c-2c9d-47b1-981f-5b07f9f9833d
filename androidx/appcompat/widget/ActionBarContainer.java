package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.ActionMode.Callback;
import android.view.ActionMode;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View;
import android.widget.FrameLayout;
import androidx.appcompat.R.styleable;

public class ActionBarContainer extends FrameLayout {
    public boolean a;
    public View b;
    public View c;
    public Drawable d;
    public Drawable e;
    public Drawable f;
    public final boolean g;
    public boolean h;
    public final int i;

    public ActionBarContainer(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0);
        this.setBackground(new ActionBarBackgroundDrawable(this));
        TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.a);
        boolean z = false;
        this.d = typedArray0.getDrawable(0);
        this.e = typedArray0.getDrawable(2);
        this.i = typedArray0.getDimensionPixelSize(13, -1);
        if(this.getId() == 0x7F0A0413) {  // id:split_action_bar
            this.g = true;
            this.f = typedArray0.getDrawable(1);
        }
        typedArray0.recycle();
        if(!this.g) {
            if(this.d == null && this.e == null) {
                z = true;
            }
        }
        else if(this.f == null) {
            z = true;
        }
        this.setWillNotDraw(z);
    }

    @Override  // android.view.ViewGroup
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        if(this.d != null && this.d.isStateful()) {
            this.d.setState(this.getDrawableState());
        }
        if(this.e != null && this.e.isStateful()) {
            this.e.setState(this.getDrawableState());
        }
        if(this.f != null && this.f.isStateful()) {
            this.f.setState(this.getDrawableState());
        }
    }

    public View getTabContainer() [...] // Inlined contents

    @Override  // android.view.ViewGroup
    public final void jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState();
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            drawable0.jumpToCurrentState();
        }
        Drawable drawable1 = this.e;
        if(drawable1 != null) {
            drawable1.jumpToCurrentState();
        }
        Drawable drawable2 = this.f;
        if(drawable2 != null) {
            drawable2.jumpToCurrentState();
        }
    }

    @Override  // android.view.View
    public final void onFinishInflate() {
        super.onFinishInflate();
        this.b = this.findViewById(0x7F0A0051);  // id:action_bar
        this.c = this.findViewById(0x7F0A005A);  // id:action_context_bar
    }

    @Override  // android.view.View
    public final boolean onHoverEvent(MotionEvent motionEvent0) {
        super.onHoverEvent(motionEvent0);
        return true;
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.ViewGroup
    public final boolean onInterceptTouchEvent(MotionEvent motionEvent0) {
        return this.a || super.onInterceptTouchEvent(motionEvent0);
    }

    @Override  // android.widget.FrameLayout
    public final void onLayout(boolean z, int v, int v1, int v2, int v3) {
        boolean z1;
        super.onLayout(z, v, v1, v2, v3);
        if(this.g) {
            Drawable drawable0 = this.f;
            if(drawable0 != null) {
                drawable0.setBounds(0, 0, this.getMeasuredWidth(), this.getMeasuredHeight());
                this.invalidate();
            }
        }
        else {
            if(this.d == null) {
                z1 = false;
            }
            else {
                if(this.b.getVisibility() == 0) {
                    this.d.setBounds(this.b.getLeft(), this.b.getTop(), this.b.getRight(), this.b.getBottom());
                }
                else if(this.c == null || this.c.getVisibility() != 0) {
                    this.d.setBounds(0, 0, 0, 0);
                }
                else {
                    this.d.setBounds(this.c.getLeft(), this.c.getTop(), this.c.getRight(), this.c.getBottom());
                }
                z1 = true;
            }
            this.h = false;
            if(z1) {
                this.invalidate();
            }
        }
    }

    @Override  // android.widget.FrameLayout
    public final void onMeasure(int v, int v1) {
        if(this.b == null && View.MeasureSpec.getMode(v1) == 0x80000000) {
            int v2 = this.i;
            if(v2 >= 0) {
                v1 = View.MeasureSpec.makeMeasureSpec(Math.min(v2, View.MeasureSpec.getSize(v1)), 0x80000000);
            }
        }
        super.onMeasure(v, v1);
        if(this.b == null) {
            return;
        }
        View.MeasureSpec.getMode(v1);
    }

    @Override  // android.view.View
    public final boolean onTouchEvent(MotionEvent motionEvent0) {
        super.onTouchEvent(motionEvent0);
        return true;
    }

    public void setPrimaryBackground(Drawable drawable0) {
        Drawable drawable1 = this.d;
        if(drawable1 != null) {
            drawable1.setCallback(null);
            this.unscheduleDrawable(this.d);
        }
        this.d = drawable0;
        if(drawable0 != null) {
            drawable0.setCallback(this);
            View view0 = this.b;
            if(view0 != null) {
                this.d.setBounds(view0.getLeft(), this.b.getTop(), this.b.getRight(), this.b.getBottom());
            }
        }
        boolean z = false;
        if(!this.g) {
            if(this.d == null && this.e == null) {
                z = true;
            }
        }
        else if(this.f == null) {
            z = true;
        }
        this.setWillNotDraw(z);
        this.invalidate();
        this.invalidateOutline();
    }

    public void setSplitBackground(Drawable drawable0) {
        Drawable drawable1 = this.f;
        if(drawable1 != null) {
            drawable1.setCallback(null);
            this.unscheduleDrawable(this.f);
        }
        this.f = drawable0;
        boolean z = this.g;
        boolean z1 = false;
        if(drawable0 != null) {
            drawable0.setCallback(this);
            if(z) {
                Drawable drawable2 = this.f;
                if(drawable2 != null) {
                    drawable2.setBounds(0, 0, this.getMeasuredWidth(), this.getMeasuredHeight());
                }
            }
        }
        if(!z) {
            if(this.d == null && this.e == null) {
                z1 = true;
            }
        }
        else if(this.f == null) {
            z1 = true;
        }
        this.setWillNotDraw(z1);
        this.invalidate();
        this.invalidateOutline();
    }

    public void setStackedBackground(Drawable drawable0) {
        Drawable drawable1 = this.e;
        if(drawable1 != null) {
            drawable1.setCallback(null);
            this.unscheduleDrawable(this.e);
        }
        this.e = drawable0;
        if(drawable0 != null) {
            drawable0.setCallback(this);
            if(this.h && this.e != null) {
                throw null;
            }
        }
        boolean z = false;
        if(!this.g) {
            if(this.d == null && this.e == null) {
                z = true;
            }
        }
        else if(this.f == null) {
            z = true;
        }
        this.setWillNotDraw(z);
        this.invalidate();
        this.invalidateOutline();
    }

    public void setTabContainer(ScrollingTabContainerView scrollingTabContainerView0) {
    }

    public void setTransitioning(boolean z) {
        this.a = z;
        this.setDescendantFocusability((z ? 0x60000 : 0x40000));
    }

    @Override  // android.view.View
    public void setVisibility(int v) {
        super.setVisibility(v);
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            drawable0.setVisible(v == 0, false);
        }
        Drawable drawable1 = this.e;
        if(drawable1 != null) {
            drawable1.setVisible(v == 0, false);
        }
        Drawable drawable2 = this.f;
        if(drawable2 != null) {
            drawable2.setVisible(v == 0, false);
        }
    }

    @Override  // android.view.ViewGroup
    public final ActionMode startActionModeForChild(View view0, ActionMode.Callback actionMode$Callback0) {
        return null;
    }

    @Override  // android.view.ViewGroup
    public final ActionMode startActionModeForChild(View view0, ActionMode.Callback actionMode$Callback0, int v) {
        return v == 0 ? null : super.startActionModeForChild(view0, actionMode$Callback0, v);
    }

    // 去混淆评级： 中等(70)
    @Override  // android.view.View
    public final boolean verifyDrawable(Drawable drawable0) {
        return drawable0 == this.d && !this.g || drawable0 == this.e && this.h || drawable0 == this.f && this.g || super.verifyDrawable(drawable0);
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.SeekBar;

public class AppCompatSeekBar extends SeekBar {
    public final AppCompatSeekBarHelper a;

    public AppCompatSeekBar(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0, 0x7F0404B2);  // attr:seekBarStyle
        ThemeUtils.a(this, this.getContext());
        AppCompatSeekBarHelper appCompatSeekBarHelper0 = new AppCompatSeekBarHelper(this);
        this.a = appCompatSeekBarHelper0;
        appCompatSeekBarHelper0.a(attributeSet0, 0x7F0404B2);  // attr:seekBarStyle
    }

    @Override  // android.widget.AbsSeekBar
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatSeekBarHelper appCompatSeekBarHelper0 = this.a;
        Drawable drawable0 = appCompatSeekBarHelper0.e;
        if(drawable0 != null && drawable0.isStateful()) {
            SeekBar seekBar0 = appCompatSeekBarHelper0.d;
            if(drawable0.setState(seekBar0.getDrawableState())) {
                seekBar0.invalidateDrawable(drawable0);
            }
        }
    }

    @Override  // android.widget.AbsSeekBar
    public final void jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState();
        Drawable drawable0 = this.a.e;
        if(drawable0 != null) {
            drawable0.jumpToCurrentState();
        }
    }

    @Override  // android.widget.AbsSeekBar
    public final void onDraw(Canvas canvas0) {
        synchronized(this) {
            super.onDraw(canvas0);
            this.a.d(canvas0);
        }
    }
}


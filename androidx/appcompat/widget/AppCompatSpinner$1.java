package androidx.appcompat.widget;

import android.view.View;
import androidx.appcompat.view.menu.ShowableListMenu;

class AppCompatSpinner.1 extends ForwardingListener {
    public final DropdownPopup j;
    public final AppCompatSpinner k;

    public AppCompatSpinner.1(AppCompatSpinner appCompatSpinner0, View view0, DropdownPopup appCompatSpinner$DropdownPopup0) {
        this.k = appCompatSpinner0;
        this.j = appCompatSpinner$DropdownPopup0;
        super(view0);
    }

    @Override  // androidx.appcompat.widget.ForwardingListener
    public final ShowableListMenu b() {
        return this.j;
    }

    @Override  // androidx.appcompat.widget.ForwardingListener
    public final boolean c() {
        AppCompatSpinner appCompatSpinner0 = this.k;
        if(!appCompatSpinner0.getInternalPopup().a()) {
            int v = appCompatSpinner0.getTextDirection();
            int v1 = appCompatSpinner0.getTextAlignment();
            appCompatSpinner0.f.m(v, v1);
        }
        return true;
    }
}


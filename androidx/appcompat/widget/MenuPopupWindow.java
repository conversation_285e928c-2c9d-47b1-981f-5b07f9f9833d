package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.transition.Transition;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.PopupWindow;
import androidx.appcompat.view.menu.ListMenuItemView;
import androidx.appcompat.view.menu.MenuAdapter;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuItemImpl;
import java.lang.reflect.Method;

public class MenuPopupWindow extends ListPopupWindow implements MenuItemHoverListener {
    static abstract class Api23Impl {
        public static void a(PopupWindow popupWindow0, Transition transition0) {
            popupWindow0.setEnterTransition(transition0);
        }

        public static void b(PopupWindow popupWindow0, Transition transition0) {
            popupWindow0.setExitTransition(transition0);
        }
    }

    static abstract class Api29Impl {
        public static void a(PopupWindow popupWindow0, boolean z) {
            popupWindow0.setTouchModal(z);
        }
    }

    public static class MenuDropDownListView extends DropDownListView {
        public final int m;
        public final int n;
        public MenuItemHoverListener o;
        public MenuItemImpl p;

        public MenuDropDownListView(Context context0, boolean z) {
            super(context0, z);
            if(1 == context0.getResources().getConfiguration().getLayoutDirection()) {
                this.m = 21;
                this.n = 22;
                return;
            }
            this.m = 22;
            this.n = 21;
        }

        @Override  // androidx.appcompat.widget.DropDownListView
        public final boolean onHoverEvent(MotionEvent motionEvent0) {
            MenuItemImpl menuItemImpl0;
            MenuAdapter menuAdapter0;
            int v;
            if(this.o != null) {
                ListAdapter listAdapter0 = this.getAdapter();
                if(listAdapter0 instanceof HeaderViewListAdapter) {
                    v = ((HeaderViewListAdapter)listAdapter0).getHeadersCount();
                    menuAdapter0 = (MenuAdapter)((HeaderViewListAdapter)listAdapter0).getWrappedAdapter();
                }
                else {
                    menuAdapter0 = (MenuAdapter)listAdapter0;
                    v = 0;
                }
                if(motionEvent0.getAction() == 10) {
                    menuItemImpl0 = null;
                }
                else {
                    int v1 = this.pointToPosition(((int)motionEvent0.getX()), ((int)motionEvent0.getY()));
                    if(v1 == -1) {
                        menuItemImpl0 = null;
                    }
                    else {
                        int v2 = v1 - v;
                        menuItemImpl0 = v2 < 0 || v2 >= menuAdapter0.getCount() ? null : menuAdapter0.b(v2);
                    }
                }
                MenuItemImpl menuItemImpl1 = this.p;
                if(menuItemImpl1 != menuItemImpl0) {
                    MenuBuilder menuBuilder0 = menuAdapter0.a;
                    if(menuItemImpl1 != null) {
                        this.o.f(menuBuilder0, menuItemImpl1);
                    }
                    this.p = menuItemImpl0;
                    if(menuItemImpl0 != null) {
                        this.o.c(menuBuilder0, menuItemImpl0);
                    }
                }
            }
            return super.onHoverEvent(motionEvent0);
        }

        @Override  // android.widget.ListView
        public final boolean onKeyDown(int v, KeyEvent keyEvent0) {
            ListMenuItemView listMenuItemView0 = (ListMenuItemView)this.getSelectedView();
            if(listMenuItemView0 != null && v == this.m) {
                if(listMenuItemView0.isEnabled() && listMenuItemView0.getItemData().hasSubMenu()) {
                    this.performItemClick(listMenuItemView0, this.getSelectedItemPosition(), this.getSelectedItemId());
                }
                return true;
            }
            if(listMenuItemView0 != null && v == this.n) {
                this.setSelection(-1);
                ListAdapter listAdapter0 = this.getAdapter();
                (listAdapter0 instanceof HeaderViewListAdapter ? ((MenuAdapter)((HeaderViewListAdapter)listAdapter0).getWrappedAdapter()) : ((MenuAdapter)listAdapter0)).a.c(false);
                return true;
            }
            return super.onKeyDown(v, keyEvent0);
        }

        public void setHoverListener(MenuItemHoverListener menuItemHoverListener0) {
            this.o = menuItemHoverListener0;
        }

        @Override  // androidx.appcompat.widget.DropDownListView
        public void setSelector(Drawable drawable0) {
            super.setSelector(drawable0);
        }
    }

    public MenuItemHoverListener D;
    public static final Method E;

    static {
        try {
            if(Build.VERSION.SDK_INT <= 28) {
                MenuPopupWindow.E = PopupWindow.class.getDeclaredMethod("setTouchModal", Boolean.TYPE);
            }
        }
        catch(NoSuchMethodException unused_ex) {
        }
    }

    @Override  // androidx.appcompat.widget.MenuItemHoverListener
    public final void c(MenuBuilder menuBuilder0, MenuItemImpl menuItemImpl0) {
        MenuItemHoverListener menuItemHoverListener0 = this.D;
        if(menuItemHoverListener0 != null) {
            menuItemHoverListener0.c(menuBuilder0, menuItemImpl0);
        }
    }

    @Override  // androidx.appcompat.widget.MenuItemHoverListener
    public final void f(MenuBuilder menuBuilder0, MenuItem menuItem0) {
        MenuItemHoverListener menuItemHoverListener0 = this.D;
        if(menuItemHoverListener0 != null) {
            menuItemHoverListener0.f(menuBuilder0, menuItem0);
        }
    }

    @Override  // androidx.appcompat.widget.ListPopupWindow
    public final DropDownListView p(Context context0, boolean z) {
        DropDownListView dropDownListView0 = new MenuDropDownListView(context0, z);
        ((MenuDropDownListView)dropDownListView0).setHoverListener(this);
        return dropDownListView0;
    }

    public final void r() {
        if(Build.VERSION.SDK_INT >= 23) {
            Api23Impl.a(this.z, null);
        }
    }

    public final void s() {
        if(Build.VERSION.SDK_INT >= 23) {
            Api23Impl.b(this.z, null);
        }
    }

    public final void t() {
        PopupWindow popupWindow0 = this.z;
        if(Build.VERSION.SDK_INT <= 28) {
            Method method0 = MenuPopupWindow.E;
            if(method0 != null) {
                try {
                    method0.invoke(popupWindow0, Boolean.FALSE);
                }
                catch(Exception unused_ex) {
                }
            }
        }
        else {
            Api29Impl.a(popupWindow0, false);
        }
    }
}


package androidx.appcompat.widget;

import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View.OnAttachStateChangeListener;
import android.view.View.OnTouchListener;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewParent;
import androidx.appcompat.view.menu.ShowableListMenu;

public abstract class ForwardingListener implements View.OnAttachStateChangeListener, View.OnTouchListener {
    class DisallowIntercept implements Runnable {
        public final ForwardingListener a;

        @Override
        public final void run() {
            ViewParent viewParent0 = this.a.d.getParent();
            if(viewParent0 != null) {
                viewParent0.requestDisallowInterceptTouchEvent(true);
            }
        }
    }

    class TriggerLongPress implements Runnable {
        public final ForwardingListener a;

        @Override
        public final void run() {
            ForwardingListener forwardingListener0 = this.a;
            forwardingListener0.a();
            View view0 = forwardingListener0.d;
            if(view0.isEnabled() && !view0.isLongClickable() && forwardingListener0.c()) {
                view0.getParent().requestDisallowInterceptTouchEvent(true);
                long v = SystemClock.uptimeMillis();
                MotionEvent motionEvent0 = MotionEvent.obtain(v, v, 3, 0.0f, 0.0f, 0);
                view0.onTouchEvent(motionEvent0);
                motionEvent0.recycle();
                forwardingListener0.g = true;
            }
        }
    }

    public final float a;
    public final int b;
    public final int c;
    public final View d;
    public Runnable e;
    public Runnable f;
    public boolean g;
    public int h;
    public final int[] i;

    public ForwardingListener(View view0) {
        this.i = new int[2];
        this.d = view0;
        view0.setLongClickable(true);
        view0.addOnAttachStateChangeListener(this);
        this.a = (float)ViewConfiguration.get(view0.getContext()).getScaledTouchSlop();
        this.b = 100;
        this.c = (ViewConfiguration.getLongPressTimeout() + 100) / 2;
    }

    public final void a() {
        Runnable runnable0 = this.f;
        View view0 = this.d;
        if(runnable0 != null) {
            view0.removeCallbacks(runnable0);
        }
        Runnable runnable1 = this.e;
        if(runnable1 != null) {
            view0.removeCallbacks(runnable1);
        }
    }

    public abstract ShowableListMenu b();

    public abstract boolean c();

    public boolean d() {
        ShowableListMenu showableListMenu0 = this.b();
        if(showableListMenu0 != null && showableListMenu0.a()) {
            showableListMenu0.dismiss();
        }
        return true;
    }

    @Override  // android.view.View$OnTouchListener
    public final boolean onTouch(View view0, MotionEvent motionEvent0) {
        boolean z3;
        boolean z2;
        boolean z = this.g;
        View view1 = this.d;
        if(z) {
            ShowableListMenu showableListMenu0 = this.b();
            if(showableListMenu0 == null || !showableListMenu0.a()) {
                z3 = this.d() ? false : true;
            }
            else {
                DropDownListView dropDownListView0 = (DropDownListView)showableListMenu0.i();
                if(dropDownListView0 != null && dropDownListView0.isShown()) {
                    MotionEvent motionEvent1 = MotionEvent.obtainNoHistory(motionEvent0);
                    view1.getLocationOnScreen(this.i);
                    motionEvent1.offsetLocation(((float)this.i[0]), ((float)this.i[1]));
                    dropDownListView0.getLocationOnScreen(this.i);
                    motionEvent1.offsetLocation(((float)(-this.i[0])), ((float)(-this.i[1])));
                    boolean z1 = dropDownListView0.b(motionEvent1, this.h);
                    motionEvent1.recycle();
                    switch(motionEvent0.getActionMasked()) {
                        case 1: 
                        case 3: {
                            z2 = false;
                            break;
                        }
                        default: {
                            z2 = true;
                        }
                    }
                    if(z1 && z2) {
                        z3 = true;
                        this.g = z3;
                        return z3 || z;
                    }
                }
                z3 = this.d() ? false : true;
                this.g = z3;
                return z3 || z;
            }
        }
        else {
            if(view1.isEnabled()) {
                switch(motionEvent0.getActionMasked()) {
                    case 0: {
                        this.h = motionEvent0.getPointerId(0);
                        if(this.e == null) {
                            this.e = new DisallowIntercept(this);
                        }
                        view1.postDelayed(this.e, ((long)this.b));
                        if(this.f == null) {
                            this.f = new TriggerLongPress(this);
                        }
                        view1.postDelayed(this.f, ((long)this.c));
                        z3 = false;
                        break;
                    }
                    case 2: {
                        int v = motionEvent0.findPointerIndex(this.h);
                        if(v >= 0) {
                            float f = motionEvent0.getX(v);
                            float f1 = motionEvent0.getY(v);
                            if(f >= -this.a && f1 >= -this.a && f < ((float)(view1.getRight() - view1.getLeft())) + this.a && f1 < ((float)(view1.getBottom() - view1.getTop())) + this.a) {
                                z3 = false;
                            }
                            else {
                                this.a();
                                view1.getParent().requestDisallowInterceptTouchEvent(true);
                                z3 = this.c();
                            }
                        }
                        else {
                            z3 = false;
                        }
                        break;
                    }
                    case 1: 
                    case 3: {
                        this.a();
                        z3 = false;
                        break;
                    }
                    default: {
                        z3 = false;
                    }
                }
            }
            else {
                z3 = false;
            }
            if(z3) {
                long v1 = SystemClock.uptimeMillis();
                MotionEvent motionEvent2 = MotionEvent.obtain(v1, v1, 3, 0.0f, 0.0f, 0);
                view1.onTouchEvent(motionEvent2);
                motionEvent2.recycle();
            }
        }
        this.g = z3;
        return z3 || z;
    }

    @Override  // android.view.View$OnAttachStateChangeListener
    public final void onViewAttachedToWindow(View view0) {
    }

    @Override  // android.view.View$OnAttachStateChangeListener
    public final void onViewDetachedFromWindow(View view0) {
        this.g = false;
        this.h = -1;
        Runnable runnable0 = this.e;
        if(runnable0 != null) {
            this.d.removeCallbacks(runnable0);
        }
    }
}


package androidx.appcompat.widget;

import androidx.appcompat.view.menu.MenuItemImpl;

public final class a implements Runnable {
    public final int a;
    public final Toolbar b;

    public a(Toolbar toolbar0, int v) {
        this.a = v;
        this.b = toolbar0;
    }

    @Override
    public final void run() {
        Toolbar toolbar0 = this.b;
        if(this.a != 0) {
            toolbar0.o();
            return;
        }
        MenuItemImpl menuItemImpl0 = toolbar0.M == null ? null : toolbar0.M.b;
        if(menuItemImpl0 != null) {
            menuItemImpl0.collapseActionView();
        }
    }
}


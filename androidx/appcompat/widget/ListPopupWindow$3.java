package androidx.appcompat.widget;

import android.view.View;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.AdapterView;

class ListPopupWindow.3 implements AdapterView.OnItemSelectedListener {
    public final ListPopupWindow a;

    public ListPopupWindow.3(ListPopupWindow listPopupWindow0) {
        this.a = listPopupWindow0;
    }

    @Override  // android.widget.AdapterView$OnItemSelectedListener
    public final void onItemSelected(AdapterView adapterView0, View view0, int v, long v1) {
        if(v != -1) {
            DropDownListView dropDownListView0 = this.a.c;
            if(dropDownListView0 != null) {
                dropDownListView0.setListSelectionHidden(false);
            }
        }
    }

    @Override  // android.widget.AdapterView$OnItemSelectedListener
    public final void onNothingSelected(AdapterView adapterView0) {
    }
}


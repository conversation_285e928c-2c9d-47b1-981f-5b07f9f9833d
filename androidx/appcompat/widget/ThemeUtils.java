package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.View;
import androidx.appcompat.R.styleable;
import androidx.core.graphics.ColorUtils;

public abstract class ThemeUtils {
    public static final ThreadLocal a;
    public static final int[] b;
    public static final int[] c;
    public static final int[] d;
    public static final int[] e;
    public static final int[] f;
    public static final int[] g;

    static {
        ThemeUtils.a = new ThreadLocal();
        ThemeUtils.b = new int[]{0xFEFEFF62};
        ThemeUtils.c = new int[]{0x101009C};
        ThemeUtils.d = new int[]{0x10100A7};
        ThemeUtils.e = new int[]{0x10100A0};
        ThemeUtils.f = new int[0];
        ThemeUtils.g = new int[1];
    }

    public static void a(View view0, Context context0) {
        TypedArray typedArray0 = context0.obtainStyledAttributes(R.styleable.j);
        try {
            if(!typedArray0.hasValue(0x75)) {
                view0.getClass().toString();
            }
        }
        finally {
            typedArray0.recycle();
        }
    }

    public static int b(int v, Context context0) {
        ColorStateList colorStateList0 = ThemeUtils.d(v, context0);
        if(colorStateList0 != null && colorStateList0.isStateful()) {
            int v1 = colorStateList0.getDefaultColor();
            return colorStateList0.getColorForState(ThemeUtils.b, v1);
        }
        ThreadLocal threadLocal0 = ThemeUtils.a;
        TypedValue typedValue0 = (TypedValue)threadLocal0.get();
        if(typedValue0 == null) {
            typedValue0 = new TypedValue();
            threadLocal0.set(typedValue0);
        }
        context0.getTheme().resolveAttribute(0x1010033, typedValue0, true);
        float f = typedValue0.getFloat();
        int v2 = ThemeUtils.c(v, context0);
        return ColorUtils.d(v2, Math.round(((float)Color.alpha(v2)) * f));
    }

    public static int c(int v, Context context0) {
        ThemeUtils.g[0] = v;
        TypedArray typedArray0 = context0.obtainStyledAttributes(null, ThemeUtils.g);
        TintTypedArray tintTypedArray0 = new TintTypedArray(context0, typedArray0);
        try {
            return typedArray0.getColor(0, 0);
        }
        finally {
            tintTypedArray0.f();
        }
    }

    public static ColorStateList d(int v, Context context0) {
        ThemeUtils.g[0] = v;
        TintTypedArray tintTypedArray0 = new TintTypedArray(context0, context0.obtainStyledAttributes(null, ThemeUtils.g));
        try {
            return tintTypedArray0.a(0);
        }
        finally {
            tintTypedArray0.f();
        }
    }
}


package androidx.appcompat.widget;

import android.graphics.Insets;
import android.graphics.Rect;
import android.os.Build.VERSION;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets.Builder;
import java.lang.reflect.Method;

public abstract class ViewUtils {
    static abstract class Api29Impl {
        public static void a(View view0, Rect rect0, Rect rect1) {
            Insets insets0 = view0.computeSystemWindowInsets(new WindowInsets.Builder().setSystemWindowInsets(Insets.of(rect0)).build(), rect1).getSystemWindowInsets();
            rect0.set(insets0.left, insets0.top, insets0.right, insets0.bottom);
        }
    }

    public static boolean a;
    public static Method b;
    public static final boolean c;

    static {
        ViewUtils.c = Build.VERSION.SDK_INT >= 27;
    }

    public static void a(ViewGroup viewGroup0, Rect rect0, Rect rect1) {
        if(Build.VERSION.SDK_INT >= 29) {
            Api29Impl.a(viewGroup0, rect0, rect1);
            return;
        }
        if(!ViewUtils.a) {
            try {
                ViewUtils.a = true;
                Method method0 = View.class.getDeclaredMethod("computeFitSystemWindows", Rect.class, Rect.class);
                ViewUtils.b = method0;
                if(!method0.isAccessible()) {
                    ViewUtils.b.setAccessible(true);
                }
            }
            catch(NoSuchMethodException unused_ex) {
            }
        }
        Method method1 = ViewUtils.b;
        if(method1 != null) {
            try {
                method1.invoke(viewGroup0, rect0, rect1);
            }
            catch(Exception unused_ex) {
            }
        }
    }
}


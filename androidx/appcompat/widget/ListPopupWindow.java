package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View.OnTouchListener;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView.OnScrollListener;
import android.widget.AbsListView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import androidx.appcompat.R.styleable;
import androidx.appcompat.view.menu.ShowableListMenu;
import androidx.core.widget.PopupWindowCompat;
import java.lang.reflect.Method;

public class ListPopupWindow implements ShowableListMenu {
    static abstract class Api24Impl {
        public static int a(PopupWindow popupWindow0, View view0, int v, boolean z) {
            return popupWindow0.getMaxAvailableHeight(view0, v, z);
        }
    }

    static abstract class Api29Impl {
        public static void a(PopupWindow popupWindow0, Rect rect0) {
            popupWindow0.setEpicenterBounds(rect0);
        }

        public static void b(PopupWindow popupWindow0, boolean z) {
            popupWindow0.setIsClippedToScreen(z);
        }
    }

    class ListSelectorHider implements Runnable {
        public final ListPopupWindow a;

        @Override
        public final void run() {
            DropDownListView dropDownListView0 = this.a.c;
            if(dropDownListView0 != null) {
                dropDownListView0.setListSelectionHidden(true);
                dropDownListView0.requestLayout();
            }
        }
    }

    class PopupDataSetObserver extends DataSetObserver {
        public final ListPopupWindow a;

        @Override  // android.database.DataSetObserver
        public final void onChanged() {
            ListPopupWindow listPopupWindow0 = ListPopupWindow.this;
            if(listPopupWindow0.z.isShowing()) {
                listPopupWindow0.show();
            }
        }

        @Override  // android.database.DataSetObserver
        public final void onInvalidated() {
            ListPopupWindow.this.dismiss();
        }
    }

    class PopupScrollListener implements AbsListView.OnScrollListener {
        public final ListPopupWindow a;

        @Override  // android.widget.AbsListView$OnScrollListener
        public final void onScroll(AbsListView absListView0, int v, int v1, int v2) {
        }

        @Override  // android.widget.AbsListView$OnScrollListener
        public final void onScrollStateChanged(AbsListView absListView0, int v) {
            if(v == 1) {
                ListPopupWindow listPopupWindow0 = this.a;
                if(listPopupWindow0.z.getInputMethodMode() != 2 && listPopupWindow0.z.getContentView() != null) {
                    listPopupWindow0.v.removeCallbacks(listPopupWindow0.r);
                    listPopupWindow0.r.run();
                }
            }
        }
    }

    class PopupTouchInterceptor implements View.OnTouchListener {
        public final ListPopupWindow a;

        @Override  // android.view.View$OnTouchListener
        public final boolean onTouch(View view0, MotionEvent motionEvent0) {
            int v = motionEvent0.getAction();
            int v1 = (int)motionEvent0.getX();
            int v2 = (int)motionEvent0.getY();
            ListPopupWindow listPopupWindow0 = this.a;
            if(v == 0 && (listPopupWindow0.z != null && listPopupWindow0.z.isShowing() && v1 >= 0 && v1 < listPopupWindow0.z.getWidth() && v2 >= 0 && v2 < listPopupWindow0.z.getHeight())) {
                listPopupWindow0.v.postDelayed(listPopupWindow0.r, 0xFAL);
                return false;
            }
            if(v == 1) {
                listPopupWindow0.v.removeCallbacks(listPopupWindow0.r);
            }
            return false;
        }
    }

    class ResizePopupRunnable implements Runnable {
        public final ListPopupWindow a;

        @Override
        public final void run() {
            ListPopupWindow listPopupWindow0 = this.a;
            if(listPopupWindow0.c != null && listPopupWindow0.c.isAttachedToWindow() && listPopupWindow0.c.getCount() > listPopupWindow0.c.getChildCount() && listPopupWindow0.c.getChildCount() <= listPopupWindow0.m) {
                listPopupWindow0.z.setInputMethodMode(2);
                listPopupWindow0.show();
            }
        }
    }

    public static final Method A;
    public static final Method B;
    public static final Method C;
    public final Context a;
    public ListAdapter b;
    public DropDownListView c;
    public final int d;
    public int e;
    public int f;
    public int g;
    public final int h;
    public boolean i;
    public boolean j;
    public boolean k;
    public int l;
    public final int m;
    public DataSetObserver n;
    public View o;
    public AdapterView.OnItemClickListener p;
    public AdapterView.OnItemSelectedListener q;
    public final ResizePopupRunnable r;
    public final PopupTouchInterceptor s;
    public final PopupScrollListener t;
    public final ListSelectorHider u;
    public final Handler v;
    public final Rect w;
    public Rect x;
    public boolean y;
    public final PopupWindow z;

    static {
        Class class0 = PopupWindow.class;
        if(Build.VERSION.SDK_INT <= 28) {
            try {
                ListPopupWindow.A = class0.getDeclaredMethod("setClipToScreenEnabled", Boolean.TYPE);
            }
            catch(NoSuchMethodException unused_ex) {
            }
            try {
                ListPopupWindow.C = class0.getDeclaredMethod("setEpicenterBounds", Rect.class);
            }
            catch(NoSuchMethodException unused_ex) {
            }
        }
        if(Build.VERSION.SDK_INT <= 23) {
            try {
                ListPopupWindow.B = class0.getDeclaredMethod("getMaxAvailableHeight", View.class, Integer.TYPE, Boolean.TYPE);
            }
            catch(NoSuchMethodException unused_ex) {
            }
        }
    }

    public ListPopupWindow(Context context0, AttributeSet attributeSet0, int v, int v1) {
        this.d = -2;
        this.e = -2;
        this.h = 1002;
        this.l = 0;
        this.m = 0x7FFFFFFF;
        this.r = new ResizePopupRunnable(this);
        this.s = new PopupTouchInterceptor(this);
        this.t = new PopupScrollListener(this);
        this.u = new ListSelectorHider(this);
        this.w = new Rect();
        this.a = context0;
        this.v = new Handler(context0.getMainLooper());
        TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.o, v, v1);
        this.f = typedArray0.getDimensionPixelOffset(0, 0);
        int v2 = typedArray0.getDimensionPixelOffset(1, 0);
        this.g = v2;
        if(v2 != 0) {
            this.i = true;
        }
        typedArray0.recycle();
        AppCompatPopupWindow appCompatPopupWindow0 = new AppCompatPopupWindow(context0, attributeSet0, v, v1);  // 初始化器: Landroid/widget/PopupWindow;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V
        TypedArray typedArray1 = context0.obtainStyledAttributes(attributeSet0, R.styleable.s, v, v1);
        TintTypedArray tintTypedArray0 = new TintTypedArray(context0, typedArray1);
        if(typedArray1.hasValue(2)) {
            PopupWindowCompat.a(appCompatPopupWindow0, typedArray1.getBoolean(2, false));
        }
        appCompatPopupWindow0.setBackgroundDrawable(tintTypedArray0.b(0));
        tintTypedArray0.f();
        this.z = appCompatPopupWindow0;
        appCompatPopupWindow0.setInputMethodMode(1);
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final boolean a() {
        return this.z.isShowing();
    }

    public final int b() {
        return this.f;
    }

    public final void d(int v) {
        this.f = v;
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final void dismiss() {
        this.z.dismiss();
        this.z.setContentView(null);
        this.c = null;
        this.v.removeCallbacks(this.r);
    }

    public final Drawable g() {
        return this.z.getBackground();
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final ListView i() {
        return this.c;
    }

    public final void j(Drawable drawable0) {
        this.z.setBackgroundDrawable(drawable0);
    }

    public final void k(int v) {
        this.g = v;
        this.i = true;
    }

    public final int n() {
        return this.i ? this.g : 0;
    }

    public void o(ListAdapter listAdapter0) {
        DataSetObserver dataSetObserver0 = this.n;
        if(dataSetObserver0 == null) {
            this.n = new PopupDataSetObserver(this);
        }
        else {
            ListAdapter listAdapter1 = this.b;
            if(listAdapter1 != null) {
                listAdapter1.unregisterDataSetObserver(dataSetObserver0);
            }
        }
        this.b = listAdapter0;
        if(listAdapter0 != null) {
            listAdapter0.registerDataSetObserver(this.n);
        }
        DropDownListView dropDownListView0 = this.c;
        if(dropDownListView0 != null) {
            dropDownListView0.setAdapter(this.b);
        }
    }

    public DropDownListView p(Context context0, boolean z) {
        return new DropDownListView(context0, z);
    }

    public final void q(int v) {
        Drawable drawable0 = this.z.getBackground();
        if(drawable0 != null) {
            drawable0.getPadding(this.w);
            this.e = this.w.left + this.w.right + v;
            return;
        }
        this.e = v;
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final void show() {
        int v11;
        int v8;
        int v6;
        int v4;
        int v2;
        PopupWindow popupWindow0 = this.z;
        Context context0 = this.a;
        if(this.c == null) {
            DropDownListView dropDownListView0 = this.p(context0, !this.y);
            this.c = dropDownListView0;
            dropDownListView0.setAdapter(this.b);
            this.c.setOnItemClickListener(this.p);
            this.c.setFocusable(true);
            this.c.setFocusableInTouchMode(true);
            this.c.setOnItemSelectedListener(new ListPopupWindow.3(this));
            this.c.setOnScrollListener(this.t);
            AdapterView.OnItemSelectedListener adapterView$OnItemSelectedListener0 = this.q;
            if(adapterView$OnItemSelectedListener0 != null) {
                this.c.setOnItemSelectedListener(adapterView$OnItemSelectedListener0);
            }
            popupWindow0.setContentView(this.c);
        }
        else {
            ViewGroup viewGroup0 = (ViewGroup)popupWindow0.getContentView();
        }
        Drawable drawable0 = popupWindow0.getBackground();
        int v = 0;
        Rect rect0 = this.w;
        if(drawable0 == null) {
            rect0.setEmpty();
            v2 = 0;
        }
        else {
            drawable0.getPadding(rect0);
            int v1 = rect0.top;
            v2 = rect0.bottom + v1;
            if(!this.i) {
                this.g = -v1;
            }
        }
        boolean z = popupWindow0.getInputMethodMode() == 2;
        View view0 = this.o;
        int v3 = this.g;
        if(Build.VERSION.SDK_INT <= 23) {
            Method method0 = ListPopupWindow.B;
            if(method0 == null) {
            label_37:
                v4 = popupWindow0.getMaxAvailableHeight(view0, v3);
            }
            else {
                try {
                    v4 = (int)(((Integer)method0.invoke(popupWindow0, view0, v3, Boolean.valueOf(z))));
                    goto label_40;
                }
                catch(Exception unused_ex) {
                }
                goto label_37;
            }
        }
        else {
            v4 = Api24Impl.a(popupWindow0, view0, v3, z);
        }
    label_40:
        int v5 = this.d;
        if(v5 == -1) {
            v6 = v4 + v2;
        }
        else {
            int v7 = this.e;
            switch(v7) {
                case -2: {
                    v8 = View.MeasureSpec.makeMeasureSpec(context0.getResources().getDisplayMetrics().widthPixels - (rect0.left + rect0.right), 0x80000000);
                    break;
                }
                case -1: {
                    v8 = View.MeasureSpec.makeMeasureSpec(context0.getResources().getDisplayMetrics().widthPixels - (rect0.left + rect0.right), 0x40000000);
                    break;
                }
                default: {
                    v8 = View.MeasureSpec.makeMeasureSpec(v7, 0x40000000);
                }
            }
            int v9 = this.c.a(v8, v4);
            if(v9 > 0) {
                int v10 = this.c.getPaddingTop();
                v11 = this.c.getPaddingBottom() + v10 + v2;
            }
            else {
                v11 = 0;
            }
            v6 = v9 + v11;
        }
        boolean z1 = this.z.getInputMethodMode() == 2;
        PopupWindowCompat.b(popupWindow0, this.h);
        if(popupWindow0.isShowing()) {
            if(!this.o.isAttachedToWindow()) {
                return;
            }
            int v12 = this.e;
            if(v12 == -1) {
                v12 = -1;
            }
            else if(v12 == -2) {
                v12 = this.o.getWidth();
            }
            if(v5 == -1) {
                v5 = z1 ? v6 : -1;
                if(z1) {
                    popupWindow0.setWidth((this.e == -1 ? -1 : 0));
                    popupWindow0.setHeight(0);
                }
                else {
                    if(this.e == -1) {
                        v = -1;
                    }
                    popupWindow0.setWidth(v);
                    popupWindow0.setHeight(-1);
                }
            }
            else if(v5 == -2) {
                v5 = v6;
            }
            popupWindow0.setOutsideTouchable(true);
            View view1 = this.o;
            int v13 = this.f;
            int v14 = this.g;
            if(v12 < 0) {
                v12 = -1;
            }
            popupWindow0.update(view1, v13, v14, v12, (v5 >= 0 ? v5 : -1));
            return;
        }
        int v15 = this.e;
        if(v15 == -1) {
            v15 = -1;
        }
        else if(v15 == -2) {
            v15 = this.o.getWidth();
        }
        if(v5 == -1) {
            v5 = -1;
        }
        else if(v5 == -2) {
            v5 = v6;
        }
        popupWindow0.setWidth(v15);
        popupWindow0.setHeight(v5);
        if(Build.VERSION.SDK_INT <= 28) {
            Method method1 = ListPopupWindow.A;
            if(method1 != null) {
                try {
                    method1.invoke(popupWindow0, Boolean.TRUE);
                }
                catch(Exception unused_ex) {
                }
            }
        }
        else {
            Api29Impl.b(popupWindow0, true);
        }
        popupWindow0.setOutsideTouchable(true);
        popupWindow0.setTouchInterceptor(this.s);
        if(this.k) {
            PopupWindowCompat.a(popupWindow0, this.j);
        }
        if(Build.VERSION.SDK_INT <= 28) {
            Method method2 = ListPopupWindow.C;
            if(method2 != null) {
                try {
                    method2.invoke(popupWindow0, this.x);
                }
                catch(Exception unused_ex) {
                }
            }
        }
        else {
            Api29Impl.a(popupWindow0, this.x);
        }
        popupWindow0.showAsDropDown(this.o, this.f, this.g, this.l);
        this.c.setSelection(-1);
        if(!this.y || this.c.isInTouchMode()) {
            DropDownListView dropDownListView1 = this.c;
            if(dropDownListView1 != null) {
                dropDownListView1.setListSelectionHidden(true);
                dropDownListView1.requestLayout();
            }
        }
        if(!this.y) {
            this.v.post(this.u);
        }
    }
}


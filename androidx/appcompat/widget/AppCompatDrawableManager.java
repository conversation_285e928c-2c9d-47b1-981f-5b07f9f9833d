package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.ColorFilter;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.os.Build.VERSION;

public final class AppCompatDrawableManager {
    public ResourceManagerInternal a;
    public static final PorterDuff.Mode b;
    public static AppCompatDrawableManager c;

    static {
        AppCompatDrawableManager.b = PorterDuff.Mode.SRC_IN;
    }

    public static AppCompatDrawableManager a() {
        synchronized(AppCompatDrawableManager.class) {
            if(AppCompatDrawableManager.c == null) {
                AppCompatDrawableManager.d();
            }
            return AppCompatDrawableManager.c;
        }
    }

    public final Drawable b(Context context0, int v) {
        synchronized(this) {
            return this.a.f(context0, v);
        }
    }

    public static PorterDuffColorFilter c(int v, PorterDuff.Mode porterDuff$Mode0) {
        synchronized(AppCompatDrawableManager.class) {
            return ResourceManagerInternal.h(v, porterDuff$Mode0);
        }
    }

    public static void d() {
        synchronized(AppCompatDrawableManager.class) {
            if(AppCompatDrawableManager.c == null) {
                AppCompatDrawableManager appCompatDrawableManager0 = new AppCompatDrawableManager();  // 初始化器: Ljava/lang/Object;-><init>()V
                AppCompatDrawableManager.c = appCompatDrawableManager0;
                appCompatDrawableManager0.a = ResourceManagerInternal.d();
                AppCompatDrawableManager.c.a.l(new AppCompatDrawableManager.1());
            }
        }
    }

    public static void e(Drawable drawable0, TintInfo tintInfo0, int[] arr_v) {
        int[] arr_v1 = drawable0.getState();
        if(drawable0.mutate() == drawable0) {
            if(drawable0 instanceof LayerDrawable && drawable0.isStateful()) {
                drawable0.setState(new int[0]);
                drawable0.setState(arr_v1);
            }
            ColorFilter colorFilter0 = null;
            boolean z = tintInfo0.d;
            if(z || tintInfo0.c) {
                ColorStateList colorStateList0 = z ? tintInfo0.a : null;
                PorterDuff.Mode porterDuff$Mode0 = tintInfo0.c ? tintInfo0.b : ResourceManagerInternal.h;
                if(colorStateList0 != null && porterDuff$Mode0 != null) {
                    colorFilter0 = ResourceManagerInternal.h(colorStateList0.getColorForState(arr_v, 0), porterDuff$Mode0);
                }
                drawable0.setColorFilter(colorFilter0);
            }
            else {
                drawable0.clearColorFilter();
            }
            if(Build.VERSION.SDK_INT <= 23) {
                drawable0.invalidateSelf();
            }
        }
    }
}


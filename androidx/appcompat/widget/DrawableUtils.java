package androidx.appcompat.widget;

import android.graphics.Insets;
import android.graphics.PorterDuff.Mode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import androidx.core.graphics.drawable.DrawableCompat;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public abstract class DrawableUtils {
    static abstract class Api18Impl {
        public static final boolean a;
        public static final Method b;
        public static final Field c;
        public static final Field d;
        public static final Field e;
        public static final Field f;

        static {
            boolean z;
            Field field3;
            Field field1;
            Field field0;
            Method method0;
            Class class0;
            try {
                class0 = Insets.class;
                method0 = null;
                method0 = Drawable.class.getMethod("getOpticalInsets");
            }
            catch(NoSuchMethodException unused_ex) {
                field0 = null;
                field1 = null;
                goto label_20;
            }
            catch(ClassNotFoundException unused_ex) {
                field0 = null;
                field1 = null;
                goto label_20;
            }
            catch(NoSuchFieldException unused_ex) {
                field0 = null;
                field1 = null;
                goto label_20;
            }
            try {
                field0 = null;
                field0 = class0.getField("left");
                field1 = null;
                field1 = class0.getField("top");
                goto label_22;
            }
            catch(NoSuchMethodException | ClassNotFoundException unused_ex) {
                field1 = null;
                goto label_20;
            }
            catch(NoSuchFieldException unused_ex) {
            }
            field1 = null;
            goto label_20;
            try {
                field1 = null;
                field1 = class0.getField("top");
                goto label_22;
            }
            catch(NoSuchMethodException | ClassNotFoundException | NoSuchFieldException unused_ex) {
            }
        label_20:
            Field field2 = null;
            goto label_29;
            try {
            label_22:
                field2 = class0.getField("right");
            }
            catch(NoSuchMethodException | ClassNotFoundException | NoSuchFieldException unused_ex) {
                field2 = null;
                goto label_29;
            }
            try {
                field3 = class0.getField("bottom");
                z = true;
                goto label_31;
            }
            catch(NoSuchMethodException | ClassNotFoundException | NoSuchFieldException unused_ex) {
            }
        label_29:
            field3 = null;
            z = false;
        label_31:
            if(z) {
                Api18Impl.b = method0;
                Api18Impl.c = field0;
                Api18Impl.d = field1;
                Api18Impl.e = field2;
                Api18Impl.f = field3;
                Api18Impl.a = true;
                return;
            }
            Api18Impl.b = null;
            Api18Impl.c = null;
            Api18Impl.d = null;
            Api18Impl.e = null;
            Api18Impl.f = null;
            Api18Impl.a = false;
        }
    }

    static abstract class Api29Impl {
        public static Insets a(Drawable drawable0) {
            return drawable0.getOpticalInsets();
        }
    }

    public static final int[] a;
    public static final int[] b;
    public static final Rect c;

    static {
        DrawableUtils.a = new int[]{0x10100A0};
        DrawableUtils.b = new int[0];
        DrawableUtils.c = new Rect();
    }

    public static void a(Drawable drawable0) {
        String s = drawable0.getClass().getName();
        int v = Build.VERSION.SDK_INT;
        int[] arr_v = DrawableUtils.a;
        int[] arr_v1 = DrawableUtils.b;
        if(v == 21 && "android.graphics.drawable.VectorDrawable".equals(s)) {
            int[] arr_v2 = drawable0.getState();
            if(arr_v2 == null || arr_v2.length == 0) {
                drawable0.setState(arr_v);
            }
            else {
                drawable0.setState(arr_v1);
            }
            drawable0.setState(arr_v2);
            return;
        }
        if(v >= 29 && v < 0x1F && "android.graphics.drawable.ColorStateListDrawable".equals(s)) {
            int[] arr_v3 = drawable0.getState();
            if(arr_v3 == null || arr_v3.length == 0) {
                drawable0.setState(arr_v);
            }
            else {
                drawable0.setState(arr_v1);
            }
            drawable0.setState(arr_v3);
        }
    }

    public static Rect b(Drawable drawable0) {
        int v = Build.VERSION.SDK_INT;
        if(v >= 29) {
            Insets insets0 = Api29Impl.a(drawable0);
            return new Rect(insets0.left, insets0.top, insets0.right, insets0.bottom);
        }
        Drawable drawable1 = DrawableCompat.m(drawable0);
        if(v < 29 && Api18Impl.a) {
            try {
                Object object0 = Api18Impl.b.invoke(drawable1);
                return object0 == null ? DrawableUtils.c : new Rect(Api18Impl.c.getInt(object0), Api18Impl.d.getInt(object0), Api18Impl.e.getInt(object0), Api18Impl.f.getInt(object0));
            }
            catch(IllegalAccessException | InvocationTargetException unused_ex) {
            }
        }
        return DrawableUtils.c;
    }

    public static PorterDuff.Mode c(int v, PorterDuff.Mode porterDuff$Mode0) {
        switch(v) {
            case 3: {
                return PorterDuff.Mode.SRC_OVER;
            }
            case 5: {
                return PorterDuff.Mode.SRC_IN;
            }
            case 9: {
                return PorterDuff.Mode.SRC_ATOP;
            }
            case 14: {
                return PorterDuff.Mode.MULTIPLY;
            }
            case 15: {
                return PorterDuff.Mode.SCREEN;
            }
            case 16: {
                return PorterDuff.Mode.ADD;
            }
            default: {
                return porterDuff$Mode0;
            }
        }
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.text.InputFilter;
import android.util.AttributeSet;
import android.widget.CheckBox;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.widget.TintableCompoundButton;
import androidx.core.widget.TintableCompoundDrawablesView;

public class AppCompatCheckBox extends CheckBox implements TintableCompoundButton, TintableCompoundDrawablesView {
    public final AppCompatCompoundButtonHelper a;
    public final AppCompatBackgroundHelper b;
    public final AppCompatTextHelper c;
    public AppCompatEmojiTextHelper d;

    public AppCompatCheckBox(Context context0, AttributeSet attributeSet0, int v) {
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, v);
        ThemeUtils.a(this, this.getContext());
        AppCompatCompoundButtonHelper appCompatCompoundButtonHelper0 = new AppCompatCompoundButtonHelper(this);
        this.a = appCompatCompoundButtonHelper0;
        appCompatCompoundButtonHelper0.b(attributeSet0, v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = new AppCompatBackgroundHelper(this);
        this.b = appCompatBackgroundHelper0;
        appCompatBackgroundHelper0.d(attributeSet0, v);
        AppCompatTextHelper appCompatTextHelper0 = new AppCompatTextHelper(this);
        this.c = appCompatTextHelper0;
        appCompatTextHelper0.f(attributeSet0, v);
        this.getEmojiTextViewHelper().a(attributeSet0, v);
    }

    @Override  // android.widget.CompoundButton
    public void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    private AppCompatEmojiTextHelper getEmojiTextViewHelper() {
        if(this.d == null) {
            this.d = new AppCompatEmojiTextHelper(this);
        }
        return this.d;
    }

    public ColorStateList getSupportBackgroundTintList() {
        return this.b == null ? null : this.b.b();
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.b == null ? null : this.b.c();
    }

    @Override  // androidx.core.widget.TintableCompoundButton
    public ColorStateList getSupportButtonTintList() {
        return this.a == null ? null : this.a.b;
    }

    public PorterDuff.Mode getSupportButtonTintMode() {
        return this.a == null ? null : this.a.c;
    }

    public ColorStateList getSupportCompoundDrawablesTintList() {
        return this.c.d();
    }

    public PorterDuff.Mode getSupportCompoundDrawablesTintMode() {
        return this.c.e();
    }

    @Override  // android.widget.TextView
    public void setAllCaps(boolean z) {
        super.setAllCaps(z);
        this.getEmojiTextViewHelper().b(z);
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.CompoundButton
    public void setButtonDrawable(int v) {
        this.setButtonDrawable(AppCompatResources.a(this.getContext(), v));
    }

    @Override  // android.widget.CompoundButton
    public void setButtonDrawable(Drawable drawable0) {
        super.setButtonDrawable(drawable0);
        AppCompatCompoundButtonHelper appCompatCompoundButtonHelper0 = this.a;
        if(appCompatCompoundButtonHelper0 != null) {
            if(appCompatCompoundButtonHelper0.f) {
                appCompatCompoundButtonHelper0.f = false;
                return;
            }
            appCompatCompoundButtonHelper0.f = true;
            appCompatCompoundButtonHelper0.a();
        }
    }

    @Override  // android.widget.TextView
    public void setCompoundDrawables(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawables(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public void setCompoundDrawablesRelative(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawablesRelative(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    public void setEmojiCompatEnabled(boolean z) {
        this.getEmojiTextViewHelper().c(z);
    }

    @Override  // android.widget.TextView
    public void setFilters(InputFilter[] arr_inputFilter) {
        super.setFilters(this.getEmojiTextViewHelper().b.a(arr_inputFilter));
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }

    @Override  // androidx.core.widget.TintableCompoundButton
    public void setSupportButtonTintList(ColorStateList colorStateList0) {
        AppCompatCompoundButtonHelper appCompatCompoundButtonHelper0 = this.a;
        if(appCompatCompoundButtonHelper0 != null) {
            appCompatCompoundButtonHelper0.b = colorStateList0;
            appCompatCompoundButtonHelper0.d = true;
            appCompatCompoundButtonHelper0.a();
        }
    }

    @Override  // androidx.core.widget.TintableCompoundButton
    public void setSupportButtonTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatCompoundButtonHelper appCompatCompoundButtonHelper0 = this.a;
        if(appCompatCompoundButtonHelper0 != null) {
            appCompatCompoundButtonHelper0.c = porterDuff$Mode0;
            appCompatCompoundButtonHelper0.e = true;
            appCompatCompoundButtonHelper0.a();
        }
    }

    @Override  // androidx.core.widget.TintableCompoundDrawablesView
    public void setSupportCompoundDrawablesTintList(ColorStateList colorStateList0) {
        this.c.k(colorStateList0);
        this.c.b();
    }

    @Override  // androidx.core.widget.TintableCompoundDrawablesView
    public void setSupportCompoundDrawablesTintMode(PorterDuff.Mode porterDuff$Mode0) {
        this.c.l(porterDuff$Mode0);
        this.c.b();
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.R.styleable;
import androidx.appcompat.view.ActionBarPolicy;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.core.view.ViewPropertyAnimatorListener;

abstract class AbsActionBarView extends ViewGroup {
    public class VisibilityAnimListener implements ViewPropertyAnimatorListener {
        public boolean a;
        public int b;
        public final AbsActionBarView c;

        public VisibilityAnimListener() {
            this.a = false;
        }

        @Override  // androidx.core.view.ViewPropertyAnimatorListener
        public final void a() {
            this.a = true;
        }

        @Override  // androidx.core.view.ViewPropertyAnimatorListener
        public final void b() {
            AbsActionBarView.a(this.c);
            this.a = false;
        }

        @Override  // androidx.core.view.ViewPropertyAnimatorListener
        public final void c() {
            if(this.a) {
                return;
            }
            this.c.f = null;
            this.c.super.setVisibility(this.b);
        }
    }

    public final VisibilityAnimListener a;
    public final Context b;
    public ActionMenuView c;
    public ActionMenuPresenter d;
    public int e;
    public ViewPropertyAnimatorCompat f;
    public boolean g;
    public boolean h;

    public AbsActionBarView(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0, 0x7F040025);  // attr:actionModeStyle
        this.a = new VisibilityAnimListener(this);
        TypedValue typedValue0 = new TypedValue();
        if(context0.getTheme().resolveAttribute(0x7F04000B, typedValue0, true) && typedValue0.resourceId != 0) {  // attr:actionBarPopupTheme
            this.b = new ContextThemeWrapper(context0, typedValue0.resourceId);
            return;
        }
        this.b = context0;
    }

    public static void a(AbsActionBarView absActionBarView0) {
        absActionBarView0.super.setVisibility(0);
    }

    public static int c(View view0, int v, int v1) {
        view0.measure(View.MeasureSpec.makeMeasureSpec(v, 0x80000000), v1);
        return Math.max(0, v - view0.getMeasuredWidth());
    }

    public static int d(View view0, boolean z, int v, int v1, int v2) {
        int v3 = view0.getMeasuredWidth();
        int v4 = view0.getMeasuredHeight();
        int v5 = (v2 - v4) / 2 + v1;
        if(z) {
            view0.layout(v - v3, v5, v, v4 + v5);
            return -v3;
        }
        view0.layout(v, v5, v + v3, v4 + v5);
        return v3;
    }

    @Override  // android.view.View
    public final void onConfigurationChanged(Configuration configuration0) {
        super.onConfigurationChanged(configuration0);
        TypedArray typedArray0 = this.getContext().obtainStyledAttributes(null, R.styleable.a, 0x7F04000E, 0);  // attr:actionBarStyle
        this.setContentHeight(typedArray0.getLayoutDimension(13, 0));
        typedArray0.recycle();
        ActionMenuPresenter actionMenuPresenter0 = this.d;
        if(actionMenuPresenter0 != null) {
            Context context0 = actionMenuPresenter0.b;
            ActionBarPolicy actionBarPolicy0 = new ActionBarPolicy();  // 初始化器: Ljava/lang/Object;-><init>()V
            actionBarPolicy0.a = context0;
            actionMenuPresenter0.q = actionBarPolicy0.a();
            MenuBuilder menuBuilder0 = actionMenuPresenter0.c;
            if(menuBuilder0 != null) {
                menuBuilder0.p(true);
            }
        }
    }

    @Override  // android.view.View
    public boolean onHoverEvent(MotionEvent motionEvent0) {
        int v = motionEvent0.getActionMasked();
        if(v == 9) {
            this.h = false;
        }
        if(!this.h && (v == 9 && !super.onHoverEvent(motionEvent0))) {
            this.h = true;
        }
        if(v == 3 || v == 10) {
            this.h = false;
        }
        return true;
    }

    @Override  // android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent0) {
        int v = motionEvent0.getActionMasked();
        if(v == 0) {
            this.g = false;
        }
        if(!this.g && (v == 0 && !super.onTouchEvent(motionEvent0))) {
            this.g = true;
        }
        if(v == 1 || v == 3) {
            this.g = false;
        }
        return true;
    }

    public abstract void setContentHeight(int arg1);

    @Override  // android.view.View
    public void setVisibility(int v) {
        if(v != this.getVisibility()) {
            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = this.f;
            if(viewPropertyAnimatorCompat0 != null) {
                viewPropertyAnimatorCompat0.b();
            }
            super.setVisibility(v);
        }
    }
}


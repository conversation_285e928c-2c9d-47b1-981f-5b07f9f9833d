package androidx.appcompat.widget;

import android.content.Context;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface;
import android.content.res.ColorStateList;
import android.content.res.Resources.Theme;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.PorterDuff.Mode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.supportv1.v4.view.b;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View.BaseSavedState;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.Adapter;
import android.widget.ArrayAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.ThemedSpinnerAdapter;
import androidx.appcompat.R.styleable;
import androidx.appcompat.app.AlertController.AlertParams;
import androidx.appcompat.app.AlertDialog.Builder;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.appcompat.view.ContextThemeWrapper;
import java.util.Objects;

public class AppCompatSpinner extends Spinner {
    static abstract class Api23Impl {
        public static void a(ThemedSpinnerAdapter themedSpinnerAdapter0, Resources.Theme resources$Theme0) {
            if(!Objects.equals(themedSpinnerAdapter0.getDropDownViewTheme(), resources$Theme0)) {
                themedSpinnerAdapter0.setDropDownViewTheme(resources$Theme0);
            }
        }
    }

    class DialogPopup implements DialogInterface.OnClickListener, SpinnerPopup {
        public AlertDialog a;
        public ListAdapter b;
        public CharSequence c;
        public final AppCompatSpinner d;

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final boolean a() {
            return this.a == null ? false : this.a.isShowing();
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final int b() {
            return 0;
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void d(int v) {
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void dismiss() {
            AlertDialog alertDialog0 = this.a;
            if(alertDialog0 != null) {
                alertDialog0.dismiss();
                this.a = null;
            }
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final CharSequence e() {
            return this.c;
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final Drawable g() {
            return null;
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void h(CharSequence charSequence0) {
            this.c = charSequence0;
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void j(Drawable drawable0) {
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void k(int v) {
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void l(int v) {
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void m(int v, int v1) {
            if(this.b == null) {
                return;
            }
            AppCompatSpinner appCompatSpinner0 = this.d;
            Builder alertDialog$Builder0 = new Builder(appCompatSpinner0.getPopupContext());
            CharSequence charSequence0 = this.c;
            AlertParams alertController$AlertParams0 = alertDialog$Builder0.a;
            if(charSequence0 != null) {
                alertController$AlertParams0.d = charSequence0;
            }
            ListAdapter listAdapter0 = this.b;
            int v2 = appCompatSpinner0.getSelectedItemPosition();
            alertController$AlertParams0.n = listAdapter0;
            alertController$AlertParams0.o = this;
            alertController$AlertParams0.t = v2;
            alertController$AlertParams0.s = true;
            AlertDialog alertDialog0 = alertDialog$Builder0.a();
            this.a = alertDialog0;
            ListView listView0 = alertDialog0.h();
            listView0.setTextDirection(v);
            listView0.setTextAlignment(v1);
            this.a.show();
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final int n() {
            return 0;
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void o(ListAdapter listAdapter0) {
            this.b = listAdapter0;
        }

        @Override  // android.content.DialogInterface$OnClickListener
        public final void onClick(DialogInterface dialogInterface0, int v) {
            AppCompatSpinner appCompatSpinner0 = this.d;
            appCompatSpinner0.setSelection(v);
            if(appCompatSpinner0.getOnItemClickListener() != null) {
                appCompatSpinner0.performItemClick(null, v, this.b.getItemId(v));
            }
            this.dismiss();
        }
    }

    static class DropDownAdapter implements ListAdapter, SpinnerAdapter {
        public SpinnerAdapter a;
        public ListAdapter b;

        @Override  // android.widget.ListAdapter
        public final boolean areAllItemsEnabled() {
            return this.b == null ? true : this.b.areAllItemsEnabled();
        }

        @Override  // android.widget.Adapter
        public final int getCount() {
            return this.a == null ? 0 : this.a.getCount();
        }

        @Override  // android.widget.SpinnerAdapter
        public final View getDropDownView(int v, View view0, ViewGroup viewGroup0) {
            return this.a == null ? null : this.a.getDropDownView(v, view0, viewGroup0);
        }

        @Override  // android.widget.Adapter
        public final Object getItem(int v) {
            return this.a == null ? null : this.a.getItem(v);
        }

        @Override  // android.widget.Adapter
        public final long getItemId(int v) {
            return this.a == null ? -1L : this.a.getItemId(v);
        }

        @Override  // android.widget.Adapter
        public final int getItemViewType(int v) {
            return 0;
        }

        @Override  // android.widget.Adapter
        public final View getView(int v, View view0, ViewGroup viewGroup0) {
            return this.getDropDownView(v, view0, viewGroup0);
        }

        @Override  // android.widget.Adapter
        public final int getViewTypeCount() {
            return 1;
        }

        @Override  // android.widget.Adapter
        public final boolean hasStableIds() {
            return this.a != null && this.a.hasStableIds();
        }

        @Override  // android.widget.Adapter
        public final boolean isEmpty() {
            return this.getCount() == 0;
        }

        @Override  // android.widget.ListAdapter
        public final boolean isEnabled(int v) {
            return this.b == null ? true : this.b.isEnabled(v);
        }

        @Override  // android.widget.Adapter
        public final void registerDataSetObserver(DataSetObserver dataSetObserver0) {
            SpinnerAdapter spinnerAdapter0 = this.a;
            if(spinnerAdapter0 != null) {
                spinnerAdapter0.registerDataSetObserver(dataSetObserver0);
            }
        }

        @Override  // android.widget.Adapter
        public final void unregisterDataSetObserver(DataSetObserver dataSetObserver0) {
            SpinnerAdapter spinnerAdapter0 = this.a;
            if(spinnerAdapter0 != null) {
                spinnerAdapter0.unregisterDataSetObserver(dataSetObserver0);
            }
        }
    }

    class DropdownPopup extends ListPopupWindow implements SpinnerPopup {
        public CharSequence D;
        public ListAdapter E;
        public final Rect F;
        public int G;
        public final AppCompatSpinner H;

        public DropdownPopup(Context context0, AttributeSet attributeSet0) {
            super(context0, attributeSet0, 0x7F0404F8, 0);  // attr:spinnerStyle
            this.F = new Rect();
            this.o = appCompatSpinner0;
            this.y = true;
            this.z.setFocusable(true);
            this.p = new AppCompatSpinner.DropdownPopup.1(this);
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final CharSequence e() {
            return this.D;
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void h(CharSequence charSequence0) {
            this.D = charSequence0;
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void l(int v) {
            this.G = v;
        }

        @Override  // androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void m(int v, int v1) {
            boolean z = this.z.isShowing();
            this.r();
            this.z.setInputMethodMode(2);
            this.show();
            DropDownListView dropDownListView0 = this.c;
            dropDownListView0.setChoiceMode(1);
            dropDownListView0.setTextDirection(v);
            dropDownListView0.setTextAlignment(v1);
            AppCompatSpinner appCompatSpinner0 = AppCompatSpinner.this;
            int v2 = appCompatSpinner0.getSelectedItemPosition();
            DropDownListView dropDownListView1 = this.c;
            if(this.z.isShowing() && dropDownListView1 != null) {
                dropDownListView1.setListSelectionHidden(false);
                dropDownListView1.setSelection(v2);
                if(dropDownListView1.getChoiceMode() != 0) {
                    dropDownListView1.setItemChecked(v2, true);
                }
            }
            if(z) {
                return;
            }
            ViewTreeObserver viewTreeObserver0 = appCompatSpinner0.getViewTreeObserver();
            if(viewTreeObserver0 != null) {
                AppCompatSpinner.DropdownPopup.2 appCompatSpinner$DropdownPopup$20 = new AppCompatSpinner.DropdownPopup.2(this);
                viewTreeObserver0.addOnGlobalLayoutListener(appCompatSpinner$DropdownPopup$20);
                AppCompatSpinner.DropdownPopup.3 appCompatSpinner$DropdownPopup$30 = new AppCompatSpinner.DropdownPopup.3(this, appCompatSpinner$DropdownPopup$20);
                this.z.setOnDismissListener(appCompatSpinner$DropdownPopup$30);
            }
        }

        @Override  // androidx.appcompat.widget.ListPopupWindow, androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup
        public final void o(ListAdapter listAdapter0) {
            super.o(listAdapter0);
            this.E = listAdapter0;
        }

        public final void r() {
            int v1;
            PopupWindow popupWindow0 = this.z;
            Drawable drawable0 = popupWindow0.getBackground();
            AppCompatSpinner appCompatSpinner0 = AppCompatSpinner.this;
            if(drawable0 == null) {
                appCompatSpinner0.h.right = 0;
                appCompatSpinner0.h.left = 0;
                v1 = 0;
            }
            else {
                drawable0.getPadding(appCompatSpinner0.h);
                int v = appCompatSpinner0.getLayoutDirection();
                Rect rect0 = appCompatSpinner0.h;
                v1 = v == 1 ? rect0.right : -rect0.left;
            }
            int v2 = appCompatSpinner0.getPaddingLeft();
            int v3 = appCompatSpinner0.getPaddingRight();
            int v4 = appCompatSpinner0.getWidth();
            int v5 = appCompatSpinner0.g;
            if(v5 == -2) {
                int v6 = appCompatSpinner0.a(((SpinnerAdapter)this.E), popupWindow0.getBackground());
                int v7 = appCompatSpinner0.getContext().getResources().getDisplayMetrics().widthPixels - appCompatSpinner0.h.left - appCompatSpinner0.h.right;
                if(v6 > v7) {
                    v6 = v7;
                }
                this.q(Math.max(v6, v4 - v2 - v3));
            }
            else if(v5 == -1) {
                this.q(v4 - v2 - v3);
            }
            else {
                this.q(v5);
            }
            this.f = appCompatSpinner0.getLayoutDirection() == 1 ? v4 - v3 - this.e - this.G + v1 : v2 + this.G + v1;
        }
    }

    static class SavedState extends View.BaseSavedState {
        public static final Parcelable.Creator CREATOR;
        public boolean a;

        static {
            SavedState.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
                @Override  // android.os.Parcelable$Creator
                public final Object createFromParcel(Parcel parcel0) {
                    SavedState appCompatSpinner$SavedState0 = new SavedState(parcel0);  // 初始化器: Landroid/view/View$BaseSavedState;-><init>(Landroid/os/Parcel;)V
                    appCompatSpinner$SavedState0.a = parcel0.readByte() != 0;
                    return appCompatSpinner$SavedState0;
                }

                @Override  // android.os.Parcelable$Creator
                public final Object[] newArray(int v) {
                    return new SavedState[v];
                }
            };
        }

        @Override  // android.view.View$BaseSavedState
        public final void writeToParcel(Parcel parcel0, int v) {
            super.writeToParcel(parcel0, v);
            parcel0.writeByte(((byte)this.a));
        }
    }

    interface SpinnerPopup {
        boolean a();

        int b();

        void d(int arg1);

        void dismiss();

        CharSequence e();

        Drawable g();

        void h(CharSequence arg1);

        void j(Drawable arg1);

        void k(int arg1);

        void l(int arg1);

        void m(int arg1, int arg2);

        int n();

        void o(ListAdapter arg1);
    }

    public final AppCompatBackgroundHelper a;
    public final Context b;
    public final ForwardingListener c;
    public SpinnerAdapter d;
    public final boolean e;
    public final SpinnerPopup f;
    public int g;
    public final Rect h;
    public static final int[] i;

    static {
        AppCompatSpinner.i = new int[]{0x10102F1};
    }

    public AppCompatSpinner(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0, 0x7F0404F8);  // attr:spinnerStyle
        TypedArray typedArray1;
        this.h = new Rect();
        ThemeUtils.a(this, this.getContext());
        int[] arr_v = R.styleable.u;
        TintTypedArray tintTypedArray0 = TintTypedArray.e(context0, attributeSet0, arr_v, 0x7F0404F8, 0);  // attr:spinnerStyle
        this.a = new AppCompatBackgroundHelper(this);
        TypedArray typedArray0 = tintTypedArray0.b;
        int v = typedArray0.getResourceId(4, 0);
        this.b = v == 0 ? context0 : new ContextThemeWrapper(context0, v);
        try {
            int v1 = -1;
            typedArray1 = null;
            typedArray1 = context0.obtainStyledAttributes(attributeSet0, AppCompatSpinner.i, 0x7F0404F8, 0);  // attr:spinnerStyle
            if(typedArray1.hasValue(0)) {
                v1 = typedArray1.getInt(0, 0);
                goto label_24;
            }
            else {
                goto label_26;
            }
            goto label_27;
        }
        catch(Exception unused_ex) {
            if(typedArray1 != null) {
                goto label_24;
            }
            goto label_27;
        }
        catch(Throwable throwable0) {
            if(typedArray1 != null) {
                typedArray1.recycle();
            }
            throw throwable0;
        }
    label_24:
        typedArray1.recycle();
        goto label_27;
    label_26:
        typedArray1.recycle();
    label_27:
        switch(v1) {
            case 0: {
                DialogPopup appCompatSpinner$DialogPopup0 = new DialogPopup(this);
                this.f = appCompatSpinner$DialogPopup0;
                appCompatSpinner$DialogPopup0.c = typedArray0.getString(2);
                break;
            }
            case 1: {
                DropdownPopup appCompatSpinner$DropdownPopup0 = new DropdownPopup(this, this.b, attributeSet0);
                TintTypedArray tintTypedArray1 = TintTypedArray.e(this.b, attributeSet0, arr_v, 0x7F0404F8, 0);  // attr:spinnerStyle
                this.g = tintTypedArray1.b.getLayoutDimension(3, -2);
                appCompatSpinner$DropdownPopup0.j(tintTypedArray1.b(1));
                appCompatSpinner$DropdownPopup0.D = typedArray0.getString(2);
                tintTypedArray1.f();
                this.f = appCompatSpinner$DropdownPopup0;
                this.c = new AppCompatSpinner.1(this, this, appCompatSpinner$DropdownPopup0);
            }
        }
        CharSequence[] arr_charSequence = typedArray0.getTextArray(0);
        if(arr_charSequence != null) {
            ArrayAdapter arrayAdapter0 = new ArrayAdapter(context0, 0x1090008, arr_charSequence);
            arrayAdapter0.setDropDownViewResource(0x7F0D015D);  // layout:support_simple_spinner_dropdown_item
            this.setAdapter(arrayAdapter0);
        }
        tintTypedArray0.f();
        this.e = true;
        SpinnerAdapter spinnerAdapter0 = this.d;
        if(spinnerAdapter0 != null) {
            this.setAdapter(spinnerAdapter0);
            this.d = null;
        }
        this.a.d(attributeSet0, 0x7F0404F8);  // attr:spinnerStyle
    }

    public final int a(SpinnerAdapter spinnerAdapter0, Drawable drawable0) {
        int v = 0;
        if(spinnerAdapter0 == null) {
            return 0;
        }
        int v1 = View.MeasureSpec.makeMeasureSpec(this.getMeasuredWidth(), 0);
        int v2 = View.MeasureSpec.makeMeasureSpec(this.getMeasuredHeight(), 0);
        int v3 = Math.max(0, this.getSelectedItemPosition());
        int v4 = Math.min(spinnerAdapter0.getCount(), v3 + 15);
        int v5 = Math.max(0, v4 - 15);
        View view0 = null;
        int v6 = 0;
        while(v5 < v4) {
            int v7 = spinnerAdapter0.getItemViewType(v5);
            if(v7 != v) {
                view0 = null;
                v = v7;
            }
            view0 = spinnerAdapter0.getView(v5, view0, this);
            if(view0.getLayoutParams() == null) {
                view0.setLayoutParams(new ViewGroup.LayoutParams(-2, -2));
            }
            view0.measure(v1, v2);
            v6 = Math.max(v6, view0.getMeasuredWidth());
            ++v5;
        }
        if(drawable0 != null) {
            drawable0.getPadding(this.h);
            return v6 + (this.h.left + this.h.right);
        }
        return v6;
    }

    @Override  // android.view.ViewGroup
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
    }

    @Override  // android.widget.Spinner
    public int getDropDownHorizontalOffset() {
        return this.f == null ? super.getDropDownHorizontalOffset() : this.f.b();
    }

    @Override  // android.widget.Spinner
    public int getDropDownVerticalOffset() {
        return this.f == null ? super.getDropDownVerticalOffset() : this.f.n();
    }

    @Override  // android.widget.Spinner
    public int getDropDownWidth() {
        return this.f == null ? super.getDropDownWidth() : this.g;
    }

    public final SpinnerPopup getInternalPopup() {
        return this.f;
    }

    @Override  // android.widget.Spinner
    public Drawable getPopupBackground() {
        return this.f == null ? super.getPopupBackground() : this.f.g();
    }

    @Override  // android.widget.Spinner
    public Context getPopupContext() {
        return this.b;
    }

    @Override  // android.widget.Spinner
    public CharSequence getPrompt() {
        return this.f == null ? super.getPrompt() : this.f.e();
    }

    public ColorStateList getSupportBackgroundTintList() {
        return this.a == null ? null : this.a.b();
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.a == null ? null : this.a.c();
    }

    @Override  // android.widget.Spinner
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        SpinnerPopup appCompatSpinner$SpinnerPopup0 = this.f;
        if(appCompatSpinner$SpinnerPopup0 != null && appCompatSpinner$SpinnerPopup0.a()) {
            appCompatSpinner$SpinnerPopup0.dismiss();
        }
    }

    @Override  // android.widget.Spinner
    public final void onMeasure(int v, int v1) {
        super.onMeasure(v, v1);
        if(this.f != null && View.MeasureSpec.getMode(v) == 0x80000000) {
            this.setMeasuredDimension(Math.min(Math.max(this.getMeasuredWidth(), this.a(this.getAdapter(), this.getBackground())), View.MeasureSpec.getSize(v)), this.getMeasuredHeight());
        }
    }

    @Override  // android.widget.Spinner
    public final void onRestoreInstanceState(Parcelable parcelable0) {
        super.onRestoreInstanceState(((SavedState)parcelable0).getSuperState());
        if(((SavedState)parcelable0).a) {
            ViewTreeObserver viewTreeObserver0 = this.getViewTreeObserver();
            if(viewTreeObserver0 != null) {
                viewTreeObserver0.addOnGlobalLayoutListener(new AppCompatSpinner.2(this));
            }
        }
    }

    @Override  // android.widget.Spinner
    public final Parcelable onSaveInstanceState() {
        Parcelable parcelable0 = new SavedState(super.onSaveInstanceState());  // 初始化器: Landroid/view/View$BaseSavedState;-><init>(Landroid/os/Parcelable;)V
        parcelable0.a = this.f != null && this.f.a();
        return parcelable0;
    }

    @Override  // android.widget.Spinner
    public final boolean onTouchEvent(MotionEvent motionEvent0) {
        return this.c == null || !this.c.onTouch(this, motionEvent0) ? super.onTouchEvent(motionEvent0) : true;
    }

    @Override  // android.widget.Spinner
    public final boolean performClick() {
        SpinnerPopup appCompatSpinner$SpinnerPopup0 = this.f;
        if(appCompatSpinner$SpinnerPopup0 != null) {
            if(!appCompatSpinner$SpinnerPopup0.a()) {
                int v = this.getTextDirection();
                int v1 = this.getTextAlignment();
                this.f.m(v, v1);
            }
            return true;
        }
        return super.performClick();
    }

    @Override  // android.widget.Spinner
    public void setAdapter(Adapter adapter0) {
        this.setAdapter(((SpinnerAdapter)adapter0));
    }

    @Override  // android.widget.Spinner
    public void setAdapter(SpinnerAdapter spinnerAdapter0) {
        if(!this.e) {
            this.d = spinnerAdapter0;
            return;
        }
        super.setAdapter(spinnerAdapter0);
        SpinnerPopup appCompatSpinner$SpinnerPopup0 = this.f;
        if(appCompatSpinner$SpinnerPopup0 != null) {
            Resources.Theme resources$Theme0 = (this.b == null ? this.getContext() : this.b).getTheme();
            DropDownAdapter appCompatSpinner$DropDownAdapter0 = new DropDownAdapter();  // 初始化器: Ljava/lang/Object;-><init>()V
            appCompatSpinner$DropDownAdapter0.a = spinnerAdapter0;
            if(spinnerAdapter0 instanceof ListAdapter) {
                appCompatSpinner$DropDownAdapter0.b = (ListAdapter)spinnerAdapter0;
            }
            if(resources$Theme0 != null && Build.VERSION.SDK_INT >= 23 && b.u(spinnerAdapter0)) {
                Api23Impl.a(((ThemedSpinnerAdapter)spinnerAdapter0), resources$Theme0);
            }
            appCompatSpinner$SpinnerPopup0.o(appCompatSpinner$DropDownAdapter0);
        }
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.Spinner
    public void setDropDownHorizontalOffset(int v) {
        SpinnerPopup appCompatSpinner$SpinnerPopup0 = this.f;
        if(appCompatSpinner$SpinnerPopup0 != null) {
            appCompatSpinner$SpinnerPopup0.l(v);
            appCompatSpinner$SpinnerPopup0.d(v);
            return;
        }
        super.setDropDownHorizontalOffset(v);
    }

    @Override  // android.widget.Spinner
    public void setDropDownVerticalOffset(int v) {
        SpinnerPopup appCompatSpinner$SpinnerPopup0 = this.f;
        if(appCompatSpinner$SpinnerPopup0 != null) {
            appCompatSpinner$SpinnerPopup0.k(v);
            return;
        }
        super.setDropDownVerticalOffset(v);
    }

    @Override  // android.widget.Spinner
    public void setDropDownWidth(int v) {
        if(this.f != null) {
            this.g = v;
            return;
        }
        super.setDropDownWidth(v);
    }

    @Override  // android.widget.Spinner
    public void setPopupBackgroundDrawable(Drawable drawable0) {
        SpinnerPopup appCompatSpinner$SpinnerPopup0 = this.f;
        if(appCompatSpinner$SpinnerPopup0 != null) {
            appCompatSpinner$SpinnerPopup0.j(drawable0);
            return;
        }
        super.setPopupBackgroundDrawable(drawable0);
    }

    @Override  // android.widget.Spinner
    public void setPopupBackgroundResource(int v) {
        this.setPopupBackgroundDrawable(AppCompatResources.a(this.getPopupContext(), v));
    }

    @Override  // android.widget.Spinner
    public void setPrompt(CharSequence charSequence0) {
        SpinnerPopup appCompatSpinner$SpinnerPopup0 = this.f;
        if(appCompatSpinner$SpinnerPopup0 != null) {
            appCompatSpinner$SpinnerPopup0.h(charSequence0);
            return;
        }
        super.setPrompt(charSequence0);
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup.MarginLayoutParams;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.R.styleable;
import androidx.appcompat.view.ActionMode;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuView;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorCompat;

public class ActionBarContextView extends AbsActionBarView {
    public CharSequence i;
    public CharSequence j;
    public View k;
    public View l;
    public View m;
    public LinearLayout n;
    public TextView o;
    public TextView p;
    public final int q;
    public final int r;
    public boolean s;
    public final int t;

    public ActionBarContextView(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0);
        TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.d, 0x7F040025, 0);  // attr:actionModeStyle
        TintTypedArray tintTypedArray0 = new TintTypedArray(context0, typedArray0);
        this.setBackground(tintTypedArray0.b(0));
        this.q = typedArray0.getResourceId(5, 0);
        this.r = typedArray0.getResourceId(4, 0);
        this.e = typedArray0.getLayoutDimension(3, 0);
        this.t = typedArray0.getResourceId(2, 0x7F0D0005);  // layout:abc_action_mode_close_item_material
        tintTypedArray0.f();
    }

    public final void e(ActionMode actionMode0) {
        View view0 = this.k;
        if(view0 == null) {
            View view1 = LayoutInflater.from(this.getContext()).inflate(this.t, this, false);
            this.k = view1;
            this.addView(view1);
        }
        else if(view0.getParent() == null) {
            this.addView(this.k);
        }
        View view2 = this.k.findViewById(0x7F0A0061);  // id:action_mode_close_button
        this.l = view2;
        view2.setOnClickListener(new ActionBarContextView.1(actionMode0));
        MenuBuilder menuBuilder0 = actionMode0.e();
        ActionMenuPresenter actionMenuPresenter0 = this.d;
        if(actionMenuPresenter0 != null) {
            actionMenuPresenter0.n();
            ActionButtonSubmenu actionMenuPresenter$ActionButtonSubmenu0 = actionMenuPresenter0.u;
            if(actionMenuPresenter$ActionButtonSubmenu0 != null && actionMenuPresenter$ActionButtonSubmenu0.b()) {
                actionMenuPresenter$ActionButtonSubmenu0.j.dismiss();
            }
        }
        ActionMenuPresenter actionMenuPresenter1 = new ActionMenuPresenter(this.getContext());
        this.d = actionMenuPresenter1;
        actionMenuPresenter1.m = true;
        actionMenuPresenter1.n = true;
        ViewGroup.LayoutParams viewGroup$LayoutParams0 = new ViewGroup.LayoutParams(-2, -1);
        menuBuilder0.b(this.d, this.b);
        ActionMenuPresenter actionMenuPresenter2 = this.d;
        MenuView menuView0 = actionMenuPresenter2.h;
        if(menuView0 == null) {
            MenuView menuView1 = (MenuView)actionMenuPresenter2.d.inflate(actionMenuPresenter2.f, this, false);
            actionMenuPresenter2.h = menuView1;
            menuView1.b(actionMenuPresenter2.c);
            actionMenuPresenter2.d(true);
        }
        MenuView menuView2 = actionMenuPresenter2.h;
        if(menuView0 != menuView2) {
            ((ActionMenuView)menuView2).setPresenter(actionMenuPresenter2);
        }
        this.c = (ActionMenuView)menuView2;
        ((ActionMenuView)menuView2).setBackground(null);
        this.addView(this.c, viewGroup$LayoutParams0);
    }

    public final void f() {
        if(this.n == null) {
            LayoutInflater.from(this.getContext()).inflate(0x7F0D0000, this);  // layout:abc_action_bar_title_item
            LinearLayout linearLayout0 = (LinearLayout)this.getChildAt(this.getChildCount() - 1);
            this.n = linearLayout0;
            this.o = (TextView)linearLayout0.findViewById(0x7F0A0057);  // id:action_bar_title
            this.p = (TextView)this.n.findViewById(0x7F0A0056);  // id:action_bar_subtitle
            int v = this.q;
            if(v != 0) {
                this.o.setTextAppearance(this.getContext(), v);
            }
            int v1 = this.r;
            if(v1 != 0) {
                this.p.setTextAppearance(this.getContext(), v1);
            }
        }
        this.o.setText(this.i);
        this.p.setText(this.j);
        boolean z = TextUtils.isEmpty(this.i);
        boolean z1 = TextUtils.isEmpty(this.j);
        int v2 = 8;
        this.p.setVisibility((!z1 == 0 ? 8 : 0));
        LinearLayout linearLayout1 = this.n;
        if(!z != 0 || !z1 != 0) {
            v2 = 0;
        }
        linearLayout1.setVisibility(v2);
        if(this.n.getParent() == null) {
            this.addView(this.n);
        }
    }

    public final void g() {
        this.removeAllViews();
        this.m = null;
        this.c = null;
        this.d = null;
        View view0 = this.l;
        if(view0 != null) {
            view0.setOnClickListener(null);
        }
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new ViewGroup.MarginLayoutParams(-1, -2);
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet0) {
        return new ViewGroup.MarginLayoutParams(this.getContext(), attributeSet0);
    }

    public int getAnimatedVisibility() {
        return this.f == null ? this.getVisibility() : this.a.b;
    }

    public int getContentHeight() {
        return this.e;
    }

    public CharSequence getSubtitle() {
        return this.j;
    }

    public CharSequence getTitle() {
        return this.i;
    }

    public final ViewPropertyAnimatorCompat h(int v, long v1) {
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = this.f;
        if(viewPropertyAnimatorCompat0 != null) {
            viewPropertyAnimatorCompat0.b();
        }
        VisibilityAnimListener absActionBarView$VisibilityAnimListener0 = this.a;
        if(v == 0) {
            if(this.getVisibility() != 0) {
                this.setAlpha(0.0f);
            }
            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1 = ViewCompat.a(this);
            viewPropertyAnimatorCompat1.a(1.0f);
            viewPropertyAnimatorCompat1.c(v1);
            absActionBarView$VisibilityAnimListener0.c.f = viewPropertyAnimatorCompat1;
            absActionBarView$VisibilityAnimListener0.b = 0;
            viewPropertyAnimatorCompat1.d(absActionBarView$VisibilityAnimListener0);
            return viewPropertyAnimatorCompat1;
        }
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat2 = ViewCompat.a(this);
        viewPropertyAnimatorCompat2.a(0.0f);
        viewPropertyAnimatorCompat2.c(v1);
        absActionBarView$VisibilityAnimListener0.c.f = viewPropertyAnimatorCompat2;
        absActionBarView$VisibilityAnimListener0.b = v;
        viewPropertyAnimatorCompat2.d(absActionBarView$VisibilityAnimListener0);
        return viewPropertyAnimatorCompat2;
    }

    public final void i() {
        ActionMenuPresenter actionMenuPresenter0 = this.d;
        if(actionMenuPresenter0 != null) {
            actionMenuPresenter0.p();
        }
    }

    @Override  // android.view.ViewGroup
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        ActionMenuPresenter actionMenuPresenter0 = this.d;
        if(actionMenuPresenter0 != null) {
            actionMenuPresenter0.n();
            ActionButtonSubmenu actionMenuPresenter$ActionButtonSubmenu0 = this.d.u;
            if(actionMenuPresenter$ActionButtonSubmenu0 != null && actionMenuPresenter$ActionButtonSubmenu0.b()) {
                actionMenuPresenter$ActionButtonSubmenu0.j.dismiss();
            }
        }
    }

    @Override  // android.view.ViewGroup
    public final void onLayout(boolean z, int v, int v1, int v2, int v3) {
        boolean z1 = this.getLayoutDirection() == 1;
        int v4 = z1 ? v2 - v - this.getPaddingRight() : this.getPaddingLeft();
        int v5 = this.getPaddingTop();
        int v6 = v3 - v1 - this.getPaddingTop() - this.getPaddingBottom();
        if(this.k != null && this.k.getVisibility() != 8) {
            ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)this.k.getLayoutParams();
            int v7 = z1 ? viewGroup$MarginLayoutParams0.rightMargin : viewGroup$MarginLayoutParams0.leftMargin;
            int v8 = z1 ? viewGroup$MarginLayoutParams0.leftMargin : viewGroup$MarginLayoutParams0.rightMargin;
            int v9 = z1 ? v4 - v7 : v4 + v7;
            int v10 = v9 + AbsActionBarView.d(this.k, z1, v9, v5, v6);
            v4 = z1 ? v10 - v8 : v10 + v8;
        }
        if(this.n != null && this.m == null && this.n.getVisibility() != 8) {
            v4 += AbsActionBarView.d(this.n, z1, v4, v5, v6);
        }
        View view0 = this.m;
        if(view0 != null) {
            AbsActionBarView.d(view0, z1, v4, v5, v6);
        }
        int v11 = z1 ? this.getPaddingLeft() : v2 - v - this.getPaddingRight();
        ActionMenuView actionMenuView0 = this.c;
        if(actionMenuView0 != null) {
            AbsActionBarView.d(actionMenuView0, !z1, v11, v5, v6);
        }
    }

    @Override  // android.view.View
    public final void onMeasure(int v, int v1) {
        int v2 = 0x40000000;
        if(View.MeasureSpec.getMode(v) != 0x40000000) {
            throw new IllegalStateException(this.getClass().getSimpleName() + " can only be used with android:layout_width=\"match_parent\" (or fill_parent)");
        }
        if(View.MeasureSpec.getMode(v1) == 0) {
            throw new IllegalStateException(this.getClass().getSimpleName() + " can only be used with android:layout_height=\"wrap_content\"");
        }
        int v3 = View.MeasureSpec.getSize(v);
        int v4 = this.e > 0 ? this.e : View.MeasureSpec.getSize(v1);
        int v5 = this.getPaddingTop();
        int v6 = this.getPaddingBottom() + v5;
        int v7 = v3 - this.getPaddingLeft() - this.getPaddingRight();
        int v8 = v4 - v6;
        int v9 = View.MeasureSpec.makeMeasureSpec(v8, 0x80000000);
        View view0 = this.k;
        if(view0 != null) {
            int v10 = AbsActionBarView.c(view0, v7, v9);
            ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)this.k.getLayoutParams();
            v7 = v10 - (viewGroup$MarginLayoutParams0.leftMargin + viewGroup$MarginLayoutParams0.rightMargin);
        }
        if(this.c != null && this.c.getParent() == this) {
            v7 = AbsActionBarView.c(this.c, v7, v9);
        }
        LinearLayout linearLayout0 = this.n;
        if(linearLayout0 != null && this.m == null) {
            if(this.s) {
                this.n.measure(0, v9);
                int v12 = this.n.getMeasuredWidth();
                boolean z = v12 <= v7;
                if(z) {
                    v7 -= v12;
                }
                this.n.setVisibility((z ? 0 : 8));
            }
            else {
                v7 = AbsActionBarView.c(linearLayout0, v7, v9);
            }
        }
        View view1 = this.m;
        if(view1 != null) {
            ViewGroup.LayoutParams viewGroup$LayoutParams0 = view1.getLayoutParams();
            int v13 = viewGroup$LayoutParams0.width;
            if(v13 >= 0) {
                v7 = Math.min(v13, v7);
            }
            int v14 = viewGroup$LayoutParams0.height;
            if(v14 == -2) {
                v2 = 0x80000000;
            }
            if(v14 >= 0) {
                v8 = Math.min(v14, v8);
            }
            this.m.measure(View.MeasureSpec.makeMeasureSpec(v7, (v13 == -2 ? 0x80000000 : 0x40000000)), View.MeasureSpec.makeMeasureSpec(v8, v2));
        }
        if(this.e <= 0) {
            int v15 = this.getChildCount();
            int v16 = 0;
            for(int v11 = 0; v11 < v15; ++v11) {
                int v17 = this.getChildAt(v11).getMeasuredHeight() + v6;
                if(v17 > v16) {
                    v16 = v17;
                }
            }
            this.setMeasuredDimension(v3, v16);
            return;
        }
        this.setMeasuredDimension(v3, v4);
    }

    @Override  // androidx.appcompat.widget.AbsActionBarView
    public void setContentHeight(int v) {
        this.e = v;
    }

    public void setCustomView(View view0) {
        View view1 = this.m;
        if(view1 != null) {
            this.removeView(view1);
        }
        this.m = view0;
        if(view0 != null) {
            LinearLayout linearLayout0 = this.n;
            if(linearLayout0 != null) {
                this.removeView(linearLayout0);
                this.n = null;
            }
        }
        if(view0 != null) {
            this.addView(view0);
        }
        this.requestLayout();
    }

    public void setSubtitle(CharSequence charSequence0) {
        this.j = charSequence0;
        this.f();
    }

    public void setTitle(CharSequence charSequence0) {
        this.i = charSequence0;
        this.f();
        ViewCompat.E(this, charSequence0);
    }

    public void setTitleOptional(boolean z) {
        if(z != this.s) {
            this.requestLayout();
        }
        this.s = z;
    }

    @Override  // androidx.appcompat.widget.AbsActionBarView
    public void setVisibility(int v) {
        super.setVisibility(v);
    }

    @Override  // android.view.ViewGroup
    public final boolean shouldDelayChildPressedState() {
        return false;
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.os.Build.VERSION;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.util.Xml;
import androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat;
import androidx.appcompat.resources.Compatibility.Api21Impl;
import androidx.collection.LongSparseArray;
import androidx.collection.LruCache;
import androidx.collection.SimpleArrayMap;
import androidx.collection.SparseArrayCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat;
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat;
import java.lang.ref.WeakReference;
import java.util.WeakHashMap;

public final class ResourceManagerInternal {
    static class AsldcInflateDelegate implements InflateDelegate {
        @Override  // androidx.appcompat.widget.ResourceManagerInternal$InflateDelegate
        public final Drawable a(Context context0, XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            try {
                return AnimatedStateListDrawableCompat.e(context0, resources$Theme0, context0.getResources(), attributeSet0, xmlResourceParser0);
            }
            catch(Exception unused_ex) {
                return null;
            }
        }
    }

    static class AvdcInflateDelegate implements InflateDelegate {
        @Override  // androidx.appcompat.widget.ResourceManagerInternal$InflateDelegate
        public final Drawable a(Context context0, XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            try {
                Resources resources0 = context0.getResources();
                Drawable drawable0 = new AnimatedVectorDrawableCompat(context0);
                ((AnimatedVectorDrawableCompat)drawable0).inflate(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                return drawable0;
            }
            catch(Exception unused_ex) {
                return null;
            }
        }
    }

    static class ColorFilterLruCache extends LruCache {
    }

    static class DrawableDelegate implements InflateDelegate {
        @Override  // androidx.appcompat.widget.ResourceManagerInternal$InflateDelegate
        public final Drawable a(Context context0, XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            String s = attributeSet0.getClassAttribute();
            if(s != null) {
                try {
                    Drawable drawable0 = (Drawable)DrawableDelegate.class.getClassLoader().loadClass(s).asSubclass(Drawable.class).getDeclaredConstructor().newInstance();
                    Compatibility.Api21Impl.c(drawable0, context0.getResources(), xmlResourceParser0, attributeSet0, resources$Theme0);
                    return drawable0;
                }
                catch(Exception unused_ex) {
                }
            }
            return null;
        }
    }

    interface InflateDelegate {
        Drawable a(Context arg1, XmlResourceParser arg2, AttributeSet arg3, Resources.Theme arg4);
    }

    public interface ResourceManagerHooks {
    }

    static class VdcInflateDelegate implements InflateDelegate {
        @Override  // androidx.appcompat.widget.ResourceManagerInternal$InflateDelegate
        public final Drawable a(Context context0, XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            try {
                Resources resources0 = context0.getResources();
                Drawable drawable0 = new VectorDrawableCompat();
                ((VectorDrawableCompat)drawable0).inflate(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                return drawable0;
            }
            catch(Exception unused_ex) {
                return null;
            }
        }
    }

    public WeakHashMap a;
    public SimpleArrayMap b;
    public SparseArrayCompat c;
    public final WeakHashMap d;
    public TypedValue e;
    public boolean f;
    public ResourceManagerHooks g;
    public static final PorterDuff.Mode h;
    public static ResourceManagerInternal i;
    public static final ColorFilterLruCache j;

    static {
        ResourceManagerInternal.h = PorterDuff.Mode.SRC_IN;
        ResourceManagerInternal.j = new ColorFilterLruCache(6);  // 初始化器: Landroidx/collection/LruCache;-><init>(I)V
    }

    public ResourceManagerInternal() {
        this.d = new WeakHashMap(0);
    }

    public final void a(String s, InflateDelegate resourceManagerInternal$InflateDelegate0) {
        if(this.b == null) {
            this.b = new SimpleArrayMap();
        }
        this.b.put(s, resourceManagerInternal$InflateDelegate0);
    }

    public final void b(Context context0, long v, Drawable drawable0) {
        __monitor_enter(this);
        try {
            Drawable.ConstantState drawable$ConstantState0 = drawable0.getConstantState();
            if(drawable$ConstantState0 != null) {
                LongSparseArray longSparseArray0 = (LongSparseArray)this.d.get(context0);
                if(longSparseArray0 == null) {
                    longSparseArray0 = new LongSparseArray();
                    this.d.put(context0, longSparseArray0);
                }
                longSparseArray0.h(v, new WeakReference(drawable$ConstantState0));
                goto label_12;
            }
            goto label_14;
        }
        catch(Throwable throwable0) {
        }
        __monitor_exit(this);
        throw throwable0;
    label_12:
        __monitor_exit(this);
        return;
    label_14:
        __monitor_exit(this);
    }

    public final Drawable c(int v, Context context0) {
        if(this.e == null) {
            this.e = new TypedValue();
        }
        Drawable drawable0 = null;
        TypedValue typedValue0 = this.e;
        context0.getResources().getValue(v, typedValue0, true);
        long v1 = ((long)typedValue0.assetCookie) << 0x20 | ((long)typedValue0.data);
        Drawable drawable1 = this.e(context0, v1);
        if(drawable1 != null) {
            return drawable1;
        }
        if(this.g != null) {
            switch(v) {
                case 0x7F080080: {  // drawable:abc_cab_background_top_material
                    drawable0 = new LayerDrawable(new Drawable[]{this.f(context0, 0x7F08007F), this.f(context0, 0x7F080081)});  // drawable:abc_cab_background_internal_bg
                    break;
                }
                case 0x7F0800A2: {  // drawable:abc_ratingbar_indicator_material
                    drawable0 = AppCompatDrawableManager.1.c(this, context0, 0x7F070334);  // dimen:abc_star_medium
                    break;
                }
                case 0x7F0800A3: {  // drawable:abc_ratingbar_material
                    drawable0 = AppCompatDrawableManager.1.c(this, context0, 0x7F070333);  // dimen:abc_star_big
                    break;
                }
                case 0x7F0800A4: {  // drawable:abc_ratingbar_small_material
                    drawable0 = AppCompatDrawableManager.1.c(this, context0, 0x7F070335);  // dimen:abc_star_small
                }
            }
        }
        if(drawable0 != null) {
            drawable0.setChangingConfigurations(typedValue0.changingConfigurations);
            this.b(context0, v1, drawable0);
        }
        return drawable0;
    }

    public static ResourceManagerInternal d() {
        synchronized(ResourceManagerInternal.class) {
            if(ResourceManagerInternal.i == null) {
                ResourceManagerInternal resourceManagerInternal0 = new ResourceManagerInternal();
                ResourceManagerInternal.i = resourceManagerInternal0;
                ResourceManagerInternal.j(resourceManagerInternal0);
            }
            return ResourceManagerInternal.i;
        }
    }

    public final Drawable e(Context context0, long v) {
        synchronized(this) {
            LongSparseArray longSparseArray0 = (LongSparseArray)this.d.get(context0);
            if(longSparseArray0 == null) {
                return null;
            }
            WeakReference weakReference0 = (WeakReference)longSparseArray0.e(v, null);
            if(weakReference0 != null) {
                Drawable.ConstantState drawable$ConstantState0 = (Drawable.ConstantState)weakReference0.get();
                if(drawable$ConstantState0 != null) {
                    return drawable$ConstantState0.newDrawable(context0.getResources());
                }
                longSparseArray0.i(v);
            }
            return null;
        }
    }

    public final Drawable f(Context context0, int v) {
        synchronized(this) {
            return this.g(context0, v, false);
        }
    }

    public final Drawable g(Context context0, int v, boolean z) {
        Drawable drawable1;
        synchronized(this) {
            if(!this.f) {
                this.f = true;
                Drawable drawable0 = this.f(context0, 0x7F0800BE);  // drawable:abc_vector_test
                if(drawable0 == null || !(drawable0 instanceof VectorDrawableCompat) && !"android.graphics.drawable.VectorDrawable".equals(drawable0.getClass().getName())) {
                    this.f = false;
                    throw new IllegalStateException("This app has been built with an incorrect configuration. Please configure your build for VectorDrawableCompat.");
                }
            }
            PorterDuff.Mode porterDuff$Mode0 = null;
            if(this.b == null || this.b.isEmpty()) {
                drawable1 = null;
            }
            else {
                SparseArrayCompat sparseArrayCompat0 = this.c;
                if(sparseArrayCompat0 == null) {
                    this.c = new SparseArrayCompat();
                label_18:
                    if(this.e == null) {
                        this.e = new TypedValue();
                    }
                    TypedValue typedValue0 = this.e;
                    Resources resources0 = context0.getResources();
                    resources0.getValue(v, typedValue0, true);
                    long v2 = ((long)typedValue0.assetCookie) << 0x20 | ((long)typedValue0.data);
                    drawable1 = this.e(context0, v2);
                    if(drawable1 == null) {
                        if(typedValue0.string != null && typedValue0.string.toString().endsWith(".xml")) {
                            try {
                                XmlResourceParser xmlResourceParser0 = resources0.getXml(v);
                                AttributeSet attributeSet0 = Xml.asAttributeSet(xmlResourceParser0);
                                do {
                                    int v3 = xmlResourceParser0.next();
                                }
                                while(v3 != 1 && v3 != 2);
                                if(v3 == 2) {
                                    String s1 = xmlResourceParser0.getName();
                                    this.c.a(v, s1);
                                    InflateDelegate resourceManagerInternal$InflateDelegate0 = (InflateDelegate)this.b.getOrDefault(s1, null);
                                    if(resourceManagerInternal$InflateDelegate0 != null) {
                                        drawable1 = resourceManagerInternal$InflateDelegate0.a(context0, xmlResourceParser0, attributeSet0, context0.getTheme());
                                    }
                                    if(drawable1 != null) {
                                        drawable1.setChangingConfigurations(typedValue0.changingConfigurations);
                                        this.b(context0, v2, drawable1);
                                    }
                                }
                            }
                            catch(Exception unused_ex) {
                            }
                        }
                        if(drawable1 == null) {
                            this.c.a(v, "appcompat_skip_skip");
                        }
                    }
                }
                else {
                    String s = (String)sparseArrayCompat0.d(v, null);
                    if(!"appcompat_skip_skip".equals(s) && (s == null || this.b.getOrDefault(s, null) != null)) {
                        goto label_18;
                    }
                    else {
                        drawable1 = null;
                    }
                }
            }
            if(drawable1 == null) {
                drawable1 = this.c(v, context0);
            }
            if(drawable1 == null) {
                drawable1 = ContextCompat.getDrawable(context0, v);
            }
            if(drawable1 != null) {
                ColorStateList colorStateList0 = this.i(v, context0);
                if(colorStateList0 != null) {
                    Drawable drawable2 = DrawableCompat.n(drawable1.mutate());
                    DrawableCompat.k(drawable2, colorStateList0);
                    if(this.g != null && v == 0x7F0800B1) {  // drawable:abc_switch_thumb_material
                        porterDuff$Mode0 = PorterDuff.Mode.MULTIPLY;
                    }
                    if(porterDuff$Mode0 != null) {
                        DrawableCompat.l(drawable2, porterDuff$Mode0);
                    }
                    porterDuff$Mode0 = drawable2;
                }
                else if(this.g == null) {
                label_70:
                    if(this.m(context0, v, drawable1) || !z) {
                    label_71:
                        porterDuff$Mode0 = drawable1;
                    }
                }
                else {
                    switch(v) {
                        case 0x7F0800A2:   // drawable:abc_ratingbar_indicator_material
                        case 0x7F0800A3:   // drawable:abc_ratingbar_material
                        case 0x7F0800A4: {  // drawable:abc_ratingbar_small_material
                            AppCompatDrawableManager.1.e(((LayerDrawable)drawable1).findDrawableByLayerId(0x1020000), ThemeUtils.b(0x7F04014E, context0), AppCompatDrawableManager.b);  // attr:colorControlNormal
                            AppCompatDrawableManager.1.e(((LayerDrawable)drawable1).findDrawableByLayerId(0x102000F), ThemeUtils.c(0x7F04014C, context0), AppCompatDrawableManager.b);  // attr:colorControlActivated
                            AppCompatDrawableManager.1.e(((LayerDrawable)drawable1).findDrawableByLayerId(0x102000D), ThemeUtils.c(0x7F04014C, context0), AppCompatDrawableManager.b);  // attr:colorControlActivated
                            goto label_71;
                        }
                        case 0x7F0800AC: {  // drawable:abc_seekbar_track_material
                            AppCompatDrawableManager.1.e(((LayerDrawable)drawable1).findDrawableByLayerId(0x1020000), ThemeUtils.c(0x7F04014E, context0), AppCompatDrawableManager.b);  // attr:colorControlNormal
                            AppCompatDrawableManager.1.e(((LayerDrawable)drawable1).findDrawableByLayerId(0x102000F), ThemeUtils.c(0x7F04014E, context0), AppCompatDrawableManager.b);  // attr:colorControlNormal
                            AppCompatDrawableManager.1.e(((LayerDrawable)drawable1).findDrawableByLayerId(0x102000D), ThemeUtils.c(0x7F04014C, context0), AppCompatDrawableManager.b);  // attr:colorControlActivated
                            goto label_71;
                        }
                        default: {
                            goto label_70;
                        }
                    }
                }
                drawable1 = porterDuff$Mode0;
            }
            if(drawable1 != null) {
                DrawableUtils.a(drawable1);
            }
            return drawable1;
        }
    }

    public static PorterDuffColorFilter h(int v, PorterDuff.Mode porterDuff$Mode0) {
        synchronized(ResourceManagerInternal.class) {
            ColorFilterLruCache resourceManagerInternal$ColorFilterLruCache0 = ResourceManagerInternal.j;
            resourceManagerInternal$ColorFilterLruCache0.getClass();
            int v2 = (v + 0x1F) * 0x1F;
            PorterDuffColorFilter porterDuffColorFilter0 = (PorterDuffColorFilter)resourceManagerInternal$ColorFilterLruCache0.get(((int)(porterDuff$Mode0.hashCode() + v2)));
            if(porterDuffColorFilter0 == null) {
                porterDuffColorFilter0 = new PorterDuffColorFilter(v, porterDuff$Mode0);
                PorterDuffColorFilter porterDuffColorFilter1 = (PorterDuffColorFilter)resourceManagerInternal$ColorFilterLruCache0.put(((int)(porterDuff$Mode0.hashCode() + v2)), porterDuffColorFilter0);
            }
            return porterDuffColorFilter0;
        }
    }

    public final ColorStateList i(int v, Context context0) {
        ColorStateList colorStateList1;
        synchronized(this) {
            WeakHashMap weakHashMap0 = this.a;
            ColorStateList colorStateList0 = null;
            if(weakHashMap0 == null) {
                colorStateList1 = null;
            }
            else {
                SparseArrayCompat sparseArrayCompat0 = (SparseArrayCompat)weakHashMap0.get(context0);
                colorStateList1 = sparseArrayCompat0 == null ? null : ((ColorStateList)sparseArrayCompat0.d(v, null));
            }
            if(colorStateList1 == null) {
                ResourceManagerHooks resourceManagerInternal$ResourceManagerHooks0 = this.g;
                if(resourceManagerInternal$ResourceManagerHooks0 != null) {
                    colorStateList0 = ((AppCompatDrawableManager.1)resourceManagerInternal$ResourceManagerHooks0).d(v, context0);
                }
                if(colorStateList0 != null) {
                    if(this.a == null) {
                        this.a = new WeakHashMap();
                    }
                    SparseArrayCompat sparseArrayCompat1 = (SparseArrayCompat)this.a.get(context0);
                    if(sparseArrayCompat1 == null) {
                        sparseArrayCompat1 = new SparseArrayCompat();
                        this.a.put(context0, sparseArrayCompat1);
                    }
                    sparseArrayCompat1.a(v, colorStateList0);
                }
                colorStateList1 = colorStateList0;
            }
            return colorStateList1;
        }
    }

    public static void j(ResourceManagerInternal resourceManagerInternal0) {
        if(Build.VERSION.SDK_INT < 24) {
            resourceManagerInternal0.a("vector", new VdcInflateDelegate());  // 初始化器: Ljava/lang/Object;-><init>()V
            resourceManagerInternal0.a("animated-vector", new AvdcInflateDelegate());  // 初始化器: Ljava/lang/Object;-><init>()V
            resourceManagerInternal0.a("animated-selector", new AsldcInflateDelegate());  // 初始化器: Ljava/lang/Object;-><init>()V
            resourceManagerInternal0.a("drawable", new DrawableDelegate());  // 初始化器: Ljava/lang/Object;-><init>()V
        }
    }

    public final void k(Context context0) {
        synchronized(this) {
            LongSparseArray longSparseArray0 = (LongSparseArray)this.d.get(context0);
            if(longSparseArray0 != null) {
                longSparseArray0.b();
            }
        }
    }

    public final void l(ResourceManagerHooks resourceManagerInternal$ResourceManagerHooks0) {
        synchronized(this) {
            this.g = resourceManagerInternal$ResourceManagerHooks0;
        }
    }

    public final boolean m(Context context0, int v, Drawable drawable0) {
        PorterDuffColorFilter porterDuffColorFilter0;
        boolean z;
        int v2;
        int v1;
        ResourceManagerHooks resourceManagerInternal$ResourceManagerHooks0 = this.g;
        if(resourceManagerInternal$ResourceManagerHooks0 != null) {
            PorterDuff.Mode porterDuff$Mode0 = AppCompatDrawableManager.b;
            if(AppCompatDrawableManager.1.a(v, ((AppCompatDrawableManager.1)resourceManagerInternal$ResourceManagerHooks0).a)) {
                v1 = 0x7F04014E;  // attr:colorControlNormal
                v2 = -1;
                z = true;
            }
            else if(AppCompatDrawableManager.1.a(v, ((AppCompatDrawableManager.1)resourceManagerInternal$ResourceManagerHooks0).c)) {
                v1 = 0x7F04014C;  // attr:colorControlActivated
                v2 = -1;
                z = true;
            }
            else if(AppCompatDrawableManager.1.a(v, ((AppCompatDrawableManager.1)resourceManagerInternal$ResourceManagerHooks0).d)) {
                porterDuff$Mode0 = PorterDuff.Mode.MULTIPLY;
                v1 = 0x1010031;
                v2 = -1;
                z = true;
            }
            else {
                switch(v) {
                    case 0x7F080083: {  // drawable:abc_dialog_material_background
                        v1 = 0x1010031;
                        v2 = -1;
                        z = true;
                        break;
                    }
                    case 0x7F080095: {
                        v2 = 41;
                        v1 = 0x1010030;
                        z = true;
                        break;
                    }
                    default: {
                        v1 = 0;
                        v2 = -1;
                        z = false;
                    }
                }
            }
            if(z) {
                Drawable drawable1 = drawable0.mutate();
                int v3 = ThemeUtils.c(v1, context0);
                synchronized(AppCompatDrawableManager.class) {
                    porterDuffColorFilter0 = ResourceManagerInternal.h(v3, porterDuff$Mode0);
                }
                drawable1.setColorFilter(porterDuffColorFilter0);
                if(v2 != -1) {
                    drawable1.setAlpha(v2);
                }
                return true;
            }
        }
        return false;
    }
}


package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.text.InputFilter;
import android.util.AttributeSet;
import android.view.ActionMode.Callback;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputMethodManager;
import android.view.textclassifier.TextClassifier;
import android.widget.TextView;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.text.PrecomputedTextCompat.Params;
import androidx.core.text.PrecomputedTextCompat;
import androidx.core.view.inputmethod.EditorInfoCompat;
import androidx.core.widget.TextViewCompat;
import androidx.core.widget.TintableCompoundDrawablesView;
import androidx.work.impl.model.c;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

public class AppCompatTextView extends TextView implements TintableCompoundDrawablesView {
    interface SuperCaller {
        void a(int arg1);

        void b(int arg1);

        void c(int arg1, float arg2);
    }

    class SuperCallerApi26 implements SuperCaller {
        public final AppCompatTextView a;

        @Override  // androidx.appcompat.widget.AppCompatTextView$SuperCaller
        public void a(int v) {
        }

        @Override  // androidx.appcompat.widget.AppCompatTextView$SuperCaller
        public void b(int v) {
        }

        @Override  // androidx.appcompat.widget.AppCompatTextView$SuperCaller
        public void c(int v, float f) {
        }
    }

    class SuperCallerApi28 extends SuperCallerApi26 {
        public final AppCompatTextView b;

        @Override  // androidx.appcompat.widget.AppCompatTextView$SuperCallerApi26
        public final void a(int v) {
            AppCompatTextView.this.super.setLastBaselineToBottomHeight(v);
        }

        @Override  // androidx.appcompat.widget.AppCompatTextView$SuperCallerApi26
        public final void b(int v) {
            AppCompatTextView.this.super.setFirstBaselineToTopHeight(v);
        }
    }

    class SuperCallerApi34 extends SuperCallerApi28 {
        public final AppCompatTextView c;

        @Override  // androidx.appcompat.widget.AppCompatTextView$SuperCallerApi26
        public final void c(int v, float f) {
            AppCompatTextView.this.super.setLineHeight(v, f);
        }
    }

    public final AppCompatBackgroundHelper a;
    public final AppCompatTextHelper b;
    public final AppCompatTextClassifierHelper c;
    public AppCompatEmojiTextHelper d;
    public boolean e;
    public SuperCallerApi26 f;
    public Future g;

    public AppCompatTextView(Context context0, AttributeSet attributeSet0) {
        this(context0, attributeSet0, 0x1010084);
    }

    public AppCompatTextView(Context context0, AttributeSet attributeSet0, int v) {
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, v);
        this.e = false;
        this.f = null;
        ThemeUtils.a(this, this.getContext());
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = new AppCompatBackgroundHelper(this);
        this.a = appCompatBackgroundHelper0;
        appCompatBackgroundHelper0.d(attributeSet0, v);
        AppCompatTextHelper appCompatTextHelper0 = new AppCompatTextHelper(this);
        this.b = appCompatTextHelper0;
        appCompatTextHelper0.f(attributeSet0, v);
        appCompatTextHelper0.b();
        AppCompatTextClassifierHelper appCompatTextClassifierHelper0 = new AppCompatTextClassifierHelper();  // 初始化器: Ljava/lang/Object;-><init>()V
        appCompatTextClassifierHelper0.a = this;
        this.c = appCompatTextClassifierHelper0;
        this.getEmojiTextViewHelper().a(attributeSet0, v);
    }

    @Override  // android.widget.TextView
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public int getAutoSizeMaxTextSize() {
        if(ViewUtils.c) {
            return ((SuperCallerApi26)this.getSuperCaller()).a.super.getAutoSizeMaxTextSize();
        }
        return this.b == null ? -1 : Math.round(this.b.i.e);
    }

    @Override  // android.widget.TextView
    public int getAutoSizeMinTextSize() {
        if(ViewUtils.c) {
            return ((SuperCallerApi26)this.getSuperCaller()).a.super.getAutoSizeMinTextSize();
        }
        return this.b == null ? -1 : Math.round(this.b.i.d);
    }

    @Override  // android.widget.TextView
    public int getAutoSizeStepGranularity() {
        if(ViewUtils.c) {
            return ((SuperCallerApi26)this.getSuperCaller()).a.super.getAutoSizeStepGranularity();
        }
        return this.b == null ? -1 : Math.round(this.b.i.c);
    }

    @Override  // android.widget.TextView
    public int[] getAutoSizeTextAvailableSizes() {
        if(ViewUtils.c) {
            return ((SuperCallerApi26)this.getSuperCaller()).a.super.getAutoSizeTextAvailableSizes();
        }
        return this.b == null ? new int[0] : this.b.i.f;
    }

    @Override  // android.widget.TextView
    @SuppressLint({"WrongConstant"})
    public int getAutoSizeTextType() {
        if(ViewUtils.c) {
            return ((SuperCallerApi26)this.getSuperCaller()).a.super.getAutoSizeTextType() == 1 ? 1 : 0;
        }
        return this.b == null ? 0 : this.b.i.a;
    }

    @Override  // android.widget.TextView
    public ActionMode.Callback getCustomSelectionActionModeCallback() {
        return TextViewCompat.j(super.getCustomSelectionActionModeCallback());
    }

    private AppCompatEmojiTextHelper getEmojiTextViewHelper() {
        if(this.d == null) {
            this.d = new AppCompatEmojiTextHelper(this);
        }
        return this.d;
    }

    @Override  // android.widget.TextView
    public int getFirstBaselineToTopHeight() {
        return this.getPaddingTop() - this.getPaint().getFontMetricsInt().top;
    }

    @Override  // android.widget.TextView
    public int getLastBaselineToBottomHeight() {
        return this.getPaddingBottom() + this.getPaint().getFontMetricsInt().bottom;
    }

    public SuperCaller getSuperCaller() {
        if(this.f == null) {
            int v = Build.VERSION.SDK_INT;
            if(v >= 34) {
                this.f = new SuperCallerApi34(this);
                return this.f;
            }
            if(v >= 28) {
                this.f = new SuperCallerApi28(this);
                return this.f;
            }
            if(v >= 26) {
                this.f = new SuperCallerApi26(this);
            }
        }
        return this.f;
    }

    public ColorStateList getSupportBackgroundTintList() {
        return this.a == null ? null : this.a.b();
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.a == null ? null : this.a.c();
    }

    public ColorStateList getSupportCompoundDrawablesTintList() {
        return this.b.d();
    }

    public PorterDuff.Mode getSupportCompoundDrawablesTintMode() {
        return this.b.e();
    }

    @Override  // android.widget.TextView
    public CharSequence getText() {
        this.m();
        return super.getText();
    }

    @Override  // android.widget.TextView
    public TextClassifier getTextClassifier() {
        if(Build.VERSION.SDK_INT < 28) {
            AppCompatTextClassifierHelper appCompatTextClassifierHelper0 = this.c;
            if(appCompatTextClassifierHelper0 != null) {
                return appCompatTextClassifierHelper0.b == null ? Api26Impl.a(appCompatTextClassifierHelper0.a) : appCompatTextClassifierHelper0.b;
            }
        }
        return ((SuperCallerApi26)this.getSuperCaller()).a.super.getTextClassifier();
    }

    public Params getTextMetricsParamsCompat() {
        return TextViewCompat.a(this);
    }

    public final void m() {
        Future future0 = this.g;
        if(future0 != null) {
            try {
                this.g = null;
                c.v(future0.get());
                if(Build.VERSION.SDK_INT >= 29) {
                    throw null;
                }
                TextViewCompat.a(this);
                throw null;
            }
            catch(InterruptedException | ExecutionException unused_ex) {
            }
        }
    }

    @Override  // android.widget.TextView
    public final InputConnection onCreateInputConnection(EditorInfo editorInfo0) {
        InputConnection inputConnection0 = super.onCreateInputConnection(editorInfo0);
        this.b.getClass();
        if(Build.VERSION.SDK_INT < 30 && inputConnection0 != null) {
            EditorInfoCompat.a(editorInfo0, this.getText());
        }
        AppCompatHintHelper.a(this, editorInfo0, inputConnection0);
        return inputConnection0;
    }

    @Override  // android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if(Build.VERSION.SDK_INT >= 30 && Build.VERSION.SDK_INT < 33 && this.onCheckIsTextEditor()) {
            ((InputMethodManager)this.getContext().getSystemService("input_method")).isActive(this);
        }
    }

    @Override  // android.widget.TextView
    public final void onLayout(boolean z, int v, int v1, int v2, int v3) {
        super.onLayout(z, v, v1, v2, v3);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null && !ViewUtils.c) {
            appCompatTextHelper0.i.a();
        }
    }

    @Override  // android.widget.TextView
    public void onMeasure(int v, int v1) {
        this.m();
        super.onMeasure(v, v1);
    }

    @Override  // android.widget.TextView
    public final void onTextChanged(CharSequence charSequence0, int v, int v1, int v2) {
        super.onTextChanged(charSequence0, v, v1, v2);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null && !ViewUtils.c) {
            AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = appCompatTextHelper0.i;
            if(appCompatTextViewAutoSizeHelper0.f()) {
                appCompatTextViewAutoSizeHelper0.a();
            }
        }
    }

    @Override  // android.widget.TextView
    public void setAllCaps(boolean z) {
        super.setAllCaps(z);
        this.getEmojiTextViewHelper().b(z);
    }

    @Override  // android.widget.TextView
    public final void setAutoSizeTextTypeUniformWithConfiguration(int v, int v1, int v2, int v3) {
        if(ViewUtils.c) {
            ((SuperCallerApi26)this.getSuperCaller()).a.super.setAutoSizeTextTypeUniformWithConfiguration(v, v1, v2, v3);
            return;
        }
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.h(v, v1, v2, v3);
        }
    }

    @Override  // android.widget.TextView
    public final void setAutoSizeTextTypeUniformWithPresetSizes(int[] arr_v, int v) {
        if(ViewUtils.c) {
            ((SuperCallerApi26)this.getSuperCaller()).a.super.setAutoSizeTextTypeUniformWithPresetSizes(arr_v, v);
            return;
        }
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.i(arr_v, v);
        }
    }

    @Override  // android.widget.TextView
    public void setAutoSizeTextTypeWithDefaults(int v) {
        if(ViewUtils.c) {
            ((SuperCallerApi26)this.getSuperCaller()).a.super.setAutoSizeTextTypeWithDefaults(v);
            return;
        }
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.j(v);
        }
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawables(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawables(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawablesRelative(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawablesRelative(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawablesRelativeWithIntrinsicBounds(int v, int v1, int v2, int v3) {
        Context context0 = this.getContext();
        Drawable drawable0 = null;
        Drawable drawable1 = v == 0 ? null : AppCompatResources.a(context0, v);
        Drawable drawable2 = v1 == 0 ? null : AppCompatResources.a(context0, v1);
        Drawable drawable3 = v2 == 0 ? null : AppCompatResources.a(context0, v2);
        if(v3 != 0) {
            drawable0 = AppCompatResources.a(context0, v3);
        }
        this.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable1, drawable2, drawable3, drawable0);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawablesRelativeWithIntrinsicBounds(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawablesWithIntrinsicBounds(int v, int v1, int v2, int v3) {
        Context context0 = this.getContext();
        Drawable drawable0 = null;
        Drawable drawable1 = v == 0 ? null : AppCompatResources.a(context0, v);
        Drawable drawable2 = v1 == 0 ? null : AppCompatResources.a(context0, v1);
        Drawable drawable3 = v2 == 0 ? null : AppCompatResources.a(context0, v2);
        if(v3 != 0) {
            drawable0 = AppCompatResources.a(context0, v3);
        }
        this.setCompoundDrawablesWithIntrinsicBounds(drawable1, drawable2, drawable3, drawable0);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawablesWithIntrinsicBounds(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawablesWithIntrinsicBounds(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback actionMode$Callback0) {
        super.setCustomSelectionActionModeCallback(TextViewCompat.k(this, actionMode$Callback0));
    }

    public void setEmojiCompatEnabled(boolean z) {
        this.getEmojiTextViewHelper().c(z);
    }

    @Override  // android.widget.TextView
    public void setFilters(InputFilter[] arr_inputFilter) {
        super.setFilters(this.getEmojiTextViewHelper().b.a(arr_inputFilter));
    }

    @Override  // android.widget.TextView
    public void setFirstBaselineToTopHeight(int v) {
        if(Build.VERSION.SDK_INT >= 28) {
            this.getSuperCaller().b(v);
            return;
        }
        TextViewCompat.d(this, v);
    }

    @Override  // android.widget.TextView
    public void setLastBaselineToBottomHeight(int v) {
        if(Build.VERSION.SDK_INT >= 28) {
            this.getSuperCaller().a(v);
            return;
        }
        TextViewCompat.e(this, v);
    }

    @Override  // android.widget.TextView
    public void setLineHeight(int v) {
        TextViewCompat.f(this, v);
    }

    @Override  // android.widget.TextView
    public final void setLineHeight(int v, float f) {
        if(Build.VERSION.SDK_INT >= 34) {
            this.getSuperCaller().c(v, f);
            return;
        }
        TextViewCompat.g(this, v, f);
    }

    public void setPrecomputedText(PrecomputedTextCompat precomputedTextCompat0) {
        if(Build.VERSION.SDK_INT >= 29) {
            throw null;
        }
        TextViewCompat.a(this);
        throw null;
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }

    @Override  // androidx.core.widget.TintableCompoundDrawablesView
    public void setSupportCompoundDrawablesTintList(ColorStateList colorStateList0) {
        this.b.k(colorStateList0);
        this.b.b();
    }

    @Override  // androidx.core.widget.TintableCompoundDrawablesView
    public void setSupportCompoundDrawablesTintMode(PorterDuff.Mode porterDuff$Mode0) {
        this.b.l(porterDuff$Mode0);
        this.b.b();
    }

    @Override  // android.widget.TextView
    public void setTextAppearance(Context context0, int v) {
        super.setTextAppearance(context0, v);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.g(v, context0);
        }
    }

    @Override  // android.widget.TextView
    public void setTextClassifier(TextClassifier textClassifier0) {
        if(Build.VERSION.SDK_INT < 28) {
            AppCompatTextClassifierHelper appCompatTextClassifierHelper0 = this.c;
            if(appCompatTextClassifierHelper0 != null) {
                appCompatTextClassifierHelper0.b = textClassifier0;
                return;
            }
        }
        ((SuperCallerApi26)this.getSuperCaller()).a.super.setTextClassifier(textClassifier0);
    }

    public void setTextFuture(Future future0) {
        this.g = future0;
        if(future0 != null) {
            this.requestLayout();
        }
    }

    public void setTextMetricsParamsCompat(Params precomputedTextCompat$Params0) {
        TextViewCompat.i(this, precomputedTextCompat$Params0);
    }

    @Override  // android.widget.TextView
    public final void setTextSize(int v, float f) {
        if(ViewUtils.c) {
            super.setTextSize(v, f);
            return;
        }
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = appCompatTextHelper0.i;
            if(!appCompatTextViewAutoSizeHelper0.f()) {
                appCompatTextViewAutoSizeHelper0.g(f, v);
            }
        }
    }

    @Override  // android.widget.TextView
    public final void setTypeface(Typeface typeface0, int v) {
        Typeface typeface1;
        if(this.e) {
            return;
        }
        if(typeface0 == null || v <= 0) {
            typeface1 = null;
        }
        else {
            if(this.getContext() == null) {
                throw new IllegalArgumentException("Context cannot be null");
            }
            typeface1 = Typeface.create(typeface0, v);
        }
        this.e = true;
        if(typeface1 != null) {
            typeface0 = typeface1;
        }
        try {
            super.setTypeface(typeface0, v);
            this.e = false;
        }
        catch(Throwable throwable0) {
            this.e = false;
            throw throwable0;
        }
    }
}


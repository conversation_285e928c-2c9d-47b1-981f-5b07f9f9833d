package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Parcel;
import android.os.Parcelable.ClassLoaderCreator;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.text.TextUtils.TruncateAt;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View.OnClickListener;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;
import android.window.OnBackInvokedCallback;
import android.window.OnBackInvokedDispatcher;
import androidx.appcompat.R.styleable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.appcompat.view.CollapsibleActionView;
import androidx.appcompat.view.SupportMenuInflater;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuItemImpl;
import androidx.appcompat.view.menu.MenuPresenter.Callback;
import androidx.appcompat.view.menu.MenuPresenter;
import androidx.appcompat.view.menu.SubMenuBuilder;
import androidx.core.view.MenuHost;
import androidx.core.view.MenuHostHelper;
import androidx.core.view.MenuProvider;
import androidx.core.view.ViewCompat;
import androidx.customview.view.AbsSavedState;
import java.util.ArrayList;
import java.util.Objects;
import l.a;

public class Toolbar extends ViewGroup implements MenuHost {
    static abstract class Api33Impl {
        public static OnBackInvokedDispatcher a(View view0) {
            return view0.findOnBackInvokedDispatcher();
        }

        public static OnBackInvokedCallback b(Runnable runnable0) {
            Objects.requireNonNull(runnable0);
            return new a(runnable0);
        }

        public static void c(Object object0, Object object1) {
            ((OnBackInvokedDispatcher)object0).registerOnBackInvokedCallback(1000000, ((OnBackInvokedCallback)object1));
        }

        public static void d(Object object0, Object object1) {
            ((OnBackInvokedDispatcher)object0).unregisterOnBackInvokedCallback(((OnBackInvokedCallback)object1));
        }
    }

    class ExpandedActionViewMenuPresenter implements MenuPresenter {
        public MenuBuilder a;
        public MenuItemImpl b;
        public final Toolbar c;

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final boolean b() {
            return false;
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final void c(MenuBuilder menuBuilder0, boolean z) {
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final void d(boolean z) {
            if(this.b != null) {
                MenuBuilder menuBuilder0 = this.a;
                if(menuBuilder0 != null) {
                    int v = menuBuilder0.f.size();
                    for(int v1 = 0; v1 < v; ++v1) {
                        if(this.a.getItem(v1) == this.b) {
                            return;
                        }
                    }
                }
                this.f(this.b);
            }
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final boolean f(MenuItemImpl menuItemImpl0) {
            Toolbar toolbar0 = this.c;
            View view0 = toolbar0.i;
            if(view0 instanceof CollapsibleActionView) {
                ((CollapsibleActionView)view0).e();
            }
            toolbar0.removeView(toolbar0.i);
            toolbar0.removeView(toolbar0.h);
            toolbar0.i = null;
            ArrayList arrayList0 = toolbar0.E;
            for(int v = arrayList0.size() - 1; v >= 0; --v) {
                toolbar0.addView(((View)arrayList0.get(v)));
            }
            arrayList0.clear();
            this.b = null;
            toolbar0.requestLayout();
            menuItemImpl0.C = false;
            menuItemImpl0.n.p(false);
            toolbar0.x();
            return true;
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final void g(Context context0, MenuBuilder menuBuilder0) {
            MenuBuilder menuBuilder1 = this.a;
            if(menuBuilder1 != null) {
                MenuItemImpl menuItemImpl0 = this.b;
                if(menuItemImpl0 != null) {
                    menuBuilder1.d(menuItemImpl0);
                }
            }
            this.a = menuBuilder0;
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final int getId() {
            return 0;
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final void h(Parcelable parcelable0) {
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final boolean j(SubMenuBuilder subMenuBuilder0) {
            return false;
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final Parcelable k() {
            return null;
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter
        public final boolean l(MenuItemImpl menuItemImpl0) {
            Toolbar toolbar0 = this.c;
            toolbar0.c();
            ViewParent viewParent0 = toolbar0.h.getParent();
            if(viewParent0 != toolbar0) {
                if(viewParent0 instanceof ViewGroup) {
                    ((ViewGroup)viewParent0).removeView(toolbar0.h);
                }
                toolbar0.addView(toolbar0.h);
            }
            View view0 = menuItemImpl0.getActionView();
            toolbar0.i = view0;
            this.b = menuItemImpl0;
            ViewParent viewParent1 = view0.getParent();
            if(viewParent1 != toolbar0) {
                if(viewParent1 instanceof ViewGroup) {
                    ((ViewGroup)viewParent1).removeView(toolbar0.i);
                }
                LayoutParams toolbar$LayoutParams0 = Toolbar.h();
                toolbar$LayoutParams0.a = toolbar0.n & 0x70 | 0x800003;
                toolbar$LayoutParams0.b = 2;
                toolbar0.i.setLayoutParams(toolbar$LayoutParams0);
                toolbar0.addView(toolbar0.i);
            }
            for(int v = toolbar0.getChildCount() - 1; v >= 0; --v) {
                View view1 = toolbar0.getChildAt(v);
                if(((LayoutParams)view1.getLayoutParams()).b != 2 && view1 != toolbar0.a) {
                    toolbar0.removeViewAt(v);
                    toolbar0.E.add(view1);
                }
            }
            toolbar0.requestLayout();
            menuItemImpl0.C = true;
            menuItemImpl0.n.p(false);
            View view2 = toolbar0.i;
            if(view2 instanceof CollapsibleActionView) {
                ((CollapsibleActionView)view2).c();
            }
            toolbar0.x();
            return true;
        }
    }

    public static class LayoutParams extends androidx.appcompat.app.ActionBar.LayoutParams {
        public int b;

    }

    public interface OnMenuItemClickListener {
        boolean onMenuItemClick(MenuItem arg1);
    }

    public static class SavedState extends AbsSavedState {
        public static final Parcelable.Creator CREATOR;
        public int c;
        public boolean d;

        static {
            SavedState.CREATOR = new Parcelable.ClassLoaderCreator() {  // 初始化器: Ljava/lang/Object;-><init>()V
                @Override  // android.os.Parcelable$Creator
                public final Object createFromParcel(Parcel parcel0) {
                    return new SavedState(parcel0, null);
                }

                @Override  // android.os.Parcelable$ClassLoaderCreator
                public final Object createFromParcel(Parcel parcel0, ClassLoader classLoader0) {
                    return new SavedState(parcel0, classLoader0);
                }

                @Override  // android.os.Parcelable$Creator
                public final Object[] newArray(int v) {
                    return new SavedState[v];
                }
            };
        }

        public SavedState(Parcel parcel0, ClassLoader classLoader0) {
            super(parcel0, classLoader0);
            this.c = parcel0.readInt();
            this.d = parcel0.readInt() != 0;
        }

        @Override  // androidx.customview.view.AbsSavedState
        public final void writeToParcel(Parcel parcel0, int v) {
            super.writeToParcel(parcel0, v);
            parcel0.writeInt(this.c);
            parcel0.writeInt(((int)this.d));
        }
    }

    public ColorStateList A;
    public boolean B;
    public boolean C;
    public final ArrayList D;
    public final ArrayList E;
    public final int[] F;
    public final MenuHostHelper G;
    public ArrayList H;
    public OnMenuItemClickListener I;
    public final androidx.appcompat.widget.ActionMenuView.OnMenuItemClickListener J;
    public ToolbarWidgetWrapper K;
    public ActionMenuPresenter L;
    public ExpandedActionViewMenuPresenter M;
    public Callback N;
    public androidx.appcompat.view.menu.MenuBuilder.Callback O;
    public boolean P;
    public OnBackInvokedCallback Q;
    public OnBackInvokedDispatcher R;
    public boolean S;
    public final Runnable T;
    public ActionMenuView a;
    public AppCompatTextView b;
    public AppCompatTextView c;
    public AppCompatImageButton d;
    public AppCompatImageView e;
    public final Drawable f;
    public final CharSequence g;
    public AppCompatImageButton h;
    public View i;
    public Context j;
    public int k;
    public int l;
    public int m;
    public final int n;
    public final int o;
    public int p;
    public int q;
    public int r;
    public int s;
    public RtlSpacingHelper t;
    public int u;
    public int v;
    public final int w;
    public CharSequence x;
    public CharSequence y;
    public ColorStateList z;

    public Toolbar(Context context0, AttributeSet attributeSet0) {
        this(context0, attributeSet0, 0);
    }

    public Toolbar(Context context0, AttributeSet attributeSet0, int v) {
        super(context0, attributeSet0, 0x7F040611);  // attr:toolbarStyle
        this.w = 0x800013;
        this.D = new ArrayList();
        this.E = new ArrayList();
        this.F = new int[2];
        this.G = new MenuHostHelper(new androidx.appcompat.widget.a(this, 1));
        this.H = new ArrayList();
        this.J = new androidx.appcompat.widget.ActionMenuView.OnMenuItemClickListener() {
            public final Toolbar a;

            {
                this.a = toolbar0;
            }
        };
        this.T = () -> this.a != null && (this.a.t != null && this.a.t.p());
        TintTypedArray tintTypedArray0 = TintTypedArray.e(this.getContext(), attributeSet0, R.styleable.x, 0x7F040611, 0);  // attr:toolbarStyle
        ViewCompat.B(this, context0, R.styleable.x, attributeSet0, tintTypedArray0.b, 0x7F040611);  // attr:toolbarStyle
        TypedArray typedArray0 = tintTypedArray0.b;
        this.l = typedArray0.getResourceId(28, 0);
        this.m = typedArray0.getResourceId(19, 0);
        this.w = typedArray0.getInteger(0, 0x800013);
        this.n = typedArray0.getInteger(2, 0x30);
        int v1 = typedArray0.getDimensionPixelOffset(22, 0);
        if(typedArray0.hasValue(27)) {
            v1 = typedArray0.getDimensionPixelOffset(27, v1);
        }
        this.s = v1;
        this.r = v1;
        this.q = v1;
        this.p = v1;
        int v2 = typedArray0.getDimensionPixelOffset(25, -1);
        if(v2 >= 0) {
            this.p = v2;
        }
        int v3 = typedArray0.getDimensionPixelOffset(24, -1);
        if(v3 >= 0) {
            this.q = v3;
        }
        int v4 = typedArray0.getDimensionPixelOffset(26, -1);
        if(v4 >= 0) {
            this.r = v4;
        }
        int v5 = typedArray0.getDimensionPixelOffset(23, -1);
        if(v5 >= 0) {
            this.s = v5;
        }
        this.o = typedArray0.getDimensionPixelSize(13, -1);
        int v6 = typedArray0.getDimensionPixelOffset(9, 0x80000000);
        int v7 = typedArray0.getDimensionPixelOffset(5, 0x80000000);
        int v8 = typedArray0.getDimensionPixelSize(7, 0);
        int v9 = typedArray0.getDimensionPixelSize(8, 0);
        this.d();
        RtlSpacingHelper rtlSpacingHelper0 = this.t;
        rtlSpacingHelper0.h = false;
        if(v8 != 0x80000000) {
            rtlSpacingHelper0.e = v8;
            rtlSpacingHelper0.a = v8;
        }
        if(v9 != 0x80000000) {
            rtlSpacingHelper0.f = v9;
            rtlSpacingHelper0.b = v9;
        }
        if(v6 != 0x80000000 || v7 != 0x80000000) {
            rtlSpacingHelper0.a(v6, v7);
        }
        this.u = typedArray0.getDimensionPixelOffset(10, 0x80000000);
        this.v = typedArray0.getDimensionPixelOffset(6, 0x80000000);
        this.f = tintTypedArray0.b(4);
        this.g = typedArray0.getText(3);
        CharSequence charSequence0 = typedArray0.getText(21);
        if(!TextUtils.isEmpty(charSequence0)) {
            this.setTitle(charSequence0);
        }
        CharSequence charSequence1 = typedArray0.getText(18);
        if(!TextUtils.isEmpty(charSequence1)) {
            this.setSubtitle(charSequence1);
        }
        this.j = this.getContext();
        this.setPopupTheme(typedArray0.getResourceId(17, 0));
        Drawable drawable0 = tintTypedArray0.b(16);
        if(drawable0 != null) {
            this.setNavigationIcon(drawable0);
        }
        CharSequence charSequence2 = typedArray0.getText(15);
        if(!TextUtils.isEmpty(charSequence2)) {
            this.setNavigationContentDescription(charSequence2);
        }
        Drawable drawable1 = tintTypedArray0.b(11);
        if(drawable1 != null) {
            this.setLogo(drawable1);
        }
        CharSequence charSequence3 = typedArray0.getText(12);
        if(!TextUtils.isEmpty(charSequence3)) {
            this.setLogoDescription(charSequence3);
        }
        if(typedArray0.hasValue(29)) {
            this.setTitleTextColor(tintTypedArray0.a(29));
        }
        if(typedArray0.hasValue(20)) {
            this.setSubtitleTextColor(tintTypedArray0.a(20));
        }
        if(typedArray0.hasValue(14)) {
            this.n(typedArray0.getResourceId(14, 0));
        }
        tintTypedArray0.f();
    }

    public final void a(int v, ArrayList arrayList0) {
        boolean z = this.getLayoutDirection() == 1;
        int v2 = this.getChildCount();
        int v3 = Gravity.getAbsoluteGravity(v, this.getLayoutDirection());
        arrayList0.clear();
        if(z) {
            for(int v4 = v2 - 1; v4 >= 0; --v4) {
                View view0 = this.getChildAt(v4);
                LayoutParams toolbar$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                if(toolbar$LayoutParams0.b == 0 && this.v(view0)) {
                    int v5 = toolbar$LayoutParams0.a;
                    int v6 = this.getLayoutDirection();
                    int v7 = Gravity.getAbsoluteGravity(v5, v6) & 7;
                    if(v7 != 1 && v7 != 3 && v7 != 5) {
                        v7 = v6 == 1 ? 5 : 3;
                    }
                    if(v7 == v3) {
                        arrayList0.add(view0);
                    }
                }
            }
            return;
        }
        for(int v1 = 0; v1 < v2; ++v1) {
            View view1 = this.getChildAt(v1);
            LayoutParams toolbar$LayoutParams1 = (LayoutParams)view1.getLayoutParams();
            if(toolbar$LayoutParams1.b == 0 && this.v(view1)) {
                int v8 = toolbar$LayoutParams1.a;
                int v9 = this.getLayoutDirection();
                int v10 = Gravity.getAbsoluteGravity(v8, v9) & 7;
                if(v10 != 1 && v10 != 3 && v10 != 5) {
                    v10 = v9 == 1 ? 5 : 3;
                }
                if(v10 == v3) {
                    arrayList0.add(view1);
                }
            }
        }
    }

    @Override  // androidx.core.view.MenuHost
    public final void addMenuProvider(MenuProvider menuProvider0) {
        this.G.b.add(menuProvider0);
        this.G.a.run();
    }

    public final void b(View view0, boolean z) {
        LayoutParams toolbar$LayoutParams0;
        ViewGroup.LayoutParams viewGroup$LayoutParams0 = view0.getLayoutParams();
        if(viewGroup$LayoutParams0 == null) {
            toolbar$LayoutParams0 = Toolbar.h();
        }
        else {
            toolbar$LayoutParams0 = this.checkLayoutParams(viewGroup$LayoutParams0) ? ((LayoutParams)viewGroup$LayoutParams0) : Toolbar.i(viewGroup$LayoutParams0);
        }
        toolbar$LayoutParams0.b = 1;
        if(z && this.i != null) {
            view0.setLayoutParams(toolbar$LayoutParams0);
            this.E.add(view0);
            return;
        }
        this.addView(view0, toolbar$LayoutParams0);
    }

    public final void c() {
        if(this.h == null) {
            AppCompatImageButton appCompatImageButton0 = new AppCompatImageButton(this.getContext(), null, 0x7F040610);  // attr:toolbarNavigationButtonStyle
            this.h = appCompatImageButton0;
            appCompatImageButton0.setImageDrawable(this.f);
            this.h.setContentDescription(this.g);
            LayoutParams toolbar$LayoutParams0 = Toolbar.h();
            toolbar$LayoutParams0.a = this.n & 0x70 | 0x800003;
            toolbar$LayoutParams0.b = 2;
            this.h.setLayoutParams(toolbar$LayoutParams0);
            this.h.setOnClickListener(new Toolbar.4(this));
        }
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.ViewGroup
    public final boolean checkLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return super.checkLayoutParams(viewGroup$LayoutParams0) && viewGroup$LayoutParams0 instanceof LayoutParams;
    }

    public final void d() {
        if(this.t == null) {
            RtlSpacingHelper rtlSpacingHelper0 = new RtlSpacingHelper();  // 初始化器: Ljava/lang/Object;-><init>()V
            rtlSpacingHelper0.a = 0;
            rtlSpacingHelper0.b = 0;
            rtlSpacingHelper0.c = 0x80000000;
            rtlSpacingHelper0.d = 0x80000000;
            rtlSpacingHelper0.e = 0;
            rtlSpacingHelper0.f = 0;
            rtlSpacingHelper0.g = false;
            rtlSpacingHelper0.h = false;
            this.t = rtlSpacingHelper0;
        }
    }

    public final void e() {
        this.f();
        ActionMenuView actionMenuView0 = this.a;
        if(actionMenuView0.p == null) {
            MenuBuilder menuBuilder0 = (MenuBuilder)actionMenuView0.getMenu();
            if(this.M == null) {
                this.M = new ExpandedActionViewMenuPresenter(this);
            }
            this.a.setExpandedActionViewsExclusive(true);
            menuBuilder0.b(this.M, this.j);
            this.x();
        }
    }

    public final void f() {
        if(this.a == null) {
            ActionMenuView actionMenuView0 = new ActionMenuView(this.getContext(), null);
            this.a = actionMenuView0;
            actionMenuView0.setPopupTheme(this.k);
            this.a.setOnMenuItemClickListener(this.J);
            ActionMenuView actionMenuView1 = this.a;
            Callback menuPresenter$Callback0 = this.N;
            Toolbar.3 toolbar$30 = new Toolbar.3(this);
            actionMenuView1.u = menuPresenter$Callback0;
            actionMenuView1.v = toolbar$30;
            LayoutParams toolbar$LayoutParams0 = Toolbar.h();
            toolbar$LayoutParams0.a = this.n & 0x70 | 0x800005;
            this.a.setLayoutParams(toolbar$LayoutParams0);
            this.b(this.a, false);
        }
    }

    public final void g() {
        if(this.d == null) {
            this.d = new AppCompatImageButton(this.getContext(), null, 0x7F040610);  // attr:toolbarNavigationButtonStyle
            LayoutParams toolbar$LayoutParams0 = Toolbar.h();
            toolbar$LayoutParams0.a = this.n & 0x70 | 0x800003;
            this.d.setLayoutParams(toolbar$LayoutParams0);
        }
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return Toolbar.h();
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet0) {
        Context context0 = this.getContext();
        ViewGroup.LayoutParams viewGroup$LayoutParams0 = new LayoutParams(context0, attributeSet0);  // 初始化器: Landroid/view/ViewGroup$MarginLayoutParams;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
        viewGroup$LayoutParams0.a = 0;
        TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.b);
        viewGroup$LayoutParams0.a = typedArray0.getInt(0, 0);
        typedArray0.recycle();
        viewGroup$LayoutParams0.b = 0;
        return viewGroup$LayoutParams0;
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return Toolbar.i(viewGroup$LayoutParams0);
    }

    public CharSequence getCollapseContentDescription() {
        return this.h == null ? null : this.h.getContentDescription();
    }

    public Drawable getCollapseIcon() {
        return this.h == null ? null : this.h.getDrawable();
    }

    public int getContentInsetEnd() {
        RtlSpacingHelper rtlSpacingHelper0 = this.t;
        if(rtlSpacingHelper0 != null) {
            return rtlSpacingHelper0.g ? rtlSpacingHelper0.a : rtlSpacingHelper0.b;
        }
        return 0;
    }

    public int getContentInsetEndWithActions() {
        return this.v == 0x80000000 ? this.getContentInsetEnd() : this.v;
    }

    public int getContentInsetLeft() {
        return this.t == null ? 0 : this.t.a;
    }

    public int getContentInsetRight() {
        return this.t == null ? 0 : this.t.b;
    }

    public int getContentInsetStart() {
        RtlSpacingHelper rtlSpacingHelper0 = this.t;
        if(rtlSpacingHelper0 != null) {
            return rtlSpacingHelper0.g ? rtlSpacingHelper0.b : rtlSpacingHelper0.a;
        }
        return 0;
    }

    public int getContentInsetStartWithNavigation() {
        return this.u == 0x80000000 ? this.getContentInsetStart() : this.u;
    }

    public int getCurrentContentInsetEnd() {
        return this.a == null || (this.a.p == null || !this.a.p.hasVisibleItems()) ? this.getContentInsetEnd() : Math.max(this.getContentInsetEnd(), Math.max(this.v, 0));
    }

    public int getCurrentContentInsetLeft() {
        return this.getLayoutDirection() == 1 ? this.getCurrentContentInsetEnd() : this.getCurrentContentInsetStart();
    }

    public int getCurrentContentInsetRight() {
        return this.getLayoutDirection() == 1 ? this.getCurrentContentInsetStart() : this.getCurrentContentInsetEnd();
    }

    public int getCurrentContentInsetStart() {
        return this.getNavigationIcon() == null ? this.getContentInsetStart() : Math.max(this.getContentInsetStart(), Math.max(this.u, 0));
    }

    private ArrayList getCurrentMenuItems() {
        ArrayList arrayList0 = new ArrayList();
        Menu menu0 = this.getMenu();
        for(int v = 0; v < menu0.size(); ++v) {
            arrayList0.add(menu0.getItem(v));
        }
        return arrayList0;
    }

    public Drawable getLogo() {
        return this.e == null ? null : this.e.getDrawable();
    }

    public CharSequence getLogoDescription() {
        return this.e == null ? null : this.e.getContentDescription();
    }

    public Menu getMenu() {
        this.e();
        return this.a.getMenu();
    }

    private MenuInflater getMenuInflater() {
        return new SupportMenuInflater(this.getContext());
    }

    public View getNavButtonView() {
        return this.d;
    }

    public CharSequence getNavigationContentDescription() {
        return this.d == null ? null : this.d.getContentDescription();
    }

    public Drawable getNavigationIcon() {
        return this.d == null ? null : this.d.getDrawable();
    }

    public ActionMenuPresenter getOuterActionMenuPresenter() {
        return this.L;
    }

    public Drawable getOverflowIcon() {
        this.e();
        return this.a.getOverflowIcon();
    }

    public Context getPopupContext() {
        return this.j;
    }

    public int getPopupTheme() {
        return this.k;
    }

    public CharSequence getSubtitle() {
        return this.y;
    }

    public final TextView getSubtitleTextView() {
        return this.c;
    }

    public CharSequence getTitle() {
        return this.x;
    }

    public int getTitleMarginBottom() {
        return this.s;
    }

    public int getTitleMarginEnd() {
        return this.q;
    }

    public int getTitleMarginStart() {
        return this.p;
    }

    public int getTitleMarginTop() {
        return this.r;
    }

    public final TextView getTitleTextView() {
        return this.b;
    }

    public DecorToolbar getWrapper() {
        if(this.K == null) {
            this.K = new ToolbarWidgetWrapper(this, true);
        }
        return this.K;
    }

    public static LayoutParams h() {
        LayoutParams toolbar$LayoutParams0 = new LayoutParams(-2, -2);  // 初始化器: Landroid/view/ViewGroup$MarginLayoutParams;-><init>(II)V
        toolbar$LayoutParams0.b = 0;
        toolbar$LayoutParams0.a = 0x800013;
        return toolbar$LayoutParams0;
    }

    public static LayoutParams i(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        if(viewGroup$LayoutParams0 instanceof LayoutParams) {
            LayoutParams toolbar$LayoutParams0 = new LayoutParams(((LayoutParams)viewGroup$LayoutParams0));  // 初始化器: Landroidx/appcompat/app/ActionBar$LayoutParams;-><init>(Landroidx/appcompat/app/ActionBar$LayoutParams;)V
            toolbar$LayoutParams0.b = ((LayoutParams)viewGroup$LayoutParams0).b;
            return toolbar$LayoutParams0;
        }
        if(viewGroup$LayoutParams0 instanceof androidx.appcompat.app.ActionBar.LayoutParams) {
            LayoutParams toolbar$LayoutParams1 = new LayoutParams(((androidx.appcompat.app.ActionBar.LayoutParams)viewGroup$LayoutParams0));  // 初始化器: Landroidx/appcompat/app/ActionBar$LayoutParams;-><init>(Landroidx/appcompat/app/ActionBar$LayoutParams;)V
            toolbar$LayoutParams1.b = 0;
            return toolbar$LayoutParams1;
        }
        if(viewGroup$LayoutParams0 instanceof ViewGroup.MarginLayoutParams) {
            LayoutParams toolbar$LayoutParams2 = new LayoutParams(((ViewGroup.MarginLayoutParams)viewGroup$LayoutParams0));  // 初始化器: Landroidx/appcompat/app/ActionBar$LayoutParams;-><init>(Landroid/view/ViewGroup$LayoutParams;)V
            toolbar$LayoutParams2.b = 0;
            toolbar$LayoutParams2.leftMargin = ((ViewGroup.MarginLayoutParams)viewGroup$LayoutParams0).leftMargin;
            toolbar$LayoutParams2.topMargin = ((ViewGroup.MarginLayoutParams)viewGroup$LayoutParams0).topMargin;
            toolbar$LayoutParams2.rightMargin = ((ViewGroup.MarginLayoutParams)viewGroup$LayoutParams0).rightMargin;
            toolbar$LayoutParams2.bottomMargin = ((ViewGroup.MarginLayoutParams)viewGroup$LayoutParams0).bottomMargin;
            return toolbar$LayoutParams2;
        }
        LayoutParams toolbar$LayoutParams3 = new LayoutParams(viewGroup$LayoutParams0);  // 初始化器: Landroidx/appcompat/app/ActionBar$LayoutParams;-><init>(Landroid/view/ViewGroup$LayoutParams;)V
        toolbar$LayoutParams3.b = 0;
        return toolbar$LayoutParams3;
    }

    public final int j(int v, View view0) {
        LayoutParams toolbar$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        int v1 = view0.getMeasuredHeight();
        int v2 = v <= 0 ? 0 : (v1 - v) / 2;
        int v3 = toolbar$LayoutParams0.a & 0x70;
        switch((v3 == 16 || v3 == 0x30 || v3 == 80 ? toolbar$LayoutParams0.a & 0x70 : this.w & 0x70)) {
            case 0x30: {
                return this.getPaddingTop() - v2;
            }
            case 80: {
                return this.getHeight() - this.getPaddingBottom() - v1 - toolbar$LayoutParams0.bottomMargin - v2;
            }
            default: {
                int v4 = this.getPaddingTop();
                int v5 = this.getPaddingBottom();
                int v6 = this.getHeight();
                int v7 = (v6 - v4 - v5 - v1) / 2;
                int v8 = toolbar$LayoutParams0.topMargin;
                if(v7 < v8) {
                    return v4 + v8;
                }
                int v9 = v6 - v5 - v1 - v7 - v4;
                int v10 = toolbar$LayoutParams0.bottomMargin;
                if(v9 < v10) {
                    v7 = Math.max(0, v7 - (v10 - v9));
                }
                return v4 + v7;
            }
        }
    }

    public static int k(View view0) {
        ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)view0.getLayoutParams();
        int v = viewGroup$MarginLayoutParams0.getMarginStart();
        return viewGroup$MarginLayoutParams0.getMarginEnd() + v;
    }

    public static int l(View view0) {
        ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)view0.getLayoutParams();
        return viewGroup$MarginLayoutParams0.topMargin + viewGroup$MarginLayoutParams0.bottomMargin;
    }

    public final boolean m() {
        return this.M != null && this.M.b != null;
    }

    public void n(int v) {
        this.getMenuInflater().inflate(v, this.getMenu());
    }

    public final void o() {
        for(Object object0: this.H) {
            this.getMenu().removeItem(((MenuItem)object0).getItemId());
        }
        Menu menu0 = this.getMenu();
        ArrayList arrayList0 = this.getCurrentMenuItems();
        MenuInflater menuInflater0 = this.getMenuInflater();
        for(Object object1: this.G.b) {
            ((MenuProvider)object1).onCreateMenu(menu0, menuInflater0);
        }
        ArrayList arrayList1 = this.getCurrentMenuItems();
        arrayList1.removeAll(arrayList0);
        this.H = arrayList1;
    }

    @Override  // android.view.ViewGroup
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.x();
    }

    @Override  // android.view.ViewGroup
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        this.removeCallbacks(this.T);
        this.x();
    }

    @Override  // android.view.View
    public final boolean onHoverEvent(MotionEvent motionEvent0) {
        int v = motionEvent0.getActionMasked();
        if(v == 9) {
            this.C = false;
        }
        if(!this.C && (v == 9 && !super.onHoverEvent(motionEvent0))) {
            this.C = true;
        }
        if(v == 3 || v == 10) {
            this.C = false;
        }
        return true;
    }

    @Override  // android.view.ViewGroup
    public void onLayout(boolean z, int v, int v1, int v2, int v3) {
        int v43;
        int v39;
        int v35;
        int v31;
        int v27;
        int v21;
        int v14;
        int v13;
        boolean z1 = this.getLayoutDirection() == 1;
        int v4 = this.getWidth();
        int v5 = this.getHeight();
        int v6 = this.getPaddingLeft();
        int v7 = this.getPaddingRight();
        int v8 = this.getPaddingTop();
        int v9 = this.getPaddingBottom();
        int v10 = v4 - v7;
        int[] arr_v = this.F;
        arr_v[1] = 0;
        arr_v[0] = 0;
        int v11 = this.getMinimumHeight();
        int v12 = v11 < 0 ? 0 : Math.min(v11, v3 - v1);
        if(!this.v(this.d)) {
            v14 = v6;
            v13 = v10;
        }
        else if(z1) {
            v13 = this.s(this.d, v10, v12, arr_v);
            v14 = v6;
        }
        else {
            v14 = this.r(this.d, v6, v12, arr_v);
            v13 = v10;
        }
        if(this.v(this.h)) {
            if(z1) {
                v13 = this.s(this.h, v13, v12, arr_v);
            }
            else {
                v14 = this.r(this.h, v14, v12, arr_v);
            }
        }
        if(this.v(this.a)) {
            if(z1) {
                v14 = this.r(this.a, v14, v12, arr_v);
            }
            else {
                v13 = this.s(this.a, v13, v12, arr_v);
            }
        }
        int v15 = this.getCurrentContentInsetLeft();
        int v16 = this.getCurrentContentInsetRight();
        arr_v[0] = Math.max(0, v15 - v14);
        arr_v[1] = Math.max(0, v16 - (v10 - v13));
        int v17 = Math.max(v14, v15);
        int v18 = Math.min(v13, v10 - v16);
        if(this.v(this.i)) {
            if(z1) {
                v18 = this.s(this.i, v18, v12, arr_v);
            }
            else {
                v17 = this.r(this.i, v17, v12, arr_v);
            }
        }
        if(this.v(this.e)) {
            if(z1) {
                v18 = this.s(this.e, v18, v12, arr_v);
            }
            else {
                v17 = this.r(this.e, v17, v12, arr_v);
            }
        }
        boolean z2 = this.v(this.b);
        boolean z3 = this.v(this.c);
        if(z2) {
            LayoutParams toolbar$LayoutParams0 = (LayoutParams)this.b.getLayoutParams();
            int v19 = toolbar$LayoutParams0.topMargin;
            int v20 = this.b.getMeasuredHeight();
            v21 = toolbar$LayoutParams0.bottomMargin + (v20 + v19);
        }
        else {
            v21 = 0;
        }
        if(z3) {
            LayoutParams toolbar$LayoutParams1 = (LayoutParams)this.c.getLayoutParams();
            int v22 = toolbar$LayoutParams1.topMargin;
            v21 += this.c.getMeasuredHeight() + v22 + toolbar$LayoutParams1.bottomMargin;
        }
        if(z2 || z3) {
            AppCompatTextView appCompatTextView0 = z3 ? this.c : this.b;
            LayoutParams toolbar$LayoutParams2 = (LayoutParams)(z2 ? this.b : this.c).getLayoutParams();
            LayoutParams toolbar$LayoutParams3 = (LayoutParams)appCompatTextView0.getLayoutParams();
            boolean z4 = z2 && this.b.getMeasuredWidth() > 0 || z3 && this.c.getMeasuredWidth() > 0;
            switch(this.w & 0x70) {
                case 0x30: {
                    v27 = this.getPaddingTop() + toolbar$LayoutParams2.topMargin + this.r;
                    break;
                }
                case 80: {
                    v27 = v5 - v9 - toolbar$LayoutParams3.bottomMargin - this.s - v21;
                    break;
                }
                default: {
                    int v23 = (v5 - v8 - v9 - v21) / 2;
                    int v24 = toolbar$LayoutParams2.topMargin + this.r;
                    if(v23 < v24) {
                        v23 = v24;
                    }
                    else {
                        int v25 = v5 - v9 - v21 - v23 - v8;
                        int v26 = this.s;
                        if(v25 < toolbar$LayoutParams2.bottomMargin + v26) {
                            v23 = Math.max(0, v23 - (toolbar$LayoutParams3.bottomMargin + v26 - v25));
                        }
                    }
                    v27 = v8 + v23;
                }
            }
            if(z1) {
                int v28 = (z4 ? this.p : 0) - arr_v[1];
                v18 -= Math.max(0, v28);
                arr_v[1] = Math.max(0, -v28);
                if(z2) {
                    LayoutParams toolbar$LayoutParams4 = (LayoutParams)this.b.getLayoutParams();
                    int v29 = v18 - this.b.getMeasuredWidth();
                    int v30 = this.b.getMeasuredHeight() + v27;
                    this.b.layout(v29, v27, v18, v30);
                    v31 = v29 - this.q;
                    v27 = v30 + toolbar$LayoutParams4.bottomMargin;
                }
                else {
                    v31 = v18;
                }
                if(z3) {
                    int v32 = v27 + ((LayoutParams)this.c.getLayoutParams()).topMargin;
                    int v33 = this.c.getMeasuredWidth();
                    int v34 = this.c.getMeasuredHeight();
                    this.c.layout(v18 - v33, v32, v18, v34 + v32);
                    v35 = v18 - this.q;
                }
                else {
                    v35 = v18;
                }
                if(z4) {
                    v18 = Math.min(v31, v35);
                }
            }
            else {
                int v36 = (z4 ? this.p : 0) - arr_v[0];
                v17 += Math.max(0, v36);
                arr_v[0] = Math.max(0, -v36);
                if(z2) {
                    LayoutParams toolbar$LayoutParams5 = (LayoutParams)this.b.getLayoutParams();
                    int v37 = this.b.getMeasuredWidth() + v17;
                    int v38 = this.b.getMeasuredHeight() + v27;
                    this.b.layout(v17, v27, v37, v38);
                    v39 = v37 + this.q;
                    v27 = v38 + toolbar$LayoutParams5.bottomMargin;
                }
                else {
                    v39 = v17;
                }
                if(z3) {
                    int v40 = v27 + ((LayoutParams)this.c.getLayoutParams()).topMargin;
                    int v41 = this.c.getMeasuredWidth() + v17;
                    int v42 = this.c.getMeasuredHeight();
                    this.c.layout(v17, v40, v41, v42 + v40);
                    v43 = v41 + this.q;
                }
                else {
                    v43 = v17;
                }
                if(z4) {
                    v17 = Math.max(v39, v43);
                }
            }
        }
        ArrayList arrayList0 = this.D;
        this.a(3, arrayList0);
        int v44 = arrayList0.size();
        int v45 = v17;
        for(int v46 = 0; v46 < v44; ++v46) {
            v45 = this.r(((View)arrayList0.get(v46)), v45, v12, arr_v);
        }
        this.a(5, arrayList0);
        int v47 = arrayList0.size();
        for(int v48 = 0; v48 < v47; ++v48) {
            v18 = this.s(((View)arrayList0.get(v48)), v18, v12, arr_v);
        }
        this.a(1, arrayList0);
        int v49 = arr_v[0];
        int v50 = arrayList0.size();
        int v51 = arr_v[1];
        int v52 = v49;
        int v53 = 0;
        int v54 = 0;
        while(v53 < v50) {
            View view0 = (View)arrayList0.get(v53);
            LayoutParams toolbar$LayoutParams6 = (LayoutParams)view0.getLayoutParams();
            int v55 = toolbar$LayoutParams6.leftMargin - v52;
            int v56 = toolbar$LayoutParams6.rightMargin - v51;
            v54 += view0.getMeasuredWidth() + Math.max(0, v55) + Math.max(0, v56);
            ++v53;
            v51 = Math.max(0, -v56);
            v52 = Math.max(0, -v55);
        }
        int v58 = (v4 - v6 - v7) / 2 + v6 - v54 / 2;
        int v59 = v54 + v58;
        if(v58 >= v45) {
            v45 = v59 <= v18 ? v58 : v58 - (v59 - v18);
        }
        int v60 = arrayList0.size();
        for(int v57 = 0; v57 < v60; ++v57) {
            v45 = this.r(((View)arrayList0.get(v57)), v45, v12, arr_v);
        }
        arrayList0.clear();
    }

    @Override  // android.view.View
    public final void onMeasure(int v, int v1) {
        int v31;
        int v30;
        int v29;
        int v15;
        int v9;
        int v8;
        int v6;
        int v4;
        int v3;
        int v2 = 0;
        if(this.getLayoutDirection() == 1) {
            v3 = 1;
            v4 = 0;
        }
        else {
            v3 = 0;
            v4 = 1;
        }
        if(this.v(this.d)) {
            this.u(this.d, v, 0, v1, this.o);
            int v5 = this.d.getMeasuredWidth();
            v6 = Toolbar.k(this.d) + v5;
            int v7 = this.d.getMeasuredHeight();
            v8 = Math.max(0, Toolbar.l(this.d) + v7);
            v9 = View.combineMeasuredStates(0, this.d.getMeasuredState());
        }
        else {
            v6 = 0;
            v8 = 0;
            v9 = 0;
        }
        if(this.v(this.h)) {
            this.u(this.h, v, 0, v1, this.o);
            int v10 = this.h.getMeasuredWidth();
            v6 = Toolbar.k(this.h) + v10;
            int v11 = this.h.getMeasuredHeight();
            v8 = Math.max(v8, Toolbar.l(this.h) + v11);
            v9 = View.combineMeasuredStates(v9, this.h.getMeasuredState());
        }
        int v12 = this.getCurrentContentInsetStart();
        int v13 = Math.max(v12, v6);
        int[] arr_v = this.F;
        arr_v[v3] = Math.max(0, v12 - v6);
        if(this.v(this.a)) {
            this.u(this.a, v, v13, v1, this.o);
            int v14 = this.a.getMeasuredWidth();
            v15 = Toolbar.k(this.a) + v14;
            int v16 = this.a.getMeasuredHeight();
            v8 = Math.max(v8, Toolbar.l(this.a) + v16);
            v9 = View.combineMeasuredStates(v9, this.a.getMeasuredState());
        }
        else {
            v15 = 0;
        }
        int v17 = this.getCurrentContentInsetEnd();
        int v18 = v13 + Math.max(v17, v15);
        arr_v[v4] = Math.max(0, v17 - v15);
        if(this.v(this.i)) {
            v18 += this.t(this.i, v, v18, v1, 0, arr_v);
            int v19 = this.i.getMeasuredHeight();
            v8 = Math.max(v8, Toolbar.l(this.i) + v19);
            v9 = View.combineMeasuredStates(v9, this.i.getMeasuredState());
        }
        if(this.v(this.e)) {
            v18 += this.t(this.e, v, v18, v1, 0, arr_v);
            int v20 = this.e.getMeasuredHeight();
            v8 = Math.max(v8, Toolbar.l(this.e) + v20);
            v9 = View.combineMeasuredStates(v9, this.e.getMeasuredState());
        }
        int v21 = this.getChildCount();
        for(int v22 = 0; v22 < v21; ++v22) {
            View view0 = this.getChildAt(v22);
            if(((LayoutParams)view0.getLayoutParams()).b == 0 && this.v(view0)) {
                v18 += this.t(view0, v, v18, v1, 0, arr_v);
                int v23 = view0.getMeasuredHeight();
                v8 = Math.max(v8, Toolbar.l(view0) + v23);
                v9 = View.combineMeasuredStates(v9, view0.getMeasuredState());
            }
        }
        int v24 = this.r + this.s;
        int v25 = this.p + this.q;
        if(this.v(this.b)) {
            this.t(this.b, v, v18 + v25, v1, v24, arr_v);
            int v26 = this.b.getMeasuredWidth();
            int v27 = Toolbar.k(this.b);
            int v28 = this.b.getMeasuredHeight();
            v29 = Toolbar.l(this.b) + v28;
            v30 = View.combineMeasuredStates(v9, this.b.getMeasuredState());
            v31 = v27 + v26;
        }
        else {
            v30 = v9;
            v31 = 0;
            v29 = 0;
        }
        if(this.v(this.c)) {
            v31 = Math.max(v31, this.t(this.c, v, v18 + v25, v1, v29 + v24, arr_v));
            int v32 = this.c.getMeasuredHeight();
            v29 += Toolbar.l(this.c) + v32;
            v30 = View.combineMeasuredStates(v30, this.c.getMeasuredState());
        }
        int v33 = this.getPaddingLeft();
        int v34 = this.getPaddingRight();
        int v35 = this.getPaddingTop();
        int v36 = this.getPaddingBottom();
        int v37 = View.resolveSizeAndState(Math.max(v34 + v33 + (v18 + v31), this.getSuggestedMinimumWidth()), v, 0xFF000000 & v30);
        int v38 = View.resolveSizeAndState(Math.max(v36 + v35 + Math.max(v8, v29), this.getSuggestedMinimumHeight()), v1, v30 << 16);
        if(this.P) {
            int v39 = this.getChildCount();
            for(int v40 = 0; v40 < v39; ++v40) {
                View view1 = this.getChildAt(v40);
                if(this.v(view1) && view1.getMeasuredWidth() > 0 && view1.getMeasuredHeight() > 0) {
                    v2 = v38;
                    this.setMeasuredDimension(v37, v2);
                    return;
                }
            }
        }
        else {
            v2 = v38;
        }
        this.setMeasuredDimension(v37, v2);
    }

    @Override  // android.view.View
    public final void onRestoreInstanceState(Parcelable parcelable0) {
        if(!(parcelable0 instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable0);
            return;
        }
        super.onRestoreInstanceState(((SavedState)parcelable0).a);
        MenuBuilder menuBuilder0 = this.a == null ? null : this.a.p;
        int v = ((SavedState)parcelable0).c;
        if(v != 0 && this.M != null && menuBuilder0 != null) {
            MenuItem menuItem0 = menuBuilder0.findItem(v);
            if(menuItem0 != null) {
                menuItem0.expandActionView();
            }
        }
        if(((SavedState)parcelable0).d) {
            this.removeCallbacks(this.T);
            this.post(this.T);
        }
    }

    @Override  // android.view.View
    public final void onRtlPropertiesChanged(int v) {
        super.onRtlPropertiesChanged(v);
        this.d();
        RtlSpacingHelper rtlSpacingHelper0 = this.t;
        if(v == 1 != rtlSpacingHelper0.g) {
            rtlSpacingHelper0.g = v == 1;
            if(rtlSpacingHelper0.h) {
                if(v == 1) {
                    rtlSpacingHelper0.a = rtlSpacingHelper0.d == 0x80000000 ? rtlSpacingHelper0.e : rtlSpacingHelper0.d;
                    rtlSpacingHelper0.b = rtlSpacingHelper0.c == 0x80000000 ? rtlSpacingHelper0.f : rtlSpacingHelper0.c;
                    return;
                }
                rtlSpacingHelper0.a = rtlSpacingHelper0.c == 0x80000000 ? rtlSpacingHelper0.e : rtlSpacingHelper0.c;
                rtlSpacingHelper0.b = rtlSpacingHelper0.d == 0x80000000 ? rtlSpacingHelper0.f : rtlSpacingHelper0.d;
                return;
            }
            rtlSpacingHelper0.a = rtlSpacingHelper0.e;
            rtlSpacingHelper0.b = rtlSpacingHelper0.f;
        }
    }

    @Override  // android.view.View
    public final Parcelable onSaveInstanceState() {
        Parcelable parcelable0 = new SavedState(super.onSaveInstanceState());  // 初始化器: Landroidx/customview/view/AbsSavedState;-><init>(Landroid/os/Parcelable;)V
        ExpandedActionViewMenuPresenter toolbar$ExpandedActionViewMenuPresenter0 = this.M;
        if(toolbar$ExpandedActionViewMenuPresenter0 != null) {
            MenuItemImpl menuItemImpl0 = toolbar$ExpandedActionViewMenuPresenter0.b;
            if(menuItemImpl0 != null) {
                parcelable0.c = menuItemImpl0.a;
            }
        }
        parcelable0.d = this.q();
        return parcelable0;
    }

    @Override  // android.view.View
    public final boolean onTouchEvent(MotionEvent motionEvent0) {
        int v = motionEvent0.getActionMasked();
        if(v == 0) {
            this.B = false;
        }
        if(!this.B && (v == 0 && !super.onTouchEvent(motionEvent0))) {
            this.B = true;
        }
        if(v == 1 || v == 3) {
            this.B = false;
        }
        return true;
    }

    public final boolean p(View view0) {
        return view0.getParent() == this || this.E.contains(view0);
    }

    public final boolean q() {
        return this.a != null && (this.a.t != null && this.a.t.o());
    }

    public final int r(View view0, int v, int v1, int[] arr_v) {
        LayoutParams toolbar$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        int v2 = toolbar$LayoutParams0.leftMargin - arr_v[0];
        int v3 = Math.max(0, v2) + v;
        arr_v[0] = Math.max(0, -v2);
        int v4 = this.j(v1, view0);
        int v5 = view0.getMeasuredWidth();
        view0.layout(v3, v4, v3 + v5, view0.getMeasuredHeight() + v4);
        return v5 + toolbar$LayoutParams0.rightMargin + v3;
    }

    @Override  // androidx.core.view.MenuHost
    public final void removeMenuProvider(MenuProvider menuProvider0) {
        this.G.c(menuProvider0);
    }

    public final int s(View view0, int v, int v1, int[] arr_v) {
        LayoutParams toolbar$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        int v2 = toolbar$LayoutParams0.rightMargin - arr_v[1];
        int v3 = v - Math.max(0, v2);
        arr_v[1] = Math.max(0, -v2);
        int v4 = this.j(v1, view0);
        int v5 = view0.getMeasuredWidth();
        view0.layout(v3 - v5, v4, v3, view0.getMeasuredHeight() + v4);
        return v3 - (v5 + toolbar$LayoutParams0.leftMargin);
    }

    public void setBackInvokedCallbackEnabled(boolean z) {
        if(this.S != z) {
            this.S = z;
            this.x();
        }
    }

    public void setCollapseContentDescription(int v) {
        this.setCollapseContentDescription((v == 0 ? null : this.getContext().getText(v)));
    }

    public void setCollapseContentDescription(CharSequence charSequence0) {
        if(!TextUtils.isEmpty(charSequence0)) {
            this.c();
        }
        AppCompatImageButton appCompatImageButton0 = this.h;
        if(appCompatImageButton0 != null) {
            appCompatImageButton0.setContentDescription(charSequence0);
        }
    }

    public void setCollapseIcon(int v) {
        this.setCollapseIcon(AppCompatResources.a(this.getContext(), v));
    }

    public void setCollapseIcon(Drawable drawable0) {
        if(drawable0 != null) {
            this.c();
            this.h.setImageDrawable(drawable0);
            return;
        }
        AppCompatImageButton appCompatImageButton0 = this.h;
        if(appCompatImageButton0 != null) {
            appCompatImageButton0.setImageDrawable(this.f);
        }
    }

    public void setCollapsible(boolean z) {
        this.P = z;
        this.requestLayout();
    }

    public void setContentInsetEndWithActions(int v) {
        if(v < 0) {
            v = 0x80000000;
        }
        if(v != this.v) {
            this.v = v;
            if(this.getNavigationIcon() != null) {
                this.requestLayout();
            }
        }
    }

    public void setContentInsetStartWithNavigation(int v) {
        if(v < 0) {
            v = 0x80000000;
        }
        if(v != this.u) {
            this.u = v;
            if(this.getNavigationIcon() != null) {
                this.requestLayout();
            }
        }
    }

    public void setLogo(int v) {
        this.setLogo(AppCompatResources.a(this.getContext(), v));
    }

    public void setLogo(Drawable drawable0) {
        if(drawable0 != null) {
            if(this.e == null) {
                this.e = new AppCompatImageView(this.getContext(), null);
            }
            if(!this.p(this.e)) {
                this.b(this.e, true);
            }
        }
        else if(this.e != null && this.p(this.e)) {
            this.removeView(this.e);
            this.E.remove(this.e);
        }
        AppCompatImageView appCompatImageView0 = this.e;
        if(appCompatImageView0 != null) {
            appCompatImageView0.setImageDrawable(drawable0);
        }
    }

    public void setLogoDescription(int v) {
        this.setLogoDescription(this.getContext().getText(v));
    }

    public void setLogoDescription(CharSequence charSequence0) {
        if(!TextUtils.isEmpty(charSequence0) && this.e == null) {
            this.e = new AppCompatImageView(this.getContext(), null);
        }
        AppCompatImageView appCompatImageView0 = this.e;
        if(appCompatImageView0 != null) {
            appCompatImageView0.setContentDescription(charSequence0);
        }
    }

    public void setNavigationContentDescription(int v) {
        this.setNavigationContentDescription((v == 0 ? null : this.getContext().getText(v)));
    }

    public void setNavigationContentDescription(CharSequence charSequence0) {
        if(!TextUtils.isEmpty(charSequence0)) {
            this.g();
        }
        AppCompatImageButton appCompatImageButton0 = this.d;
        if(appCompatImageButton0 != null) {
            appCompatImageButton0.setContentDescription(charSequence0);
            TooltipCompat.a(this.d, charSequence0);
        }
    }

    public void setNavigationIcon(int v) {
        this.setNavigationIcon(AppCompatResources.a(this.getContext(), v));
    }

    public void setNavigationIcon(Drawable drawable0) {
        if(drawable0 != null) {
            this.g();
            if(!this.p(this.d)) {
                this.b(this.d, true);
            }
        }
        else if(this.d != null && this.p(this.d)) {
            this.removeView(this.d);
            this.E.remove(this.d);
        }
        AppCompatImageButton appCompatImageButton0 = this.d;
        if(appCompatImageButton0 != null) {
            appCompatImageButton0.setImageDrawable(drawable0);
        }
    }

    public void setNavigationOnClickListener(View.OnClickListener view$OnClickListener0) {
        this.g();
        this.d.setOnClickListener(view$OnClickListener0);
    }

    public void setOnMenuItemClickListener(OnMenuItemClickListener toolbar$OnMenuItemClickListener0) {
        this.I = toolbar$OnMenuItemClickListener0;
    }

    public void setOverflowIcon(Drawable drawable0) {
        this.e();
        this.a.setOverflowIcon(drawable0);
    }

    public void setPopupTheme(int v) {
        if(this.k != v) {
            this.k = v;
            if(v == 0) {
                this.j = this.getContext();
                return;
            }
            this.j = new ContextThemeWrapper(this.getContext(), v);
        }
    }

    public void setSubtitle(int v) {
        this.setSubtitle(this.getContext().getText(v));
    }

    public void setSubtitle(CharSequence charSequence0) {
        if(!TextUtils.isEmpty(charSequence0)) {
            if(this.c == null) {
                Context context0 = this.getContext();
                AppCompatTextView appCompatTextView0 = new AppCompatTextView(context0, null);
                this.c = appCompatTextView0;
                appCompatTextView0.setSingleLine();
                this.c.setEllipsize(TextUtils.TruncateAt.END);
                int v = this.m;
                if(v != 0) {
                    this.c.setTextAppearance(context0, v);
                }
                ColorStateList colorStateList0 = this.A;
                if(colorStateList0 != null) {
                    this.c.setTextColor(colorStateList0);
                }
            }
            if(!this.p(this.c)) {
                this.b(this.c, true);
            }
        }
        else if(this.c != null && this.p(this.c)) {
            this.removeView(this.c);
            this.E.remove(this.c);
        }
        AppCompatTextView appCompatTextView1 = this.c;
        if(appCompatTextView1 != null) {
            appCompatTextView1.setText(charSequence0);
        }
        this.y = charSequence0;
    }

    public void setSubtitleTextColor(int v) {
        this.setSubtitleTextColor(ColorStateList.valueOf(v));
    }

    public void setSubtitleTextColor(ColorStateList colorStateList0) {
        this.A = colorStateList0;
        AppCompatTextView appCompatTextView0 = this.c;
        if(appCompatTextView0 != null) {
            appCompatTextView0.setTextColor(colorStateList0);
        }
    }

    public void setTitle(int v) {
        this.setTitle(this.getContext().getText(v));
    }

    public void setTitle(CharSequence charSequence0) {
        if(!TextUtils.isEmpty(charSequence0)) {
            if(this.b == null) {
                Context context0 = this.getContext();
                AppCompatTextView appCompatTextView0 = new AppCompatTextView(context0, null);
                this.b = appCompatTextView0;
                appCompatTextView0.setSingleLine();
                this.b.setEllipsize(TextUtils.TruncateAt.END);
                int v = this.l;
                if(v != 0) {
                    this.b.setTextAppearance(context0, v);
                }
                ColorStateList colorStateList0 = this.z;
                if(colorStateList0 != null) {
                    this.b.setTextColor(colorStateList0);
                }
            }
            if(!this.p(this.b)) {
                this.b(this.b, true);
            }
        }
        else if(this.b != null && this.p(this.b)) {
            this.removeView(this.b);
            this.E.remove(this.b);
        }
        AppCompatTextView appCompatTextView1 = this.b;
        if(appCompatTextView1 != null) {
            appCompatTextView1.setText(charSequence0);
        }
        this.x = charSequence0;
    }

    public void setTitleMarginBottom(int v) {
        this.s = v;
        this.requestLayout();
    }

    public void setTitleMarginEnd(int v) {
        this.q = v;
        this.requestLayout();
    }

    public void setTitleMarginStart(int v) {
        this.p = v;
        this.requestLayout();
    }

    public void setTitleMarginTop(int v) {
        this.r = v;
        this.requestLayout();
    }

    public void setTitleTextColor(int v) {
        this.setTitleTextColor(ColorStateList.valueOf(v));
    }

    public void setTitleTextColor(ColorStateList colorStateList0) {
        this.z = colorStateList0;
        AppCompatTextView appCompatTextView0 = this.b;
        if(appCompatTextView0 != null) {
            appCompatTextView0.setTextColor(colorStateList0);
        }
    }

    public final int t(View view0, int v, int v1, int v2, int v3, int[] arr_v) {
        ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)view0.getLayoutParams();
        int v4 = viewGroup$MarginLayoutParams0.leftMargin - arr_v[0];
        int v5 = viewGroup$MarginLayoutParams0.rightMargin - arr_v[1];
        int v6 = Math.max(0, v5) + Math.max(0, v4);
        arr_v[0] = Math.max(0, -v4);
        arr_v[1] = Math.max(0, -v5);
        int v7 = this.getPaddingLeft();
        int v8 = ViewGroup.getChildMeasureSpec(v, this.getPaddingRight() + v7 + v6 + v1, viewGroup$MarginLayoutParams0.width);
        int v9 = this.getPaddingTop();
        view0.measure(v8, ViewGroup.getChildMeasureSpec(v2, this.getPaddingBottom() + v9 + viewGroup$MarginLayoutParams0.topMargin + viewGroup$MarginLayoutParams0.bottomMargin + v3, viewGroup$MarginLayoutParams0.height));
        return view0.getMeasuredWidth() + v6;
    }

    public final void u(View view0, int v, int v1, int v2, int v3) {
        ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)view0.getLayoutParams();
        int v4 = this.getPaddingLeft();
        int v5 = ViewGroup.getChildMeasureSpec(v, this.getPaddingRight() + v4 + viewGroup$MarginLayoutParams0.leftMargin + viewGroup$MarginLayoutParams0.rightMargin + v1, viewGroup$MarginLayoutParams0.width);
        int v6 = this.getPaddingTop();
        int v7 = ViewGroup.getChildMeasureSpec(v2, this.getPaddingBottom() + v6 + viewGroup$MarginLayoutParams0.topMargin + viewGroup$MarginLayoutParams0.bottomMargin, viewGroup$MarginLayoutParams0.height);
        int v8 = View.MeasureSpec.getMode(v7);
        if(v8 != 0x40000000 && v3 >= 0) {
            if(v8 != 0) {
                v3 = Math.min(View.MeasureSpec.getSize(v7), v3);
            }
            v7 = View.MeasureSpec.makeMeasureSpec(v3, 0x40000000);
        }
        view0.measure(v5, v7);
    }

    public final boolean v(View view0) {
        return view0 != null && view0.getParent() == this && view0.getVisibility() != 8;
    }

    // 检测为 Lambda 实现
    public final boolean w() [...]

    public final void x() {
        if(Build.VERSION.SDK_INT >= 33) {
            OnBackInvokedDispatcher onBackInvokedDispatcher0 = Api33Impl.a(this);
            boolean z = this.m() && onBackInvokedDispatcher0 != null && this.isAttachedToWindow() && this.S;
            if(z && this.R == null) {
                if(this.Q == null) {
                    this.Q = Api33Impl.b(new androidx.appcompat.widget.a(this, 0));
                }
                Api33Impl.c(onBackInvokedDispatcher0, this.Q);
                this.R = onBackInvokedDispatcher0;
                return;
            }
            if(!z) {
                OnBackInvokedDispatcher onBackInvokedDispatcher1 = this.R;
                if(onBackInvokedDispatcher1 != null) {
                    Api33Impl.d(onBackInvokedDispatcher1, this.Q);
                    this.R = null;
                }
            }
        }
    }

    class androidx.appcompat.widget.Toolbar.2 implements Runnable {
        public final Toolbar a;

        public androidx.appcompat.widget.Toolbar.2() {
            this.a = toolbar0;
        }

        @Override
        public final void run() {
            this.a.w();
        }
    }

}


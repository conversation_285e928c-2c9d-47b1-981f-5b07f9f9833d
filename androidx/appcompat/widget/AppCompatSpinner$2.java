package androidx.appcompat.widget;

import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.view.ViewTreeObserver;

class AppCompatSpinner.2 implements ViewTreeObserver.OnGlobalLayoutListener {
    public final AppCompatSpinner a;

    public AppCompatSpinner.2(AppCompatSpinner appCompatSpinner0) {
        this.a = appCompatSpinner0;
    }

    @Override  // android.view.ViewTreeObserver$OnGlobalLayoutListener
    public final void onGlobalLayout() {
        AppCompatSpinner appCompatSpinner0 = this.a;
        if(!appCompatSpinner0.getInternalPopup().a()) {
            int v = appCompatSpinner0.getTextDirection();
            int v1 = appCompatSpinner0.getTextAlignment();
            appCompatSpinner0.f.m(v, v1);
        }
        ViewTreeObserver viewTreeObserver0 = appCompatSpinner0.getViewTreeObserver();
        if(viewTreeObserver0 != null) {
            viewTreeObserver0.removeOnGlobalLayoutListener(this);
        }
    }
}


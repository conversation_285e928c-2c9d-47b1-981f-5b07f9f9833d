package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources.NotFoundException;
import android.content.res.TypedArray;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.ActionMode.Callback;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.CheckedTextView;
import androidx.appcompat.R.styleable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.view.ViewCompat;
import androidx.core.widget.TextViewCompat;
import androidx.core.widget.TintableCompoundDrawablesView;

public class AppCompatCheckedTextView extends CheckedTextView implements TintableCompoundDrawablesView {
    public final AppCompatCheckedTextViewHelper a;
    public final AppCompatBackgroundHelper b;
    public final AppCompatTextHelper c;
    public AppCompatEmojiTextHelper d;

    public AppCompatCheckedTextView(Context context0, AttributeSet attributeSet0) {
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, 0x7F040110);  // attr:checkedTextViewStyle
        ThemeUtils.a(this, this.getContext());
        AppCompatTextHelper appCompatTextHelper0 = new AppCompatTextHelper(this);
        this.c = appCompatTextHelper0;
        appCompatTextHelper0.f(attributeSet0, 0x7F040110);  // attr:checkedTextViewStyle
        appCompatTextHelper0.b();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = new AppCompatBackgroundHelper(this);
        this.b = appCompatBackgroundHelper0;
        appCompatBackgroundHelper0.d(attributeSet0, 0x7F040110);  // attr:checkedTextViewStyle
        this.a = new AppCompatCheckedTextViewHelper(this);
        TintTypedArray tintTypedArray0 = TintTypedArray.e(this.getContext(), attributeSet0, R.styleable.l, 0x7F040110, 0);  // attr:checkedTextViewStyle
        TypedArray typedArray0 = tintTypedArray0.b;
        ViewCompat.B(this, this.getContext(), R.styleable.l, attributeSet0, tintTypedArray0.b, 0x7F040110);  // attr:checkedTextViewStyle
        try {
            if(typedArray0.hasValue(1)) {
                int v1 = typedArray0.getResourceId(1, 0);
                if(v1 != 0) {
                    try {
                        this.setCheckMarkDrawable(AppCompatResources.a(this.getContext(), v1));
                        goto label_24;
                    }
                    catch(Resources.NotFoundException unused_ex) {
                    }
                }
                goto label_20;
            }
            else {
            label_20:
                if(typedArray0.hasValue(0)) {
                    int v2 = typedArray0.getResourceId(0, 0);
                    if(v2 != 0) {
                        this.setCheckMarkDrawable(AppCompatResources.a(this.getContext(), v2));
                    }
                }
            }
        label_24:
            if(typedArray0.hasValue(2)) {
                this.setCheckMarkTintList(tintTypedArray0.a(2));
            }
            if(typedArray0.hasValue(3)) {
                this.setCheckMarkTintMode(DrawableUtils.c(typedArray0.getInt(3, -1), null));
            }
        }
        finally {
            tintTypedArray0.f();
        }
        this.getEmojiTextViewHelper().a(attributeSet0, 0x7F040110);  // attr:checkedTextViewStyle
    }

    @Override  // android.widget.CheckedTextView
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
        AppCompatCheckedTextViewHelper appCompatCheckedTextViewHelper0 = this.a;
        if(appCompatCheckedTextViewHelper0 != null) {
            appCompatCheckedTextViewHelper0.a();
        }
    }

    @Override  // android.widget.TextView
    public ActionMode.Callback getCustomSelectionActionModeCallback() {
        return TextViewCompat.j(super.getCustomSelectionActionModeCallback());
    }

    private AppCompatEmojiTextHelper getEmojiTextViewHelper() {
        if(this.d == null) {
            this.d = new AppCompatEmojiTextHelper(this);
        }
        return this.d;
    }

    public ColorStateList getSupportBackgroundTintList() {
        return this.b == null ? null : this.b.b();
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.b == null ? null : this.b.c();
    }

    public ColorStateList getSupportCheckMarkTintList() {
        return this.a == null ? null : this.a.b;
    }

    public PorterDuff.Mode getSupportCheckMarkTintMode() {
        return this.a == null ? null : this.a.c;
    }

    public ColorStateList getSupportCompoundDrawablesTintList() {
        return this.c.d();
    }

    public PorterDuff.Mode getSupportCompoundDrawablesTintMode() {
        return this.c.e();
    }

    @Override  // android.widget.TextView
    public final InputConnection onCreateInputConnection(EditorInfo editorInfo0) {
        InputConnection inputConnection0 = super.onCreateInputConnection(editorInfo0);
        AppCompatHintHelper.a(this, editorInfo0, inputConnection0);
        return inputConnection0;
    }

    @Override  // android.widget.TextView
    public void setAllCaps(boolean z) {
        super.setAllCaps(z);
        this.getEmojiTextViewHelper().b(z);
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.CheckedTextView
    public void setCheckMarkDrawable(int v) {
        this.setCheckMarkDrawable(AppCompatResources.a(this.getContext(), v));
    }

    @Override  // android.widget.CheckedTextView
    public void setCheckMarkDrawable(Drawable drawable0) {
        super.setCheckMarkDrawable(drawable0);
        AppCompatCheckedTextViewHelper appCompatCheckedTextViewHelper0 = this.a;
        if(appCompatCheckedTextViewHelper0 != null) {
            if(appCompatCheckedTextViewHelper0.f) {
                appCompatCheckedTextViewHelper0.f = false;
                return;
            }
            appCompatCheckedTextViewHelper0.f = true;
            appCompatCheckedTextViewHelper0.a();
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawables(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawables(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawablesRelative(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawablesRelative(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback actionMode$Callback0) {
        super.setCustomSelectionActionModeCallback(TextViewCompat.k(this, actionMode$Callback0));
    }

    public void setEmojiCompatEnabled(boolean z) {
        this.getEmojiTextViewHelper().c(z);
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.b;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }

    public void setSupportCheckMarkTintList(ColorStateList colorStateList0) {
        AppCompatCheckedTextViewHelper appCompatCheckedTextViewHelper0 = this.a;
        if(appCompatCheckedTextViewHelper0 != null) {
            appCompatCheckedTextViewHelper0.b = colorStateList0;
            appCompatCheckedTextViewHelper0.d = true;
            appCompatCheckedTextViewHelper0.a();
        }
    }

    public void setSupportCheckMarkTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatCheckedTextViewHelper appCompatCheckedTextViewHelper0 = this.a;
        if(appCompatCheckedTextViewHelper0 != null) {
            appCompatCheckedTextViewHelper0.c = porterDuff$Mode0;
            appCompatCheckedTextViewHelper0.e = true;
            appCompatCheckedTextViewHelper0.a();
        }
    }

    @Override  // androidx.core.widget.TintableCompoundDrawablesView
    public void setSupportCompoundDrawablesTintList(ColorStateList colorStateList0) {
        this.c.k(colorStateList0);
        this.c.b();
    }

    @Override  // androidx.core.widget.TintableCompoundDrawablesView
    public void setSupportCompoundDrawablesTintMode(PorterDuff.Mode porterDuff$Mode0) {
        this.c.l(porterDuff$Mode0);
        this.c.b();
    }

    @Override  // android.widget.TextView
    public final void setTextAppearance(Context context0, int v) {
        super.setTextAppearance(context0, v);
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.g(v, context0);
        }
    }
}


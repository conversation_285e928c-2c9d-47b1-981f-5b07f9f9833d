package androidx.appcompat.widget;

import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.SeekBar;
import androidx.appcompat.R.styleable;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.core.view.ViewCompat;

class AppCompatSeekBarHelper extends AppCompatProgressBarHelper {
    public final SeekBar d;
    public Drawable e;
    public ColorStateList f;
    public PorterDuff.Mode g;
    public boolean h;
    public boolean i;

    public AppCompatSeekBarHelper(SeekBar seekBar0) {
        super(seekBar0);
        this.f = null;
        this.g = null;
        this.h = false;
        this.i = false;
        this.d = seekBar0;
    }

    @Override  // androidx.appcompat.widget.AppCompatProgressBarHelper
    public final void a(AttributeSet attributeSet0, int v) {
        super.a(attributeSet0, 0x7F0404B2);  // attr:seekBarStyle
        SeekBar seekBar0 = this.d;
        TintTypedArray tintTypedArray0 = TintTypedArray.e(seekBar0.getContext(), attributeSet0, R.styleable.g, 0x7F0404B2, 0);  // attr:seekBarStyle
        ViewCompat.B(seekBar0, seekBar0.getContext(), R.styleable.g, attributeSet0, tintTypedArray0.b, 0x7F0404B2);  // attr:seekBarStyle
        Drawable drawable0 = tintTypedArray0.c(0);
        if(drawable0 != null) {
            seekBar0.setThumb(drawable0);
        }
        Drawable drawable1 = tintTypedArray0.b(1);
        Drawable drawable2 = this.e;
        if(drawable2 != null) {
            drawable2.setCallback(null);
        }
        this.e = drawable1;
        if(drawable1 != null) {
            drawable1.setCallback(seekBar0);
            DrawableCompat.i(drawable1, seekBar0.getLayoutDirection());
            if(drawable1.isStateful()) {
                drawable1.setState(seekBar0.getDrawableState());
            }
            this.c();
        }
        seekBar0.invalidate();
        TypedArray typedArray0 = tintTypedArray0.b;
        if(typedArray0.hasValue(3)) {
            this.g = DrawableUtils.c(typedArray0.getInt(3, -1), this.g);
            this.i = true;
        }
        if(typedArray0.hasValue(2)) {
            this.f = tintTypedArray0.a(2);
            this.h = true;
        }
        tintTypedArray0.f();
        this.c();
    }

    public final void c() {
        Drawable drawable0 = this.e;
        if(drawable0 != null && (this.h || this.i)) {
            Drawable drawable1 = DrawableCompat.n(drawable0.mutate());
            this.e = drawable1;
            if(this.h) {
                DrawableCompat.k(drawable1, this.f);
            }
            if(this.i) {
                DrawableCompat.l(this.e, this.g);
            }
            if(this.e.isStateful()) {
                this.e.setState(this.d.getDrawableState());
            }
        }
    }

    public final void d(Canvas canvas0) {
        if(this.e != null) {
            SeekBar seekBar0 = this.d;
            int v = seekBar0.getMax();
            int v1 = 1;
            if(v > 1) {
                int v2 = this.e.getIntrinsicWidth();
                int v3 = this.e.getIntrinsicHeight();
                int v4 = v2 < 0 ? 1 : v2 / 2;
                if(v3 >= 0) {
                    v1 = v3 / 2;
                }
                this.e.setBounds(-v4, -v1, v4, v1);
                int v5 = seekBar0.getWidth();
                int v6 = seekBar0.getPaddingLeft();
                int v7 = seekBar0.getPaddingRight();
                int v8 = canvas0.save();
                canvas0.translate(((float)seekBar0.getPaddingLeft()), ((float)(seekBar0.getHeight() / 2)));
                for(int v9 = 0; v9 <= v; ++v9) {
                    this.e.draw(canvas0);
                    canvas0.translate(((float)(v5 - v6 - v7)) / ((float)v), 0.0f);
                }
                canvas0.restoreToCount(v8);
            }
        }
    }
}


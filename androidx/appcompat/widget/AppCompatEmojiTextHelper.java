package androidx.appcompat.widget;

import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.TextView;
import androidx.appcompat.R.styleable;
import androidx.emoji2.viewsintegration.EmojiTextViewHelper;

class AppCompatEmojiTextHelper {
    public final TextView a;
    public final EmojiTextViewHelper b;

    public AppCompatEmojiTextHelper(TextView textView0) {
        this.a = textView0;
        this.b = new EmojiTextViewHelper(textView0);
    }

    public final void a(AttributeSet attributeSet0, int v) {
        boolean z = true;
        TypedArray typedArray0 = this.a.getContext().obtainStyledAttributes(attributeSet0, R.styleable.i, v, 0);
        try {
            if(typedArray0.hasValue(14)) {
                z = typedArray0.getBoolean(14, true);
            }
        }
        finally {
            typedArray0.recycle();
        }
        this.c(z);
    }

    public final void b(boolean z) {
        this.b.c(z);
    }

    public final void c(boolean z) {
        this.b.d(z);
    }
}


package androidx.appcompat.widget;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.text.Editable;
import android.text.method.KeyListener;
import android.text.method.NumberKeyListener;
import android.util.AttributeSet;
import android.view.ActionMode.Callback;
import android.view.DragEvent;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputMethodManager;
import android.view.textclassifier.TextClassifier;
import android.widget.EditText;
import androidx.core.view.ContentInfoCompat.Builder;
import androidx.core.view.ContentInfoCompat;
import androidx.core.view.OnReceiveContentViewBehavior;
import androidx.core.view.ViewCompat;
import androidx.core.view.inputmethod.EditorInfoCompat;
import androidx.core.view.inputmethod.InputConnectionCompat;
import androidx.core.widget.TextViewCompat;
import androidx.core.widget.TextViewOnReceiveContentListener;
import androidx.core.widget.TintableCompoundDrawablesView;

public class AppCompatEditText extends EditText implements OnReceiveContentViewBehavior, TintableCompoundDrawablesView {
    class SuperCaller {
        public final AppCompatEditText a;

    }

    public final AppCompatBackgroundHelper a;
    public final AppCompatTextHelper b;
    public final AppCompatTextClassifierHelper c;
    public final TextViewOnReceiveContentListener d;
    public final AppCompatEmojiEditTextHelper e;
    public SuperCaller f;

    public AppCompatEditText(Context context0, AttributeSet attributeSet0) {
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, 0x7F040213);  // attr:editTextStyle
        ThemeUtils.a(this, this.getContext());
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = new AppCompatBackgroundHelper(this);
        this.a = appCompatBackgroundHelper0;
        appCompatBackgroundHelper0.d(attributeSet0, 0x7F040213);  // attr:editTextStyle
        AppCompatTextHelper appCompatTextHelper0 = new AppCompatTextHelper(this);
        this.b = appCompatTextHelper0;
        appCompatTextHelper0.f(attributeSet0, 0x7F040213);  // attr:editTextStyle
        appCompatTextHelper0.b();
        AppCompatTextClassifierHelper appCompatTextClassifierHelper0 = new AppCompatTextClassifierHelper();  // 初始化器: Ljava/lang/Object;-><init>()V
        appCompatTextClassifierHelper0.a = this;
        this.c = appCompatTextClassifierHelper0;
        this.d = new TextViewOnReceiveContentListener();  // 初始化器: Ljava/lang/Object;-><init>()V
        AppCompatEmojiEditTextHelper appCompatEmojiEditTextHelper0 = new AppCompatEmojiEditTextHelper(this);
        this.e = appCompatEmojiEditTextHelper0;
        appCompatEmojiEditTextHelper0.b(attributeSet0, 0x7F040213);  // attr:editTextStyle
        KeyListener keyListener0 = this.getKeyListener();
        if(!(keyListener0 instanceof NumberKeyListener) != 0) {
            boolean z = super.isFocusable();
            boolean z1 = super.isClickable();
            boolean z2 = super.isLongClickable();
            int v = super.getInputType();
            KeyListener keyListener1 = appCompatEmojiEditTextHelper0.a(keyListener0);
            if(keyListener1 != keyListener0) {
                super.setKeyListener(keyListener1);
                super.setRawInputType(v);
                super.setFocusable(z);
                super.setClickable(z1);
                super.setLongClickable(z2);
            }
        }
    }

    @Override  // androidx.core.view.OnReceiveContentViewBehavior
    public final ContentInfoCompat a(ContentInfoCompat contentInfoCompat0) {
        return this.d.a(this, contentInfoCompat0);
    }

    @Override  // android.widget.TextView
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public ActionMode.Callback getCustomSelectionActionModeCallback() {
        return TextViewCompat.j(super.getCustomSelectionActionModeCallback());
    }

    private SuperCaller getSuperCaller() {
        if(this.f == null) {
            this.f = new SuperCaller(this);
        }
        return this.f;
    }

    public ColorStateList getSupportBackgroundTintList() {
        return this.a == null ? null : this.a.b();
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.a == null ? null : this.a.c();
    }

    public ColorStateList getSupportCompoundDrawablesTintList() {
        return this.b.d();
    }

    public PorterDuff.Mode getSupportCompoundDrawablesTintMode() {
        return this.b.e();
    }

    @Override  // android.widget.EditText
    public Editable getText() {
        return Build.VERSION.SDK_INT < 28 ? super.getEditableText() : super.getText();
    }

    @Override  // android.widget.EditText
    public CharSequence getText() {
        return this.getText();
    }

    @Override  // android.widget.TextView
    public TextClassifier getTextClassifier() {
        if(Build.VERSION.SDK_INT < 28) {
            AppCompatTextClassifierHelper appCompatTextClassifierHelper0 = this.c;
            if(appCompatTextClassifierHelper0 != null) {
                return appCompatTextClassifierHelper0.b == null ? Api26Impl.a(appCompatTextClassifierHelper0.a) : appCompatTextClassifierHelper0.b;
            }
        }
        return this.getSuperCaller().a.super.getTextClassifier();
    }

    @Override  // android.widget.TextView
    public InputConnection onCreateInputConnection(EditorInfo editorInfo0) {
        InputConnection inputConnection0 = super.onCreateInputConnection(editorInfo0);
        this.b.getClass();
        int v = Build.VERSION.SDK_INT;
        if(v < 30 && inputConnection0 != null) {
            EditorInfoCompat.a(editorInfo0, this.getText());
        }
        AppCompatHintHelper.a(this, editorInfo0, inputConnection0);
        if(inputConnection0 != null && v <= 30) {
            String[] arr_s = ViewCompat.n(this);
            if(arr_s != null) {
                if(v >= 25) {
                    editorInfo0.contentMimeTypes = arr_s;
                }
                else {
                    if(editorInfo0.extras == null) {
                        editorInfo0.extras = new Bundle();
                    }
                    editorInfo0.extras.putStringArray("androidx.core.view.inputmethod.EditorInfoCompat.CONTENT_MIME_TYPES", arr_s);
                    editorInfo0.extras.putStringArray("android.support.v13.view.inputmethod.EditorInfoCompat.CONTENT_MIME_TYPES", arr_s);
                }
                inputConnection0 = InputConnectionCompat.b(this, editorInfo0, inputConnection0);
            }
        }
        return this.e.b.b(inputConnection0, editorInfo0);
    }

    @Override  // android.view.View
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if(Build.VERSION.SDK_INT >= 30 && Build.VERSION.SDK_INT < 33) {
            ((InputMethodManager)this.getContext().getSystemService("input_method")).isActive(this);
        }
    }

    @Override  // android.widget.TextView
    public final boolean onDragEvent(DragEvent dragEvent0) {
        if(Build.VERSION.SDK_INT < 0x1F && Build.VERSION.SDK_INT >= 24 && dragEvent0.getLocalState() == null && ViewCompat.n(this) != null) {
            for(Context context0 = this.getContext(); context0 instanceof ContextWrapper; context0 = ((ContextWrapper)context0).getBaseContext()) {
                if(context0 instanceof Activity) {
                    Activity activity0 = (Activity)context0;
                    return activity0 == null || dragEvent0.getAction() == 1 || dragEvent0.getAction() != 3 || !AppCompatReceiveContentHelper.OnDropApi24Impl.a(dragEvent0, this, activity0) ? super.onDragEvent(dragEvent0) : true;
                }
            }
            return super.onDragEvent(dragEvent0);
        }
        return super.onDragEvent(dragEvent0);
    }

    @Override  // android.widget.EditText
    public final boolean onTextContextMenuItem(int v) {
        if(Build.VERSION.SDK_INT < 0x1F && ViewCompat.n(this) != null && (v == 0x1020022 || v == 0x1020031)) {
            ClipboardManager clipboardManager0 = (ClipboardManager)this.getContext().getSystemService("clipboard");
            ClipData clipData0 = clipboardManager0 == null ? null : clipboardManager0.getPrimaryClip();
            if(clipData0 != null && clipData0.getItemCount() > 0) {
                Builder contentInfoCompat$Builder0 = new Builder(clipData0, 1);
                contentInfoCompat$Builder0.c((v == 0x1020022 ? 0 : 1));
                ViewCompat.x(this, contentInfoCompat$Builder0.a());
            }
            return true;
        }
        return super.onTextContextMenuItem(v);
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawables(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawables(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public final void setCompoundDrawablesRelative(Drawable drawable0, Drawable drawable1, Drawable drawable2, Drawable drawable3) {
        super.setCompoundDrawablesRelative(drawable0, drawable1, drawable2, drawable3);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback actionMode$Callback0) {
        super.setCustomSelectionActionModeCallback(TextViewCompat.k(this, actionMode$Callback0));
    }

    public void setEmojiCompatEnabled(boolean z) {
        this.e.b.c(z);
    }

    @Override  // android.widget.TextView
    public void setKeyListener(KeyListener keyListener0) {
        super.setKeyListener(this.e.a(keyListener0));
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }

    @Override  // androidx.core.widget.TintableCompoundDrawablesView
    public void setSupportCompoundDrawablesTintList(ColorStateList colorStateList0) {
        this.b.k(colorStateList0);
        this.b.b();
    }

    @Override  // androidx.core.widget.TintableCompoundDrawablesView
    public void setSupportCompoundDrawablesTintMode(PorterDuff.Mode porterDuff$Mode0) {
        this.b.l(porterDuff$Mode0);
        this.b.b();
    }

    @Override  // android.widget.TextView
    public final void setTextAppearance(Context context0, int v) {
        super.setTextAppearance(context0, v);
        AppCompatTextHelper appCompatTextHelper0 = this.b;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.g(v, context0);
        }
    }

    @Override  // android.widget.TextView
    public void setTextClassifier(TextClassifier textClassifier0) {
        if(Build.VERSION.SDK_INT < 28) {
            AppCompatTextClassifierHelper appCompatTextClassifierHelper0 = this.c;
            if(appCompatTextClassifierHelper0 != null) {
                appCompatTextClassifierHelper0.b = textClassifier0;
                return;
            }
        }
        this.getSuperCaller().a.super.setTextClassifier(textClassifier0);
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Rect;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager.LayoutParams;
import android.widget.TextView;

class TooltipPopup {
    public final Context a;
    public final View b;
    public final TextView c;
    public final WindowManager.LayoutParams d;
    public final Rect e;
    public final int[] f;
    public final int[] g;

    public TooltipPopup(Context context0) {
        WindowManager.LayoutParams windowManager$LayoutParams0 = new WindowManager.LayoutParams();
        this.d = windowManager$LayoutParams0;
        this.e = new Rect();
        this.f = new int[2];
        this.g = new int[2];
        this.a = context0;
        View view0 = LayoutInflater.from(context0).inflate(0x7F0D001B, null);  // layout:abc_tooltip
        this.b = view0;
        this.c = (TextView)view0.findViewById(0x7F0A02F0);  // id:message
        windowManager$LayoutParams0.setTitle(this.getClass().getSimpleName());
        windowManager$LayoutParams0.packageName = "com.pdf.editor.viewer.pdfreader.pdfviewer";
        windowManager$LayoutParams0.type = 1002;
        windowManager$LayoutParams0.width = -2;
        windowManager$LayoutParams0.height = -2;
        windowManager$LayoutParams0.format = -3;
        windowManager$LayoutParams0.windowAnimations = 0x7F150007;  // style:Animation.AppCompat.Tooltip
        windowManager$LayoutParams0.flags = 24;
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources.NotFoundException;
import android.content.res.TypedArray;
import android.graphics.PorterDuff.Mode;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.LocaleList;
import android.text.method.PasswordTransformationMethod;
import android.text.method.TransformationMethod;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.widget.TextView;
import androidx.appcompat.R.styleable;
import androidx.core.view.ViewCompat;
import androidx.core.widget.TextViewCompat;
import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.Locale;

class AppCompatTextHelper {
    static abstract class Api21Impl {
        public static Locale a(String s) {
            return Locale.forLanguageTag(s);
        }
    }

    static abstract class Api24Impl {
        public static LocaleList a(String s) {
            return LocaleList.forLanguageTags(s);
        }

        public static void b(TextView textView0, LocaleList localeList0) {
            textView0.setTextLocales(localeList0);
        }
    }

    static abstract class Api26Impl {
        public static int a(TextView textView0) {
            return textView0.getAutoSizeStepGranularity();
        }

        public static void b(TextView textView0, int v, int v1, int v2, int v3) {
            textView0.setAutoSizeTextTypeUniformWithConfiguration(v, v1, v2, v3);
        }

        public static void c(TextView textView0, int[] arr_v, int v) {
            textView0.setAutoSizeTextTypeUniformWithPresetSizes(arr_v, v);
        }

        public static boolean d(TextView textView0, String s) {
            return textView0.setFontVariationSettings(s);
        }
    }

    static abstract class Api28Impl {
        public static Typeface a(Typeface typeface0, int v, boolean z) {
            return Typeface.create(typeface0, v, z);
        }
    }

    public final TextView a;
    public TintInfo b;
    public TintInfo c;
    public TintInfo d;
    public TintInfo e;
    public TintInfo f;
    public TintInfo g;
    public TintInfo h;
    public final AppCompatTextViewAutoSizeHelper i;
    public int j;
    public int k;
    public Typeface l;
    public boolean m;

    public AppCompatTextHelper(TextView textView0) {
        this.j = 0;
        this.k = -1;
        this.a = textView0;
        this.i = new AppCompatTextViewAutoSizeHelper(textView0);
    }

    public final void a(Drawable drawable0, TintInfo tintInfo0) {
        if(drawable0 != null && tintInfo0 != null) {
            AppCompatDrawableManager.e(drawable0, tintInfo0, this.a.getDrawableState());
        }
    }

    public final void b() {
        TextView textView0 = this.a;
        if(this.b != null || this.c != null || this.d != null || this.e != null) {
            Drawable[] arr_drawable = textView0.getCompoundDrawables();
            this.a(arr_drawable[0], this.b);
            this.a(arr_drawable[1], this.c);
            this.a(arr_drawable[2], this.d);
            this.a(arr_drawable[3], this.e);
        }
        if(this.f != null || this.g != null) {
            Drawable[] arr_drawable1 = textView0.getCompoundDrawablesRelative();
            this.a(arr_drawable1[0], this.f);
            this.a(arr_drawable1[2], this.g);
        }
    }

    public static TintInfo c(Context context0, AppCompatDrawableManager appCompatDrawableManager0, int v) {
        ColorStateList colorStateList0;
        synchronized(appCompatDrawableManager0) {
            colorStateList0 = appCompatDrawableManager0.a.i(v, context0);
        }
        if(colorStateList0 != null) {
            TintInfo tintInfo0 = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
            tintInfo0.d = true;
            tintInfo0.a = colorStateList0;
            return tintInfo0;
        }
        return null;
    }

    public final ColorStateList d() {
        return this.h == null ? null : this.h.a;
    }

    public final PorterDuff.Mode e() {
        return this.h == null ? null : this.h.b;
    }

    public final void f(AttributeSet attributeSet0, int v) {
        int v16;
        float f3;
        boolean z2;
        String s1;
        String s;
        ColorStateList colorStateList2;
        ColorStateList colorStateList1;
        ColorStateList colorStateList0;
        boolean z1;
        boolean z;
        TextView textView0 = this.a;
        Context context0 = textView0.getContext();
        AppCompatDrawableManager appCompatDrawableManager0 = AppCompatDrawableManager.a();
        TintTypedArray tintTypedArray0 = TintTypedArray.e(context0, attributeSet0, R.styleable.h, v, 0);
        ViewCompat.B(textView0, textView0.getContext(), R.styleable.h, attributeSet0, tintTypedArray0.b, v);
        TypedArray typedArray0 = tintTypedArray0.b;
        int v1 = typedArray0.getResourceId(0, -1);
        if(typedArray0.hasValue(3)) {
            this.b = AppCompatTextHelper.c(context0, appCompatDrawableManager0, typedArray0.getResourceId(3, 0));
        }
        if(typedArray0.hasValue(1)) {
            this.c = AppCompatTextHelper.c(context0, appCompatDrawableManager0, typedArray0.getResourceId(1, 0));
        }
        if(typedArray0.hasValue(4)) {
            this.d = AppCompatTextHelper.c(context0, appCompatDrawableManager0, typedArray0.getResourceId(4, 0));
        }
        if(typedArray0.hasValue(2)) {
            this.e = AppCompatTextHelper.c(context0, appCompatDrawableManager0, typedArray0.getResourceId(2, 0));
        }
        if(typedArray0.hasValue(5)) {
            this.f = AppCompatTextHelper.c(context0, appCompatDrawableManager0, typedArray0.getResourceId(5, 0));
        }
        if(typedArray0.hasValue(6)) {
            this.g = AppCompatTextHelper.c(context0, appCompatDrawableManager0, typedArray0.getResourceId(6, 0));
        }
        tintTypedArray0.f();
        TransformationMethod transformationMethod0 = textView0.getTransformationMethod();
        int[] arr_v = R.styleable.w;
        if(v1 == -1) {
            s1 = null;
            z = false;
            z1 = false;
            colorStateList0 = null;
            colorStateList1 = null;
            colorStateList2 = null;
            s = null;
        }
        else {
            TypedArray typedArray1 = context0.obtainStyledAttributes(v1, arr_v);
            TintTypedArray tintTypedArray1 = new TintTypedArray(context0, typedArray1);
            if(transformationMethod0 instanceof PasswordTransformationMethod || !typedArray1.hasValue(14)) {
                z = false;
                z1 = false;
            }
            else {
                z = typedArray1.getBoolean(14, false);
                z1 = true;
            }
            this.m(context0, tintTypedArray1);
            int v2 = Build.VERSION.SDK_INT;
            if(v2 < 23) {
                colorStateList0 = typedArray1.hasValue(3) ? tintTypedArray1.a(3) : null;
                colorStateList1 = typedArray1.hasValue(4) ? tintTypedArray1.a(4) : null;
                colorStateList2 = typedArray1.hasValue(5) ? tintTypedArray1.a(5) : null;
            }
            else {
                colorStateList0 = null;
                colorStateList1 = null;
                colorStateList2 = null;
            }
            s = typedArray1.hasValue(15) ? typedArray1.getString(15) : null;
            s1 = v2 < 26 || !typedArray1.hasValue(13) ? null : typedArray1.getString(13);
            tintTypedArray1.f();
        }
        TypedArray typedArray2 = context0.obtainStyledAttributes(attributeSet0, arr_v, v, 0);
        TintTypedArray tintTypedArray2 = new TintTypedArray(context0, typedArray2);
        if(transformationMethod0 instanceof PasswordTransformationMethod || !typedArray2.hasValue(14)) {
            z2 = z;
        }
        else {
            z2 = typedArray2.getBoolean(14, false);
            z1 = true;
        }
        int v3 = Build.VERSION.SDK_INT;
        if(v3 < 23) {
            if(typedArray2.hasValue(3)) {
                colorStateList0 = tintTypedArray2.a(3);
            }
            if(typedArray2.hasValue(4)) {
                colorStateList1 = tintTypedArray2.a(4);
            }
            if(typedArray2.hasValue(5)) {
                colorStateList2 = tintTypedArray2.a(5);
            }
        }
        if(typedArray2.hasValue(15)) {
            s = typedArray2.getString(15);
        }
        String s2 = v3 < 26 || !typedArray2.hasValue(13) ? s1 : typedArray2.getString(13);
        if(v3 >= 28 && typedArray2.hasValue(0) && typedArray2.getDimensionPixelSize(0, -1) == 0) {
            textView0.setTextSize(0, 0.0f);
        }
        this.m(context0, tintTypedArray2);
        tintTypedArray2.f();
        if(colorStateList0 != null) {
            textView0.setTextColor(colorStateList0);
        }
        if(colorStateList1 != null) {
            textView0.setHintTextColor(colorStateList1);
        }
        if(colorStateList2 != null) {
            textView0.setLinkTextColor(colorStateList2);
        }
        if(!(transformationMethod0 instanceof PasswordTransformationMethod) && z1) {
            textView0.setAllCaps(z2);
        }
        Typeface typeface0 = this.l;
        if(typeface0 != null) {
            if(this.k == -1) {
                textView0.setTypeface(typeface0, this.j);
            }
            else {
                textView0.setTypeface(typeface0);
            }
        }
        if(s2 != null) {
            Api26Impl.d(textView0, s2);
        }
        if(s != null) {
            if(v3 >= 24) {
                Api24Impl.b(textView0, Api24Impl.a(s));
            }
            else {
                textView0.setTextLocale(Api21Impl.a(s.split(",")[0]));
            }
        }
        int[] arr_v1 = R.styleable.i;
        AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = this.i;
        Context context1 = appCompatTextViewAutoSizeHelper0.j;
        TypedArray typedArray3 = context1.obtainStyledAttributes(attributeSet0, arr_v1, v, 0);
        Context context2 = appCompatTextViewAutoSizeHelper0.i.getContext();
        ViewCompat.B(appCompatTextViewAutoSizeHelper0.i, context2, arr_v1, attributeSet0, typedArray3, v);
        if(typedArray3.hasValue(5)) {
            appCompatTextViewAutoSizeHelper0.a = typedArray3.getInt(5, 0);
        }
        float f = typedArray3.hasValue(4) ? typedArray3.getDimension(4, -1.0f) : -1.0f;
        float f1 = typedArray3.hasValue(2) ? typedArray3.getDimension(2, -1.0f) : -1.0f;
        float f2 = typedArray3.hasValue(1) ? typedArray3.getDimension(1, -1.0f) : -1.0f;
        if(typedArray3.hasValue(3)) {
            int v4 = typedArray3.getResourceId(3, 0);
            if(v4 > 0) {
                TypedArray typedArray4 = typedArray3.getResources().obtainTypedArray(v4);
                int v5 = typedArray4.length();
                int[] arr_v2 = new int[v5];
                if(v5 > 0) {
                    for(int v6 = 0; v6 < v5; ++v6) {
                        arr_v2[v6] = typedArray4.getDimensionPixelSize(v6, -1);
                    }
                    appCompatTextViewAutoSizeHelper0.f = AppCompatTextViewAutoSizeHelper.b(arr_v2);
                    appCompatTextViewAutoSizeHelper0.i();
                }
                typedArray4.recycle();
            }
        }
        typedArray3.recycle();
        if(!appCompatTextViewAutoSizeHelper0.j()) {
            appCompatTextViewAutoSizeHelper0.a = 0;
        }
        else if(appCompatTextViewAutoSizeHelper0.a == 1) {
            if(!appCompatTextViewAutoSizeHelper0.g) {
                DisplayMetrics displayMetrics0 = context1.getResources().getDisplayMetrics();
                if(f1 == -1.0f) {
                    f1 = TypedValue.applyDimension(2, 12.0f, displayMetrics0);
                }
                if(f2 == -1.0f) {
                    f2 = TypedValue.applyDimension(2, 112.0f, displayMetrics0);
                }
                if(f == -1.0f) {
                    f = 1.0f;
                }
                appCompatTextViewAutoSizeHelper0.k(f1, f2, f);
            }
            appCompatTextViewAutoSizeHelper0.h();
        }
        if(ViewUtils.c && appCompatTextViewAutoSizeHelper0.a != 0) {
            int[] arr_v3 = appCompatTextViewAutoSizeHelper0.f;
            if(arr_v3.length > 0) {
                if(((float)Api26Impl.a(textView0)) == -1.0f) {
                    Api26Impl.c(textView0, arr_v3, 0);
                }
                else {
                    Api26Impl.b(textView0, Math.round(appCompatTextViewAutoSizeHelper0.d), Math.round(appCompatTextViewAutoSizeHelper0.e), Math.round(appCompatTextViewAutoSizeHelper0.c), 0);
                }
            }
        }
        TypedArray typedArray5 = context0.obtainStyledAttributes(attributeSet0, arr_v1);
        TintTypedArray tintTypedArray3 = new TintTypedArray(context0, typedArray5);
        int v7 = typedArray5.getResourceId(8, -1);
        Drawable drawable0 = v7 == -1 ? null : appCompatDrawableManager0.b(context0, v7);
        int v8 = typedArray5.getResourceId(13, -1);
        Drawable drawable1 = v8 == -1 ? null : appCompatDrawableManager0.b(context0, v8);
        int v9 = typedArray5.getResourceId(9, -1);
        Drawable drawable2 = v9 == -1 ? null : appCompatDrawableManager0.b(context0, v9);
        int v10 = typedArray5.getResourceId(6, -1);
        Drawable drawable3 = v10 == -1 ? null : appCompatDrawableManager0.b(context0, v10);
        int v11 = typedArray5.getResourceId(10, -1);
        Drawable drawable4 = v11 == -1 ? null : appCompatDrawableManager0.b(context0, v11);
        int v12 = typedArray5.getResourceId(7, -1);
        Drawable drawable5 = v12 == -1 ? null : appCompatDrawableManager0.b(context0, v12);
        if(drawable4 != null || drawable5 != null) {
            Drawable[] arr_drawable2 = textView0.getCompoundDrawablesRelative();
            if(drawable4 == null) {
                drawable4 = arr_drawable2[0];
            }
            if(drawable1 == null) {
                drawable1 = arr_drawable2[1];
            }
            if(drawable5 == null) {
                drawable5 = arr_drawable2[2];
            }
            if(drawable3 == null) {
                drawable3 = arr_drawable2[3];
            }
            textView0.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable4, drawable1, drawable5, drawable3);
        }
        else if(drawable0 != null || drawable1 != null || drawable2 != null || drawable3 != null) {
            Drawable[] arr_drawable = textView0.getCompoundDrawablesRelative();
            Drawable drawable6 = arr_drawable[0];
            if(drawable6 != null || arr_drawable[2] != null) {
                if(drawable1 == null) {
                    drawable1 = arr_drawable[1];
                }
                if(drawable3 == null) {
                    drawable3 = arr_drawable[3];
                }
                textView0.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable6, drawable1, arr_drawable[2], drawable3);
            }
            else {
                Drawable[] arr_drawable1 = textView0.getCompoundDrawables();
                if(drawable0 == null) {
                    drawable0 = arr_drawable1[0];
                }
                if(drawable1 == null) {
                    drawable1 = arr_drawable1[1];
                }
                if(drawable2 == null) {
                    drawable2 = arr_drawable1[2];
                }
                if(drawable3 == null) {
                    drawable3 = arr_drawable1[3];
                }
                textView0.setCompoundDrawablesWithIntrinsicBounds(drawable0, drawable1, drawable2, drawable3);
            }
        }
        if(typedArray5.hasValue(11)) {
            TextViewCompat.b(textView0, tintTypedArray3.a(11));
        }
        if(typedArray5.hasValue(12)) {
            TextViewCompat.c(textView0, DrawableUtils.c(typedArray5.getInt(12, -1), null));
        }
        int v13 = typedArray5.getDimensionPixelSize(15, -1);
        int v14 = typedArray5.getDimensionPixelSize(18, -1);
        if(typedArray5.hasValue(19)) {
            TypedValue typedValue0 = typedArray5.peekValue(19);
            if(typedValue0 == null || typedValue0.type != 5) {
                f3 = (float)typedArray5.getDimensionPixelSize(19, -1);
                v16 = -1;
            }
            else {
                int v15 = typedValue0.data & 15;
                f3 = TypedValue.complexToFloat(typedValue0.data);
                v16 = v15;
            }
        }
        else {
            f3 = -1.0f;
            v16 = -1;
        }
        tintTypedArray3.f();
        if(v13 != -1) {
            TextViewCompat.d(textView0, v13);
        }
        if(v14 != -1) {
            TextViewCompat.e(textView0, v14);
        }
        if(f3 != -1.0f) {
            if(v16 == -1) {
                TextViewCompat.f(textView0, ((int)f3));
                return;
            }
            TextViewCompat.g(textView0, v16, f3);
        }
    }

    public final void g(int v, Context context0) {
        TypedArray typedArray0 = context0.obtainStyledAttributes(v, R.styleable.w);
        TintTypedArray tintTypedArray0 = new TintTypedArray(context0, typedArray0);
        boolean z = typedArray0.hasValue(14);
        TextView textView0 = this.a;
        if(z) {
            textView0.setAllCaps(typedArray0.getBoolean(14, false));
        }
        int v1 = Build.VERSION.SDK_INT;
        if(v1 < 23) {
            if(typedArray0.hasValue(3)) {
                ColorStateList colorStateList0 = tintTypedArray0.a(3);
                if(colorStateList0 != null) {
                    textView0.setTextColor(colorStateList0);
                }
            }
            if(typedArray0.hasValue(5)) {
                ColorStateList colorStateList1 = tintTypedArray0.a(5);
                if(colorStateList1 != null) {
                    textView0.setLinkTextColor(colorStateList1);
                }
            }
            if(typedArray0.hasValue(4)) {
                ColorStateList colorStateList2 = tintTypedArray0.a(4);
                if(colorStateList2 != null) {
                    textView0.setHintTextColor(colorStateList2);
                }
            }
        }
        if(typedArray0.hasValue(0) && typedArray0.getDimensionPixelSize(0, -1) == 0) {
            textView0.setTextSize(0, 0.0f);
        }
        this.m(context0, tintTypedArray0);
        if(v1 >= 26 && typedArray0.hasValue(13)) {
            String s = typedArray0.getString(13);
            if(s != null) {
                Api26Impl.d(textView0, s);
            }
        }
        tintTypedArray0.f();
        Typeface typeface0 = this.l;
        if(typeface0 != null) {
            textView0.setTypeface(typeface0, this.j);
        }
    }

    public final void h(int v, int v1, int v2, int v3) {
        AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = this.i;
        if(appCompatTextViewAutoSizeHelper0.j()) {
            DisplayMetrics displayMetrics0 = appCompatTextViewAutoSizeHelper0.j.getResources().getDisplayMetrics();
            appCompatTextViewAutoSizeHelper0.k(TypedValue.applyDimension(v3, ((float)v), displayMetrics0), TypedValue.applyDimension(v3, ((float)v1), displayMetrics0), TypedValue.applyDimension(v3, ((float)v2), displayMetrics0));
            if(appCompatTextViewAutoSizeHelper0.h()) {
                appCompatTextViewAutoSizeHelper0.a();
            }
        }
    }

    public final void i(int[] arr_v, int v) {
        AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = this.i;
        if(appCompatTextViewAutoSizeHelper0.j()) {
            if(arr_v.length > 0) {
                int[] arr_v1 = new int[arr_v.length];
                if(v == 0) {
                    arr_v1 = Arrays.copyOf(arr_v, arr_v.length);
                }
                else {
                    DisplayMetrics displayMetrics0 = appCompatTextViewAutoSizeHelper0.j.getResources().getDisplayMetrics();
                    for(int v1 = 0; v1 < arr_v.length; ++v1) {
                        arr_v1[v1] = Math.round(TypedValue.applyDimension(v, ((float)arr_v[v1]), displayMetrics0));
                    }
                }
                appCompatTextViewAutoSizeHelper0.f = AppCompatTextViewAutoSizeHelper.b(arr_v1);
                if(!appCompatTextViewAutoSizeHelper0.i()) {
                    throw new IllegalArgumentException("None of the preset sizes is valid: " + Arrays.toString(arr_v));
                }
            }
            else {
                appCompatTextViewAutoSizeHelper0.g = false;
            }
            if(appCompatTextViewAutoSizeHelper0.h()) {
                appCompatTextViewAutoSizeHelper0.a();
            }
        }
    }

    public final void j(int v) {
        AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = this.i;
        if(appCompatTextViewAutoSizeHelper0.j()) {
            switch(v) {
                case 0: {
                    appCompatTextViewAutoSizeHelper0.a = 0;
                    appCompatTextViewAutoSizeHelper0.d = -1.0f;
                    appCompatTextViewAutoSizeHelper0.e = -1.0f;
                    appCompatTextViewAutoSizeHelper0.c = -1.0f;
                    appCompatTextViewAutoSizeHelper0.f = new int[0];
                    appCompatTextViewAutoSizeHelper0.b = false;
                    return;
                }
                case 1: {
                    DisplayMetrics displayMetrics0 = appCompatTextViewAutoSizeHelper0.j.getResources().getDisplayMetrics();
                    appCompatTextViewAutoSizeHelper0.k(TypedValue.applyDimension(2, 12.0f, displayMetrics0), TypedValue.applyDimension(2, 112.0f, displayMetrics0), 1.0f);
                    if(appCompatTextViewAutoSizeHelper0.h()) {
                        appCompatTextViewAutoSizeHelper0.a();
                        return;
                    }
                    break;
                }
                default: {
                    throw new IllegalArgumentException("Unknown auto-size text type: " + v);
                }
            }
        }
    }

    public final void k(ColorStateList colorStateList0) {
        if(this.h == null) {
            this.h = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        this.h.a = colorStateList0;
        this.h.d = colorStateList0 != null;
        this.b = this.h;
        this.c = this.h;
        this.d = this.h;
        this.e = this.h;
        this.f = this.h;
        this.g = this.h;
    }

    public final void l(PorterDuff.Mode porterDuff$Mode0) {
        if(this.h == null) {
            this.h = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        this.h.b = porterDuff$Mode0;
        this.h.c = porterDuff$Mode0 != null;
        this.b = this.h;
        this.c = this.h;
        this.d = this.h;
        this.e = this.h;
        this.f = this.h;
        this.g = this.h;
    }

    public final void m(Context context0, TintTypedArray tintTypedArray0) {
        TypedArray typedArray0 = tintTypedArray0.b;
        this.j = typedArray0.getInt(2, this.j);
        int v = Build.VERSION.SDK_INT;
        if(v >= 28) {
            int v1 = typedArray0.getInt(11, -1);
            this.k = v1;
            if(v1 != -1) {
                this.j &= 2;
            }
        }
        int v2 = 10;
        boolean z = false;
        if(!typedArray0.hasValue(10) && !typedArray0.hasValue(12)) {
            if(typedArray0.hasValue(1)) {
                this.m = false;
                int v3 = typedArray0.getInt(1, 1);
                switch(v3) {
                    case 1: {
                        this.l = Typeface.SANS_SERIF;
                        break;
                    }
                    case 2: {
                        this.l = Typeface.SERIF;
                        return;
                    label_18:
                        if(v3 == 3) {
                            this.l = Typeface.MONOSPACE;
                            return;
                        }
                        break;
                    }
                    default: {
                        goto label_18;
                    }
                }
            }
            return;
        }
        this.l = null;
        if(typedArray0.hasValue(12)) {
            v2 = 12;
        }
        int v4 = this.k;
        int v5 = this.j;
        if(!context0.isRestricted()) {
            AppCompatTextHelper.1 appCompatTextHelper$10 = new AppCompatTextHelper.1(this, v4, v5, new WeakReference(this.a));
            try {
                Typeface typeface0 = tintTypedArray0.d(v2, this.j, appCompatTextHelper$10);
                if(typeface0 != null) {
                    this.l = v < 28 || this.k == -1 ? typeface0 : Api28Impl.a(Typeface.create(typeface0, 0), this.k, (this.j & 2) != 0);
                }
                this.m = this.l == null;
            }
            catch(UnsupportedOperationException | Resources.NotFoundException unused_ex) {
            }
        }
        if(this.l == null) {
            String s = typedArray0.getString(v2);
            if(s != null) {
                if(Build.VERSION.SDK_INT >= 28 && this.k != -1) {
                    Typeface typeface1 = Typeface.create(s, 0);
                    int v6 = this.k;
                    if((this.j & 2) != 0) {
                        z = true;
                    }
                    this.l = Api28Impl.a(typeface1, v6, z);
                    return;
                }
                this.l = Typeface.create(s, this.j);
            }
        }
    }
}


package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.RippleDrawable;
import android.net.Uri;
import android.util.AttributeSet;
import android.widget.ImageButton;
import android.widget.ImageView;
import androidx.appcompat.content.res.AppCompatResources;

public class AppCompatImageButton extends ImageButton {
    public final AppCompatBackgroundHelper a;
    public final AppCompatImageHelper b;
    public boolean c;

    public AppCompatImageButton(Context context0, AttributeSet attributeSet0) {
        this(context0, attributeSet0, 0x7F0402C4);  // attr:imageButtonStyle
    }

    public AppCompatImageButton(Context context0, AttributeSet attributeSet0, int v) {
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, v);
        this.c = false;
        ThemeUtils.a(this, this.getContext());
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = new AppCompatBackgroundHelper(this);
        this.a = appCompatBackgroundHelper0;
        appCompatBackgroundHelper0.d(attributeSet0, v);
        AppCompatImageHelper appCompatImageHelper0 = new AppCompatImageHelper(this);
        this.b = appCompatImageHelper0;
        appCompatImageHelper0.b(attributeSet0, v);
    }

    @Override  // android.widget.ImageView
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            appCompatImageHelper0.a();
        }
    }

    public ColorStateList getSupportBackgroundTintList() {
        return this.a == null ? null : this.a.b();
    }

    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.a == null ? null : this.a.c();
    }

    public ColorStateList getSupportImageTintList() {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            return appCompatImageHelper0.b == null ? null : appCompatImageHelper0.b.a;
        }
        return null;
    }

    public PorterDuff.Mode getSupportImageTintMode() {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            return appCompatImageHelper0.b == null ? null : appCompatImageHelper0.b.b;
        }
        return null;
    }

    // 去混淆评级： 低(20)
    @Override  // android.widget.ImageView
    public final boolean hasOverlappingRendering() {
        return !(this.b.a.getBackground() instanceof RippleDrawable) != 0 && super.hasOverlappingRendering();
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.ImageView
    public void setImageBitmap(Bitmap bitmap0) {
        super.setImageBitmap(bitmap0);
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            appCompatImageHelper0.a();
        }
    }

    @Override  // android.widget.ImageView
    public void setImageDrawable(Drawable drawable0) {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null && drawable0 != null && !this.c) {
            appCompatImageHelper0.d = drawable0.getLevel();
        }
        super.setImageDrawable(drawable0);
        if(appCompatImageHelper0 != null) {
            appCompatImageHelper0.a();
            if(!this.c) {
                ImageView imageView0 = appCompatImageHelper0.a;
                if(imageView0.getDrawable() != null) {
                    imageView0.getDrawable().setLevel(appCompatImageHelper0.d);
                }
            }
        }
    }

    @Override  // android.widget.ImageView
    public void setImageLevel(int v) {
        super.setImageLevel(v);
        this.c = true;
    }

    @Override  // android.widget.ImageView
    public void setImageResource(int v) {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        ImageView imageView0 = appCompatImageHelper0.a;
        if(v == 0) {
            imageView0.setImageDrawable(null);
        }
        else {
            Drawable drawable0 = AppCompatResources.a(imageView0.getContext(), v);
            if(drawable0 != null) {
                DrawableUtils.a(drawable0);
            }
            imageView0.setImageDrawable(drawable0);
        }
        appCompatImageHelper0.a();
    }

    @Override  // android.widget.ImageView
    public void setImageURI(Uri uri0) {
        super.setImageURI(uri0);
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            appCompatImageHelper0.a();
        }
    }

    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }

    public void setSupportImageTintList(ColorStateList colorStateList0) {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            if(appCompatImageHelper0.b == null) {
                appCompatImageHelper0.b = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
            }
            appCompatImageHelper0.b.a = colorStateList0;
            appCompatImageHelper0.b.d = true;
            appCompatImageHelper0.a();
        }
    }

    public void setSupportImageTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            if(appCompatImageHelper0.b == null) {
                appCompatImageHelper0.b = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
            }
            TintInfo tintInfo0 = appCompatImageHelper0.b;
            tintInfo0.b = porterDuff$Mode0;
            tintInfo0.c = true;
            appCompatImageHelper0.a();
        }
    }
}


package androidx.appcompat.widget;

import androidx.core.view.ViewPropertyAnimatorListenerAdapter;

class ToolbarWidgetWrapper.2 extends ViewPropertyAnimatorListenerAdapter {
    public boolean a;
    public final int b;
    public final ToolbarWidgetWrapper c;

    public ToolbarWidgetWrapper.2(ToolbarWidgetWrapper toolbarWidgetWrapper0, int v) {
        this.c = toolbarWidgetWrapper0;
        this.b = v;
        this.a = false;
    }

    @Override  // androidx.core.view.ViewPropertyAnimatorListenerAdapter
    public final void a() {
        this.a = true;
    }

    @Override  // androidx.core.view.ViewPropertyAnimatorListenerAdapter
    public final void b() {
        this.c.a.setVisibility(0);
    }

    @Override  // androidx.core.view.ViewPropertyAnimatorListener
    public final void c() {
        if(!this.a) {
            this.c.a.setVisibility(this.b);
        }
    }
}


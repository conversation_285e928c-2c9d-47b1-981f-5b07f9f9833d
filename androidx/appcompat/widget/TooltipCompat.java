package androidx.appcompat.widget;

import android.os.Build.VERSION;
import android.text.TextUtils;
import android.view.View;

public abstract class TooltipCompat {
    static abstract class Api26Impl {
        public static void a(View view0, CharSequence charSequence0) {
            view0.setTooltipText(charSequence0);
        }
    }

    public static void a(View view0, CharSequence charSequence0) {
        if(Build.VERSION.SDK_INT >= 26) {
            Api26Impl.a(view0, charSequence0);
            return;
        }
        if(TooltipCompatHandler.k != null && TooltipCompatHandler.k.a == view0) {
            TooltipCompatHandler.b(null);
        }
        if(TextUtils.isEmpty(charSequence0)) {
            TooltipCompatHandler tooltipCompatHandler0 = TooltipCompatHandler.l;
            if(tooltipCompatHandler0 != null && tooltipCompatHandler0.a == view0) {
                tooltipCompatHandler0.a();
            }
            view0.setOnLongClickListener(null);
            view0.setLongClickable(false);
            view0.setOnHoverListener(null);
            return;
        }
        new TooltipCompatHandler(view0, charSequence0);
    }
}


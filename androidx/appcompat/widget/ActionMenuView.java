package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.ContextThemeWrapper;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import androidx.appcompat.view.menu.ActionMenuItemView;
import androidx.appcompat.view.menu.MenuBuilder.ItemInvoker;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuItemImpl;
import androidx.appcompat.view.menu.MenuPresenter.Callback;
import androidx.appcompat.view.menu.MenuView;
import androidx.core.view.MenuProvider;

public class ActionMenuView extends LinearLayoutCompat implements ItemInvoker, MenuView {
    public interface ActionMenuChildView {
        boolean a();

        boolean b();
    }

    static class ActionMenuPresenterCallback implements Callback {
        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final void c(MenuBuilder menuBuilder0, boolean z) {
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final boolean d(MenuBuilder menuBuilder0) {
            return false;
        }
    }

    public static class LayoutParams extends androidx.appcompat.widget.LinearLayoutCompat.LayoutParams {
        public boolean a;
        public int b;
        public int c;
        public boolean d;
        public boolean e;
        public boolean f;

    }

    class MenuBuilderCallback implements androidx.appcompat.view.menu.MenuBuilder.Callback {
        public final ActionMenuView a;

        @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
        public final boolean a(MenuBuilder menuBuilder0, MenuItem menuItem0) {
            OnMenuItemClickListener actionMenuView$OnMenuItemClickListener0 = this.a.A;
            if(actionMenuView$OnMenuItemClickListener0 != null) {
                Toolbar toolbar0 = ((androidx.appcompat.widget.Toolbar.1)actionMenuView$OnMenuItemClickListener0).a;
                for(Object object0: toolbar0.G.b) {
                    if(((MenuProvider)object0).onMenuItemSelected(menuItem0)) {
                        return true;
                    }
                    if(false) {
                        break;
                    }
                }
                return toolbar0.I != null && toolbar0.I.onMenuItemClick(menuItem0);
            }
            return false;
        }

        @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
        public final void b(MenuBuilder menuBuilder0) {
            androidx.appcompat.view.menu.MenuBuilder.Callback menuBuilder$Callback0 = this.a.v;
            if(menuBuilder$Callback0 != null) {
                menuBuilder$Callback0.b(menuBuilder0);
            }
        }
    }

    public interface OnMenuItemClickListener {
    }

    public OnMenuItemClickListener A;
    public MenuBuilder p;
    public Context q;
    public int r;
    public boolean s;
    public ActionMenuPresenter t;
    public Callback u;
    public androidx.appcompat.view.menu.MenuBuilder.Callback v;
    public boolean w;
    public int x;
    public final int y;
    public final int z;

    public ActionMenuView(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0);
        this.setBaselineAligned(false);
        DisplayMetrics displayMetrics0 = context0.getResources().getDisplayMetrics();
        this.y = (int)(56.0f * displayMetrics0.density);
        this.z = (int)(displayMetrics0.density * 4.0f);
        this.q = context0;
        this.r = 0;
    }

    @Override  // androidx.appcompat.view.menu.MenuBuilder$ItemInvoker
    public final boolean a(MenuItemImpl menuItemImpl0) {
        return this.p.q(menuItemImpl0, null, 0);
    }

    @Override  // androidx.appcompat.view.menu.MenuView
    public final void b(MenuBuilder menuBuilder0) {
        this.p = menuBuilder0;
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final boolean checkLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return viewGroup$LayoutParams0 instanceof LayoutParams;
    }

    @Override  // android.view.View
    public final boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent0) {
        return false;
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return ActionMenuView.l();
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet0) {
        return new LayoutParams(this.getContext(), attributeSet0);  // 初始化器: Landroid/widget/LinearLayout$LayoutParams;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return ActionMenuView.m(viewGroup$LayoutParams0);
    }

    public Menu getMenu() {
        if(this.p == null) {
            Context context0 = this.getContext();
            MenuBuilder menuBuilder0 = new MenuBuilder(context0);
            this.p = menuBuilder0;
            menuBuilder0.e = new MenuBuilderCallback(this);
            ActionMenuPresenter actionMenuPresenter0 = new ActionMenuPresenter(context0);
            this.t = actionMenuPresenter0;
            actionMenuPresenter0.m = true;
            actionMenuPresenter0.n = true;
            Callback menuPresenter$Callback0 = this.u;
            if(menuPresenter$Callback0 == null) {
                menuPresenter$Callback0 = new ActionMenuPresenterCallback();  // 初始化器: Ljava/lang/Object;-><init>()V
            }
            actionMenuPresenter0.e = menuPresenter$Callback0;
            this.p.b(actionMenuPresenter0, this.q);
            this.t.h = this;
            this.p = this.t.c;
        }
        return this.p;
    }

    public Drawable getOverflowIcon() {
        this.getMenu();
        ActionMenuPresenter actionMenuPresenter0 = this.t;
        OverflowMenuButton actionMenuPresenter$OverflowMenuButton0 = actionMenuPresenter0.j;
        if(actionMenuPresenter$OverflowMenuButton0 != null) {
            return actionMenuPresenter$OverflowMenuButton0.getDrawable();
        }
        return actionMenuPresenter0.l ? actionMenuPresenter0.k : null;
    }

    public int getPopupTheme() {
        return this.r;
    }

    public int getWindowAnimations() {
        return 0;
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final androidx.appcompat.widget.LinearLayoutCompat.LayoutParams h() {
        return ActionMenuView.l();
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final androidx.appcompat.widget.LinearLayoutCompat.LayoutParams i(AttributeSet attributeSet0) {
        return new LayoutParams(this.getContext(), attributeSet0);  // 初始化器: Landroid/widget/LinearLayout$LayoutParams;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final androidx.appcompat.widget.LinearLayoutCompat.LayoutParams j(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return ActionMenuView.m(viewGroup$LayoutParams0);
    }

    public static LayoutParams l() {
        LayoutParams actionMenuView$LayoutParams0 = new LayoutParams(-2, -2);  // 初始化器: Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V
        actionMenuView$LayoutParams0.a = false;
        actionMenuView$LayoutParams0.gravity = 16;
        return actionMenuView$LayoutParams0;
    }

    public static LayoutParams m(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        LayoutParams actionMenuView$LayoutParams0;
        if(viewGroup$LayoutParams0 != null) {
            if(viewGroup$LayoutParams0 instanceof LayoutParams) {
                actionMenuView$LayoutParams0 = new LayoutParams(((LayoutParams)viewGroup$LayoutParams0));  // 初始化器: Landroid/widget/LinearLayout$LayoutParams;-><init>(Landroid/view/ViewGroup$LayoutParams;)V
                actionMenuView$LayoutParams0.a = ((LayoutParams)viewGroup$LayoutParams0).a;
            }
            else {
                actionMenuView$LayoutParams0 = new LayoutParams(viewGroup$LayoutParams0);  // 初始化器: Landroid/widget/LinearLayout$LayoutParams;-><init>(Landroid/view/ViewGroup$LayoutParams;)V
            }
            if(actionMenuView$LayoutParams0.gravity <= 0) {
                actionMenuView$LayoutParams0.gravity = 16;
            }
            return actionMenuView$LayoutParams0;
        }
        return ActionMenuView.l();
    }

    public final boolean n(int v) {
        boolean z = false;
        if(v == 0) {
            return false;
        }
        View view0 = this.getChildAt(v - 1);
        View view1 = this.getChildAt(v);
        if(v < this.getChildCount() && view0 instanceof ActionMenuChildView) {
            z = ((ActionMenuChildView)view0).a();
        }
        return v > 0 && view1 instanceof ActionMenuChildView ? z | ((ActionMenuChildView)view1).b() : z;
    }

    @Override  // android.view.View
    public final void onConfigurationChanged(Configuration configuration0) {
        super.onConfigurationChanged(configuration0);
        ActionMenuPresenter actionMenuPresenter0 = this.t;
        if(actionMenuPresenter0 != null) {
            actionMenuPresenter0.d(false);
            if(this.t.o()) {
                this.t.n();
                this.t.p();
            }
        }
    }

    @Override  // android.view.ViewGroup
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        ActionMenuPresenter actionMenuPresenter0 = this.t;
        if(actionMenuPresenter0 != null) {
            actionMenuPresenter0.n();
            ActionButtonSubmenu actionMenuPresenter$ActionButtonSubmenu0 = actionMenuPresenter0.u;
            if(actionMenuPresenter$ActionButtonSubmenu0 != null && actionMenuPresenter$ActionButtonSubmenu0.b()) {
                actionMenuPresenter$ActionButtonSubmenu0.j.dismiss();
            }
        }
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final void onLayout(boolean z, int v, int v1, int v2, int v3) {
        int v15;
        int v14;
        if(!this.w) {
            super.onLayout(z, v, v1, v2, v3);
            return;
        }
        int v4 = this.getChildCount();
        int v5 = (v3 - v1) / 2;
        int v6 = this.getDividerWidth();
        int v7 = v2 - v;
        int v8 = v7 - this.getPaddingRight() - this.getPaddingLeft();
        boolean z1 = this.getLayoutDirection() == 1;
        int v10 = 0;
        int v11 = 0;
        for(int v9 = 0; v9 < v4; ++v9) {
            View view0 = this.getChildAt(v9);
            if(view0.getVisibility() != 8) {
                LayoutParams actionMenuView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                if(actionMenuView$LayoutParams0.a) {
                    int v12 = view0.getMeasuredWidth();
                    if(this.n(v9)) {
                        v12 += v6;
                    }
                    int v13 = view0.getMeasuredHeight();
                    if(z1) {
                        v14 = this.getPaddingLeft() + actionMenuView$LayoutParams0.leftMargin;
                        v15 = v14 + v12;
                    }
                    else {
                        v15 = this.getWidth() - this.getPaddingRight() - actionMenuView$LayoutParams0.rightMargin;
                        v14 = v15 - v12;
                    }
                    int v16 = v5 - v13 / 2;
                    view0.layout(v14, v16, v15, v13 + v16);
                    v8 -= v12;
                    v10 = 1;
                }
                else {
                    v8 -= view0.getMeasuredWidth() + actionMenuView$LayoutParams0.leftMargin + actionMenuView$LayoutParams0.rightMargin;
                    this.n(v9);
                    ++v11;
                }
            }
        }
        if(v4 == 1 && v10 == 0) {
            View view1 = this.getChildAt(0);
            int v17 = view1.getMeasuredWidth();
            int v18 = view1.getMeasuredHeight();
            int v19 = v7 / 2 - v17 / 2;
            int v20 = v5 - v18 / 2;
            view1.layout(v19, v20, v17 + v19, v18 + v20);
            return;
        }
        int v21 = v11 - (v10 ^ 1);
        int v22 = Math.max(0, (v21 <= 0 ? 0 : v8 / v21));
        if(z1) {
            int v23 = this.getWidth() - this.getPaddingRight();
            for(int v24 = 0; v24 < v4; ++v24) {
                View view2 = this.getChildAt(v24);
                LayoutParams actionMenuView$LayoutParams1 = (LayoutParams)view2.getLayoutParams();
                if(view2.getVisibility() != 8 && !actionMenuView$LayoutParams1.a) {
                    int v25 = v23 - actionMenuView$LayoutParams1.rightMargin;
                    int v26 = view2.getMeasuredWidth();
                    int v27 = view2.getMeasuredHeight();
                    int v28 = v5 - v27 / 2;
                    view2.layout(v25 - v26, v28, v25, v27 + v28);
                    v23 = v25 - (v26 + actionMenuView$LayoutParams1.leftMargin + v22);
                }
            }
            return;
        }
        int v29 = this.getPaddingLeft();
        for(int v30 = 0; v30 < v4; ++v30) {
            View view3 = this.getChildAt(v30);
            LayoutParams actionMenuView$LayoutParams2 = (LayoutParams)view3.getLayoutParams();
            if(view3.getVisibility() != 8 && !actionMenuView$LayoutParams2.a) {
                int v31 = v29 + actionMenuView$LayoutParams2.leftMargin;
                int v32 = view3.getMeasuredWidth();
                int v33 = view3.getMeasuredHeight();
                int v34 = v5 - v33 / 2;
                view3.layout(v31, v34, v31 + v32, v33 + v34);
                v29 = v32 + actionMenuView$LayoutParams2.rightMargin + v22 + v31;
            }
        }
    }

    @Override  // androidx.appcompat.widget.LinearLayoutCompat
    public final void onMeasure(int v, int v1) {
        int v49;
        int v48;
        int v47;
        int v43;
        int v40;
        int v39;
        int v38;
        int v37;
        int v29;
        int v25;
        int v24;
        int v23;
        boolean z = this.w;
        boolean z1 = View.MeasureSpec.getMode(v) == 0x40000000;
        this.w = z1;
        if(z != z1) {
            this.x = 0;
        }
        int v2 = View.MeasureSpec.getSize(v);
        if(this.w) {
            MenuBuilder menuBuilder0 = this.p;
            if(menuBuilder0 != null && v2 != this.x) {
                this.x = v2;
                menuBuilder0.p(true);
            }
        }
        int v3 = this.getChildCount();
        if(this.w && v3 > 0) {
            int v4 = View.MeasureSpec.getMode(v1);
            int v5 = View.MeasureSpec.getSize(v);
            int v6 = View.MeasureSpec.getSize(v1);
            int v7 = this.getPaddingLeft();
            int v8 = this.getPaddingRight();
            int v9 = this.getPaddingTop();
            int v10 = this.getPaddingBottom() + v9;
            int v11 = ViewGroup.getChildMeasureSpec(v1, v10, -2);
            int v12 = v5 - (v8 + v7);
            int v13 = this.y;
            int v14 = v12 / v13;
            if(v14 == 0) {
                this.setMeasuredDimension(v12, 0);
                return;
            }
            int v15 = v12 % v13 / v14 + v13;
            int v16 = this.getChildCount();
            int v17 = 0;
            int v18 = 0;
            int v19 = 0;
            boolean z2 = false;
            long v20 = 0L;
            int v21 = 0;
            int v22 = 0;
            while(true) {
                v23 = this.z;
                if(v19 >= v16) {
                    break;
                }
                View view0 = this.getChildAt(v19);
                if(view0.getVisibility() == 8) {
                    v24 = v12;
                    v25 = v10;
                }
                else {
                    if(view0 instanceof ActionMenuItemView) {
                        view0.setPadding(v23, 0, v23, 0);
                    }
                    LayoutParams actionMenuView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                    actionMenuView$LayoutParams0.f = false;
                    actionMenuView$LayoutParams0.c = 0;
                    actionMenuView$LayoutParams0.b = 0;
                    actionMenuView$LayoutParams0.d = false;
                    actionMenuView$LayoutParams0.leftMargin = 0;
                    actionMenuView$LayoutParams0.rightMargin = 0;
                    actionMenuView$LayoutParams0.e = view0 instanceof ActionMenuItemView && !TextUtils.isEmpty(((ActionMenuItemView)view0).getText()) != 0;
                    int v26 = actionMenuView$LayoutParams0.a ? 1 : v14;
                    ViewGroup.LayoutParams viewGroup$LayoutParams0 = view0.getLayoutParams();
                    v24 = v12;
                    v25 = v10;
                    int v27 = View.MeasureSpec.makeMeasureSpec(View.MeasureSpec.getSize(v11) - v10, View.MeasureSpec.getMode(v11));
                    ActionMenuItemView actionMenuItemView0 = view0 instanceof ActionMenuItemView ? ((ActionMenuItemView)view0) : null;
                    boolean z3 = actionMenuItemView0 != null && !TextUtils.isEmpty(actionMenuItemView0.getText()) != 0;
                    if(v26 <= 0 || z3 && v26 < 2) {
                        v29 = 0;
                    }
                    else {
                        view0.measure(View.MeasureSpec.makeMeasureSpec(v26 * v15, 0x80000000), v27);
                        int v28 = view0.getMeasuredWidth();
                        v29 = v28 / v15;
                        if(v28 % v15 != 0) {
                            ++v29;
                        }
                        if(z3 && v29 < 2) {
                            v29 = 2;
                        }
                    }
                    ((LayoutParams)viewGroup$LayoutParams0).d = !((LayoutParams)viewGroup$LayoutParams0).a && z3;
                    ((LayoutParams)viewGroup$LayoutParams0).b = v29;
                    view0.measure(View.MeasureSpec.makeMeasureSpec(v29 * v15, 0x40000000), v27);
                    v18 = Math.max(v18, v29);
                    if(actionMenuView$LayoutParams0.d) {
                        ++v21;
                    }
                    if(actionMenuView$LayoutParams0.a) {
                        z2 = true;
                    }
                    v14 -= v29;
                    v22 = Math.max(v22, view0.getMeasuredHeight());
                    if(v29 == 1) {
                        v20 |= (long)(1 << v19);
                    }
                    ++v17;
                }
                ++v19;
                v10 = v25;
                v12 = v24;
            }
            int v30 = v22;
            int v31 = 0;
            while(true) {
                if(v21 <= 0 || v14 <= 0) {
                    goto label_134;
                }
                int v32 = 0x7FFFFFFF;
                int v33 = 0;
                long v35 = 0L;
                for(int v34 = 0; v34 < v16; ++v34) {
                    LayoutParams actionMenuView$LayoutParams1 = (LayoutParams)this.getChildAt(v34).getLayoutParams();
                    if(actionMenuView$LayoutParams1.d) {
                        int v36 = actionMenuView$LayoutParams1.b;
                        if(v36 < v32) {
                            v35 = 1L << v34;
                            v32 = v36;
                            v33 = 1;
                        }
                        else if(v36 == v32) {
                            ++v33;
                            v35 |= 1L << v34;
                        }
                    }
                }
                v37 = v30;
                v38 = v31;
                v20 |= v35;
                if(v33 > v14) {
                    v39 = v4;
                }
                else {
                    int v41 = v32 + 1;
                    for(int v42 = 0; v42 < v16; ++v42) {
                        View view1 = this.getChildAt(v42);
                        LayoutParams actionMenuView$LayoutParams2 = (LayoutParams)view1.getLayoutParams();
                        if((v35 & ((long)(1 << v42))) != 0L) {
                            if(z2 && v17 == 2 && actionMenuView$LayoutParams2.e && v14 == 1) {
                                view1.setPadding(v23 + v15, 0, v23, 0);
                            }
                            ++actionMenuView$LayoutParams2.b;
                            actionMenuView$LayoutParams2.f = true;
                            --v14;
                        }
                        else if(actionMenuView$LayoutParams2.b == v41) {
                            v20 |= (long)(1 << v42);
                        }
                    }
                    v30 = v37;
                    v31 = 1;
                    continue;
                label_134:
                    v39 = v4;
                    v37 = v30;
                    v38 = v31;
                }
                v40 = v11;
                break;
            }
            boolean z4 = !z2 && v17 == 1;
            if(v14 > 0 && v20 != 0L && (v14 < v17 - 1 || z4 || v18 > 1)) {
                float f = (float)Long.bitCount(v20);
                if(!z4) {
                    if((v20 & 1L) != 0L && !((LayoutParams)this.getChildAt(0).getLayoutParams()).e) {
                        f -= 0.5f;
                    }
                    if((v20 & ((long)(1 << v16 - 1))) != 0L && !((LayoutParams)this.getChildAt(v16 - 1).getLayoutParams()).e) {
                        f -= 0.5f;
                    }
                }
                int v44 = f > 0.0f ? ((int)(((float)(v14 * v15)) / f)) : 0;
                v43 = v16;
                for(int v45 = 0; v45 < v43; ++v45) {
                    if((v20 & ((long)(1 << v45))) != 0L) {
                        View view2 = this.getChildAt(v45);
                        LayoutParams actionMenuView$LayoutParams3 = (LayoutParams)view2.getLayoutParams();
                        if(view2 instanceof ActionMenuItemView) {
                            actionMenuView$LayoutParams3.c = v44;
                            actionMenuView$LayoutParams3.f = true;
                            if(v45 == 0 && !actionMenuView$LayoutParams3.e) {
                                actionMenuView$LayoutParams3.leftMargin = -v44 / 2;
                            }
                            v38 = 1;
                        }
                        else if(actionMenuView$LayoutParams3.a) {
                            actionMenuView$LayoutParams3.c = v44;
                            actionMenuView$LayoutParams3.f = true;
                            actionMenuView$LayoutParams3.rightMargin = -v44 / 2;
                            v38 = 1;
                        }
                        else {
                            if(v45 != 0) {
                                actionMenuView$LayoutParams3.leftMargin = v44 / 2;
                            }
                            if(v45 != v43 - 1) {
                                actionMenuView$LayoutParams3.rightMargin = v44 / 2;
                            }
                        }
                    }
                }
            }
            else {
                v43 = v16;
            }
            if(v38 != 0) {
                int v46 = 0;
                while(v46 < v43) {
                    View view3 = this.getChildAt(v46);
                    LayoutParams actionMenuView$LayoutParams4 = (LayoutParams)view3.getLayoutParams();
                    if(actionMenuView$LayoutParams4.f) {
                        v47 = v40;
                        view3.measure(View.MeasureSpec.makeMeasureSpec(actionMenuView$LayoutParams4.b * v15 + actionMenuView$LayoutParams4.c, 0x40000000), v47);
                    }
                    else {
                        v47 = v40;
                    }
                    ++v46;
                    v40 = v47;
                }
            }
            if(v39 == 0x40000000) {
                v49 = v6;
                v48 = v12;
            }
            else {
                v48 = v12;
                v49 = v37;
            }
            this.setMeasuredDimension(v48, v49);
            return;
        }
        for(int v50 = 0; v50 < v3; ++v50) {
            LayoutParams actionMenuView$LayoutParams5 = (LayoutParams)this.getChildAt(v50).getLayoutParams();
            actionMenuView$LayoutParams5.rightMargin = 0;
            actionMenuView$LayoutParams5.leftMargin = 0;
        }
        super.onMeasure(v, v1);
    }

    public void setExpandedActionViewsExclusive(boolean z) {
        this.t.r = z;
    }

    public void setOnMenuItemClickListener(OnMenuItemClickListener actionMenuView$OnMenuItemClickListener0) {
        this.A = actionMenuView$OnMenuItemClickListener0;
    }

    public void setOverflowIcon(Drawable drawable0) {
        this.getMenu();
        ActionMenuPresenter actionMenuPresenter0 = this.t;
        OverflowMenuButton actionMenuPresenter$OverflowMenuButton0 = actionMenuPresenter0.j;
        if(actionMenuPresenter$OverflowMenuButton0 != null) {
            actionMenuPresenter$OverflowMenuButton0.setImageDrawable(drawable0);
            return;
        }
        actionMenuPresenter0.l = true;
        actionMenuPresenter0.k = drawable0;
    }

    public void setOverflowReserved(boolean z) {
        this.s = z;
    }

    public void setPopupTheme(int v) {
        if(this.r != v) {
            this.r = v;
            if(v == 0) {
                this.q = this.getContext();
                return;
            }
            this.q = new ContextThemeWrapper(this.getContext(), v);
        }
    }

    public void setPresenter(ActionMenuPresenter actionMenuPresenter0) {
        this.t = actionMenuPresenter0;
        actionMenuPresenter0.h = this;
        this.p = actionMenuPresenter0.c;
    }
}


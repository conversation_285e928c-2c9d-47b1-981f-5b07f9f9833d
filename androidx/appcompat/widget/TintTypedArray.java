package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat.FontCallback;
import androidx.core.content.res.ResourcesCompat;

public class TintTypedArray {
    public final Context a;
    public final TypedArray b;
    public TypedValue c;

    public TintTypedArray(Context context0, TypedArray typedArray0) {
        this.a = context0;
        this.b = typedArray0;
    }

    public final ColorStateList a(int v) {
        TypedArray typedArray0 = this.b;
        if(typedArray0.hasValue(v)) {
            int v1 = typedArray0.getResourceId(v, 0);
            if(v1 != 0) {
                ColorStateList colorStateList0 = ContextCompat.getColorStateList(this.a, v1);
                return colorStateList0 == null ? typedArray0.getColorStateList(v) : colorStateList0;
            }
        }
        return typedArray0.getColorStateList(v);
    }

    public final Drawable b(int v) {
        TypedArray typedArray0 = this.b;
        if(typedArray0.hasValue(v)) {
            int v1 = typedArray0.getResourceId(v, 0);
            return v1 == 0 ? typedArray0.getDrawable(v) : AppCompatResources.a(this.a, v1);
        }
        return typedArray0.getDrawable(v);
    }

    public final Drawable c(int v) {
        if(this.b.hasValue(v)) {
            int v1 = this.b.getResourceId(v, 0);
            if(v1 != 0) {
                AppCompatDrawableManager appCompatDrawableManager0 = AppCompatDrawableManager.a();
                synchronized(appCompatDrawableManager0) {
                    return appCompatDrawableManager0.a.g(this.a, v1, true);
                }
            }
        }
        return null;
    }

    public final Typeface d(int v, int v1, FontCallback resourcesCompat$FontCallback0) {
        int v2 = this.b.getResourceId(v, 0);
        if(v2 == 0) {
            return null;
        }
        if(this.c == null) {
            this.c = new TypedValue();
        }
        TypedValue typedValue0 = this.c;
        return this.a.isRestricted() ? null : ResourcesCompat.e(this.a, v2, typedValue0, v1, resourcesCompat$FontCallback0, true, false);
    }

    public static TintTypedArray e(Context context0, AttributeSet attributeSet0, int[] arr_v, int v, int v1) {
        return new TintTypedArray(context0, context0.obtainStyledAttributes(attributeSet0, arr_v, v, v1));
    }

    public final void f() {
        this.b.recycle();
    }
}


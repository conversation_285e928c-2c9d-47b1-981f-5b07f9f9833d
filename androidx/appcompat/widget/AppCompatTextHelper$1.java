package androidx.appcompat.widget;

import android.graphics.Typeface;
import android.os.Build.VERSION;
import android.widget.TextView;
import androidx.core.content.res.ResourcesCompat.FontCallback;
import java.lang.ref.WeakReference;

class AppCompatTextHelper.1 extends FontCallback {
    public final int a;
    public final int b;
    public final WeakReference c;
    public final AppCompatTextHelper d;

    public AppCompatTextHelper.1(AppCompatTextHelper appCompatTextHelper0, int v, int v1, WeakReference weakReference0) {
        this.d = appCompatTextHelper0;
        this.a = v;
        this.b = v1;
        this.c = weakReference0;
    }

    @Override  // androidx.core.content.res.ResourcesCompat$FontCallback
    public final void c(int v) {
    }

    @Override  // androidx.core.content.res.ResourcesCompat$FontCallback
    public final void d(Typeface typeface0) {
        if(Build.VERSION.SDK_INT >= 28) {
            int v = this.a;
            if(v != -1) {
                typeface0 = Api28Impl.a(typeface0, v, (this.b & 2) != 0);
            }
        }
        AppCompatTextHelper appCompatTextHelper0 = this.d;
        if(appCompatTextHelper0.m) {
            appCompatTextHelper0.l = typeface0;
            TextView textView0 = (TextView)this.c.get();
            if(textView0 != null) {
                if(textView0.isAttachedToWindow()) {
                    textView0.post(new AppCompatTextHelper.2(textView0, typeface0, appCompatTextHelper0.j));
                    return;
                }
                textView0.setTypeface(typeface0, appCompatTextHelper0.j);
            }
        }
    }
}


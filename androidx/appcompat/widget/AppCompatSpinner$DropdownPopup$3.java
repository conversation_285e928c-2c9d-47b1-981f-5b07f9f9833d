package androidx.appcompat.widget;

import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.view.ViewTreeObserver;
import android.widget.PopupWindow.OnDismissListener;

class AppCompatSpinner.DropdownPopup.3 implements PopupWindow.OnDismissListener {
    public final ViewTreeObserver.OnGlobalLayoutListener a;
    public final DropdownPopup b;

    public AppCompatSpinner.DropdownPopup.3(DropdownPopup appCompatSpinner$DropdownPopup0, ViewTreeObserver.OnGlobalLayoutListener viewTreeObserver$OnGlobalLayoutListener0) {
        this.b = appCompatSpinner$DropdownPopup0;
        this.a = viewTreeObserver$OnGlobalLayoutListener0;
    }

    @Override  // android.widget.PopupWindow$OnDismissListener
    public final void onDismiss() {
        ViewTreeObserver viewTreeObserver0 = AppCompatSpinner.this.getViewTreeObserver();
        if(viewTreeObserver0 != null) {
            viewTreeObserver0.removeGlobalOnLayoutListener(this.a);
        }
    }
}


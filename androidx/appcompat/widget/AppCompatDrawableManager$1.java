package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap.Config;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Shader.TileMode;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.ColorUtils;

class AppCompatDrawableManager.1 implements ResourceManagerHooks {
    public final int[] a;
    public final int[] b;
    public final int[] c;
    public final int[] d;
    public final int[] e;
    public final int[] f;

    public AppCompatDrawableManager.1() {
        this.a = new int[]{0x7F0800BC, 0x7F0800BA, 0x7F080070};
        this.b = new int[]{0x7F080088, 0x7F0800AB, 0x7F08008F, 0x7F08008A, 0x7F08008B, 0x7F08008E, 0x7F08008D};  // drawable:abc_seekbar_tick_mark_material
        this.c = new int[]{0x7F0800B9, 0x7F0800BB, 0x7F080081, 0x7F0800B5, 0x7F0800B6, 0x7F0800B7, 0x7F0800B8};  // drawable:abc_text_cursor_material
        this.d = new int[]{0x7F0800A1, 0x7F08007F, 0x7F0800A0};  // drawable:abc_cab_background_internal_bg
        this.e = new int[]{0x7F0800B3, 0x7F0800BD};  // drawable:abc_tab_indicator_material
        this.f = new int[]{0x7F080073, 0x7F080079, 0x7F080074, 0x7F08007A};  // drawable:abc_btn_check_material
    }

    public static boolean a(int v, int[] arr_v) {
        for(int v1 = 0; v1 < arr_v.length; ++v1) {
            if(arr_v[v1] == v) {
                return true;
            }
        }
        return false;
    }

    public static ColorStateList b(int v, Context context0) {
        int[] arr_v = new int[4];
        int v1 = ThemeUtils.c(0x7F04014D, context0);  // attr:colorControlHighlight
        arr_v[0] = ThemeUtils.b(0x7F04014A, context0);  // attr:colorButtonNormal
        arr_v[1] = ColorUtils.b(v1, v);
        arr_v[2] = ColorUtils.b(v1, v);
        arr_v[3] = v;
        return new ColorStateList(new int[][]{ThemeUtils.b, ThemeUtils.d, ThemeUtils.c, ThemeUtils.f}, arr_v);
    }

    public static LayerDrawable c(ResourceManagerInternal resourceManagerInternal0, Context context0, int v) {
        BitmapDrawable bitmapDrawable2;
        BitmapDrawable bitmapDrawable1;
        BitmapDrawable bitmapDrawable0;
        int v1 = context0.getResources().getDimensionPixelSize(v);
        Drawable drawable0 = resourceManagerInternal0.f(context0, 0x7F0800AF);  // drawable:abc_star_black_48dp
        Drawable drawable1 = resourceManagerInternal0.f(context0, 0x7F0800B0);  // drawable:abc_star_half_black_48dp
        if(!(drawable0 instanceof BitmapDrawable) || drawable0.getIntrinsicWidth() != v1 || drawable0.getIntrinsicHeight() != v1) {
            Bitmap bitmap0 = Bitmap.createBitmap(v1, v1, Bitmap.Config.ARGB_8888);
            Canvas canvas0 = new Canvas(bitmap0);
            drawable0.setBounds(0, 0, v1, v1);
            drawable0.draw(canvas0);
            bitmapDrawable0 = new BitmapDrawable(bitmap0);
            bitmapDrawable1 = new BitmapDrawable(bitmap0);
        }
        else {
            bitmapDrawable0 = (BitmapDrawable)drawable0;
            bitmapDrawable1 = new BitmapDrawable(bitmapDrawable0.getBitmap());
        }
        bitmapDrawable1.setTileModeX(Shader.TileMode.REPEAT);
        if(!(drawable1 instanceof BitmapDrawable) || drawable1.getIntrinsicWidth() != v1 || drawable1.getIntrinsicHeight() != v1) {
            Bitmap bitmap1 = Bitmap.createBitmap(v1, v1, Bitmap.Config.ARGB_8888);
            Canvas canvas1 = new Canvas(bitmap1);
            drawable1.setBounds(0, 0, v1, v1);
            drawable1.draw(canvas1);
            bitmapDrawable2 = new BitmapDrawable(bitmap1);
        }
        else {
            bitmapDrawable2 = (BitmapDrawable)drawable1;
        }
        LayerDrawable layerDrawable0 = new LayerDrawable(new Drawable[]{bitmapDrawable0, bitmapDrawable2, bitmapDrawable1});
        layerDrawable0.setId(0, 0x1020000);
        layerDrawable0.setId(1, 0x102000F);
        layerDrawable0.setId(2, 0x102000D);
        return layerDrawable0;
    }

    public final ColorStateList d(int v, Context context0) {
        switch(v) {
            case 0x7F080072: {  // drawable:abc_btn_borderless_material
                return AppCompatDrawableManager.1.b(0, context0);
            }
            case 0x7F080077: {  // drawable:abc_btn_colored_material
                return AppCompatDrawableManager.1.b(ThemeUtils.c(0x7F040148, context0), context0);  // attr:colorAccent
            }
            case 0x7F080078: {  // drawable:abc_btn_default_mtrl_shape
                return AppCompatDrawableManager.1.b(ThemeUtils.c(0x7F04014A, context0), context0);  // attr:colorButtonNormal
            }
            case 0x7F080084: {  // drawable:abc_edit_text_material
                return ContextCompat.getColorStateList(context0, 0x7F060015);  // color:abc_tint_edittext
            }
            case 0x7F0800AD: 
            case 0x7F0800AE: {  // drawable:abc_spinner_textfield_background_material
                return ContextCompat.getColorStateList(context0, 0x7F060017);  // color:abc_tint_spinner
            }
            case 0x7F0800B1: {  // drawable:abc_switch_thumb_material
                int[][] arr2_v = new int[3][];
                int[] arr_v = new int[3];
                ColorStateList colorStateList0 = ThemeUtils.d(0x7F040180, context0);  // attr:colorSwitchThumbNormal
                if(colorStateList0 != null && colorStateList0.isStateful()) {
                    arr2_v[0] = ThemeUtils.b;
                    arr_v[0] = colorStateList0.getColorForState(ThemeUtils.b, 0);
                    arr2_v[1] = ThemeUtils.e;
                    arr_v[1] = ThemeUtils.c(0x7F04014C, context0);  // attr:colorControlActivated
                    arr2_v[2] = ThemeUtils.f;
                    arr_v[2] = colorStateList0.getDefaultColor();
                    return new ColorStateList(arr2_v, arr_v);
                }
                arr2_v[0] = ThemeUtils.b;
                arr_v[0] = ThemeUtils.b(0x7F040180, context0);  // attr:colorSwitchThumbNormal
                arr2_v[1] = ThemeUtils.e;
                arr_v[1] = ThemeUtils.c(0x7F04014C, context0);  // attr:colorControlActivated
                arr2_v[2] = ThemeUtils.f;
                arr_v[2] = ThemeUtils.c(0x7F040180, context0);  // attr:colorSwitchThumbNormal
                return new ColorStateList(arr2_v, arr_v);
            }
            case 0x7F0800B2: {
                return ContextCompat.getColorStateList(context0, 0x7F060018);  // color:abc_tint_switch_track
            }
            default: {
                if(AppCompatDrawableManager.1.a(v, this.b)) {
                    return ThemeUtils.d(0x7F04014E, context0);  // attr:colorControlNormal
                }
                if(AppCompatDrawableManager.1.a(v, this.e)) {
                    return ContextCompat.getColorStateList(context0, 0x7F060014);  // color:abc_tint_default
                }
                if(AppCompatDrawableManager.1.a(v, this.f)) {
                    return ContextCompat.getColorStateList(context0, 0x7F060013);  // color:abc_tint_btn_checkable
                }
                return v == 0x7F0800AA ? ContextCompat.getColorStateList(context0, 0x7F060016) : null;  // drawable:abc_seekbar_thumb_material
            }
        }
    }

    public static void e(Drawable drawable0, int v, PorterDuff.Mode porterDuff$Mode0) {
        PorterDuffColorFilter porterDuffColorFilter0;
        Drawable drawable1 = drawable0.mutate();
        if(porterDuff$Mode0 == null) {
            porterDuff$Mode0 = AppCompatDrawableManager.b;
        }
        synchronized(AppCompatDrawableManager.class) {
            porterDuffColorFilter0 = ResourceManagerInternal.h(v, porterDuff$Mode0);
        }
        drawable1.setColorFilter(porterDuffColorFilter0);
    }
}


package androidx.appcompat.widget;

import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.util.AttributeSet;
import android.widget.ImageView;
import androidx.appcompat.R.styleable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.view.ViewCompat;
import androidx.core.widget.ImageViewCompat;

public class AppCompatImageHelper {
    public final ImageView a;
    public TintInfo b;
    public TintInfo c;
    public int d;

    public AppCompatImageHelper(ImageView imageView0) {
        this.d = 0;
        this.a = imageView0;
    }

    public final void a() {
        ImageView imageView0 = this.a;
        Drawable drawable0 = imageView0.getDrawable();
        if(drawable0 != null) {
            DrawableUtils.a(drawable0);
        }
        if(drawable0 != null) {
            if(Build.VERSION.SDK_INT <= 21 && Build.VERSION.SDK_INT == 21) {
                if(this.c == null) {
                    this.c = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
                }
                TintInfo tintInfo0 = this.c;
                tintInfo0.a = null;
                tintInfo0.d = false;
                tintInfo0.b = null;
                tintInfo0.c = false;
                ColorStateList colorStateList0 = ImageViewCompat.a(imageView0);
                if(colorStateList0 != null) {
                    tintInfo0.d = true;
                    tintInfo0.a = colorStateList0;
                }
                PorterDuff.Mode porterDuff$Mode0 = ImageViewCompat.b(imageView0);
                if(porterDuff$Mode0 != null) {
                    tintInfo0.c = true;
                    tintInfo0.b = porterDuff$Mode0;
                }
                if(tintInfo0.d || tintInfo0.c) {
                    AppCompatDrawableManager.e(drawable0, tintInfo0, imageView0.getDrawableState());
                    return;
                }
            }
            TintInfo tintInfo1 = this.b;
            if(tintInfo1 != null) {
                AppCompatDrawableManager.e(drawable0, tintInfo1, imageView0.getDrawableState());
            }
        }
    }

    public final void b(AttributeSet attributeSet0, int v) {
        ImageView imageView0 = this.a;
        TintTypedArray tintTypedArray0 = TintTypedArray.e(imageView0.getContext(), attributeSet0, R.styleable.f, v, 0);
        ViewCompat.B(imageView0, imageView0.getContext(), R.styleable.f, attributeSet0, tintTypedArray0.b, v);
        try {
            Drawable drawable0 = imageView0.getDrawable();
            TypedArray typedArray0 = tintTypedArray0.b;
            if(drawable0 == null) {
                int v2 = typedArray0.getResourceId(1, -1);
                if(v2 != -1) {
                    drawable0 = AppCompatResources.a(imageView0.getContext(), v2);
                    if(drawable0 != null) {
                        imageView0.setImageDrawable(drawable0);
                    }
                }
            }
            if(drawable0 != null) {
                DrawableUtils.a(drawable0);
            }
            if(typedArray0.hasValue(2)) {
                ImageViewCompat.c(imageView0, tintTypedArray0.a(2));
            }
            if(typedArray0.hasValue(3)) {
                ImageViewCompat.d(imageView0, DrawableUtils.c(typedArray0.getInt(3, -1), null));
            }
        }
        finally {
            tintTypedArray0.f();
        }
    }
}


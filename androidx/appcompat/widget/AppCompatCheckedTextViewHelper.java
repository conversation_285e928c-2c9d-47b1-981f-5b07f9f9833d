package androidx.appcompat.widget;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.widget.CheckedTextView;
import androidx.core.graphics.drawable.DrawableCompat;

class AppCompatCheckedTextViewHelper {
    public final CheckedTextView a;
    public ColorStateList b;
    public PorterDuff.Mode c;
    public boolean d;
    public boolean e;
    public boolean f;

    public AppCompatCheckedTextViewHelper(CheckedTextView checkedTextView0) {
        this.b = null;
        this.c = null;
        this.d = false;
        this.e = false;
        this.a = checkedTextView0;
    }

    public final void a() {
        CheckedTextView checkedTextView0 = this.a;
        Drawable drawable0 = checkedTextView0.getCheckMarkDrawable();
        if(drawable0 != null && (this.d || this.e)) {
            Drawable drawable1 = DrawableCompat.n(drawable0).mutate();
            if(this.d) {
                DrawableCompat.k(drawable1, this.b);
            }
            if(this.e) {
                DrawableCompat.l(drawable1, this.c);
            }
            if(drawable1.isStateful()) {
                drawable1.setState(checkedTextView0.getDrawableState());
            }
            checkedTextView0.setCheckMarkDrawable(drawable1);
        }
    }
}


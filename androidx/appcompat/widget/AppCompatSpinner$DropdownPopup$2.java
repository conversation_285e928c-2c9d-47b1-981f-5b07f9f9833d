package androidx.appcompat.widget;

import android.view.ViewTreeObserver.OnGlobalLayoutListener;

class AppCompatSpinner.DropdownPopup.2 implements ViewTreeObserver.OnGlobalLayoutListener {
    public final DropdownPopup a;

    public AppCompatSpinner.DropdownPopup.2(DropdownPopup appCompatSpinner$DropdownPopup0) {
        this.a = appCompatSpinner$DropdownPopup0;
    }

    @Override  // android.view.ViewTreeObserver$OnGlobalLayoutListener
    public final void onGlobalLayout() {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.a;
        appCompatSpinner$DropdownPopup0.getClass();
        if(AppCompatSpinner.this.isAttachedToWindow() && AppCompatSpinner.this.getGlobalVisibleRect(appCompatSpinner$DropdownPopup0.F)) {
            appCompatSpinner$DropdownPopup0.r();
            appCompatSpinner$DropdownPopup0.show();
            return;
        }
        appCompatSpinner$DropdownPopup0.dismiss();
    }
}


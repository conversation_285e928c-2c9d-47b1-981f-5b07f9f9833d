package androidx.appcompat.graphics.drawable;

import android.animation.ObjectAnimator;
import android.animation.TimeInterpolator;
import android.content.Context;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.StateSet;
import androidx.appcompat.resources.Compatibility.Api21Impl;
import androidx.appcompat.resources.R.styleable;
import androidx.appcompat.widget.ResourceManagerInternal;
import androidx.collection.LongSparseArray;
import androidx.collection.SparseArrayCompat;
import androidx.core.content.res.TypedArrayUtils;
import androidx.core.graphics.drawable.TintAwareDrawable;
import androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat;
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat;
import org.xmlpull.v1.XmlPullParserException;

public class AnimatedStateListDrawableCompat extends StateListDrawableCompat implements TintAwareDrawable {
    static class AnimatableTransition extends Transition {
        public final Animatable a;

        public AnimatableTransition(Animatable animatable0) {
            this.a = animatable0;
        }

        @Override  // androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void c() {
            this.a.start();
        }

        @Override  // androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void d() {
            this.a.stop();
        }
    }

    static class AnimatedStateListState extends StateListState {
        public LongSparseArray I;
        public SparseArrayCompat J;
        public static final int K;

        public AnimatedStateListState(AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0, AnimatedStateListDrawableCompat animatedStateListDrawableCompat0, Resources resources0) {
            super(animatedStateListDrawableCompat$AnimatedStateListState0, animatedStateListDrawableCompat0, resources0);
            this.H = animatedStateListDrawableCompat$AnimatedStateListState0 == null ? new int[this.g.length][] : animatedStateListDrawableCompat$AnimatedStateListState0.H;
            if(animatedStateListDrawableCompat$AnimatedStateListState0 != null) {
                this.I = animatedStateListDrawableCompat$AnimatedStateListState0.I;
                this.J = animatedStateListDrawableCompat$AnimatedStateListState0.J;
                return;
            }
            this.I = new LongSparseArray();
            this.J = new SparseArrayCompat();
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable() {
            return new AnimatedStateListDrawableCompat(this, null);
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable(Resources resources0) {
            return new AnimatedStateListDrawableCompat(this, resources0);
        }
    }

    static class AnimatedVectorDrawableTransition extends Transition {
        public final AnimatedVectorDrawableCompat a;

        public AnimatedVectorDrawableTransition(AnimatedVectorDrawableCompat animatedVectorDrawableCompat0) {
            this.a = animatedVectorDrawableCompat0;
        }

        @Override  // androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void c() {
            this.a.start();
        }

        @Override  // androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void d() {
            this.a.stop();
        }
    }

    static class AnimationDrawableTransition extends Transition {
        public final ObjectAnimator a;
        public final boolean b;

        public AnimationDrawableTransition(AnimationDrawable animationDrawable0, boolean z, boolean z1) {
            int v = animationDrawable0.getNumberOfFrames();
            FrameInterpolator animatedStateListDrawableCompat$FrameInterpolator0 = new FrameInterpolator();  // 初始化器: Ljava/lang/Object;-><init>()V
            int v2 = animationDrawable0.getNumberOfFrames();
            animatedStateListDrawableCompat$FrameInterpolator0.b = v2;
            if(animatedStateListDrawableCompat$FrameInterpolator0.a == null || animatedStateListDrawableCompat$FrameInterpolator0.a.length < v2) {
                animatedStateListDrawableCompat$FrameInterpolator0.a = new int[v2];
            }
            int[] arr_v = animatedStateListDrawableCompat$FrameInterpolator0.a;
            int v3 = 0;
            for(int v1 = 0; v1 < v2; ++v1) {
                int v4 = animationDrawable0.getDuration((z ? v2 - v1 - 1 : v1));
                arr_v[v1] = v4;
                v3 += v4;
            }
            animatedStateListDrawableCompat$FrameInterpolator0.c = v3;
            ObjectAnimator objectAnimator0 = ObjectAnimator.ofInt(animationDrawable0, "currentIndex", new int[]{(z ? v - 1 : 0), (z ? 0 : v - 1)});
            objectAnimator0.setAutoCancel(true);
            objectAnimator0.setDuration(((long)animatedStateListDrawableCompat$FrameInterpolator0.c));
            objectAnimator0.setInterpolator(animatedStateListDrawableCompat$FrameInterpolator0);
            this.b = z1;
            this.a = objectAnimator0;
        }

        @Override  // androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final boolean a() {
            return this.b;
        }

        @Override  // androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void b() {
            this.a.reverse();
        }

        @Override  // androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void c() {
            this.a.start();
        }

        @Override  // androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void d() {
            this.a.cancel();
        }
    }

    static class FrameInterpolator implements TimeInterpolator {
        public int[] a;
        public int b;
        public int c;

        @Override  // android.animation.TimeInterpolator
        public final float getInterpolation(float f) {
            int v = (int)(f * ((float)this.c) + 0.5f);
            int v1 = this.b;
            int[] arr_v = this.a;
            int v2;
            for(v2 = 0; v2 < v1; ++v2) {
                int v3 = arr_v[v2];
                if(v < v3) {
                    break;
                }
                v -= v3;
            }
            return v2 >= v1 ? ((float)v2) / ((float)v1) + 0.0f : ((float)v2) / ((float)v1) + ((float)v) / ((float)this.c);
        }
    }

    static abstract class Transition {
        public boolean a() {
            return false;
        }

        public void b() {
        }

        public abstract void c();

        public abstract void d();
    }

    public AnimatedStateListState p;
    public Transition q;
    public int r;
    public int s;
    public boolean t;

    public AnimatedStateListDrawableCompat(AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0, Resources resources0) {
        this.e = 0xFF;
        this.g = -1;
        this.r = -1;
        this.s = -1;
        this.d(new AnimatedStateListState(animatedStateListDrawableCompat$AnimatedStateListState0, this, resources0));
        this.onStateChange(this.getState());
        this.jumpToCurrentState();
    }

    @Override  // androidx.appcompat.graphics.drawable.DrawableContainerCompat
    public final void d(AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0) {
        this.a = animatedStateListDrawableCompat$AnimatedStateListState0;
        int v = this.g;
        if(v >= 0) {
            Drawable drawable0 = animatedStateListDrawableCompat$AnimatedStateListState0.d(v);
            this.c = drawable0;
            if(drawable0 != null) {
                this.b(drawable0);
            }
        }
        this.d = null;
        this.n = animatedStateListDrawableCompat$AnimatedStateListState0;
        this.p = animatedStateListDrawableCompat$AnimatedStateListState0;
    }

    public static AnimatedStateListDrawableCompat e(Context context0, Resources.Theme resources$Theme0, Resources resources0, AttributeSet attributeSet0, XmlResourceParser xmlResourceParser0) {
        String s = xmlResourceParser0.getName();
        if(!s.equals("animated-selector")) {
            throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": invalid animated-selector tag " + s);
        }
        AnimatedStateListDrawableCompat animatedStateListDrawableCompat0 = new AnimatedStateListDrawableCompat(null, null);
        TypedArray typedArray0 = TypedArrayUtils.e(resources0, resources$Theme0, attributeSet0, R.styleable.a);
        animatedStateListDrawableCompat0.setVisible(typedArray0.getBoolean(1, true), true);
        AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = animatedStateListDrawableCompat0.p;
        animatedStateListDrawableCompat$AnimatedStateListState0.d |= Compatibility.Api21Impl.b(typedArray0);
        animatedStateListDrawableCompat$AnimatedStateListState0.i = typedArray0.getBoolean(2, animatedStateListDrawableCompat$AnimatedStateListState0.i);
        animatedStateListDrawableCompat$AnimatedStateListState0.l = typedArray0.getBoolean(3, animatedStateListDrawableCompat$AnimatedStateListState0.l);
        animatedStateListDrawableCompat$AnimatedStateListState0.y = typedArray0.getInt(4, animatedStateListDrawableCompat$AnimatedStateListState0.y);
        animatedStateListDrawableCompat$AnimatedStateListState0.z = typedArray0.getInt(5, animatedStateListDrawableCompat$AnimatedStateListState0.z);
        animatedStateListDrawableCompat0.setDither(typedArray0.getBoolean(0, animatedStateListDrawableCompat$AnimatedStateListState0.w));
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = animatedStateListDrawableCompat0.a;
        if(resources0 == null) {
            drawableContainerCompat$DrawableContainerState0.getClass();
        }
        else {
            drawableContainerCompat$DrawableContainerState0.b = resources0;
            int v = resources0.getDisplayMetrics().densityDpi;
            if(v == 0) {
                v = 0xA0;
            }
            int v1 = drawableContainerCompat$DrawableContainerState0.c;
            drawableContainerCompat$DrawableContainerState0.c = v;
            if(v1 != v) {
                drawableContainerCompat$DrawableContainerState0.m = false;
                drawableContainerCompat$DrawableContainerState0.j = false;
            }
        }
        typedArray0.recycle();
        int v2 = xmlResourceParser0.getDepth();
        while(true) {
            int v3 = xmlResourceParser0.next();
            if(v3 == 1) {
                animatedStateListDrawableCompat0.onStateChange(animatedStateListDrawableCompat0.getState());
                return animatedStateListDrawableCompat0;
            }
            int v4 = xmlResourceParser0.getDepth();
            if(v4 < v2 + 1 && v3 == 3) {
                animatedStateListDrawableCompat0.onStateChange(animatedStateListDrawableCompat0.getState());
                return animatedStateListDrawableCompat0;
            }
            if(v3 != 2 || v4 > v2 + 1) {
            }
            else if(xmlResourceParser0.getName().equals("item")) {
                TypedArray typedArray1 = TypedArrayUtils.e(resources0, resources$Theme0, attributeSet0, R.styleable.b);
                int v5 = typedArray1.getResourceId(0, 0);
                int v6 = typedArray1.getResourceId(1, -1);
                Drawable drawable0 = v6 <= 0 ? null : ResourceManagerInternal.d().f(context0, v6);
                typedArray1.recycle();
                int v7 = attributeSet0.getAttributeCount();
                int[] arr_v = new int[v7];
                int v8 = 0;
                for(int v9 = 0; v9 < v7; ++v9) {
                    int v10 = attributeSet0.getAttributeNameResource(v9);
                    if(v10 != 0 && v10 != 0x10100D0 && v10 != 0x1010199) {
                        if(!attributeSet0.getAttributeBooleanValue(v9, false)) {
                            v10 = -v10;
                        }
                        arr_v[v8] = v10;
                        ++v8;
                    }
                }
                int[] arr_v1 = StateSet.trimStateSet(arr_v, v8);
                if(drawable0 == null) {
                alab1:
                    while(true) {
                        switch(xmlResourceParser0.next()) {
                            case 2: {
                                if(xmlResourceParser0.getName().equals("vector")) {
                                    drawable0 = new VectorDrawableCompat();
                                    ((VectorDrawableCompat)drawable0).inflate(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                                }
                                else {
                                    drawable0 = Compatibility.Api21Impl.a(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                                }
                                break alab1;
                            }
                            case 4: {
                                break;
                            }
                            default: {
                                throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <item> tag requires a \'drawable\' attribute or child tag defining a drawable");
                            }
                        }
                    }
                }
                if(drawable0 == null) {
                    throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <item> tag requires a \'drawable\' attribute or child tag defining a drawable");
                }
                AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState1 = animatedStateListDrawableCompat0.p;
                int v11 = animatedStateListDrawableCompat$AnimatedStateListState1.a(drawable0);
                animatedStateListDrawableCompat$AnimatedStateListState1.H[v11] = arr_v1;
                animatedStateListDrawableCompat$AnimatedStateListState1.J.e(v11, v5);
            }
            else if(xmlResourceParser0.getName().equals("transition")) {
                TypedArray typedArray2 = TypedArrayUtils.e(resources0, resources$Theme0, attributeSet0, R.styleable.c);
                int v12 = typedArray2.getResourceId(2, -1);
                int v13 = typedArray2.getResourceId(1, -1);
                int v14 = typedArray2.getResourceId(0, -1);
                Drawable drawable1 = v14 <= 0 ? null : ResourceManagerInternal.d().f(context0, v14);
                boolean z = typedArray2.getBoolean(3, false);
                typedArray2.recycle();
                if(drawable1 == null) {
                alab2:
                    while(true) {
                        switch(xmlResourceParser0.next()) {
                            case 2: {
                                if(xmlResourceParser0.getName().equals("animated-vector")) {
                                    drawable1 = new AnimatedVectorDrawableCompat(context0);
                                    ((AnimatedVectorDrawableCompat)drawable1).inflate(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                                }
                                else {
                                    drawable1 = Compatibility.Api21Impl.a(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                                }
                                break alab2;
                            }
                            case 4: {
                                break;
                            }
                            default: {
                                throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <transition> tag requires a \'drawable\' attribute or child tag defining a drawable");
                            }
                        }
                    }
                }
                if(drawable1 == null) {
                    throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <transition> tag requires a \'drawable\' attribute or child tag defining a drawable");
                }
                if(v12 == -1 || v13 == -1) {
                    break;
                }
                AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState2 = animatedStateListDrawableCompat0.p;
                int v15 = animatedStateListDrawableCompat$AnimatedStateListState2.a(drawable1);
                animatedStateListDrawableCompat$AnimatedStateListState2.I.a(((long)v12) << 0x20 | ((long)v13), ((long)(((long)v15) | (z ? 0x200000000L : 0L))));
                if(z) {
                    animatedStateListDrawableCompat$AnimatedStateListState2.I.a(((long)v13) << 0x20 | ((long)v12), ((long)(((long)v15) | 0x300000000L)));
                }
            }
        }
        throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <transition> tag requires \'fromId\' & \'toId\' attributes");
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean isStateful() {
        return true;
    }

    @Override  // androidx.appcompat.graphics.drawable.DrawableContainerCompat
    public final void jumpToCurrentState() {
        super.jumpToCurrentState();
        Transition animatedStateListDrawableCompat$Transition0 = this.q;
        if(animatedStateListDrawableCompat$Transition0 != null) {
            animatedStateListDrawableCompat$Transition0.d();
            this.q = null;
            this.c(this.r);
            this.r = -1;
            this.s = -1;
        }
    }

    @Override  // androidx.appcompat.graphics.drawable.StateListDrawableCompat
    public final Drawable mutate() {
        if(!this.t) {
            super.mutate();
            AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = this.p;
            animatedStateListDrawableCompat$AnimatedStateListState0.I = animatedStateListDrawableCompat$AnimatedStateListState0.I.c();
            animatedStateListDrawableCompat$AnimatedStateListState0.J = animatedStateListDrawableCompat$AnimatedStateListState0.J.b();
            this.t = true;
        }
        return this;
    }

    @Override  // androidx.appcompat.graphics.drawable.StateListDrawableCompat
    public final boolean onStateChange(int[] arr_v) {
        AnimationDrawableTransition animatedStateListDrawableCompat$AnimationDrawableTransition0;
        int v4;
        AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = this.p;
        int[][] arr2_v = animatedStateListDrawableCompat$AnimatedStateListState0.H;
        int v = animatedStateListDrawableCompat$AnimatedStateListState0.h;
        boolean z = false;
        int v1;
        for(v1 = 0; true; ++v1) {
            if(v1 >= v) {
                v1 = -1;
                break;
            }
            if(StateSet.stateSetMatches(arr2_v[v1], arr_v)) {
                break;
            }
        }
        if(v1 < 0) {
            int[] arr_v1 = StateSet.WILD_CARD;
            int[][] arr2_v1 = animatedStateListDrawableCompat$AnimatedStateListState0.H;
            int v2 = animatedStateListDrawableCompat$AnimatedStateListState0.h;
            for(v1 = 0; true; ++v1) {
                if(v1 >= v2) {
                    v1 = -1;
                    break;
                }
                if(StateSet.stateSetMatches(arr2_v1[v1], arr_v1)) {
                    break;
                }
            }
        }
        int v3 = this.g;
        if(v1 != v3) {
            Transition animatedStateListDrawableCompat$Transition0 = this.q;
            if(animatedStateListDrawableCompat$Transition0 == null) {
            label_38:
                this.q = null;
                this.s = -1;
                this.r = -1;
                AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState1 = this.p;
                if(v3 < 0) {
                    animatedStateListDrawableCompat$AnimatedStateListState1.getClass();
                    v4 = 0;
                }
                else {
                    v4 = (int)(((Integer)animatedStateListDrawableCompat$AnimatedStateListState1.J.d(v3, 0)));
                }
                int v5 = v1 >= 0 ? ((int)(((Integer)animatedStateListDrawableCompat$AnimatedStateListState1.J.d(v1, 0)))) : 0;
                if(v5 == 0 || v4 == 0) {
                label_71:
                    if(this.c(v1)) {
                        z = true;
                    }
                }
                else {
                    long v6 = ((long)v5) | ((long)v4) << 0x20;
                    int v7 = (int)(((long)(((Long)animatedStateListDrawableCompat$AnimatedStateListState1.I.e(v6, -1L)))));
                    if(v7 >= 0) {
                        boolean z1 = (((long)(((Long)animatedStateListDrawableCompat$AnimatedStateListState1.I.e(v6, -1L)))) & 0x200000000L) != 0L;
                        this.c(v7);
                        Drawable drawable0 = this.c;
                        if(drawable0 instanceof AnimationDrawable) {
                            if((((long)(((Long)animatedStateListDrawableCompat$AnimatedStateListState1.I.e(v6, -1L)))) & 0x100000000L) != 0L) {
                                z = true;
                            }
                            animatedStateListDrawableCompat$AnimationDrawableTransition0 = new AnimationDrawableTransition(((AnimationDrawable)drawable0), z, z1);
                            goto label_65;
                        }
                        else if(drawable0 instanceof AnimatedVectorDrawableCompat) {
                            animatedStateListDrawableCompat$AnimationDrawableTransition0 = new AnimatedVectorDrawableTransition(((AnimatedVectorDrawableCompat)drawable0));
                            goto label_65;
                        }
                        else {
                            if(drawable0 instanceof Animatable) {
                                animatedStateListDrawableCompat$AnimationDrawableTransition0 = new AnimatableTransition(((Animatable)drawable0));
                            label_65:
                                animatedStateListDrawableCompat$AnimationDrawableTransition0.c();
                                this.q = animatedStateListDrawableCompat$AnimationDrawableTransition0;
                                this.s = v3;
                                this.r = v1;
                                z = true;
                                goto label_73;
                            }
                            goto label_71;
                        }
                    }
                    else {
                        goto label_71;
                    }
                }
            }
            else if(v1 == this.r) {
                z = true;
            }
            else if(v1 == this.s && animatedStateListDrawableCompat$Transition0.a()) {
                animatedStateListDrawableCompat$Transition0.b();
                this.r = this.s;
                this.s = v1;
                z = true;
            }
            else {
                v3 = this.r;
                animatedStateListDrawableCompat$Transition0.d();
                goto label_38;
            }
        }
    label_73:
        Drawable drawable1 = this.c;
        return drawable1 != null ? z | drawable1.setState(arr_v) : z;
    }

    @Override  // androidx.appcompat.graphics.drawable.DrawableContainerCompat
    public final boolean setVisible(boolean z, boolean z1) {
        boolean z2 = super.setVisible(z, z1);
        Transition animatedStateListDrawableCompat$Transition0 = this.q;
        if(animatedStateListDrawableCompat$Transition0 != null && (z2 || z1)) {
            if(z) {
                animatedStateListDrawableCompat$Transition0.c();
                return z2;
            }
            this.jumpToCurrentState();
        }
        return z2;
    }
}


package androidx.appcompat.graphics.drawable;

import android.content.res.ColorStateList;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.PorterDuff.Mode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable.Callback;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.SystemClock;
import android.util.SparseArray;
import androidx.core.graphics.drawable.DrawableCompat;

public abstract class DrawableContainerCompat extends Drawable implements Drawable.Callback {
    static class BlockInvalidateCallback implements Drawable.Callback {
        public Drawable.Callback a;

        @Override  // android.graphics.drawable.Drawable$Callback
        public final void invalidateDrawable(Drawable drawable0) {
        }

        @Override  // android.graphics.drawable.Drawable$Callback
        public final void scheduleDrawable(Drawable drawable0, Runnable runnable0, long v) {
            Drawable.Callback drawable$Callback0 = this.a;
            if(drawable$Callback0 != null) {
                drawable$Callback0.scheduleDrawable(drawable0, runnable0, v);
            }
        }

        @Override  // android.graphics.drawable.Drawable$Callback
        public final void unscheduleDrawable(Drawable drawable0, Runnable runnable0) {
            Drawable.Callback drawable$Callback0 = this.a;
            if(drawable$Callback0 != null) {
                drawable$Callback0.unscheduleDrawable(drawable0, runnable0);
            }
        }
    }

    static abstract class DrawableContainerState extends Drawable.ConstantState {
        public boolean A;
        public ColorFilter B;
        public boolean C;
        public ColorStateList D;
        public PorterDuff.Mode E;
        public boolean F;
        public boolean G;
        public final DrawableContainerCompat a;
        public Resources b;
        public int c;
        public int d;
        public int e;
        public SparseArray f;
        public Drawable[] g;
        public int h;
        public boolean i;
        public boolean j;
        public Rect k;
        public boolean l;
        public boolean m;
        public int n;
        public int o;
        public int p;
        public int q;
        public boolean r;
        public int s;
        public boolean t;
        public boolean u;
        public boolean v;
        public boolean w;
        public int x;
        public int y;
        public int z;

        public DrawableContainerState(DrawableContainerState drawableContainerCompat$DrawableContainerState0, DrawableContainerCompat drawableContainerCompat0, Resources resources0) {
            Resources resources1;
            this.i = false;
            this.l = false;
            this.w = true;
            this.y = 0;
            this.z = 0;
            this.a = drawableContainerCompat0;
            Rect rect0 = null;
            if(resources0 == null) {
                resources1 = drawableContainerCompat$DrawableContainerState0 == null ? null : drawableContainerCompat$DrawableContainerState0.b;
            }
            else {
                resources1 = resources0;
            }
            this.b = resources1;
            int v1 = drawableContainerCompat$DrawableContainerState0 == null ? 0 : drawableContainerCompat$DrawableContainerState0.c;
            if(resources0 != null) {
                v1 = resources0.getDisplayMetrics().densityDpi;
            }
            if(v1 == 0) {
                v1 = 0xA0;
            }
            this.c = v1;
            if(drawableContainerCompat$DrawableContainerState0 != null) {
                this.d = drawableContainerCompat$DrawableContainerState0.d;
                this.e = drawableContainerCompat$DrawableContainerState0.e;
                this.u = true;
                this.v = true;
                this.i = drawableContainerCompat$DrawableContainerState0.i;
                this.l = drawableContainerCompat$DrawableContainerState0.l;
                this.w = drawableContainerCompat$DrawableContainerState0.w;
                this.x = drawableContainerCompat$DrawableContainerState0.x;
                this.y = drawableContainerCompat$DrawableContainerState0.y;
                this.z = drawableContainerCompat$DrawableContainerState0.z;
                this.A = drawableContainerCompat$DrawableContainerState0.A;
                this.B = drawableContainerCompat$DrawableContainerState0.B;
                this.C = drawableContainerCompat$DrawableContainerState0.C;
                this.D = drawableContainerCompat$DrawableContainerState0.D;
                this.E = drawableContainerCompat$DrawableContainerState0.E;
                this.F = drawableContainerCompat$DrawableContainerState0.F;
                this.G = drawableContainerCompat$DrawableContainerState0.G;
                if(drawableContainerCompat$DrawableContainerState0.c == v1) {
                    if(drawableContainerCompat$DrawableContainerState0.j) {
                        if(drawableContainerCompat$DrawableContainerState0.k != null) {
                            rect0 = new Rect(drawableContainerCompat$DrawableContainerState0.k);
                        }
                        this.k = rect0;
                        this.j = true;
                    }
                    if(drawableContainerCompat$DrawableContainerState0.m) {
                        this.n = drawableContainerCompat$DrawableContainerState0.n;
                        this.o = drawableContainerCompat$DrawableContainerState0.o;
                        this.p = drawableContainerCompat$DrawableContainerState0.p;
                        this.q = drawableContainerCompat$DrawableContainerState0.q;
                        this.m = true;
                    }
                }
                if(drawableContainerCompat$DrawableContainerState0.r) {
                    this.s = drawableContainerCompat$DrawableContainerState0.s;
                    this.r = true;
                }
                if(drawableContainerCompat$DrawableContainerState0.t) {
                    this.t = true;
                }
                Drawable[] arr_drawable = drawableContainerCompat$DrawableContainerState0.g;
                this.g = new Drawable[arr_drawable.length];
                this.h = drawableContainerCompat$DrawableContainerState0.h;
                SparseArray sparseArray0 = drawableContainerCompat$DrawableContainerState0.f;
                this.f = sparseArray0 == null ? new SparseArray(this.h) : sparseArray0.clone();
                int v2 = this.h;
                for(int v = 0; v < v2; ++v) {
                    Drawable drawable0 = arr_drawable[v];
                    if(drawable0 != null) {
                        Drawable.ConstantState drawable$ConstantState0 = drawable0.getConstantState();
                        if(drawable$ConstantState0 == null) {
                            this.g[v] = arr_drawable[v];
                        }
                        else {
                            this.f.put(v, drawable$ConstantState0);
                        }
                    }
                }
                return;
            }
            this.g = new Drawable[10];
            this.h = 0;
        }

        public final int a(Drawable drawable0) {
            int v = this.h;
            if(v >= this.g.length) {
                Drawable[] arr_drawable = new Drawable[v + 10];
                Drawable[] arr_drawable1 = ((StateListState)this).g;
                if(arr_drawable1 != null) {
                    System.arraycopy(arr_drawable1, 0, arr_drawable, 0, v);
                }
                ((StateListState)this).g = arr_drawable;
                int[][] arr2_v = new int[v + 10][];
                System.arraycopy(((StateListState)this).H, 0, arr2_v, 0, v);
                ((StateListState)this).H = arr2_v;
            }
            drawable0.mutate();
            drawable0.setVisible(false, true);
            drawable0.setCallback(this.a);
            this.g[v] = drawable0;
            ++this.h;
            int v1 = this.e;
            this.e = drawable0.getChangingConfigurations() | v1;
            this.r = false;
            this.t = false;
            this.k = null;
            this.j = false;
            this.m = false;
            this.u = false;
            return v;
        }

        public final void b() {
            this.m = true;
            this.c();
            int v = this.h;
            Drawable[] arr_drawable = this.g;
            this.o = -1;
            this.n = -1;
            this.q = 0;
            this.p = 0;
            for(int v1 = 0; v1 < v; ++v1) {
                Drawable drawable0 = arr_drawable[v1];
                int v2 = drawable0.getIntrinsicWidth();
                if(v2 > this.n) {
                    this.n = v2;
                }
                int v3 = drawable0.getIntrinsicHeight();
                if(v3 > this.o) {
                    this.o = v3;
                }
                int v4 = drawable0.getMinimumWidth();
                if(v4 > this.p) {
                    this.p = v4;
                }
                int v5 = drawable0.getMinimumHeight();
                if(v5 > this.q) {
                    this.q = v5;
                }
            }
        }

        public final void c() {
            SparseArray sparseArray0 = this.f;
            if(sparseArray0 != null) {
                int v = sparseArray0.size();
                for(int v1 = 0; v1 < v; ++v1) {
                    int v2 = this.f.keyAt(v1);
                    Drawable.ConstantState drawable$ConstantState0 = (Drawable.ConstantState)this.f.valueAt(v1);
                    Drawable[] arr_drawable = this.g;
                    Drawable drawable0 = drawable$ConstantState0.newDrawable(this.b);
                    if(Build.VERSION.SDK_INT >= 23) {
                        DrawableCompat.i(drawable0, this.x);
                    }
                    Drawable drawable1 = drawable0.mutate();
                    drawable1.setCallback(this.a);
                    arr_drawable[v2] = drawable1;
                }
                this.f = null;
            }
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final boolean canApplyTheme() {
            int v = this.h;
            Drawable[] arr_drawable = this.g;
            for(int v1 = 0; v1 < v; ++v1) {
                Drawable drawable0 = arr_drawable[v1];
                if(drawable0 == null) {
                    Drawable.ConstantState drawable$ConstantState0 = (Drawable.ConstantState)this.f.get(v1);
                    if(drawable$ConstantState0 != null && drawable$ConstantState0.canApplyTheme()) {
                        return true;
                    }
                }
                else if(DrawableCompat.b(drawable0)) {
                    return true;
                }
            }
            return false;
        }

        public final Drawable d(int v) {
            Drawable drawable0 = this.g[v];
            if(drawable0 != null) {
                return drawable0;
            }
            SparseArray sparseArray0 = this.f;
            if(sparseArray0 != null) {
                int v1 = sparseArray0.indexOfKey(v);
                if(v1 >= 0) {
                    Drawable drawable1 = ((Drawable.ConstantState)this.f.valueAt(v1)).newDrawable(this.b);
                    if(Build.VERSION.SDK_INT >= 23) {
                        DrawableCompat.i(drawable1, this.x);
                    }
                    Drawable drawable2 = drawable1.mutate();
                    drawable2.setCallback(this.a);
                    this.g[v] = drawable2;
                    this.f.removeAt(v1);
                    if(this.f.size() == 0) {
                        this.f = null;
                    }
                    return drawable2;
                }
            }
            return null;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final int getChangingConfigurations() {
            return this.d | this.e;
        }
    }

    public DrawableContainerState a;
    public Rect b;
    public Drawable c;
    public Drawable d;
    public int e;
    public boolean f;
    public int g;
    public boolean h;
    public Runnable i;
    public long j;
    public long k;
    public BlockInvalidateCallback l;
    public static final int m;

    public final void a(boolean z) {
        int v3;
        int v = 1;
        this.f = true;
        long v1 = SystemClock.uptimeMillis();
        Drawable drawable0 = this.c;
        if(drawable0 == null) {
            this.j = 0L;
            v3 = 0;
        }
        else {
            long v2 = this.j;
            if(v2 == 0L) {
                v3 = 0;
            }
            else if(v2 <= v1) {
                drawable0.setAlpha(this.e);
                this.j = 0L;
                v3 = 0;
            }
            else {
                drawable0.setAlpha((0xFF - ((int)((v2 - v1) * 0xFFL)) / this.a.y) * this.e / 0xFF);
                v3 = 1;
            }
        }
        Drawable drawable1 = this.d;
        if(drawable1 == null) {
            this.k = 0L;
            v = v3;
        }
        else {
            long v4 = this.k;
            if(v4 == 0L) {
                v = v3;
            }
            else if(v4 <= v1) {
                drawable1.setVisible(false, false);
                this.d = null;
                this.k = 0L;
                v = v3;
            }
            else {
                drawable1.setAlpha(((int)((v4 - v1) * 0xFFL)) / this.a.z * this.e / 0xFF);
            }
        }
        if(z && v != 0) {
            this.scheduleSelf(this.i, v1 + 16L);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void applyTheme(Resources.Theme resources$Theme0) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(resources$Theme0 == null) {
            drawableContainerCompat$DrawableContainerState0.getClass();
        }
        else {
            drawableContainerCompat$DrawableContainerState0.c();
            int v = drawableContainerCompat$DrawableContainerState0.h;
            Drawable[] arr_drawable = drawableContainerCompat$DrawableContainerState0.g;
            for(int v1 = 0; v1 < v; ++v1) {
                Drawable drawable0 = arr_drawable[v1];
                if(drawable0 != null && DrawableCompat.b(drawable0)) {
                    DrawableCompat.a(arr_drawable[v1], resources$Theme0);
                    drawableContainerCompat$DrawableContainerState0.e |= arr_drawable[v1].getChangingConfigurations();
                }
            }
            Resources resources0 = resources$Theme0.getResources();
            if(resources0 != null) {
                drawableContainerCompat$DrawableContainerState0.b = resources0;
                int v2 = resources0.getDisplayMetrics().densityDpi;
                if(v2 == 0) {
                    v2 = 0xA0;
                }
                int v3 = drawableContainerCompat$DrawableContainerState0.c;
                drawableContainerCompat$DrawableContainerState0.c = v2;
                if(v3 != v2) {
                    drawableContainerCompat$DrawableContainerState0.m = false;
                    drawableContainerCompat$DrawableContainerState0.j = false;
                }
            }
        }
    }

    public final void b(Drawable drawable0) {
        if(this.l == null) {
            this.l = new BlockInvalidateCallback();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        BlockInvalidateCallback drawableContainerCompat$BlockInvalidateCallback0 = this.l;
        drawableContainerCompat$BlockInvalidateCallback0.a = drawable0.getCallback();
        drawable0.setCallback(drawableContainerCompat$BlockInvalidateCallback0);
        try {
            if(this.a.y <= 0 && this.f) {
                drawable0.setAlpha(this.e);
            }
            DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
            if(drawableContainerCompat$DrawableContainerState0.C) {
                drawable0.setColorFilter(drawableContainerCompat$DrawableContainerState0.B);
            }
            else {
                if(drawableContainerCompat$DrawableContainerState0.F) {
                    DrawableCompat.k(drawable0, drawableContainerCompat$DrawableContainerState0.D);
                }
                DrawableContainerState drawableContainerCompat$DrawableContainerState1 = this.a;
                if(drawableContainerCompat$DrawableContainerState1.G) {
                    DrawableCompat.l(drawable0, drawableContainerCompat$DrawableContainerState1.E);
                }
            }
            drawable0.setVisible(this.isVisible(), true);
            drawable0.setDither(this.a.w);
            drawable0.setState(this.getState());
            drawable0.setLevel(this.getLevel());
            drawable0.setBounds(this.getBounds());
            if(Build.VERSION.SDK_INT >= 23) {
                DrawableCompat.i(drawable0, DrawableCompat.e(this));
            }
            drawable0.setAutoMirrored(this.a.A);
            Rect rect0 = this.b;
            if(rect0 != null) {
                DrawableCompat.h(drawable0, rect0.left, rect0.top, rect0.right, rect0.bottom);
            }
        }
        finally {
            Drawable.Callback drawable$Callback0 = this.l.a;
            this.l.a = null;
            drawable0.setCallback(drawable$Callback0);
        }
    }

    public final boolean c(int v) {
        if(v == this.g) {
            return false;
        }
        long v1 = SystemClock.uptimeMillis();
        if(this.a.z > 0) {
            Drawable drawable0 = this.d;
            if(drawable0 != null) {
                drawable0.setVisible(false, false);
            }
            Drawable drawable1 = this.c;
            if(drawable1 == null) {
                this.d = null;
                this.k = 0L;
            }
            else {
                this.d = drawable1;
                this.k = ((long)this.a.z) + v1;
            }
        }
        else {
            Drawable drawable2 = this.c;
            if(drawable2 != null) {
                drawable2.setVisible(false, false);
            }
        }
        if(v >= 0) {
            DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
            if(v < drawableContainerCompat$DrawableContainerState0.h) {
                Drawable drawable3 = drawableContainerCompat$DrawableContainerState0.d(v);
                this.c = drawable3;
                this.g = v;
                if(drawable3 != null) {
                    int v2 = this.a.y;
                    if(v2 > 0) {
                        this.j = v1 + ((long)v2);
                    }
                    this.b(drawable3);
                }
            }
            else {
                this.c = null;
                this.g = -1;
            }
        }
        else {
            this.c = null;
            this.g = -1;
        }
        if(this.j != 0L || this.k != 0L) {
            Runnable runnable0 = this.i;
            if(runnable0 == null) {
                this.i = new DrawableContainerCompat.1(((AnimatedStateListDrawableCompat)this));
            }
            else {
                this.unscheduleSelf(runnable0);
            }
            this.a(true);
        }
        this.invalidateSelf();
        return true;
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean canApplyTheme() {
        return this.a.canApplyTheme();
    }

    public abstract void d(AnimatedStateListState arg1);

    @Override  // android.graphics.drawable.Drawable
    public final void draw(Canvas canvas0) {
        Drawable drawable0 = this.c;
        if(drawable0 != null) {
            drawable0.draw(canvas0);
        }
        Drawable drawable1 = this.d;
        if(drawable1 != null) {
            drawable1.draw(canvas0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getAlpha() {
        return this.e;
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getChangingConfigurations() {
        return super.getChangingConfigurations() | this.a.getChangingConfigurations();
    }

    @Override  // android.graphics.drawable.Drawable
    public final Drawable.ConstantState getConstantState() {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(!drawableContainerCompat$DrawableContainerState0.u) {
            drawableContainerCompat$DrawableContainerState0.c();
            drawableContainerCompat$DrawableContainerState0.u = true;
            int v = drawableContainerCompat$DrawableContainerState0.h;
            Drawable[] arr_drawable = drawableContainerCompat$DrawableContainerState0.g;
            for(int v1 = 0; v1 < v; ++v1) {
                if(arr_drawable[v1].getConstantState() == null) {
                    drawableContainerCompat$DrawableContainerState0.v = false;
                    return null;
                }
            }
            drawableContainerCompat$DrawableContainerState0.v = true;
        }
        else if(!drawableContainerCompat$DrawableContainerState0.v) {
            return null;
        }
        DrawableContainerState drawableContainerCompat$DrawableContainerState1 = this.a;
        drawableContainerCompat$DrawableContainerState1.d = this.getChangingConfigurations();
        return this.a;
    }

    @Override  // android.graphics.drawable.Drawable
    public final Drawable getCurrent() {
        return this.c;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void getHotspotBounds(Rect rect0) {
        Rect rect1 = this.b;
        if(rect1 != null) {
            rect0.set(rect1);
            return;
        }
        super.getHotspotBounds(rect0);
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getIntrinsicHeight() {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(drawableContainerCompat$DrawableContainerState0.l) {
            if(!drawableContainerCompat$DrawableContainerState0.m) {
                drawableContainerCompat$DrawableContainerState0.b();
            }
            return drawableContainerCompat$DrawableContainerState0.o;
        }
        return this.c == null ? -1 : this.c.getIntrinsicHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getIntrinsicWidth() {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(drawableContainerCompat$DrawableContainerState0.l) {
            if(!drawableContainerCompat$DrawableContainerState0.m) {
                drawableContainerCompat$DrawableContainerState0.b();
            }
            return drawableContainerCompat$DrawableContainerState0.n;
        }
        return this.c == null ? -1 : this.c.getIntrinsicWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getMinimumHeight() {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(drawableContainerCompat$DrawableContainerState0.l) {
            if(!drawableContainerCompat$DrawableContainerState0.m) {
                drawableContainerCompat$DrawableContainerState0.b();
            }
            return drawableContainerCompat$DrawableContainerState0.q;
        }
        return this.c == null ? 0 : this.c.getMinimumHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getMinimumWidth() {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(drawableContainerCompat$DrawableContainerState0.l) {
            if(!drawableContainerCompat$DrawableContainerState0.m) {
                drawableContainerCompat$DrawableContainerState0.b();
            }
            return drawableContainerCompat$DrawableContainerState0.p;
        }
        return this.c == null ? 0 : this.c.getMinimumWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getOpacity() {
        int v = -2;
        if(this.c != null && this.c.isVisible()) {
            DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
            if(drawableContainerCompat$DrawableContainerState0.r) {
                return drawableContainerCompat$DrawableContainerState0.s;
            }
            drawableContainerCompat$DrawableContainerState0.c();
            int v1 = drawableContainerCompat$DrawableContainerState0.h;
            Drawable[] arr_drawable = drawableContainerCompat$DrawableContainerState0.g;
            if(v1 > 0) {
                v = arr_drawable[0].getOpacity();
            }
            for(int v2 = 1; v2 < v1; ++v2) {
                v = Drawable.resolveOpacity(v, arr_drawable[v2].getOpacity());
            }
            drawableContainerCompat$DrawableContainerState0.s = v;
            drawableContainerCompat$DrawableContainerState0.r = true;
        }
        return v;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void getOutline(Outline outline0) {
        Drawable drawable0 = this.c;
        if(drawable0 != null) {
            drawable0.getOutline(outline0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean getPadding(Rect rect0) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        boolean z = false;
        Rect rect1 = null;
        if(!drawableContainerCompat$DrawableContainerState0.i) {
            Rect rect2 = drawableContainerCompat$DrawableContainerState0.k;
            if(rect2 != null || drawableContainerCompat$DrawableContainerState0.j) {
                rect1 = rect2;
            }
            else {
                drawableContainerCompat$DrawableContainerState0.c();
                Rect rect3 = new Rect();
                int v = drawableContainerCompat$DrawableContainerState0.h;
                Drawable[] arr_drawable = drawableContainerCompat$DrawableContainerState0.g;
                for(int v1 = 0; v1 < v; ++v1) {
                    if(arr_drawable[v1].getPadding(rect3)) {
                        if(rect1 == null) {
                            rect1 = new Rect(0, 0, 0, 0);
                        }
                        int v2 = rect3.left;
                        if(v2 > rect1.left) {
                            rect1.left = v2;
                        }
                        int v3 = rect3.top;
                        if(v3 > rect1.top) {
                            rect1.top = v3;
                        }
                        int v4 = rect3.right;
                        if(v4 > rect1.right) {
                            rect1.right = v4;
                        }
                        int v5 = rect3.bottom;
                        if(v5 > rect1.bottom) {
                            rect1.bottom = v5;
                        }
                    }
                }
                drawableContainerCompat$DrawableContainerState0.j = true;
                drawableContainerCompat$DrawableContainerState0.k = rect1;
            }
        }
        if(rect1 == null) {
            Drawable drawable0 = this.c;
            z = drawable0 == null ? super.getPadding(rect0) : drawable0.getPadding(rect0);
        }
        else {
            rect0.set(rect1);
            if((rect1.left | rect1.top | rect1.bottom | rect1.right) != 0) {
                z = true;
            }
        }
        if(this.a.A && DrawableCompat.e(this) == 1) {
            int v6 = rect0.left;
            rect0.left = rect0.right;
            rect0.right = v6;
        }
        return z;
    }

    @Override  // android.graphics.drawable.Drawable$Callback
    public final void invalidateDrawable(Drawable drawable0) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(drawableContainerCompat$DrawableContainerState0 != null) {
            drawableContainerCompat$DrawableContainerState0.r = false;
            drawableContainerCompat$DrawableContainerState0.t = false;
        }
        if(drawable0 == this.c && this.getCallback() != null) {
            this.getCallback().invalidateDrawable(this);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean isAutoMirrored() {
        return this.a.A;
    }

    @Override  // android.graphics.drawable.Drawable
    public void jumpToCurrentState() {
        int v1;
        Drawable drawable0 = this.d;
        int v = 1;
        if(drawable0 == null) {
            v1 = 0;
        }
        else {
            drawable0.jumpToCurrentState();
            this.d = null;
            v1 = 1;
        }
        Drawable drawable1 = this.c;
        if(drawable1 != null) {
            drawable1.jumpToCurrentState();
            if(this.f) {
                this.c.setAlpha(this.e);
            }
        }
        if(this.k == 0L) {
            v = v1;
        }
        else {
            this.k = 0L;
        }
        if(this.j != 0L) {
            this.j = 0L;
            this.invalidateSelf();
            return;
        }
        if(v != 0) {
            this.invalidateSelf();
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public Drawable mutate() {
        if(!this.h && super.mutate() == this) {
            AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = new AnimatedStateListState(((AnimatedStateListDrawableCompat)this).p, ((AnimatedStateListDrawableCompat)this), null);
            animatedStateListDrawableCompat$AnimatedStateListState0.I = animatedStateListDrawableCompat$AnimatedStateListState0.I.c();
            animatedStateListDrawableCompat$AnimatedStateListState0.J = animatedStateListDrawableCompat$AnimatedStateListState0.J.b();
            this.d(animatedStateListDrawableCompat$AnimatedStateListState0);
            this.h = true;
        }
        return this;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void onBoundsChange(Rect rect0) {
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            drawable0.setBounds(rect0);
        }
        Drawable drawable1 = this.c;
        if(drawable1 != null) {
            drawable1.setBounds(rect0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean onLayoutDirectionChanged(int v) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        int v1 = this.g;
        int v2 = drawableContainerCompat$DrawableContainerState0.h;
        Drawable[] arr_drawable = drawableContainerCompat$DrawableContainerState0.g;
        boolean z = false;
        for(int v3 = 0; v3 < v2; ++v3) {
            Drawable drawable0 = arr_drawable[v3];
            if(drawable0 != null) {
                boolean z1 = Build.VERSION.SDK_INT < 23 ? false : DrawableCompat.i(drawable0, v);
                if(v3 == v1) {
                    z = z1;
                }
            }
        }
        drawableContainerCompat$DrawableContainerState0.x = v;
        return z;
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean onLevelChange(int v) {
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            return drawable0.setLevel(v);
        }
        return this.c == null ? false : this.c.setLevel(v);
    }

    @Override  // android.graphics.drawable.Drawable$Callback
    public final void scheduleDrawable(Drawable drawable0, Runnable runnable0, long v) {
        if(drawable0 == this.c && this.getCallback() != null) {
            this.getCallback().scheduleDrawable(this, runnable0, v);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setAlpha(int v) {
        if(!this.f || this.e != v) {
            this.f = true;
            this.e = v;
            Drawable drawable0 = this.c;
            if(drawable0 != null) {
                if(this.j == 0L) {
                    drawable0.setAlpha(v);
                    return;
                }
                this.a(false);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setAutoMirrored(boolean z) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(drawableContainerCompat$DrawableContainerState0.A != z) {
            drawableContainerCompat$DrawableContainerState0.A = z;
            Drawable drawable0 = this.c;
            if(drawable0 != null) {
                drawable0.setAutoMirrored(z);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setColorFilter(ColorFilter colorFilter0) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        drawableContainerCompat$DrawableContainerState0.C = true;
        if(drawableContainerCompat$DrawableContainerState0.B != colorFilter0) {
            drawableContainerCompat$DrawableContainerState0.B = colorFilter0;
            Drawable drawable0 = this.c;
            if(drawable0 != null) {
                drawable0.setColorFilter(colorFilter0);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setDither(boolean z) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        if(drawableContainerCompat$DrawableContainerState0.w != z) {
            drawableContainerCompat$DrawableContainerState0.w = z;
            Drawable drawable0 = this.c;
            if(drawable0 != null) {
                drawable0.setDither(z);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setHotspot(float f, float f1) {
        Drawable drawable0 = this.c;
        if(drawable0 != null) {
            DrawableCompat.g(drawable0, f, f1);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setHotspotBounds(int v, int v1, int v2, int v3) {
        Rect rect0 = this.b;
        if(rect0 == null) {
            this.b = new Rect(v, v1, v2, v3);
        }
        else {
            rect0.set(v, v1, v2, v3);
        }
        Drawable drawable0 = this.c;
        if(drawable0 != null) {
            DrawableCompat.h(drawable0, v, v1, v2, v3);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setTint(int v) {
        this.setTintList(ColorStateList.valueOf(v));
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setTintList(ColorStateList colorStateList0) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        drawableContainerCompat$DrawableContainerState0.F = true;
        if(drawableContainerCompat$DrawableContainerState0.D != colorStateList0) {
            drawableContainerCompat$DrawableContainerState0.D = colorStateList0;
            DrawableCompat.k(this.c, colorStateList0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setTintMode(PorterDuff.Mode porterDuff$Mode0) {
        DrawableContainerState drawableContainerCompat$DrawableContainerState0 = this.a;
        drawableContainerCompat$DrawableContainerState0.G = true;
        if(drawableContainerCompat$DrawableContainerState0.E != porterDuff$Mode0) {
            drawableContainerCompat$DrawableContainerState0.E = porterDuff$Mode0;
            DrawableCompat.l(this.c, porterDuff$Mode0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public boolean setVisible(boolean z, boolean z1) {
        boolean z2 = super.setVisible(z, z1);
        Drawable drawable0 = this.d;
        if(drawable0 != null) {
            drawable0.setVisible(z, z1);
        }
        Drawable drawable1 = this.c;
        if(drawable1 != null) {
            drawable1.setVisible(z, z1);
        }
        return z2;
    }

    @Override  // android.graphics.drawable.Drawable$Callback
    public final void unscheduleDrawable(Drawable drawable0, Runnable runnable0) {
        if(drawable0 == this.c && this.getCallback() != null) {
            this.getCallback().unscheduleDrawable(this, runnable0);
        }
    }
}


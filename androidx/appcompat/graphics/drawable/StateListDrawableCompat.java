package androidx.appcompat.graphics.drawable;

import android.content.res.Resources.Theme;
import android.graphics.drawable.Drawable;

public abstract class StateListDrawableCompat extends DrawableContainerCompat {
    static abstract class StateListState extends DrawableContainerState {
        public int[][] H;

    }

    public StateListState n;
    public boolean o;

    @Override  // androidx.appcompat.graphics.drawable.DrawableContainerCompat
    public final void applyTheme(Resources.Theme resources$Theme0) {
        super.applyTheme(resources$Theme0);
        this.onStateChange(this.getState());
    }

    @Override  // androidx.appcompat.graphics.drawable.DrawableContainerCompat
    public Drawable mutate() {
        if(!this.o) {
            super.mutate();
            AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = (AnimatedStateListState)this.n;
            animatedStateListDrawableCompat$AnimatedStateListState0.I = animatedStateListDrawableCompat$AnimatedStateListState0.I.c();
            animatedStateListDrawableCompat$AnimatedStateListState0.J = animatedStateListDrawableCompat$AnimatedStateListState0.J.b();
            this.o = true;
        }
        return this;
    }

    @Override  // android.graphics.drawable.Drawable
    public abstract boolean onStateChange(int[] arg1);
}


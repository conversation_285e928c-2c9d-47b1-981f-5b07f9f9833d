package androidx.appcompat.app;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup.MarginLayoutParams;
import android.widget.FrameLayout.LayoutParams;
import androidx.appcompat.widget.ViewUtils;
import androidx.core.content.ContextCompat;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

class AppCompatDelegateImpl.3 implements OnApplyWindowInsetsListener {
    public final AppCompatDelegateImpl a;

    public AppCompatDelegateImpl.3(AppCompatDelegateImpl appCompatDelegateImpl0) {
        this.a = appCompatDelegateImpl0;
    }

    @Override  // androidx.core.view.OnApplyWindowInsetsListener
    public final WindowInsetsCompat a(View view0, WindowInsetsCompat windowInsetsCompat0) {
        int v12;
        int v9;
        int v = windowInsetsCompat0.h();
        AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
        appCompatDelegateImpl0.getClass();
        int v1 = windowInsetsCompat0.h();
        int v2 = 0;
        if(appCompatDelegateImpl0.v == null || !(appCompatDelegateImpl0.v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams)) {
            v12 = 0;
        }
        else {
            ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)appCompatDelegateImpl0.v.getLayoutParams();
            int v3 = 1;
            if(appCompatDelegateImpl0.v.isShown()) {
                if(appCompatDelegateImpl0.p0 == null) {
                    appCompatDelegateImpl0.p0 = new Rect();
                    appCompatDelegateImpl0.q0 = new Rect();
                }
                Rect rect0 = appCompatDelegateImpl0.p0;
                Rect rect1 = appCompatDelegateImpl0.q0;
                rect0.set(windowInsetsCompat0.f(), windowInsetsCompat0.h(), windowInsetsCompat0.g(), windowInsetsCompat0.e());
                ViewUtils.a(appCompatDelegateImpl0.A, rect0, rect1);
                int v4 = rect0.top;
                int v5 = rect0.left;
                int v6 = rect0.right;
                WindowInsetsCompat windowInsetsCompat1 = ViewCompat.o(appCompatDelegateImpl0.A);
                int v7 = windowInsetsCompat1 == null ? 0 : windowInsetsCompat1.f();
                int v8 = windowInsetsCompat1 == null ? 0 : windowInsetsCompat1.g();
                if(viewGroup$MarginLayoutParams0.topMargin != v4 || viewGroup$MarginLayoutParams0.leftMargin != v5 || viewGroup$MarginLayoutParams0.rightMargin != v6) {
                    viewGroup$MarginLayoutParams0.topMargin = v4;
                    viewGroup$MarginLayoutParams0.leftMargin = v5;
                    viewGroup$MarginLayoutParams0.rightMargin = v6;
                    v9 = 1;
                }
                else {
                    v9 = 0;
                }
                Context context0 = appCompatDelegateImpl0.k;
                if(v4 <= 0 || appCompatDelegateImpl0.C != null) {
                    View view2 = appCompatDelegateImpl0.C;
                    if(view2 != null) {
                        ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams1 = (ViewGroup.MarginLayoutParams)view2.getLayoutParams();
                        int v10 = viewGroup$MarginLayoutParams0.topMargin;
                        if(viewGroup$MarginLayoutParams1.height != v10 || viewGroup$MarginLayoutParams1.leftMargin != v7 || viewGroup$MarginLayoutParams1.rightMargin != v8) {
                            viewGroup$MarginLayoutParams1.height = v10;
                            viewGroup$MarginLayoutParams1.leftMargin = v7;
                            viewGroup$MarginLayoutParams1.rightMargin = v8;
                            appCompatDelegateImpl0.C.setLayoutParams(viewGroup$MarginLayoutParams1);
                        }
                    }
                }
                else {
                    View view1 = new View(context0);
                    appCompatDelegateImpl0.C = view1;
                    view1.setVisibility(8);
                    FrameLayout.LayoutParams frameLayout$LayoutParams0 = new FrameLayout.LayoutParams(-1, viewGroup$MarginLayoutParams0.topMargin, 51);
                    frameLayout$LayoutParams0.leftMargin = v7;
                    frameLayout$LayoutParams0.rightMargin = v8;
                    appCompatDelegateImpl0.A.addView(appCompatDelegateImpl0.C, -1, frameLayout$LayoutParams0);
                }
                View view3 = appCompatDelegateImpl0.C;
                if(view3 == null) {
                    v3 = 0;
                }
                else if(view3.getVisibility() != 0) {
                    appCompatDelegateImpl0.C.setBackgroundColor(((appCompatDelegateImpl0.C.getWindowSystemUiVisibility() & 0x2000) == 0 ? ContextCompat.getColor(context0, 0x7F060005) : ContextCompat.getColor(context0, 0x7F060006)));  // color:abc_decor_view_status_guard
                }
                if(!appCompatDelegateImpl0.H && v3 != 0) {
                    v1 = 0;
                }
                int v11 = v3;
                v3 = v9;
                v12 = v11;
            }
            else if(viewGroup$MarginLayoutParams0.topMargin == 0) {
                v12 = 0;
                v3 = 0;
            }
            else {
                viewGroup$MarginLayoutParams0.topMargin = 0;
                v12 = 0;
            }
            if(v3 != 0) {
                appCompatDelegateImpl0.v.setLayoutParams(viewGroup$MarginLayoutParams0);
            }
        }
        View view4 = appCompatDelegateImpl0.C;
        if(view4 != null) {
            if(v12 == 0) {
                v2 = 8;
            }
            view4.setVisibility(v2);
        }
        return v == v1 ? ViewCompat.w(view0, windowInsetsCompat0) : ViewCompat.w(view0, windowInsetsCompat0.l(windowInsetsCompat0.f(), v1, windowInsetsCompat0.g(), windowInsetsCompat0.e()));
    }
}


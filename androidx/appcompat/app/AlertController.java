package androidx.appcompat.app;

import android.content.Context;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface.OnKeyListener;
import android.content.DialogInterface.OnMultiChoiceClickListener;
import android.content.DialogInterface;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View.OnClickListener;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewStub;
import android.view.Window;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.R.styleable;
import androidx.core.widget.NestedScrollView;
import java.lang.ref.WeakReference;

class AlertController {
    public static class AlertParams {
        public final Context a;
        public final LayoutInflater b;
        public Drawable c;
        public CharSequence d;
        public View e;
        public CharSequence f;
        public CharSequence g;
        public DialogInterface.OnClickListener h;
        public CharSequence i;
        public DialogInterface.OnClickListener j;
        public boolean k;
        public DialogInterface.OnKeyListener l;
        public CharSequence[] m;
        public ListAdapter n;
        public DialogInterface.OnClickListener o;
        public View p;
        public boolean[] q;
        public boolean r;
        public boolean s;
        public int t;
        public DialogInterface.OnMultiChoiceClickListener u;

        public AlertParams(ContextThemeWrapper contextThemeWrapper0) {
            this.t = -1;
            this.a = contextThemeWrapper0;
            this.k = true;
            this.b = (LayoutInflater)contextThemeWrapper0.getSystemService("layout_inflater");
        }
    }

    static final class ButtonHandler extends Handler {
        public WeakReference a;

        @Override  // android.os.Handler
        public final void handleMessage(Message message0) {
            switch(message0.what) {
                case -3: 
                case -2: 
                case -1: {
                    ((DialogInterface.OnClickListener)message0.obj).onClick(((DialogInterface)this.a.get()), message0.what);
                    return;
                }
                case 1: {
                    ((DialogInterface)message0.obj).dismiss();
                }
            }
        }
    }

    static class CheckedItemAdapter extends ArrayAdapter {
        @Override  // android.widget.ArrayAdapter
        public final long getItemId(int v) {
            return (long)v;
        }

        @Override  // android.widget.BaseAdapter, android.widget.Adapter
        public final boolean hasStableIds() {
            return true;
        }
    }

    public static class RecycleListView extends ListView {
        public final int a;
        public final int b;

        public RecycleListView(Context context0, AttributeSet attributeSet0) {
            super(context0, attributeSet0);
            TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.t);
            this.b = typedArray0.getDimensionPixelOffset(0, -1);
            this.a = typedArray0.getDimensionPixelOffset(1, -1);
        }
    }

    public TextView A;
    public TextView B;
    public View C;
    public ListAdapter D;
    public int E;
    public final int F;
    public final int G;
    public final int H;
    public final int I;
    public final int J;
    public final boolean K;
    public final Handler L;
    public final View.OnClickListener M;
    public final Context a;
    public final AppCompatDialog b;
    public final Window c;
    public final int d;
    public CharSequence e;
    public CharSequence f;
    public RecycleListView g;
    public View h;
    public int i;
    public boolean j;
    public Button k;
    public CharSequence l;
    public Message m;
    public Drawable n;
    public Button o;
    public CharSequence p;
    public Message q;
    public Drawable r;
    public Button s;
    public CharSequence t;
    public Message u;
    public Drawable v;
    public NestedScrollView w;
    public int x;
    public Drawable y;
    public ImageView z;

    public AlertController(Context context0, AppCompatDialog appCompatDialog0, Window window0) {
        this.j = false;
        this.x = 0;
        this.E = -1;
        this.M = new View.OnClickListener() {
            public final AlertController a;

            {
                this.a = alertController0;
            }

            @Override  // android.view.View$OnClickListener
            public final void onClick(View view0) {
                Message message1;
                AlertController alertController0 = this.a;
                if(view0 == alertController0.k) {
                    Message message0 = alertController0.m;
                    if(message0 != null) {
                        message1 = Message.obtain(message0);
                        goto label_19;
                    }
                    goto label_6;
                }
                else {
                label_6:
                    if(view0 == alertController0.o) {
                        Message message2 = alertController0.q;
                        if(message2 != null) {
                            message1 = Message.obtain(message2);
                            goto label_19;
                        }
                        goto label_11;
                    }
                    else {
                    label_11:
                        if(view0 == alertController0.s) {
                            Message message3 = alertController0.u;
                            message1 = message3 == null ? null : Message.obtain(message3);
                        }
                        else {
                            message1 = null;
                        }
                    }
                }
            label_19:
                if(message1 != null) {
                    message1.sendToTarget();
                }
                alertController0.L.obtainMessage(1, alertController0.b).sendToTarget();
            }
        };
        this.a = context0;
        this.b = appCompatDialog0;
        this.c = window0;
        ButtonHandler alertController$ButtonHandler0 = new ButtonHandler();  // 初始化器: Landroid/os/Handler;-><init>()V
        alertController$ButtonHandler0.a = new WeakReference(appCompatDialog0);
        this.L = alertController$ButtonHandler0;
        TypedArray typedArray0 = context0.obtainStyledAttributes(null, R.styleable.e, 0x7F040038, 0);  // attr:alertDialogStyle
        this.F = typedArray0.getResourceId(0, 0);
        typedArray0.getResourceId(2, 0);
        this.G = typedArray0.getResourceId(4, 0);
        this.H = typedArray0.getResourceId(5, 0);
        this.I = typedArray0.getResourceId(7, 0);
        this.J = typedArray0.getResourceId(3, 0);
        this.K = typedArray0.getBoolean(6, true);
        this.d = typedArray0.getDimensionPixelSize(1, 0);
        typedArray0.recycle();
        appCompatDialog0.d().h(1);
    }

    public static boolean a(View view0) {
        if(view0.onCheckIsTextEditor()) {
            return true;
        }
        if(!(view0 instanceof ViewGroup)) {
            return false;
        }
        int v = ((ViewGroup)view0).getChildCount();
        while(v > 0) {
            --v;
            if(AlertController.a(((ViewGroup)view0).getChildAt(v))) {
                return true;
            }
            if(false) {
                break;
            }
        }
        return false;
    }

    public static void b(View view0, View view1, View view2) {
        int v = 4;
        if(view1 != null) {
            view1.setVisibility((view0.canScrollVertically(-1) ? 0 : 4));
        }
        if(view2 != null) {
            if(view0.canScrollVertically(1)) {
                v = 0;
            }
            view2.setVisibility(v);
        }
    }

    public static ViewGroup c(View view0, View view1) {
        if(view0 == null) {
            if(view1 instanceof ViewStub) {
                view1 = ((ViewStub)view1).inflate();
            }
            return (ViewGroup)view1;
        }
        if(view1 != null) {
            ViewParent viewParent0 = view1.getParent();
            if(viewParent0 instanceof ViewGroup) {
                ((ViewGroup)viewParent0).removeView(view1);
            }
        }
        if(view0 instanceof ViewStub) {
            view0 = ((ViewStub)view0).inflate();
        }
        return (ViewGroup)view0;
    }

    public final void d(int v, CharSequence charSequence0, DialogInterface.OnClickListener dialogInterface$OnClickListener0) {
        Message message0 = dialogInterface$OnClickListener0 == null ? null : this.L.obtainMessage(v, dialogInterface$OnClickListener0);
        switch(v) {
            case -3: {
                this.t = charSequence0;
                this.u = message0;
                this.v = null;
                return;
            }
            case -2: {
                this.p = charSequence0;
                this.q = message0;
                this.r = null;
                return;
            }
            case -1: {
                this.l = charSequence0;
                this.m = message0;
                this.n = null;
                return;
            }
            default: {
                throw new IllegalArgumentException("Button does not exist");
            }
        }
    }
}


package androidx.appcompat.app;

import android.app.LocaleManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Bundle;
import android.os.LocaleList;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import androidx.appcompat.view.ActionMode.Callback;
import androidx.appcompat.view.ActionMode;
import androidx.collection.ArraySet;
import androidx.core.os.LocaleListCompat;
import java.lang.ref.WeakReference;
import java.util.ArrayDeque;
import java.util.Iterator;
import java.util.concurrent.Executor;

public abstract class AppCompatDelegate {
    static abstract class Api24Impl {
        public static LocaleList a(String s) {
            return LocaleList.forLanguageTags(s);
        }
    }

    static abstract class Api33Impl {
        public static LocaleList a(Object object0) {
            return ((LocaleManager)object0).getApplicationLocales();
        }

        public static void b(Object object0, LocaleList localeList0) {
            ((LocaleManager)object0).setApplicationLocales(localeList0);
        }
    }

    static class SerialExecutor implements Executor {
        public final Object a;
        public final ArrayDeque b;
        public final Executor c;
        public Runnable d;

        public SerialExecutor(Executor executor0) {
            this.a = new Object();
            this.b = new ArrayDeque();
            this.c = executor0;
        }

        public final void a() {
            synchronized(this.a) {
                Runnable runnable0 = (Runnable)this.b.poll();
                this.d = runnable0;
                if(runnable0 != null) {
                    this.c.execute(runnable0);
                }
            }
        }

        @Override
        public final void execute(Runnable runnable0) {
            synchronized(this.a) {
                b b0 = new b(this, runnable0);
                this.b.add(b0);
                if(this.d == null) {
                    this.a();
                }
            }
        }
    }

    static class ThreadPerTaskExecutor implements Executor {
        @Override
        public final void execute(Runnable runnable0) {
            new Thread(runnable0).start();
        }
    }

    public static final SerialExecutor a;
    public static final int b;
    public static LocaleListCompat c;
    public static LocaleListCompat d;
    public static Boolean e;
    public static boolean f;
    public static final ArraySet g;
    public static final Object h;
    public static final Object i;

    static {
        AppCompatDelegate.a = new SerialExecutor(new ThreadPerTaskExecutor());  // 初始化器: Ljava/lang/Object;-><init>()V
        AppCompatDelegate.b = -100;
        AppCompatDelegate.c = null;
        AppCompatDelegate.d = null;
        AppCompatDelegate.e = null;
        AppCompatDelegate.f = false;
        AppCompatDelegate.g = new ArraySet(0);
        AppCompatDelegate.h = new Object();
        AppCompatDelegate.i = new Object();
    }

    public abstract void c();

    public static boolean d(Context context0) {
        if(AppCompatDelegate.e == null) {
            try {
                Bundle bundle0 = context0.getPackageManager().getServiceInfo(new ComponentName(context0, AppLocalesMetadataHolderService.class), 640).metaData;
                if(bundle0 != null) {
                    AppCompatDelegate.e = Boolean.valueOf(bundle0.getBoolean("autoStoreLocales"));
                    return AppCompatDelegate.e.booleanValue();
                }
            }
            catch(PackageManager.NameNotFoundException unused_ex) {
                AppCompatDelegate.e = Boolean.FALSE;
                return AppCompatDelegate.e.booleanValue();
            }
        }
        return AppCompatDelegate.e.booleanValue();
    }

    public abstract void e();

    public abstract void f();

    public static void g(AppCompatDelegate appCompatDelegate0) {
        synchronized(AppCompatDelegate.h) {
            Iterator iterator0 = AppCompatDelegate.g.iterator();
            while(iterator0.hasNext()) {
                Object object1 = iterator0.next();
                AppCompatDelegate appCompatDelegate1 = (AppCompatDelegate)((WeakReference)object1).get();
                if(appCompatDelegate1 == appCompatDelegate0 || appCompatDelegate1 == null) {
                    iterator0.remove();
                }
            }
        }
    }

    public abstract boolean h(int arg1);

    public abstract void i(int arg1);

    public abstract void j(View arg1);

    public abstract void k(View arg1, ViewGroup.LayoutParams arg2);

    public abstract void l(CharSequence arg1);

    public abstract ActionMode m(Callback arg1);
}


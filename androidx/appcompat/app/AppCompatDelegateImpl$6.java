package androidx.appcompat.app;

import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorCompat;

class AppCompatDelegateImpl.6 implements Runnable {
    public final AppCompatDelegateImpl a;

    public AppCompatDelegateImpl.6(AppCompatDelegateImpl appCompatDelegateImpl0) {
        this.a = appCompatDelegateImpl0;
    }

    @Override
    public final void run() {
        AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
        appCompatDelegateImpl0.w.showAtLocation(appCompatDelegateImpl0.v, 55, 0, 0);
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = appCompatDelegateImpl0.y;
        if(viewPropertyAnimatorCompat0 != null) {
            viewPropertyAnimatorCompat0.b();
        }
        if(appCompatDelegateImpl0.z && (appCompatDelegateImpl0.A != null && appCompatDelegateImpl0.A.isLaidOut())) {
            appCompatDelegateImpl0.v.setAlpha(0.0f);
            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1 = ViewCompat.a(appCompatDelegateImpl0.v);
            viewPropertyAnimatorCompat1.a(1.0f);
            appCompatDelegateImpl0.y = viewPropertyAnimatorCompat1;
            viewPropertyAnimatorCompat1.d(new AppCompatDelegateImpl.6.1(this));
            return;
        }
        appCompatDelegateImpl0.v.setAlpha(1.0f);
        appCompatDelegateImpl0.v.setVisibility(0);
    }
}


package androidx.appcompat.app;

import android.view.View;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;

class AlertController.AlertParams.3 implements AdapterView.OnItemClickListener {
    public final AlertController a;
    public final AlertParams b;

    public AlertController.AlertParams.3(AlertParams alertController$AlertParams0, AlertController alertController0) {
        this.b = alertController$AlertParams0;
        this.a = alertController0;
    }

    @Override  // android.widget.AdapterView$OnItemClickListener
    public final void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
        AlertController alertController0 = this.a;
        this.b.o.onClick(alertController0.b, v);
        if(!this.b.s) {
            alertController0.b.dismiss();
        }
    }
}


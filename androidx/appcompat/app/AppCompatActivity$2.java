package androidx.appcompat.app;

import android.content.Context;
import android.view.LayoutInflater;
import androidx.activity.contextaware.OnContextAvailableListener;

class AppCompatActivity.2 implements OnContextAvailableListener {
    public final AppCompatActivity a;

    public AppCompatActivity.2(AppCompatActivity appCompatActivity0) {
        this.a = appCompatActivity0;
    }

    @Override  // androidx.activity.contextaware.OnContextAvailableListener
    public final void onContextAvailable(Context context0) {
        AppCompatActivity appCompatActivity0 = this.a;
        AppCompatDelegate appCompatDelegate0 = appCompatActivity0.getDelegate();
        LayoutInflater layoutInflater0 = LayoutInflater.from(((AppCompatDelegateImpl)appCompatDelegate0).k);
        if(layoutInflater0.getFactory() == null) {
            layoutInflater0.setFactory2(((AppCompatDelegateImpl)appCompatDelegate0));
        }
        else {
            layoutInflater0.getFactory2();
        }
        appCompatActivity0.getSavedStateRegistry().a("androidx:appcompat");
        appCompatDelegate0.e();
    }
}


package androidx.appcompat.app;

import android.content.Context;
import android.location.LocationManager;

class TwilightManager {
    static class TwilightState {
        public boolean a;
        public long b;

    }

    public final Context a;
    public final LocationManager b;
    public final TwilightState c;
    public static TwilightManager d;

    public TwilightManager(Context context0, LocationManager locationManager0) {
        this.c = new TwilightState();  // 初始化器: Ljava/lang/Object;-><init>()V
        this.a = context0;
        this.b = locationManager0;
    }
}


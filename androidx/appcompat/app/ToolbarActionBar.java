package androidx.appcompat.app;

import android.content.Context;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.Window.Callback;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuPresenter.Callback;
import androidx.appcompat.widget.ActionMenuView;
import androidx.appcompat.widget.Toolbar.OnMenuItemClickListener;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.ToolbarWidgetWrapper;
import androidx.core.view.ViewCompat;
import androidx.work.impl.model.c;
import java.util.ArrayList;

class ToolbarActionBar extends ActionBar {
    final class ActionMenuPresenterCallback implements Callback {
        public boolean a;
        public final ToolbarActionBar b;

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final void c(MenuBuilder menuBuilder0, boolean z) {
            if(this.a) {
                return;
            }
            this.a = true;
            this.b.a.b();
            this.b.b.onPanelClosed(108, menuBuilder0);
            this.a = false;
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final boolean d(MenuBuilder menuBuilder0) {
            this.b.b.onMenuOpened(108, menuBuilder0);
            return true;
        }
    }

    final class MenuBuilderCallback implements androidx.appcompat.view.menu.MenuBuilder.Callback {
        public final ToolbarActionBar a;

        @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
        public final boolean a(MenuBuilder menuBuilder0, MenuItem menuItem0) {
            return false;
        }

        @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
        public final void b(MenuBuilder menuBuilder0) {
            boolean z = this.a.a.a.q();
            Window.Callback window$Callback0 = this.a.b;
            if(z) {
                window$Callback0.onPanelClosed(108, menuBuilder0);
                return;
            }
            if(window$Callback0.onPreparePanel(0, null, menuBuilder0)) {
                window$Callback0.onMenuOpened(108, menuBuilder0);
            }
        }
    }

    class ToolbarMenuCallback implements ActionBarMenuCallback {
        public final ToolbarActionBar a;

    }

    public final ToolbarWidgetWrapper a;
    public final Window.Callback b;
    public final ToolbarMenuCallback c;
    public boolean d;
    public boolean e;
    public boolean f;
    public final ArrayList g;
    public final Runnable h;

    public ToolbarActionBar(Toolbar toolbar0, CharSequence charSequence0, Window.Callback window$Callback0) {
        this.g = new ArrayList();
        this.h = new Runnable() {
            public final ToolbarActionBar a;

            {
                this.a = toolbarActionBar0;
            }

            @Override
            public final void run() {
                Window.Callback window$Callback0 = this.a.b;
                Menu menu0 = this.a.q();
                MenuBuilder menuBuilder0 = menu0 instanceof MenuBuilder ? ((MenuBuilder)menu0) : null;
                if(menuBuilder0 != null) {
                    menuBuilder0.w();
                }
                try {
                    menu0.clear();
                    if(!window$Callback0.onCreatePanelMenu(0, menu0) || !window$Callback0.onPreparePanel(0, null, menu0)) {
                        menu0.clear();
                    }
                }
                finally {
                    if(menuBuilder0 != null) {
                        menuBuilder0.v();
                    }
                }
            }
        };
        androidx.appcompat.app.ToolbarActionBar.2 toolbarActionBar$20 = new OnMenuItemClickListener() {
            public final ToolbarActionBar a;

            {
                this.a = toolbarActionBar0;
            }

            @Override  // androidx.appcompat.widget.Toolbar$OnMenuItemClickListener
            public final boolean onMenuItemClick(MenuItem menuItem0) {
                return this.a.b.onMenuItemSelected(0, menuItem0);
            }
        };
        toolbar0.getClass();
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = new ToolbarWidgetWrapper(toolbar0, false);
        this.a = toolbarWidgetWrapper0;
        window$Callback0.getClass();
        this.b = window$Callback0;
        toolbarWidgetWrapper0.k = window$Callback0;
        toolbar0.setOnMenuItemClickListener(toolbarActionBar$20);
        if(!toolbarWidgetWrapper0.g) {
            toolbarWidgetWrapper0.h = charSequence0;
            if((toolbarWidgetWrapper0.b & 8) != 0) {
                Toolbar toolbar1 = toolbarWidgetWrapper0.a;
                toolbar1.setTitle(charSequence0);
                if(toolbarWidgetWrapper0.g) {
                    ViewCompat.E(toolbar1.getRootView(), charSequence0);
                }
            }
        }
        this.c = new ToolbarMenuCallback(this);
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final boolean a() {
        return this.a.c();
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final boolean b() {
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = this.a;
        if(toolbarWidgetWrapper0.a.m()) {
            toolbarWidgetWrapper0.a();
            return true;
        }
        return false;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void c(boolean z) {
        if(z == this.f) {
            return;
        }
        this.f = z;
        ArrayList arrayList0 = this.g;
        if(arrayList0.size() <= 0) {
            return;
        }
        c.v(arrayList0.get(0));
        throw null;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final int d() {
        return this.a.b;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final Context e() {
        return this.a.a.getContext();
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final boolean f() {
        this.a.a.removeCallbacks(this.h);
        this.a.a.postOnAnimation(this.h);
        return true;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void g() {
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void h() {
        this.a.a.removeCallbacks(this.h);
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final boolean i(int v, KeyEvent keyEvent0) {
        Menu menu0 = this.q();
        if(menu0 != null) {
            menu0.setQwertyMode(KeyCharacterMap.load((keyEvent0 == null ? -1 : keyEvent0.getDeviceId())).getKeyboardType() != 1);
            return menu0.performShortcut(v, keyEvent0, 0);
        }
        return false;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final boolean j(KeyEvent keyEvent0) {
        if(keyEvent0.getAction() == 1) {
            this.k();
        }
        return true;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final boolean k() {
        return this.a.a.w();
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void l(boolean z) {
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void m() {
        this.a.d(this.a.b & -9);
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void n(boolean z) {
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void o(CharSequence charSequence0) {
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = this.a;
        if(!toolbarWidgetWrapper0.g) {
            toolbarWidgetWrapper0.h = charSequence0;
            if((toolbarWidgetWrapper0.b & 8) != 0) {
                Toolbar toolbar0 = toolbarWidgetWrapper0.a;
                toolbar0.setTitle(charSequence0);
                if(toolbarWidgetWrapper0.g) {
                    ViewCompat.E(toolbar0.getRootView(), charSequence0);
                }
            }
        }
    }

    public final Menu q() {
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = this.a;
        if(!this.e) {
            ActionMenuPresenterCallback toolbarActionBar$ActionMenuPresenterCallback0 = new ActionMenuPresenterCallback(this);
            MenuBuilderCallback toolbarActionBar$MenuBuilderCallback0 = new MenuBuilderCallback(this);
            toolbarWidgetWrapper0.a.N = toolbarActionBar$ActionMenuPresenterCallback0;
            toolbarWidgetWrapper0.a.O = toolbarActionBar$MenuBuilderCallback0;
            ActionMenuView actionMenuView0 = toolbarWidgetWrapper0.a.a;
            if(actionMenuView0 != null) {
                actionMenuView0.u = toolbarActionBar$ActionMenuPresenterCallback0;
                actionMenuView0.v = toolbarActionBar$MenuBuilderCallback0;
            }
            this.e = true;
        }
        return toolbarWidgetWrapper0.a.getMenu();
    }
}


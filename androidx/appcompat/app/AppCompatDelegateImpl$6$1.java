package androidx.appcompat.app;

import androidx.core.view.ViewPropertyAnimatorListenerAdapter;

class AppCompatDelegateImpl.6.1 extends ViewPropertyAnimatorListenerAdapter {
    public final AppCompatDelegateImpl.6 a;

    public AppCompatDelegateImpl.6.1(AppCompatDelegateImpl.6 appCompatDelegateImpl$60) {
        this.a = appCompatDelegateImpl$60;
    }

    @Override  // androidx.core.view.ViewPropertyAnimatorListenerAdapter
    public final void b() {
        this.a.a.v.setVisibility(0);
    }

    @Override  // androidx.core.view.ViewPropertyAnimatorListener
    public final void c() {
        this.a.a.v.setAlpha(1.0f);
        this.a.a.y.d(null);
        this.a.a.y = null;
    }
}


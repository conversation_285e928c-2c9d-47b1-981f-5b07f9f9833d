package androidx.appcompat.app;

import android.content.ComponentName;
import android.content.Context;
import android.os.Build.VERSION;
import androidx.core.app.AppLocalesStorageHelper;
import androidx.core.os.LocaleListCompat;
import java.lang.ref.WeakReference;

public final class a implements Runnable {
    public final Context a;

    public a(Context context0) {
        this.a = context0;
    }

    @Override
    public final void run() {
        LocaleListCompat localeListCompat0;
        int v = Build.VERSION.SDK_INT;
        if(v >= 33) {
            Context context0 = this.a;
            ComponentName componentName0 = new ComponentName(context0, "androidx.appcompat.app.AppLocalesMetadataHolderService");
            if(context0.getPackageManager().getComponentEnabledSetting(componentName0) != 1) {
                if(v >= 33) {
                    Object object0 = null;
                    for(Object object1: AppCompatDelegate.g) {
                        AppCompatDelegate appCompatDelegate0 = (AppCompatDelegate)((WeakReference)object1).get();
                        if(appCompatDelegate0 != null) {
                            Context context1 = ((AppCompatDelegateImpl)appCompatDelegate0).k;
                            if(context1 != null) {
                                object0 = context1.getSystemService("locale");
                                break;
                            }
                        }
                    }
                    localeListCompat0 = object0 == null ? LocaleListCompat.b : LocaleListCompat.g(Api33Impl.a(object0));
                }
                else {
                    localeListCompat0 = AppCompatDelegate.c;
                    if(localeListCompat0 == null) {
                        localeListCompat0 = LocaleListCompat.b;
                    }
                }
                if(localeListCompat0.d()) {
                    String s = AppLocalesStorageHelper.b(context0);
                    Object object2 = context0.getSystemService("locale");
                    if(object2 != null) {
                        Api33Impl.b(object2, Api24Impl.a(s));
                    }
                }
                context0.getPackageManager().setComponentEnabledSetting(componentName0, 1, 1);
            }
        }
        AppCompatDelegate.f = true;
    }
}


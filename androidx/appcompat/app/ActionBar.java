package androidx.appcompat.app;

import android.content.Context;
import android.view.KeyEvent;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup.MarginLayoutParams;
import androidx.appcompat.view.ActionMode.Callback;
import androidx.appcompat.view.ActionMode;

public abstract class ActionBar {
    public static abstract class LayoutParams extends ViewGroup.MarginLayoutParams {
        public int a;

        public LayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
            super(viewGroup$LayoutParams0);
            this.a = 0;
        }

        public LayoutParams(LayoutParams actionBar$LayoutParams0) {
            super(actionBar$LayoutParams0);
            this.a = actionBar$LayoutParams0.a;
        }
    }

    public boolean a() {
        return false;
    }

    public abstract boolean b();

    public abstract void c(boolean arg1);

    public abstract int d();

    public abstract Context e();

    public boolean f() {
        return false;
    }

    public abstract void g();

    public void h() {
    }

    public abstract boolean i(int arg1, KeyEvent arg2);

    public boolean j(KeyEvent keyEvent0) {
        return false;
    }

    public boolean k() {
        return false;
    }

    public abstract void l(boolean arg1);

    public abstract void m();

    public abstract void n(boolean arg1);

    public abstract void o(CharSequence arg1);

    public ActionMode p(Callback actionMode$Callback0) {
        return null;
    }
}


package androidx.appcompat.app;

import android.content.Context;
import android.content.DialogInterface.OnKeyListener;
import android.content.DialogInterface;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View.OnClickListener;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout.LayoutParams;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.widget.LinearLayoutCompat.LayoutParams;
import androidx.core.view.ViewCompat;
import androidx.core.widget.NestedScrollView;
import com.facebook.login.c;

public class AlertDialog extends AppCompatDialog implements DialogInterface {
    public static class Builder {
        public final AlertParams a;
        public final int b;

        public Builder(Context context0) {
            int v = AlertDialog.i(0, context0);
            super();
            this.a = new AlertParams(new ContextThemeWrapper(context0, AlertDialog.i(v, context0)));
            this.b = v;
        }

        public final AlertDialog a() {
            ListAdapter listAdapter0;
            AlertParams alertController$AlertParams0 = this.a;
            AlertDialog alertDialog0 = new AlertDialog(alertController$AlertParams0.a, this.b);
            View view0 = alertController$AlertParams0.e;
            AlertController alertController0 = alertDialog0.f;
            if(view0 == null) {
                CharSequence charSequence0 = alertController$AlertParams0.d;
                if(charSequence0 != null) {
                    alertController0.e = charSequence0;
                    TextView textView0 = alertController0.A;
                    if(textView0 != null) {
                        textView0.setText(charSequence0);
                    }
                }
                Drawable drawable0 = alertController$AlertParams0.c;
                if(drawable0 != null) {
                    alertController0.y = drawable0;
                    alertController0.x = 0;
                    ImageView imageView0 = alertController0.z;
                    if(imageView0 != null) {
                        imageView0.setVisibility(0);
                        alertController0.z.setImageDrawable(drawable0);
                    }
                }
            }
            else {
                alertController0.C = view0;
            }
            CharSequence charSequence1 = alertController$AlertParams0.f;
            if(charSequence1 != null) {
                alertController0.f = charSequence1;
                TextView textView1 = alertController0.B;
                if(textView1 != null) {
                    textView1.setText(charSequence1);
                }
            }
            CharSequence charSequence2 = alertController$AlertParams0.g;
            if(charSequence2 != null) {
                alertController0.d(-1, charSequence2, alertController$AlertParams0.h);
            }
            CharSequence charSequence3 = alertController$AlertParams0.i;
            if(charSequence3 != null) {
                alertController0.d(-2, charSequence3, alertController$AlertParams0.j);
            }
            if(alertController$AlertParams0.m != null || alertController$AlertParams0.n != null) {
                View view1 = alertController$AlertParams0.b.inflate(alertController0.G, null);
                if(alertController$AlertParams0.r) {
                    listAdapter0 = new AlertController.AlertParams.1(alertController$AlertParams0, alertController$AlertParams0.a, alertController0.H, alertController$AlertParams0.m, ((RecycleListView)view1));
                }
                else {
                    int v = alertController$AlertParams0.s ? alertController0.I : alertController0.J;
                    listAdapter0 = alertController$AlertParams0.n;
                    if(listAdapter0 == null) {
                        listAdapter0 = new CheckedItemAdapter(alertController$AlertParams0.a, v, 0x1020014, alertController$AlertParams0.m);  // 初始化器: Landroid/widget/ArrayAdapter;-><init>(Landroid/content/Context;II[Ljava/lang/Object;)V
                    }
                }
                alertController0.D = listAdapter0;
                alertController0.E = alertController$AlertParams0.t;
                if(alertController$AlertParams0.o != null) {
                    ((RecycleListView)view1).setOnItemClickListener(new AlertController.AlertParams.3(alertController$AlertParams0, alertController0));
                }
                else if(alertController$AlertParams0.u != null) {
                    ((RecycleListView)view1).setOnItemClickListener(new AlertController.AlertParams.4(alertController$AlertParams0, ((RecycleListView)view1), alertController0));
                }
                if(alertController$AlertParams0.s) {
                    ((RecycleListView)view1).setChoiceMode(1);
                }
                else if(alertController$AlertParams0.r) {
                    ((RecycleListView)view1).setChoiceMode(2);
                }
                alertController0.g = (RecycleListView)view1;
            }
            View view2 = alertController$AlertParams0.p;
            if(view2 != null) {
                alertController0.h = view2;
                alertController0.i = 0;
                alertController0.j = false;
            }
            alertDialog0.setCancelable(alertController$AlertParams0.k);
            if(alertController$AlertParams0.k) {
                alertDialog0.setCanceledOnTouchOutside(true);
            }
            alertDialog0.setOnCancelListener(null);
            alertDialog0.setOnDismissListener(null);
            DialogInterface.OnKeyListener dialogInterface$OnKeyListener0 = alertController$AlertParams0.l;
            if(dialogInterface$OnKeyListener0 != null) {
                alertDialog0.setOnKeyListener(dialogInterface$OnKeyListener0);
            }
            return alertDialog0;
        }
    }

    public final AlertController f;

    public AlertDialog(Context context0, int v) {
        super(context0, AlertDialog.i(v, context0));
        this.f = new AlertController(this.getContext(), this, this.getWindow());
    }

    public final Button g(int v) {
        AlertController alertController0 = this.f;
        switch(v) {
            case -3: {
                return alertController0.s;
            }
            case -2: {
                return alertController0.o;
            }
            case -1: {
                return alertController0.k;
            }
            default: {
                alertController0.getClass();
                return null;
            }
        }
    }

    public final ListView h() {
        return this.f.g;
    }

    public static int i(int v, Context context0) {
        if((v >>> 24 & 0xFF) >= 1) {
            return v;
        }
        TypedValue typedValue0 = new TypedValue();
        context0.getTheme().resolveAttribute(0x7F040039, typedValue0, true);  // attr:alertDialogTheme
        return typedValue0.resourceId;
    }

    public final void j(int v, CharSequence charSequence0, c c0) {
        this.f.d(v, charSequence0, c0);
    }

    @Override  // androidx.appcompat.app.AppCompatDialog
    public final void onCreate(Bundle bundle0) {
        View view13;
        int v3;
        super.onCreate(bundle0);
        AlertController alertController0 = this.f;
        alertController0.b.setContentView(alertController0.F);
        Window window0 = alertController0.c;
        View view0 = window0.findViewById(0x7F0A0368);  // id:parentPanel
        View view1 = view0.findViewById(0x7F0A0475);  // id:topPanel
        View view2 = view0.findViewById(0x7F0A0142);  // id:contentPanel
        View view3 = view0.findViewById(0x7F0A010D);  // id:buttonPanel
        ViewGroup viewGroup0 = (ViewGroup)view0.findViewById(0x7F0A015B);  // id:customPanel
        View view4 = alertController0.h;
        int v = 0;
        Context context0 = alertController0.a;
        if(view4 == null) {
            view4 = alertController0.i == 0 ? null : LayoutInflater.from(context0).inflate(alertController0.i, viewGroup0, false);
        }
        if(view4 == null || !AlertController.a(view4)) {
            window0.setFlags(0x20000, 0x20000);
        }
        if(view4 == null) {
            viewGroup0.setVisibility(8);
        }
        else {
            FrameLayout frameLayout0 = (FrameLayout)window0.findViewById(0x7F0A015A);  // id:custom
            frameLayout0.addView(view4, new ViewGroup.LayoutParams(-1, -1));
            if(alertController0.j) {
                frameLayout0.setPadding(0, 0, 0, 0);
            }
            if(alertController0.g != null) {
                ((LayoutParams)viewGroup0.getLayoutParams()).weight = 0.0f;
            }
        }
        View view5 = viewGroup0.findViewById(0x7F0A0475);  // id:topPanel
        View view6 = viewGroup0.findViewById(0x7F0A0142);  // id:contentPanel
        View view7 = viewGroup0.findViewById(0x7F0A010D);  // id:buttonPanel
        ViewGroup viewGroup1 = AlertController.c(view5, view1);
        ViewGroup viewGroup2 = AlertController.c(view6, view2);
        ViewGroup viewGroup3 = AlertController.c(view7, view3);
        NestedScrollView nestedScrollView0 = (NestedScrollView)window0.findViewById(0x7F0A03CF);  // id:scrollView
        alertController0.w = nestedScrollView0;
        nestedScrollView0.setFocusable(false);
        alertController0.w.setNestedScrollingEnabled(false);
        TextView textView0 = (TextView)viewGroup2.findViewById(0x102000B);
        alertController0.B = textView0;
        if(textView0 != null) {
            CharSequence charSequence0 = alertController0.f;
            if(charSequence0 == null) {
                textView0.setVisibility(8);
                alertController0.w.removeView(alertController0.B);
                if(alertController0.g == null) {
                    viewGroup2.setVisibility(8);
                }
                else {
                    ViewGroup viewGroup4 = (ViewGroup)alertController0.w.getParent();
                    int v1 = viewGroup4.indexOfChild(alertController0.w);
                    viewGroup4.removeViewAt(v1);
                    viewGroup4.addView(alertController0.g, v1, new ViewGroup.LayoutParams(-1, -1));
                }
            }
            else {
                textView0.setText(charSequence0);
            }
        }
        Button button0 = (Button)viewGroup3.findViewById(0x1020019);
        alertController0.k = button0;
        View.OnClickListener view$OnClickListener0 = alertController0.M;
        button0.setOnClickListener(view$OnClickListener0);
        boolean z = TextUtils.isEmpty(alertController0.l);
        int v2 = alertController0.d;
        if(!z || alertController0.n != null) {
            alertController0.k.setText(alertController0.l);
            Drawable drawable0 = alertController0.n;
            if(drawable0 != null) {
                drawable0.setBounds(0, 0, v2, v2);
                alertController0.k.setCompoundDrawables(alertController0.n, null, null, null);
            }
            alertController0.k.setVisibility(0);
            v3 = 1;
        }
        else {
            alertController0.k.setVisibility(8);
            v3 = 0;
        }
        Button button1 = (Button)viewGroup3.findViewById(0x102001A);
        alertController0.o = button1;
        button1.setOnClickListener(view$OnClickListener0);
        if(!TextUtils.isEmpty(alertController0.p) || alertController0.r != null) {
            alertController0.o.setText(alertController0.p);
            Drawable drawable1 = alertController0.r;
            if(drawable1 != null) {
                drawable1.setBounds(0, 0, v2, v2);
                alertController0.o.setCompoundDrawables(alertController0.r, null, null, null);
            }
            alertController0.o.setVisibility(0);
            v3 |= 2;
        }
        else {
            alertController0.o.setVisibility(8);
        }
        Button button2 = (Button)viewGroup3.findViewById(0x102001B);
        alertController0.s = button2;
        button2.setOnClickListener(view$OnClickListener0);
        if(!TextUtils.isEmpty(alertController0.t) || alertController0.v != null) {
            alertController0.s.setText(alertController0.t);
            Drawable drawable2 = alertController0.v;
            if(drawable2 != null) {
                drawable2.setBounds(0, 0, v2, v2);
                alertController0.s.setCompoundDrawables(alertController0.v, null, null, null);
            }
            alertController0.s.setVisibility(0);
            v3 |= 4;
        }
        else {
            alertController0.s.setVisibility(8);
        }
        TypedValue typedValue0 = new TypedValue();
        context0.getTheme().resolveAttribute(0x7F040037, typedValue0, true);  // attr:alertDialogCenterButtons
        if(typedValue0.data != 0) {
            switch(v3) {
                case 1: {
                    Button button3 = alertController0.k;
                    LinearLayout.LayoutParams linearLayout$LayoutParams0 = (LinearLayout.LayoutParams)button3.getLayoutParams();
                    linearLayout$LayoutParams0.gravity = 1;
                    linearLayout$LayoutParams0.weight = 0.5f;
                    button3.setLayoutParams(linearLayout$LayoutParams0);
                    break;
                }
                case 2: {
                    Button button4 = alertController0.o;
                    LinearLayout.LayoutParams linearLayout$LayoutParams1 = (LinearLayout.LayoutParams)button4.getLayoutParams();
                    linearLayout$LayoutParams1.gravity = 1;
                    linearLayout$LayoutParams1.weight = 0.5f;
                    button4.setLayoutParams(linearLayout$LayoutParams1);
                    break;
                }
                case 4: {
                    Button button5 = alertController0.s;
                    LinearLayout.LayoutParams linearLayout$LayoutParams2 = (LinearLayout.LayoutParams)button5.getLayoutParams();
                    linearLayout$LayoutParams2.gravity = 1;
                    linearLayout$LayoutParams2.weight = 0.5f;
                    button5.setLayoutParams(linearLayout$LayoutParams2);
                }
            }
        }
        if(v3 == 0) {
            viewGroup3.setVisibility(8);
        }
        if(alertController0.C == null) {
            alertController0.z = (ImageView)window0.findViewById(0x1020006);
            if(!TextUtils.isEmpty(alertController0.e) == 0 || !alertController0.K) {
                window0.findViewById(0x7F0A046A).setVisibility(8);  // id:title_template
                alertController0.z.setVisibility(8);
                viewGroup1.setVisibility(8);
            }
            else {
                TextView textView1 = (TextView)window0.findViewById(0x7F0A007F);  // id:alertTitle
                alertController0.A = textView1;
                textView1.setText(alertController0.e);
                int v4 = alertController0.x;
                if(v4 == 0) {
                    Drawable drawable3 = alertController0.y;
                    if(drawable3 == null) {
                        alertController0.A.setPadding(alertController0.z.getPaddingLeft(), alertController0.z.getPaddingTop(), alertController0.z.getPaddingRight(), alertController0.z.getPaddingBottom());
                        alertController0.z.setVisibility(8);
                    }
                    else {
                        alertController0.z.setImageDrawable(drawable3);
                    }
                }
                else {
                    alertController0.z.setImageResource(v4);
                }
            }
        }
        else {
            ViewGroup.LayoutParams viewGroup$LayoutParams0 = new ViewGroup.LayoutParams(-1, -2);
            viewGroup1.addView(alertController0.C, 0, viewGroup$LayoutParams0);
            window0.findViewById(0x7F0A046A).setVisibility(8);  // id:title_template
        }
        boolean z1 = viewGroup0.getVisibility() != 8;
        int v5 = viewGroup1 == null || viewGroup1.getVisibility() == 8 ? 0 : 1;
        boolean z2 = viewGroup3.getVisibility() != 8;
        if(!z2) {
            View view8 = viewGroup2.findViewById(0x7F0A044E);  // id:textSpacerNoButtons
            if(view8 != null) {
                view8.setVisibility(0);
            }
        }
        if(v5 == 0) {
            View view10 = viewGroup2.findViewById(0x7F0A044F);  // id:textSpacerNoTitle
            if(view10 != null) {
                view10.setVisibility(0);
            }
        }
        else {
            NestedScrollView nestedScrollView1 = alertController0.w;
            if(nestedScrollView1 != null) {
                nestedScrollView1.setClipToPadding(true);
            }
            View view9 = alertController0.f != null || alertController0.g != null ? viewGroup1.findViewById(0x7F0A0468) : null;  // id:titleDividerNoCustom
            if(view9 != null) {
                view9.setVisibility(0);
            }
        }
        RecycleListView alertController$RecycleListView0 = alertController0.g;
        if(alertController$RecycleListView0 instanceof RecycleListView) {
            alertController$RecycleListView0.getClass();
            if(!z2 || v5 == 0) {
                alertController$RecycleListView0.setPadding(alertController$RecycleListView0.getPaddingLeft(), (v5 == 0 ? alertController$RecycleListView0.a : alertController$RecycleListView0.getPaddingTop()), alertController$RecycleListView0.getPaddingRight(), (z2 ? alertController$RecycleListView0.getPaddingBottom() : alertController$RecycleListView0.b));
            }
        }
        if(!z1) {
            RecycleListView alertController$RecycleListView1 = alertController0.g;
            if(alertController$RecycleListView1 == null) {
                alertController$RecycleListView1 = alertController0.w;
            }
            if(alertController$RecycleListView1 != null) {
                if(z2) {
                    v = 2;
                }
                int v6 = v5 | v;
                View view11 = window0.findViewById(0x7F0A03CE);  // id:scrollIndicatorUp
                View view12 = window0.findViewById(0x7F0A03CD);  // id:scrollIndicatorDown
                if(Build.VERSION.SDK_INT >= 23) {
                    ViewCompat.M(alertController$RecycleListView1, v6);
                    if(view11 != null) {
                        viewGroup2.removeView(view11);
                    }
                    if(view12 != null) {
                        viewGroup2.removeView(view12);
                    }
                }
                else {
                    if(view11 != null && (v6 & 1) == 0) {
                        viewGroup2.removeView(view11);
                        view11 = null;
                    }
                    if(view12 == null || (v6 & 2) != 0) {
                        view13 = view12;
                    }
                    else {
                        viewGroup2.removeView(view12);
                        view13 = null;
                    }
                    if(view11 != null || view13 != null) {
                        if(alertController0.f == null) {
                            RecycleListView alertController$RecycleListView2 = alertController0.g;
                            if(alertController$RecycleListView2 == null) {
                                if(view11 != null) {
                                    viewGroup2.removeView(view11);
                                }
                                if(view13 != null) {
                                    viewGroup2.removeView(view13);
                                }
                            }
                            else {
                                alertController$RecycleListView2.setOnScrollListener(new AlertController.4(view11, view13));
                                alertController0.g.post(new AlertController.5(alertController0, view11, view13));
                            }
                        }
                        else {
                            alertController0.w.setOnScrollChangeListener(new AlertController.2(view11, view13));
                            alertController0.w.post(new AlertController.3(alertController0, view11, view13));
                        }
                    }
                }
            }
        }
        RecycleListView alertController$RecycleListView3 = alertController0.g;
        if(alertController$RecycleListView3 != null) {
            ListAdapter listAdapter0 = alertController0.D;
            if(listAdapter0 != null) {
                alertController$RecycleListView3.setAdapter(listAdapter0);
                int v7 = alertController0.E;
                if(v7 > -1) {
                    alertController$RecycleListView3.setItemChecked(v7, true);
                    alertController$RecycleListView3.setSelection(v7);
                }
            }
        }
    }

    @Override  // android.app.Dialog
    public final boolean onKeyDown(int v, KeyEvent keyEvent0) {
        NestedScrollView nestedScrollView0 = this.f.w;
        return nestedScrollView0 == null || !nestedScrollView0.e(keyEvent0) ? super.onKeyDown(v, keyEvent0) : true;
    }

    @Override  // android.app.Dialog
    public final boolean onKeyUp(int v, KeyEvent keyEvent0) {
        NestedScrollView nestedScrollView0 = this.f.w;
        return nestedScrollView0 == null || !nestedScrollView0.e(keyEvent0) ? super.onKeyUp(v, keyEvent0) : true;
    }

    @Override  // androidx.appcompat.app.AppCompatDialog
    public final void setTitle(CharSequence charSequence0) {
        super.setTitle(charSequence0);
        this.f.e = charSequence0;
        TextView textView0 = this.f.A;
        if(textView0 != null) {
            textView0.setText(charSequence0);
        }
    }
}


package androidx.appcompat.app;

import android.content.DialogInterface.OnMultiChoiceClickListener;
import android.view.View;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;

class AlertController.AlertParams.4 implements AdapterView.OnItemClickListener {
    public final RecycleListView a;
    public final AlertController b;
    public final AlertParams c;

    public AlertController.AlertParams.4(AlertParams alertController$AlertParams0, RecycleListView alertController$RecycleListView0, AlertController alertController0) {
        this.c = alertController$AlertParams0;
        this.a = alertController$RecycleListView0;
        this.b = alertController0;
    }

    @Override  // android.widget.AdapterView$OnItemClickListener
    public final void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
        AlertParams alertController$AlertParams0 = this.c;
        boolean[] arr_z = alertController$AlertParams0.q;
        RecycleListView alertController$RecycleListView0 = this.a;
        if(arr_z != null) {
            arr_z[v] = alertController$RecycleListView0.isItemChecked(v);
        }
        DialogInterface.OnMultiChoiceClickListener dialogInterface$OnMultiChoiceClickListener0 = alertController$AlertParams0.u;
        boolean z = alertController$RecycleListView0.isItemChecked(v);
        dialogInterface$OnMultiChoiceClickListener0.onClick(this.b.b, v, z);
    }
}


package androidx.appcompat.app;

import android.content.Context;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import androidx.activity.ComponentDialog;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner;
import androidx.appcompat.view.ActionMode.Callback;
import androidx.appcompat.view.ActionMode;
import androidx.core.view.KeyEventDispatcher;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;
import k.a;

public abstract class AppCompatDialog extends ComponentDialog implements AppCompatCallback {
    public AppCompatDelegate d;
    public final a e;

    public AppCompatDialog(Context context0, int v) {
        int v1;
        if(v == 0) {
            TypedValue typedValue0 = new TypedValue();
            context0.getTheme().resolveAttribute(0x7F0401EA, typedValue0, true);  // attr:dialogTheme
            v1 = typedValue0.resourceId;
        }
        else {
            v1 = v;
        }
        super(context0, v1);
        this.e = (KeyEvent keyEvent0) -> super.dispatchKeyEvent(keyEvent0);
        AppCompatDelegate appCompatDelegate0 = this.d();
        if(v == 0) {
            TypedValue typedValue1 = new TypedValue();
            context0.getTheme().resolveAttribute(0x7F0401EA, typedValue1, true);  // attr:dialogTheme
            v = typedValue1.resourceId;
        }
        ((AppCompatDelegateImpl)appCompatDelegate0).T = v;
        appCompatDelegate0.e();
    }

    @Override  // androidx.activity.ComponentDialog
    public final void addContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.d();
        appCompatDelegateImpl0.w();
        ((ViewGroup)appCompatDelegateImpl0.A.findViewById(0x1020002)).addView(view0, viewGroup$LayoutParams0);
        appCompatDelegateImpl0.m.a(appCompatDelegateImpl0.l.getCallback());
    }

    public final AppCompatDelegate d() {
        if(this.d == null) {
            this.d = new AppCompatDelegateImpl(this.getContext(), this.getWindow(), this, this);
        }
        return this.d;
    }

    @Override  // android.app.Dialog
    public final void dismiss() {
        super.dismiss();
        this.d().f();
    }

    @Override  // android.app.Dialog
    public final boolean dispatchKeyEvent(KeyEvent keyEvent0) {
        View view0 = this.getWindow().getDecorView();
        return KeyEventDispatcher.b(this.e, view0, this, keyEvent0);
    }

    public final void e() {
        ViewTreeLifecycleOwner.a(this.getWindow().getDecorView(), this);
        ViewTreeSavedStateRegistryOwner.a(this.getWindow().getDecorView(), this);
        ViewTreeOnBackPressedDispatcherOwner.a(this.getWindow().getDecorView(), this);
    }

    // 检测为 Lambda 实现
    public final boolean f(KeyEvent keyEvent0) [...]

    @Override  // android.app.Dialog
    public final View findViewById(int v) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.d();
        appCompatDelegateImpl0.w();
        return appCompatDelegateImpl0.l.findViewById(v);
    }

    @Override  // android.app.Dialog
    public final void invalidateOptionsMenu() {
        this.d().c();
    }

    @Override  // androidx.activity.ComponentDialog
    public void onCreate(Bundle bundle0) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.d();
        LayoutInflater layoutInflater0 = LayoutInflater.from(appCompatDelegateImpl0.k);
        if(layoutInflater0.getFactory() == null) {
            layoutInflater0.setFactory2(appCompatDelegateImpl0);
        }
        else {
            layoutInflater0.getFactory2();
        }
        super.onCreate(bundle0);
        this.d().e();
    }

    @Override  // androidx.activity.ComponentDialog
    public final void onStop() {
        super.onStop();
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.d();
        appCompatDelegateImpl0.B();
        ActionBar actionBar0 = appCompatDelegateImpl0.o;
        if(actionBar0 != null) {
            actionBar0.n(false);
        }
    }

    @Override  // androidx.appcompat.app.AppCompatCallback
    public final void onSupportActionModeFinished(ActionMode actionMode0) {
    }

    @Override  // androidx.appcompat.app.AppCompatCallback
    public final void onSupportActionModeStarted(ActionMode actionMode0) {
    }

    @Override  // androidx.appcompat.app.AppCompatCallback
    public final ActionMode onWindowStartingSupportActionMode(Callback actionMode$Callback0) {
        return null;
    }

    @Override  // androidx.activity.ComponentDialog
    public final void setContentView(int v) {
        this.e();
        this.d().i(v);
    }

    @Override  // androidx.activity.ComponentDialog
    public final void setContentView(View view0) {
        this.e();
        this.d().j(view0);
    }

    @Override  // androidx.activity.ComponentDialog
    public final void setContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        this.e();
        this.d().k(view0, viewGroup$LayoutParams0);
    }

    @Override  // android.app.Dialog
    public final void setTitle(int v) {
        super.setTitle(v);
        this.d().l(this.getContext().getString(v));
    }

    @Override  // android.app.Dialog
    public void setTitle(CharSequence charSequence0) {
        super.setTitle(charSequence0);
        this.d().l(charSequence0);
    }
}


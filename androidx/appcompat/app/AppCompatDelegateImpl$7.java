package androidx.appcompat.app;

import android.view.View;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorListenerAdapter;

class AppCompatDelegateImpl.7 extends ViewPropertyAnimatorListenerAdapter {
    public final AppCompatDelegateImpl a;

    public AppCompatDelegateImpl.7(AppCompatDelegateImpl appCompatDelegateImpl0) {
        this.a = appCompatDelegateImpl0;
    }

    @Override  // androidx.core.view.ViewPropertyAnimatorListenerAdapter
    public final void b() {
        AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
        appCompatDelegateImpl0.v.setVisibility(0);
        if(appCompatDelegateImpl0.v.getParent() instanceof View) {
            ViewCompat.A(((View)appCompatDelegateImpl0.v.getParent()));
        }
    }

    @Override  // androidx.core.view.ViewPropertyAnimatorListener
    public final void c() {
        this.a.v.setAlpha(1.0f);
        this.a.y.d(null);
        this.a.y = null;
    }
}


package androidx.appcompat.app;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import androidx.appcompat.R.styleable;
import androidx.appcompat.view.ActionBarPolicy;
import androidx.appcompat.view.ActionMode;
import androidx.appcompat.view.SupportMenuInflater;
import androidx.appcompat.view.ViewPropertyAnimatorCompatSet;
import androidx.appcompat.view.menu.MenuBuilder.Callback;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.widget.ActionBarContainer;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.appcompat.widget.ActionBarOverlayLayout.ActionBarVisibilityCallback;
import androidx.appcompat.widget.ActionBarOverlayLayout;
import androidx.appcompat.widget.DecorToolbar;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.ToolbarWidgetWrapper;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.core.view.ViewPropertyAnimatorListener;
import androidx.core.view.ViewPropertyAnimatorListenerAdapter;
import androidx.core.view.ViewPropertyAnimatorUpdateListener;
import androidx.work.impl.model.c;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import v.h;

public class WindowDecorActionBar extends ActionBar implements ActionBarVisibilityCallback {
    public class ActionModeImpl extends ActionMode implements Callback {
        public final Context c;
        public final MenuBuilder d;
        public androidx.appcompat.view.ActionMode.Callback e;
        public WeakReference f;
        public final WindowDecorActionBar g;

        public ActionModeImpl(Context context0, androidx.appcompat.view.ActionMode.Callback actionMode$Callback0) {
            this.c = context0;
            this.e = actionMode$Callback0;
            MenuBuilder menuBuilder0 = new MenuBuilder(context0);
            menuBuilder0.l = 1;
            this.d = menuBuilder0;
            menuBuilder0.e = this;
        }

        @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
        public final boolean a(MenuBuilder menuBuilder0, MenuItem menuItem0) {
            return this.e == null ? false : this.e.c(this, menuItem0);
        }

        @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
        public final void b(MenuBuilder menuBuilder0) {
            if(this.e == null) {
                return;
            }
            this.i();
            this.g.f.i();
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final void c() {
            WindowDecorActionBar windowDecorActionBar0 = this.g;
            if(windowDecorActionBar0.i != this) {
                return;
            }
            if(windowDecorActionBar0.p) {
                windowDecorActionBar0.j = this;
                windowDecorActionBar0.k = this.e;
            }
            else {
                this.e.a(this);
            }
            this.e = null;
            windowDecorActionBar0.q(false);
            ActionBarContextView actionBarContextView0 = windowDecorActionBar0.f;
            if(actionBarContextView0.k == null) {
                actionBarContextView0.g();
            }
            windowDecorActionBar0.c.setHideOnContentScrollEnabled(windowDecorActionBar0.u);
            windowDecorActionBar0.i = null;
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final View d() {
            return this.f == null ? null : ((View)this.f.get());
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final MenuBuilder e() {
            return this.d;
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final MenuInflater f() {
            return new SupportMenuInflater(this.c);
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final CharSequence g() {
            return this.g.f.getSubtitle();
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final CharSequence h() {
            return this.g.f.getTitle();
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final void i() {
            if(this.g.i != this) {
                return;
            }
            this.d.w();
            try {
                this.e.d(this, this.d);
            }
            finally {
                this.d.v();
            }
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final boolean j() {
            return this.g.f.s;
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final void k(View view0) {
            this.g.f.setCustomView(view0);
            this.f = new WeakReference(view0);
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final void l(int v) {
            this.m(this.g.a.getResources().getString(v));
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final void m(CharSequence charSequence0) {
            this.g.f.setSubtitle(charSequence0);
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final void n(int v) {
            this.o(this.g.a.getResources().getString(v));
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final void o(CharSequence charSequence0) {
            this.g.f.setTitle(charSequence0);
        }

        @Override  // androidx.appcompat.view.ActionMode
        public final void p(boolean z) {
            this.b = z;
            this.g.f.setTitleOptional(z);
        }
    }

    public Context a;
    public Context b;
    public ActionBarOverlayLayout c;
    public ActionBarContainer d;
    public DecorToolbar e;
    public ActionBarContextView f;
    public final View g;
    public boolean h;
    public ActionModeImpl i;
    public ActionModeImpl j;
    public androidx.appcompat.view.ActionMode.Callback k;
    public boolean l;
    public final ArrayList m;
    public int n;
    public boolean o;
    public boolean p;
    public boolean q;
    public boolean r;
    public ViewPropertyAnimatorCompatSet s;
    public boolean t;
    public boolean u;
    public final ViewPropertyAnimatorListener v;
    public final ViewPropertyAnimatorListener w;
    public final ViewPropertyAnimatorUpdateListener x;
    public static final AccelerateInterpolator y;
    public static final DecelerateInterpolator z;

    static {
        WindowDecorActionBar.y = new AccelerateInterpolator();
        WindowDecorActionBar.z = new DecelerateInterpolator();
    }

    public WindowDecorActionBar(Dialog dialog0) {
        new ArrayList();
        this.m = new ArrayList();
        this.n = 0;
        this.o = true;
        this.r = true;
        this.v = new ViewPropertyAnimatorListenerAdapter() {
            public final WindowDecorActionBar a;

            {
                this.a = windowDecorActionBar0;
            }

            @Override  // androidx.core.view.ViewPropertyAnimatorListener
            public final void c() {
                WindowDecorActionBar windowDecorActionBar0 = this.a;
                if(windowDecorActionBar0.o) {
                    View view0 = windowDecorActionBar0.g;
                    if(view0 != null) {
                        view0.setTranslationY(0.0f);
                        windowDecorActionBar0.d.setTranslationY(0.0f);
                    }
                }
                windowDecorActionBar0.d.setVisibility(8);
                windowDecorActionBar0.d.setTransitioning(false);
                windowDecorActionBar0.s = null;
                androidx.appcompat.view.ActionMode.Callback actionMode$Callback0 = windowDecorActionBar0.k;
                if(actionMode$Callback0 != null) {
                    actionMode$Callback0.a(windowDecorActionBar0.j);
                    windowDecorActionBar0.j = null;
                    windowDecorActionBar0.k = null;
                }
                ActionBarOverlayLayout actionBarOverlayLayout0 = windowDecorActionBar0.c;
                if(actionBarOverlayLayout0 != null) {
                    ViewCompat.A(actionBarOverlayLayout0);
                }
            }
        };
        this.w = new ViewPropertyAnimatorListenerAdapter() {
            public final WindowDecorActionBar a;

            {
                this.a = windowDecorActionBar0;
            }

            @Override  // androidx.core.view.ViewPropertyAnimatorListener
            public final void c() {
                this.a.s = null;
                this.a.d.requestLayout();
            }
        };
        this.x = new ViewPropertyAnimatorUpdateListener() {
            public final WindowDecorActionBar a;

            {
                this.a = windowDecorActionBar0;
            }

            @Override  // androidx.core.view.ViewPropertyAnimatorUpdateListener
            public final void a() {
                ((View)this.a.d.getParent()).invalidate();
            }
        };
        this.r(dialog0.getWindow().getDecorView());
    }

    public WindowDecorActionBar(boolean z, Activity activity0) {
        new ArrayList();
        this.m = new ArrayList();
        this.n = 0;
        this.o = true;
        this.r = true;
        this.v = new ViewPropertyAnimatorListenerAdapter() {
            public final WindowDecorActionBar a;

            {
                this.a = windowDecorActionBar0;
            }

            @Override  // androidx.core.view.ViewPropertyAnimatorListener
            public final void c() {
                WindowDecorActionBar windowDecorActionBar0 = this.a;
                if(windowDecorActionBar0.o) {
                    View view0 = windowDecorActionBar0.g;
                    if(view0 != null) {
                        view0.setTranslationY(0.0f);
                        windowDecorActionBar0.d.setTranslationY(0.0f);
                    }
                }
                windowDecorActionBar0.d.setVisibility(8);
                windowDecorActionBar0.d.setTransitioning(false);
                windowDecorActionBar0.s = null;
                androidx.appcompat.view.ActionMode.Callback actionMode$Callback0 = windowDecorActionBar0.k;
                if(actionMode$Callback0 != null) {
                    actionMode$Callback0.a(windowDecorActionBar0.j);
                    windowDecorActionBar0.j = null;
                    windowDecorActionBar0.k = null;
                }
                ActionBarOverlayLayout actionBarOverlayLayout0 = windowDecorActionBar0.c;
                if(actionBarOverlayLayout0 != null) {
                    ViewCompat.A(actionBarOverlayLayout0);
                }
            }
        };
        this.w = new ViewPropertyAnimatorListenerAdapter() {
            public final WindowDecorActionBar a;

            {
                this.a = windowDecorActionBar0;
            }

            @Override  // androidx.core.view.ViewPropertyAnimatorListener
            public final void c() {
                this.a.s = null;
                this.a.d.requestLayout();
            }
        };
        this.x = new ViewPropertyAnimatorUpdateListener() {
            public final WindowDecorActionBar a;

            {
                this.a = windowDecorActionBar0;
            }

            @Override  // androidx.core.view.ViewPropertyAnimatorUpdateListener
            public final void a() {
                ((View)this.a.d.getParent()).invalidate();
            }
        };
        View view0 = activity0.getWindow().getDecorView();
        this.r(view0);
        if(!z) {
            this.g = view0.findViewById(0x1020002);
        }
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final boolean b() {
        if(this.e != null && ((ToolbarWidgetWrapper)this.e).a.m()) {
            ((ToolbarWidgetWrapper)this.e).a();
            return true;
        }
        return false;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void c(boolean z) {
        if(z == this.l) {
            return;
        }
        this.l = z;
        ArrayList arrayList0 = this.m;
        if(arrayList0.size() <= 0) {
            return;
        }
        c.v(arrayList0.get(0));
        throw null;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final int d() {
        return ((ToolbarWidgetWrapper)this.e).b;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final Context e() {
        if(this.b == null) {
            TypedValue typedValue0 = new TypedValue();
            this.a.getTheme().resolveAttribute(0x7F040013, typedValue0, true);  // attr:actionBarWidgetTheme
            int v = typedValue0.resourceId;
            if(v != 0) {
                this.b = new ContextThemeWrapper(this.a, v);
                return this.b;
            }
            this.b = this.a;
        }
        return this.b;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void g() {
        Context context0 = this.a;
        new ActionBarPolicy().a = context0;  // 初始化器: Ljava/lang/Object;-><init>()V
        this.s(context0.getResources().getBoolean(0x7F050000));  // bool:abc_action_bar_embed_tabs
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final boolean i(int v, KeyEvent keyEvent0) {
        ActionModeImpl windowDecorActionBar$ActionModeImpl0 = this.i;
        if(windowDecorActionBar$ActionModeImpl0 == null) {
            return false;
        }
        MenuBuilder menuBuilder0 = windowDecorActionBar$ActionModeImpl0.d;
        if(menuBuilder0 != null) {
            menuBuilder0.setQwertyMode(KeyCharacterMap.load((keyEvent0 == null ? -1 : keyEvent0.getDeviceId())).getKeyboardType() != 1);
            return menuBuilder0.performShortcut(v, keyEvent0, 0);
        }
        return false;
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void l(boolean z) {
        if(!this.h) {
            this.h = true;
            ((ToolbarWidgetWrapper)this.e).d((z ? 4 : 0) & 4 | ((ToolbarWidgetWrapper)this.e).b & -5);
        }
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void m() {
        ((ToolbarWidgetWrapper)this.e).d(((ToolbarWidgetWrapper)this.e).b & -9);
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void n(boolean z) {
        this.t = z;
        if(!z) {
            ViewPropertyAnimatorCompatSet viewPropertyAnimatorCompatSet0 = this.s;
            if(viewPropertyAnimatorCompatSet0 != null) {
                viewPropertyAnimatorCompatSet0.a();
            }
        }
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final void o(CharSequence charSequence0) {
        ToolbarWidgetWrapper toolbarWidgetWrapper0 = (ToolbarWidgetWrapper)this.e;
        if(!toolbarWidgetWrapper0.g) {
            toolbarWidgetWrapper0.h = charSequence0;
            if((toolbarWidgetWrapper0.b & 8) != 0) {
                Toolbar toolbar0 = toolbarWidgetWrapper0.a;
                toolbar0.setTitle(charSequence0);
                if(toolbarWidgetWrapper0.g) {
                    ViewCompat.E(toolbar0.getRootView(), charSequence0);
                }
            }
        }
    }

    @Override  // androidx.appcompat.app.ActionBar
    public final ActionMode p(androidx.appcompat.view.ActionMode.Callback actionMode$Callback0) {
        ActionModeImpl windowDecorActionBar$ActionModeImpl0 = this.i;
        if(windowDecorActionBar$ActionModeImpl0 != null) {
            windowDecorActionBar$ActionModeImpl0.c();
        }
        this.c.setHideOnContentScrollEnabled(false);
        this.f.g();
        ActionModeImpl windowDecorActionBar$ActionModeImpl1 = new ActionModeImpl(this, this.f.getContext(), actionMode$Callback0);
        windowDecorActionBar$ActionModeImpl1.d.w();
        try {
            boolean z = windowDecorActionBar$ActionModeImpl1.e.b(windowDecorActionBar$ActionModeImpl1, windowDecorActionBar$ActionModeImpl1.d);
        }
        finally {
            windowDecorActionBar$ActionModeImpl1.d.v();
        }
        if(z) {
            this.i = windowDecorActionBar$ActionModeImpl1;
            windowDecorActionBar$ActionModeImpl1.i();
            this.f.e(windowDecorActionBar$ActionModeImpl1);
            this.q(true);
            return windowDecorActionBar$ActionModeImpl1;
        }
        return null;
    }

    public final void q(boolean z) {
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1;
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0;
        if(!z) {
            if(this.q) {
                this.q = false;
                this.t(false);
            }
        }
        else if(!this.q) {
            this.q = true;
            this.t(false);
        }
        if(this.d.isLaidOut()) {
            if(z) {
                viewPropertyAnimatorCompat0 = ((ToolbarWidgetWrapper)this.e).e(4, 100L);
                viewPropertyAnimatorCompat1 = this.f.h(0, 200L);
            }
            else {
                viewPropertyAnimatorCompat1 = ((ToolbarWidgetWrapper)this.e).e(0, 200L);
                viewPropertyAnimatorCompat0 = this.f.h(8, 100L);
            }
            ViewPropertyAnimatorCompatSet viewPropertyAnimatorCompatSet0 = new ViewPropertyAnimatorCompatSet();
            ArrayList arrayList0 = viewPropertyAnimatorCompatSet0.a;
            arrayList0.add(viewPropertyAnimatorCompat0);
            View view0 = (View)viewPropertyAnimatorCompat0.a.get();
            long v = view0 == null ? 0L : view0.animate().getDuration();
            View view1 = (View)viewPropertyAnimatorCompat1.a.get();
            if(view1 != null) {
                view1.animate().setStartDelay(v);
            }
            arrayList0.add(viewPropertyAnimatorCompat1);
            viewPropertyAnimatorCompatSet0.b();
            return;
        }
        if(z) {
            ((ToolbarWidgetWrapper)this.e).a.setVisibility(4);
            this.f.setVisibility(0);
            return;
        }
        ((ToolbarWidgetWrapper)this.e).a.setVisibility(0);
        this.f.setVisibility(8);
    }

    public final void r(View view0) {
        DecorToolbar decorToolbar0;
        ActionBarOverlayLayout actionBarOverlayLayout0 = (ActionBarOverlayLayout)view0.findViewById(0x7F0A0167);  // id:decor_content_parent
        this.c = actionBarOverlayLayout0;
        if(actionBarOverlayLayout0 != null) {
            actionBarOverlayLayout0.setActionBarVisibilityCallback(this);
        }
        View view1 = view0.findViewById(0x7F0A0051);  // id:action_bar
        if(view1 instanceof DecorToolbar) {
            decorToolbar0 = (DecorToolbar)view1;
        }
        else if(view1 instanceof Toolbar) {
            decorToolbar0 = ((Toolbar)view1).getWrapper();
        }
        else {
            throw new IllegalStateException("Can\'t make a decor toolbar out of " + (view1 == null ? "null" : view1.getClass().getSimpleName()));
        }
        this.e = decorToolbar0;
        this.f = (ActionBarContextView)view0.findViewById(0x7F0A005A);  // id:action_context_bar
        ActionBarContainer actionBarContainer0 = (ActionBarContainer)view0.findViewById(0x7F0A0053);  // id:action_bar_container
        this.d = actionBarContainer0;
        DecorToolbar decorToolbar1 = this.e;
        if(decorToolbar1 == null || this.f == null || actionBarContainer0 == null) {
            throw new IllegalStateException(this.getClass().getSimpleName() + " can only be used with a compatible window decor layout");
        }
        Context context0 = ((ToolbarWidgetWrapper)decorToolbar1).a.getContext();
        this.a = context0;
        if((((ToolbarWidgetWrapper)this.e).b & 4) != 0) {
            this.h = true;
        }
        ActionBarPolicy actionBarPolicy0 = new ActionBarPolicy();  // 初始化器: Ljava/lang/Object;-><init>()V
        actionBarPolicy0.a = context0;
        context0.getApplicationInfo();
        this.e.getClass();
        this.s(actionBarPolicy0.a.getResources().getBoolean(0x7F050000));  // bool:abc_action_bar_embed_tabs
        TypedArray typedArray0 = this.a.obtainStyledAttributes(null, R.styleable.a, 0x7F04000E, 0);  // attr:actionBarStyle
        if(typedArray0.getBoolean(14, false)) {
            ActionBarOverlayLayout actionBarOverlayLayout1 = this.c;
            if(!actionBarOverlayLayout1.g) {
                throw new IllegalStateException("Action bar must be in overlay mode (Window.FEATURE_OVERLAY_ACTION_BAR) to enable hide on content scroll");
            }
            this.u = true;
            actionBarOverlayLayout1.setHideOnContentScrollEnabled(true);
        }
        int v = typedArray0.getDimensionPixelSize(12, 0);
        if(v != 0) {
            ViewCompat.H(this.d, ((float)v));
        }
        typedArray0.recycle();
    }

    public final void s(boolean z) {
        if(!z) {
        }
        ((ToolbarWidgetWrapper)this.e).getClass();
        this.e.getClass();
        ((ToolbarWidgetWrapper)this.e).a.setCollapsible(false);
        this.c.setHasNonEmbeddedTabs(false);
    }

    public final void t(boolean z) {
        boolean z2;
        boolean z1 = this.p;
        if(this.q) {
            z2 = true;
        }
        else if(z1) {
            z2 = false;
        }
        else {
            z2 = true;
        }
        ViewPropertyAnimatorUpdateListener viewPropertyAnimatorUpdateListener0 = this.x;
        View view0 = this.g;
        h h0 = null;
        if(z2) {
            if(!this.r) {
                this.r = true;
                ViewPropertyAnimatorCompatSet viewPropertyAnimatorCompatSet0 = this.s;
                if(viewPropertyAnimatorCompatSet0 != null) {
                    viewPropertyAnimatorCompatSet0.a();
                }
                this.d.setVisibility(0);
                ViewPropertyAnimatorListener viewPropertyAnimatorListener0 = this.w;
                if(this.n != 0 || !this.t && !z) {
                    this.d.setAlpha(1.0f);
                    this.d.setTranslationY(0.0f);
                    if(this.o && view0 != null) {
                        view0.setTranslationY(0.0f);
                    }
                    ((androidx.appcompat.app.WindowDecorActionBar.2)viewPropertyAnimatorListener0).c();
                }
                else {
                    this.d.setTranslationY(0.0f);
                    float f = (float)(-this.d.getHeight());
                    if(z) {
                        int[] arr_v = {0, 0};
                        this.d.getLocationInWindow(arr_v);
                        f -= (float)arr_v[1];
                    }
                    this.d.setTranslationY(f);
                    ViewPropertyAnimatorCompatSet viewPropertyAnimatorCompatSet1 = new ViewPropertyAnimatorCompatSet();
                    ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = ViewCompat.a(this.d);
                    viewPropertyAnimatorCompat0.e(0.0f);
                    View view1 = (View)viewPropertyAnimatorCompat0.a.get();
                    if(view1 != null) {
                        if(viewPropertyAnimatorUpdateListener0 != null) {
                            h0 = new h(0, viewPropertyAnimatorUpdateListener0, view1);
                        }
                        view1.animate().setUpdateListener(h0);
                    }
                    ArrayList arrayList0 = viewPropertyAnimatorCompatSet1.a;
                    if(!viewPropertyAnimatorCompatSet1.e) {
                        arrayList0.add(viewPropertyAnimatorCompat0);
                    }
                    if(this.o && view0 != null) {
                        view0.setTranslationY(f);
                        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1 = ViewCompat.a(view0);
                        viewPropertyAnimatorCompat1.e(0.0f);
                        if(!viewPropertyAnimatorCompatSet1.e) {
                            arrayList0.add(viewPropertyAnimatorCompat1);
                        }
                    }
                    DecelerateInterpolator decelerateInterpolator0 = WindowDecorActionBar.z;
                    boolean z3 = viewPropertyAnimatorCompatSet1.e;
                    if(!z3) {
                        viewPropertyAnimatorCompatSet1.c = decelerateInterpolator0;
                    }
                    if(!z3) {
                        viewPropertyAnimatorCompatSet1.b = 0xFAL;
                    }
                    if(!z3) {
                        viewPropertyAnimatorCompatSet1.d = (ViewPropertyAnimatorListenerAdapter)viewPropertyAnimatorListener0;
                    }
                    this.s = viewPropertyAnimatorCompatSet1;
                    viewPropertyAnimatorCompatSet1.b();
                }
                ActionBarOverlayLayout actionBarOverlayLayout0 = this.c;
                if(actionBarOverlayLayout0 != null) {
                    ViewCompat.A(actionBarOverlayLayout0);
                }
            }
        }
        else if(this.r) {
            this.r = false;
            ViewPropertyAnimatorCompatSet viewPropertyAnimatorCompatSet2 = this.s;
            if(viewPropertyAnimatorCompatSet2 != null) {
                viewPropertyAnimatorCompatSet2.a();
            }
            ViewPropertyAnimatorListener viewPropertyAnimatorListener1 = this.v;
            if(this.n == 0 && (this.t || z)) {
                this.d.setAlpha(1.0f);
                this.d.setTransitioning(true);
                ViewPropertyAnimatorCompatSet viewPropertyAnimatorCompatSet3 = new ViewPropertyAnimatorCompatSet();
                float f1 = (float)(-this.d.getHeight());
                if(z) {
                    int[] arr_v1 = {0, 0};
                    this.d.getLocationInWindow(arr_v1);
                    f1 -= (float)arr_v1[1];
                }
                ViewPropertyAnimatorCompat viewPropertyAnimatorCompat2 = ViewCompat.a(this.d);
                viewPropertyAnimatorCompat2.e(f1);
                View view2 = (View)viewPropertyAnimatorCompat2.a.get();
                if(view2 != null) {
                    if(viewPropertyAnimatorUpdateListener0 != null) {
                        h0 = new h(0, viewPropertyAnimatorUpdateListener0, view2);
                    }
                    view2.animate().setUpdateListener(h0);
                }
                ArrayList arrayList1 = viewPropertyAnimatorCompatSet3.a;
                if(!viewPropertyAnimatorCompatSet3.e) {
                    arrayList1.add(viewPropertyAnimatorCompat2);
                }
                if(this.o && view0 != null) {
                    ViewPropertyAnimatorCompat viewPropertyAnimatorCompat3 = ViewCompat.a(view0);
                    viewPropertyAnimatorCompat3.e(f1);
                    if(!viewPropertyAnimatorCompatSet3.e) {
                        arrayList1.add(viewPropertyAnimatorCompat3);
                    }
                }
                AccelerateInterpolator accelerateInterpolator0 = WindowDecorActionBar.y;
                boolean z4 = viewPropertyAnimatorCompatSet3.e;
                if(!z4) {
                    viewPropertyAnimatorCompatSet3.c = accelerateInterpolator0;
                }
                if(!z4) {
                    viewPropertyAnimatorCompatSet3.b = 0xFAL;
                }
                if(!z4) {
                    viewPropertyAnimatorCompatSet3.d = (ViewPropertyAnimatorListenerAdapter)viewPropertyAnimatorListener1;
                }
                this.s = viewPropertyAnimatorCompatSet3;
                viewPropertyAnimatorCompatSet3.b();
                return;
            }
            ((androidx.appcompat.app.WindowDecorActionBar.1)viewPropertyAnimatorListener1).c();
        }
    }
}


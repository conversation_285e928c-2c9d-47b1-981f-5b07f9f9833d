package androidx.appcompat.app;

import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.widget.ActionBarOverlayLayout;
import androidx.appcompat.widget.ContentFrameLayout.OnAttachListener;
import androidx.appcompat.widget.DecorContentParent;
import androidx.core.view.ViewPropertyAnimatorCompat;

class AppCompatDelegateImpl.5 implements OnAttachListener {
    public final AppCompatDelegateImpl a;

    public AppCompatDelegateImpl.5(AppCompatDelegateImpl appCompatDelegateImpl0) {
        this.a = appCompatDelegateImpl0;
    }

    @Override  // androidx.appcompat.widget.ContentFrameLayout$OnAttachListener
    public final void onDetachedFromWindow() {
        AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
        DecorContentParent decorContentParent0 = appCompatDelegateImpl0.r;
        if(decorContentParent0 != null) {
            ((ActionBarOverlayLayout)decorContentParent0).c();
        }
        if(appCompatDelegateImpl0.w != null) {
            appCompatDelegateImpl0.l.getDecorView().removeCallbacks(appCompatDelegateImpl0.x);
            if(appCompatDelegateImpl0.w.isShowing()) {
                try {
                    appCompatDelegateImpl0.w.dismiss();
                }
                catch(IllegalArgumentException unused_ex) {
                }
            }
            appCompatDelegateImpl0.w = null;
        }
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = appCompatDelegateImpl0.y;
        if(viewPropertyAnimatorCompat0 != null) {
            viewPropertyAnimatorCompat0.b();
        }
        MenuBuilder menuBuilder0 = appCompatDelegateImpl0.A(0).h;
        if(menuBuilder0 != null) {
            menuBuilder0.c(true);
        }
    }
}


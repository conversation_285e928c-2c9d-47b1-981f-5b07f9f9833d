package androidx.appcompat.app;

import android.view.View;
import android.widget.PopupWindow;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorListenerAdapter;

class AppCompatDelegateImpl.ActionModeCallbackWrapperV9.1 extends ViewPropertyAnimatorListenerAdapter {
    public final ActionModeCallbackWrapperV9 a;

    public AppCompatDelegateImpl.ActionModeCallbackWrapperV9.1(ActionModeCallbackWrapperV9 appCompatDelegateImpl$ActionModeCallbackWrapperV90) {
        this.a = appCompatDelegateImpl$ActionModeCallbackWrapperV90;
    }

    @Override  // androidx.core.view.ViewPropertyAnimatorListener
    public final void c() {
        this.a.b.v.setVisibility(8);
        AppCompatDelegateImpl appCompatDelegateImpl0 = this.a.b;
        PopupWindow popupWindow0 = appCompatDelegateImpl0.w;
        if(popupWindow0 != null) {
            popupWindow0.dismiss();
        }
        else if(appCompatDelegateImpl0.v.getParent() instanceof View) {
            ViewCompat.A(((View)appCompatDelegateImpl0.v.getParent()));
        }
        appCompatDelegateImpl0.v.g();
        appCompatDelegateImpl0.y.d(null);
        appCompatDelegateImpl0.y = null;
        ViewCompat.A(appCompatDelegateImpl0.A);
    }
}


package androidx.appcompat.app;

import a.a;
import android.content.Context;
import android.content.ContextWrapper;
import android.util.AttributeSet;
import android.view.View.OnClickListener;
import android.view.View;
import androidx.appcompat.widget.AppCompatAutoCompleteTextView;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.collection.SimpleArrayMap;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class AppCompatViewInflater {
    static class DeclaredOnClickListener implements View.OnClickListener {
        public final View a;
        public final String b;
        public Method c;
        public Context d;

        public DeclaredOnClickListener(View view0, String s) {
            this.a = view0;
            this.b = s;
        }

        @Override  // android.view.View$OnClickListener
        public final void onClick(View view0) {
            if(this.c == null) {
                View view1 = this.a;
                Context context0 = view1.getContext();
                while(true) {
                    String s = this.b;
                    if(context0 != null) {
                        try {
                            if(!context0.isRestricted()) {
                                Method method0 = context0.getClass().getMethod(s, View.class);
                                if(method0 != null) {
                                    this.c = method0;
                                    this.d = context0;
                                    break;
                                }
                            }
                        }
                        catch(NoSuchMethodException unused_ex) {
                        }
                        if(context0 instanceof ContextWrapper) {
                            context0 = ((ContextWrapper)context0).getBaseContext();
                            continue;
                        }
                        context0 = null;
                        continue;
                    }
                    int v = view1.getId();
                    String s1 = v == -1 ? "" : " with id \'" + view1.getContext().getResources().getResourceEntryName(v) + "\'";
                    StringBuilder stringBuilder0 = a.y("Could not find method ", s, "(View) in a parent or ancestor Context for android:onClick attribute defined on view ");
                    stringBuilder0.append(view1.getClass());
                    stringBuilder0.append(s1);
                    throw new IllegalStateException(stringBuilder0.toString());
                }
            }
            try {
                this.c.invoke(this.d, view0);
            }
            catch(IllegalAccessException illegalAccessException0) {
                throw new IllegalStateException("Could not execute non-public method for android:onClick", illegalAccessException0);
            }
            catch(InvocationTargetException invocationTargetException0) {
                throw new IllegalStateException("Could not execute method for android:onClick", invocationTargetException0);
            }
        }
    }

    public final Object[] a;
    public static final Class[] b;
    public static final int[] c;
    public static final int[] d;
    public static final int[] e;
    public static final int[] f;
    public static final String[] g;
    public static final SimpleArrayMap h;

    static {
        AppCompatViewInflater.b = new Class[]{Context.class, AttributeSet.class};
        AppCompatViewInflater.c = new int[]{0x101026F};
        AppCompatViewInflater.d = new int[]{0x1010580};
        AppCompatViewInflater.e = new int[]{0x101057C};
        AppCompatViewInflater.f = new int[]{0x1010574};
        AppCompatViewInflater.g = new String[]{"android.widget.", "android.view.", "android.webkit."};
        AppCompatViewInflater.h = new SimpleArrayMap();
    }

    public AppCompatViewInflater() {
        this.a = new Object[2];
    }

    public AppCompatAutoCompleteTextView a(Context context0, AttributeSet attributeSet0) {
        return new AppCompatAutoCompleteTextView(context0, attributeSet0);
    }

    public AppCompatButton b(Context context0, AttributeSet attributeSet0) {
        return new AppCompatButton(context0, attributeSet0);
    }

    public AppCompatCheckBox c(Context context0, AttributeSet attributeSet0) {
        return new AppCompatCheckBox(context0, attributeSet0, 0x7F040105);  // attr:checkboxStyle
    }

    public AppCompatRadioButton d(Context context0, AttributeSet attributeSet0) {
        return new AppCompatRadioButton(context0, attributeSet0);
    }

    public AppCompatTextView e(Context context0, AttributeSet attributeSet0) {
        return new AppCompatTextView(context0, attributeSet0);
    }

    public final View f(Context context0, String s, String s1) {
        SimpleArrayMap simpleArrayMap0 = AppCompatViewInflater.h;
        Constructor constructor0 = (Constructor)simpleArrayMap0.getOrDefault(s, null);
        try {
            if(constructor0 == null) {
                constructor0 = Class.forName((s1 == null ? s : s1 + s), false, context0.getClassLoader()).asSubclass(View.class).getConstructor(AppCompatViewInflater.b);
                simpleArrayMap0.put(s, constructor0);
            }
            constructor0.setAccessible(true);
            return (View)constructor0.newInstance(this.a);
        }
        catch(Exception unused_ex) {
            return null;
        }
    }
}


package androidx.appcompat.app;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.view.Window;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner;
import androidx.appcompat.view.ActionMode.Callback;
import androidx.appcompat.view.ActionMode;
import androidx.appcompat.view.SupportMenuInflater;
import androidx.appcompat.widget.AppCompatDrawableManager;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.AppLocalesStorageHelper;
import androidx.core.app.NavUtils;
import androidx.core.app.TaskStackBuilder.SupportParentable;
import androidx.core.app.TaskStackBuilder;
import androidx.core.content.res.ResourcesCompat.ThemeCompat;
import androidx.core.os.LocaleListCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;
import java.util.Objects;
import jeb.synthetic.FIN;
import kotlin.jvm.internal.Intrinsics;

public abstract class AppCompatActivity extends FragmentActivity implements AppCompatCallback, SupportParentable {
    private static final String DELEGATE_TAG = "androidx:appcompat";
    private AppCompatDelegate mDelegate;
    private Resources mResources;

    public AppCompatActivity() {
        this.getSavedStateRegistry().c("androidx:appcompat", new AppCompatActivity.1(this));
        this.addOnContextAvailableListener(new AppCompatActivity.2(this));
    }

    @Override  // androidx.activity.ComponentActivity
    public void addContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        this.x();
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        appCompatDelegateImpl0.w();
        ((ViewGroup)appCompatDelegateImpl0.A.findViewById(0x1020002)).addView(view0, viewGroup$LayoutParams0);
        appCompatDelegateImpl0.m.a(appCompatDelegateImpl0.l.getCallback());
    }

    @Override  // android.app.Activity
    public void attachBaseContext(Context context0) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        appCompatDelegateImpl0.O = true;
        int v = appCompatDelegateImpl0.D((appCompatDelegateImpl0.S == -100 ? AppCompatDelegate.b : appCompatDelegateImpl0.S), context0);
        if(AppCompatDelegate.d(context0) && AppCompatDelegate.d(context0)) {
            if(Build.VERSION.SDK_INT < 33) {
                Object object0 = AppCompatDelegate.i;
                __monitor_enter(object0);
                int v1 = FIN.finallyOpen$NT();
                LocaleListCompat localeListCompat0 = AppCompatDelegate.c;
                if(localeListCompat0 == null) {
                    if(AppCompatDelegate.d == null) {
                        AppCompatDelegate.d = LocaleListCompat.b(AppLocalesStorageHelper.b(context0));
                    }
                    if(AppCompatDelegate.d.d()) {
                        FIN.finallyExec$NT(v1);
                    }
                    else {
                        AppCompatDelegate.c = AppCompatDelegate.d;
                        goto label_24;
                    }
                }
                else {
                    if(!localeListCompat0.equals(AppCompatDelegate.d)) {
                        AppCompatDelegate.d = AppCompatDelegate.c;
                        AppLocalesStorageHelper.a(context0, AppCompatDelegate.c.f());
                    }
                label_24:
                    FIN.finallyCodeBegin$NT(v1);
                    __monitor_exit(object0);
                    FIN.finallyCodeEnd$NT(v1);
                }
            }
            else if(!AppCompatDelegate.f) {
                a a0 = new a(context0);
                AppCompatDelegate.a.execute(a0);
            }
        }
        LocaleListCompat localeListCompat1 = AppCompatDelegateImpl.p(context0);
        Configuration configuration0 = null;
        if(context0 instanceof ContextThemeWrapper) {
            Configuration configuration1 = AppCompatDelegateImpl.t(context0, v, localeListCompat1, null, false);
            try {
                ((ContextThemeWrapper)context0).applyOverrideConfiguration(configuration1);
                goto label_122;
            }
            catch(IllegalStateException unused_ex) {
            }
            goto label_33;
        }
        else {
        label_33:
            if(context0 instanceof androidx.appcompat.view.ContextThemeWrapper) {
                Configuration configuration2 = AppCompatDelegateImpl.t(context0, v, localeListCompat1, null, false);
                try {
                    ((androidx.appcompat.view.ContextThemeWrapper)context0).a(configuration2);
                    goto label_122;
                }
                catch(IllegalStateException unused_ex) {
                }
                goto label_37;
            }
            else {
            label_37:
                if(AppCompatDelegateImpl.w0) {
                    Configuration configuration3 = new Configuration();
                    configuration3.uiMode = -1;
                    configuration3.fontScale = 0.0f;
                    Configuration configuration4 = context0.createConfigurationContext(configuration3).getResources().getConfiguration();
                    Configuration configuration5 = context0.getResources().getConfiguration();
                    configuration4.uiMode = configuration5.uiMode;
                    if(!configuration4.equals(configuration5)) {
                        configuration0 = new Configuration();
                        configuration0.fontScale = 0.0f;
                        if(configuration4.diff(configuration5) != 0) {
                            float f = configuration5.fontScale;
                            if(configuration4.fontScale != f) {
                                configuration0.fontScale = f;
                            }
                            int v2 = configuration5.mcc;
                            if(configuration4.mcc != v2) {
                                configuration0.mcc = v2;
                            }
                            int v3 = configuration5.mnc;
                            if(configuration4.mnc != v3) {
                                configuration0.mnc = v3;
                            }
                            int v4 = Build.VERSION.SDK_INT;
                            if(v4 >= 24) {
                                Api24Impl.a(configuration4, configuration5, configuration0);
                            }
                            else if(!Objects.equals(configuration4.locale, configuration5.locale)) {
                                configuration0.locale = configuration5.locale;
                            }
                            int v5 = configuration5.touchscreen;
                            if(configuration4.touchscreen != v5) {
                                configuration0.touchscreen = v5;
                            }
                            int v6 = configuration5.keyboard;
                            if(configuration4.keyboard != v6) {
                                configuration0.keyboard = v6;
                            }
                            int v7 = configuration5.keyboardHidden;
                            if(configuration4.keyboardHidden != v7) {
                                configuration0.keyboardHidden = v7;
                            }
                            int v8 = configuration5.navigation;
                            if(configuration4.navigation != v8) {
                                configuration0.navigation = v8;
                            }
                            int v9 = configuration5.navigationHidden;
                            if(configuration4.navigationHidden != v9) {
                                configuration0.navigationHidden = v9;
                            }
                            int v10 = configuration5.orientation;
                            if(configuration4.orientation != v10) {
                                configuration0.orientation = v10;
                            }
                            int v11 = configuration5.screenLayout & 15;
                            if((configuration4.screenLayout & 15) != v11) {
                                configuration0.screenLayout |= v11;
                            }
                            int v12 = configuration5.screenLayout & 0xC0;
                            if((configuration4.screenLayout & 0xC0) != v12) {
                                configuration0.screenLayout |= v12;
                            }
                            int v13 = configuration5.screenLayout & 0x30;
                            if((configuration4.screenLayout & 0x30) != v13) {
                                configuration0.screenLayout |= v13;
                            }
                            int v14 = configuration5.screenLayout & 0x300;
                            if((configuration4.screenLayout & 0x300) != v14) {
                                configuration0.screenLayout |= v14;
                            }
                            if(v4 >= 26) {
                                if((j2.a.a(configuration4) & 3) != (j2.a.a(configuration5) & 3)) {
                                    j2.a.v(configuration0, j2.a.a(configuration0) | j2.a.a(configuration5) & 3);
                                }
                                if((j2.a.a(configuration4) & 12) != (j2.a.a(configuration5) & 12)) {
                                    j2.a.v(configuration0, j2.a.a(configuration0) | j2.a.a(configuration5) & 12);
                                }
                            }
                            int v15 = configuration5.uiMode & 15;
                            if((configuration4.uiMode & 15) != v15) {
                                configuration0.uiMode |= v15;
                            }
                            int v16 = configuration5.uiMode & 0x30;
                            if((configuration4.uiMode & 0x30) != v16) {
                                configuration0.uiMode |= v16;
                            }
                            int v17 = configuration5.screenWidthDp;
                            if(configuration4.screenWidthDp != v17) {
                                configuration0.screenWidthDp = v17;
                            }
                            int v18 = configuration5.screenHeightDp;
                            if(configuration4.screenHeightDp != v18) {
                                configuration0.screenHeightDp = v18;
                            }
                            int v19 = configuration5.smallestScreenWidthDp;
                            if(configuration4.smallestScreenWidthDp != v19) {
                                configuration0.smallestScreenWidthDp = v19;
                            }
                            int v20 = configuration5.densityDpi;
                            if(configuration4.densityDpi != v20) {
                                configuration0.densityDpi = v20;
                            }
                        }
                    }
                    Configuration configuration6 = AppCompatDelegateImpl.t(context0, v, localeListCompat1, configuration0, true);
                    androidx.appcompat.view.ContextThemeWrapper contextThemeWrapper0 = new androidx.appcompat.view.ContextThemeWrapper(context0, 0x7F1502A3);  // style:Theme.AppCompat.Empty
                    contextThemeWrapper0.a(configuration6);
                    try {
                        if(context0.getTheme() != null) {
                            goto label_120;
                        }
                    }
                    catch(NullPointerException unused_ex) {
                    }
                    goto label_121;
                label_120:
                    ThemeCompat.a(contextThemeWrapper0.getTheme());
                label_121:
                    context0 = contextThemeWrapper0;
                }
            }
        }
    label_122:
        super.attachBaseContext(context0);
    }

    @Override  // android.app.Activity
    public void closeOptionsMenu() {
        ActionBar actionBar0 = this.getSupportActionBar();
        if(this.getWindow().hasFeature(0) && (actionBar0 == null || !actionBar0.a())) {
            super.closeOptionsMenu();
        }
    }

    @Override  // androidx.core.app.ComponentActivity
    public boolean dispatchKeyEvent(KeyEvent keyEvent0) {
        int v = keyEvent0.getKeyCode();
        ActionBar actionBar0 = this.getSupportActionBar();
        return v != 82 || actionBar0 == null || !actionBar0.j(keyEvent0) ? super.dispatchKeyEvent(keyEvent0) : true;
    }

    @Override  // android.app.Activity
    public View findViewById(int v) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        appCompatDelegateImpl0.w();
        return appCompatDelegateImpl0.l.findViewById(v);
    }

    public AppCompatDelegate getDelegate() {
        if(this.mDelegate == null) {
            this.mDelegate = new AppCompatDelegateImpl(this, null, this, this);
        }
        return this.mDelegate;
    }

    public ActionBarDrawerToggle.Delegate getDrawerToggleDelegate() {
        ((AppCompatDelegateImpl)this.getDelegate()).getClass();
        return new ActionBarDrawableToggleImpl();  // 初始化器: Ljava/lang/Object;-><init>()V
    }

    @Override  // android.app.Activity
    public MenuInflater getMenuInflater() {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        if(appCompatDelegateImpl0.p == null) {
            appCompatDelegateImpl0.B();
            appCompatDelegateImpl0.p = new SupportMenuInflater((appCompatDelegateImpl0.o == null ? appCompatDelegateImpl0.k : appCompatDelegateImpl0.o.e()));
        }
        return appCompatDelegateImpl0.p;
    }

    @Override  // android.view.ContextThemeWrapper
    public Resources getResources() {
        return this.mResources == null ? super.getResources() : this.mResources;
    }

    public ActionBar getSupportActionBar() {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        appCompatDelegateImpl0.B();
        return appCompatDelegateImpl0.o;
    }

    @Override  // androidx.core.app.TaskStackBuilder$SupportParentable
    public Intent getSupportParentActivityIntent() {
        return NavUtils.a(this);
    }

    @Override  // android.app.Activity
    public void invalidateOptionsMenu() {
        this.getDelegate().c();
    }

    @Override  // androidx.activity.ComponentActivity
    public void onConfigurationChanged(Configuration configuration0) {
        super.onConfigurationChanged(configuration0);
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        if(appCompatDelegateImpl0.F && appCompatDelegateImpl0.z) {
            appCompatDelegateImpl0.B();
            ActionBar actionBar0 = appCompatDelegateImpl0.o;
            if(actionBar0 != null) {
                actionBar0.g();
            }
        }
        AppCompatDrawableManager appCompatDrawableManager0 = AppCompatDrawableManager.a();
        synchronized(appCompatDrawableManager0) {
            appCompatDrawableManager0.a.k(appCompatDelegateImpl0.k);
        }
        appCompatDelegateImpl0.R = new Configuration(appCompatDelegateImpl0.k.getResources().getConfiguration());
        appCompatDelegateImpl0.n(false, false);
        if(this.mResources != null) {
            Configuration configuration1 = super.getResources().getConfiguration();
            DisplayMetrics displayMetrics0 = super.getResources().getDisplayMetrics();
            this.mResources.updateConfiguration(configuration1, displayMetrics0);
        }
    }

    @Override  // android.app.Activity
    public void onContentChanged() {
    }

    public void onCreateSupportNavigateUpTaskStack(TaskStackBuilder taskStackBuilder0) {
        taskStackBuilder0.getClass();
        Intent intent0 = this.getSupportParentActivityIntent();
        if(intent0 == null) {
            intent0 = NavUtils.a(this);
        }
        if(intent0 != null) {
            ComponentName componentName0 = intent0.getComponent();
            if(componentName0 == null) {
                componentName0 = intent0.resolveActivity(taskStackBuilder0.b.getPackageManager());
            }
            taskStackBuilder0.a(componentName0);
            taskStackBuilder0.a.add(intent0);
        }
    }

    @Override  // androidx.fragment.app.FragmentActivity
    public void onDestroy() {
        super.onDestroy();
        this.getDelegate().f();
    }

    @Override  // android.app.Activity
    public boolean onKeyDown(int v, KeyEvent keyEvent0) {
        if(Build.VERSION.SDK_INT < 26 && !keyEvent0.isCtrlPressed() && !KeyEvent.metaStateHasNoModifiers(keyEvent0.getMetaState()) && keyEvent0.getRepeatCount() == 0 && !KeyEvent.isModifierKey(keyEvent0.getKeyCode())) {
            Window window0 = this.getWindow();
            return window0 == null || window0.getDecorView() == null || !window0.getDecorView().dispatchKeyShortcutEvent(keyEvent0) ? super.onKeyDown(v, keyEvent0) : true;
        }
        return super.onKeyDown(v, keyEvent0);
    }

    public void onLocalesChanged(LocaleListCompat localeListCompat0) {
    }

    @Override  // androidx.fragment.app.FragmentActivity
    public final boolean onMenuItemSelected(int v, MenuItem menuItem0) {
        if(super.onMenuItemSelected(v, menuItem0)) {
            return true;
        }
        ActionBar actionBar0 = this.getSupportActionBar();
        return menuItem0.getItemId() != 0x102002C || actionBar0 == null || (actionBar0.d() & 4) == 0 ? false : this.onSupportNavigateUp();
    }

    @Override  // android.app.Activity
    public boolean onMenuOpened(int v, Menu menu0) {
        return super.onMenuOpened(v, menu0);
    }

    public void onNightModeChanged(int v) {
    }

    @Override  // androidx.activity.ComponentActivity
    public void onPanelClosed(int v, Menu menu0) {
        super.onPanelClosed(v, menu0);
    }

    @Override  // android.app.Activity
    public void onPostCreate(Bundle bundle0) {
        super.onPostCreate(bundle0);
        ((AppCompatDelegateImpl)this.getDelegate()).w();
    }

    @Override  // androidx.fragment.app.FragmentActivity
    public void onPostResume() {
        super.onPostResume();
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        appCompatDelegateImpl0.B();
        ActionBar actionBar0 = appCompatDelegateImpl0.o;
        if(actionBar0 != null) {
            actionBar0.n(true);
        }
    }

    public void onPrepareSupportNavigateUpTaskStack(TaskStackBuilder taskStackBuilder0) {
    }

    @Override  // androidx.fragment.app.FragmentActivity
    public void onStart() {
        super.onStart();
        ((AppCompatDelegateImpl)this.getDelegate()).n(true, false);
    }

    @Override  // androidx.fragment.app.FragmentActivity
    public void onStop() {
        super.onStop();
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        appCompatDelegateImpl0.B();
        ActionBar actionBar0 = appCompatDelegateImpl0.o;
        if(actionBar0 != null) {
            actionBar0.n(false);
        }
    }

    @Override  // androidx.appcompat.app.AppCompatCallback
    public void onSupportActionModeFinished(ActionMode actionMode0) {
    }

    @Override  // androidx.appcompat.app.AppCompatCallback
    public void onSupportActionModeStarted(ActionMode actionMode0) {
    }

    @Deprecated
    public void onSupportContentChanged() {
    }

    public boolean onSupportNavigateUp() {
        Intent intent0 = this.getSupportParentActivityIntent();
        if(intent0 != null) {
            if(this.supportShouldUpRecreateTask(intent0)) {
                TaskStackBuilder taskStackBuilder0 = new TaskStackBuilder(this);
                this.onCreateSupportNavigateUpTaskStack(taskStackBuilder0);
                taskStackBuilder0.d();
                try {
                    this.finishAffinity();
                }
                catch(IllegalStateException unused_ex) {
                    this.finish();
                }
                return true;
            }
            this.supportNavigateUpTo(intent0);
            return true;
        }
        return false;
    }

    @Override  // android.app.Activity
    public void onTitleChanged(CharSequence charSequence0, int v) {
        super.onTitleChanged(charSequence0, v);
        this.getDelegate().l(charSequence0);
    }

    @Override  // androidx.appcompat.app.AppCompatCallback
    public ActionMode onWindowStartingSupportActionMode(Callback actionMode$Callback0) {
        return null;
    }

    @Override  // android.app.Activity
    public void openOptionsMenu() {
        ActionBar actionBar0 = this.getSupportActionBar();
        if(this.getWindow().hasFeature(0) && (actionBar0 == null || !actionBar0.k())) {
            super.openOptionsMenu();
        }
    }

    @Override  // androidx.activity.ComponentActivity
    public void setContentView(int v) {
        this.x();
        this.getDelegate().i(v);
    }

    @Override  // androidx.activity.ComponentActivity
    public void setContentView(View view0) {
        this.x();
        this.getDelegate().j(view0);
    }

    @Override  // androidx.activity.ComponentActivity
    public void setContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        this.x();
        this.getDelegate().k(view0, viewGroup$LayoutParams0);
    }

    public void setSupportActionBar(Toolbar toolbar0) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.getDelegate();
        if(appCompatDelegateImpl0.j instanceof Activity) {
            appCompatDelegateImpl0.B();
            ActionBar actionBar0 = appCompatDelegateImpl0.o;
            if(actionBar0 instanceof WindowDecorActionBar) {
                throw new IllegalStateException("This Activity already has an action bar supplied by the window decor. Do not request Window.FEATURE_SUPPORT_ACTION_BAR and set windowActionBar to false in your theme to use a Toolbar instead.");
            }
            appCompatDelegateImpl0.p = null;
            if(actionBar0 != null) {
                actionBar0.h();
            }
            appCompatDelegateImpl0.o = null;
            if(toolbar0 == null) {
                appCompatDelegateImpl0.m.b = null;
            }
            else {
                ToolbarActionBar toolbarActionBar0 = new ToolbarActionBar(toolbar0, (appCompatDelegateImpl0.j instanceof Activity ? ((Activity)appCompatDelegateImpl0.j).getTitle() : appCompatDelegateImpl0.q), appCompatDelegateImpl0.m);
                appCompatDelegateImpl0.o = toolbarActionBar0;
                appCompatDelegateImpl0.m.b = toolbarActionBar0.c;
                toolbar0.setBackInvokedCallbackEnabled(true);
            }
            appCompatDelegateImpl0.c();
        }
    }

    @Deprecated
    public void setSupportProgress(int v) {
    }

    @Deprecated
    public void setSupportProgressBarIndeterminate(boolean z) {
    }

    @Deprecated
    public void setSupportProgressBarIndeterminateVisibility(boolean z) {
    }

    @Deprecated
    public void setSupportProgressBarVisibility(boolean z) {
    }

    @Override  // android.app.Activity
    public void setTheme(int v) {
        super.setTheme(v);
        ((AppCompatDelegateImpl)this.getDelegate()).T = v;
    }

    public ActionMode startSupportActionMode(Callback actionMode$Callback0) {
        return this.getDelegate().m(actionMode$Callback0);
    }

    @Override  // androidx.fragment.app.FragmentActivity
    public void supportInvalidateOptionsMenu() {
        this.getDelegate().c();
    }

    public void supportNavigateUpTo(Intent intent0) {
        this.navigateUpTo(intent0);
    }

    public boolean supportRequestWindowFeature(int v) {
        return this.getDelegate().h(v);
    }

    public boolean supportShouldUpRecreateTask(Intent intent0) {
        return this.shouldUpRecreateTask(intent0);
    }

    public final void x() {
        ViewTreeLifecycleOwner.a(this.getWindow().getDecorView(), this);
        View view0 = this.getWindow().getDecorView();
        Intrinsics.f(view0, "<this>");
        view0.setTag(0x7F0A0501, this);  // id:view_tree_view_model_store_owner
        ViewTreeSavedStateRegistryOwner.a(this.getWindow().getDecorView(), this);
        ViewTreeOnBackPressedDispatcherOwner.a(this.getWindow().getDecorView(), this);
    }
}


package androidx.appcompat.app;

import android.util.LongSparseArray;
import java.lang.reflect.Field;

abstract class ResourcesFlusher {
    public static Field a;
    public static boolean b;
    public static Class c;
    public static boolean d;
    public static Field e;
    public static boolean f;
    public static Field g;
    public static boolean h;

    public static void a(Object object0) {
        LongSparseArray longSparseArray0;
        if(!ResourcesFlusher.d) {
            try {
                ResourcesFlusher.c = Class.forName("android.content.res.ThemedResourceCache");
            }
            catch(ClassNotFoundException unused_ex) {
            }
            ResourcesFlusher.d = true;
        }
        Class class0 = ResourcesFlusher.c;
        if(class0 == null) {
            return;
        }
        if(!ResourcesFlusher.f) {
            try {
                Field field0 = class0.getDeclaredField("mUnthemedEntries");
                ResourcesFlusher.e = field0;
                field0.setAccessible(true);
            }
            catch(NoSuchFieldException unused_ex) {
            }
            ResourcesFlusher.f = true;
        }
        Field field1 = ResourcesFlusher.e;
        if(field1 == null) {
            return;
        }
        try {
            longSparseArray0 = null;
            longSparseArray0 = (LongSparseArray)field1.get(object0);
        }
        catch(IllegalAccessException unused_ex) {
        }
        if(longSparseArray0 != null) {
            longSparseArray0.clear();
        }
    }
}


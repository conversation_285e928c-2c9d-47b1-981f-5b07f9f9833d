package androidx.appcompat.app;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

class AppCompatDelegateImpl.AutoNightModeManager.1 extends BroadcastReceiver {
    public final AutoNightModeManager a;

    public AppCompatDelegateImpl.AutoNightModeManager.1(AutoNightModeManager appCompatDelegateImpl$AutoNightModeManager0) {
        this.a = appCompatDelegateImpl$AutoNightModeManager0;
        super();
    }

    @Override  // android.content.BroadcastReceiver
    public final void onReceive(Context context0, Intent intent0) {
        this.a.d();
    }
}


package androidx.appcompat.app;

import android.view.View;
import androidx.core.widget.NestedScrollView.OnScrollChangeListener;
import androidx.core.widget.NestedScrollView;

class AlertController.2 implements OnScrollChangeListener {
    public final View a;
    public final View b;

    public AlertController.2(View view0, View view1) {
        this.a = view0;
        this.b = view1;
    }

    @Override  // androidx.core.widget.NestedScrollView$OnScrollChangeListener
    public final void a(NestedScrollView nestedScrollView0) {
        AlertController.b(nestedScrollView0, this.a, this.b);
    }
}


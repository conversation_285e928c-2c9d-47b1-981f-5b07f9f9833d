package androidx.appcompat.app;

import android.app.Activity;
import android.app.Dialog;
import android.app.UiModeManager;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.location.LocationManager;
import android.media.AudioManager;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Handler;
import android.os.LocaleList;
import android.os.PowerManager;
import android.supportv1.v4.app.d;
import android.text.TextUtils;
import android.util.AndroidRuntimeException;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.ActionMode.Callback;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.LayoutInflater.Factory2;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.Window.Callback;
import android.view.Window;
import android.view.WindowManager.LayoutParams;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.window.OnBackInvokedCallback;
import android.window.OnBackInvokedDispatcher;
import androidx.activity.i;
import androidx.appcompat.R.styleable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.appcompat.view.ActionMode;
import androidx.appcompat.view.ContextThemeWrapper;
import androidx.appcompat.view.StandaloneActionMode;
import androidx.appcompat.view.SupportActionModeWrapper.CallbackWrapper;
import androidx.appcompat.view.SupportMenuInflater;
import androidx.appcompat.view.WindowCallbackWrapper;
import androidx.appcompat.view.menu.ListMenuPresenter;
import androidx.appcompat.view.menu.MenuBuilder.Callback;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.appcompat.widget.ActionBarOverlayLayout;
import androidx.appcompat.widget.AppCompatCheckedTextView;
import androidx.appcompat.widget.AppCompatDrawableManager;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView;
import androidx.appcompat.widget.AppCompatRatingBar;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.AppCompatToggleButton;
import androidx.appcompat.widget.ContentFrameLayout;
import androidx.appcompat.widget.DecorContentParent;
import androidx.appcompat.widget.TintTypedArray;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.ToolbarWidgetWrapper;
import androidx.appcompat.widget.ViewStubCompat;
import androidx.collection.SimpleArrayMap;
import androidx.core.app.NavUtils;
import androidx.core.app.a;
import androidx.core.content.PermissionChecker;
import androidx.core.os.LocaleListCompat;
import androidx.core.view.KeyEventDispatcher.Component;
import androidx.core.view.KeyEventDispatcher;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.core.widget.PopupWindowCompat;
import androidx.lifecycle.Lifecycle.State;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import java.lang.ref.WeakReference;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

class AppCompatDelegateImpl extends AppCompatDelegate implements LayoutInflater.Factory2, Callback {
    class ActionBarDrawableToggleImpl implements ActionBarDrawerToggle.Delegate {
    }

    interface ActionBarMenuCallback {
    }

    final class ActionMenuPresenterCallback implements androidx.appcompat.view.menu.MenuPresenter.Callback {
        public final AppCompatDelegateImpl a;

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final void c(MenuBuilder menuBuilder0, boolean z) {
            this.a.r(menuBuilder0);
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final boolean d(MenuBuilder menuBuilder0) {
            Window.Callback window$Callback0 = this.a.l.getCallback();
            if(window$Callback0 != null) {
                window$Callback0.onMenuOpened(108, menuBuilder0);
            }
            return true;
        }
    }

    class ActionModeCallbackWrapperV9 implements androidx.appcompat.view.ActionMode.Callback {
        public final androidx.appcompat.view.ActionMode.Callback a;
        public final AppCompatDelegateImpl b;

        public ActionModeCallbackWrapperV9(androidx.appcompat.view.ActionMode.Callback actionMode$Callback0) {
            this.a = actionMode$Callback0;
        }

        @Override  // androidx.appcompat.view.ActionMode$Callback
        public final void a(ActionMode actionMode0) {
            this.a.a(actionMode0);
            AppCompatDelegateImpl appCompatDelegateImpl0 = this.b;
            if(appCompatDelegateImpl0.w != null) {
                appCompatDelegateImpl0.l.getDecorView().removeCallbacks(appCompatDelegateImpl0.x);
            }
            if(appCompatDelegateImpl0.v != null) {
                ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = appCompatDelegateImpl0.y;
                if(viewPropertyAnimatorCompat0 != null) {
                    viewPropertyAnimatorCompat0.b();
                }
                ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1 = ViewCompat.a(appCompatDelegateImpl0.v);
                viewPropertyAnimatorCompat1.a(0.0f);
                appCompatDelegateImpl0.y = viewPropertyAnimatorCompat1;
                viewPropertyAnimatorCompat1.d(new AppCompatDelegateImpl.ActionModeCallbackWrapperV9.1(this));
            }
            AppCompatCallback appCompatCallback0 = appCompatDelegateImpl0.n;
            if(appCompatCallback0 != null) {
                appCompatCallback0.onSupportActionModeFinished(appCompatDelegateImpl0.u);
            }
            appCompatDelegateImpl0.u = null;
            ViewCompat.A(appCompatDelegateImpl0.A);
            appCompatDelegateImpl0.J();
        }

        @Override  // androidx.appcompat.view.ActionMode$Callback
        public final boolean b(ActionMode actionMode0, MenuBuilder menuBuilder0) {
            return this.a.b(actionMode0, menuBuilder0);
        }

        @Override  // androidx.appcompat.view.ActionMode$Callback
        public final boolean c(ActionMode actionMode0, MenuItem menuItem0) {
            return this.a.c(actionMode0, menuItem0);
        }

        @Override  // androidx.appcompat.view.ActionMode$Callback
        public final boolean d(ActionMode actionMode0, MenuBuilder menuBuilder0) {
            ViewCompat.A(this.b.A);
            return this.a.d(actionMode0, menuBuilder0);
        }
    }

    static abstract class Api21Impl {
        public static boolean a(PowerManager powerManager0) {
            return powerManager0.isPowerSaveMode();
        }

        public static String b(Locale locale0) {
            return locale0.toLanguageTag();
        }
    }

    static abstract class Api24Impl {
        public static void a(Configuration configuration0, Configuration configuration1, Configuration configuration2) {
            LocaleList localeList0 = configuration0.getLocales();
            LocaleList localeList1 = configuration1.getLocales();
            if(!d.y(localeList1, localeList0)) {
                configuration2.setLocales(localeList1);
                configuration2.locale = configuration1.locale;
            }
        }

        public static LocaleListCompat b(Configuration configuration0) {
            return LocaleListCompat.b(configuration0.getLocales().toLanguageTags());
        }

        public static void c(LocaleListCompat localeListCompat0) {
            LocaleList.setDefault(LocaleList.forLanguageTags(localeListCompat0.f()));
        }

        public static void d(Configuration configuration0, LocaleListCompat localeListCompat0) {
            configuration0.setLocales(LocaleList.forLanguageTags(localeListCompat0.f()));
        }
    }

    static abstract class Api33Impl {
        public static OnBackInvokedDispatcher a(Activity activity0) {
            return activity0.getOnBackInvokedDispatcher();
        }

        public static OnBackInvokedCallback b(Object object0, AppCompatDelegateImpl appCompatDelegateImpl0) {
            Objects.requireNonNull(appCompatDelegateImpl0);
            OnBackInvokedCallback onBackInvokedCallback0 = new c(appCompatDelegateImpl0);
            i.l(((OnBackInvokedDispatcher)object0), ((c)onBackInvokedCallback0));
            return onBackInvokedCallback0;
        }

        public static void c(Object object0, Object object1) {
            ((OnBackInvokedDispatcher)object0).unregisterOnBackInvokedCallback(((OnBackInvokedCallback)object1));
        }
    }

    class AppCompatWindowCallback extends WindowCallbackWrapper {
        public ActionBarMenuCallback b;
        public boolean c;
        public boolean d;
        public boolean e;
        public final AppCompatDelegateImpl f;

        public AppCompatWindowCallback(Window.Callback window$Callback0) {
            super(window$Callback0);
        }

        public final void a(Window.Callback window$Callback0) {
            try {
                this.c = true;
                window$Callback0.onContentChanged();
                this.c = false;
            }
            catch(Throwable throwable0) {
                this.c = false;
                throw throwable0;
            }
        }

        // 去混淆评级： 低(40)
        @Override  // android.view.Window$Callback
        public final boolean dispatchKeyEvent(KeyEvent keyEvent0) {
            return this.d ? this.a.dispatchKeyEvent(keyEvent0) : AppCompatDelegateImpl.this.u(keyEvent0) || this.a.dispatchKeyEvent(keyEvent0);
        }

        @Override  // android.view.Window$Callback
        public final boolean dispatchKeyShortcutEvent(KeyEvent keyEvent0) {
            if(!this.a.dispatchKeyShortcutEvent(keyEvent0)) {
                int v = keyEvent0.getKeyCode();
                AppCompatDelegateImpl appCompatDelegateImpl0 = AppCompatDelegateImpl.this;
                appCompatDelegateImpl0.B();
                if(appCompatDelegateImpl0.o == null || !appCompatDelegateImpl0.o.i(v, keyEvent0)) {
                    if(appCompatDelegateImpl0.M == null || !appCompatDelegateImpl0.G(appCompatDelegateImpl0.M, keyEvent0.getKeyCode(), keyEvent0)) {
                        goto label_10;
                    }
                    PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = appCompatDelegateImpl0.M;
                    if(appCompatDelegateImpl$PanelFeatureState0 != null) {
                        appCompatDelegateImpl$PanelFeatureState0.l = true;
                        return true;
                    label_10:
                        if(appCompatDelegateImpl0.M == null) {
                            PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = appCompatDelegateImpl0.A(0);
                            appCompatDelegateImpl0.H(appCompatDelegateImpl$PanelFeatureState1, keyEvent0);
                            boolean z = appCompatDelegateImpl0.G(appCompatDelegateImpl$PanelFeatureState1, keyEvent0.getKeyCode(), keyEvent0);
                            appCompatDelegateImpl$PanelFeatureState1.k = false;
                            return z;
                        }
                        return false;
                    }
                }
            }
            return true;
        }

        @Override  // android.view.Window$Callback
        public final void onContentChanged() {
            if(this.c) {
                this.a.onContentChanged();
            }
        }

        @Override  // android.view.Window$Callback
        public final boolean onCreatePanelMenu(int v, Menu menu0) {
            return v != 0 || menu0 instanceof MenuBuilder ? this.a.onCreatePanelMenu(v, menu0) : false;
        }

        @Override  // android.view.Window$Callback
        public final View onCreatePanelView(int v) {
            ActionBarMenuCallback appCompatDelegateImpl$ActionBarMenuCallback0 = this.b;
            if(appCompatDelegateImpl$ActionBarMenuCallback0 != null) {
                View view0 = v == 0 ? new View(((ToolbarMenuCallback)appCompatDelegateImpl$ActionBarMenuCallback0).a.a.a.getContext()) : null;
                return view0 == null ? this.a.onCreatePanelView(v) : view0;
            }
            return this.a.onCreatePanelView(v);
        }

        @Override  // androidx.appcompat.view.WindowCallbackWrapper
        public final boolean onMenuOpened(int v, Menu menu0) {
            super.onMenuOpened(v, menu0);
            AppCompatDelegateImpl appCompatDelegateImpl0 = AppCompatDelegateImpl.this;
            if(v == 108) {
                appCompatDelegateImpl0.B();
                ActionBar actionBar0 = appCompatDelegateImpl0.o;
                if(actionBar0 != null) {
                    actionBar0.c(true);
                    return true;
                }
            }
            else {
                appCompatDelegateImpl0.getClass();
            }
            return true;
        }

        @Override  // androidx.appcompat.view.WindowCallbackWrapper
        public final void onPanelClosed(int v, Menu menu0) {
            if(this.e) {
                this.a.onPanelClosed(v, menu0);
                return;
            }
            super.onPanelClosed(v, menu0);
            AppCompatDelegateImpl appCompatDelegateImpl0 = AppCompatDelegateImpl.this;
            if(v == 108) {
                appCompatDelegateImpl0.B();
                ActionBar actionBar0 = appCompatDelegateImpl0.o;
                if(actionBar0 != null) {
                    actionBar0.c(false);
                }
            }
            else if(v == 0) {
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = appCompatDelegateImpl0.A(0);
                if(appCompatDelegateImpl$PanelFeatureState0.m) {
                    appCompatDelegateImpl0.s(appCompatDelegateImpl$PanelFeatureState0, false);
                }
            }
            else {
                appCompatDelegateImpl0.getClass();
            }
        }

        @Override  // android.view.Window$Callback
        public final boolean onPreparePanel(int v, View view0, Menu menu0) {
            MenuBuilder menuBuilder0 = menu0 instanceof MenuBuilder ? ((MenuBuilder)menu0) : null;
            if(v == 0 && menuBuilder0 == null) {
                return false;
            }
            if(menuBuilder0 != null) {
                menuBuilder0.x = true;
            }
            ActionBarMenuCallback appCompatDelegateImpl$ActionBarMenuCallback0 = this.b;
            if(appCompatDelegateImpl$ActionBarMenuCallback0 != null && v == 0) {
                ToolbarActionBar toolbarActionBar0 = ((ToolbarMenuCallback)appCompatDelegateImpl$ActionBarMenuCallback0).a;
                if(!toolbarActionBar0.d) {
                    toolbarActionBar0.a.l = true;
                    toolbarActionBar0.d = true;
                }
            }
            boolean z = this.a.onPreparePanel(v, view0, menu0);
            if(menuBuilder0 != null) {
                menuBuilder0.x = false;
            }
            return z;
        }

        @Override  // androidx.appcompat.view.WindowCallbackWrapper
        public final void onProvideKeyboardShortcuts(List list0, Menu menu0, int v) {
            MenuBuilder menuBuilder0 = AppCompatDelegateImpl.this.A(0).h;
            if(menuBuilder0 != null) {
                super.onProvideKeyboardShortcuts(list0, menuBuilder0, v);
                return;
            }
            super.onProvideKeyboardShortcuts(list0, menu0, v);
        }

        @Override  // android.view.Window$Callback
        public final android.view.ActionMode onWindowStartingActionMode(ActionMode.Callback actionMode$Callback0) {
            if(Build.VERSION.SDK_INT >= 23) {
                return null;
            }
            AppCompatDelegateImpl.this.getClass();
            CallbackWrapper supportActionModeWrapper$CallbackWrapper0 = new CallbackWrapper(AppCompatDelegateImpl.this.k, actionMode$Callback0);
            ActionMode actionMode0 = AppCompatDelegateImpl.this.m(supportActionModeWrapper$CallbackWrapper0);
            return actionMode0 != null ? supportActionModeWrapper$CallbackWrapper0.e(actionMode0) : null;
        }

        @Override  // androidx.appcompat.view.WindowCallbackWrapper
        public final android.view.ActionMode onWindowStartingActionMode(ActionMode.Callback actionMode$Callback0, int v) {
            AppCompatDelegateImpl appCompatDelegateImpl0 = AppCompatDelegateImpl.this;
            appCompatDelegateImpl0.getClass();
            if(v != 0) {
                return super.onWindowStartingActionMode(actionMode$Callback0, v);
            }
            CallbackWrapper supportActionModeWrapper$CallbackWrapper0 = new CallbackWrapper(appCompatDelegateImpl0.k, actionMode$Callback0);
            ActionMode actionMode0 = appCompatDelegateImpl0.m(supportActionModeWrapper$CallbackWrapper0);
            return actionMode0 != null ? supportActionModeWrapper$CallbackWrapper0.e(actionMode0) : null;
        }
    }

    class AutoBatteryNightModeManager extends AutoNightModeManager {
        public final PowerManager c;
        public final AppCompatDelegateImpl d;

        public AutoBatteryNightModeManager(Context context0) {
            this.c = (PowerManager)context0.getApplicationContext().getSystemService("power");
        }

        @Override  // androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager
        public final IntentFilter b() {
            IntentFilter intentFilter0 = new IntentFilter();
            intentFilter0.addAction("android.os.action.POWER_SAVE_MODE_CHANGED");
            return intentFilter0;
        }

        // 去混淆评级： 低(20)
        @Override  // androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager
        public final int c() {
            return Api21Impl.a(this.c) ? 2 : 1;
        }

        @Override  // androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager
        public final void d() {
            AppCompatDelegateImpl.this.n(true, true);
        }
    }

    abstract class AutoNightModeManager {
        public BroadcastReceiver a;
        public final AppCompatDelegateImpl b;

        public final void a() {
            BroadcastReceiver broadcastReceiver0 = this.a;
            if(broadcastReceiver0 != null) {
                try {
                    this.b.k.unregisterReceiver(broadcastReceiver0);
                }
                catch(IllegalArgumentException unused_ex) {
                }
                this.a = null;
            }
        }

        public abstract IntentFilter b();

        public abstract int c();

        public abstract void d();

        public final void e() {
            this.a();
            IntentFilter intentFilter0 = this.b();
            if(intentFilter0.countActions() == 0) {
                return;
            }
            if(this.a == null) {
                this.a = new AppCompatDelegateImpl.AutoNightModeManager.1(this);
            }
            this.b.k.registerReceiver(this.a, intentFilter0);
        }
    }

    class AutoTimeNightModeManager extends AutoNightModeManager {
        public final TwilightManager c;
        public final AppCompatDelegateImpl d;

        public AutoTimeNightModeManager(TwilightManager twilightManager0) {
            this.c = twilightManager0;
        }

        @Override  // androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager
        public final IntentFilter b() {
            IntentFilter intentFilter0 = new IntentFilter();
            intentFilter0.addAction("android.intent.action.TIME_SET");
            intentFilter0.addAction("android.intent.action.TIMEZONE_CHANGED");
            intentFilter0.addAction("android.intent.action.TIME_TICK");
            return intentFilter0;
        }

        @Override  // androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager
        public final int c() {
            long v5;
            Location location1;
            TwilightManager twilightManager0 = this.c;
            TwilightState twilightManager$TwilightState0 = twilightManager0.c;
            if(twilightManager$TwilightState0.b > System.currentTimeMillis()) {
                return twilightManager$TwilightState0.a ? 2 : 1;
            }
            Context context0 = twilightManager0.a;
            int v = PermissionChecker.a(context0, "android.permission.ACCESS_COARSE_LOCATION");
            Location location0 = null;
            LocationManager locationManager0 = twilightManager0.b;
            if(v == 0) {
                try {
                    location1 = locationManager0.isProviderEnabled("network") ? locationManager0.getLastKnownLocation("network") : null;
                }
                catch(Exception unused_ex) {
                    location1 = null;
                }
            }
            else {
                location1 = null;
            }
            if(PermissionChecker.a(context0, "android.permission.ACCESS_FINE_LOCATION") == 0) {
                try {
                    if(locationManager0.isProviderEnabled("gps")) {
                        location0 = locationManager0.getLastKnownLocation("gps");
                    }
                }
                catch(Exception unused_ex) {
                }
            }
            if(location0 == null || location1 == null) {
                if(location0 != null) {
                    location1 = location0;
                }
            }
            else if(location0.getTime() > location1.getTime()) {
                location1 = location0;
            }
            if(location1 != null) {
                long v1 = System.currentTimeMillis();
                if(TwilightCalculator.d == null) {
                    TwilightCalculator.d = new TwilightCalculator();  // 初始化器: Ljava/lang/Object;-><init>()V
                }
                TwilightCalculator twilightCalculator0 = TwilightCalculator.d;
                twilightCalculator0.a(v1 - 86400000L, location1.getLatitude(), location1.getLongitude());
                twilightCalculator0.a(v1, location1.getLatitude(), location1.getLongitude());
                boolean z = twilightCalculator0.c == 1;
                long v2 = twilightCalculator0.b;
                long v3 = twilightCalculator0.a;
                twilightCalculator0.a(v1 + 86400000L, location1.getLatitude(), location1.getLongitude());
                long v4 = twilightCalculator0.b;
                if(v2 == -1L || v3 == -1L) {
                    v5 = v1 + 43200000L;
                }
                else {
                    if(v1 <= v3) {
                        v4 = v1 <= v2 ? v2 : v3;
                    }
                    v5 = v4 + 60000L;
                }
                twilightManager$TwilightState0.a = z;
                twilightManager$TwilightState0.b = v5;
                return z ? 2 : 1;
            }
            int v6 = Calendar.getInstance().get(11);
            return v6 >= 6 && v6 < 22 ? 1 : 2;
        }

        @Override  // androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager
        public final void d() {
            AppCompatDelegateImpl.this.n(true, true);
        }
    }

    class ListMenuDecorView extends ContentFrameLayout {
        public final AppCompatDelegateImpl i;

        public ListMenuDecorView(ContextThemeWrapper contextThemeWrapper0) {
            super(contextThemeWrapper0, null);
        }

        // 去混淆评级： 低(20)
        @Override  // android.view.ViewGroup
        public final boolean dispatchKeyEvent(KeyEvent keyEvent0) {
            return AppCompatDelegateImpl.this.u(keyEvent0) || super.dispatchKeyEvent(keyEvent0);
        }

        @Override  // android.view.ViewGroup
        public final boolean onInterceptTouchEvent(MotionEvent motionEvent0) {
            if(motionEvent0.getAction() == 0) {
                int v = (int)motionEvent0.getX();
                int v1 = (int)motionEvent0.getY();
                if(v < -5 || v1 < -5 || v > this.getWidth() + 5 || v1 > this.getHeight() + 5) {
                    PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = AppCompatDelegateImpl.this.A(0);
                    AppCompatDelegateImpl.this.s(appCompatDelegateImpl$PanelFeatureState0, true);
                    return true;
                }
            }
            return super.onInterceptTouchEvent(motionEvent0);
        }

        @Override  // android.view.View
        public final void setBackgroundResource(int v) {
            this.setBackgroundDrawable(AppCompatResources.a(this.getContext(), v));
        }
    }

    public static final class PanelFeatureState {
        public int a;
        public int b;
        public int c;
        public int d;
        public ViewGroup e;
        public View f;
        public View g;
        public MenuBuilder h;
        public ListMenuPresenter i;
        public ContextThemeWrapper j;
        public boolean k;
        public boolean l;
        public boolean m;
        public boolean n;
        public boolean o;
        public Bundle p;

    }

    final class PanelMenuPresenterCallback implements androidx.appcompat.view.menu.MenuPresenter.Callback {
        public final AppCompatDelegateImpl a;

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final void c(MenuBuilder menuBuilder0, boolean z) {
            PanelFeatureState appCompatDelegateImpl$PanelFeatureState0;
            MenuBuilder menuBuilder1 = menuBuilder0.k();
            boolean z1 = menuBuilder1 != menuBuilder0;
            if(z1) {
                menuBuilder0 = menuBuilder1;
            }
            AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
            PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState = appCompatDelegateImpl0.L;
            int v1 = arr_appCompatDelegateImpl$PanelFeatureState == null ? 0 : arr_appCompatDelegateImpl$PanelFeatureState.length;
            for(int v = 0; true; ++v) {
                appCompatDelegateImpl$PanelFeatureState0 = null;
                if(v >= v1) {
                    break;
                }
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = arr_appCompatDelegateImpl$PanelFeatureState[v];
                if(appCompatDelegateImpl$PanelFeatureState1 != null && appCompatDelegateImpl$PanelFeatureState1.h == menuBuilder0) {
                    appCompatDelegateImpl$PanelFeatureState0 = appCompatDelegateImpl$PanelFeatureState1;
                    break;
                }
            }
            if(appCompatDelegateImpl$PanelFeatureState0 != null) {
                if(z1) {
                    appCompatDelegateImpl0.q(appCompatDelegateImpl$PanelFeatureState0.a, appCompatDelegateImpl$PanelFeatureState0, menuBuilder1);
                    appCompatDelegateImpl0.s(appCompatDelegateImpl$PanelFeatureState0, true);
                    return;
                }
                appCompatDelegateImpl0.s(appCompatDelegateImpl$PanelFeatureState0, z);
            }
        }

        @Override  // androidx.appcompat.view.menu.MenuPresenter$Callback
        public final boolean d(MenuBuilder menuBuilder0) {
            if(menuBuilder0 == menuBuilder0.k()) {
                AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
                if(appCompatDelegateImpl0.F) {
                    Window.Callback window$Callback0 = appCompatDelegateImpl0.l.getCallback();
                    if(window$Callback0 != null && !appCompatDelegateImpl0.Q) {
                        window$Callback0.onMenuOpened(108, menuBuilder0);
                    }
                }
            }
            return true;
        }
    }

    public ViewGroup A;
    public TextView B;
    public View C;
    public boolean D;
    public boolean E;
    public boolean F;
    public boolean G;
    public boolean H;
    public boolean I;
    public boolean J;
    public boolean K;
    public PanelFeatureState[] L;
    public PanelFeatureState M;
    public boolean N;
    public boolean O;
    public boolean P;
    public boolean Q;
    public Configuration R;
    public final int S;
    public int T;
    public int U;
    public boolean V;
    public AutoTimeNightModeManager W;
    public AutoBatteryNightModeManager X;
    public boolean Y;
    public int Z;
    public final Object j;
    public final Context k;
    public Window l;
    public AppCompatWindowCallback m;
    public final AppCompatCallback n;
    public final Runnable n0;
    public ActionBar o;
    public boolean o0;
    public SupportMenuInflater p;
    public Rect p0;
    public CharSequence q;
    public Rect q0;
    public DecorContentParent r;
    public AppCompatViewInflater r0;
    public ActionMenuPresenterCallback s;
    public OnBackInvokedDispatcher s0;
    public PanelMenuPresenterCallback t;
    public OnBackInvokedCallback t0;
    public ActionMode u;
    public static final SimpleArrayMap u0;
    public ActionBarContextView v;
    public static final int[] v0;
    public PopupWindow w;
    public static final boolean w0;
    public Runnable x;
    public ViewPropertyAnimatorCompat y;
    public boolean z;

    static {
        AppCompatDelegateImpl.u0 = new SimpleArrayMap();
        AppCompatDelegateImpl.v0 = new int[]{0x1010054};
        AppCompatDelegateImpl.w0 = true;
    }

    public AppCompatDelegateImpl(Context context0, Window window0, AppCompatCallback appCompatCallback0, Object object0) {
        AppCompatActivity appCompatActivity0;
        this.y = null;
        this.S = -100;
        this.n0 = new Runnable() {
            public final AppCompatDelegateImpl a;

            {
                this.a = appCompatDelegateImpl0;
            }

            @Override
            public final void run() {
                AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
                if((appCompatDelegateImpl0.Z & 1) != 0) {
                    appCompatDelegateImpl0.v(0);
                }
                if((appCompatDelegateImpl0.Z & 0x1000) != 0) {
                    appCompatDelegateImpl0.v(108);
                }
                appCompatDelegateImpl0.Y = false;
                appCompatDelegateImpl0.Z = 0;
            }
        };
        this.k = context0;
        this.n = appCompatCallback0;
        this.j = object0;
        if(object0 instanceof Dialog) {
            while(true) {
                if(context0 != null) {
                    if(context0 instanceof AppCompatActivity) {
                        appCompatActivity0 = (AppCompatActivity)context0;
                        break;
                    }
                    if(context0 instanceof ContextWrapper) {
                        context0 = ((ContextWrapper)context0).getBaseContext();
                        continue;
                    }
                }
                appCompatActivity0 = null;
                break;
            }
            if(appCompatActivity0 != null) {
                this.S = ((AppCompatDelegateImpl)appCompatActivity0.getDelegate()).S;
            }
        }
        if(this.S == -100) {
            SimpleArrayMap simpleArrayMap0 = AppCompatDelegateImpl.u0;
            Integer integer0 = (Integer)simpleArrayMap0.getOrDefault(this.j.getClass().getName(), null);
            if(integer0 != null) {
                this.S = (int)integer0;
                simpleArrayMap0.remove(this.j.getClass().getName());
            }
        }
        if(window0 != null) {
            this.o(window0);
        }
        AppCompatDrawableManager.d();
    }

    public final PanelFeatureState A(int v) {
        PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState = this.L;
        if(arr_appCompatDelegateImpl$PanelFeatureState == null || arr_appCompatDelegateImpl$PanelFeatureState.length <= v) {
            PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState1 = new PanelFeatureState[v + 1];
            if(arr_appCompatDelegateImpl$PanelFeatureState != null) {
                System.arraycopy(arr_appCompatDelegateImpl$PanelFeatureState, 0, arr_appCompatDelegateImpl$PanelFeatureState1, 0, arr_appCompatDelegateImpl$PanelFeatureState.length);
            }
            this.L = arr_appCompatDelegateImpl$PanelFeatureState1;
            arr_appCompatDelegateImpl$PanelFeatureState = arr_appCompatDelegateImpl$PanelFeatureState1;
        }
        PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = arr_appCompatDelegateImpl$PanelFeatureState[v];
        if(appCompatDelegateImpl$PanelFeatureState0 == null) {
            appCompatDelegateImpl$PanelFeatureState0 = new PanelFeatureState();  // 初始化器: Ljava/lang/Object;-><init>()V
            appCompatDelegateImpl$PanelFeatureState0.a = v;
            appCompatDelegateImpl$PanelFeatureState0.n = false;
            arr_appCompatDelegateImpl$PanelFeatureState[v] = appCompatDelegateImpl$PanelFeatureState0;
        }
        return appCompatDelegateImpl$PanelFeatureState0;
    }

    public final void B() {
        this.w();
        if(this.F && this.o == null) {
            Object object0 = this.j;
            if(object0 instanceof Activity) {
                this.o = new WindowDecorActionBar(this.G, ((Activity)object0));
            }
            else if(object0 instanceof Dialog) {
                this.o = new WindowDecorActionBar(((Dialog)object0));
            }
            ActionBar actionBar0 = this.o;
            if(actionBar0 != null) {
                actionBar0.l(this.o0);
            }
        }
    }

    public final void C(int v) {
        this.Z |= 1 << v;
        if(!this.Y) {
            this.l.getDecorView().postOnAnimation(this.n0);
            this.Y = true;
        }
    }

    public final int D(int v, Context context0) {
        switch(v) {
            case -100: {
                return -1;
            }
            case 0: {
                return Build.VERSION.SDK_INT < 23 || ((UiModeManager)context0.getApplicationContext().getSystemService("uimode")).getNightMode() != 0 ? this.y(context0).c() : -1;
            }
            case -1: 
            case 1: 
            case 2: {
                return v;
            }
            case 3: {
                if(this.X == null) {
                    this.X = new AutoBatteryNightModeManager(this, context0);
                }
                return this.X.c();
            }
            default: {
                throw new IllegalStateException("Unknown value set for night mode. Please use one of the MODE_NIGHT values from AppCompatDelegate.");
            }
        }
    }

    public final boolean E() {
        boolean z = this.N;
        this.N = false;
        PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = this.A(0);
        if(appCompatDelegateImpl$PanelFeatureState0.m) {
            if(!z) {
                this.s(appCompatDelegateImpl$PanelFeatureState0, true);
            }
            return true;
        }
        ActionMode actionMode0 = this.u;
        if(actionMode0 != null) {
            actionMode0.c();
            return true;
        }
        this.B();
        return this.o != null && this.o.b();
    }

    public final void F(PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, KeyEvent keyEvent0) {
        int v1;
        if(!appCompatDelegateImpl$PanelFeatureState0.m && !this.Q) {
            int v = appCompatDelegateImpl$PanelFeatureState0.a;
            Context context0 = this.k;
            if(v == 0 && (context0.getResources().getConfiguration().screenLayout & 15) == 4) {
                return;
            }
            Window.Callback window$Callback0 = this.l.getCallback();
            if(window$Callback0 != null && !window$Callback0.onMenuOpened(v, appCompatDelegateImpl$PanelFeatureState0.h)) {
                this.s(appCompatDelegateImpl$PanelFeatureState0, true);
                return;
            }
            WindowManager windowManager0 = (WindowManager)context0.getSystemService("window");
            if(windowManager0 == null) {
                return;
            }
            if(!this.H(appCompatDelegateImpl$PanelFeatureState0, keyEvent0)) {
                return;
            }
            ViewGroup viewGroup0 = appCompatDelegateImpl$PanelFeatureState0.e;
            if(viewGroup0 != null && !appCompatDelegateImpl$PanelFeatureState0.n) {
                View view0 = appCompatDelegateImpl$PanelFeatureState0.g;
                if(view0 == null) {
                    goto label_79;
                }
                ViewGroup.LayoutParams viewGroup$LayoutParams0 = view0.getLayoutParams();
                if(viewGroup$LayoutParams0 == null || viewGroup$LayoutParams0.width != -1) {
                    goto label_79;
                }
                v1 = -1;
                goto label_80;
            }
            if(viewGroup0 == null) {
                this.B();
                Context context1 = this.o == null ? null : this.o.e();
                if(context1 != null) {
                    context0 = context1;
                }
                TypedValue typedValue0 = new TypedValue();
                Resources.Theme resources$Theme0 = context0.getResources().newTheme();
                resources$Theme0.setTo(context0.getTheme());
                resources$Theme0.resolveAttribute(0x7F04000B, typedValue0, true);  // attr:actionBarPopupTheme
                int v2 = typedValue0.resourceId;
                if(v2 != 0) {
                    resources$Theme0.applyStyle(v2, true);
                }
                resources$Theme0.resolveAttribute(0x7F040425, typedValue0, true);  // attr:panelMenuListTheme
                int v3 = typedValue0.resourceId;
                if(v3 == 0) {
                    resources$Theme0.applyStyle(0x7F150297, true);  // style:Theme.AppCompat.CompactMenu
                }
                else {
                    resources$Theme0.applyStyle(v3, true);
                }
                ContextThemeWrapper contextThemeWrapper0 = new ContextThemeWrapper(context0, 0);
                contextThemeWrapper0.getTheme().setTo(resources$Theme0);
                appCompatDelegateImpl$PanelFeatureState0.j = contextThemeWrapper0;
                TypedArray typedArray0 = contextThemeWrapper0.obtainStyledAttributes(R.styleable.j);
                appCompatDelegateImpl$PanelFeatureState0.b = typedArray0.getResourceId(86, 0);
                appCompatDelegateImpl$PanelFeatureState0.d = typedArray0.getResourceId(1, 0);
                typedArray0.recycle();
                appCompatDelegateImpl$PanelFeatureState0.e = new ListMenuDecorView(this, appCompatDelegateImpl$PanelFeatureState0.j);
                appCompatDelegateImpl$PanelFeatureState0.c = 81;
            }
            else if(appCompatDelegateImpl$PanelFeatureState0.n && viewGroup0.getChildCount() > 0) {
                appCompatDelegateImpl$PanelFeatureState0.e.removeAllViews();
            }
            View view1 = appCompatDelegateImpl$PanelFeatureState0.g;
            if(view1 != null) {
                appCompatDelegateImpl$PanelFeatureState0.f = view1;
                goto label_68;
            }
            else if(appCompatDelegateImpl$PanelFeatureState0.h != null) {
                if(this.t == null) {
                    this.t = new PanelMenuPresenterCallback(this);
                }
                PanelMenuPresenterCallback appCompatDelegateImpl$PanelMenuPresenterCallback0 = this.t;
                if(appCompatDelegateImpl$PanelFeatureState0.i == null) {
                    ListMenuPresenter listMenuPresenter0 = new ListMenuPresenter(appCompatDelegateImpl$PanelFeatureState0.j);
                    appCompatDelegateImpl$PanelFeatureState0.i = listMenuPresenter0;
                    listMenuPresenter0.e = appCompatDelegateImpl$PanelMenuPresenterCallback0;
                    appCompatDelegateImpl$PanelFeatureState0.h.b(listMenuPresenter0, appCompatDelegateImpl$PanelFeatureState0.h.a);
                }
                View view2 = (View)appCompatDelegateImpl$PanelFeatureState0.i.i(appCompatDelegateImpl$PanelFeatureState0.e);
                appCompatDelegateImpl$PanelFeatureState0.f = view2;
                if(view2 != null) {
                label_68:
                    if(appCompatDelegateImpl$PanelFeatureState0.f != null && (appCompatDelegateImpl$PanelFeatureState0.g != null || appCompatDelegateImpl$PanelFeatureState0.i.a().getCount() > 0)) {
                        ViewGroup.LayoutParams viewGroup$LayoutParams1 = appCompatDelegateImpl$PanelFeatureState0.f.getLayoutParams();
                        if(viewGroup$LayoutParams1 == null) {
                            viewGroup$LayoutParams1 = new ViewGroup.LayoutParams(-2, -2);
                        }
                        appCompatDelegateImpl$PanelFeatureState0.e.setBackgroundResource(appCompatDelegateImpl$PanelFeatureState0.b);
                        ViewParent viewParent0 = appCompatDelegateImpl$PanelFeatureState0.f.getParent();
                        if(viewParent0 instanceof ViewGroup) {
                            ((ViewGroup)viewParent0).removeView(appCompatDelegateImpl$PanelFeatureState0.f);
                        }
                        appCompatDelegateImpl$PanelFeatureState0.e.addView(appCompatDelegateImpl$PanelFeatureState0.f, viewGroup$LayoutParams1);
                        if(!appCompatDelegateImpl$PanelFeatureState0.f.hasFocus()) {
                            appCompatDelegateImpl$PanelFeatureState0.f.requestFocus();
                        }
                    label_79:
                        v1 = -2;
                    label_80:
                        appCompatDelegateImpl$PanelFeatureState0.l = false;
                        WindowManager.LayoutParams windowManager$LayoutParams0 = new WindowManager.LayoutParams(v1, -2, 0, 0, 1002, 0x820000, -3);
                        windowManager$LayoutParams0.gravity = appCompatDelegateImpl$PanelFeatureState0.c;
                        windowManager$LayoutParams0.windowAnimations = appCompatDelegateImpl$PanelFeatureState0.d;
                        windowManager0.addView(appCompatDelegateImpl$PanelFeatureState0.e, windowManager$LayoutParams0);
                        appCompatDelegateImpl$PanelFeatureState0.m = true;
                        if(v == 0) {
                            this.J();
                        }
                        return;
                    }
                }
            }
            appCompatDelegateImpl$PanelFeatureState0.n = true;
        }
    }

    public final boolean G(PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, int v, KeyEvent keyEvent0) {
        if(keyEvent0.isSystem()) {
            return false;
        }
        if(appCompatDelegateImpl$PanelFeatureState0.k || this.H(appCompatDelegateImpl$PanelFeatureState0, keyEvent0)) {
            return appCompatDelegateImpl$PanelFeatureState0.h == null ? false : appCompatDelegateImpl$PanelFeatureState0.h.performShortcut(v, keyEvent0, 1);
        }
        return false;
    }

    public final boolean H(PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, KeyEvent keyEvent0) {
        Resources.Theme resources$Theme1;
        if(this.Q) {
            return false;
        }
        if(appCompatDelegateImpl$PanelFeatureState0.k) {
            return true;
        }
        PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = this.M;
        if(appCompatDelegateImpl$PanelFeatureState1 != null && appCompatDelegateImpl$PanelFeatureState1 != appCompatDelegateImpl$PanelFeatureState0) {
            this.s(appCompatDelegateImpl$PanelFeatureState1, false);
        }
        Window.Callback window$Callback0 = this.l.getCallback();
        int v = appCompatDelegateImpl$PanelFeatureState0.a;
        if(window$Callback0 != null) {
            appCompatDelegateImpl$PanelFeatureState0.g = window$Callback0.onCreatePanelView(v);
        }
        boolean z = v == 0 || v == 108;
        if(z) {
            DecorContentParent decorContentParent0 = this.r;
            if(decorContentParent0 != null) {
                ((ActionBarOverlayLayout)decorContentParent0).h();
                ((ToolbarWidgetWrapper)((ActionBarOverlayLayout)decorContentParent0).e).l = true;
            }
        }
        if(appCompatDelegateImpl$PanelFeatureState0.g == null && (!z || !(this.o instanceof ToolbarActionBar))) {
            MenuBuilder menuBuilder0 = appCompatDelegateImpl$PanelFeatureState0.h;
            if(menuBuilder0 == null || appCompatDelegateImpl$PanelFeatureState0.o) {
                if(menuBuilder0 == null) {
                    Context context0 = this.k;
                    if((v == 0 || v == 108) && this.r != null) {
                        TypedValue typedValue0 = new TypedValue();
                        Resources.Theme resources$Theme0 = context0.getTheme();
                        resources$Theme0.resolveAttribute(0x7F040012, typedValue0, true);  // attr:actionBarTheme
                        if(typedValue0.resourceId == 0) {
                            resources$Theme0.resolveAttribute(0x7F040013, typedValue0, true);  // attr:actionBarWidgetTheme
                            resources$Theme1 = null;
                        }
                        else {
                            resources$Theme1 = context0.getResources().newTheme();
                            resources$Theme1.setTo(resources$Theme0);
                            resources$Theme1.applyStyle(typedValue0.resourceId, true);
                            resources$Theme1.resolveAttribute(0x7F040013, typedValue0, true);  // attr:actionBarWidgetTheme
                        }
                        if(typedValue0.resourceId != 0) {
                            if(resources$Theme1 == null) {
                                resources$Theme1 = context0.getResources().newTheme();
                                resources$Theme1.setTo(resources$Theme0);
                            }
                            resources$Theme1.applyStyle(typedValue0.resourceId, true);
                        }
                        if(resources$Theme1 != null) {
                            ContextThemeWrapper contextThemeWrapper0 = new ContextThemeWrapper(context0, 0);
                            contextThemeWrapper0.getTheme().setTo(resources$Theme1);
                            context0 = contextThemeWrapper0;
                        }
                    }
                    MenuBuilder menuBuilder1 = new MenuBuilder(context0);
                    menuBuilder1.e = this;
                    MenuBuilder menuBuilder2 = appCompatDelegateImpl$PanelFeatureState0.h;
                    if(menuBuilder1 != menuBuilder2) {
                        if(menuBuilder2 != null) {
                            menuBuilder2.r(appCompatDelegateImpl$PanelFeatureState0.i);
                        }
                        appCompatDelegateImpl$PanelFeatureState0.h = menuBuilder1;
                        ListMenuPresenter listMenuPresenter0 = appCompatDelegateImpl$PanelFeatureState0.i;
                        if(listMenuPresenter0 != null) {
                            menuBuilder1.b(listMenuPresenter0, menuBuilder1.a);
                        }
                    }
                    if(appCompatDelegateImpl$PanelFeatureState0.h == null) {
                        return false;
                    }
                }
                if(z) {
                    DecorContentParent decorContentParent1 = this.r;
                    if(decorContentParent1 != null) {
                        if(this.s == null) {
                            this.s = new ActionMenuPresenterCallback(this);
                        }
                        ((ActionBarOverlayLayout)decorContentParent1).i(appCompatDelegateImpl$PanelFeatureState0.h, this.s);
                    }
                }
                appCompatDelegateImpl$PanelFeatureState0.h.w();
                if(!window$Callback0.onCreatePanelMenu(v, appCompatDelegateImpl$PanelFeatureState0.h)) {
                    MenuBuilder menuBuilder3 = appCompatDelegateImpl$PanelFeatureState0.h;
                    if(menuBuilder3 != null) {
                        menuBuilder3.r(appCompatDelegateImpl$PanelFeatureState0.i);
                        appCompatDelegateImpl$PanelFeatureState0.h = null;
                    }
                    if(z) {
                        DecorContentParent decorContentParent2 = this.r;
                        if(decorContentParent2 != null) {
                            ((ActionBarOverlayLayout)decorContentParent2).i(null, this.s);
                        }
                    }
                    return false;
                }
                appCompatDelegateImpl$PanelFeatureState0.o = false;
            }
            appCompatDelegateImpl$PanelFeatureState0.h.w();
            Bundle bundle0 = appCompatDelegateImpl$PanelFeatureState0.p;
            if(bundle0 != null) {
                appCompatDelegateImpl$PanelFeatureState0.h.s(bundle0);
                appCompatDelegateImpl$PanelFeatureState0.p = null;
            }
            if(!window$Callback0.onPreparePanel(0, appCompatDelegateImpl$PanelFeatureState0.g, appCompatDelegateImpl$PanelFeatureState0.h)) {
                if(z) {
                    DecorContentParent decorContentParent3 = this.r;
                    if(decorContentParent3 != null) {
                        ((ActionBarOverlayLayout)decorContentParent3).i(null, this.s);
                    }
                }
                appCompatDelegateImpl$PanelFeatureState0.h.v();
                return false;
            }
            boolean z1 = KeyCharacterMap.load((keyEvent0 == null ? -1 : keyEvent0.getDeviceId())).getKeyboardType() != 1;
            appCompatDelegateImpl$PanelFeatureState0.h.setQwertyMode(z1);
            appCompatDelegateImpl$PanelFeatureState0.h.v();
        }
        appCompatDelegateImpl$PanelFeatureState0.k = true;
        appCompatDelegateImpl$PanelFeatureState0.l = false;
        this.M = appCompatDelegateImpl$PanelFeatureState0;
        return true;
    }

    public final void I() {
        if(this.z) {
            throw new AndroidRuntimeException("Window feature must be requested before adding content");
        }
    }

    public final void J() {
        boolean z = false;
        if(Build.VERSION.SDK_INT >= 33) {
            if(this.s0 != null) {
                if(this.A(0).m) {
                    z = true;
                }
                else if(this.u != null) {
                    z = true;
                }
            }
            if(z && this.t0 == null) {
                this.t0 = Api33Impl.b(this.s0, this);
                return;
            }
            if(!z) {
                OnBackInvokedCallback onBackInvokedCallback0 = this.t0;
                if(onBackInvokedCallback0 != null) {
                    Api33Impl.c(this.s0, onBackInvokedCallback0);
                    this.t0 = null;
                }
            }
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
    public final boolean a(MenuBuilder menuBuilder0, MenuItem menuItem0) {
        Window.Callback window$Callback0 = this.l.getCallback();
        if(window$Callback0 != null && !this.Q) {
            MenuBuilder menuBuilder1 = menuBuilder0.k();
            PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState = this.L;
            int v = arr_appCompatDelegateImpl$PanelFeatureState == null ? 0 : arr_appCompatDelegateImpl$PanelFeatureState.length;
            for(int v1 = 0; v1 < v; ++v1) {
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = arr_appCompatDelegateImpl$PanelFeatureState[v1];
                if(appCompatDelegateImpl$PanelFeatureState0 != null && appCompatDelegateImpl$PanelFeatureState0.h == menuBuilder1) {
                    return appCompatDelegateImpl$PanelFeatureState0 == null ? false : window$Callback0.onMenuItemSelected(appCompatDelegateImpl$PanelFeatureState0.a, menuItem0);
                }
            }
            throw new NullPointerException();
        }
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
    public final void b(MenuBuilder menuBuilder0) {
        DecorContentParent decorContentParent0 = this.r;
        if(decorContentParent0 == null) {
        label_26:
            PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = this.A(0);
            appCompatDelegateImpl$PanelFeatureState1.n = true;
            this.s(appCompatDelegateImpl$PanelFeatureState1, false);
            this.F(appCompatDelegateImpl$PanelFeatureState1, null);
        }
        else {
            ((ActionBarOverlayLayout)decorContentParent0).h();
            Toolbar toolbar0 = ((ToolbarWidgetWrapper)((ActionBarOverlayLayout)decorContentParent0).e).a;
            if(toolbar0.getVisibility() != 0 || (toolbar0.a == null || !toolbar0.a.s || ViewConfiguration.get(this.k).hasPermanentMenuKey() && !((ActionBarOverlayLayout)this.r).g())) {
                goto label_26;
            }
            else {
                Window.Callback window$Callback0 = this.l.getCallback();
                ActionBarOverlayLayout actionBarOverlayLayout0 = (ActionBarOverlayLayout)this.r;
                actionBarOverlayLayout0.h();
                if(((ToolbarWidgetWrapper)actionBarOverlayLayout0.e).a.q()) {
                    ActionBarOverlayLayout actionBarOverlayLayout1 = (ActionBarOverlayLayout)this.r;
                    actionBarOverlayLayout1.h();
                    ((ToolbarWidgetWrapper)actionBarOverlayLayout1.e).c();
                    if(!this.Q) {
                        window$Callback0.onPanelClosed(108, this.A(0).h);
                    }
                }
                else if(window$Callback0 != null && !this.Q) {
                    if(this.Y && (1 & this.Z) != 0) {
                        this.l.getDecorView().removeCallbacks(this.n0);
                        ((androidx.appcompat.app.AppCompatDelegateImpl.2)this.n0).run();
                    }
                    PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = this.A(0);
                    if(appCompatDelegateImpl$PanelFeatureState0.h != null && !appCompatDelegateImpl$PanelFeatureState0.o && window$Callback0.onPreparePanel(0, appCompatDelegateImpl$PanelFeatureState0.g, appCompatDelegateImpl$PanelFeatureState0.h)) {
                        window$Callback0.onMenuOpened(108, appCompatDelegateImpl$PanelFeatureState0.h);
                        ActionBarOverlayLayout actionBarOverlayLayout2 = (ActionBarOverlayLayout)this.r;
                        actionBarOverlayLayout2.h();
                        ((ToolbarWidgetWrapper)actionBarOverlayLayout2.e).a.w();
                    }
                }
            }
        }
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final void c() {
        if(this.o != null) {
            this.B();
            if(!this.o.f()) {
                this.C(0);
            }
        }
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final void e() {
        String s;
        this.O = true;
        this.n(false, true);
        this.x();
        Object object0 = this.j;
        if(object0 instanceof Activity) {
            try {
                Activity activity0 = (Activity)object0;
                try {
                    s = NavUtils.c(activity0, activity0.getComponentName());
                    goto label_9;
                }
                catch(PackageManager.NameNotFoundException unused_ex) {
                }
                goto label_8;
            }
            catch(IllegalArgumentException unused_ex) {
            label_8:
                s = null;
            }
        label_9:
            if(s != null) {
                ActionBar actionBar0 = this.o;
                if(actionBar0 == null) {
                    this.o0 = true;
                }
                else {
                    actionBar0.l(true);
                }
            }
            synchronized(AppCompatDelegate.h) {
                AppCompatDelegate.g(this);
                WeakReference weakReference0 = new WeakReference(this);
                AppCompatDelegate.g.add(weakReference0);
            }
        }
        this.R = new Configuration(this.k.getResources().getConfiguration());
        this.P = true;
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final void f() {
        if(this.j instanceof Activity) {
            synchronized(AppCompatDelegate.h) {
                AppCompatDelegate.g(this);
            }
        }
        if(this.Y) {
            this.l.getDecorView().removeCallbacks(this.n0);
        }
        this.Q = true;
        if(this.S == -100 || (!(this.j instanceof Activity) || !((Activity)this.j).isChangingConfigurations())) {
            String s1 = this.j.getClass().getName();
            AppCompatDelegateImpl.u0.remove(s1);
        }
        else {
            String s = this.j.getClass().getName();
            AppCompatDelegateImpl.u0.put(s, this.S);
        }
        ActionBar actionBar0 = this.o;
        if(actionBar0 != null) {
            actionBar0.h();
        }
        AutoTimeNightModeManager appCompatDelegateImpl$AutoTimeNightModeManager0 = this.W;
        if(appCompatDelegateImpl$AutoTimeNightModeManager0 != null) {
            appCompatDelegateImpl$AutoTimeNightModeManager0.a();
        }
        AutoBatteryNightModeManager appCompatDelegateImpl$AutoBatteryNightModeManager0 = this.X;
        if(appCompatDelegateImpl$AutoBatteryNightModeManager0 != null) {
            appCompatDelegateImpl$AutoBatteryNightModeManager0.a();
        }
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final boolean h(int v) {
        switch(v) {
            case 8: {
                v = 108;
                break;
            }
            case 9: {
                v = 109;
            }
        }
        if(this.J && v == 108) {
            return false;
        }
        if(this.F && v == 1) {
            this.F = false;
        }
        switch(v) {
            case 1: {
                this.I();
                this.J = true;
                return true;
            }
            case 2: {
                this.I();
                this.D = true;
                return true;
            }
            case 5: {
                this.I();
                this.E = true;
                return true;
            }
            case 10: {
                this.I();
                this.H = true;
                return true;
            }
            case 108: {
                this.I();
                this.F = true;
                return true;
            }
            case 109: {
                this.I();
                this.G = true;
                return true;
            }
            default: {
                return this.l.requestFeature(v);
            }
        }
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final void i(int v) {
        this.w();
        ViewGroup viewGroup0 = (ViewGroup)this.A.findViewById(0x1020002);
        viewGroup0.removeAllViews();
        LayoutInflater.from(this.k).inflate(v, viewGroup0);
        this.m.a(this.l.getCallback());
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final void j(View view0) {
        this.w();
        ViewGroup viewGroup0 = (ViewGroup)this.A.findViewById(0x1020002);
        viewGroup0.removeAllViews();
        viewGroup0.addView(view0);
        this.m.a(this.l.getCallback());
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final void k(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        this.w();
        ViewGroup viewGroup0 = (ViewGroup)this.A.findViewById(0x1020002);
        viewGroup0.removeAllViews();
        viewGroup0.addView(view0, viewGroup$LayoutParams0);
        this.m.a(this.l.getCallback());
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final void l(CharSequence charSequence0) {
        this.q = charSequence0;
        DecorContentParent decorContentParent0 = this.r;
        if(decorContentParent0 != null) {
            decorContentParent0.setWindowTitle(charSequence0);
            return;
        }
        ActionBar actionBar0 = this.o;
        if(actionBar0 != null) {
            actionBar0.o(charSequence0);
            return;
        }
        TextView textView0 = this.B;
        if(textView0 != null) {
            textView0.setText(charSequence0);
        }
    }

    @Override  // androidx.appcompat.app.AppCompatDelegate
    public final ActionMode m(androidx.appcompat.view.ActionMode.Callback actionMode$Callback0) {
        if(actionMode$Callback0 == null) {
            throw new IllegalArgumentException("ActionMode callback can not be null.");
        }
        ActionMode actionMode0 = this.u;
        if(actionMode0 != null) {
            actionMode0.c();
        }
        ActionModeCallbackWrapperV9 appCompatDelegateImpl$ActionModeCallbackWrapperV90 = new ActionModeCallbackWrapperV9(this, actionMode$Callback0);
        this.B();
        ActionBar actionBar0 = this.o;
        AppCompatCallback appCompatCallback0 = this.n;
        if(actionBar0 != null) {
            ActionMode actionMode1 = actionBar0.p(appCompatDelegateImpl$ActionModeCallbackWrapperV90);
            this.u = actionMode1;
            if(actionMode1 != null && appCompatCallback0 != null) {
                appCompatCallback0.onSupportActionModeStarted(actionMode1);
            }
        }
        if(this.u == null) {
            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = this.y;
            if(viewPropertyAnimatorCompat0 != null) {
                viewPropertyAnimatorCompat0.b();
            }
            ActionMode actionMode2 = this.u;
            if(actionMode2 != null) {
                actionMode2.c();
            }
            ActionMode actionMode3 = null;
            if(appCompatCallback0 != null && !this.Q) {
                try {
                    actionMode3 = appCompatCallback0.onWindowStartingSupportActionMode(appCompatDelegateImpl$ActionModeCallbackWrapperV90);
                }
                catch(AbstractMethodError unused_ex) {
                }
            }
            if(actionMode3 == null) {
                if(this.v == null) {
                    Context context0 = this.k;
                    if(this.I) {
                        TypedValue typedValue0 = new TypedValue();
                        Resources.Theme resources$Theme0 = context0.getTheme();
                        resources$Theme0.resolveAttribute(0x7F040012, typedValue0, true);  // attr:actionBarTheme
                        if(typedValue0.resourceId != 0) {
                            Resources.Theme resources$Theme1 = context0.getResources().newTheme();
                            resources$Theme1.setTo(resources$Theme0);
                            resources$Theme1.applyStyle(typedValue0.resourceId, true);
                            ContextThemeWrapper contextThemeWrapper0 = new ContextThemeWrapper(context0, 0);
                            contextThemeWrapper0.getTheme().setTo(resources$Theme1);
                            context0 = contextThemeWrapper0;
                        }
                        this.v = new ActionBarContextView(context0, null);
                        PopupWindow popupWindow0 = new PopupWindow(context0, null, 0x7F040021);  // attr:actionModePopupWindowStyle
                        this.w = popupWindow0;
                        PopupWindowCompat.b(popupWindow0, 2);
                        this.w.setContentView(this.v);
                        this.w.setWidth(-1);
                        context0.getTheme().resolveAttribute(0x7F04000C, typedValue0, true);  // attr:actionBarSize
                        int v = TypedValue.complexToDimensionPixelSize(typedValue0.data, context0.getResources().getDisplayMetrics());
                        this.v.setContentHeight(v);
                        this.w.setHeight(-2);
                        this.x = new AppCompatDelegateImpl.6(this);
                    }
                    else {
                        ViewStubCompat viewStubCompat0 = (ViewStubCompat)this.A.findViewById(0x7F0A0060);  // id:action_mode_bar_stub
                        if(viewStubCompat0 != null) {
                            this.B();
                            Context context1 = this.o == null ? null : this.o.e();
                            if(context1 != null) {
                                context0 = context1;
                            }
                            viewStubCompat0.setLayoutInflater(LayoutInflater.from(context0));
                            this.v = (ActionBarContextView)viewStubCompat0.a();
                        }
                    }
                }
                if(this.v != null) {
                    ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1 = this.y;
                    if(viewPropertyAnimatorCompat1 != null) {
                        viewPropertyAnimatorCompat1.b();
                    }
                    this.v.g();
                    Context context2 = this.v.getContext();
                    ActionBarContextView actionBarContextView0 = this.v;
                    StandaloneActionMode standaloneActionMode0 = new StandaloneActionMode();  // 初始化器: Ljava/lang/Object;-><init>()V
                    standaloneActionMode0.c = context2;
                    standaloneActionMode0.d = actionBarContextView0;
                    standaloneActionMode0.e = appCompatDelegateImpl$ActionModeCallbackWrapperV90;
                    MenuBuilder menuBuilder0 = new MenuBuilder(actionBarContextView0.getContext());
                    menuBuilder0.l = 1;
                    standaloneActionMode0.h = menuBuilder0;
                    menuBuilder0.e = standaloneActionMode0;
                    if(appCompatDelegateImpl$ActionModeCallbackWrapperV90.a.b(standaloneActionMode0, menuBuilder0)) {
                        standaloneActionMode0.i();
                        this.v.e(standaloneActionMode0);
                        this.u = standaloneActionMode0;
                        if(!this.z || (this.A == null || !this.A.isLaidOut())) {
                            this.v.setAlpha(1.0f);
                            this.v.setVisibility(0);
                            if(this.v.getParent() instanceof View) {
                                ViewCompat.A(((View)this.v.getParent()));
                            }
                        }
                        else {
                            this.v.setAlpha(0.0f);
                            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat2 = ViewCompat.a(this.v);
                            viewPropertyAnimatorCompat2.a(1.0f);
                            this.y = viewPropertyAnimatorCompat2;
                            viewPropertyAnimatorCompat2.d(new AppCompatDelegateImpl.7(this));
                        }
                        if(this.w != null) {
                            this.l.getDecorView().post(this.x);
                        }
                    }
                    else {
                        this.u = null;
                    }
                }
            }
            else {
                this.u = actionMode3;
            }
            ActionMode actionMode4 = this.u;
            if(actionMode4 != null && appCompatCallback0 != null) {
                appCompatCallback0.onSupportActionModeStarted(actionMode4);
            }
            this.J();
            this.u = this.u;
        }
        this.J();
        return this.u;
    }

    public final boolean n(boolean z, boolean z1) {
        boolean z3;
        Map map0;
        Object object1;
        boolean z2;
        int v4;
        int v3;
        if(this.Q) {
            return false;
        }
        int v = this.S == -100 ? AppCompatDelegate.b : this.S;
        Context context0 = this.k;
        int v1 = this.D(v, context0);
        int v2 = Build.VERSION.SDK_INT;
        LocaleListCompat localeListCompat0 = v2 >= 33 ? null : AppCompatDelegateImpl.p(context0);
        if(!z1 && localeListCompat0 != null) {
            localeListCompat0 = AppCompatDelegateImpl.z(context0.getResources().getConfiguration());
        }
        Configuration configuration0 = AppCompatDelegateImpl.t(context0, v1, localeListCompat0, null, false);
        Object object0 = this.j;
        if(this.V || !(object0 instanceof Activity)) {
            this.V = true;
            v3 = this.U;
        }
        else {
            PackageManager packageManager0 = context0.getPackageManager();
            if(packageManager0 == null) {
                v3 = 0;
            }
            else {
                if(v2 >= 29) {
                    v4 = 0x100C0000;
                }
                else {
                    v4 = v2 < 24 ? 0 : 0xC0000;
                }
                try {
                    ActivityInfo activityInfo0 = packageManager0.getActivityInfo(new ComponentName(context0, object0.getClass()), v4);
                    if(activityInfo0 != null) {
                        this.U = activityInfo0.configChanges;
                    }
                }
                catch(PackageManager.NameNotFoundException unused_ex) {
                    this.U = 0;
                }
                this.V = true;
                v3 = this.U;
            }
        }
        Configuration configuration1 = this.R == null ? context0.getResources().getConfiguration() : this.R;
        int v5 = configuration1.uiMode & 0x30;
        int v6 = configuration0.uiMode & 0x30;
        LocaleListCompat localeListCompat1 = AppCompatDelegateImpl.z(configuration1);
        LocaleListCompat localeListCompat2 = localeListCompat0 == null ? null : AppCompatDelegateImpl.z(configuration0);
        int v7 = v5 == v6 ? 0 : 0x200;
        if(localeListCompat2 != null && !localeListCompat1.equals(localeListCompat2)) {
            v7 |= 0x2004;
        }
        if((~v3 & v7) == 0 || !z || !this.O || !AppCompatDelegateImpl.w0 && !this.P || !(object0 instanceof Activity) || ((Activity)object0).isChild()) {
            z2 = false;
        }
        else {
            int v8 = Build.VERSION.SDK_INT;
            if(v8 >= 0x1F && (v7 & 0x2000) != 0) {
                ((Activity)object0).getWindow().getDecorView().setLayoutDirection(configuration0.getLayoutDirection());
            }
            if(v8 >= 28) {
                ((Activity)object0).recreate();
            }
            else {
                new Handler(((Activity)object0).getMainLooper()).post(new a(((Activity)object0), 0));
            }
            z2 = true;
        }
        if(z2 || v7 == 0) {
            z3 = z2;
        }
        else {
            Resources resources0 = context0.getResources();
            Configuration configuration2 = new Configuration(resources0.getConfiguration());
            configuration2.uiMode = resources0.getConfiguration().uiMode & -49 | v6;
            if(localeListCompat2 != null) {
                if(Build.VERSION.SDK_INT >= 24) {
                    Api24Impl.d(configuration2, localeListCompat2);
                }
                else {
                    configuration2.setLocale(localeListCompat2.c(0));
                    configuration2.setLayoutDirection(localeListCompat2.c(0));
                }
            }
            resources0.updateConfiguration(configuration2, null);
            int v9 = Build.VERSION.SDK_INT;
            if(v9 < 26 && v9 < 28) {
                Class class0 = Resources.class;
                if(v9 >= 24) {
                    if(!ResourcesFlusher.h) {
                        try {
                            Field field0 = class0.getDeclaredField("mResourcesImpl");
                            ResourcesFlusher.g = field0;
                            field0.setAccessible(true);
                        }
                        catch(NoSuchFieldException unused_ex) {
                        }
                        ResourcesFlusher.h = true;
                    }
                    Field field1 = ResourcesFlusher.g;
                    if(field1 != null) {
                        try {
                            object1 = null;
                            object1 = field1.get(resources0);
                        }
                        catch(IllegalAccessException unused_ex) {
                        }
                        if(object1 != null) {
                            if(!ResourcesFlusher.b) {
                                try {
                                    Field field2 = object1.getClass().getDeclaredField("mDrawableCache");
                                    ResourcesFlusher.a = field2;
                                    field2.setAccessible(true);
                                }
                                catch(NoSuchFieldException unused_ex) {
                                }
                                ResourcesFlusher.b = true;
                            }
                            Field field3 = ResourcesFlusher.a;
                            Object object2 = null;
                            if(field3 != null) {
                                try {
                                    object2 = field3.get(object1);
                                }
                                catch(IllegalAccessException unused_ex) {
                                }
                            }
                            if(object2 != null) {
                                ResourcesFlusher.a(object2);
                            }
                        }
                    }
                }
                else if(v9 >= 23) {
                    if(!ResourcesFlusher.b) {
                        try {
                            Field field4 = class0.getDeclaredField("mDrawableCache");
                            ResourcesFlusher.a = field4;
                            field4.setAccessible(true);
                        }
                        catch(NoSuchFieldException unused_ex) {
                        }
                        ResourcesFlusher.b = true;
                    }
                    Field field5 = ResourcesFlusher.a;
                    Object object3 = null;
                    if(field5 != null) {
                        try {
                            object3 = field5.get(resources0);
                        }
                        catch(IllegalAccessException unused_ex) {
                        }
                    }
                    if(object3 != null) {
                        ResourcesFlusher.a(object3);
                    }
                }
                else {
                    if(!ResourcesFlusher.b) {
                        try {
                            Field field6 = class0.getDeclaredField("mDrawableCache");
                            ResourcesFlusher.a = field6;
                            field6.setAccessible(true);
                        }
                        catch(NoSuchFieldException unused_ex) {
                        }
                        ResourcesFlusher.b = true;
                    }
                    Field field7 = ResourcesFlusher.a;
                    if(field7 != null) {
                        try {
                            map0 = null;
                            map0 = (Map)field7.get(resources0);
                        }
                        catch(IllegalAccessException unused_ex) {
                        }
                        if(map0 != null) {
                            map0.clear();
                        }
                    }
                }
            }
            int v10 = this.T;
            if(v10 != 0) {
                context0.setTheme(v10);
                if(Build.VERSION.SDK_INT >= 23) {
                    context0.getTheme().applyStyle(this.T, true);
                }
            }
            if((v3 & v7) == v7 && object0 instanceof Activity) {
                if(!(((Activity)object0) instanceof LifecycleOwner)) {
                    if(this.P && !this.Q) {
                        ((Activity)object0).onConfigurationChanged(configuration2);
                    }
                }
                else if(((LifecycleRegistry)((LifecycleOwner)(((Activity)object0))).getLifecycle()).c.a(State.c)) {
                    ((Activity)object0).onConfigurationChanged(configuration2);
                }
            }
            z3 = true;
        }
        if(localeListCompat2 != null) {
            LocaleListCompat localeListCompat3 = AppCompatDelegateImpl.z(context0.getResources().getConfiguration());
            if(Build.VERSION.SDK_INT >= 24) {
                Api24Impl.c(localeListCompat3);
            }
            else {
                Locale.setDefault(localeListCompat3.c(0));
            }
        }
        if(v == 0) {
            this.y(context0).e();
        }
        else {
            AutoTimeNightModeManager appCompatDelegateImpl$AutoTimeNightModeManager0 = this.W;
            if(appCompatDelegateImpl$AutoTimeNightModeManager0 != null) {
                appCompatDelegateImpl$AutoTimeNightModeManager0.a();
            }
        }
        if(v == 3) {
            if(this.X == null) {
                this.X = new AutoBatteryNightModeManager(this, context0);
            }
            this.X.e();
            return z3;
        }
        AutoBatteryNightModeManager appCompatDelegateImpl$AutoBatteryNightModeManager0 = this.X;
        if(appCompatDelegateImpl$AutoBatteryNightModeManager0 != null) {
            appCompatDelegateImpl$AutoBatteryNightModeManager0.a();
        }
        return z3;
    }

    public final void o(Window window0) {
        if(this.l != null) {
            throw new IllegalStateException("AppCompat has already installed itself into the Window");
        }
        Window.Callback window$Callback0 = window0.getCallback();
        if(window$Callback0 instanceof AppCompatWindowCallback) {
            throw new IllegalStateException("AppCompat has already installed itself into the Window");
        }
        AppCompatWindowCallback appCompatDelegateImpl$AppCompatWindowCallback0 = new AppCompatWindowCallback(this, window$Callback0);
        this.m = appCompatDelegateImpl$AppCompatWindowCallback0;
        window0.setCallback(appCompatDelegateImpl$AppCompatWindowCallback0);
        TypedArray typedArray0 = this.k.obtainStyledAttributes(null, AppCompatDelegateImpl.v0);
        TintTypedArray tintTypedArray0 = new TintTypedArray(this.k, typedArray0);
        Drawable drawable0 = tintTypedArray0.c(0);
        if(drawable0 != null) {
            window0.setBackgroundDrawable(drawable0);
        }
        tintTypedArray0.f();
        this.l = window0;
        if(Build.VERSION.SDK_INT >= 33 && this.s0 == null) {
            Object object0 = this.j;
            this.s0 = !(object0 instanceof Activity) || ((Activity)object0).getWindow() == null ? null : Api33Impl.a(((Activity)object0));
            this.J();
        }
    }

    @Override  // android.view.LayoutInflater$Factory2
    public final View onCreateView(View view0, String s, Context context0, AttributeSet attributeSet0) {
        View view3;
        View view2;
        if(this.r0 == null) {
            Context context1 = this.k;
            TypedArray typedArray0 = context1.obtainStyledAttributes(R.styleable.j);
            String s1 = typedArray0.getString(0x74);
            typedArray0.recycle();
            if(s1 == null) {
                this.r0 = new AppCompatViewInflater();
            }
            else {
                try {
                    this.r0 = (AppCompatViewInflater)context1.getClassLoader().loadClass(s1).getDeclaredConstructor().newInstance();
                }
                catch(Throwable unused_ex) {
                    this.r0 = new AppCompatViewInflater();
                }
            }
        }
        AppCompatViewInflater appCompatViewInflater0 = this.r0;
        appCompatViewInflater0.getClass();
        TypedArray typedArray1 = context0.obtainStyledAttributes(attributeSet0, R.styleable.y, 0, 0);
        int v = 4;
        int v1 = typedArray1.getResourceId(4, 0);
        typedArray1.recycle();
        Context context2 = v1 == 0 || context0 instanceof ContextThemeWrapper && ((ContextThemeWrapper)context0).a == v1 ? context0 : new ContextThemeWrapper(context0, v1);
        s.getClass();
        switch(s) {
            case "AutoCompleteTextView": {
                v = 10;
                break;
            }
            case "Button": {
                v = 13;
                break;
            }
            case "CheckBox": {
                v = 11;
                break;
            }
            case "CheckedTextView": {
                v = 1;
                break;
            }
            case "EditText": {
                v = 12;
                break;
            }
            case "ImageButton": {
                break;
            }
            case "ImageView": {
                v = 9;
                break;
            }
            case "MultiAutoCompleteTextView": {
                v = 2;
                break;
            }
            case "RadioButton": {
                v = 7;
                break;
            }
            case "RatingBar": {
                v = 0;
                break;
            }
            case "SeekBar": {
                v = 5;
                break;
            }
            case "Spinner": {
                v = 6;
                break;
            }
            case "TextView": {
                v = 3;
                break;
            }
            case "ToggleButton": {
                v = 8;
                break;
            }
            default: {
                v = -1;
            }
        }
        View view1 = null;
        switch(v) {
            case 0: {
                view2 = new AppCompatRatingBar(context2, attributeSet0);
                break;
            }
            case 1: {
                view2 = new AppCompatCheckedTextView(context2, attributeSet0);
                break;
            }
            case 2: {
                view2 = new AppCompatMultiAutoCompleteTextView(context2, attributeSet0);
                break;
            }
            case 3: {
                view2 = appCompatViewInflater0.e(context2, attributeSet0);
                break;
            }
            case 4: {
                view2 = new AppCompatImageButton(context2, attributeSet0);
                break;
            }
            case 5: {
                view2 = new AppCompatSeekBar(context2, attributeSet0);
                break;
            }
            case 6: {
                view2 = new AppCompatSpinner(context2, attributeSet0);
                break;
            }
            case 7: {
                view2 = appCompatViewInflater0.d(context2, attributeSet0);
                break;
            }
            case 8: {
                view2 = new AppCompatToggleButton(context2, attributeSet0);
                break;
            }
            case 9: {
                view2 = new AppCompatImageView(context2, attributeSet0);
                break;
            }
            case 10: {
                view2 = appCompatViewInflater0.a(context2, attributeSet0);
                break;
            }
            case 11: {
                view2 = appCompatViewInflater0.c(context2, attributeSet0);
                break;
            }
            case 12: {
                view2 = new AppCompatEditText(context2, attributeSet0);
                break;
            }
            case 13: {
                view2 = appCompatViewInflater0.b(context2, attributeSet0);
                break;
            }
            default: {
                view2 = null;
            }
        }
        if(view2 == null && context0 != context2) {
            Object[] arr_object = appCompatViewInflater0.a;
            if(s.equals("view")) {
                s = attributeSet0.getAttributeValue(null, "class");
            }
            try {
                arr_object[0] = context2;
                arr_object[1] = attributeSet0;
                if(-1 == s.indexOf(46)) {
                    int v3 = 0;
                    while(true) {
                        String[] arr_s = AppCompatViewInflater.g;
                        if(v3 >= 3) {
                            break;
                        }
                        view3 = appCompatViewInflater0.f(context2, s, arr_s[v3]);
                        if(view3 != null) {
                            goto label_96;
                        }
                        ++v3;
                    }
                }
                else {
                    goto label_104;
                }
                goto label_110;
            }
            catch(Exception unused_ex) {
                goto label_108;
            }
            finally {
                arr_object[0] = null;
                arr_object[1] = null;
            }
        label_96:
            view1 = view3;
            try {
                goto label_110;
            label_104:
                View view4 = appCompatViewInflater0.f(context2, s, null);
                view1 = view4;
            }
            catch(Exception unused_ex) {
            label_108:
                arr_object[0] = null;
                arr_object[1] = null;
            }
        label_110:
            view2 = view1;
        }
        if(view2 != null) {
            Context context3 = view2.getContext();
            if(context3 instanceof ContextWrapper && view2.hasOnClickListeners()) {
                TypedArray typedArray2 = context3.obtainStyledAttributes(attributeSet0, AppCompatViewInflater.c);
                String s2 = typedArray2.getString(0);
                if(s2 != null) {
                    view2.setOnClickListener(new DeclaredOnClickListener(view2, s2));
                }
                typedArray2.recycle();
            }
            if(Build.VERSION.SDK_INT <= 28) {
                TypedArray typedArray3 = context2.obtainStyledAttributes(attributeSet0, AppCompatViewInflater.d);
                if(typedArray3.hasValue(0)) {
                    ViewCompat.D(view2, typedArray3.getBoolean(0, false));
                }
                typedArray3.recycle();
                TypedArray typedArray4 = context2.obtainStyledAttributes(attributeSet0, AppCompatViewInflater.e);
                if(typedArray4.hasValue(0)) {
                    ViewCompat.E(view2, typedArray4.getString(0));
                }
                typedArray4.recycle();
                TypedArray typedArray5 = context2.obtainStyledAttributes(attributeSet0, AppCompatViewInflater.f);
                if(typedArray5.hasValue(0)) {
                    ViewCompat.L(view2, typedArray5.getBoolean(0, false));
                }
                typedArray5.recycle();
            }
        }
        return view2;
    }

    @Override  // android.view.LayoutInflater$Factory
    public final View onCreateView(String s, Context context0, AttributeSet attributeSet0) {
        return this.onCreateView(null, s, context0, attributeSet0);
    }

    public static LocaleListCompat p(Context context0) {
        LocaleListCompat localeListCompat2;
        int v = Build.VERSION.SDK_INT;
        if(v >= 33) {
            return null;
        }
        LocaleListCompat localeListCompat0 = AppCompatDelegate.c;
        if(localeListCompat0 == null) {
            return null;
        }
        LocaleListCompat localeListCompat1 = AppCompatDelegateImpl.z(context0.getApplicationContext().getResources().getConfiguration());
        if(v >= 24) {
            if(localeListCompat0.d()) {
                return LocaleListCompat.b.d() ? localeListCompat1 : LocaleListCompat.b;
            }
            LinkedHashSet linkedHashSet0 = new LinkedHashSet();
            for(int v1 = 0; v1 < localeListCompat1.e() + localeListCompat0.e(); ++v1) {
                Locale locale0 = v1 >= localeListCompat0.e() ? localeListCompat1.c(v1 - localeListCompat0.e()) : localeListCompat0.c(v1);
                if(locale0 != null) {
                    linkedHashSet0.add(locale0);
                }
            }
            localeListCompat2 = LocaleListCompat.a(((Locale[])linkedHashSet0.toArray(new Locale[linkedHashSet0.size()])));
            return localeListCompat2.d() ? localeListCompat1 : localeListCompat2;
        }
        localeListCompat2 = localeListCompat0.d() ? LocaleListCompat.b : LocaleListCompat.b(Api21Impl.b(localeListCompat0.c(0)));
        return localeListCompat2.d() ? localeListCompat1 : localeListCompat2;
    }

    public final void q(int v, PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, MenuBuilder menuBuilder0) {
        if(menuBuilder0 == null) {
            if(appCompatDelegateImpl$PanelFeatureState0 == null && v >= 0) {
                PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState = this.L;
                if(v < arr_appCompatDelegateImpl$PanelFeatureState.length) {
                    appCompatDelegateImpl$PanelFeatureState0 = arr_appCompatDelegateImpl$PanelFeatureState[v];
                }
            }
            if(appCompatDelegateImpl$PanelFeatureState0 != null) {
                menuBuilder0 = appCompatDelegateImpl$PanelFeatureState0.h;
            }
        }
        if(appCompatDelegateImpl$PanelFeatureState0 != null && !appCompatDelegateImpl$PanelFeatureState0.m) {
            return;
        }
        if(!this.Q) {
            AppCompatWindowCallback appCompatDelegateImpl$AppCompatWindowCallback0 = this.m;
            Window.Callback window$Callback0 = this.l.getCallback();
            appCompatDelegateImpl$AppCompatWindowCallback0.getClass();
            try {
                appCompatDelegateImpl$AppCompatWindowCallback0.e = true;
                window$Callback0.onPanelClosed(v, menuBuilder0);
                appCompatDelegateImpl$AppCompatWindowCallback0.e = false;
            }
            catch(Throwable throwable0) {
                appCompatDelegateImpl$AppCompatWindowCallback0.e = false;
                throw throwable0;
            }
        }
    }

    public final void r(MenuBuilder menuBuilder0) {
        if(this.K) {
            return;
        }
        this.K = true;
        ((ActionBarOverlayLayout)this.r).c();
        Window.Callback window$Callback0 = this.l.getCallback();
        if(window$Callback0 != null && !this.Q) {
            window$Callback0.onPanelClosed(108, menuBuilder0);
        }
        this.K = false;
    }

    public final void s(PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, boolean z) {
        if(z && appCompatDelegateImpl$PanelFeatureState0.a == 0) {
            DecorContentParent decorContentParent0 = this.r;
            if(decorContentParent0 != null) {
                ((ActionBarOverlayLayout)decorContentParent0).h();
                if(((ToolbarWidgetWrapper)((ActionBarOverlayLayout)decorContentParent0).e).a.q()) {
                    this.r(appCompatDelegateImpl$PanelFeatureState0.h);
                    return;
                }
            }
        }
        WindowManager windowManager0 = (WindowManager)this.k.getSystemService("window");
        if(windowManager0 != null && appCompatDelegateImpl$PanelFeatureState0.m) {
            ViewGroup viewGroup0 = appCompatDelegateImpl$PanelFeatureState0.e;
            if(viewGroup0 != null) {
                windowManager0.removeView(viewGroup0);
                if(z) {
                    this.q(appCompatDelegateImpl$PanelFeatureState0.a, appCompatDelegateImpl$PanelFeatureState0, null);
                }
            }
        }
        appCompatDelegateImpl$PanelFeatureState0.k = false;
        appCompatDelegateImpl$PanelFeatureState0.l = false;
        appCompatDelegateImpl$PanelFeatureState0.m = false;
        appCompatDelegateImpl$PanelFeatureState0.f = null;
        appCompatDelegateImpl$PanelFeatureState0.n = true;
        if(this.M == appCompatDelegateImpl$PanelFeatureState0) {
            this.M = null;
        }
        if(appCompatDelegateImpl$PanelFeatureState0.a == 0) {
            this.J();
        }
    }

    public static Configuration t(Context context0, int v, LocaleListCompat localeListCompat0, Configuration configuration0, boolean z) {
        int v1;
        switch(v) {
            case 1: {
                v1 = 16;
                break;
            }
            case 2: {
                v1 = 0x20;
                break;
            }
            default: {
                v1 = z ? 0 : context0.getApplicationContext().getResources().getConfiguration().uiMode & 0x30;
            }
        }
        Configuration configuration1 = new Configuration();
        configuration1.fontScale = 0.0f;
        if(configuration0 != null) {
            configuration1.setTo(configuration0);
        }
        configuration1.uiMode = v1 | configuration1.uiMode & -49;
        if(localeListCompat0 != null) {
            if(Build.VERSION.SDK_INT >= 24) {
                Api24Impl.d(configuration1, localeListCompat0);
                return configuration1;
            }
            configuration1.setLocale(localeListCompat0.c(0));
            configuration1.setLayoutDirection(localeListCompat0.c(0));
        }
        return configuration1;
    }

    public final boolean u(KeyEvent keyEvent0) {
        boolean z2;
        boolean z = true;
        if(this.j instanceof Component || this.j instanceof AppCompatDialog) {
            View view0 = this.l.getDecorView();
            if(view0 != null && KeyEventDispatcher.a(view0, keyEvent0)) {
                return true;
            }
        }
        if(keyEvent0.getKeyCode() == 82) {
            AppCompatWindowCallback appCompatDelegateImpl$AppCompatWindowCallback0 = this.m;
            Window.Callback window$Callback0 = this.l.getCallback();
            appCompatDelegateImpl$AppCompatWindowCallback0.getClass();
            try {
                appCompatDelegateImpl$AppCompatWindowCallback0.d = true;
                boolean z1 = window$Callback0.dispatchKeyEvent(keyEvent0);
                appCompatDelegateImpl$AppCompatWindowCallback0.d = false;
            }
            catch(Throwable throwable0) {
                appCompatDelegateImpl$AppCompatWindowCallback0.d = false;
                throw throwable0;
            }
            if(z1) {
                return true;
            }
        }
        int v = keyEvent0.getKeyCode();
        if(keyEvent0.getAction() == 0) {
            switch(v) {
                case 4: {
                    goto label_22;
                }
                case 82: {
                    goto label_26;
                }
            }
            return false;
        label_22:
            if((keyEvent0.getFlags() & 0x80) == 0) {
                z = false;
            }
            this.N = z;
            return false;
        label_26:
            if(keyEvent0.getRepeatCount() == 0) {
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = this.A(0);
                if(!appCompatDelegateImpl$PanelFeatureState0.m) {
                    this.H(appCompatDelegateImpl$PanelFeatureState0, keyEvent0);
                    return true;
                }
            }
        }
        else {
            switch(v) {
                case 4: {
                    return this.E();
                }
                case 82: {
                    if(this.u == null) {
                        PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = this.A(0);
                        DecorContentParent decorContentParent0 = this.r;
                        Context context0 = this.k;
                        if(decorContentParent0 == null) {
                        label_54:
                            boolean z3 = appCompatDelegateImpl$PanelFeatureState1.m;
                            if(z3 || appCompatDelegateImpl$PanelFeatureState1.l) {
                                this.s(appCompatDelegateImpl$PanelFeatureState1, true);
                                z2 = z3;
                            label_66:
                                if(z2) {
                                label_67:
                                    AudioManager audioManager0 = (AudioManager)context0.getApplicationContext().getSystemService("audio");
                                    if(audioManager0 != null) {
                                        audioManager0.playSoundEffect(0);
                                        return true;
                                    }
                                }
                            }
                            else if(appCompatDelegateImpl$PanelFeatureState1.k) {
                                if(appCompatDelegateImpl$PanelFeatureState1.o) {
                                    appCompatDelegateImpl$PanelFeatureState1.k = false;
                                    if(this.H(appCompatDelegateImpl$PanelFeatureState1, keyEvent0)) {
                                        this.F(appCompatDelegateImpl$PanelFeatureState1, keyEvent0);
                                        goto label_67;
                                    }
                                }
                                else {
                                    this.F(appCompatDelegateImpl$PanelFeatureState1, keyEvent0);
                                    goto label_67;
                                }
                            }
                        }
                        else {
                            ((ActionBarOverlayLayout)decorContentParent0).h();
                            Toolbar toolbar0 = ((ToolbarWidgetWrapper)((ActionBarOverlayLayout)decorContentParent0).e).a;
                            if(toolbar0.getVisibility() != 0 || (toolbar0.a == null || !toolbar0.a.s || ViewConfiguration.get(context0).hasPermanentMenuKey())) {
                                goto label_54;
                            }
                            else {
                                ActionBarOverlayLayout actionBarOverlayLayout0 = (ActionBarOverlayLayout)this.r;
                                actionBarOverlayLayout0.h();
                                if(((ToolbarWidgetWrapper)actionBarOverlayLayout0.e).a.q()) {
                                    ActionBarOverlayLayout actionBarOverlayLayout2 = (ActionBarOverlayLayout)this.r;
                                    actionBarOverlayLayout2.h();
                                    z2 = ((ToolbarWidgetWrapper)actionBarOverlayLayout2.e).c();
                                    goto label_66;
                                }
                                else if(!this.Q && this.H(appCompatDelegateImpl$PanelFeatureState1, keyEvent0)) {
                                    ActionBarOverlayLayout actionBarOverlayLayout1 = (ActionBarOverlayLayout)this.r;
                                    actionBarOverlayLayout1.h();
                                    z2 = ((ToolbarWidgetWrapper)actionBarOverlayLayout1.e).a.w();
                                    goto label_66;
                                }
                            }
                        }
                    }
                    break;
                }
                default: {
                    return false;
                }
            }
        }
        return true;
    }

    public final void v(int v) {
        PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = this.A(v);
        if(appCompatDelegateImpl$PanelFeatureState0.h != null) {
            Bundle bundle0 = new Bundle();
            appCompatDelegateImpl$PanelFeatureState0.h.t(bundle0);
            if(bundle0.size() > 0) {
                appCompatDelegateImpl$PanelFeatureState0.p = bundle0;
            }
            appCompatDelegateImpl$PanelFeatureState0.h.w();
            appCompatDelegateImpl$PanelFeatureState0.h.clear();
        }
        appCompatDelegateImpl$PanelFeatureState0.o = true;
        appCompatDelegateImpl$PanelFeatureState0.n = true;
        if((v == 0 || v == 108) && this.r != null) {
            PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = this.A(0);
            appCompatDelegateImpl$PanelFeatureState1.k = false;
            this.H(appCompatDelegateImpl$PanelFeatureState1, null);
        }
    }

    public final void w() {
        ViewGroup viewGroup0;
        if(!this.z) {
            int[] arr_v = R.styleable.j;
            Context context0 = this.k;
            TypedArray typedArray0 = context0.obtainStyledAttributes(arr_v);
            if(!typedArray0.hasValue(0x75)) {
                typedArray0.recycle();
                throw new IllegalStateException("You need to use a Theme.AppCompat theme (or descendant) with this activity.");
            }
            if(typedArray0.getBoolean(0x7E, false)) {
                this.h(1);
            }
            else if(typedArray0.getBoolean(0x75, false)) {
                this.h(108);
            }
            if(typedArray0.getBoolean(0x76, false)) {
                this.h(109);
            }
            if(typedArray0.getBoolean(0x77, false)) {
                this.h(10);
            }
            this.I = typedArray0.getBoolean(0, false);
            typedArray0.recycle();
            this.x();
            this.l.getDecorView();
            LayoutInflater layoutInflater0 = LayoutInflater.from(context0);
            if(this.J) {
                viewGroup0 = this.H ? ((ViewGroup)layoutInflater0.inflate(0x7F0D0016, null)) : ((ViewGroup)layoutInflater0.inflate(0x7F0D0015, null));  // layout:abc_screen_simple_overlay_action_mode
            }
            else if(this.I) {
                viewGroup0 = (ViewGroup)layoutInflater0.inflate(0x7F0D000C, null);  // layout:abc_dialog_title_material
                this.G = false;
                this.F = false;
            }
            else if(this.F) {
                TypedValue typedValue0 = new TypedValue();
                context0.getTheme().resolveAttribute(0x7F040012, typedValue0, true);  // attr:actionBarTheme
                Context context1 = typedValue0.resourceId == 0 ? context0 : new ContextThemeWrapper(context0, typedValue0.resourceId);
                viewGroup0 = (ViewGroup)LayoutInflater.from(context1).inflate(0x7F0D0017, null);  // layout:abc_screen_toolbar
                DecorContentParent decorContentParent0 = (DecorContentParent)viewGroup0.findViewById(0x7F0A0167);  // id:decor_content_parent
                this.r = decorContentParent0;
                decorContentParent0.setWindowCallback(this.l.getCallback());
                if(this.G) {
                    ((ActionBarOverlayLayout)this.r).f(109);
                }
                if(this.D) {
                    ((ActionBarOverlayLayout)this.r).f(2);
                }
                if(this.E) {
                    ((ActionBarOverlayLayout)this.r).f(5);
                }
            }
            else {
                viewGroup0 = null;
            }
            if(viewGroup0 == null) {
                throw new IllegalArgumentException("AppCompat does not support the current theme features: { windowActionBar: " + this.F + ", windowActionBarOverlay: " + this.G + ", android:windowIsFloating: " + this.I + ", windowActionModeOverlay: " + this.H + ", windowNoTitle: " + this.J + " }");
            }
            ViewCompat.J(viewGroup0, new AppCompatDelegateImpl.3(this));
            if(this.r == null) {
                this.B = (TextView)viewGroup0.findViewById(0x7F0A0467);  // id:title
            }
            try {
                Method method0 = viewGroup0.getClass().getMethod("makeOptionalFitsSystemWindows");
                if(!method0.isAccessible()) {
                    method0.setAccessible(true);
                }
                method0.invoke(viewGroup0);
            }
            catch(NoSuchMethodException | InvocationTargetException | IllegalAccessException unused_ex) {
            }
            ContentFrameLayout contentFrameLayout0 = (ContentFrameLayout)viewGroup0.findViewById(0x7F0A0052);  // id:action_bar_activity_content
            ViewGroup viewGroup1 = (ViewGroup)this.l.findViewById(0x1020002);
            if(viewGroup1 != null) {
                while(viewGroup1.getChildCount() > 0) {
                    View view0 = viewGroup1.getChildAt(0);
                    viewGroup1.removeViewAt(0);
                    contentFrameLayout0.addView(view0);
                }
                viewGroup1.setId(-1);
                contentFrameLayout0.setId(0x1020002);
                if(viewGroup1 instanceof FrameLayout) {
                    ((FrameLayout)viewGroup1).setForeground(null);
                }
            }
            this.l.setContentView(viewGroup0);
            contentFrameLayout0.setAttachListener(new AppCompatDelegateImpl.5(this));
            this.A = viewGroup0;
            CharSequence charSequence0 = this.j instanceof Activity ? ((Activity)this.j).getTitle() : this.q;
            if(!TextUtils.isEmpty(charSequence0)) {
                DecorContentParent decorContentParent1 = this.r;
                if(decorContentParent1 == null) {
                    ActionBar actionBar0 = this.o;
                    if(actionBar0 == null) {
                        TextView textView0 = this.B;
                        if(textView0 != null) {
                            textView0.setText(charSequence0);
                        }
                    }
                    else {
                        actionBar0.o(charSequence0);
                    }
                }
                else {
                    decorContentParent1.setWindowTitle(charSequence0);
                }
            }
            ContentFrameLayout contentFrameLayout1 = (ContentFrameLayout)this.A.findViewById(0x1020002);
            View view1 = this.l.getDecorView();
            int v = view1.getPaddingLeft();
            int v1 = view1.getPaddingTop();
            int v2 = view1.getPaddingRight();
            int v3 = view1.getPaddingBottom();
            contentFrameLayout1.g.set(v, v1, v2, v3);
            if(contentFrameLayout1.isLaidOut()) {
                contentFrameLayout1.requestLayout();
            }
            TypedArray typedArray1 = context0.obtainStyledAttributes(arr_v);
            typedArray1.getValue(0x7C, contentFrameLayout1.getMinWidthMajor());
            typedArray1.getValue(0x7D, contentFrameLayout1.getMinWidthMinor());
            if(typedArray1.hasValue(0x7A)) {
                typedArray1.getValue(0x7A, contentFrameLayout1.getFixedWidthMajor());
            }
            if(typedArray1.hasValue(0x7B)) {
                typedArray1.getValue(0x7B, contentFrameLayout1.getFixedWidthMinor());
            }
            if(typedArray1.hasValue(120)) {
                typedArray1.getValue(120, contentFrameLayout1.getFixedHeightMajor());
            }
            if(typedArray1.hasValue(0x79)) {
                typedArray1.getValue(0x79, contentFrameLayout1.getFixedHeightMinor());
            }
            typedArray1.recycle();
            contentFrameLayout1.requestLayout();
            this.z = true;
            PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = this.A(0);
            if(!this.Q && appCompatDelegateImpl$PanelFeatureState0.h == null) {
                this.C(108);
            }
        }
    }

    public final void x() {
        if(this.l == null) {
            Object object0 = this.j;
            if(object0 instanceof Activity) {
                this.o(((Activity)object0).getWindow());
            }
        }
        if(this.l == null) {
            throw new IllegalStateException("We have not been given a Window");
        }
    }

    public final AutoNightModeManager y(Context context0) {
        if(this.W == null) {
            if(TwilightManager.d == null) {
                Context context1 = context0.getApplicationContext();
                TwilightManager.d = new TwilightManager(context1, ((LocationManager)context1.getSystemService("location")));
            }
            this.W = new AutoTimeNightModeManager(this, TwilightManager.d);
        }
        return this.W;
    }

    public static LocaleListCompat z(Configuration configuration0) {
        return Build.VERSION.SDK_INT < 24 ? LocaleListCompat.b(Api21Impl.b(configuration0.locale)) : Api24Impl.b(configuration0);
    }
}


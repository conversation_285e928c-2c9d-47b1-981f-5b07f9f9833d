package androidx.appcompat;

public abstract class R.styleable {
    public static final int[] A;
    public static final int[] a;
    public static final int[] b;
    public static final int[] c;
    public static final int[] d;
    public static final int[] e;
    public static final int[] f;
    public static final int[] g;
    public static final int[] h;
    public static final int[] i;
    public static final int[] j;
    public static final int[] k;
    public static final int[] l;
    public static final int[] m;
    public static final int[] n;
    public static final int[] o;
    public static final int[] p;
    public static final int[] q;
    public static final int[] r;
    public static final int[] s;
    public static final int[] t;
    public static final int[] u;
    public static final int[] v;
    public static final int[] w;
    public static final int[] x;
    public static final int[] y;
    public static final int[] z;

    static {
        R.styleable.a = new int[]{0x7F04005D, 0x7F040064, 0x7F040065, 0x7F04019F, 0x7F0401A0, 0x7F0401A1, 0x7F0401A2, 0x7F0401A3, 0x7F0401A4, 0x7F0401CE, 0x7F0401ED, 0x7F0401EE, 0x7F040214, 0x7F04029F, 0x7F0402A7, 0x7F0402AD, 0x7F0402AE, 0x7F0402B6, 0x7F0402CA, 0x7F0402E4, 0x7F040368, 0x7F040404, 0x7F040443, 0x7F040456, 0x7F040457, 0x7F04056A, 0x7F04056E, 0x7F0405FF, 0x7F04060D};  // attr:background
        R.styleable.b = new int[]{0x10100B3};
        R.styleable.c = new int[]{0x101013F};
        R.styleable.d = new int[]{0x7F04005D, 0x7F040064, 0x7F04013B, 0x7F04029F, 0x7F04056E, 0x7F04060D};  // attr:background
        R.styleable.e = new int[]{0x10100F2, 0x7F0400A8, 0x7F0400AB, 0x7F04035D, 0x7F04035E, 0x7F0403FF, 0x7F0404E4, 0x7F0404EC};  // attr:buttonIconDimen
        R.styleable.f = new int[]{0x1010119, 0x7F040503, 0x7F0405FC, 0x7F0405FD};  // attr:srcCompat
        R.styleable.g = new int[]{0x1010142, 0x7F0405F6, 0x7F0405F7, 0x7F0405F8};  // attr:tickMark
        R.styleable.h = new int[]{0x1010034, 0x101016D, 0x101016E, 0x101016F, 0x1010170, 0x1010392, 0x1010393};
        R.styleable.i = new int[]{0x1010034, 0x7F040056, 0x7F040057, 0x7F040058, 0x7F040059, 0x7F04005A, 0x7F0401FE, 0x7F0401FF, 0x7F040200, 0x7F040201, 0x7F040203, 0x7F040204, 0x7F040205, 0x7F040206, 0x7F040218, 0x7F040258, 0x7F04027C, 0x7F040285, 0x7F040301, 0x7F040356, 0x7F0405A0, 0x7F0405D7};  // attr:autoSizeMaxTextSize
        R.styleable.j = new int[]{0x1010057, 0x10100AE, 0x7F040009, 0x7F04000A, 0x7F04000B, 0x7F04000C, 0x7F04000D, 0x7F04000E, 0x7F04000F, 0x7F040010, 0x7F040011, 0x7F040012, 0x7F040013, 0x7F040014, 0x7F040015, 0x7F040017, 0x7F040018, 0x7F040019, 0x7F04001A, 0x7F04001B, 0x7F04001C, 0x7F04001D, 0x7F04001E, 0x7F04001F, 0x7F040020, 0x7F040021, 0x7F040022, 0x7F040023, 0x7F040024, 0x7F040025, 0x7F040026, 0x7F040027, 0x7F040028, 0x7F040029, 0x7F04002F, 0x7F040036, 0x7F040037, 0x7F040038, 0x7F040039, 0x7F040054, 0x7F04008D, 0x7F0400A0, 0x7F0400A1, 0x7F0400A2, 0x7F0400A3, 0x7F0400A4, 0x7F0400AD, 0x7F0400AE, 0x7F040105, 0x7F040110, 0x7F040148, 0x7F040149, 0x7F04014A, 0x7F04014C, 0x7F04014D, 0x7F04014E, 0x7F04014F, 0x7F040168, 0x7F04016A, 0x7F040180, 0x7F0401AE, 0x7F0401E4, 0x7F0401E9, 0x7F0401EA, 0x7F0401F0, 0x7F0401F5, 0x7F04020B, 0x7F04020C, 0x7F040210, 0x7F040211, 0x7F040213, 0x7F0402AD, 0x7F0402C4, 0x7F040359, 0x7F04035A, 0x7F04035B, 0x7F04035C, 0x7F04035F, 0x7F040360, 0x7F040361, 0x7F040362, 0x7F040363, 0x7F040364, 0x7F040365, 0x7F040366, 0x7F040367, 0x7F040424, 0x7F040425, 0x7F040426, 0x7F040442, 0x7F040444, 0x7F04045E, 0x7F040460, 0x7F040461, 0x7F040462, 0x7F0404AD, 0x7F0404B2, 0x7F0404B4, 0x7F0404B5, 0x7F0404F7, 0x7F0404F8, 0x7F04057A, 0x7F0405B7, 0x7F0405B9, 0x7F0405BA, 0x7F0405BB, 0x7F0405BD, 0x7F0405BE, 0x7F0405BF, 0x7F0405C0, 0x7F0405CB, 0x7F0405CC, 0x7F040610, 0x7F040611, 0x7F040613, 0x7F040614, 0x7F04064D, 0x7F04065D, 0x7F04065E, 0x7F04065F, 0x7F040660, 0x7F040661, 0x7F040662, 0x7F040663, 0x7F040664, 0x7F040665, 0x7F040666};  // attr:actionBarDivider
        R.styleable.k = new int[]{0x7F04003D};  // attr:allowStacking
        R.styleable.l = new int[]{0x1010108, 0x7F040102, 0x7F040103, 0x7F040104};  // attr:checkMarkCompat
        R.styleable.m = new int[]{0x1010107, 0x7F0400A5, 0x7F0400AF, 0x7F0400B0};  // attr:buttonCompat
        R.styleable.n = new int[]{0x10100AF, 0x10100C4, 0x1010126, 0x1010127, 0x1010128, 0x7F0401EE, 0x7F0401F3, 0x7F0403BF, 0x7F0404DE};  // attr:divider
        R.styleable.o = new int[]{0x10102AC, 0x10102AD};
        R.styleable.p = new int[]{0x101000E, 0x10100D0, 0x1010194, 0x10101DE, 0x10101DF, 0x10101E0};
        R.styleable.q = new int[]{0x1010002, 0x101000E, 0x10100D0, 0x1010106, 0x1010194, 0x10101DE, 0x10101DF, 0x10101E1, 0x10101E2, 0x10101E3, 0x10101E4, 0x10101E5, 0x101026F, 0x7F040016, 0x7F04002A, 0x7F04002C, 0x7F04003F, 0x7F04019E, 0x7F0402BD, 0x7F0402BE, 0x7F04040E, 0x7F0404DC, 0x7F040616};  // attr:actionLayout
        R.styleable.r = new int[]{0x10100AE, 0x101012C, 0x101012D, 0x101012E, 0x101012F, 0x1010130, 0x1010131, 0x7F040453, 0x7F040564};  // attr:preserveIconSpacing
        R.styleable.s = new int[]{0x1010176, 0x10102C9, 0x7F040419};  // attr:overlapAnchor
        R.styleable.t = new int[]{0x7F04041B, 0x7F040422};  // attr:paddingBottomNoButtons
        R.styleable.u = new int[]{0x10100B2, 0x1010176, 0x101017B, 0x1010262, 0x7F040443};  // attr:popupTheme
        R.styleable.v = new int[]{0x1010124, 0x1010125, 0x1010142, 0x7F0404E3, 0x7F0404FD, 0x7F040576, 0x7F040577, 0x7F04057B, 0x7F0405EE, 0x7F0405EF, 0x7F0405F0, 0x7F04061B, 0x7F040627, 0x7F040628};  // attr:showText
        R.styleable.w = new int[]{0x1010095, 0x1010096, 0x1010097, 0x1010098, 0x101009A, 0x101009B, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x10103AC, 0x1010585, 0x7F04027C, 0x7F040285, 0x7F0405A0, 0x7F0405D7};  // attr:fontFamily
        R.styleable.x = new int[]{0x10100AF, 0x1010140, 0x7F0400A6, 0x7F04013C, 0x7F04013D, 0x7F04019F, 0x7F0401A0, 0x7F0401A1, 0x7F0401A2, 0x7F0401A3, 0x7F0401A4, 0x7F040368, 0x7F04036A, 0x7F0403B7, 0x7F0403C0, 0x7F040401, 0x7F040402, 0x7F040443, 0x7F04056A, 0x7F04056C, 0x7F04056D, 0x7F0405FF, 0x7F040603, 0x7F040604, 0x7F040605, 0x7F040606, 0x7F040607, 0x7F040608, 0x7F04060A, 0x7F04060B};  // attr:buttonGravity
        R.styleable.y = new int[]{0x1010000, 0x10100DA, 0x7F04041D, 0x7F040420, 0x7F0405E2};  // attr:paddingEnd
        R.styleable.z = new int[]{0x10100D4, 0x7F040066, 0x7F040067};  // attr:backgroundTint
        R.styleable.A = new int[]{0x10100D0, 0x10100F2, 0x10100F3};
    }
}


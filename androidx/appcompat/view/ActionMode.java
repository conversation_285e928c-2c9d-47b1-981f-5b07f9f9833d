package androidx.appcompat.view;

import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import androidx.appcompat.view.menu.MenuBuilder;

public abstract class ActionMode {
    public interface Callback {
        void a(ActionMode arg1);

        boolean b(ActionMode arg1, MenuBuilder arg2);

        boolean c(ActionMode arg1, MenuItem arg2);

        boolean d(ActionMode arg1, MenuBuilder arg2);
    }

    public Object a;
    public boolean b;

    public abstract void c();

    public abstract View d();

    public abstract MenuBuilder e();

    public abstract MenuInflater f();

    public abstract CharSequence g();

    public abstract CharSequence h();

    public abstract void i();

    public abstract boolean j();

    public abstract void k(View arg1);

    public abstract void l(int arg1);

    public abstract void m(CharSequence arg1);

    public abstract void n(int arg1);

    public abstract void o(CharSequence arg1);

    public abstract void p(boolean arg1);
}


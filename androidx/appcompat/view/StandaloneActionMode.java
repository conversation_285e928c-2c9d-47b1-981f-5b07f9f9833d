package androidx.appcompat.view;

import android.content.Context;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import androidx.appcompat.view.menu.MenuBuilder.Callback;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.widget.ActionBarContextView;
import java.lang.ref.WeakReference;

public class StandaloneActionMode extends ActionMode implements Callback {
    public Context c;
    public ActionBarContextView d;
    public androidx.appcompat.view.ActionMode.Callback e;
    public WeakReference f;
    public boolean g;
    public MenuBuilder h;

    @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
    public final boolean a(MenuBuilder menuBuilder0, MenuItem menuItem0) {
        return this.e.c(this, menuItem0);
    }

    @Override  // androidx.appcompat.view.menu.MenuBuilder$Callback
    public final void b(MenuBuilder menuBuilder0) {
        this.i();
        this.d.i();
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final void c() {
        if(this.g) {
            return;
        }
        this.g = true;
        this.e.a(this);
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final View d() {
        return this.f == null ? null : ((View)this.f.get());
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final MenuBuilder e() {
        return this.h;
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final MenuInflater f() {
        return new SupportMenuInflater(this.d.getContext());
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final CharSequence g() {
        return this.d.getSubtitle();
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final CharSequence h() {
        return this.d.getTitle();
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final void i() {
        this.e.d(this, this.h);
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final boolean j() {
        return this.d.s;
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final void k(View view0) {
        this.d.setCustomView(view0);
        this.f = view0 == null ? null : new WeakReference(view0);
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final void l(int v) {
        this.m(this.c.getString(v));
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final void m(CharSequence charSequence0) {
        this.d.setSubtitle(charSequence0);
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final void n(int v) {
        this.o(this.c.getString(v));
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final void o(CharSequence charSequence0) {
        this.d.setTitle(charSequence0);
    }

    @Override  // androidx.appcompat.view.ActionMode
    public final void p(boolean z) {
        this.b = z;
        this.d.setTitleOptional(z);
    }
}


package androidx.appcompat.view;

import a.a;
import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.PorterDuff.Mode;
import android.util.AttributeSet;
import android.util.Xml;
import android.view.InflateException;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem.OnMenuItemClickListener;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import androidx.appcompat.R.styleable;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuItemImpl;
import androidx.appcompat.view.menu.MenuItemWrapperICS;
import androidx.appcompat.widget.DrawableUtils;
import androidx.appcompat.widget.TintTypedArray;
import androidx.core.internal.view.SupportMenu;
import androidx.core.internal.view.SupportMenuItem;
import androidx.core.view.ActionProvider;
import androidx.core.view.MenuItemCompat;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import org.xmlpull.v1.XmlPullParserException;

public class SupportMenuInflater extends MenuInflater {
    static class InflatedOnMenuItemClickListener implements MenuItem.OnMenuItemClickListener {
        public Object a;
        public Method b;
        public static final Class[] c;

        static {
            InflatedOnMenuItemClickListener.c = new Class[]{MenuItem.class};
        }

        @Override  // android.view.MenuItem$OnMenuItemClickListener
        public final boolean onMenuItemClick(MenuItem menuItem0) {
            try {
                Method method0 = this.b;
                Class class0 = method0.getReturnType();
                Object object0 = this.a;
                if(class0 == Boolean.TYPE) {
                    return ((Boolean)method0.invoke(object0, menuItem0)).booleanValue();
                }
                method0.invoke(object0, menuItem0);
                return true;
            }
            catch(Exception exception0) {
            }
            throw new RuntimeException(exception0);
        }
    }

    class MenuState {
        public CharSequence A;
        public CharSequence B;
        public ColorStateList C;
        public PorterDuff.Mode D;
        public final SupportMenuInflater E;
        public final Menu a;
        public int b;
        public int c;
        public int d;
        public int e;
        public boolean f;
        public boolean g;
        public boolean h;
        public int i;
        public int j;
        public CharSequence k;
        public CharSequence l;
        public int m;
        public char n;
        public int o;
        public char p;
        public int q;
        public int r;
        public boolean s;
        public boolean t;
        public boolean u;
        public int v;
        public int w;
        public String x;
        public String y;
        public ActionProvider z;

        public MenuState(Menu menu0) {
            this.C = null;
            this.D = null;
            this.a = menu0;
            this.b = 0;
            this.c = 0;
            this.d = 0;
            this.e = 0;
            this.f = true;
            this.g = true;
        }

        public final Object a(String s, Class[] arr_class, Object[] arr_object) {
            try {
                Constructor constructor0 = Class.forName(s, false, this.E.c.getClassLoader()).getConstructor(arr_class);
                constructor0.setAccessible(true);
                return constructor0.newInstance(arr_object);
            }
            catch(Exception unused_ex) {
                return null;
            }
        }

        public final void b(MenuItem menuItem0) {
            boolean z = false;
            menuItem0.setChecked(this.s).setVisible(this.t).setEnabled(this.u).setCheckable(this.r >= 1).setTitleCondensed(this.l).setIcon(this.m);
            int v = this.v;
            if(v >= 0) {
                menuItem0.setShowAsAction(v);
            }
            SupportMenuInflater supportMenuInflater0 = this.E;
            if(this.y != null) {
                if(supportMenuInflater0.c.isRestricted()) {
                    throw new IllegalStateException("The android:onClick attribute cannot be used within a restricted context");
                }
                if(supportMenuInflater0.d == null) {
                    supportMenuInflater0.d = SupportMenuInflater.a(supportMenuInflater0.c);
                }
                Object object0 = supportMenuInflater0.d;
                String s = this.y;
                InflatedOnMenuItemClickListener supportMenuInflater$InflatedOnMenuItemClickListener0 = new InflatedOnMenuItemClickListener();  // 初始化器: Ljava/lang/Object;-><init>()V
                supportMenuInflater$InflatedOnMenuItemClickListener0.a = object0;
                Class class0 = object0.getClass();
                try {
                    supportMenuInflater$InflatedOnMenuItemClickListener0.b = class0.getMethod(s, InflatedOnMenuItemClickListener.c);
                }
                catch(Exception exception0) {
                    StringBuilder stringBuilder0 = a.y("Couldn\'t resolve menu item onClick handler ", s, " in class ");
                    stringBuilder0.append(class0.getName());
                    InflateException inflateException0 = new InflateException(stringBuilder0.toString());
                    inflateException0.initCause(exception0);
                    throw inflateException0;
                }
                menuItem0.setOnMenuItemClickListener(supportMenuInflater$InflatedOnMenuItemClickListener0);
            }
            if(this.r >= 2) {
                if(menuItem0 instanceof MenuItemImpl) {
                    ((MenuItemImpl)menuItem0).f(true);
                }
                else if(menuItem0 instanceof MenuItemWrapperICS) {
                    MenuItemWrapperICS menuItemWrapperICS0 = (MenuItemWrapperICS)menuItem0;
                    SupportMenuItem supportMenuItem0 = menuItemWrapperICS0.c;
                    try {
                        if(menuItemWrapperICS0.d == null) {
                            menuItemWrapperICS0.d = supportMenuItem0.getClass().getDeclaredMethod("setExclusiveCheckable", Boolean.TYPE);
                        }
                        menuItemWrapperICS0.d.invoke(supportMenuItem0, Boolean.TRUE);
                    }
                    catch(Exception unused_ex) {
                    }
                }
            }
            String s1 = this.x;
            if(s1 != null) {
                menuItem0.setActionView(((View)this.a(s1, SupportMenuInflater.e, supportMenuInflater0.a)));
                z = true;
            }
            int v1 = this.w;
            if(v1 > 0 && !z) {
                menuItem0.setActionView(v1);
            }
            ActionProvider actionProvider0 = this.z;
            if(actionProvider0 != null && menuItem0 instanceof SupportMenuItem) {
                ((SupportMenuItem)menuItem0).a(actionProvider0);
            }
            MenuItemCompat.b(menuItem0, this.A);
            MenuItemCompat.f(menuItem0, this.B);
            MenuItemCompat.a(menuItem0, this.n, this.o);
            MenuItemCompat.e(menuItem0, this.p, this.q);
            PorterDuff.Mode porterDuff$Mode0 = this.D;
            if(porterDuff$Mode0 != null) {
                MenuItemCompat.d(menuItem0, porterDuff$Mode0);
            }
            ColorStateList colorStateList0 = this.C;
            if(colorStateList0 != null) {
                MenuItemCompat.c(menuItem0, colorStateList0);
            }
        }
    }

    public final Object[] a;
    public final Object[] b;
    public final Context c;
    public Object d;
    public static final Class[] e;
    public static final Class[] f;

    static {
        Class[] arr_class = {Context.class};
        SupportMenuInflater.e = arr_class;
        SupportMenuInflater.f = arr_class;
    }

    public SupportMenuInflater(Context context0) {
        super(context0);
        this.c = context0;
        Object[] arr_object = {context0};
        this.a = arr_object;
        this.b = arr_object;
    }

    public static Object a(Context context0) {
        if(context0 instanceof Activity) {
            return context0;
        }
        return context0 instanceof ContextWrapper ? SupportMenuInflater.a(((ContextWrapper)context0).getBaseContext()) : context0;
    }

    public final void b(XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Menu menu0) {
        MenuState supportMenuInflater$MenuState0 = new MenuState(this, menu0);
        int v = xmlResourceParser0.getEventType();
        do {
            if(v == 2) {
                String s = xmlResourceParser0.getName();
                if(!s.equals("menu")) {
                    throw new RuntimeException("Expecting menu, got " + s);
                }
                v = xmlResourceParser0.next();
                break;
            }
            v = xmlResourceParser0.next();
        }
        while(v != 1);
        boolean z = false;
        String s1 = null;
        boolean z1 = false;
        while(!z1) {
            if(v == 1) {
                throw new RuntimeException("Unexpected end of document");
            }
            switch(v) {
                case 2: {
                    if(!z) {
                        String s2 = xmlResourceParser0.getName();
                        SupportMenuInflater supportMenuInflater0 = supportMenuInflater$MenuState0.E;
                        if(s2.equals("group")) {
                            TypedArray typedArray0 = supportMenuInflater0.c.obtainStyledAttributes(attributeSet0, R.styleable.p);
                            supportMenuInflater$MenuState0.b = typedArray0.getResourceId(1, 0);
                            supportMenuInflater$MenuState0.c = typedArray0.getInt(3, 0);
                            supportMenuInflater$MenuState0.d = typedArray0.getInt(4, 0);
                            supportMenuInflater$MenuState0.e = typedArray0.getInt(5, 0);
                            supportMenuInflater$MenuState0.f = typedArray0.getBoolean(2, true);
                            supportMenuInflater$MenuState0.g = typedArray0.getBoolean(0, true);
                            typedArray0.recycle();
                        }
                        else if(s2.equals("item")) {
                            TypedArray typedArray1 = supportMenuInflater0.c.obtainStyledAttributes(attributeSet0, R.styleable.q);
                            TintTypedArray tintTypedArray0 = new TintTypedArray(supportMenuInflater0.c, typedArray1);
                            supportMenuInflater$MenuState0.i = typedArray1.getResourceId(2, 0);
                            int v1 = typedArray1.getInt(5, supportMenuInflater$MenuState0.c);
                            supportMenuInflater$MenuState0.j = typedArray1.getInt(6, supportMenuInflater$MenuState0.d) & 0xFFFF | v1 & 0xFFFF0000;
                            supportMenuInflater$MenuState0.k = typedArray1.getText(7);
                            supportMenuInflater$MenuState0.l = typedArray1.getText(8);
                            supportMenuInflater$MenuState0.m = typedArray1.getResourceId(0, 0);
                            String s3 = typedArray1.getString(9);
                            supportMenuInflater$MenuState0.n = s3 == null ? '\u0000' : s3.charAt(0);
                            supportMenuInflater$MenuState0.o = typedArray1.getInt(16, 0x1000);
                            String s4 = typedArray1.getString(10);
                            supportMenuInflater$MenuState0.p = s4 == null ? '\u0000' : s4.charAt(0);
                            supportMenuInflater$MenuState0.q = typedArray1.getInt(20, 0x1000);
                            supportMenuInflater$MenuState0.r = typedArray1.hasValue(11) ? typedArray1.getBoolean(11, false) : supportMenuInflater$MenuState0.e;
                            supportMenuInflater$MenuState0.s = typedArray1.getBoolean(3, false);
                            supportMenuInflater$MenuState0.t = typedArray1.getBoolean(4, supportMenuInflater$MenuState0.f);
                            supportMenuInflater$MenuState0.u = typedArray1.getBoolean(1, supportMenuInflater$MenuState0.g);
                            supportMenuInflater$MenuState0.v = typedArray1.getInt(21, -1);
                            supportMenuInflater$MenuState0.y = typedArray1.getString(12);
                            supportMenuInflater$MenuState0.w = typedArray1.getResourceId(13, 0);
                            supportMenuInflater$MenuState0.x = typedArray1.getString(15);
                            String s5 = typedArray1.getString(14);
                            supportMenuInflater$MenuState0.z = s5 == null || supportMenuInflater$MenuState0.w != 0 || supportMenuInflater$MenuState0.x != null ? null : ((ActionProvider)supportMenuInflater$MenuState0.a(s5, SupportMenuInflater.f, supportMenuInflater0.b));
                            supportMenuInflater$MenuState0.A = typedArray1.getText(17);
                            supportMenuInflater$MenuState0.B = typedArray1.getText(22);
                            supportMenuInflater$MenuState0.D = typedArray1.hasValue(19) ? DrawableUtils.c(typedArray1.getInt(19, -1), supportMenuInflater$MenuState0.D) : null;
                            supportMenuInflater$MenuState0.C = typedArray1.hasValue(18) ? tintTypedArray0.a(18) : null;
                            tintTypedArray0.f();
                            supportMenuInflater$MenuState0.h = false;
                        }
                        else if(s2.equals("menu")) {
                            supportMenuInflater$MenuState0.h = true;
                            SubMenu subMenu0 = supportMenuInflater$MenuState0.a.addSubMenu(supportMenuInflater$MenuState0.b, supportMenuInflater$MenuState0.i, supportMenuInflater$MenuState0.j, supportMenuInflater$MenuState0.k);
                            supportMenuInflater$MenuState0.b(subMenu0.getItem());
                            this.b(xmlResourceParser0, attributeSet0, subMenu0);
                        }
                        else {
                            s1 = s2;
                            z = true;
                        }
                    }
                    break;
                }
                case 3: {
                    String s6 = xmlResourceParser0.getName();
                    if(z && s6.equals(s1)) {
                        z = false;
                        s1 = null;
                    }
                    else if(s6.equals("group")) {
                        supportMenuInflater$MenuState0.b = 0;
                        supportMenuInflater$MenuState0.c = 0;
                        supportMenuInflater$MenuState0.d = 0;
                        supportMenuInflater$MenuState0.e = 0;
                        supportMenuInflater$MenuState0.f = true;
                        supportMenuInflater$MenuState0.g = true;
                    }
                    else if(!s6.equals("item")) {
                        if(s6.equals("menu")) {
                            z1 = true;
                        }
                    }
                    else if(!supportMenuInflater$MenuState0.h) {
                        if(supportMenuInflater$MenuState0.z == null || !supportMenuInflater$MenuState0.z.a()) {
                            supportMenuInflater$MenuState0.h = true;
                            supportMenuInflater$MenuState0.b(supportMenuInflater$MenuState0.a.add(supportMenuInflater$MenuState0.b, supportMenuInflater$MenuState0.i, supportMenuInflater$MenuState0.j, supportMenuInflater$MenuState0.k));
                        }
                        else {
                            supportMenuInflater$MenuState0.h = true;
                            supportMenuInflater$MenuState0.b(supportMenuInflater$MenuState0.a.addSubMenu(supportMenuInflater$MenuState0.b, supportMenuInflater$MenuState0.i, supportMenuInflater$MenuState0.j, supportMenuInflater$MenuState0.k).getItem());
                        }
                    }
                }
            }
            v = xmlResourceParser0.next();
        }
    }

    @Override  // android.view.MenuInflater
    public final void inflate(int v, Menu menu0) {
        XmlResourceParser xmlResourceParser0;
        if(!(menu0 instanceof SupportMenu)) {
            super.inflate(v, menu0);
            return;
        }
        try {
            try {
                xmlResourceParser0 = null;
                boolean z = false;
                xmlResourceParser0 = this.c.getResources().getLayout(v);
                AttributeSet attributeSet0 = Xml.asAttributeSet(xmlResourceParser0);
                if(menu0 instanceof MenuBuilder && !((MenuBuilder)menu0).p != 0) {
                    ((MenuBuilder)menu0).w();
                    z = true;
                }
                this.b(xmlResourceParser0, attributeSet0, menu0);
                goto label_24;
            }
            catch(XmlPullParserException xmlPullParserException0) {
            }
            catch(IOException iOException0) {
                throw new InflateException("Error inflating menu XML", iOException0);
            }
            throw new InflateException("Error inflating menu XML", xmlPullParserException0);
        }
        catch(Throwable throwable0) {
        }
        if(z) {
            ((MenuBuilder)menu0).v();
        }
        if(xmlResourceParser0 != null) {
            xmlResourceParser0.close();
        }
        throw throwable0;
    label_24:
        if(z) {
            ((MenuBuilder)menu0).v();
        }
        xmlResourceParser0.close();
    }
}


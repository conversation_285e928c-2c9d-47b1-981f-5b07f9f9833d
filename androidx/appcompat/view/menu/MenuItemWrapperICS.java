package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.view.ActionProvider.VisibilityListener;
import android.view.ContextMenu.ContextMenuInfo;
import android.view.MenuItem.OnActionExpandListener;
import android.view.MenuItem.OnMenuItemClickListener;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.widget.FrameLayout;
import androidx.appcompat.view.CollapsibleActionView;
import androidx.core.internal.view.SupportMenuItem;
import androidx.core.view.ActionProvider.VisibilityListener;
import androidx.core.view.ActionProvider;
import java.lang.reflect.Method;

public class MenuItemWrapperICS extends BaseMenuWrapper implements MenuItem {
    class ActionProviderWrapper extends ActionProvider implements ActionProvider.VisibilityListener {
        public VisibilityListener a;
        public final android.view.ActionProvider b;
        public final MenuItemWrapperICS c;

        public ActionProviderWrapper(android.view.ActionProvider actionProvider0) {
            this.b = actionProvider0;
        }

        @Override  // androidx.core.view.ActionProvider
        public final boolean a() {
            return this.b.hasSubMenu();
        }

        @Override  // android.view.ActionProvider$VisibilityListener
        public final void onActionProviderVisibilityChanged(boolean z) {
            VisibilityListener actionProvider$VisibilityListener0 = this.a;
            if(actionProvider$VisibilityListener0 != null) {
                ((MenuItemImpl.1)actionProvider$VisibilityListener0).a.n.h = true;
                ((MenuItemImpl.1)actionProvider$VisibilityListener0).a.n.p(true);
            }
        }
    }

    static class CollapsibleActionViewWrapper extends FrameLayout implements CollapsibleActionView {
        public final android.view.CollapsibleActionView a;

        public CollapsibleActionViewWrapper(View view0) {
            super(view0.getContext());
            this.a = (android.view.CollapsibleActionView)view0;
            this.addView(view0);
        }

        @Override  // androidx.appcompat.view.CollapsibleActionView
        public final void c() {
            this.a.onActionViewExpanded();
        }

        @Override  // androidx.appcompat.view.CollapsibleActionView
        public final void e() {
            this.a.onActionViewCollapsed();
        }
    }

    class OnActionExpandListenerWrapper implements MenuItem.OnActionExpandListener {
        public final MenuItem.OnActionExpandListener a;
        public final MenuItemWrapperICS b;

        public OnActionExpandListenerWrapper(MenuItem.OnActionExpandListener menuItem$OnActionExpandListener0) {
            this.a = menuItem$OnActionExpandListener0;
        }

        @Override  // android.view.MenuItem$OnActionExpandListener
        public final boolean onMenuItemActionCollapse(MenuItem menuItem0) {
            MenuItem menuItem1 = this.b.b(menuItem0);
            return this.a.onMenuItemActionCollapse(menuItem1);
        }

        @Override  // android.view.MenuItem$OnActionExpandListener
        public final boolean onMenuItemActionExpand(MenuItem menuItem0) {
            MenuItem menuItem1 = this.b.b(menuItem0);
            return this.a.onMenuItemActionExpand(menuItem1);
        }
    }

    class OnMenuItemClickListenerWrapper implements MenuItem.OnMenuItemClickListener {
        public final MenuItem.OnMenuItemClickListener a;
        public final MenuItemWrapperICS b;

        public OnMenuItemClickListenerWrapper(MenuItem.OnMenuItemClickListener menuItem$OnMenuItemClickListener0) {
            this.a = menuItem$OnMenuItemClickListener0;
        }

        @Override  // android.view.MenuItem$OnMenuItemClickListener
        public final boolean onMenuItemClick(MenuItem menuItem0) {
            MenuItem menuItem1 = this.b.b(menuItem0);
            return this.a.onMenuItemClick(menuItem1);
        }
    }

    public final SupportMenuItem c;
    public Method d;

    public MenuItemWrapperICS(Context context0, SupportMenuItem supportMenuItem0) {
        super(context0);
        if(supportMenuItem0 == null) {
            throw new IllegalArgumentException("Wrapped Object can not be null.");
        }
        this.c = supportMenuItem0;
    }

    @Override  // android.view.MenuItem
    public final boolean collapseActionView() {
        return this.c.collapseActionView();
    }

    @Override  // android.view.MenuItem
    public final boolean expandActionView() {
        return this.c.expandActionView();
    }

    @Override  // android.view.MenuItem
    public final android.view.ActionProvider getActionProvider() {
        ActionProvider actionProvider0 = this.c.getSupportActionProvider();
        return actionProvider0 instanceof ActionProviderWrapper ? ((ActionProviderWrapper)actionProvider0).b : null;
    }

    @Override  // android.view.MenuItem
    public final View getActionView() {
        View view0 = this.c.getActionView();
        return view0 instanceof CollapsibleActionViewWrapper ? ((View)((CollapsibleActionViewWrapper)view0).a) : view0;
    }

    @Override  // android.view.MenuItem
    public final int getAlphabeticModifiers() {
        return this.c.getAlphabeticModifiers();
    }

    @Override  // android.view.MenuItem
    public final char getAlphabeticShortcut() {
        return this.c.getAlphabeticShortcut();
    }

    @Override  // android.view.MenuItem
    public final CharSequence getContentDescription() {
        return this.c.getContentDescription();
    }

    @Override  // android.view.MenuItem
    public final int getGroupId() {
        return this.c.getGroupId();
    }

    @Override  // android.view.MenuItem
    public final Drawable getIcon() {
        return this.c.getIcon();
    }

    @Override  // android.view.MenuItem
    public final ColorStateList getIconTintList() {
        return this.c.getIconTintList();
    }

    @Override  // android.view.MenuItem
    public final PorterDuff.Mode getIconTintMode() {
        return this.c.getIconTintMode();
    }

    @Override  // android.view.MenuItem
    public final Intent getIntent() {
        return this.c.getIntent();
    }

    @Override  // android.view.MenuItem
    public final int getItemId() {
        return this.c.getItemId();
    }

    @Override  // android.view.MenuItem
    public final ContextMenu.ContextMenuInfo getMenuInfo() {
        return this.c.getMenuInfo();
    }

    @Override  // android.view.MenuItem
    public final int getNumericModifiers() {
        return this.c.getNumericModifiers();
    }

    @Override  // android.view.MenuItem
    public final char getNumericShortcut() {
        return this.c.getNumericShortcut();
    }

    @Override  // android.view.MenuItem
    public final int getOrder() {
        return this.c.getOrder();
    }

    @Override  // android.view.MenuItem
    public final SubMenu getSubMenu() {
        return this.c.getSubMenu();
    }

    @Override  // android.view.MenuItem
    public final CharSequence getTitle() {
        return this.c.getTitle();
    }

    @Override  // android.view.MenuItem
    public final CharSequence getTitleCondensed() {
        return this.c.getTitleCondensed();
    }

    @Override  // android.view.MenuItem
    public final CharSequence getTooltipText() {
        return this.c.getTooltipText();
    }

    @Override  // android.view.MenuItem
    public final boolean hasSubMenu() {
        return this.c.hasSubMenu();
    }

    @Override  // android.view.MenuItem
    public final boolean isActionViewExpanded() {
        return this.c.isActionViewExpanded();
    }

    @Override  // android.view.MenuItem
    public final boolean isCheckable() {
        return this.c.isCheckable();
    }

    @Override  // android.view.MenuItem
    public final boolean isChecked() {
        return this.c.isChecked();
    }

    @Override  // android.view.MenuItem
    public final boolean isEnabled() {
        return this.c.isEnabled();
    }

    @Override  // android.view.MenuItem
    public final boolean isVisible() {
        return this.c.isVisible();
    }

    @Override  // android.view.MenuItem
    public final MenuItem setActionProvider(android.view.ActionProvider actionProvider0) {
        ActionProviderWrapper menuItemWrapperICS$ActionProviderWrapper0 = new ActionProviderWrapper(this, actionProvider0);
        if(actionProvider0 == null) {
            menuItemWrapperICS$ActionProviderWrapper0 = null;
        }
        this.c.a(menuItemWrapperICS$ActionProviderWrapper0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setActionView(int v) {
        SupportMenuItem supportMenuItem0 = this.c;
        supportMenuItem0.setActionView(v);
        View view0 = supportMenuItem0.getActionView();
        if(view0 instanceof android.view.CollapsibleActionView) {
            supportMenuItem0.setActionView(new CollapsibleActionViewWrapper(view0));
        }
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setActionView(View view0) {
        if(view0 instanceof android.view.CollapsibleActionView) {
            view0 = new CollapsibleActionViewWrapper(view0);
        }
        this.c.setActionView(view0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setAlphabeticShortcut(char c) {
        this.c.setAlphabeticShortcut(c);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setAlphabeticShortcut(char c, int v) {
        this.c.setAlphabeticShortcut(c, v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setCheckable(boolean z) {
        this.c.setCheckable(z);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setChecked(boolean z) {
        this.c.setChecked(z);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setContentDescription(CharSequence charSequence0) {
        this.c.setContentDescription(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setEnabled(boolean z) {
        this.c.setEnabled(z);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIcon(int v) {
        this.c.setIcon(v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIcon(Drawable drawable0) {
        this.c.setIcon(drawable0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIconTintList(ColorStateList colorStateList0) {
        this.c.setIconTintList(colorStateList0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIconTintMode(PorterDuff.Mode porterDuff$Mode0) {
        this.c.setIconTintMode(porterDuff$Mode0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIntent(Intent intent0) {
        this.c.setIntent(intent0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setNumericShortcut(char c) {
        this.c.setNumericShortcut(c);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setNumericShortcut(char c, int v) {
        this.c.setNumericShortcut(c, v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setOnActionExpandListener(MenuItem.OnActionExpandListener menuItem$OnActionExpandListener0) {
        OnActionExpandListenerWrapper menuItemWrapperICS$OnActionExpandListenerWrapper0 = menuItem$OnActionExpandListener0 == null ? null : new OnActionExpandListenerWrapper(this, menuItem$OnActionExpandListener0);
        this.c.setOnActionExpandListener(menuItemWrapperICS$OnActionExpandListenerWrapper0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setOnMenuItemClickListener(MenuItem.OnMenuItemClickListener menuItem$OnMenuItemClickListener0) {
        OnMenuItemClickListenerWrapper menuItemWrapperICS$OnMenuItemClickListenerWrapper0 = menuItem$OnMenuItemClickListener0 == null ? null : new OnMenuItemClickListenerWrapper(this, menuItem$OnMenuItemClickListener0);
        this.c.setOnMenuItemClickListener(menuItemWrapperICS$OnMenuItemClickListenerWrapper0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setShortcut(char c, char c1) {
        this.c.setShortcut(c, c1);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setShortcut(char c, char c1, int v, int v1) {
        this.c.setShortcut(c, c1, v, v1);
        return this;
    }

    @Override  // android.view.MenuItem
    public final void setShowAsAction(int v) {
        this.c.setShowAsAction(v);
    }

    @Override  // android.view.MenuItem
    public final MenuItem setShowAsActionFlags(int v) {
        this.c.setShowAsActionFlags(v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitle(int v) {
        this.c.setTitle(v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitle(CharSequence charSequence0) {
        this.c.setTitle(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitleCondensed(CharSequence charSequence0) {
        this.c.setTitleCondensed(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTooltipText(CharSequence charSequence0) {
        this.c.setTooltipText(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setVisible(boolean z) {
        return this.c.setVisible(z);
    }
}


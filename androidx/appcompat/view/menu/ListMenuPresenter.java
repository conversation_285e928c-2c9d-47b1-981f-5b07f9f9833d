package androidx.appcompat.view.menu;

import android.content.Context;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager.LayoutParams;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListAdapter;
import androidx.appcompat.app.AlertController.AlertParams;
import androidx.appcompat.app.AlertDialog.Builder;
import androidx.appcompat.app.AlertDialog;
import java.util.ArrayList;

public class ListMenuPresenter implements AdapterView.OnItemClickListener, MenuPresenter {
    class MenuAdapter extends BaseAdapter {
        public int a;
        public final ListMenuPresenter b;

        public MenuAdapter() {
            this.a = -1;
            this.a();
        }

        public final void a() {
            MenuBuilder menuBuilder0 = ListMenuPresenter.this.c;
            MenuItemImpl menuItemImpl0 = menuBuilder0.v;
            if(menuItemImpl0 != null) {
                menuBuilder0.i();
                ArrayList arrayList0 = menuBuilder0.j;
                int v = arrayList0.size();
                for(int v1 = 0; v1 < v; ++v1) {
                    if(((MenuItemImpl)arrayList0.get(v1)) == menuItemImpl0) {
                        this.a = v1;
                        return;
                    }
                }
            }
            this.a = -1;
        }

        public final MenuItemImpl b(int v) {
            MenuBuilder menuBuilder0 = ListMenuPresenter.this.c;
            menuBuilder0.i();
            ArrayList arrayList0 = menuBuilder0.j;
            ListMenuPresenter.this.getClass();
            if(this.a >= 0 && v >= this.a) {
                ++v;
            }
            return (MenuItemImpl)arrayList0.get(v);
        }

        @Override  // android.widget.Adapter
        public final int getCount() {
            MenuBuilder menuBuilder0 = ListMenuPresenter.this.c;
            menuBuilder0.i();
            int v = menuBuilder0.j.size();
            ListMenuPresenter.this.getClass();
            return this.a >= 0 ? v - 1 : v;
        }

        @Override  // android.widget.Adapter
        public final Object getItem(int v) {
            return this.b(v);
        }

        @Override  // android.widget.Adapter
        public final long getItemId(int v) {
            return (long)v;
        }

        @Override  // android.widget.Adapter
        public final View getView(int v, View view0, ViewGroup viewGroup0) {
            if(view0 == null) {
                view0 = ListMenuPresenter.this.b.inflate(0x7F0D0010, viewGroup0, false);  // layout:abc_list_menu_item_layout
            }
            ((ItemView)view0).d(this.b(v));
            return view0;
        }

        @Override  // android.widget.BaseAdapter
        public final void notifyDataSetChanged() {
            this.a();
            super.notifyDataSetChanged();
        }
    }

    public Context a;
    public LayoutInflater b;
    public MenuBuilder c;
    public ExpandedMenuView d;
    public Callback e;
    public MenuAdapter f;

    public ListMenuPresenter(Context context0) {
        this.a = context0;
        this.b = LayoutInflater.from(context0);
    }

    public final ListAdapter a() {
        if(this.f == null) {
            this.f = new MenuAdapter(this);
        }
        return this.f;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean b() {
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void c(MenuBuilder menuBuilder0, boolean z) {
        Callback menuPresenter$Callback0 = this.e;
        if(menuPresenter$Callback0 != null) {
            menuPresenter$Callback0.c(menuBuilder0, z);
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void d(boolean z) {
        MenuAdapter listMenuPresenter$MenuAdapter0 = this.f;
        if(listMenuPresenter$MenuAdapter0 != null) {
            listMenuPresenter$MenuAdapter0.notifyDataSetChanged();
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void e(Callback menuPresenter$Callback0) {
        this.e = menuPresenter$Callback0;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean f(MenuItemImpl menuItemImpl0) {
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void g(Context context0, MenuBuilder menuBuilder0) {
        if(this.a != null) {
            this.a = context0;
            if(this.b == null) {
                this.b = LayoutInflater.from(context0);
            }
        }
        this.c = menuBuilder0;
        MenuAdapter listMenuPresenter$MenuAdapter0 = this.f;
        if(listMenuPresenter$MenuAdapter0 != null) {
            listMenuPresenter$MenuAdapter0.notifyDataSetChanged();
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final int getId() {
        return 0;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void h(Parcelable parcelable0) {
        SparseArray sparseArray0 = ((Bundle)parcelable0).getSparseParcelableArray("android:menu:list");
        if(sparseArray0 != null) {
            this.d.restoreHierarchyState(sparseArray0);
        }
    }

    public final MenuView i(ViewGroup viewGroup0) {
        if(this.d == null) {
            this.d = (ExpandedMenuView)this.b.inflate(0x7F0D000D, viewGroup0, false);  // layout:abc_expanded_menu_layout
            if(this.f == null) {
                this.f = new MenuAdapter(this);
            }
            this.d.setAdapter(this.f);
            this.d.setOnItemClickListener(this);
        }
        return this.d;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean j(SubMenuBuilder subMenuBuilder0) {
        if(!subMenuBuilder0.hasVisibleItems()) {
            return false;
        }
        MenuDialogHelper menuDialogHelper0 = new MenuDialogHelper();  // 初始化器: Ljava/lang/Object;-><init>()V
        menuDialogHelper0.a = subMenuBuilder0;
        Builder alertDialog$Builder0 = new Builder(subMenuBuilder0.a);
        AlertParams alertController$AlertParams0 = alertDialog$Builder0.a;
        ListMenuPresenter listMenuPresenter0 = new ListMenuPresenter(alertController$AlertParams0.a);
        menuDialogHelper0.c = listMenuPresenter0;
        listMenuPresenter0.e = menuDialogHelper0;
        subMenuBuilder0.b(listMenuPresenter0, subMenuBuilder0.a);
        alertController$AlertParams0.n = menuDialogHelper0.c.a();
        alertController$AlertParams0.o = menuDialogHelper0;
        View view0 = subMenuBuilder0.o;
        if(view0 == null) {
            alertController$AlertParams0.c = subMenuBuilder0.n;
            alertController$AlertParams0.d = subMenuBuilder0.m;
        }
        else {
            alertController$AlertParams0.e = view0;
        }
        alertController$AlertParams0.l = menuDialogHelper0;
        AlertDialog alertDialog0 = alertDialog$Builder0.a();
        menuDialogHelper0.b = alertDialog0;
        alertDialog0.setOnDismissListener(menuDialogHelper0);
        WindowManager.LayoutParams windowManager$LayoutParams0 = menuDialogHelper0.b.getWindow().getAttributes();
        windowManager$LayoutParams0.type = 1003;
        windowManager$LayoutParams0.flags |= 0x20000;
        menuDialogHelper0.b.show();
        Callback menuPresenter$Callback0 = this.e;
        if(menuPresenter$Callback0 != null) {
            menuPresenter$Callback0.d(subMenuBuilder0);
        }
        return true;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final Parcelable k() {
        if(this.d == null) {
            return null;
        }
        Parcelable parcelable0 = new Bundle();
        SparseArray sparseArray0 = new SparseArray();
        ExpandedMenuView expandedMenuView0 = this.d;
        if(expandedMenuView0 != null) {
            expandedMenuView0.saveHierarchyState(sparseArray0);
        }
        ((Bundle)parcelable0).putSparseParcelableArray("android:menu:list", sparseArray0);
        return parcelable0;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean l(MenuItemImpl menuItemImpl0) {
        return false;
    }

    @Override  // android.widget.AdapterView$OnItemClickListener
    public final void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
        this.c.q(this.f.b(v), this, 0);
    }
}


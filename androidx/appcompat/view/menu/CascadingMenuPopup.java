package androidx.appcompat.view.menu;

import a.a;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Rect;
import android.os.Build.VERSION;
import android.os.Handler;
import android.os.Parcelable;
import android.os.SystemClock;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View.OnAttachStateChangeListener;
import android.view.View.OnKeyListener;
import android.view.View;
import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow.OnDismissListener;
import android.widget.TextView;
import androidx.appcompat.widget.DropDownListView;
import androidx.appcompat.widget.MenuItemHoverListener;
import androidx.appcompat.widget.MenuPopupWindow;
import java.util.ArrayList;

final class CascadingMenuPopup extends MenuPopup implements View.OnKeyListener, PopupWindow.OnDismissListener, MenuPresenter {
    static class CascadingMenuInfo {
        public final MenuPopupWindow a;
        public final MenuBuilder b;
        public final int c;

        public CascadingMenuInfo(MenuPopupWindow menuPopupWindow0, MenuBuilder menuBuilder0, int v) {
            this.a = menuPopupWindow0;
            this.b = menuBuilder0;
            this.c = v;
        }
    }

    public boolean A;
    public final Context b;
    public final int c;
    public final int d;
    public final int e;
    public final boolean f;
    public final Handler g;
    public final ArrayList h;
    public final ArrayList i;
    public final ViewTreeObserver.OnGlobalLayoutListener j;
    public final View.OnAttachStateChangeListener k;
    public final MenuItemHoverListener l;
    public int m;
    public int n;
    public View o;
    public View p;
    public int q;
    public boolean r;
    public boolean s;
    public int t;
    public int u;
    public boolean v;
    public boolean w;
    public Callback x;
    public ViewTreeObserver y;
    public PopupWindow.OnDismissListener z;

    public CascadingMenuPopup(Context context0, View view0, int v, int v1, boolean z) {
        this.h = new ArrayList();
        this.i = new ArrayList();
        this.j = new ViewTreeObserver.OnGlobalLayoutListener() {
            public final CascadingMenuPopup a;

            {
                this.a = cascadingMenuPopup0;
            }

            @Override  // android.view.ViewTreeObserver$OnGlobalLayoutListener
            public final void onGlobalLayout() {
                CascadingMenuPopup cascadingMenuPopup0 = this.a;
                if(cascadingMenuPopup0.a()) {
                    ArrayList arrayList0 = cascadingMenuPopup0.i;
                    if(arrayList0.size() > 0 && !((CascadingMenuInfo)arrayList0.get(0)).a.y) {
                        if(cascadingMenuPopup0.p != null && cascadingMenuPopup0.p.isShown()) {
                            for(Object object0: arrayList0) {
                                ((CascadingMenuInfo)object0).a.show();
                            }
                            return;
                        }
                        cascadingMenuPopup0.dismiss();
                    }
                }
            }
        };
        this.k = new View.OnAttachStateChangeListener() {
            public final CascadingMenuPopup a;

            {
                this.a = cascadingMenuPopup0;
            }

            @Override  // android.view.View$OnAttachStateChangeListener
            public final void onViewAttachedToWindow(View view0) {
            }

            @Override  // android.view.View$OnAttachStateChangeListener
            public final void onViewDetachedFromWindow(View view0) {
                CascadingMenuPopup cascadingMenuPopup0 = this.a;
                ViewTreeObserver viewTreeObserver0 = cascadingMenuPopup0.y;
                if(viewTreeObserver0 != null) {
                    if(!viewTreeObserver0.isAlive()) {
                        cascadingMenuPopup0.y = view0.getViewTreeObserver();
                    }
                    cascadingMenuPopup0.y.removeGlobalOnLayoutListener(cascadingMenuPopup0.j);
                }
                view0.removeOnAttachStateChangeListener(this);
            }
        };
        this.l = new MenuItemHoverListener() {
            public final CascadingMenuPopup a;

            {
                this.a = cascadingMenuPopup0;
            }

            @Override  // androidx.appcompat.widget.MenuItemHoverListener
            public final void c(MenuBuilder menuBuilder0, MenuItemImpl menuItemImpl0) {
                CascadingMenuPopup cascadingMenuPopup0 = this.a;
                CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo0 = null;
                cascadingMenuPopup0.g.removeCallbacksAndMessages(null);
                ArrayList arrayList0 = cascadingMenuPopup0.i;
                int v = arrayList0.size();
                int v1;
                for(v1 = 0; true; ++v1) {
                    if(v1 >= v) {
                        v1 = -1;
                        break;
                    }
                    if(menuBuilder0 == ((CascadingMenuInfo)arrayList0.get(v1)).b) {
                        break;
                    }
                }
                if(v1 == -1) {
                    return;
                }
                if(v1 + 1 < arrayList0.size()) {
                    cascadingMenuPopup$CascadingMenuInfo0 = (CascadingMenuInfo)arrayList0.get(v1 + 1);
                }
                CascadingMenuPopup.3.1 cascadingMenuPopup$3$10 = new CascadingMenuPopup.3.1(this, cascadingMenuPopup$CascadingMenuInfo0, menuItemImpl0, menuBuilder0);
                long v2 = SystemClock.uptimeMillis();
                cascadingMenuPopup0.g.postAtTime(cascadingMenuPopup$3$10, menuBuilder0, v2 + 200L);
            }

            @Override  // androidx.appcompat.widget.MenuItemHoverListener
            public final void f(MenuBuilder menuBuilder0, MenuItem menuItem0) {
                this.a.g.removeCallbacksAndMessages(menuBuilder0);
            }
        };
        int v2 = 0;
        this.m = 0;
        this.n = 0;
        this.b = context0;
        this.o = view0;
        this.d = v;
        this.e = v1;
        this.f = z;
        this.v = false;
        if(view0.getLayoutDirection() != 1) {
            v2 = 1;
        }
        this.q = v2;
        Resources resources0 = context0.getResources();
        this.c = Math.max(resources0.getDisplayMetrics().widthPixels / 2, resources0.getDimensionPixelSize(0x7F07030F));  // dimen:abc_config_prefDialogWidth
        this.g = new Handler();
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final boolean a() {
        return this.i.size() > 0 && ((CascadingMenuInfo)this.i.get(0)).a.z.isShowing();
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean b() {
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void c(MenuBuilder menuBuilder0, boolean z) {
        ArrayList arrayList0 = this.i;
        int v = arrayList0.size();
        int v1;
        for(v1 = 0; true; ++v1) {
            if(v1 >= v) {
                v1 = -1;
                break;
            }
            if(menuBuilder0 == ((CascadingMenuInfo)arrayList0.get(v1)).b) {
                break;
            }
        }
        if(v1 < 0) {
            return;
        }
        if(v1 + 1 < arrayList0.size()) {
            ((CascadingMenuInfo)arrayList0.get(v1 + 1)).b.c(false);
        }
        CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo0 = (CascadingMenuInfo)arrayList0.remove(v1);
        cascadingMenuPopup$CascadingMenuInfo0.b.r(this);
        MenuPopupWindow menuPopupWindow0 = cascadingMenuPopup$CascadingMenuInfo0.a;
        if(this.A) {
            menuPopupWindow0.s();
            menuPopupWindow0.z.setAnimationStyle(0);
        }
        menuPopupWindow0.dismiss();
        int v2 = arrayList0.size();
        if(v2 > 0) {
            this.q = ((CascadingMenuInfo)arrayList0.get(v2 - 1)).c;
        }
        else {
            this.q = this.o.getLayoutDirection() == 1 ? 0 : 1;
        }
        if(v2 == 0) {
            this.dismiss();
            Callback menuPresenter$Callback0 = this.x;
            if(menuPresenter$Callback0 != null) {
                menuPresenter$Callback0.c(menuBuilder0, true);
            }
            ViewTreeObserver viewTreeObserver0 = this.y;
            if(viewTreeObserver0 != null) {
                if(viewTreeObserver0.isAlive()) {
                    this.y.removeGlobalOnLayoutListener(this.j);
                }
                this.y = null;
            }
            this.p.removeOnAttachStateChangeListener(this.k);
            this.z.onDismiss();
            return;
        }
        if(z) {
            ((CascadingMenuInfo)arrayList0.get(0)).b.c(false);
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void d(boolean z) {
        for(Object object0: this.i) {
            ListAdapter listAdapter0 = ((CascadingMenuInfo)object0).a.c.getAdapter();
            (listAdapter0 instanceof HeaderViewListAdapter ? ((MenuAdapter)((HeaderViewListAdapter)listAdapter0).getWrappedAdapter()) : ((MenuAdapter)listAdapter0)).notifyDataSetChanged();
        }
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final void dismiss() {
        ArrayList arrayList0 = this.i;
        int v = arrayList0.size();
        if(v > 0) {
            CascadingMenuInfo[] arr_cascadingMenuPopup$CascadingMenuInfo = (CascadingMenuInfo[])arrayList0.toArray(new CascadingMenuInfo[v]);
            for(int v1 = v - 1; v1 >= 0; --v1) {
                CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo0 = arr_cascadingMenuPopup$CascadingMenuInfo[v1];
                if(cascadingMenuPopup$CascadingMenuInfo0.a.z.isShowing()) {
                    cascadingMenuPopup$CascadingMenuInfo0.a.dismiss();
                }
            }
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void e(Callback menuPresenter$Callback0) {
        this.x = menuPresenter$Callback0;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void h(Parcelable parcelable0) {
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final ListView i() {
        ArrayList arrayList0 = this.i;
        return arrayList0.isEmpty() ? null : ((CascadingMenuInfo)a.h(arrayList0, 1)).a.c;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean j(SubMenuBuilder subMenuBuilder0) {
        for(Object object0: this.i) {
            CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo0 = (CascadingMenuInfo)object0;
            if(subMenuBuilder0 == cascadingMenuPopup$CascadingMenuInfo0.b) {
                cascadingMenuPopup$CascadingMenuInfo0.a.c.requestFocus();
                return true;
            }
            if(false) {
                break;
            }
        }
        if(subMenuBuilder0.hasVisibleItems()) {
            this.m(subMenuBuilder0);
            Callback menuPresenter$Callback0 = this.x;
            if(menuPresenter$Callback0 != null) {
                menuPresenter$Callback0.d(subMenuBuilder0);
            }
            return true;
        }
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final Parcelable k() {
        return null;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void m(MenuBuilder menuBuilder0) {
        menuBuilder0.b(this, this.b);
        if(this.a()) {
            this.w(menuBuilder0);
            return;
        }
        this.h.add(menuBuilder0);
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void o(View view0) {
        if(this.o != view0) {
            this.o = view0;
            this.n = Gravity.getAbsoluteGravity(this.m, view0.getLayoutDirection());
        }
    }

    @Override  // android.widget.PopupWindow$OnDismissListener
    public final void onDismiss() {
        CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo0;
        ArrayList arrayList0 = this.i;
        int v = arrayList0.size();
        for(int v1 = 0; true; ++v1) {
            cascadingMenuPopup$CascadingMenuInfo0 = null;
            if(v1 >= v) {
                break;
            }
            CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo1 = (CascadingMenuInfo)arrayList0.get(v1);
            if(!cascadingMenuPopup$CascadingMenuInfo1.a.z.isShowing()) {
                cascadingMenuPopup$CascadingMenuInfo0 = cascadingMenuPopup$CascadingMenuInfo1;
                break;
            }
        }
        if(cascadingMenuPopup$CascadingMenuInfo0 != null) {
            cascadingMenuPopup$CascadingMenuInfo0.b.c(false);
        }
    }

    @Override  // android.view.View$OnKeyListener
    public final boolean onKey(View view0, int v, KeyEvent keyEvent0) {
        if(keyEvent0.getAction() == 1 && v == 82) {
            this.dismiss();
            return true;
        }
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void p(boolean z) {
        this.v = z;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void q(int v) {
        if(this.m != v) {
            this.m = v;
            this.n = Gravity.getAbsoluteGravity(v, this.o.getLayoutDirection());
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void r(int v) {
        this.r = true;
        this.t = v;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void s(PopupWindow.OnDismissListener popupWindow$OnDismissListener0) {
        this.z = popupWindow$OnDismissListener0;
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final void show() {
        if(this.a()) {
            return;
        }
        ArrayList arrayList0 = this.h;
        for(Object object0: arrayList0) {
            this.w(((MenuBuilder)object0));
        }
        arrayList0.clear();
        View view0 = this.o;
        this.p = view0;
        if(view0 != null) {
            boolean z = this.y == null;
            ViewTreeObserver viewTreeObserver0 = view0.getViewTreeObserver();
            this.y = viewTreeObserver0;
            if(z) {
                viewTreeObserver0.addOnGlobalLayoutListener(this.j);
            }
            this.p.addOnAttachStateChangeListener(this.k);
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void t(boolean z) {
        this.w = z;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void u(int v) {
        this.s = true;
        this.u = v;
    }

    public final void w(MenuBuilder menuBuilder0) {
        int v13;
        int v10;
        int v9;
        int v8;
        MenuAdapter menuAdapter1;
        int v3;
        View view0;
        CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo0;
        Context context0 = this.b;
        LayoutInflater layoutInflater0 = LayoutInflater.from(context0);
        MenuAdapter menuAdapter0 = new MenuAdapter(menuBuilder0, layoutInflater0, this.f, 0x7F0D000B);  // layout:abc_cascading_menu_item_layout
        if(!this.a() && this.v) {
            menuAdapter0.c = true;
        }
        else if(this.a()) {
            menuAdapter0.c = MenuPopup.v(menuBuilder0);
        }
        int v = MenuPopup.n(menuAdapter0, context0, this.c);
        MenuPopupWindow menuPopupWindow0 = new MenuPopupWindow(context0, null, this.d, this.e);  // 初始化器: Landroidx/appcompat/widget/ListPopupWindow;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V
        menuPopupWindow0.D = this.l;
        menuPopupWindow0.p = this;
        menuPopupWindow0.z.setOnDismissListener(this);
        menuPopupWindow0.o = this.o;
        menuPopupWindow0.l = this.n;
        menuPopupWindow0.y = true;
        menuPopupWindow0.z.setFocusable(true);
        menuPopupWindow0.z.setInputMethodMode(2);
        menuPopupWindow0.o(menuAdapter0);
        menuPopupWindow0.q(v);
        menuPopupWindow0.l = this.n;
        ArrayList arrayList0 = this.i;
        if(arrayList0.size() > 0) {
            cascadingMenuPopup$CascadingMenuInfo0 = (CascadingMenuInfo)a.h(arrayList0, 1);
            MenuBuilder menuBuilder1 = cascadingMenuPopup$CascadingMenuInfo0.b;
            int v1 = menuBuilder1.f.size();
            for(int v2 = 0; true; ++v2) {
                MenuItem menuItem0 = null;
                if(v2 >= v1) {
                    break;
                }
                MenuItem menuItem1 = menuBuilder1.getItem(v2);
                if(menuItem1.hasSubMenu() && menuBuilder0 == menuItem1.getSubMenu()) {
                    menuItem0 = menuItem1;
                    break;
                }
            }
            if(menuItem0 == null) {
                view0 = null;
            }
            else {
                DropDownListView dropDownListView0 = cascadingMenuPopup$CascadingMenuInfo0.a.c;
                ListAdapter listAdapter0 = dropDownListView0.getAdapter();
                if(listAdapter0 instanceof HeaderViewListAdapter) {
                    v3 = ((HeaderViewListAdapter)listAdapter0).getHeadersCount();
                    menuAdapter1 = (MenuAdapter)((HeaderViewListAdapter)listAdapter0).getWrappedAdapter();
                }
                else {
                    menuAdapter1 = (MenuAdapter)listAdapter0;
                    v3 = 0;
                }
                int v4 = menuAdapter1.getCount();
                int v5;
                for(v5 = 0; true; ++v5) {
                    if(v5 >= v4) {
                        v5 = -1;
                        break;
                    }
                    if(menuItem0 == menuAdapter1.b(v5)) {
                        break;
                    }
                }
                if(v5 == -1) {
                    view0 = null;
                }
                else {
                    int v6 = v5 + v3 - dropDownListView0.getFirstVisiblePosition();
                    view0 = v6 < 0 || v6 >= dropDownListView0.getChildCount() ? null : dropDownListView0.getChildAt(v6);
                }
            }
        }
        else {
            cascadingMenuPopup$CascadingMenuInfo0 = null;
            view0 = null;
        }
        if(view0 == null) {
            if(this.r) {
                menuPopupWindow0.f = this.t;
            }
            if(this.s) {
                menuPopupWindow0.k(this.u);
            }
            menuPopupWindow0.x = this.a == null ? null : new Rect(this.a);
        }
        else {
            menuPopupWindow0.t();
            menuPopupWindow0.r();
            DropDownListView dropDownListView1 = ((CascadingMenuInfo)arrayList0.get(arrayList0.size() - 1)).a.c;
            int[] arr_v = new int[2];
            dropDownListView1.getLocationOnScreen(arr_v);
            Rect rect0 = new Rect();
            this.p.getWindowVisibleDisplayFrame(rect0);
            if(this.q == 1) {
                int v7 = arr_v[0];
                v8 = dropDownListView1.getWidth() + v7 + v <= rect0.right ? 1 : 0;
            }
            else {
                v8 = arr_v[0] - v >= 0 ? 0 : 1;
            }
            this.q = v8;
            if(Build.VERSION.SDK_INT >= 26) {
                menuPopupWindow0.o = view0;
                v9 = 0;
                v10 = 0;
            }
            else {
                int[] arr_v1 = new int[2];
                this.o.getLocationOnScreen(arr_v1);
                int[] arr_v2 = new int[2];
                view0.getLocationOnScreen(arr_v2);
                if((this.n & 7) == 5) {
                    int v11 = arr_v1[0];
                    arr_v1[0] = this.o.getWidth() + v11;
                    int v12 = arr_v2[0];
                    arr_v2[0] = view0.getWidth() + v12;
                }
                v10 = arr_v2[0] - arr_v1[0];
                v9 = arr_v2[1] - arr_v1[1];
            }
            if((this.n & 5) != 5) {
                v13 = v8 == 1 ? v10 + view0.getWidth() : v10 - v;
            }
            else if(v8 == 1) {
                v13 = v10 + v;
            }
            else {
                v13 = v10 - view0.getWidth();
            }
            menuPopupWindow0.f = v13;
            menuPopupWindow0.k = true;
            menuPopupWindow0.j = true;
            menuPopupWindow0.k(v9);
        }
        arrayList0.add(new CascadingMenuInfo(menuPopupWindow0, menuBuilder0, this.q));
        menuPopupWindow0.show();
        DropDownListView dropDownListView2 = menuPopupWindow0.c;
        dropDownListView2.setOnKeyListener(this);
        if(cascadingMenuPopup$CascadingMenuInfo0 == null && this.w && menuBuilder0.m != null) {
            FrameLayout frameLayout0 = (FrameLayout)layoutInflater0.inflate(0x7F0D0012, dropDownListView2, false);  // layout:abc_popup_menu_header_item_layout
            TextView textView0 = (TextView)frameLayout0.findViewById(0x1020016);
            frameLayout0.setEnabled(false);
            textView0.setText(menuBuilder0.m);
            dropDownListView2.addHeaderView(frameLayout0, null, false);
            menuPopupWindow0.show();
        }
    }
}


package androidx.appcompat.view.menu;

import android.view.MenuItem;

class CascadingMenuPopup.3.1 implements Runnable {
    public final CascadingMenuInfo a;
    public final MenuItem b;
    public final MenuBuilder c;
    public final androidx.appcompat.view.menu.CascadingMenuPopup.3 d;

    public CascadingMenuPopup.3.1(androidx.appcompat.view.menu.CascadingMenuPopup.3 cascadingMenuPopup$30, CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo0, MenuItemImpl menuItemImpl0, MenuBuilder menuBuilder0) {
        this.d = cascadingMenuPopup$30;
        this.a = cascadingMenuPopup$CascadingMenuInfo0;
        this.b = menuItemImpl0;
        this.c = menuBuilder0;
    }

    @Override
    public final void run() {
        CascadingMenuInfo cascadingMenuPopup$CascadingMenuInfo0 = this.a;
        if(cascadingMenuPopup$CascadingMenuInfo0 != null) {
            this.d.a.A = true;
            cascadingMenuPopup$CascadingMenuInfo0.b.c(false);
            this.d.a.A = false;
        }
        MenuItem menuItem0 = this.b;
        if(menuItem0.isEnabled() && menuItem0.hasSubMenu()) {
            this.c.q(menuItem0, null, 4);
        }
    }
}


package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Rect;
import android.os.Parcelable;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View.OnAttachStateChangeListener;
import android.view.View.OnKeyListener;
import android.view.View;
import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.view.ViewTreeObserver;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.FrameLayout;
import android.widget.ListView;
import android.widget.PopupWindow.OnDismissListener;
import android.widget.TextView;
import androidx.appcompat.widget.DropDownListView;
import androidx.appcompat.widget.MenuPopupWindow;

final class StandardMenuPopup extends MenuPopup implements View.OnKeyListener, AdapterView.OnItemClickListener, PopupWindow.OnDismissListener, MenuPresenter {
    public final Context b;
    public final MenuBuilder c;
    public final MenuAdapter d;
    public final boolean e;
    public final int f;
    public final int g;
    public final int h;
    public final MenuPopupWindow i;
    public final ViewTreeObserver.OnGlobalLayoutListener j;
    public final View.OnAttachStateChangeListener k;
    public PopupWindow.OnDismissListener l;
    public View m;
    public View n;
    public Callback o;
    public ViewTreeObserver p;
    public boolean q;
    public boolean r;
    public int s;
    public int t;
    public boolean u;

    public StandardMenuPopup(int v, int v1, Context context0, View view0, MenuBuilder menuBuilder0, boolean z) {
        this.j = new ViewTreeObserver.OnGlobalLayoutListener() {
            public final StandardMenuPopup a;

            {
                this.a = standardMenuPopup0;
            }

            @Override  // android.view.ViewTreeObserver$OnGlobalLayoutListener
            public final void onGlobalLayout() {
                StandardMenuPopup standardMenuPopup0 = this.a;
                if(standardMenuPopup0.a() && !standardMenuPopup0.i.y) {
                    if(standardMenuPopup0.n != null && standardMenuPopup0.n.isShown()) {
                        standardMenuPopup0.i.show();
                        return;
                    }
                    standardMenuPopup0.dismiss();
                }
            }
        };
        this.k = new View.OnAttachStateChangeListener() {
            public final StandardMenuPopup a;

            {
                this.a = standardMenuPopup0;
            }

            @Override  // android.view.View$OnAttachStateChangeListener
            public final void onViewAttachedToWindow(View view0) {
            }

            @Override  // android.view.View$OnAttachStateChangeListener
            public final void onViewDetachedFromWindow(View view0) {
                StandardMenuPopup standardMenuPopup0 = this.a;
                ViewTreeObserver viewTreeObserver0 = standardMenuPopup0.p;
                if(viewTreeObserver0 != null) {
                    if(!viewTreeObserver0.isAlive()) {
                        standardMenuPopup0.p = view0.getViewTreeObserver();
                    }
                    standardMenuPopup0.p.removeGlobalOnLayoutListener(standardMenuPopup0.j);
                }
                view0.removeOnAttachStateChangeListener(this);
            }
        };
        this.t = 0;
        this.b = context0;
        this.c = menuBuilder0;
        this.e = z;
        this.d = new MenuAdapter(menuBuilder0, LayoutInflater.from(context0), z, 0x7F0D0013);  // layout:abc_popup_menu_item_layout
        this.g = v;
        this.h = v1;
        Resources resources0 = context0.getResources();
        this.f = Math.max(resources0.getDisplayMetrics().widthPixels / 2, resources0.getDimensionPixelSize(0x7F07030F));  // dimen:abc_config_prefDialogWidth
        this.m = view0;
        this.i = new MenuPopupWindow(context0, null, v, v1);  // 初始化器: Landroidx/appcompat/widget/ListPopupWindow;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V
        menuBuilder0.b(this, context0);
    }

    // 去混淆评级： 低(20)
    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final boolean a() {
        return !this.q && this.i.z.isShowing();
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean b() {
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void c(MenuBuilder menuBuilder0, boolean z) {
        if(menuBuilder0 != this.c) {
            return;
        }
        this.dismiss();
        Callback menuPresenter$Callback0 = this.o;
        if(menuPresenter$Callback0 != null) {
            menuPresenter$Callback0.c(menuBuilder0, z);
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void d(boolean z) {
        this.r = false;
        MenuAdapter menuAdapter0 = this.d;
        if(menuAdapter0 != null) {
            menuAdapter0.notifyDataSetChanged();
        }
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final void dismiss() {
        if(this.a()) {
            this.i.dismiss();
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void e(Callback menuPresenter$Callback0) {
        this.o = menuPresenter$Callback0;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void h(Parcelable parcelable0) {
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final ListView i() {
        return this.i.c;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean j(SubMenuBuilder subMenuBuilder0) {
        if(subMenuBuilder0.hasVisibleItems()) {
            MenuPopupHelper menuPopupHelper0 = new MenuPopupHelper(this.g, this.h, this.b, this.n, subMenuBuilder0, this.e);
            Callback menuPresenter$Callback0 = this.o;
            menuPopupHelper0.i = menuPresenter$Callback0;
            MenuPopup menuPopup0 = menuPopupHelper0.j;
            if(menuPopup0 != null) {
                menuPopup0.e(menuPresenter$Callback0);
            }
            menuPopupHelper0.d(MenuPopup.v(subMenuBuilder0));
            menuPopupHelper0.k = this.l;
            this.l = null;
            this.c.c(false);
            int v = this.i.f;
            int v1 = this.i.n();
            if((Gravity.getAbsoluteGravity(this.t, this.m.getLayoutDirection()) & 7) == 5) {
                v += this.m.getWidth();
            }
            boolean z = false;
            if(menuPopupHelper0.b()) {
                z = true;
            }
            else if(menuPopupHelper0.f != null) {
                z = true;
                menuPopupHelper0.e(v, v1, true, true);
            }
            if(z) {
                Callback menuPresenter$Callback1 = this.o;
                if(menuPresenter$Callback1 != null) {
                    menuPresenter$Callback1.d(subMenuBuilder0);
                }
                return true;
            }
        }
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final Parcelable k() {
        return null;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void m(MenuBuilder menuBuilder0) {
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void o(View view0) {
        this.m = view0;
    }

    @Override  // android.widget.PopupWindow$OnDismissListener
    public final void onDismiss() {
        this.q = true;
        this.c.c(true);
        ViewTreeObserver viewTreeObserver0 = this.p;
        if(viewTreeObserver0 != null) {
            if(!viewTreeObserver0.isAlive()) {
                this.p = this.n.getViewTreeObserver();
            }
            this.p.removeGlobalOnLayoutListener(this.j);
            this.p = null;
        }
        this.n.removeOnAttachStateChangeListener(this.k);
        PopupWindow.OnDismissListener popupWindow$OnDismissListener0 = this.l;
        if(popupWindow$OnDismissListener0 != null) {
            popupWindow$OnDismissListener0.onDismiss();
        }
    }

    @Override  // android.view.View$OnKeyListener
    public final boolean onKey(View view0, int v, KeyEvent keyEvent0) {
        if(keyEvent0.getAction() == 1 && v == 82) {
            this.dismiss();
            return true;
        }
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void p(boolean z) {
        this.d.c = z;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void q(int v) {
        this.t = v;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void r(int v) {
        this.i.f = v;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void s(PopupWindow.OnDismissListener popupWindow$OnDismissListener0) {
        this.l = popupWindow$OnDismissListener0;
    }

    @Override  // androidx.appcompat.view.menu.ShowableListMenu
    public final void show() {
        if(!this.a()) {
            if(!this.q) {
                View view0 = this.m;
                if(view0 != null) {
                    this.n = view0;
                    MenuPopupWindow menuPopupWindow0 = this.i;
                    menuPopupWindow0.z.setOnDismissListener(this);
                    menuPopupWindow0.p = this;
                    menuPopupWindow0.y = true;
                    menuPopupWindow0.z.setFocusable(true);
                    View view1 = this.n;
                    boolean z = this.p == null;
                    ViewTreeObserver viewTreeObserver0 = view1.getViewTreeObserver();
                    this.p = viewTreeObserver0;
                    if(z) {
                        viewTreeObserver0.addOnGlobalLayoutListener(this.j);
                    }
                    view1.addOnAttachStateChangeListener(this.k);
                    menuPopupWindow0.o = view1;
                    menuPopupWindow0.l = this.t;
                    Context context0 = this.b;
                    MenuAdapter menuAdapter0 = this.d;
                    if(!this.r) {
                        this.s = MenuPopup.n(menuAdapter0, context0, this.f);
                        this.r = true;
                    }
                    menuPopupWindow0.q(this.s);
                    menuPopupWindow0.z.setInputMethodMode(2);
                    menuPopupWindow0.x = this.a == null ? null : new Rect(this.a);
                    menuPopupWindow0.show();
                    DropDownListView dropDownListView0 = menuPopupWindow0.c;
                    dropDownListView0.setOnKeyListener(this);
                    if(this.u) {
                        MenuBuilder menuBuilder0 = this.c;
                        if(menuBuilder0.m != null) {
                            FrameLayout frameLayout0 = (FrameLayout)LayoutInflater.from(context0).inflate(0x7F0D0012, dropDownListView0, false);  // layout:abc_popup_menu_header_item_layout
                            TextView textView0 = (TextView)frameLayout0.findViewById(0x1020016);
                            if(textView0 != null) {
                                textView0.setText(menuBuilder0.m);
                            }
                            frameLayout0.setEnabled(false);
                            dropDownListView0.addHeaderView(frameLayout0, null, false);
                        }
                    }
                    menuPopupWindow0.o(menuAdapter0);
                    menuPopupWindow0.show();
                    return;
                }
            }
            throw new IllegalStateException("StandardMenuPopup cannot be used without an anchor");
        }
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void t(boolean z) {
        this.u = z;
    }

    @Override  // androidx.appcompat.view.menu.MenuPopup
    public final void u(int v) {
        this.i.k(v);
    }
}


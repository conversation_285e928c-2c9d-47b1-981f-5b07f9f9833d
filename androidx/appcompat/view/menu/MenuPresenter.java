package androidx.appcompat.view.menu;

import android.content.Context;
import android.os.Parcelable;

public interface MenuPresenter {
    public interface Callback {
        void c(MenuBuilder arg1, boolean arg2);

        boolean d(MenuBuilder arg1);
    }

    boolean b();

    void c(MenuBuilder arg1, boolean arg2);

    void d(boolean arg1);

    void e(Callback arg1);

    boolean f(MenuItemImpl arg1);

    void g(Context arg1, MenuBuilder arg2);

    int getId();

    void h(Parcelable arg1);

    boolean j(SubMenuBuilder arg1);

    Parcelable k();

    boolean l(MenuItemImpl arg1);
}


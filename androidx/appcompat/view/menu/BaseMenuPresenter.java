package androidx.appcompat.view.menu;

import android.content.Context;
import android.view.LayoutInflater;

public abstract class BaseMenuPresenter implements MenuPresenter {
    public Context a;
    public Context b;
    public MenuBuilder c;
    public LayoutInflater d;
    public Callback e;
    public int f;
    public int g;
    public MenuView h;
    public int i;

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void e(Callback menuPresenter$Callback0) {
        this.e = menuPresenter$Callback0;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean f(MenuItemImpl menuItemImpl0) {
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final int getId() {
        return this.i;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean l(MenuItemImpl menuItemImpl0) {
        return false;
    }
}


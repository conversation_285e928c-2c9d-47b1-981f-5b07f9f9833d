package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View.OnClickListener;
import android.view.View;
import android.widget.Button;
import androidx.appcompat.R.styleable;
import androidx.appcompat.widget.ActionMenuView.ActionMenuChildView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.ForwardingListener;
import androidx.appcompat.widget.TooltipCompat;

public class ActionMenuItemView extends AppCompatTextView implements View.OnClickListener, ItemView, ActionMenuChildView {
    class ActionMenuItemForwardingListener extends ForwardingListener {
        public final ActionMenuItemView j;

        @Override  // androidx.appcompat.widget.ForwardingListener
        public final ShowableListMenu b() {
            PopupCallback actionMenuItemView$PopupCallback0 = ActionMenuItemView.this.m;
            return actionMenuItemView$PopupCallback0 == null ? null : actionMenuItemView$PopupCallback0.a();
        }

        @Override  // androidx.appcompat.widget.ForwardingListener
        public final boolean c() {
            ItemInvoker menuBuilder$ItemInvoker0 = ActionMenuItemView.this.k;
            if(menuBuilder$ItemInvoker0 != null && menuBuilder$ItemInvoker0.a(ActionMenuItemView.this.h)) {
                ShowableListMenu showableListMenu0 = this.b();
                return showableListMenu0 != null && showableListMenu0.a();
            }
            return false;
        }
    }

    public static abstract class PopupCallback {
        public abstract ShowableListMenu a();
    }

    public MenuItemImpl h;
    public CharSequence i;
    public Drawable j;
    public ItemInvoker k;
    public ForwardingListener l;
    public PopupCallback m;
    public boolean n;
    public boolean o;
    public final int p;
    public int q;
    public final int r;

    public ActionMenuItemView(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0, 0);
        Resources resources0 = context0.getResources();
        this.n = this.n();
        TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.c, 0, 0);
        this.p = typedArray0.getDimensionPixelSize(0, 0);
        typedArray0.recycle();
        this.r = (int)(resources0.getDisplayMetrics().density * 32.0f + 0.5f);
        this.setOnClickListener(this);
        this.q = -1;
        this.setSaveEnabled(false);
    }

    @Override  // androidx.appcompat.widget.ActionMenuView$ActionMenuChildView
    public final boolean a() {
        return !TextUtils.isEmpty(this.getText());
    }

    @Override  // androidx.appcompat.widget.ActionMenuView$ActionMenuChildView
    public final boolean b() {
        return !TextUtils.isEmpty(this.getText()) != 0 && this.h.getIcon() == null;
    }

    @Override  // androidx.appcompat.view.menu.MenuView$ItemView
    public final void d(MenuItemImpl menuItemImpl0) {
        this.h = menuItemImpl0;
        this.setIcon(menuItemImpl0.getIcon());
        this.setTitle(menuItemImpl0.getTitleCondensed());
        this.setId(menuItemImpl0.a);
        this.setVisibility((menuItemImpl0.isVisible() ? 0 : 8));
        this.setEnabled(menuItemImpl0.isEnabled());
        if(menuItemImpl0.hasSubMenu() && this.l == null) {
            this.l = new ActionMenuItemForwardingListener(this);
        }
    }

    @Override  // android.widget.TextView
    public CharSequence getAccessibilityClassName() {
        return Button.class.getName();
    }

    @Override  // androidx.appcompat.view.menu.MenuView$ItemView
    public MenuItemImpl getItemData() {
        return this.h;
    }

    public final boolean n() {
        Configuration configuration0 = this.getContext().getResources().getConfiguration();
        return configuration0.screenWidthDp >= 480 || configuration0.screenWidthDp >= 640 && configuration0.screenHeightDp >= 480 || configuration0.orientation == 2;
    }

    public final void o() {
        int v = !TextUtils.isEmpty(this.i) & (this.j == null || (this.h.y & 4) == 4 && (this.n || this.o) ? 1 : 0);
        CharSequence charSequence0 = null;
        this.setText((v == 0 ? null : this.i));
        CharSequence charSequence1 = this.h.q;
        if(TextUtils.isEmpty(charSequence1)) {
            this.setContentDescription((v == 0 ? this.h.e : null));
        }
        else {
            this.setContentDescription(charSequence1);
        }
        CharSequence charSequence2 = this.h.r;
        if(TextUtils.isEmpty(charSequence2)) {
            if(v == 0) {
                charSequence0 = this.h.e;
            }
            TooltipCompat.a(this, charSequence0);
            return;
        }
        TooltipCompat.a(this, charSequence2);
    }

    @Override  // android.view.View$OnClickListener
    public final void onClick(View view0) {
        ItemInvoker menuBuilder$ItemInvoker0 = this.k;
        if(menuBuilder$ItemInvoker0 != null) {
            menuBuilder$ItemInvoker0.a(this.h);
        }
    }

    @Override  // android.widget.TextView
    public final void onConfigurationChanged(Configuration configuration0) {
        super.onConfigurationChanged(configuration0);
        this.n = this.n();
        this.o();
    }

    @Override  // androidx.appcompat.widget.AppCompatTextView
    public final void onMeasure(int v, int v1) {
        boolean z = TextUtils.isEmpty(this.getText());
        if(!z != 0) {
            int v2 = this.q;
            if(v2 >= 0) {
                super.setPadding(v2, this.getPaddingTop(), this.getPaddingRight(), this.getPaddingBottom());
            }
        }
        super.onMeasure(v, v1);
        int v3 = View.MeasureSpec.getMode(v);
        int v4 = View.MeasureSpec.getSize(v);
        int v5 = this.getMeasuredWidth();
        int v6 = v3 == 0x80000000 ? Math.min(v4, this.p) : this.p;
        if(v3 != 0x40000000 && this.p > 0 && v5 < v6) {
            super.onMeasure(View.MeasureSpec.makeMeasureSpec(v6, 0x40000000), v1);
        }
        if(!z == 0 && this.j != null) {
            super.setPadding((this.getMeasuredWidth() - this.j.getBounds().width()) / 2, this.getPaddingTop(), this.getPaddingRight(), this.getPaddingBottom());
        }
    }

    @Override  // android.widget.TextView
    public final void onRestoreInstanceState(Parcelable parcelable0) {
        super.onRestoreInstanceState(null);
    }

    // 去混淆评级： 低(20)
    @Override  // android.widget.TextView
    public final boolean onTouchEvent(MotionEvent motionEvent0) {
        return !this.h.hasSubMenu() || (this.l == null || !this.l.onTouch(this, motionEvent0)) ? super.onTouchEvent(motionEvent0) : true;
    }

    public void setCheckable(boolean z) {
    }

    public void setChecked(boolean z) {
    }

    public void setExpandedFormat(boolean z) {
        if(this.o != z) {
            this.o = z;
            MenuItemImpl menuItemImpl0 = this.h;
            if(menuItemImpl0 != null) {
                menuItemImpl0.n.k = true;
                menuItemImpl0.n.p(true);
            }
        }
    }

    public void setIcon(Drawable drawable0) {
        this.j = drawable0;
        if(drawable0 != null) {
            int v = drawable0.getIntrinsicWidth();
            int v1 = drawable0.getIntrinsicHeight();
            int v2 = this.r;
            if(v > v2) {
                v1 = (int)(((float)v1) * (((float)v2) / ((float)v)));
                v = v2;
            }
            if(v1 > v2) {
                v = (int)(((float)v) * (((float)v2) / ((float)v1)));
            }
            else {
                v2 = v1;
            }
            drawable0.setBounds(0, 0, v, v2);
        }
        this.setCompoundDrawables(drawable0, null, null, null);
        this.o();
    }

    public void setItemInvoker(ItemInvoker menuBuilder$ItemInvoker0) {
        this.k = menuBuilder$ItemInvoker0;
    }

    @Override  // android.widget.TextView
    public final void setPadding(int v, int v1, int v2, int v3) {
        this.q = v;
        super.setPadding(v, v1, v2, v3);
    }

    public void setPopupCallback(PopupCallback actionMenuItemView$PopupCallback0) {
        this.m = actionMenuItemView$PopupCallback0;
    }

    public void setTitle(CharSequence charSequence0) {
        this.i = charSequence0;
        this.o();
    }
}


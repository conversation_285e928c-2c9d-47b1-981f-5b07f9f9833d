package androidx.appcompat.view.menu;

import android.content.Context;
import android.view.MenuItem;
import androidx.collection.SimpleArrayMap;
import androidx.core.internal.view.SupportMenuItem;

abstract class BaseMenuWrapper {
    public final Context a;
    public SimpleArrayMap b;

    public BaseMenuWrapper(Context context0) {
        this.a = context0;
    }

    public final MenuItem b(MenuItem menuItem0) {
        if(menuItem0 instanceof SupportMenuItem) {
            if(this.b == null) {
                this.b = new SimpleArrayMap();
            }
            MenuItem menuItem1 = (MenuItem)this.b.getOrDefault(((SupportMenuItem)menuItem0), null);
            if(menuItem1 == null) {
                menuItem1 = new MenuItemWrapperICS(this.a, ((SupportMenuItem)menuItem0));
                this.b.put(((SupportMenuItem)menuItem0), menuItem1);
            }
            return menuItem1;
        }
        return menuItem0;
    }
}


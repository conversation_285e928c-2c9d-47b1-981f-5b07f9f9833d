package androidx.appcompat.view.menu;

import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow.OnDismissListener;

public class MenuPopupHelper {
    public final Context a;
    public final MenuBuilder b;
    public final boolean c;
    public final int d;
    public final int e;
    public View f;
    public int g;
    public boolean h;
    public Callback i;
    public MenuPopup j;
    public PopupWindow.OnDismissListener k;
    public final PopupWindow.OnDismissListener l;

    public MenuPopupHelper(int v, int v1, Context context0, View view0, MenuBuilder menuBuilder0, boolean z) {
        this.g = 0x800003;
        this.l = () -> {
            this.j = null;
            PopupWindow.OnDismissListener popupWindow$OnDismissListener0 = this.k;
            if(popupWindow$OnDismissListener0 != null) {
                popupWindow$OnDismissListener0.onDismiss();
            }
        };
        this.a = context0;
        this.b = menuBuilder0;
        this.f = view0;
        this.c = z;
        this.d = v;
        this.e = v1;
    }

    public final MenuPopup a() {
        if(this.j == null) {
            Display display0 = ((WindowManager)this.a.getSystemService("window")).getDefaultDisplay();
            Point point0 = new Point();
            display0.getRealSize(point0);
            CascadingMenuPopup cascadingMenuPopup0 = Math.min(point0.x, point0.y) >= this.a.getResources().getDimensionPixelSize(0x7F07030E) ? new CascadingMenuPopup(this.a, this.f, this.d, this.e, this.c) : new StandardMenuPopup(this.d, this.e, this.a, this.f, this.b, this.c);  // dimen:abc_cascading_menus_min_smallest_width
            cascadingMenuPopup0.m(this.b);
            cascadingMenuPopup0.s(this.l);
            cascadingMenuPopup0.o(this.f);
            cascadingMenuPopup0.e(this.i);
            cascadingMenuPopup0.p(this.h);
            cascadingMenuPopup0.q(this.g);
            this.j = cascadingMenuPopup0;
        }
        return this.j;
    }

    public final boolean b() {
        return this.j != null && this.j.a();
    }

    // 检测为 Lambda 实现
    public void c() [...]

    public final void d(boolean z) {
        this.h = z;
        MenuPopup menuPopup0 = this.j;
        if(menuPopup0 != null) {
            menuPopup0.p(z);
        }
    }

    public final void e(int v, int v1, boolean z, boolean z1) {
        MenuPopup menuPopup0 = this.a();
        menuPopup0.t(z1);
        if(z) {
            if((Gravity.getAbsoluteGravity(this.g, this.f.getLayoutDirection()) & 7) == 5) {
                v -= this.f.getWidth();
            }
            menuPopup0.r(v);
            menuPopup0.u(v1);
            int v2 = (int)(this.a.getResources().getDisplayMetrics().density * 48.0f / 2.0f);
            menuPopup0.a = new Rect(v - v2, v1 - v2, v + v2, v1 + v2);
        }
        menuPopup0.show();
    }

    class androidx.appcompat.view.menu.MenuPopupHelper.1 implements PopupWindow.OnDismissListener {
        public final MenuPopupHelper a;

        public androidx.appcompat.view.menu.MenuPopupHelper.1() {
            this.a = menuPopupHelper0;
        }

        @Override  // android.widget.PopupWindow$OnDismissListener
        public final void onDismiss() {
            this.a.c();
        }
    }

}


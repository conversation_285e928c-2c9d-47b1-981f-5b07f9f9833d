package androidx.appcompat.view.menu;

import android.content.Context;
import android.graphics.Rect;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.PopupWindow.OnDismissListener;

abstract class MenuPopup implements AdapterView.OnItemClickListener, MenuPresenter, ShowableListMenu {
    public Rect a;

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean f(MenuItemImpl menuItemImpl0) {
        return false;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final void g(Context context0, MenuBuilder menuBuilder0) {
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final int getId() {
        return 0;
    }

    @Override  // androidx.appcompat.view.menu.MenuPresenter
    public final boolean l(MenuItemImpl menuItemImpl0) {
        return false;
    }

    public abstract void m(MenuBuilder arg1);

    public static int n(MenuAdapter menuAdapter0, Context context0, int v) {
        int v2 = menuAdapter0.getCount();
        FrameLayout frameLayout0 = null;
        View view0 = null;
        int v3 = 0;
        int v4 = 0;
        for(int v1 = 0; v1 < v2; ++v1) {
            int v5 = menuAdapter0.getItemViewType(v1);
            if(v5 != v4) {
                view0 = null;
                v4 = v5;
            }
            if(frameLayout0 == null) {
                frameLayout0 = new FrameLayout(context0);
            }
            view0 = menuAdapter0.getView(v1, view0, frameLayout0);
            view0.measure(0, 0);
            int v6 = view0.getMeasuredWidth();
            if(v6 >= v) {
                return v;
            }
            if(v6 > v3) {
                v3 = v6;
            }
        }
        return v3;
    }

    public abstract void o(View arg1);

    @Override  // android.widget.AdapterView$OnItemClickListener
    public final void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
        ListAdapter listAdapter0 = (ListAdapter)adapterView0.getAdapter();
        (listAdapter0 instanceof HeaderViewListAdapter ? ((MenuAdapter)((HeaderViewListAdapter)listAdapter0).getWrappedAdapter()) : ((MenuAdapter)listAdapter0)).a.q(((MenuItem)listAdapter0.getItem(v)), this, (!(this instanceof CascadingMenuPopup) == 0 ? 4 : 0));
    }

    public abstract void p(boolean arg1);

    public abstract void q(int arg1);

    public abstract void r(int arg1);

    public abstract void s(PopupWindow.OnDismissListener arg1);

    public abstract void t(boolean arg1);

    public abstract void u(int arg1);

    public static boolean v(MenuBuilder menuBuilder0) {
        int v = menuBuilder0.f.size();
        for(int v1 = 0; v1 < v; ++v1) {
            MenuItem menuItem0 = menuBuilder0.getItem(v1);
            if(menuItem0.isVisible() && menuItem0.getIcon() != null) {
                return true;
            }
        }
        return false;
    }
}


package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;
import android.widget.ListView;
import androidx.appcompat.widget.TintTypedArray;

public final class ExpandedMenuView extends ListView implements AdapterView.OnItemClickListener, ItemInvoker, MenuView {
    public MenuBuilder a;
    public static final int[] b;

    static {
        ExpandedMenuView.b = new int[]{0x10100D4, 0x1010129};
    }

    public ExpandedMenuView(Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0);
        this.setOnItemClickListener(this);
        TintTypedArray tintTypedArray0 = TintTypedArray.e(context0, attributeSet0, ExpandedMenuView.b, 0x1010074, 0);
        TypedArray typedArray0 = tintTypedArray0.b;
        if(typedArray0.hasValue(0)) {
            this.setBackgroundDrawable(tintTypedArray0.b(0));
        }
        if(typedArray0.hasValue(1)) {
            this.setDivider(tintTypedArray0.b(1));
        }
        tintTypedArray0.f();
    }

    @Override  // androidx.appcompat.view.menu.MenuBuilder$ItemInvoker
    public final boolean a(MenuItemImpl menuItemImpl0) {
        return this.a.q(menuItemImpl0, null, 0);
    }

    @Override  // androidx.appcompat.view.menu.MenuView
    public final void b(MenuBuilder menuBuilder0) {
        this.a = menuBuilder0;
    }

    public int getWindowAnimations() {
        return 0;
    }

    @Override  // android.widget.ListView
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        this.setChildrenDrawingCacheEnabled(false);
    }

    @Override  // android.widget.AdapterView$OnItemClickListener
    public final void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
        this.a(((MenuItemImpl)this.getAdapter().getItem(v)));
    }
}


package androidx.appcompat.view;

import android.content.Context;
import android.view.ActionMode.Callback;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuItemWrapperICS;
import androidx.appcompat.view.menu.MenuWrapperICS;
import androidx.collection.SimpleArrayMap;
import androidx.core.internal.view.SupportMenuItem;
import java.util.ArrayList;

public class SupportActionModeWrapper extends ActionMode {
    public static class CallbackWrapper implements Callback {
        public final ActionMode.Callback a;
        public final Context b;
        public final ArrayList c;
        public final SimpleArrayMap d;

        public CallbackWrapper(Context context0, ActionMode.Callback actionMode$Callback0) {
            this.b = context0;
            this.a = actionMode$Callback0;
            this.c = new ArrayList();
            this.d = new SimpleArrayMap();
        }

        @Override  // androidx.appcompat.view.ActionMode$Callback
        public final void a(androidx.appcompat.view.ActionMode actionMode0) {
            SupportActionModeWrapper supportActionModeWrapper0 = this.e(actionMode0);
            this.a.onDestroyActionMode(supportActionModeWrapper0);
        }

        @Override  // androidx.appcompat.view.ActionMode$Callback
        public final boolean b(androidx.appcompat.view.ActionMode actionMode0, MenuBuilder menuBuilder0) {
            SupportActionModeWrapper supportActionModeWrapper0 = this.e(actionMode0);
            SimpleArrayMap simpleArrayMap0 = this.d;
            Menu menu0 = (Menu)simpleArrayMap0.getOrDefault(menuBuilder0, null);
            if(menu0 == null) {
                menu0 = new MenuWrapperICS(this.b, menuBuilder0);
                simpleArrayMap0.put(menuBuilder0, menu0);
            }
            return this.a.onCreateActionMode(supportActionModeWrapper0, menu0);
        }

        @Override  // androidx.appcompat.view.ActionMode$Callback
        public final boolean c(androidx.appcompat.view.ActionMode actionMode0, MenuItem menuItem0) {
            SupportActionModeWrapper supportActionModeWrapper0 = this.e(actionMode0);
            MenuItemWrapperICS menuItemWrapperICS0 = new MenuItemWrapperICS(this.b, ((SupportMenuItem)menuItem0));
            return this.a.onActionItemClicked(supportActionModeWrapper0, menuItemWrapperICS0);
        }

        @Override  // androidx.appcompat.view.ActionMode$Callback
        public final boolean d(androidx.appcompat.view.ActionMode actionMode0, MenuBuilder menuBuilder0) {
            SupportActionModeWrapper supportActionModeWrapper0 = this.e(actionMode0);
            SimpleArrayMap simpleArrayMap0 = this.d;
            Menu menu0 = (Menu)simpleArrayMap0.getOrDefault(menuBuilder0, null);
            if(menu0 == null) {
                menu0 = new MenuWrapperICS(this.b, menuBuilder0);
                simpleArrayMap0.put(menuBuilder0, menu0);
            }
            return this.a.onPrepareActionMode(supportActionModeWrapper0, menu0);
        }

        public final SupportActionModeWrapper e(androidx.appcompat.view.ActionMode actionMode0) {
            ArrayList arrayList0 = this.c;
            int v = arrayList0.size();
            for(int v1 = 0; v1 < v; ++v1) {
                SupportActionModeWrapper supportActionModeWrapper0 = (SupportActionModeWrapper)arrayList0.get(v1);
                if(supportActionModeWrapper0 != null && supportActionModeWrapper0.b == actionMode0) {
                    return supportActionModeWrapper0;
                }
            }
            SupportActionModeWrapper supportActionModeWrapper1 = new SupportActionModeWrapper(this.b, actionMode0);
            arrayList0.add(supportActionModeWrapper1);
            return supportActionModeWrapper1;
        }
    }

    public final Context a;
    public final androidx.appcompat.view.ActionMode b;

    public SupportActionModeWrapper(Context context0, androidx.appcompat.view.ActionMode actionMode0) {
        this.a = context0;
        this.b = actionMode0;
    }

    @Override  // android.view.ActionMode
    public final void finish() {
        this.b.c();
    }

    @Override  // android.view.ActionMode
    public final View getCustomView() {
        return this.b.d();
    }

    @Override  // android.view.ActionMode
    public final Menu getMenu() {
        MenuBuilder menuBuilder0 = this.b.e();
        return new MenuWrapperICS(this.a, menuBuilder0);
    }

    @Override  // android.view.ActionMode
    public final MenuInflater getMenuInflater() {
        return this.b.f();
    }

    @Override  // android.view.ActionMode
    public final CharSequence getSubtitle() {
        return this.b.g();
    }

    @Override  // android.view.ActionMode
    public final Object getTag() {
        return this.b.a;
    }

    @Override  // android.view.ActionMode
    public final CharSequence getTitle() {
        return this.b.h();
    }

    @Override  // android.view.ActionMode
    public final boolean getTitleOptionalHint() {
        return this.b.b;
    }

    @Override  // android.view.ActionMode
    public final void invalidate() {
        this.b.i();
    }

    @Override  // android.view.ActionMode
    public final boolean isTitleOptional() {
        return this.b.j();
    }

    @Override  // android.view.ActionMode
    public final void setCustomView(View view0) {
        this.b.k(view0);
    }

    @Override  // android.view.ActionMode
    public final void setSubtitle(int v) {
        this.b.l(v);
    }

    @Override  // android.view.ActionMode
    public final void setSubtitle(CharSequence charSequence0) {
        this.b.m(charSequence0);
    }

    @Override  // android.view.ActionMode
    public final void setTag(Object object0) {
        this.b.a = object0;
    }

    @Override  // android.view.ActionMode
    public final void setTitle(int v) {
        this.b.n(v);
    }

    @Override  // android.view.ActionMode
    public final void setTitle(CharSequence charSequence0) {
        this.b.o(charSequence0);
    }

    @Override  // android.view.ActionMode
    public final void setTitleOptionalHint(boolean z) {
        this.b.p(z);
    }
}


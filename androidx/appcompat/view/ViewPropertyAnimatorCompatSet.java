package androidx.appcompat.view;

import android.view.View;
import android.view.animation.Interpolator;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.core.view.ViewPropertyAnimatorListener;
import androidx.core.view.ViewPropertyAnimatorListenerAdapter;
import java.util.ArrayList;

public class ViewPropertyAnimatorCompatSet {
    public final ArrayList a;
    public long b;
    public Interpolator c;
    public ViewPropertyAnimatorListener d;
    public boolean e;
    public final ViewPropertyAnimatorListenerAdapter f;

    public ViewPropertyAnimatorCompatSet() {
        this.b = -1L;
        this.f = new ViewPropertyAnimatorListenerAdapter() {
            public boolean a;
            public int b;
            public final ViewPropertyAnimatorCompatSet c;

            {
                this.c = viewPropertyAnimatorCompatSet0;
                this.a = false;
                this.b = 0;
            }

            @Override  // androidx.core.view.ViewPropertyAnimatorListenerAdapter
            public final void b() {
                if(this.a) {
                    return;
                }
                this.a = true;
                ViewPropertyAnimatorListener viewPropertyAnimatorListener0 = this.c.d;
                if(viewPropertyAnimatorListener0 != null) {
                    viewPropertyAnimatorListener0.b();
                }
            }

            @Override  // androidx.core.view.ViewPropertyAnimatorListener
            public final void c() {
                int v = this.b + 1;
                this.b = v;
                ViewPropertyAnimatorCompatSet viewPropertyAnimatorCompatSet0 = this.c;
                if(v == viewPropertyAnimatorCompatSet0.a.size()) {
                    ViewPropertyAnimatorListener viewPropertyAnimatorListener0 = viewPropertyAnimatorCompatSet0.d;
                    if(viewPropertyAnimatorListener0 != null) {
                        viewPropertyAnimatorListener0.c();
                    }
                    this.b = 0;
                    this.a = false;
                    viewPropertyAnimatorCompatSet0.e = false;
                }
            }
        };
        this.a = new ArrayList();
    }

    public final void a() {
        if(!this.e) {
            return;
        }
        for(Object object0: this.a) {
            ((ViewPropertyAnimatorCompat)object0).b();
        }
        this.e = false;
    }

    public final void b() {
        if(this.e) {
            return;
        }
        for(Object object0: this.a) {
            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = (ViewPropertyAnimatorCompat)object0;
            long v = this.b;
            if(v >= 0L) {
                viewPropertyAnimatorCompat0.c(v);
            }
            Interpolator interpolator0 = this.c;
            if(interpolator0 != null) {
                View view0 = (View)viewPropertyAnimatorCompat0.a.get();
                if(view0 != null) {
                    view0.animate().setInterpolator(interpolator0);
                }
            }
            if(this.d != null) {
                viewPropertyAnimatorCompat0.d(this.f);
            }
            View view1 = (View)viewPropertyAnimatorCompat0.a.get();
            if(view1 != null) {
                view1.animate().start();
            }
        }
        this.e = true;
    }
}


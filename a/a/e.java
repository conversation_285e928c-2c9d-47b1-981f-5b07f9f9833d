package a.a;

import camp.jaxi.Provider;

public final class e implements Provider {
    public volatile Provider a;
    public volatile Object b;
    public static final Object c;

    static {
        e.c = new Object();
    }

    public static Provider a(Provider provider0) {
        if(!(provider0 instanceof e) && !(provider0 instanceof a)) {
            Provider provider1 = new e();  // 初始化器: Ljava/lang/Object;-><init>()V
            provider1.b = e.c;
            provider1.a = provider0;
            return provider1;
        }
        return provider0;
    }

    @Override  // camp.jaxi.Provider
    public final Object get() {
        Object object0 = this.b;
        if(object0 == e.c) {
            Provider provider0 = this.a;
            if(provider0 == null) {
                return this.b;
            }
            object0 = provider0.get();
            this.b = object0;
            this.a = null;
        }
        return object0;
    }
}


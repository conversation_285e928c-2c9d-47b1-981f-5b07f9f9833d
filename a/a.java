package a;

import android.content.SharedPreferences;
import androidx.datastore.preferences.protobuf.CodedOutputStream;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider.Factory;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.viewmodel.CreationExtras;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import kotlin.jvm.internal.Intrinsics;

public abstract class a {
    public static void A(SharedPreferences sharedPreferences0, String s, boolean z) {
        sharedPreferences0.edit().putBoolean(s, z).apply();
    }

    public static void B(Object object0) {
        throw new ClassCastException();
    }

    public static int C(int v, int v1, int v2, int v3) [...] // Inlined contents

    public static String D(String s, String s1, String s2) [...] // Inlined contents

    public static ViewModel a(Factory viewModelProvider$Factory0, Class class0, CreationExtras creationExtras0) {
        Intrinsics.f(class0, "modelClass");
        Intrinsics.f(creationExtras0, "extras");
        return viewModelProvider$Factory0.create(class0);
    }

    public static double b(double f, double f1, double f2, double f3) [...] // Inlined contents

    public static float c(float f, float f1, float f2, float f3) [...] // Inlined contents

    public static int d(int v, int v1, int v2, int v3) {
        return CodedOutputStream.v(v) + v1 + v2 + v3;
    }

    public static Factory e(Fragment fragment0, String s) {
        Factory viewModelProvider$Factory0 = fragment0.requireActivity().getDefaultViewModelProviderFactory();
        Intrinsics.e(viewModelProvider$Factory0, s);
        return viewModelProvider$Factory0;
    }

    public static ViewModelStore f(Fragment fragment0, String s) {
        ViewModelStore viewModelStore0 = fragment0.requireActivity().getViewModelStore();
        Intrinsics.e(viewModelStore0, s);
        return viewModelStore0;
    }

    public static CreationExtras g(Fragment fragment0, String s) {
        CreationExtras creationExtras0 = fragment0.requireActivity().getDefaultViewModelCreationExtras();
        Intrinsics.e(creationExtras0, s);
        return creationExtras0;
    }

    public static Object h(ArrayList arrayList0, int v) {
        return arrayList0.get(arrayList0.size() - v);
    }

    public static String i(RecyclerView recyclerView0, StringBuilder stringBuilder0) {
        stringBuilder0.append(recyclerView0.exceptionLabel());
        return stringBuilder0.toString();
    }

    public static String j(String s, int v) [...] // Inlined contents

    public static String k(String s, int v, String s1) [...] // Inlined contents

    public static String l(String s, int v, String s1, int v1) [...] // Inlined contents

    public static String m(String s, Fragment fragment0, String s1) [...] // Inlined contents

    public static String n(String s, Class class0, String s1) [...] // Inlined contents

    public static String o(String s, Object object0) [...] // Inlined contents

    public static String p(String s, String s1) [...] // Inlined contents

    public static String q(String s, String s1, String s2) [...] // Inlined contents

    public static String r(StringBuilder stringBuilder0, long v, String s) {
        stringBuilder0.append(v);
        stringBuilder0.append(s);
        return stringBuilder0.toString();
    }

    public static String s(StringBuilder stringBuilder0, String s, String s1) [...] // 潜在的解密器

    public static String t(StringBuilder stringBuilder0, String s, String s1, String s2) [...] // 潜在的解密器

    public static StringBuilder u(String s, int v, String s1) {
        StringBuilder stringBuilder0 = new StringBuilder(s);
        stringBuilder0.append(v);
        stringBuilder0.append(s1);
        return stringBuilder0;
    }

    public static StringBuilder v(String s, int v, String s1, int v1, String s2) {
        StringBuilder stringBuilder0 = new StringBuilder(s);
        stringBuilder0.append(v);
        stringBuilder0.append(s1);
        stringBuilder0.append(v1);
        stringBuilder0.append(s2);
        return stringBuilder0;
    }

    public static StringBuilder w(String s, long v, String s1) {
        StringBuilder stringBuilder0 = new StringBuilder(s);
        stringBuilder0.append(v);
        stringBuilder0.append(s1);
        return stringBuilder0;
    }

    public static StringBuilder x(String s, android.supportv1.v4.app.Fragment fragment0, String s1) {
        StringBuilder stringBuilder0 = new StringBuilder(s);
        stringBuilder0.append(fragment0);
        stringBuilder0.append(s1);
        return stringBuilder0;
    }

    public static StringBuilder y(String s, String s1, String s2) {
        StringBuilder stringBuilder0 = new StringBuilder(s);
        stringBuilder0.append(s1);
        stringBuilder0.append(s2);
        return stringBuilder0;
    }

    public static StringBuilder z(String s, String s1, String s2, String s3, String s4) {
        StringBuilder stringBuilder0 = new StringBuilder(s);
        stringBuilder0.append(s1);
        stringBuilder0.append(s2);
        stringBuilder0.append(s3);
        stringBuilder0.append(s4);
        return stringBuilder0;
    }
}


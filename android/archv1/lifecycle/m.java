package android.archv1.lifecycle;

import android.app.Activity;
import android.app.Fragment;
import android.os.Bundle;

public class m extends Fragment {
    public static final int a;

    public final void a(a c$a0) {
        Activity activity0 = this.getActivity();
        if(activity0 instanceof e) {
            c c0 = ((e)activity0).getLifecycle();
            if(c0 instanceof f) {
                ((f)c0).a(c$a0);
            }
        }
    }

    @Override  // android.app.Fragment
    public final void onActivityCreated(Bundle bundle0) {
        super.onActivityCreated(bundle0);
        this.a(a.b);
    }

    @Override  // android.app.Fragment
    public final void onDestroy() {
        super.onDestroy();
        this.a(a.c);
    }

    @Override  // android.app.Fragment
    public final void onPause() {
        super.onPause();
        this.a(a.d);
    }

    @Override  // android.app.Fragment
    public final void onResume() {
        super.onResume();
        this.a(a.e);
    }

    @Override  // android.app.Fragment
    public final void onStart() {
        super.onStart();
        this.a(a.f);
    }

    @Override  // android.app.Fragment
    public final void onStop() {
        super.onStop();
        this.a(a.g);
    }
}


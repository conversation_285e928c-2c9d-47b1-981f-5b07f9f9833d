package android.archv1.lifecycle;

public abstract class c {
    // 部分失败：枚举糖化
    // 枚举按原样呈现，而不是糖化为Java 5枚举。
    public static final class a extends Enum {
        public static final a[] a;
        public static final enum a b;
        public static final enum a c;
        public static final enum a d;
        public static final enum a e;
        public static final enum a f;
        public static final enum a g;

        static {
            a.b = new a("ON_CREATE", 0);  // 初始化器: Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V
            a.f = new a("ON_START", 1);  // 初始化器: Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V
            a.e = new a("ON_RESUME", 2);  // 初始化器: Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V
            a.d = new a("ON_PAUSE", 3);  // 初始化器: Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V
            a.g = new a("ON_STOP", 4);  // 初始化器: Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V
            a.c = new a("ON_DESTROY", 5);  // 初始化器: Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V
            a.a = new a[]{a.b, a.f, a.e, a.d, a.g, a.c, new a("ON_ANY", 6)};  // 初始化器: Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V
        }

        public static a valueOf(String s) {
            return (a)Enum.valueOf(a.class, s);
        }

        public static a[] values() {
            return (a[])a.a.clone();
        }
    }

    public static enum b {
        DESTROYED,
        INITIALIZED,
        CREATED,
        STARTED,
        RESUMED;

    }

}


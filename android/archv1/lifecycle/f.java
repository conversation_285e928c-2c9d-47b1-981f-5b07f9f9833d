package android.archv1.lifecycle;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map.Entry;

public class f extends c {
    static class a {
        public b a;
        public GenericLifecycleObserver b;

        public final void a(e e0, android.archv1.lifecycle.c.a c$a0) {
            b c$b0 = f.b(c$a0);
            this.a = c$b0.compareTo(this.a) >= 0 ? this.a : c$b0;
            this.b.a(e0, c$a0);
            this.a = c$b0;
        }
    }

    public final android.archv1.a.b.a a;
    public b b;
    public final WeakReference c;
    public int d;
    public boolean e;
    public boolean f;
    public final ArrayList g;

    public f(e e0) {
        android.archv1.a.b.a a0 = new android.archv1.a.b.a();  // 初始化器: Landroid/archv1/a/b/b;-><init>()V
        a0.e = new HashMap();
        this.a = a0;
        this.d = 0;
        this.e = false;
        this.f = false;
        this.g = new ArrayList();
        this.c = new WeakReference(e0);
        this.b = b.b;
    }

    public final void a(android.archv1.lifecycle.c.a c$a0) {
        this.c(f.b(c$a0));
    }

    public static b b(android.archv1.lifecycle.c.a c$a0) {
        switch(android.archv1.lifecycle.f.1.a[c$a0.ordinal()]) {
            case 1: 
            case 2: {
                return b.c;
            }
            case 3: 
            case 4: {
                return b.d;
            }
            case 5: {
                return b.e;
            }
            case 6: {
                return b.a;
            }
            default: {
                throw new IllegalArgumentException("Unexpected event value " + c$a0);
            }
        }
    }

    public final void c(b c$b0) {
        if(this.b == c$b0) {
            return;
        }
        this.b = c$b0;
        if(!this.e && this.d == 0) {
            this.e = true;
            this.e();
            this.e = false;
            return;
        }
        this.f = true;
    }

    public final b d(d d0) {
        Map.Entry map$Entry0 = this.a.h(d0);
        b c$b0 = null;
        b c$b1 = map$Entry0 == null ? null : ((a)map$Entry0.getValue()).a;
        ArrayList arrayList0 = this.g;
        if(!arrayList0.isEmpty()) {
            c$b0 = (b)a.a.h(arrayList0, 1);
        }
        b c$b2 = this.b;
        if(c$b1 == null || c$b1.compareTo(c$b2) >= 0) {
            c$b1 = c$b2;
        }
        return c$b0 != null && c$b0.compareTo(c$b1) < 0 ? c$b0 : c$b1;
    }

    public final void e() {
        android.archv1.a.b.b.d b$d0;
        android.archv1.lifecycle.c.a c$a0;
        boolean z;
        e e0 = (e)this.c.get();
        if(e0 == null) {
            return;
        }
    alab1:
        while(true) {
            while(true) {
                android.archv1.a.b.a a0 = this.a;
                if(a0.d == 0) {
                    z = true;
                }
                else {
                    b c$b0 = ((a)a0.a.getValue()).a;
                    b c$b1 = ((a)a0.b.getValue()).a;
                    if(c$b0 != c$b1 || this.b != c$b1) {
                        z = false;
                    }
                }
                this.f = false;
                if(z) {
                    break alab1;
                }
                if(this.b.compareTo(((a)a0.a.getValue()).a) < 0) {
                    Iterator iterator0 = a0.f();
                label_15:
                    while(iterator0.hasNext() && !this.f) {
                        Object object0 = iterator0.next();
                        Map.Entry map$Entry0 = (Map.Entry)object0;
                        a f$a0 = (a)map$Entry0.getValue();
                        while(f$a0.a.compareTo(this.b) > 0 && !this.f) {
                            Object object1 = map$Entry0.getKey();
                            if(a0.e.containsKey(object1)) {
                                b c$b2 = f$a0.a;
                                switch(android.archv1.lifecycle.f.1.b[c$b2.ordinal()]) {
                                    case 1: {
                                        throw new IllegalArgumentException();
                                    }
                                    case 2: {
                                        c$a0 = android.archv1.lifecycle.c.a.c;
                                        break;
                                    }
                                    case 3: {
                                        c$a0 = android.archv1.lifecycle.c.a.g;
                                        break;
                                    }
                                    case 4: {
                                        c$a0 = android.archv1.lifecycle.c.a.d;
                                        break;
                                    }
                                    case 5: {
                                        throw new IllegalArgumentException();
                                    }
                                    default: {
                                        throw new IllegalArgumentException("Unexpected state value " + c$b2);
                                    }
                                }
                                b c$b3 = f.b(c$a0);
                                this.g.add(c$b3);
                                f$a0.a(e0, c$a0);
                                this.g.remove(this.g.size() - 1);
                                continue;
                            }
                            continue label_15;
                        }
                    }
                }
                if(!this.f && a0.b != null && this.b.compareTo(((a)a0.b.getValue()).a) > 0) {
                    b$d0 = a0.g();
                label_42:
                    if(!b$d0.hasNext() || this.f) {
                        continue;
                    }
                    break;
                }
            }
            Object object2 = b$d0.next();
            Map.Entry map$Entry1 = (Map.Entry)object2;
            a f$a1 = (a)map$Entry1.getValue();
            while(true) {
                if(f$a1.a.compareTo(this.b) >= 0 || this.f) {
                    goto label_42;
                }
                Object object3 = map$Entry1.getKey();
                if(!a0.e.containsKey(object3)) {
                    goto label_42;
                }
                this.g.add(f$a1.a);
                f$a1.a(e0, f.f(f$a1.a));
                this.g.remove(this.g.size() - 1);
            }
        }
    }

    public static android.archv1.lifecycle.c.a f(b c$b0) {
        switch(android.archv1.lifecycle.f.1.b[c$b0.ordinal()]) {
            case 2: {
                return android.archv1.lifecycle.c.a.f;
            }
            case 3: {
                return android.archv1.lifecycle.c.a.e;
            }
            case 4: {
                throw new IllegalArgumentException();
            }
            case 1: 
            case 5: {
                return android.archv1.lifecycle.c.a.b;
            }
            default: {
                throw new IllegalArgumentException("Unexpected state value " + c$b0);
            }
        }
    }
}


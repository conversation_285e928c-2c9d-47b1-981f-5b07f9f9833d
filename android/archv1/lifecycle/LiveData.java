package android.archv1.lifecycle;

import android.archv1.a.b.b.d;
import android.os.Looper;
import java.util.Map.Entry;

public abstract class LiveData {
    class LifecycleBoundObserver extends b implements GenericLifecycleObserver {
        public final e e;
        public final LiveData f;

        public LifecycleBoundObserver(e e0, k k0) {
            super(k0);
            this.e = e0;
        }

        @Override  // android.archv1.lifecycle.GenericLifecycleObserver
        public final void a(e e0, a c$a0) {
            if(((f)this.e.getLifecycle()).b == android.archv1.lifecycle.c.b.a) {
                LiveData.this.removeObserver(this.a);
                return;
            }
            this.b(this.c());
        }

        @Override  // android.archv1.lifecycle.LiveData$b
        public final boolean c() {
            return ((f)this.e.getLifecycle()).b.compareTo(android.archv1.lifecycle.c.b.d) >= 0;
        }

        @Override  // android.archv1.lifecycle.LiveData$b
        public final boolean d(e e0) {
            return this.e == e0;
        }

        @Override  // android.archv1.lifecycle.LiveData$b
        public final void e() {
            ((f)this.e.getLifecycle()).a.e(this);
        }
    }

    class android.archv1.lifecycle.LiveData.a extends b {
        @Override  // android.archv1.lifecycle.LiveData$b
        public final boolean c() {
            return true;
        }
    }

    abstract class b {
        public final k a;
        public boolean b;
        public int c;
        public final LiveData d;

        public b(k k0) {
            this.c = -1;
            this.a = k0;
        }

        public final void b(boolean z) {
            if(z == this.b) {
                return;
            }
            this.b = z;
            LiveData liveData0 = this.d;
            int v = 1;
            boolean z1 = liveData0.mActiveCount == 0;
            int v1 = liveData0.mActiveCount;
            if(!this.b) {
                v = -1;
            }
            liveData0.mActiveCount = v1 + v;
            if(z1 && this.b) {
                liveData0.onActive();
            }
            if(liveData0.mActiveCount == 0 && !this.b) {
                liveData0.onInactive();
            }
            if(this.b) {
                liveData0.c(this);
            }
        }

        public abstract boolean c();

        public boolean d(e e0) {
            return false;
        }

        public void e() {
        }
    }

    private static final Object NOT_SET = null;
    static final int START_VERSION = -1;
    private int mActiveCount;
    private volatile Object mData;
    private final Object mDataLock;
    private boolean mDispatchInvalidated;
    private boolean mDispatchingValue;
    private android.archv1.a.b.b mObservers;
    private volatile Object mPendingData;
    private final Runnable mPostValueRunnable;
    private int mVersion;

    static {
        LiveData.NOT_SET = new Object();
    }

    public LiveData() {
        this.mDataLock = new Object();
        this.mObservers = new android.archv1.a.b.b();
        this.mActiveCount = 0;
        this.mData = LiveData.NOT_SET;
        this.mPendingData = LiveData.NOT_SET;
        this.mVersion = -1;
        this.mPostValueRunnable = new Runnable() {
            public final LiveData a;

            {
                this.a = liveData0;
            }

            @Override
            public final void run() {
                synchronized(this.a.mDataLock) {
                    this.a.mPendingData = LiveData.NOT_SET;
                }
                this.a.setValue(this.a.mPendingData);
            }
        };
    }

    public static void a(String s) {
        android.archv1.a.a.a.a().a.getClass();
        if(Looper.getMainLooper().getThread() != Thread.currentThread()) {
            throw new IllegalStateException(a.a.y("Cannot invoke ", s, " on a background thread").toString());
        }
    }

    public final void b(b liveData$b0) {
        if(!liveData$b0.b) {
            return;
        }
        if(!liveData$b0.c()) {
            liveData$b0.b(false);
            return;
        }
        int v = this.mVersion;
        if(liveData$b0.c >= v) {
            return;
        }
        liveData$b0.c = v;
        liveData$b0.a.onChanged(this.mData);
    }

    public final void c(b liveData$b0) {
        if(this.mDispatchingValue) {
            this.mDispatchInvalidated = true;
            return;
        }
        this.mDispatchingValue = true;
        do {
            this.mDispatchInvalidated = false;
            if(liveData$b0 == null) {
                d b$d0 = this.mObservers.g();
                while(b$d0.hasNext()) {
                    Object object0 = b$d0.next();
                    this.b(((b)((Map.Entry)object0).getValue()));
                    if(this.mDispatchInvalidated) {
                        break;
                    }
                }
            }
            else {
                this.b(liveData$b0);
                liveData$b0 = null;
            }
        }
        while(this.mDispatchInvalidated);
        this.mDispatchingValue = false;
    }

    public Object getValue() {
        return this.mData == LiveData.NOT_SET ? null : this.mData;
    }

    public int getVersion() {
        return this.mVersion;
    }

    public boolean hasActiveObservers() {
        return this.mActiveCount > 0;
    }

    public boolean hasObservers() {
        return this.mObservers.d > 0;
    }

    public void observe(e e0, k k0) {
        android.archv1.lifecycle.c.b c$b0 = ((f)e0.getLifecycle()).b;
        android.archv1.lifecycle.c.b c$b1 = android.archv1.lifecycle.c.b.a;
        if(c$b0 == c$b1) {
            return;
        }
        LifecycleBoundObserver liveData$LifecycleBoundObserver0 = new LifecycleBoundObserver(this, e0, k0);
        b liveData$b0 = (b)this.mObservers.d(k0, liveData$LifecycleBoundObserver0);
        if(liveData$b0 != null && !liveData$b0.d(e0)) {
            throw new IllegalArgumentException("Cannot add the same observer with different lifecycles");
        }
        if(liveData$b0 != null) {
            return;
        }
        f f0 = (f)e0.getLifecycle();
        if(f0.b != c$b1) {
            c$b1 = android.archv1.lifecycle.c.b.b;
        }
        android.archv1.lifecycle.f.a f$a0 = new android.archv1.lifecycle.f.a();  // 初始化器: Ljava/lang/Object;-><init>()V
        f$a0.b = liveData$LifecycleBoundObserver0;
        f$a0.a = c$b1;
        android.archv1.a.b.a a0 = f0.a;
        if(((android.archv1.lifecycle.f.a)a0.d(liveData$LifecycleBoundObserver0, f$a0)) == null) {
            e e1 = (e)f0.c.get();
            if(e1 != null) {
                boolean z = f0.d != 0 || f0.e;
                android.archv1.lifecycle.c.b c$b2 = f0.d(liveData$LifecycleBoundObserver0);
                ++f0.d;
                while(f$a0.a.compareTo(c$b2) < 0 && a0.e.containsKey(liveData$LifecycleBoundObserver0)) {
                    f0.g.add(f$a0.a);
                    f$a0.a(e1, f.f(f$a0.a));
                    f0.g.remove(f0.g.size() - 1);
                    c$b2 = f0.d(liveData$LifecycleBoundObserver0);
                }
                if(!z) {
                    f0.e();
                }
                --f0.d;
            }
        }
    }

    public void observeForever(k k0) {
        android.archv1.lifecycle.LiveData.a liveData$a0 = new android.archv1.lifecycle.LiveData.a(this, k0);  // 初始化器: Landroid/archv1/lifecycle/LiveData$b;-><init>(Landroid/archv1/lifecycle/LiveData;Landroid/archv1/lifecycle/k;)V
        b liveData$b0 = (b)this.mObservers.d(k0, liveData$a0);
        if(liveData$b0 != null && liveData$b0 instanceof LifecycleBoundObserver) {
            throw new IllegalArgumentException("Cannot add the same observer with different lifecycles");
        }
        if(liveData$b0 != null) {
            return;
        }
        liveData$a0.b(true);
    }

    public void onActive() {
    }

    public void onInactive() {
    }

    public void postValue(Object object0) {
        synchronized(this.mDataLock) {
            boolean z = this.mPendingData == LiveData.NOT_SET;
            this.mPendingData = object0;
        }
        if(!z) {
            return;
        }
        android.archv1.a.a.a.a().b(this.mPostValueRunnable);
    }

    public void removeObserver(k k0) {
        LiveData.a("removeObserver");
        b liveData$b0 = (b)this.mObservers.e(k0);
        if(liveData$b0 == null) {
            return;
        }
        liveData$b0.e();
        liveData$b0.b(false);
    }

    public void removeObservers(e e0) {
        LiveData.a("removeObservers");
        for(Object object0: this.mObservers) {
            Map.Entry map$Entry0 = (Map.Entry)object0;
            if(((b)map$Entry0.getValue()).d(e0)) {
                this.removeObserver(((k)map$Entry0.getKey()));
            }
        }
    }

    public void setValue(Object object0) {
        LiveData.a("setValue");
        ++this.mVersion;
        this.mData = object0;
        this.c(null);
    }
}


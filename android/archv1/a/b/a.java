package android.archv1.a.b;

import java.util.HashMap;
import java.util.Map.Entry;

public class a extends b {
    public HashMap e;

    @Override  // android.archv1.a.b.b
    public final c a(Object object0) {
        return (c)this.e.get(object0);
    }

    @Override  // android.archv1.a.b.b
    public final Object d(Object object0, Object object1) {
        c b$c0 = this.a(object0);
        if(b$c0 != null) {
            return b$c0.b;
        }
        HashMap hashMap0 = this.e;
        c b$c1 = new c(object0, object1);
        ++this.d;
        c b$c2 = this.b;
        if(b$c2 == null) {
            this.a = b$c1;
        }
        else {
            b$c2.c = b$c1;
            b$c1.d = b$c2;
        }
        this.b = b$c1;
        hashMap0.put(object0, b$c1);
        return null;
    }

    @Override  // android.archv1.a.b.b
    public final Object e(Object object0) {
        Object object1 = super.e(object0);
        this.e.remove(object0);
        return object1;
    }

    public final Map.Entry h(Object object0) {
        HashMap hashMap0 = this.e;
        return hashMap0.containsKey(object0) ? ((c)hashMap0.get(object0)).d : null;
    }
}


package android.archv1.a.b;

import java.util.Iterator;
import java.util.Map.Entry;
import java.util.WeakHashMap;

public class b implements Iterable {
    static class a extends e {
        @Override  // android.archv1.a.b.b$e
        public final c b(c b$c0) {
            return b$c0.c;
        }

        @Override  // android.archv1.a.b.b$e
        public final c c(c b$c0) {
            return b$c0.d;
        }
    }

    static class android.archv1.a.b.b.b extends e {
        @Override  // android.archv1.a.b.b$e
        public final c b(c b$c0) {
            return b$c0.d;
        }

        @Override  // android.archv1.a.b.b$e
        public final c c(c b$c0) {
            return b$c0.c;
        }
    }

    static class c implements Map.Entry {
        public final Object a;
        public final Object b;
        public c c;
        public c d;

        public c(Object object0, Object object1) {
            this.a = object0;
            this.b = object1;
        }

        @Override
        public final boolean equals(Object object0) {
            if(object0 == this) {
                return true;
            }
            return object0 instanceof c ? this.a.equals(((c)object0).a) && this.b.equals(((c)object0).b) : false;
        }

        @Override
        public final Object getKey() {
            return this.a;
        }

        @Override
        public final Object getValue() {
            return this.b;
        }

        @Override
        public final Object setValue(Object object0) {
            throw new UnsupportedOperationException("An entry modification is not supported");
        }

        @Override
        public final String toString() {
            return this.a + "=" + this.b;
        }
    }

    class d implements f, Iterator {
        public final b a;
        public c b;
        public boolean c;

        public d() {
            this.c = true;
        }

        @Override  // android.archv1.a.b.b$f
        public final void a(c b$c0) {
            c b$c1 = this.b;
            if(b$c0 == b$c1) {
                this.b = b$c1.d;
                this.c = b$c1.d == null;
            }
        }

        // 去混淆评级： 低(30)
        @Override
        public final boolean hasNext() {
            return this.c ? this.a.a != null : this.b != null && this.b.c != null;
        }

        @Override
        public final Object next() {
            c b$c0;
            if(this.c) {
                this.c = false;
                b$c0 = this.a.a;
            }
            else {
                b$c0 = this.b == null ? null : this.b.c;
            }
            this.b = b$c0;
            return b$c0;
        }
    }

    static abstract class e implements f, Iterator {
        public c a;
        public c b;

        public e(c b$c0, c b$c1) {
            this.a = b$c1;
            this.b = b$c0;
        }

        @Override  // android.archv1.a.b.b$f
        public final void a(c b$c0) {
            c b$c1 = null;
            if(this.a == b$c0 && b$c0 == this.b) {
                this.b = null;
                this.a = null;
            }
            c b$c2 = this.a;
            if(b$c2 == b$c0) {
                this.a = this.c(b$c2);
            }
            c b$c3 = this.b;
            if(b$c3 == b$c0) {
                if(b$c3 != this.a && this.a != null) {
                    b$c1 = this.b(b$c3);
                }
                this.b = b$c1;
            }
        }

        public abstract c b(c arg1);

        public abstract c c(c arg1);

        @Override
        public final boolean hasNext() {
            return this.b != null;
        }

        @Override
        public final Object next() {
            c b$c0 = this.b;
            this.b = b$c0 == this.a || this.a == null ? null : this.b(b$c0);
            return b$c0;
        }
    }

    interface f {
        void a(c arg1);
    }

    public c a;
    public c b;
    public final WeakHashMap c;
    public int d;

    public b() {
        this.c = new WeakHashMap();
        this.d = 0;
    }

    public c a(Object object0) {
        c b$c0;
        for(b$c0 = this.a; b$c0 != null && !b$c0.a.equals(object0); b$c0 = b$c0.c) {
        }
        return b$c0;
    }

    public Object d(Object object0, Object object1) {
        c b$c0 = this.a(object0);
        if(b$c0 != null) {
            return b$c0.b;
        }
        c b$c1 = new c(object0, object1);
        ++this.d;
        c b$c2 = this.b;
        if(b$c2 == null) {
            this.a = b$c1;
        }
        else {
            b$c2.c = b$c1;
            b$c1.d = b$c2;
        }
        this.b = b$c1;
        return null;
    }

    public Object e(Object object0) {
        c b$c0 = this.a(object0);
        if(b$c0 == null) {
            return null;
        }
        --this.d;
        WeakHashMap weakHashMap0 = this.c;
        if(!weakHashMap0.isEmpty()) {
            for(Object object1: weakHashMap0.keySet()) {
                ((f)object1).a(b$c0);
            }
        }
        c b$c1 = b$c0.d;
        c b$c2 = b$c0.c;
        if(b$c1 == null) {
            this.a = b$c2;
        }
        else {
            b$c1.c = b$c2;
        }
        c b$c3 = b$c0.c;
        if(b$c3 == null) {
            this.b = b$c1;
        }
        else {
            b$c3.d = b$c1;
        }
        b$c0.c = null;
        b$c0.d = null;
        return b$c0.b;
    }

    @Override
    public final boolean equals(Object object0) {
        if(object0 == this) {
            return true;
        }
        if(!(object0 instanceof b)) {
            return false;
        }
        if(this.d != ((b)object0).d) {
            return false;
        }
        Iterator iterator0 = this.iterator();
        Iterator iterator1 = ((b)object0).iterator();
        while(((e)iterator0).hasNext() && ((e)iterator1).hasNext()) {
            Map.Entry map$Entry0 = (Map.Entry)((e)iterator0).next();
            Object object1 = ((e)iterator1).next();
            if(map$Entry0 == null && object1 != null || map$Entry0 != null && !map$Entry0.equals(object1)) {
                return false;
            }
            if(false) {
                break;
            }
        }
        return !((e)iterator0).hasNext() && !((e)iterator1).hasNext();
    }

    public final Iterator f() {
        Iterator iterator0 = new android.archv1.a.b.b.b(this.b, this.a);  // 初始化器: Landroid/archv1/a/b/b$e;-><init>(Landroid/archv1/a/b/b$c;Landroid/archv1/a/b/b$c;)V
        this.c.put(iterator0, Boolean.FALSE);
        return iterator0;
    }

    public final d g() {
        d b$d0 = new d(this);
        this.c.put(b$d0, Boolean.FALSE);
        return b$d0;
    }

    @Override
    public final Iterator iterator() {
        Iterator iterator0 = new a(this.a, this.b);  // 初始化器: Landroid/archv1/a/b/b$e;-><init>(Landroid/archv1/a/b/b$c;Landroid/archv1/a/b/b$c;)V
        this.c.put(iterator0, Boolean.FALSE);
        return iterator0;
    }

    @Override
    public final String toString() {
        StringBuilder stringBuilder0 = new StringBuilder("[");
        Iterator iterator0 = this.iterator();
        while(((e)iterator0).hasNext()) {
            stringBuilder0.append(((Map.Entry)((e)iterator0).next()).toString());
            if(((e)iterator0).hasNext()) {
                stringBuilder0.append(", ");
            }
        }
        stringBuilder0.append("]");
        return stringBuilder0.toString();
    }
}


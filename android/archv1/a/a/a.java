package android.archv1.a.a;

import android.os.Handler;
import android.os.Looper;

public class a extends c {
    public b a;
    public static volatile a b;

    public static a a() {
        if(a.b != null) {
            return a.b;
        }
        synchronized(a.class) {
            if(a.b == null) {
                a a0 = new a();  // 初始化器: Ljava/lang/Object;-><init>()V
                a0.a = new b();
                a.b = a0;
            }
            return a.b;
        }
    }

    public final void b(Runnable runnable0) {
        b b0 = this.a;
        if(b0.c == null) {
            synchronized(b0.a) {
                if(b0.c == null) {
                    b0.c = new Handler(Looper.getMainLooper());
                }
            }
        }
        b0.c.post(runnable0);
    }
}


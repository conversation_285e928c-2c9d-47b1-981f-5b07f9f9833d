package android.support.v4.os;

import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.Parcelable.Creator;

public interface IResultReceiver extends IInterface {
    public static abstract class Stub extends Binder implements IResultReceiver {
        static class Proxy implements IResultReceiver {
            public IBinder a;

            @Override  // android.os.IInterface
            public final IBinder asBinder() {
                return this.a;
            }
        }

        public static final int a;

        @Override  // android.os.IInterface
        public final IBinder asBinder() {
            return this;
        }

        @Override  // android.os.Binder
        public final boolean onTransact(int v, Parcel parcel0, Parcel parcel1, int v1) {
            if(v >= 1 && v <= 0xFFFFFF) {
                parcel0.enforceInterface("android.support.v4.os.IResultReceiver");
            }
            switch(v) {
                case 1: {
                    int v2 = parcel0.readInt();
                    Parcelable.Creator parcelable$Creator0 = Bundle.CREATOR;
                    Object object0 = parcel0.readInt() == 0 ? null : parcelable$Creator0.createFromParcel(parcel0);
                    ResultReceiver.this.getClass();
                    ResultReceiver.this.a(v2, ((Bundle)object0));
                    return true;
                }
                case 0x5F4E5446: {
                    parcel1.writeString("android.support.v4.os.IResultReceiver");
                    return true;
                }
                default: {
                    return super.onTransact(v, parcel0, parcel1, v1);
                }
            }
        }
    }

    public static final String a0;

    static {
        IResultReceiver.a0 = "android.support.v4.os.IResultReceiver";
    }
}


package android.support.v4.os;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;

@SuppressLint({"BanParcelableUsage"})
public class ResultReceiver implements Parcelable {
    class MyResultReceiver extends Stub {
        public final ResultReceiver b;

        public MyResultReceiver() {
            this.attachInterface(this, "android.support.v4.os.IResultReceiver");
        }
    }

    public static final Parcelable.Creator CREATOR;
    public IResultReceiver a;

    static {
        ResultReceiver.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
            @Override  // android.os.Parcelable$Creator
            public final Object createFromParcel(Parcel parcel0) {
                return new ResultReceiver(parcel0);
            }

            @Override  // android.os.Parcelable$Creator
            public final Object[] newArray(int v) {
                return new ResultReceiver[v];
            }
        };
    }

    public ResultReceiver(Parcel parcel0) {
        IResultReceiver iResultReceiver0;
        IBinder iBinder0 = parcel0.readStrongBinder();
        if(iBinder0 == null) {
            iResultReceiver0 = null;
        }
        else {
            IInterface iInterface0 = iBinder0.queryLocalInterface("android.support.v4.os.IResultReceiver");
            if(iInterface0 == null || !(iInterface0 instanceof IResultReceiver)) {
                Proxy iResultReceiver$Stub$Proxy0 = new Proxy();  // 初始化器: Ljava/lang/Object;-><init>()V
                iResultReceiver$Stub$Proxy0.a = iBinder0;
                iResultReceiver0 = iResultReceiver$Stub$Proxy0;
            }
            else {
                iResultReceiver0 = (IResultReceiver)iInterface0;
            }
        }
        this.a = iResultReceiver0;
    }

    public void a(int v, Bundle bundle0) {
    }

    @Override  // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override  // android.os.Parcelable
    public final void writeToParcel(Parcel parcel0, int v) {
        synchronized(this) {
            if(this.a == null) {
                this.a = new MyResultReceiver(this);
            }
            parcel0.writeStrongBinder(this.a.asBinder());
        }
    }
}


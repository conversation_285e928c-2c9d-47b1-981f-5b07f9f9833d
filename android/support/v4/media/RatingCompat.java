package android.support.v4.media;

import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;

public final class RatingCompat implements Parcelable {
    public static final Parcelable.Creator CREATOR;
    public final int a;
    public final float b;

    static {
        RatingCompat.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
            @Override  // android.os.Parcelable$Creator
            public final Object createFromParcel(Parcel parcel0) {
                return new RatingCompat(parcel0.readInt(), parcel0.readFloat());
            }

            @Override  // android.os.Parcelable$Creator
            public final Object[] newArray(int v) {
                return new RatingCompat[v];
            }
        };
    }

    public RatingCompat(int v, float f) {
        this.a = v;
        this.b = f;
    }

    @Override  // android.os.Parcelable
    public final int describeContents() {
        return this.a;
    }

    @Override
    public final String toString() {
        return "Rating:style=" + this.a + " rating=" + (this.b < 0.0f ? "unrated" : String.valueOf(this.b));
    }

    @Override  // android.os.Parcelable
    public final void writeToParcel(Parcel parcel0, int v) {
        parcel0.writeInt(this.a);
        parcel0.writeFloat(this.b);
    }
}


package android.support.v4.media;

import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;

public class MediaBrowserCompat.MediaItem implements Parcelable {
    public static final Parcelable.Creator CREATOR;
    public final int a;
    public final MediaDescriptionCompat b;

    static {
        MediaBrowserCompat.MediaItem.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
            @Override  // android.os.Parcelable$Creator
            public final Object createFromParcel(Parcel parcel0) {
                return new MediaBrowserCompat.MediaItem(parcel0);
            }

            @Override  // android.os.Parcelable$Creator
            public final Object[] newArray(int v) {
                return new MediaBrowserCompat.MediaItem[v];
            }
        };
    }

    public MediaBrowserCompat.MediaItem(Parcel parcel0) {
        this.a = parcel0.readInt();
        this.b = (MediaDescriptionCompat)MediaDescriptionCompat.CREATOR.createFromParcel(parcel0);
    }

    @Override  // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override
    public final String toString() {
        return "MediaItem{mFlags=" + this.a + ", mDescription=" + this.b + '}';
    }

    @Override  // android.os.Parcelable
    public final void writeToParcel(Parcel parcel0, int v) {
        parcel0.writeInt(this.a);
        this.b.writeToParcel(parcel0, v);
    }
}


package android.support.v4.media.session;

import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;

public class ParcelableVolumeInfo implements Parcelable {
    public static final Parcelable.Creator CREATOR;
    public int a;
    public int b;
    public int c;
    public int d;
    public int e;

    static {
        ParcelableVolumeInfo.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
            @Override  // android.os.Parcelable$Creator
            public final Object createFromParcel(Parcel parcel0) {
                ParcelableVolumeInfo parcelableVolumeInfo0 = new ParcelableVolumeInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
                parcelableVolumeInfo0.a = parcel0.readInt();
                parcelableVolumeInfo0.c = parcel0.readInt();
                parcelableVolumeInfo0.d = parcel0.readInt();
                parcelableVolumeInfo0.e = parcel0.readInt();
                parcelableVolumeInfo0.b = parcel0.readInt();
                return parcelableVolumeInfo0;
            }

            @Override  // android.os.Parcelable$Creator
            public final Object[] newArray(int v) {
                return new ParcelableVolumeInfo[v];
            }
        };
    }

    @Override  // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override  // android.os.Parcelable
    public final void writeToParcel(Parcel parcel0, int v) {
        parcel0.writeInt(this.a);
        parcel0.writeInt(this.c);
        parcel0.writeInt(this.d);
        parcel0.writeInt(this.e);
        parcel0.writeInt(this.b);
    }
}


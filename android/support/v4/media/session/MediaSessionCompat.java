package android.support.v4.media.session;

import a.a;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.os.ResultReceiver;
import android.support.v4.media.MediaDescriptionCompat;

public abstract class MediaSessionCompat {
    public static final class QueueItem implements Parcelable {
        public static final Parcelable.Creator CREATOR;
        public final MediaDescriptionCompat a;
        public final long b;

        static {
            QueueItem.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
                @Override  // android.os.Parcelable$Creator
                public final Object createFromParcel(Parcel parcel0) {
                    return new QueueItem(parcel0);
                }

                @Override  // android.os.Parcelable$Creator
                public final Object[] newArray(int v) {
                    return new QueueItem[v];
                }
            };
        }

        public QueueItem(Parcel parcel0) {
            this.a = (MediaDescriptionCompat)MediaDescriptionCompat.CREATOR.createFromParcel(parcel0);
            this.b = parcel0.readLong();
        }

        @Override  // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override
        public final String toString() {
            StringBuilder stringBuilder0 = new StringBuilder("MediaSession.QueueItem {Description=");
            stringBuilder0.append(this.a);
            stringBuilder0.append(", Id=");
            return a.r(stringBuilder0, this.b, " }");
        }

        @Override  // android.os.Parcelable
        public final void writeToParcel(Parcel parcel0, int v) {
            this.a.writeToParcel(parcel0, v);
            parcel0.writeLong(this.b);
        }
    }

    public static final class ResultReceiverWrapper implements Parcelable {
        public static final Parcelable.Creator CREATOR;
        public ResultReceiver a;

        static {
            ResultReceiverWrapper.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
                @Override  // android.os.Parcelable$Creator
                public final Object createFromParcel(Parcel parcel0) {
                    ResultReceiverWrapper mediaSessionCompat$ResultReceiverWrapper0 = new ResultReceiverWrapper();  // 初始化器: Ljava/lang/Object;-><init>()V
                    mediaSessionCompat$ResultReceiverWrapper0.a = (ResultReceiver)ResultReceiver.CREATOR.createFromParcel(parcel0);
                    return mediaSessionCompat$ResultReceiverWrapper0;
                }

                @Override  // android.os.Parcelable$Creator
                public final Object[] newArray(int v) {
                    return new ResultReceiverWrapper[v];
                }
            };
        }

        @Override  // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override  // android.os.Parcelable
        public final void writeToParcel(Parcel parcel0, int v) {
            this.a.writeToParcel(parcel0, v);
        }
    }

    public static final class Token implements Parcelable {
        public static final Parcelable.Creator CREATOR;
        public final Object a;

        static {
            Token.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
                @Override  // android.os.Parcelable$Creator
                public final Object createFromParcel(Parcel parcel0) {
                    return new Token(parcel0.readParcelable(null));
                }

                @Override  // android.os.Parcelable$Creator
                public final Object[] newArray(int v) {
                    return new Token[v];
                }
            };
        }

        public Token(Parcelable parcelable0) {
            this.a = parcelable0;
        }

        @Override  // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override
        public final boolean equals(Object object0) {
            if(this == object0) {
                return true;
            }
            if(!(object0 instanceof Token)) {
                return false;
            }
            Object object1 = this.a;
            if(object1 == null) {
                return ((Token)object0).a == null;
            }
            Object object2 = ((Token)object0).a;
            return object2 == null ? false : object1.equals(object2);
        }

        @Override
        public final int hashCode() {
            return this.a == null ? 0 : this.a.hashCode();
        }

        @Override  // android.os.Parcelable
        public final void writeToParcel(Parcel parcel0, int v) {
            parcel0.writeParcelable(((Parcelable)this.a), v);
        }
    }

}


package android.support.v4.media.session;

import a.a;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.text.TextUtils;
import java.util.ArrayList;

public final class PlaybackStateCompat implements Parcelable {
    public static final class CustomAction implements Parcelable {
        public static final Parcelable.Creator CREATOR;
        public final String a;
        public final CharSequence b;
        public final int c;
        public final Bundle d;

        static {
            CustomAction.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
                @Override  // android.os.Parcelable$Creator
                public final Object createFromParcel(Parcel parcel0) {
                    return new CustomAction(parcel0);
                }

                @Override  // android.os.Parcelable$Creator
                public final Object[] newArray(int v) {
                    return new CustomAction[v];
                }
            };
        }

        public CustomAction(Parcel parcel0) {
            this.a = parcel0.readString();
            this.b = (CharSequence)TextUtils.CHAR_SEQUENCE_CREATOR.createFromParcel(parcel0);
            this.c = parcel0.readInt();
            this.d = parcel0.readBundle(MediaSessionCompat.class.getClassLoader());
        }

        @Override  // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override
        public final String toString() {
            return "Action:mName=\'" + this.b + ", mIcon=" + this.c + ", mExtras=" + this.d;
        }

        @Override  // android.os.Parcelable
        public final void writeToParcel(Parcel parcel0, int v) {
            parcel0.writeString(this.a);
            TextUtils.writeToParcel(this.b, parcel0, v);
            parcel0.writeInt(this.c);
            parcel0.writeBundle(this.d);
        }
    }

    public static final Parcelable.Creator CREATOR;
    public final int a;
    public final long b;
    public final long c;
    public final float d;
    public final long e;
    public final int f;
    public final CharSequence g;
    public final long h;
    public final ArrayList i;
    public final long j;
    public final Bundle k;

    static {
        PlaybackStateCompat.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
            @Override  // android.os.Parcelable$Creator
            public final Object createFromParcel(Parcel parcel0) {
                return new PlaybackStateCompat(parcel0);
            }

            @Override  // android.os.Parcelable$Creator
            public final Object[] newArray(int v) {
                return new PlaybackStateCompat[v];
            }
        };
    }

    public PlaybackStateCompat(Parcel parcel0) {
        this.a = parcel0.readInt();
        this.b = parcel0.readLong();
        this.d = parcel0.readFloat();
        this.h = parcel0.readLong();
        this.c = parcel0.readLong();
        this.e = parcel0.readLong();
        this.g = (CharSequence)TextUtils.CHAR_SEQUENCE_CREATOR.createFromParcel(parcel0);
        this.i = parcel0.createTypedArrayList(CustomAction.CREATOR);
        this.j = parcel0.readLong();
        this.k = parcel0.readBundle(MediaSessionCompat.class.getClassLoader());
        this.f = parcel0.readInt();
    }

    @Override  // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override
    public final String toString() {
        StringBuilder stringBuilder0 = new StringBuilder("PlaybackState {state=");
        stringBuilder0.append(this.a);
        stringBuilder0.append(", position=");
        stringBuilder0.append(this.b);
        stringBuilder0.append(", buffered position=");
        stringBuilder0.append(this.c);
        stringBuilder0.append(", speed=");
        stringBuilder0.append(this.d);
        stringBuilder0.append(", updated=");
        stringBuilder0.append(this.h);
        stringBuilder0.append(", actions=");
        stringBuilder0.append(this.e);
        stringBuilder0.append(", error code=");
        stringBuilder0.append(this.f);
        stringBuilder0.append(", error message=");
        stringBuilder0.append(this.g);
        stringBuilder0.append(", custom actions=");
        stringBuilder0.append(this.i);
        stringBuilder0.append(", active item id=");
        return a.r(stringBuilder0, this.j, "}");
    }

    @Override  // android.os.Parcelable
    public final void writeToParcel(Parcel parcel0, int v) {
        parcel0.writeInt(this.a);
        parcel0.writeLong(this.b);
        parcel0.writeFloat(this.d);
        parcel0.writeLong(this.h);
        parcel0.writeLong(this.c);
        parcel0.writeLong(this.e);
        TextUtils.writeToParcel(this.g, parcel0, v);
        parcel0.writeTypedList(this.i);
        parcel0.writeLong(this.j);
        parcel0.writeBundle(this.k);
        parcel0.writeInt(this.f);
    }
}


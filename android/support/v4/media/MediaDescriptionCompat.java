package android.support.v4.media;

import android.graphics.Bitmap;
import android.media.MediaDescription.Builder;
import android.media.MediaDescription;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.support.v4.media.session.MediaSessionCompat;

public final class MediaDescriptionCompat implements Parcelable {
    public static final class Builder {
        public String a;
        public CharSequence b;
        public CharSequence c;
        public CharSequence d;
        public Bitmap e;
        public Uri f;
        public Bundle g;
        public Uri h;

    }

    public static final Parcelable.Creator CREATOR;
    public final String a;
    public final CharSequence b;
    public final CharSequence c;
    public final CharSequence d;
    public final Bitmap e;
    public final Uri f;
    public final Bundle g;
    public final Uri h;
    public Object i;

    static {
        MediaDescriptionCompat.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
            @Override  // android.os.Parcelable$Creator
            public final Object createFromParcel(Parcel parcel0) {
                Uri uri0;
                int v = Build.VERSION.SDK_INT;
                Object object0 = MediaDescription.CREATOR.createFromParcel(parcel0);
                Bundle bundle0 = null;
                if(object0 != null) {
                    Builder mediaDescriptionCompat$Builder0 = new Builder();  // 初始化器: Ljava/lang/Object;-><init>()V
                    mediaDescriptionCompat$Builder0.a = ((MediaDescription)object0).getMediaId();
                    mediaDescriptionCompat$Builder0.b = ((MediaDescription)object0).getTitle();
                    mediaDescriptionCompat$Builder0.c = ((MediaDescription)object0).getSubtitle();
                    mediaDescriptionCompat$Builder0.d = ((MediaDescription)object0).getDescription();
                    mediaDescriptionCompat$Builder0.e = ((MediaDescription)object0).getIconBitmap();
                    mediaDescriptionCompat$Builder0.f = ((MediaDescription)object0).getIconUri();
                    Bundle bundle1 = ((MediaDescription)object0).getExtras();
                    if(bundle1 == null) {
                        uri0 = null;
                    }
                    else {
                        bundle1.setClassLoader(MediaSessionCompat.class.getClassLoader());
                        uri0 = (Uri)bundle1.getParcelable("android.support.v4.media.description.MEDIA_URI");
                    }
                    if(uri0 == null) {
                        bundle0 = bundle1;
                    }
                    else if(!bundle1.containsKey("android.support.v4.media.description.NULL_BUNDLE_FLAG") || bundle1.size() != 2) {
                        bundle1.remove("android.support.v4.media.description.MEDIA_URI");
                        bundle1.remove("android.support.v4.media.description.NULL_BUNDLE_FLAG");
                        bundle0 = bundle1;
                    }
                    mediaDescriptionCompat$Builder0.g = bundle0;
                    if(uri0 != null) {
                        mediaDescriptionCompat$Builder0.h = uri0;
                    }
                    else if(v >= 23) {
                        mediaDescriptionCompat$Builder0.h = ((MediaDescription)object0).getMediaUri();
                    }
                    bundle0 = new MediaDescriptionCompat(mediaDescriptionCompat$Builder0.a, mediaDescriptionCompat$Builder0.b, mediaDescriptionCompat$Builder0.c, mediaDescriptionCompat$Builder0.d, mediaDescriptionCompat$Builder0.e, mediaDescriptionCompat$Builder0.f, mediaDescriptionCompat$Builder0.g, mediaDescriptionCompat$Builder0.h);
                    bundle0.i = object0;
                }
                return bundle0;
            }

            @Override  // android.os.Parcelable$Creator
            public final Object[] newArray(int v) {
                return new MediaDescriptionCompat[v];
            }
        };
    }

    public MediaDescriptionCompat(String s, CharSequence charSequence0, CharSequence charSequence1, CharSequence charSequence2, Bitmap bitmap0, Uri uri0, Bundle bundle0, Uri uri1) {
        this.a = s;
        this.b = charSequence0;
        this.c = charSequence1;
        this.d = charSequence2;
        this.e = bitmap0;
        this.f = uri0;
        this.g = bundle0;
        this.h = uri1;
    }

    @Override  // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override
    public final String toString() {
        return this.b + ", " + this.c + ", " + this.d;
    }

    @Override  // android.os.Parcelable
    public final void writeToParcel(Parcel parcel0, int v) {
        int v1 = Build.VERSION.SDK_INT;
        MediaDescription mediaDescription0 = this.i;
        if(mediaDescription0 == null) {
            MediaDescription.Builder mediaDescription$Builder0 = new MediaDescription.Builder();
            mediaDescription$Builder0.setMediaId(this.a);
            mediaDescription$Builder0.setTitle(this.b);
            mediaDescription$Builder0.setSubtitle(this.c);
            mediaDescription$Builder0.setDescription(this.d);
            mediaDescription$Builder0.setIconBitmap(this.e);
            mediaDescription$Builder0.setIconUri(this.f);
            Uri uri0 = this.h;
            Bundle bundle0 = this.g;
            if(v1 < 23 && uri0 != null) {
                if(bundle0 == null) {
                    bundle0 = new Bundle();
                    bundle0.putBoolean("android.support.v4.media.description.NULL_BUNDLE_FLAG", true);
                }
                bundle0.putParcelable("android.support.v4.media.description.MEDIA_URI", uri0);
            }
            mediaDescription$Builder0.setExtras(bundle0);
            if(v1 >= 23) {
                mediaDescription$Builder0.setMediaUri(uri0);
            }
            mediaDescription0 = mediaDescription$Builder0.build();
            this.i = mediaDescription0;
        }
        mediaDescription0.writeToParcel(parcel0, v);
    }
}


package android.support.v4.media;

import android.app.AppOpsManager;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.drawable.Icon;

public abstract class a {
    public static int a(int v, Context context0) {
        return context0.getColor(v);
    }

    public static ColorStateList g(int v, Context context0) {
        return context0.getColorStateList(v);
    }

    public static Icon i(int v, String s) {
        return Icon.createWithResource(s, v);
    }

    public static Icon j(int v, byte[] arr_b, int v1) {
        return Icon.createWithData(arr_b, v, v1);
    }

    public static Object n(Context context0) {
        return context0.getSystemService(AppOpsManager.class);
    }
}


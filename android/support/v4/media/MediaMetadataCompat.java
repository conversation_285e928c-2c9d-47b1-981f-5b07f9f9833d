package android.support.v4.media;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.support.v4.media.session.MediaSessionCompat;
import androidx.collection.ArrayMap;

public final class MediaMetadataCompat implements Parcelable {
    public static final Parcelable.Creator CREATOR;
    public final Bundle a;

    static {
        ArrayMap arrayMap0 = new ArrayMap();  // 初始化器: Landroidx/collection/SimpleArrayMap;-><init>()V
        arrayMap0.put("android.media.metadata.TITLE", 1);
        arrayMap0.put("android.media.metadata.ARTIST", 1);
        arrayMap0.put("android.media.metadata.DURATION", 0);
        arrayMap0.put("android.media.metadata.ALBUM", 1);
        arrayMap0.put("android.media.metadata.AUTHOR", 1);
        arrayMap0.put("android.media.metadata.WRITER", 1);
        arrayMap0.put("android.media.metadata.COMPOSER", 1);
        arrayMap0.put("android.media.metadata.COMPILATION", 1);
        arrayMap0.put("android.media.metadata.DATE", 1);
        arrayMap0.put("android.media.metadata.YEAR", 0);
        arrayMap0.put("android.media.metadata.GENRE", 1);
        arrayMap0.put("android.media.metadata.TRACK_NUMBER", 0);
        arrayMap0.put("android.media.metadata.NUM_TRACKS", 0);
        arrayMap0.put("android.media.metadata.DISC_NUMBER", 0);
        arrayMap0.put("android.media.metadata.ALBUM_ARTIST", 1);
        arrayMap0.put("android.media.metadata.ART", 2);
        arrayMap0.put("android.media.metadata.ART_URI", 1);
        arrayMap0.put("android.media.metadata.ALBUM_ART", 2);
        arrayMap0.put("android.media.metadata.ALBUM_ART_URI", 1);
        arrayMap0.put("android.media.metadata.USER_RATING", 3);
        arrayMap0.put("android.media.metadata.RATING", 3);
        arrayMap0.put("android.media.metadata.DISPLAY_TITLE", 1);
        arrayMap0.put("android.media.metadata.DISPLAY_SUBTITLE", 1);
        arrayMap0.put("android.media.metadata.DISPLAY_DESCRIPTION", 1);
        arrayMap0.put("android.media.metadata.DISPLAY_ICON", 2);
        arrayMap0.put("android.media.metadata.DISPLAY_ICON_URI", 1);
        arrayMap0.put("android.media.metadata.MEDIA_ID", 1);
        arrayMap0.put("android.media.metadata.BT_FOLDER_TYPE", 0);
        arrayMap0.put("android.media.metadata.MEDIA_URI", 1);
        arrayMap0.put("android.media.metadata.ADVERTISEMENT", 0);
        arrayMap0.put("android.media.metadata.DOWNLOAD_STATUS", 0);
        MediaMetadataCompat.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
            @Override  // android.os.Parcelable$Creator
            public final Object createFromParcel(Parcel parcel0) {
                return new MediaMetadataCompat(parcel0);
            }

            @Override  // android.os.Parcelable$Creator
            public final Object[] newArray(int v) {
                return new MediaMetadataCompat[v];
            }
        };
    }

    public MediaMetadataCompat(Parcel parcel0) {
        this.a = parcel0.readBundle(MediaSessionCompat.class.getClassLoader());
    }

    @Override  // android.os.Parcelable
    public final int describeContents() {
        return 0;
    }

    @Override  // android.os.Parcelable
    public final void writeToParcel(Parcel parcel0, int v) {
        parcel0.writeBundle(this.a);
    }
}


package android.support.customtabs;

import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;

public interface ICustomTabsService extends IInterface {
    public static abstract class Stub extends Binder implements ICustomTabsService {
        static class Proxy implements ICustomTabsService {
            public IBinder a;

            @Override  // android.os.IInterface
            public final IBinder asBinder() {
                return this.a;
            }

            @Override  // android.support.customtabs.ICustomTabsService
            public final boolean b(ICustomTabsCallback iCustomTabsCallback0, Uri uri0, Bundle bundle0) {
                Parcel parcel0 = Parcel.obtain();
                Parcel parcel1 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken("android.support.customtabs.ICustomTabsService");
                    parcel0.writeStrongBinder((iCustomTabsCallback0 == null ? null : iCustomTabsCallback0.asBinder()));
                    boolean z = true;
                    parcel0.writeInt(1);
                    uri0.writeToParcel(parcel0, 0);
                    parcel0.writeInt(1);
                    bundle0.writeToParcel(parcel0, 0);
                    parcel0.writeTypedList(null);
                    this.a.transact(4, parcel0, parcel1, 0);
                    parcel1.readException();
                    if(parcel1.readInt() == 0) {
                        z = false;
                    }
                    return z;
                }
                finally {
                    parcel1.recycle();
                    parcel0.recycle();
                }
            }

            @Override  // android.support.customtabs.ICustomTabsService
            public final int g(ICustomTabsCallback iCustomTabsCallback0, String s, Bundle bundle0) {
                Parcel parcel0 = Parcel.obtain();
                Parcel parcel1 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken("android.support.customtabs.ICustomTabsService");
                    parcel0.writeStrongBinder((iCustomTabsCallback0 == null ? null : iCustomTabsCallback0.asBinder()));
                    parcel0.writeString(s);
                    parcel0.writeInt(1);
                    bundle0.writeToParcel(parcel0, 0);
                    this.a.transact(8, parcel0, parcel1, 0);
                    parcel1.readException();
                    return parcel1.readInt();
                }
                finally {
                    parcel1.recycle();
                    parcel0.recycle();
                }
            }

            @Override  // android.support.customtabs.ICustomTabsService
            public final boolean h(ICustomTabsCallback iCustomTabsCallback0) {
                Parcel parcel0 = Parcel.obtain();
                Parcel parcel1 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken("android.support.customtabs.ICustomTabsService");
                    parcel0.writeStrongBinder(((android.support.customtabs.ICustomTabsCallback.Stub)iCustomTabsCallback0));
                    boolean z = false;
                    this.a.transact(3, parcel0, parcel1, 0);
                    parcel1.readException();
                    if(parcel1.readInt() != 0) {
                        z = true;
                    }
                    return z;
                }
                finally {
                    parcel1.recycle();
                    parcel0.recycle();
                }
            }

            @Override  // android.support.customtabs.ICustomTabsService
            public final boolean i(ICustomTabsCallback iCustomTabsCallback0, Uri uri0, Bundle bundle0) {
                Parcel parcel0 = Parcel.obtain();
                Parcel parcel1 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken("android.support.customtabs.ICustomTabsService");
                    parcel0.writeStrongBinder((iCustomTabsCallback0 == null ? null : iCustomTabsCallback0.asBinder()));
                    boolean z = true;
                    if(uri0 == null) {
                        parcel0.writeInt(0);
                    }
                    else {
                        parcel0.writeInt(1);
                        uri0.writeToParcel(parcel0, 0);
                    }
                    parcel0.writeInt(1);
                    bundle0.writeToParcel(parcel0, 0);
                    this.a.transact(11, parcel0, parcel1, 0);
                    parcel1.readException();
                    if(parcel1.readInt() == 0) {
                        z = false;
                    }
                    return z;
                }
                finally {
                    parcel1.recycle();
                    parcel0.recycle();
                }
            }

            @Override  // android.support.customtabs.ICustomTabsService
            public final boolean l(ICustomTabsCallback iCustomTabsCallback0, Uri uri0) {
                Parcel parcel0 = Parcel.obtain();
                Parcel parcel1 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken("android.support.customtabs.ICustomTabsService");
                    parcel0.writeStrongBinder((iCustomTabsCallback0 == null ? null : iCustomTabsCallback0.asBinder()));
                    boolean z = true;
                    if(uri0 == null) {
                        parcel0.writeInt(0);
                    }
                    else {
                        parcel0.writeInt(1);
                        uri0.writeToParcel(parcel0, 0);
                    }
                    this.a.transact(7, parcel0, parcel1, 0);
                    parcel1.readException();
                    if(parcel1.readInt() == 0) {
                        z = false;
                    }
                    return z;
                }
                finally {
                    parcel1.recycle();
                    parcel0.recycle();
                }
            }

            @Override  // android.support.customtabs.ICustomTabsService
            public final boolean q() {
                Parcel parcel0 = Parcel.obtain();
                Parcel parcel1 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken("android.support.customtabs.ICustomTabsService");
                    parcel0.writeLong(0L);
                    boolean z = false;
                    this.a.transact(2, parcel0, parcel1, 0);
                    parcel1.readException();
                    if(parcel1.readInt() != 0) {
                        z = true;
                    }
                    return z;
                }
                finally {
                    parcel1.recycle();
                    parcel0.recycle();
                }
            }
        }

        public static final int a;

        public static ICustomTabsService r(IBinder iBinder0) {
            if(iBinder0 == null) {
                return null;
            }
            IInterface iInterface0 = iBinder0.queryLocalInterface("android.support.customtabs.ICustomTabsService");
            if(iInterface0 != null && iInterface0 instanceof ICustomTabsService) {
                return (ICustomTabsService)iInterface0;
            }
            ICustomTabsService iCustomTabsService0 = new Proxy();  // 初始化器: Ljava/lang/Object;-><init>()V
            iCustomTabsService0.a = iBinder0;
            return iCustomTabsService0;
        }
    }

    boolean b(ICustomTabsCallback arg1, Uri arg2, Bundle arg3);

    int g(ICustomTabsCallback arg1, String arg2, Bundle arg3);

    boolean h(ICustomTabsCallback arg1);

    boolean i(ICustomTabsCallback arg1, Uri arg2, Bundle arg3);

    boolean l(ICustomTabsCallback arg1, Uri arg2);

    boolean q();
}


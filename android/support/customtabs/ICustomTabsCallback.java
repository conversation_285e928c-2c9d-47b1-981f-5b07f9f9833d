package android.support.customtabs;

import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;

public interface ICustomTabsCallback extends IInterface {
    public static abstract class Stub extends Binder implements ICustomTabsCallback {
        @Override  // android.os.IInterface
        public final IBinder asBinder() {
            return this;
        }

        @Override  // android.os.Binder
        public final boolean onTransact(int v, Parcel parcel0, Parcel parcel1, int v1) {
            if(v != 0x5F4E5446) {
                boolean z = false;
                Bundle bundle0 = null;
                switch(v) {
                    case 2: {
                        parcel0.enforceInterface("android.support.customtabs.ICustomTabsCallback");
                        int v2 = parcel0.readInt();
                        if(parcel0.readInt() != 0) {
                            bundle0 = (Bundle)Bundle.CREATOR.createFromParcel(parcel0);
                        }
                        this.k(v2, bundle0);
                        parcel1.writeNoException();
                        return true;
                    }
                    case 3: {
                        parcel0.enforceInterface("android.support.customtabs.ICustomTabsCallback");
                        String s = parcel0.readString();
                        if(parcel0.readInt() != 0) {
                            bundle0 = (Bundle)Bundle.CREATOR.createFromParcel(parcel0);
                        }
                        this.j(s, bundle0);
                        parcel1.writeNoException();
                        return true;
                    }
                    case 4: {
                        parcel0.enforceInterface("android.support.customtabs.ICustomTabsCallback");
                        if(parcel0.readInt() != 0) {
                            bundle0 = (Bundle)Bundle.CREATOR.createFromParcel(parcel0);
                        }
                        this.n(bundle0);
                        parcel1.writeNoException();
                        return true;
                    }
                    case 5: {
                        parcel0.enforceInterface("android.support.customtabs.ICustomTabsCallback");
                        String s1 = parcel0.readString();
                        if(parcel0.readInt() != 0) {
                            bundle0 = (Bundle)Bundle.CREATOR.createFromParcel(parcel0);
                        }
                        this.m(s1, bundle0);
                        parcel1.writeNoException();
                        return true;
                    }
                    case 6: {
                        parcel0.enforceInterface("android.support.customtabs.ICustomTabsCallback");
                        int v3 = parcel0.readInt();
                        Uri uri0 = parcel0.readInt() == 0 ? null : ((Uri)Uri.CREATOR.createFromParcel(parcel0));
                        if(parcel0.readInt() != 0) {
                            z = true;
                        }
                        if(parcel0.readInt() != 0) {
                            bundle0 = (Bundle)Bundle.CREATOR.createFromParcel(parcel0);
                        }
                        this.p(v3, uri0, z, bundle0);
                        parcel1.writeNoException();
                        return true;
                    }
                    case 7: {
                        parcel0.enforceInterface("android.support.customtabs.ICustomTabsCallback");
                        String s2 = parcel0.readString();
                        if(parcel0.readInt() != 0) {
                            bundle0 = (Bundle)Bundle.CREATOR.createFromParcel(parcel0);
                        }
                        Bundle bundle1 = this.d(s2, bundle0);
                        parcel1.writeNoException();
                        if(bundle1 != null) {
                            parcel1.writeInt(1);
                            bundle1.writeToParcel(parcel1, 1);
                            return true;
                        }
                        parcel1.writeInt(0);
                        return true;
                    }
                    default: {
                        return super.onTransact(v, parcel0, parcel1, v1);
                    }
                }
            }
            parcel1.writeString("android.support.customtabs.ICustomTabsCallback");
            return true;
        }
    }

    Bundle d(String arg1, Bundle arg2);

    void j(String arg1, Bundle arg2);

    void k(int arg1, Bundle arg2);

    void m(String arg1, Bundle arg2);

    void n(Bundle arg1);

    void p(int arg1, Uri arg2, boolean arg3, Bundle arg4);
}


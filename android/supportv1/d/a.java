package android.supportv1.d;

import android.content.res.AssetManager.AssetInputStream;
import androidx.work.impl.model.c;
import com.artifex.solib.m;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.DataInput;
import java.io.DataInputStream;
import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.TimeZone;
import java.util.regex.Pattern;

public class a {
    static class android.supportv1.d.a.a extends InputStream implements DataInput {
        public final int a;
        public int b;
        public final DataInputStream c;
        public ByteOrder d;
        public static final ByteOrder e;
        public static final ByteOrder f;

        static {
            android.supportv1.d.a.a.e = ByteOrder.LITTLE_ENDIAN;
            android.supportv1.d.a.a.f = ByteOrder.BIG_ENDIAN;
        }

        public android.supportv1.d.a.a(InputStream inputStream0) {
            this.d = ByteOrder.BIG_ENDIAN;
            DataInputStream dataInputStream0 = new DataInputStream(inputStream0);
            this.c = dataInputStream0;
            int v = dataInputStream0.available();
            this.a = v;
            this.b = 0;
            dataInputStream0.mark(v);
        }

        public android.supportv1.d.a.a(byte[] arr_b) {
            this(new ByteArrayInputStream(arr_b));
        }

        public final void a(long v) {
            long v1 = (long)this.b;
            if(v1 > v) {
                this.b = 0;
                this.c.reset();
                this.c.mark(this.a);
            }
            else {
                v -= v1;
            }
            if(this.skipBytes(((int)v)) != ((int)v)) {
                throw new IOException("Couldn\'t seek up to the byteCount");
            }
        }

        @Override
        public final int available() {
            return this.c.available();
        }

        @Override
        public final int read() {
            ++this.b;
            return this.c.read();
        }

        @Override
        public final int read(byte[] arr_b, int v, int v1) {
            int v2 = this.c.read(arr_b, v, v1);
            this.b += v2;
            return v2;
        }

        @Override
        public final boolean readBoolean() {
            ++this.b;
            return this.c.readBoolean();
        }

        @Override
        public final byte readByte() {
            int v = this.b + 1;
            this.b = v;
            if(v > this.a) {
                throw new EOFException();
            }
            int v1 = this.c.read();
            if(v1 < 0) {
                throw new EOFException();
            }
            return (byte)v1;
        }

        @Override
        public final char readChar() {
            this.b += 2;
            return this.c.readChar();
        }

        @Override
        public final double readDouble() {
            return Double.longBitsToDouble(this.readLong());
        }

        @Override
        public final float readFloat() {
            return Float.intBitsToFloat(this.readInt());
        }

        @Override
        public final void readFully(byte[] arr_b) {
            int v = this.b + arr_b.length;
            this.b = v;
            if(v > this.a) {
                throw new EOFException();
            }
            if(this.c.read(arr_b, 0, arr_b.length) != arr_b.length) {
                throw new IOException("Couldn\'t read up to the length of buffer");
            }
        }

        @Override
        public final void readFully(byte[] arr_b, int v, int v1) {
            int v2 = this.b + v1;
            this.b = v2;
            if(v2 > this.a) {
                throw new EOFException();
            }
            if(this.c.read(arr_b, v, v1) != v1) {
                throw new IOException("Couldn\'t read up to the length of buffer");
            }
        }

        @Override
        public final int readInt() {
            int v = this.b + 4;
            this.b = v;
            if(v > this.a) {
                throw new EOFException();
            }
            int v1 = this.c.read();
            int v2 = this.c.read();
            int v3 = this.c.read();
            int v4 = this.c.read();
            if((v1 | v2 | v3 | v4) < 0) {
                throw new EOFException();
            }
            ByteOrder byteOrder0 = this.d;
            if(byteOrder0 == android.supportv1.d.a.a.e) {
                return (v4 << 24) + (v3 << 16) + (v2 << 8) + v1;
            }
            if(byteOrder0 != android.supportv1.d.a.a.f) {
                throw new IOException("Invalid byte order: " + this.d);
            }
            return (v1 << 24) + (v2 << 16) + (v3 << 8) + v4;
        }

        @Override
        public final String readLine() {
            return null;
        }

        @Override
        public final long readLong() {
            int v = this.b + 8;
            this.b = v;
            if(v > this.a) {
                throw new EOFException();
            }
            int v1 = this.c.read();
            int v2 = this.c.read();
            int v3 = this.c.read();
            int v4 = this.c.read();
            int v5 = this.c.read();
            int v6 = this.c.read();
            int v7 = this.c.read();
            int v8 = this.c.read();
            if((v1 | v2 | v3 | v4 | v5 | v6 | v7 | v8) < 0) {
                throw new EOFException();
            }
            ByteOrder byteOrder0 = this.d;
            if(byteOrder0 == android.supportv1.d.a.a.e) {
                return (((long)v8) << 56) + (((long)v7) << 0x30) + (((long)v6) << 40) + (((long)v5) << 0x20) + (((long)v4) << 24) + (((long)v3) << 16) + (((long)v2) << 8) + ((long)v1);
            }
            if(byteOrder0 != android.supportv1.d.a.a.f) {
                throw new IOException("Invalid byte order: " + this.d);
            }
            return (((long)v1) << 56) + (((long)v2) << 0x30) + (((long)v3) << 40) + (((long)v4) << 0x20) + (((long)v5) << 24) + (((long)v6) << 16) + (((long)v7) << 8) + ((long)v8);
        }

        @Override
        public final short readShort() {
            int v = this.b + 2;
            this.b = v;
            if(v > this.a) {
                throw new EOFException();
            }
            int v1 = this.c.read();
            int v2 = this.c.read();
            if((v1 | v2) < 0) {
                throw new EOFException();
            }
            ByteOrder byteOrder0 = this.d;
            if(byteOrder0 == android.supportv1.d.a.a.e) {
                return (short)((v2 << 8) + v1);
            }
            if(byteOrder0 != android.supportv1.d.a.a.f) {
                throw new IOException("Invalid byte order: " + this.d);
            }
            return (short)((v1 << 8) + v2);
        }

        @Override
        public final String readUTF() {
            this.b += 2;
            return this.c.readUTF();
        }

        @Override
        public final int readUnsignedByte() {
            ++this.b;
            return this.c.readUnsignedByte();
        }

        @Override
        public final int readUnsignedShort() {
            int v = this.b + 2;
            this.b = v;
            if(v > this.a) {
                throw new EOFException();
            }
            int v1 = this.c.read();
            int v2 = this.c.read();
            if((v1 | v2) < 0) {
                throw new EOFException();
            }
            ByteOrder byteOrder0 = this.d;
            if(byteOrder0 == android.supportv1.d.a.a.e) {
                return (v2 << 8) + v1;
            }
            if(byteOrder0 != android.supportv1.d.a.a.f) {
                throw new IOException("Invalid byte order: " + this.d);
            }
            return (v1 << 8) + v2;
        }

        @Override
        public final int skipBytes(int v) {
            int v1 = Math.min(v, this.a - this.b);
            int v2;
            for(v2 = 0; v2 < v1; v2 += this.c.skipBytes(v1 - v2)) {
            }
            this.b += v2;
            return v2;
        }
    }

    static class b {
        public final int a;
        public final int b;
        public final byte[] c;

        public b(int v, byte[] arr_b, int v1) {
            this.a = v;
            this.b = v1;
            this.c = arr_b;
        }

        public static b a(int v, ByteOrder byteOrder0) {
            ByteBuffer byteBuffer0 = ByteBuffer.wrap(new byte[a.s[3]]);
            byteBuffer0.order(byteOrder0);
            byteBuffer0.putShort(((short)v));
            return new b(3, byteBuffer0.array(), 1);
        }

        public static b b(long v, ByteOrder byteOrder0) {
            ByteBuffer byteBuffer0 = ByteBuffer.wrap(new byte[a.s[4]]);
            byteBuffer0.order(byteOrder0);
            byteBuffer0.putInt(((int)v));
            return new b(4, byteBuffer0.array(), 1);
        }

        public static b c(d a$d0, ByteOrder byteOrder0) {
            ByteBuffer byteBuffer0 = ByteBuffer.wrap(new byte[a.s[5]]);
            byteBuffer0.order(byteOrder0);
            byteBuffer0.putInt(((int)a$d0.a));
            byteBuffer0.putInt(((int)a$d0.b));
            return new b(5, byteBuffer0.array(), 1);
        }

        public final Serializable d(ByteOrder byteOrder0) {
            Serializable serializable10;
            Serializable serializable9;
            Serializable serializable8;
            Serializable serializable7;
            Serializable serializable6;
            Serializable serializable5;
            Serializable serializable4;
            Serializable serializable3;
            Serializable serializable2;
            Serializable serializable1;
            Serializable serializable0;
            int v1;
            android.supportv1.d.a.a a$a0;
            byte[] arr_b;
            int v = 0;
            try {
                arr_b = this.c;
                a$a0 = null;
                a$a0 = new android.supportv1.d.a.a(arr_b);
                a$a0.d = byteOrder0;
                v1 = this.b;
                switch(this.a) {
                    case 3: {
                        goto label_40;
                    }
                    case 4: {
                        goto label_47;
                    }
                    case 5: {
                        goto label_54;
                    }
                    case 1: 
                    case 6: {
                        goto label_9;
                    }
                    case 2: 
                    case 7: {
                        goto label_18;
                    }
                    case 8: {
                        goto label_61;
                    }
                    case 9: {
                        goto label_68;
                    }
                    case 10: {
                        goto label_75;
                    }
                    case 11: {
                        goto label_82;
                    }
                    case 12: {
                        goto label_89;
                    }
                }
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return null;
        label_9:
            if(arr_b.length == 1) {
                int v2 = arr_b[0];
                try {
                    if(v2 >= 0 && v2 <= 1) {
                        serializable0 = new String(new char[]{((char)(v2 + 0x30))});
                        goto label_13;
                    }
                    goto label_15;
                }
                catch(IOException unused_ex) {
                    goto label_94;
                }
                catch(Throwable throwable0) {
                    goto label_98;
                }
                try {
                label_13:
                    a$a0.close();
                }
                catch(IOException unused_ex) {
                }
                return serializable0;
            }
            try {
            label_15:
                serializable1 = new String(arr_b, a.v);
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable1;
            try {
            label_18:
                if(v1 >= a.t.length) {
                    for(int v3 = 0; true; ++v3) {
                        byte[] arr_b1 = a.t;
                        if(v3 >= arr_b1.length) {
                            v = arr_b1.length;
                            break;
                        }
                        if(arr_b[v3] != arr_b1[v3]) {
                            break;
                        }
                    }
                }
                StringBuilder stringBuilder0 = new StringBuilder();
                while(v < v1) {
                    int v4 = arr_b[v];
                    if(v4 == 0) {
                        break;
                    }
                    if(v4 >= 0x20) {
                        stringBuilder0.append(((char)v4));
                    }
                    else {
                        stringBuilder0.append('?');
                    }
                    ++v;
                }
                serializable2 = stringBuilder0.toString();
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable2;
            try {
            label_40:
                serializable3 = new int[v1];
                while(v < v1) {
                    serializable3[v] = a$a0.readUnsignedShort();
                    ++v;
                }
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable3;
            try {
            label_47:
                serializable4 = new long[v1];
                while(v < v1) {
                    serializable4[v] = ((long)a$a0.readInt()) & 0xFFFFFFFFL;
                    ++v;
                }
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable4;
            try {
            label_54:
                serializable5 = new d[v1];
                while(v < v1) {
                    serializable5[v] = new d(((long)a$a0.readInt()) & 0xFFFFFFFFL, ((long)a$a0.readInt()) & 0xFFFFFFFFL);
                    ++v;
                }
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable5;
            try {
            label_61:
                serializable6 = new int[v1];
                while(v < v1) {
                    serializable6[v] = a$a0.readShort();
                    ++v;
                }
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable6;
            try {
            label_68:
                serializable7 = new int[v1];
                while(v < v1) {
                    serializable7[v] = a$a0.readInt();
                    ++v;
                }
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable7;
            try {
            label_75:
                serializable8 = new d[v1];
                while(v < v1) {
                    serializable8[v] = new d(((long)a$a0.readInt()), ((long)a$a0.readInt()));
                    ++v;
                }
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable8;
            try {
            label_82:
                serializable9 = new double[v1];
                while(v < v1) {
                    serializable9[v] = (double)a$a0.readFloat();
                    ++v;
                }
            }
            catch(IOException unused_ex) {
                goto label_94;
            }
            catch(Throwable throwable0) {
                goto label_98;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable9;
            try {
            label_89:
                serializable10 = new double[v1];
                while(v < v1) {
                    serializable10[v] = a$a0.readDouble();
                    ++v;
                }
            }
            catch(IOException unused_ex) {
            label_94:
                if(a$a0 != null) {
                    try {
                        a$a0.close();
                    }
                    catch(IOException unused_ex) {
                    }
                }
                return null;
            }
            catch(Throwable throwable0) {
            label_98:
                if(a$a0 != null) {
                    try {
                        a$a0.close();
                    }
                    catch(IOException unused_ex) {
                    }
                }
                throw throwable0;
            }
            try {
                a$a0.close();
            }
            catch(IOException unused_ex) {
            }
            return serializable10;
        }

        public final double e(ByteOrder byteOrder0) {
            Serializable serializable0 = this.d(byteOrder0);
            if(serializable0 == null) {
                throw new NumberFormatException("NULL can\'t be converted to a double value");
            }
            if(serializable0 instanceof String) {
                return Double.parseDouble(((String)serializable0));
            }
            if(serializable0 instanceof long[]) {
                if(((long[])serializable0).length != 1) {
                    throw new NumberFormatException("There are more than one component");
                }
                return (double)((long[])serializable0)[0];
            }
            if(serializable0 instanceof int[]) {
                if(((int[])serializable0).length != 1) {
                    throw new NumberFormatException("There are more than one component");
                }
                return (double)((int[])serializable0)[0];
            }
            if(serializable0 instanceof double[]) {
                if(((double[])serializable0).length != 1) {
                    throw new NumberFormatException("There are more than one component");
                }
                return ((double[])serializable0)[0];
            }
            if(!(serializable0 instanceof d[])) {
                throw new NumberFormatException("Couldn\'t find a double value");
            }
            if(((d[])serializable0).length != 1) {
                throw new NumberFormatException("There are more than one component");
            }
            return ((double)((d[])serializable0)[0].a) / ((double)((d[])serializable0)[0].b);
        }

        public final int f(ByteOrder byteOrder0) {
            Serializable serializable0 = this.d(byteOrder0);
            if(serializable0 == null) {
                throw new NumberFormatException("NULL can\'t be converted to a integer value");
            }
            if(serializable0 instanceof String) {
                return Integer.parseInt(((String)serializable0));
            }
            if(serializable0 instanceof long[]) {
                if(((long[])serializable0).length != 1) {
                    throw new NumberFormatException("There are more than one component");
                }
                return (int)((long[])serializable0)[0];
            }
            if(!(serializable0 instanceof int[])) {
                throw new NumberFormatException("Couldn\'t find a integer value");
            }
            if(((int[])serializable0).length != 1) {
                throw new NumberFormatException("There are more than one component");
            }
            return ((int[])serializable0)[0];
        }

        public final String g(ByteOrder byteOrder0) {
            Serializable serializable0 = this.d(byteOrder0);
            if(serializable0 == null) {
                return null;
            }
            if(serializable0 instanceof String) {
                return (String)serializable0;
            }
            StringBuilder stringBuilder0 = new StringBuilder();
            int v = 0;
            if(serializable0 instanceof long[]) {
                while(v < ((long[])serializable0).length) {
                    stringBuilder0.append(((long[])serializable0)[v]);
                    ++v;
                    if(v != ((long[])serializable0).length) {
                        stringBuilder0.append(",");
                    }
                }
                return stringBuilder0.toString();
            }
            if(serializable0 instanceof int[]) {
                while(v < ((int[])serializable0).length) {
                    stringBuilder0.append(((int[])serializable0)[v]);
                    ++v;
                    if(v != ((int[])serializable0).length) {
                        stringBuilder0.append(",");
                    }
                }
                return stringBuilder0.toString();
            }
            if(serializable0 instanceof double[]) {
                while(v < ((double[])serializable0).length) {
                    stringBuilder0.append(((double[])serializable0)[v]);
                    ++v;
                    if(v != ((double[])serializable0).length) {
                        stringBuilder0.append(",");
                    }
                }
                return stringBuilder0.toString();
            }
            if(serializable0 instanceof d[]) {
                while(v < ((d[])serializable0).length) {
                    stringBuilder0.append(((d[])serializable0)[v].a);
                    stringBuilder0.append('/');
                    stringBuilder0.append(((d[])serializable0)[v].b);
                    ++v;
                    if(v != ((d[])serializable0).length) {
                        stringBuilder0.append(",");
                    }
                }
                return stringBuilder0.toString();
            }
            return null;
        }

        @Override
        public final String toString() {
            StringBuilder stringBuilder0 = new StringBuilder("(");
            stringBuilder0.append(a.r[this.a]);
            stringBuilder0.append(", data length:");
            return c.l(stringBuilder0, this.c.length, ")");
        }
    }

    static class android.supportv1.d.a.c {
        public final int a;
        public final String b;
        public final int c;
        public final int d;

        public android.supportv1.d.a.c(int v, String s) {
            this.b = s;
            this.a = v;
            this.c = 3;
            this.d = 4;
        }

        public android.supportv1.d.a.c(String s, int v, int v1) {
            this.b = s;
            this.a = v;
            this.c = v1;
            this.d = -1;
        }
    }

    static class d {
        public final long a;
        public final long b;

        public d(long v, long v1) {
            if(v1 == 0L) {
                this.a = 0L;
                this.b = 1L;
                return;
            }
            this.a = v;
            this.b = v1;
        }

        @Override
        public final String toString() {
            return this.a + "/" + this.b;
        }
    }

    public static final byte[] A;
    public static final android.supportv1.d.a.c B;
    public static final android.supportv1.d.a.c[] C;
    public final AssetManager.AssetInputStream a;
    public int b;
    public final HashMap[] c;
    public final HashSet d;
    public ByteOrder e;
    public int f;
    public int g;
    public int h;
    public int i;
    public int j;
    public static final HashMap[] k;
    public static final HashMap[] l;
    public static final HashSet m;
    public static final HashMap n;
    public static final int[] o;
    public static final int[] p;
    public static final byte[] q;
    public static final String[] r;
    public static final int[] s;
    public static final byte[] t;
    public static final android.supportv1.d.a.c[][] u;
    public static final Charset v;
    public static final byte[] w;
    public static final List x;
    public static final List y;
    public static final byte[] z;

    static {
        a.x = Arrays.asList(new Integer[]{1, 6, 3, 8});
        a.y = Arrays.asList(new Integer[]{2, 7, 4, 5});
        a.o = new int[]{8, 8, 8};
        a.p = new int[]{8};
        a.q = new byte[]{-1, -40, -1};
        a.z = new byte[]{0x4F, 76, 89, 77, 80, 0};
        a.A = new byte[]{0x4F, 76, 89, 77, 80, 85, 83, 0, 73, 73};
        a.r = new String[]{"", "BYTE", "STRING", "USHORT", "ULONG", "URATIONAL", "SBYTE", "UNDEFINED", "SSHORT", "SLONG", "SRATIONAL", "SINGLE", "DOUBLE"};
        a.s = new int[]{0, 1, 1, 2, 4, 8, 1, 1, 2, 4, 8, 4, 8, 1};
        a.t = new byte[]{65, 83, 67, 73, 73, 0, 0, 0};
        android.supportv1.d.a.c[] arr_a$c = {new android.supportv1.d.a.c("NewSubfileType", 0xFE, 4), new android.supportv1.d.a.c("SubfileType", 0xFF, 4), new android.supportv1.d.a.c(0x100, "ImageWidth"), new android.supportv1.d.a.c(0x101, "ImageLength"), new android.supportv1.d.a.c("BitsPerSample", 0x102, 3), new android.supportv1.d.a.c("Compression", 0x103, 3), new android.supportv1.d.a.c("PhotometricInterpretation", 0x106, 3), new android.supportv1.d.a.c("ImageDescription", 270, 2), new android.supportv1.d.a.c("Make", 0x10F, 2), new android.supportv1.d.a.c("Model", 0x110, 2), new android.supportv1.d.a.c(273, "StripOffsets"), new android.supportv1.d.a.c("Orientation", 274, 3), new android.supportv1.d.a.c("SamplesPerPixel", 277, 3), new android.supportv1.d.a.c(278, "RowsPerStrip"), new android.supportv1.d.a.c(279, "StripByteCounts"), new android.supportv1.d.a.c("XResolution", 282, 5), new android.supportv1.d.a.c("YResolution", 283, 5), new android.supportv1.d.a.c("PlanarConfiguration", 284, 3), new android.supportv1.d.a.c("ResolutionUnit", 296, 3), new android.supportv1.d.a.c("TransferFunction", 301, 3), new android.supportv1.d.a.c("Software", 305, 2), new android.supportv1.d.a.c("DateTime", 306, 2), new android.supportv1.d.a.c("Artist", 315, 2), new android.supportv1.d.a.c("WhitePoint", 318, 5), new android.supportv1.d.a.c("PrimaryChromaticities", 0x13F, 5), new android.supportv1.d.a.c("SubIFDPointer", 330, 4), new android.supportv1.d.a.c("JPEGInterchangeFormat", 0x201, 4), new android.supportv1.d.a.c("JPEGInterchangeFormatLength", 0x202, 4), new android.supportv1.d.a.c("YCbCrCoefficients", 529, 5), new android.supportv1.d.a.c("YCbCrSubSampling", 530, 3), new android.supportv1.d.a.c("YCbCrPositioning", 531, 3), new android.supportv1.d.a.c("ReferenceBlackWhite", 532, 5), new android.supportv1.d.a.c("Copyright", 0x8298, 2), new android.supportv1.d.a.c("ExifIFDPointer", 0x8769, 4), new android.supportv1.d.a.c("GPSInfoIFDPointer", 0x8825, 4), new android.supportv1.d.a.c("SensorTopBorder", 4, 4), new android.supportv1.d.a.c("SensorLeftBorder", 5, 4), new android.supportv1.d.a.c("SensorBottomBorder", 6, 4), new android.supportv1.d.a.c("SensorRightBorder", 7, 4), new android.supportv1.d.a.c("ISO", 23, 3), new android.supportv1.d.a.c("JpgFromRaw", 46, 7)};
        android.supportv1.d.a.c[] arr_a$c1 = {new android.supportv1.d.a.c("ExposureTime", 0x829A, 5), new android.supportv1.d.a.c("FNumber", 0x829D, 5), new android.supportv1.d.a.c("ExposureProgram", 34850, 3), new android.supportv1.d.a.c("SpectralSensitivity", 0x8824, 2), new android.supportv1.d.a.c("PhotographicSensitivity", 0x8827, 3), new android.supportv1.d.a.c("OECF", 0x8828, 7), new android.supportv1.d.a.c("ExifVersion", 0x9000, 2), new android.supportv1.d.a.c("DateTimeOriginal", 0x9003, 2), new android.supportv1.d.a.c("DateTimeDigitized", 0x9004, 2), new android.supportv1.d.a.c("ComponentsConfiguration", 0x9101, 7), new android.supportv1.d.a.c("CompressedBitsPerPixel", 0x9102, 5), new android.supportv1.d.a.c("ShutterSpeedValue", 0x9201, 10), new android.supportv1.d.a.c("ApertureValue", 0x9202, 5), new android.supportv1.d.a.c("BrightnessValue", 0x9203, 10), new android.supportv1.d.a.c("ExposureBiasValue", 0x9204, 10), new android.supportv1.d.a.c("MaxApertureValue", 0x9205, 5), new android.supportv1.d.a.c("SubjectDistance", 0x9206, 5), new android.supportv1.d.a.c("MeteringMode", 0x9207, 3), new android.supportv1.d.a.c("LightSource", 0x9208, 3), new android.supportv1.d.a.c("Flash", 0x9209, 3), new android.supportv1.d.a.c("FocalLength", 0x920A, 5), new android.supportv1.d.a.c("SubjectArea", 0x9214, 3), new android.supportv1.d.a.c("MakerNote", 37500, 7), new android.supportv1.d.a.c("UserComment", 37510, 7), new android.supportv1.d.a.c("SubSecTime", 0x9290, 2), new android.supportv1.d.a.c("SubSecTimeOriginal", 0x9291, 2), new android.supportv1.d.a.c("SubSecTimeDigitized", 0x9292, 2), new android.supportv1.d.a.c("FlashpixVersion", 0xA000, 7), new android.supportv1.d.a.c("ColorSpace", 0xA001, 3), new android.supportv1.d.a.c(0xA002, "PixelXDimension"), new android.supportv1.d.a.c(0xA003, "PixelYDimension"), new android.supportv1.d.a.c("RelatedSoundFile", 0xA004, 2), new android.supportv1.d.a.c("InteroperabilityIFDPointer", 0xA005, 4), new android.supportv1.d.a.c("FlashEnergy", 0xA20B, 5), new android.supportv1.d.a.c("SpatialFrequencyResponse", 0xA20C, 7), new android.supportv1.d.a.c("FocalPlaneXResolution", 0xA20E, 5), new android.supportv1.d.a.c("FocalPlaneYResolution", 0xA20F, 5), new android.supportv1.d.a.c("FocalPlaneResolutionUnit", 0xA210, 3), new android.supportv1.d.a.c("SubjectLocation", 0xA214, 3), new android.supportv1.d.a.c("ExposureIndex", 0xA215, 5), new android.supportv1.d.a.c("SensingMethod", 0xA217, 3), new android.supportv1.d.a.c("FileSource", 0xA300, 7), new android.supportv1.d.a.c("SceneType", 0xA301, 7), new android.supportv1.d.a.c("CFAPattern", 0xA302, 7), new android.supportv1.d.a.c("CustomRendered", 0xA401, 3), new android.supportv1.d.a.c("ExposureMode", 0xA402, 3), new android.supportv1.d.a.c("WhiteBalance", 0xA403, 3), new android.supportv1.d.a.c("DigitalZoomRatio", 0xA404, 5), new android.supportv1.d.a.c("FocalLengthIn35mmFilm", 0xA405, 3), new android.supportv1.d.a.c("SceneCaptureType", 0xA406, 3), new android.supportv1.d.a.c("GainControl", 0xA407, 3), new android.supportv1.d.a.c("Contrast", 0xA408, 3), new android.supportv1.d.a.c("Saturation", 0xA409, 3), new android.supportv1.d.a.c("Sharpness", 0xA40A, 3), new android.supportv1.d.a.c("DeviceSettingDescription", 0xA40B, 7), new android.supportv1.d.a.c("SubjectDistanceRange", 0xA40C, 3), new android.supportv1.d.a.c("ImageUniqueID", 0xA420, 2), new android.supportv1.d.a.c("DNGVersion", 50706, 1), new android.supportv1.d.a.c(50720, "DefaultCropSize")};
        android.supportv1.d.a.c[] arr_a$c2 = {new android.supportv1.d.a.c("GPSVersionID", 0, 1), new android.supportv1.d.a.c("GPSLatitudeRef", 1, 2), new android.supportv1.d.a.c("GPSLatitude", 2, 5), new android.supportv1.d.a.c("GPSLongitudeRef", 3, 2), new android.supportv1.d.a.c("GPSLongitude", 4, 5), new android.supportv1.d.a.c("GPSAltitudeRef", 5, 1), new android.supportv1.d.a.c("GPSAltitude", 6, 5), new android.supportv1.d.a.c("GPSTimeStamp", 7, 5), new android.supportv1.d.a.c("GPSSatellites", 8, 2), new android.supportv1.d.a.c("GPSStatus", 9, 2), new android.supportv1.d.a.c("GPSMeasureMode", 10, 2), new android.supportv1.d.a.c("GPSDOP", 11, 5), new android.supportv1.d.a.c("GPSSpeedRef", 12, 2), new android.supportv1.d.a.c("GPSSpeed", 13, 5), new android.supportv1.d.a.c("GPSTrackRef", 14, 2), new android.supportv1.d.a.c("GPSTrack", 15, 5), new android.supportv1.d.a.c("GPSImgDirectionRef", 16, 2), new android.supportv1.d.a.c("GPSImgDirection", 17, 5), new android.supportv1.d.a.c("GPSMapDatum", 18, 2), new android.supportv1.d.a.c("GPSDestLatitudeRef", 19, 2), new android.supportv1.d.a.c("GPSDestLatitude", 20, 5), new android.supportv1.d.a.c("GPSDestLongitudeRef", 21, 2), new android.supportv1.d.a.c("GPSDestLongitude", 22, 5), new android.supportv1.d.a.c("GPSDestBearingRef", 23, 2), new android.supportv1.d.a.c("GPSDestBearing", 24, 5), new android.supportv1.d.a.c("GPSDestDistanceRef", 25, 2), new android.supportv1.d.a.c("GPSDestDistance", 26, 5), new android.supportv1.d.a.c("GPSProcessingMethod", 27, 7), new android.supportv1.d.a.c("GPSAreaInformation", 28, 7), new android.supportv1.d.a.c("GPSDateStamp", 29, 2), new android.supportv1.d.a.c("GPSDifferential", 30, 3)};
        android.supportv1.d.a.c[] arr_a$c3 = {new android.supportv1.d.a.c("InteroperabilityIndex", 1, 2)};
        android.supportv1.d.a.c[] arr_a$c4 = {new android.supportv1.d.a.c("NewSubfileType", 0xFE, 4), new android.supportv1.d.a.c("SubfileType", 0xFF, 4), new android.supportv1.d.a.c(0x100, "ThumbnailImageWidth"), new android.supportv1.d.a.c(0x101, "ThumbnailImageLength"), new android.supportv1.d.a.c("BitsPerSample", 0x102, 3), new android.supportv1.d.a.c("Compression", 0x103, 3), new android.supportv1.d.a.c("PhotometricInterpretation", 0x106, 3), new android.supportv1.d.a.c("ImageDescription", 270, 2), new android.supportv1.d.a.c("Make", 0x10F, 2), new android.supportv1.d.a.c("Model", 0x110, 2), new android.supportv1.d.a.c(273, "StripOffsets"), new android.supportv1.d.a.c("Orientation", 274, 3), new android.supportv1.d.a.c("SamplesPerPixel", 277, 3), new android.supportv1.d.a.c(278, "RowsPerStrip"), new android.supportv1.d.a.c(279, "StripByteCounts"), new android.supportv1.d.a.c("XResolution", 282, 5), new android.supportv1.d.a.c("YResolution", 283, 5), new android.supportv1.d.a.c("PlanarConfiguration", 284, 3), new android.supportv1.d.a.c("ResolutionUnit", 296, 3), new android.supportv1.d.a.c("TransferFunction", 301, 3), new android.supportv1.d.a.c("Software", 305, 2), new android.supportv1.d.a.c("DateTime", 306, 2), new android.supportv1.d.a.c("Artist", 315, 2), new android.supportv1.d.a.c("WhitePoint", 318, 5), new android.supportv1.d.a.c("PrimaryChromaticities", 0x13F, 5), new android.supportv1.d.a.c("SubIFDPointer", 330, 4), new android.supportv1.d.a.c("JPEGInterchangeFormat", 0x201, 4), new android.supportv1.d.a.c("JPEGInterchangeFormatLength", 0x202, 4), new android.supportv1.d.a.c("YCbCrCoefficients", 529, 5), new android.supportv1.d.a.c("YCbCrSubSampling", 530, 3), new android.supportv1.d.a.c("YCbCrPositioning", 531, 3), new android.supportv1.d.a.c("ReferenceBlackWhite", 532, 5), new android.supportv1.d.a.c("Copyright", 0x8298, 2), new android.supportv1.d.a.c("ExifIFDPointer", 0x8769, 4), new android.supportv1.d.a.c("GPSInfoIFDPointer", 0x8825, 4), new android.supportv1.d.a.c("DNGVersion", 50706, 1), new android.supportv1.d.a.c(50720, "DefaultCropSize")};
        a.B = new android.supportv1.d.a.c("StripOffsets", 273, 3);
        a.u = new android.supportv1.d.a.c[][]{arr_a$c, arr_a$c1, arr_a$c2, arr_a$c3, arr_a$c4, arr_a$c, new android.supportv1.d.a.c[]{new android.supportv1.d.a.c("ThumbnailImage", 0x100, 7), new android.supportv1.d.a.c("CameraSettingsIFDPointer", 0x2020, 4), new android.supportv1.d.a.c("ImageProcessingIFDPointer", 0x2040, 4)}, new android.supportv1.d.a.c[]{new android.supportv1.d.a.c("PreviewImageStart", 0x101, 4), new android.supportv1.d.a.c("PreviewImageLength", 0x102, 4)}, new android.supportv1.d.a.c[]{new android.supportv1.d.a.c("AspectFrame", 4371, 3)}, new android.supportv1.d.a.c[]{new android.supportv1.d.a.c("ColorSpace", 55, 3)}};
        a.C = new android.supportv1.d.a.c[]{new android.supportv1.d.a.c("SubIFDPointer", 330, 4), new android.supportv1.d.a.c("ExifIFDPointer", 0x8769, 4), new android.supportv1.d.a.c("GPSInfoIFDPointer", 0x8825, 4), new android.supportv1.d.a.c("InteroperabilityIFDPointer", 0xA005, 4), new android.supportv1.d.a.c("CameraSettingsIFDPointer", 0x2020, 1), new android.supportv1.d.a.c("ImageProcessingIFDPointer", 0x2040, 1)};
        a.k = new HashMap[10];
        a.l = new HashMap[10];
        a.m = new HashSet(Arrays.asList(new String[]{"FNumber", "DigitalZoomRatio", "ExposureTime", "SubjectDistance", "GPSTimeStamp"}));
        a.n = new HashMap();
        Charset charset0 = Charset.forName("US-ASCII");
        a.v = charset0;
        a.w = "Exif\u0000\u0000".getBytes(charset0);
        new SimpleDateFormat("yyyy:MM:dd HH:mm:ss").setTimeZone(TimeZone.getTimeZone("UTC"));
        for(int v = 0; true; ++v) {
            android.supportv1.d.a.c[][] arr2_a$c = a.u;
            if(v >= arr2_a$c.length) {
                break;
            }
            a.k[v] = new HashMap();
            a.l[v] = new HashMap();
            android.supportv1.d.a.c[] arr_a$c5 = arr2_a$c[v];
            for(int v1 = 0; v1 < arr_a$c5.length; ++v1) {
                android.supportv1.d.a.c a$c0 = arr_a$c5[v1];
                a.k[v].put(a$c0.a, a$c0);
                a.l[v].put(a$c0.b, a$c0);
            }
        }
        a.n.put(a.C[0].a, 5);
        a.n.put(a.C[1].a, 1);
        a.n.put(a.C[2].a, 2);
        a.n.put(a.C[3].a, 3);
        a.n.put(a.C[4].a, 7);
        a.n.put(a.C[5].a, 8);
        Pattern.compile(".*[1-9].*");
        Pattern.compile("^([0-9][0-9]):([0-9][0-9]):([0-9][0-9])$");
    }

    public a(m m0) {
        android.supportv1.d.a.c[][] arr2_a$c = a.u;
        this.c = new HashMap[arr2_a$c.length];
        this.d = new HashSet(arr2_a$c.length);
        this.e = ByteOrder.BIG_ENDIAN;
        AssetManager.AssetInputStream assetManager$AssetInputStream0 = m0 instanceof AssetManager.AssetInputStream ? ((AssetManager.AssetInputStream)m0) : null;
        try {
            this.a = assetManager$AssetInputStream0;
            for(int v = 0; v < arr2_a$c.length; ++v) {
                this.c[v] = new HashMap();
            }
            BufferedInputStream bufferedInputStream0 = new BufferedInputStream(m0, 5000);
            this.b = this.b(bufferedInputStream0);
            android.supportv1.d.a.a a$a0 = new android.supportv1.d.a.a(bufferedInputStream0);
            switch(this.b) {
                case 4: {
                    this.h(a$a0, 0, 0);
                    break;
                }
                case 7: {
                    this.n(a$a0);
                    break;
                }
                case 9: {
                    this.k(a$a0);
                    break;
                }
                case 10: {
                    this.o(a$a0);
                    break;
                }
                case 0: 
                case 1: 
                case 2: 
                case 3: 
                case 5: 
                case 6: 
                case 8: 
                case 11: {
                    this.f(a$a0);
                }
            }
            this.r(a$a0);
        }
        catch(IOException unused_ex) {
        }
        finally {
            this.d();
        }
    }

    public final int a() {
        b a$b0 = this.j("Orientation");
        if(a$b0 == null) {
            return 1;
        }
        try {
            return a$b0.f(this.e);
        }
        catch(NumberFormatException unused_ex) {
            return 1;
        }
    }

    public final int b(BufferedInputStream bufferedInputStream0) {
        bufferedInputStream0.mark(5000);
        byte[] arr_b = new byte[5000];
        bufferedInputStream0.read(arr_b);
        bufferedInputStream0.reset();
        for(int v = 0; true; ++v) {
            byte[] arr_b1 = a.q;
            if(v >= arr_b1.length) {
                break;
            }
            if(arr_b[v] != arr_b1[v]) {
                byte[] arr_b2 = "FUJIFILMCCD-RAW".getBytes(Charset.defaultCharset());
                for(int v1 = 0; v1 < arr_b2.length; ++v1) {
                    if(arr_b[v1] != arr_b2[v1]) {
                        android.supportv1.d.a.a a$a0 = new android.supportv1.d.a.a(arr_b);
                        ByteOrder byteOrder0 = a.q(a$a0);
                        this.e = byteOrder0;
                        a$a0.d = byteOrder0;
                        int v2 = a$a0.readShort();
                        a$a0.close();
                        if(v2 != 20306 && v2 != 21330) {
                            android.supportv1.d.a.a a$a1 = new android.supportv1.d.a.a(arr_b);
                            ByteOrder byteOrder1 = a.q(a$a1);
                            this.e = byteOrder1;
                            a$a1.d = byteOrder1;
                            int v3 = a$a1.readShort();
                            a$a1.close();
                            return v3 == 85 ? 10 : 0;
                        }
                        return 7;
                    }
                }
                return 9;
            }
        }
        return 4;
    }

    public final String c(String s) {
        b a$b0 = this.j(s);
        if(a$b0 != null) {
            if(!a.m.contains(s)) {
                return a$b0.g(this.e);
            }
            if(s.equals("GPSTimeStamp")) {
                if(a$b0.a == 5 || a$b0.a == 10) {
                    d[] arr_a$d = (d[])a$b0.d(this.e);
                    if(arr_a$d != null && arr_a$d.length == 3) {
                        return String.format("%02d:%02d:%02d", ((int)(((float)arr_a$d[0].a) / ((float)arr_a$d[0].b))), ((int)(((float)arr_a$d[1].a) / ((float)arr_a$d[1].b))), ((int)(((float)arr_a$d[2].a) / ((float)arr_a$d[2].b))));
                    }
                    Arrays.toString(arr_a$d);
                }
                return null;
            }
            try {
                return Double.toString(a$b0.e(this.e));
            }
            catch(NumberFormatException unused_ex) {
            }
        }
        return null;
    }

    public final void d() {
        String s = this.c("DateTimeOriginal");
        HashMap[] arr_hashMap = this.c;
        if(s != null && this.c("DateTime") == null) {
            HashMap hashMap0 = arr_hashMap[0];
            byte[] arr_b = (s + "\u0000").getBytes(a.v);
            hashMap0.put("DateTime", new b(2, arr_b, arr_b.length));
        }
        if(this.c("ImageWidth") == null) {
            arr_hashMap[0].put("ImageWidth", b.b(0L, this.e));
        }
        if(this.c("ImageLength") == null) {
            arr_hashMap[0].put("ImageLength", b.b(0L, this.e));
        }
        if(this.c("Orientation") == null) {
            arr_hashMap[0].put("Orientation", b.b(0L, this.e));
        }
        if(this.c("LightSource") == null) {
            arr_hashMap[1].put("LightSource", b.b(0L, this.e));
        }
    }

    public final void e(int v, int v1) {
        HashMap[] arr_hashMap = this.c;
        if(!arr_hashMap[v].isEmpty() && !arr_hashMap[v1].isEmpty()) {
            b a$b0 = (b)arr_hashMap[v].get("ImageLength");
            b a$b1 = (b)arr_hashMap[v].get("ImageWidth");
            b a$b2 = (b)arr_hashMap[v1].get("ImageLength");
            b a$b3 = (b)arr_hashMap[v1].get("ImageWidth");
            if(a$b0 != null && a$b1 != null && a$b2 != null && a$b3 != null) {
                int v2 = a$b0.f(this.e);
                int v3 = a$b1.f(this.e);
                if(v2 < a$b2.f(this.e) && v3 < a$b3.f(this.e)) {
                    HashMap hashMap0 = arr_hashMap[v];
                    arr_hashMap[v] = arr_hashMap[v1];
                    arr_hashMap[v1] = hashMap0;
                }
            }
        }
    }

    public final void f(android.supportv1.d.a.a a$a0) {
        this.g(a$a0, a$a0.c.available());
        this.l(a$a0, 0);
        this.p(a$a0, 0);
        this.p(a$a0, 5);
        this.p(a$a0, 4);
        this.e(0, 5);
        this.e(0, 4);
        this.e(5, 4);
        HashMap[] arr_hashMap = this.c;
        b a$b0 = (b)arr_hashMap[1].get("PixelXDimension");
        b a$b1 = (b)arr_hashMap[1].get("PixelYDimension");
        if(a$b0 != null && a$b1 != null) {
            arr_hashMap[0].put("ImageWidth", a$b0);
            arr_hashMap[0].put("ImageLength", a$b1);
        }
        if(arr_hashMap[4].isEmpty() && this.m(arr_hashMap[5])) {
            arr_hashMap[4] = arr_hashMap[5];
            arr_hashMap[5] = new HashMap();
        }
        this.m(arr_hashMap[4]);
        if(this.b == 8) {
            b a$b2 = (b)arr_hashMap[1].get("MakerNote");
            if(a$b2 != null) {
                android.supportv1.d.a.a a$a1 = new android.supportv1.d.a.a(a$b2.c);
                a$a1.d = this.e;
                a$a1.a(6L);
                this.l(a$a1, 9);
                b a$b3 = (b)arr_hashMap[9].get("ColorSpace");
                if(a$b3 != null) {
                    arr_hashMap[1].put("ColorSpace", a$b3);
                }
            }
        }
    }

    public final void g(android.supportv1.d.a.a a$a0, int v) {
        ByteOrder byteOrder0 = a.q(a$a0);
        this.e = byteOrder0;
        a$a0.d = byteOrder0;
        int v1 = a$a0.readUnsignedShort();
        if(this.b != 7 && this.b != 10 && v1 != 42) {
            throw new IOException("Invalid start code: " + Integer.toHexString(v1));
        }
        int v2 = a$a0.readInt();
        if(v2 < 8 || v2 >= v) {
            throw new IOException("Invalid first Ifd offset: " + v2);
        }
        if(v2 - 8 > 0 && a$a0.skipBytes(v2 - 8) != v2 - 8) {
            throw new IOException("Couldn\'t jump to first Ifd: " + (v2 - 8));
        }
    }

    public final void h(android.supportv1.d.a.a a$a0, int v, int v1) {
        a$a0.d = ByteOrder.BIG_ENDIAN;
        a$a0.a(((long)v));
        int v2 = a$a0.readByte();
        if(v2 != -1) {
            throw new IOException("Invalid marker: " + Integer.toHexString(v2 & 0xFF));
        }
        if(a$a0.readByte() != -40) {
            throw new IOException("Invalid marker: ff");
        }
        for(int v3 = v + 2; true; v3 = v8 + v7) {
            int v4 = a$a0.readByte();
            if(v4 != -1) {
                throw new IOException("Invalid marker:" + Integer.toHexString(v4 & 0xFF));
            }
            int v5 = a$a0.readByte();
            if(v5 == -39 || v5 == -38) {
                a$a0.d = this.e;
                return;
            }
            int v6 = a$a0.readUnsignedShort();
            int v7 = v6 - 2;
            int v8 = v3 + 4;
            if(v7 < 0) {
                throw new IOException("Invalid length");
            }
            if(v5 != 0xFFFFFFE1) {
                HashMap[] arr_hashMap = this.c;
                if(v5 == -2) {
                    byte[] arr_b = new byte[v7];
                    if(a$a0.read(arr_b) != v7) {
                        throw new IOException("Invalid exif");
                    }
                    if(this.c("UserComment") == null) {
                        HashMap hashMap0 = arr_hashMap[1];
                        byte[] arr_b1 = (new String(arr_b, a.v) + "\u0000").getBytes(a.v);
                        hashMap0.put("UserComment", new b(2, arr_b1, arr_b1.length));
                    }
                    v7 = 0;
                }
                else if(v5 == 0xFFFFFFC0 || v5 == 0xFFFFFFC1 || v5 == -62 || v5 == -61 || (v5 == -59 || v5 == -58 || v5 == -57) || (v5 == -55 || v5 == -54 || v5 == -53) || (v5 == -51 || v5 == -50 || v5 == -49)) {
                    if(a$a0.skipBytes(1) != 1) {
                        throw new IOException("Invalid SOFx");
                    }
                    arr_hashMap[v1].put("ImageLength", b.b(a$a0.readUnsignedShort(), this.e));
                    arr_hashMap[v1].put("ImageWidth", b.b(a$a0.readUnsignedShort(), this.e));
                    v7 = v6 - 7;
                }
            }
            else if(v7 >= 6) {
                byte[] arr_b2 = new byte[6];
                if(a$a0.read(arr_b2) != 6) {
                    throw new IOException("Invalid exif");
                }
                v8 = v3 + 10;
                if(Arrays.equals(arr_b2, a.w)) {
                    if(v6 - 8 <= 0) {
                        throw new IOException("Invalid exif");
                    }
                    this.f = v8;
                    byte[] arr_b3 = new byte[v6 - 8];
                    if(a$a0.read(arr_b3) != v6 - 8) {
                        throw new IOException("Invalid exif");
                    }
                    v8 += v6 - 8;
                    android.supportv1.d.a.a a$a1 = new android.supportv1.d.a.a(arr_b3);
                    this.g(a$a1, v6 - 8);
                    this.l(a$a1, v1);
                    v7 = 0;
                }
                else {
                    v7 = v6 - 8;
                }
            }
            if(v7 < 0) {
                throw new IOException("Invalid length");
            }
            if(a$a0.skipBytes(v7) != v7) {
                break;
            }
        }
        throw new IOException("Invalid JPEG segment");
    }

    public static long[] i(Serializable serializable0) {
        if(serializable0 instanceof int[]) {
            long[] arr_v = new long[((int[])serializable0).length];
            for(int v = 0; v < ((int[])serializable0).length; ++v) {
                arr_v[v] = (long)((int[])serializable0)[v];
            }
            return arr_v;
        }
        return serializable0 instanceof long[] ? ((long[])serializable0) : null;
    }

    public final b j(String s) {
        if("ISOSpeedRatings".equals(s)) {
            s = "PhotographicSensitivity";
        }
        for(int v = 0; v < a.u.length; ++v) {
            b a$b0 = (b)this.c[v].get(s);
            if(a$b0 != null) {
                return a$b0;
            }
        }
        return null;
    }

    public final void k(android.supportv1.d.a.a a$a0) {
        a$a0.skipBytes(84);
        byte[] arr_b = new byte[4];
        byte[] arr_b1 = new byte[4];
        a$a0.read(arr_b);
        a$a0.skipBytes(4);
        a$a0.read(arr_b1);
        int v = ByteBuffer.wrap(arr_b).getInt();
        int v1 = ByteBuffer.wrap(arr_b1).getInt();
        this.h(a$a0, v, 5);
        a$a0.a(((long)v1));
        a$a0.d = ByteOrder.BIG_ENDIAN;
        int v2 = a$a0.readInt();
        for(int v3 = 0; v3 < v2; ++v3) {
            int v4 = a$a0.readUnsignedShort();
            int v5 = a$a0.readUnsignedShort();
            if(v4 == a.B.a) {
                int v6 = a$a0.readShort();
                int v7 = a$a0.readShort();
                b a$b0 = b.a(v6, this.e);
                b a$b1 = b.a(v7, this.e);
                this.c[0].put("ImageLength", a$b0);
                this.c[0].put("ImageWidth", a$b1);
                return;
            }
            a$a0.skipBytes(v5);
        }
    }

    public final void l(android.supportv1.d.a.a a$a0, int v) {
        StringBuilder stringBuilder0;
        long v18;
        HashMap[] arr_hashMap1;
        int v17;
        int v16;
        long v14;
        boolean z;
        long v12;
        int v11;
        int v10;
        HashSet hashSet0 = this.d;
        hashSet0.add(a$a0.b);
        int v1 = a$a0.a;
        if(a$a0.b + 2 > v1) {
            return;
        }
        int v2 = a$a0.readShort();
        if(v2 * 12 + a$a0.b <= v1 && v2 > 0) {
            int v3 = 0;
            while(true) {
                HashMap[] arr_hashMap = this.c;
                if(v3 >= v2) {
                    break;
                }
                int v4 = a$a0.readUnsignedShort();
                int v5 = a$a0.readUnsignedShort();
                int v6 = a$a0.readInt();
                long v7 = ((long)a$a0.b) + 4L;
                android.supportv1.d.a.c a$c0 = (android.supportv1.d.a.c)a.k[v].get(v4);
                if(a$c0 == null || v5 <= 0) {
                    v10 = v2;
                    v11 = v4;
                    v12 = 0L;
                }
                else {
                    int[] arr_v = a.s;
                    if(v5 < arr_v.length) {
                        int v8 = a$c0.c;
                        if(v8 == 7 || v5 == 7) {
                            v10 = v2;
                        }
                        else if(v8 != v5) {
                            int v9 = a$c0.d;
                            if(v9 == v5) {
                                v10 = v2;
                            }
                            else {
                                v10 = v2;
                                if((v8 != 4 && v9 != 4 || v5 != 3) && (v8 != 9 && v9 != 9 || v5 != 8) && (v8 != 12 && v9 != 12 || v5 != 11)) {
                                    String s = a.r[v5];
                                    v11 = v4;
                                    v12 = 0L;
                                    goto label_48;
                                }
                            }
                        }
                        else {
                            v10 = v2;
                        }
                        if(v5 == 7) {
                            v5 = v8;
                        }
                        v11 = v4;
                        v12 = ((long)v6) * ((long)arr_v[v5]);
                        if(v12 >= 0L && v12 <= 0x7FFFFFFFL) {
                            z = true;
                            goto label_49;
                        }
                    }
                    else {
                        v10 = v2;
                        v11 = v4;
                        v12 = 0L;
                    }
                }
            label_48:
                z = false;
            label_49:
                if(z) {
                    if(v12 > 4L) {
                        int v13 = a$a0.readInt();
                        v14 = v7;
                        int v15 = this.b;
                        if(v15 == 7) {
                            if("MakerNote".equals(a$c0.b)) {
                                this.g = v13;
                            }
                            else if(v == 6 && "ThumbnailImage".equals(a$c0.b)) {
                                this.h = v13;
                                this.i = v6;
                                b a$b0 = b.a(6, this.e);
                                v16 = v5;
                                v17 = v6;
                                b a$b1 = b.b(this.h, this.e);
                                b a$b2 = b.b(this.i, this.e);
                                arr_hashMap[4].put("Compression", a$b0);
                                arr_hashMap[4].put("JPEGInterchangeFormat", a$b1);
                                arr_hashMap[4].put("JPEGInterchangeFormatLength", a$b2);
                                goto label_77;
                            }
                            v16 = v5;
                            v17 = v6;
                        }
                        else {
                            v16 = v5;
                            v17 = v6;
                            if(v15 == 10 && "JpgFromRaw".equals(a$c0.b)) {
                                this.j = v13;
                            }
                        }
                    label_77:
                        arr_hashMap1 = arr_hashMap;
                        if(((long)v13) + v12 <= ((long)v1)) {
                            a$a0.a(((long)v13));
                            goto label_87;
                        }
                        else {
                            a$a0.a(v14);
                            goto label_117;
                        }
                        goto label_83;
                    }
                    else {
                    label_83:
                        v14 = v7;
                        arr_hashMap1 = arr_hashMap;
                        v16 = v5;
                        v17 = v6;
                    }
                label_87:
                    Integer integer0 = (Integer)a.n.get(v11);
                    if(integer0 == null) {
                        byte[] arr_b = new byte[((int)v12)];
                        a$a0.readFully(arr_b);
                        b a$b3 = new b(v16, arr_b, v17);
                        arr_hashMap1[v].put(a$c0.b, a$b3);
                        String s1 = a$c0.b;
                        if("DNGVersion".equals(s1)) {
                            this.b = 3;
                        }
                        if(("Make".equals(s1) || "Model".equals(s1)) && a$b3.g(this.e).contains("PENTAX") || "Compression".equals(s1) && a$b3.f(this.e) == 0xFFFF) {
                            this.b = 8;
                        }
                        if(((long)a$a0.b) != v14) {
                            a$a0.a(v14);
                        }
                    }
                    else {
                        switch(v16) {
                            case 3: {
                                v18 = (long)a$a0.readUnsignedShort();
                                break;
                            }
                            case 4: {
                                v18 = ((long)a$a0.readInt()) & 0xFFFFFFFFL;
                                break;
                            }
                            case 8: {
                                v18 = (long)a$a0.readShort();
                                break;
                            }
                            case 9: 
                            case 13: {
                                v18 = (long)a$a0.readInt();
                                break;
                            }
                            default: {
                                v18 = -1L;
                            }
                        }
                        if(v18 > 0L && v18 < ((long)v1) && !hashSet0.contains(((int)v18))) {
                            a$a0.a(v18);
                            this.l(a$a0, ((int)integer0));
                        }
                        a$a0.a(v14);
                    }
                }
                else {
                    a$a0.a(v7);
                }
            label_117:
                v3 = (short)(v3 + 1);
                v2 = v10;
            }
            if(a$a0.b + 4 <= v1) {
                int v19 = a$a0.readInt();
                if(((long)v19) <= 0L || v19 >= v1) {
                    stringBuilder0 = new StringBuilder();
                    stringBuilder0.append("Stop reading file since a wrong offset may cause an infinite loop: ");
                    stringBuilder0.append(v19);
                }
                else {
                    if(hashSet0.contains(v19)) {
                        stringBuilder0 = new StringBuilder();
                        stringBuilder0.append("Stop reading file since re-reading an IFD may cause an infinite loop: ");
                        stringBuilder0.append(v19);
                        return;
                    }
                    int v20 = 5;
                    a$a0.a(((long)v19));
                    boolean z1 = false;
                    if(arr_hashMap[4].isEmpty()) {
                        z1 = true;
                        v20 = 4;
                    }
                    else if(arr_hashMap[5].isEmpty()) {
                        z1 = true;
                    }
                    if(z1) {
                        this.l(a$a0, v20);
                    }
                }
            }
        }
    }

    public final boolean m(HashMap hashMap0) {
        b a$b0 = (b)hashMap0.get("ImageLength");
        b a$b1 = (b)hashMap0.get("ImageWidth");
        return a$b0 != null && a$b1 != null && (a$b0.f(this.e) <= 0x200 && a$b1.f(this.e) <= 0x200);
    }

    public final void n(android.supportv1.d.a.a a$a0) {
        this.f(a$a0);
        HashMap[] arr_hashMap = this.c;
        b a$b0 = (b)arr_hashMap[1].get("MakerNote");
        if(a$b0 != null) {
            android.supportv1.d.a.a a$a1 = new android.supportv1.d.a.a(a$b0.c);
            a$a1.d = this.e;
            byte[] arr_b = new byte[a.z.length];
            a$a1.readFully(arr_b);
            a$a1.a(0L);
            byte[] arr_b1 = a.A;
            byte[] arr_b2 = new byte[arr_b1.length];
            a$a1.readFully(arr_b2);
            if(Arrays.equals(arr_b, a.z)) {
                a$a1.a(8L);
            }
            else if(Arrays.equals(arr_b2, arr_b1)) {
                a$a1.a(12L);
            }
            this.l(a$a1, 6);
            b a$b1 = (b)arr_hashMap[7].get("PreviewImageStart");
            b a$b2 = (b)arr_hashMap[7].get("PreviewImageLength");
            if(a$b1 != null && a$b2 != null) {
                arr_hashMap[5].put("JPEGInterchangeFormat", a$b1);
                arr_hashMap[5].put("JPEGInterchangeFormatLength", a$b2);
            }
            b a$b3 = (b)arr_hashMap[8].get("AspectFrame");
            if(a$b3 != null) {
                int[] arr_v = (int[])a$b3.d(this.e);
                if(arr_v == null || arr_v.length != 4) {
                    Arrays.toString(arr_v);
                }
                else {
                    int v = arr_v[2];
                    int v1 = arr_v[0];
                    if(v > v1) {
                        int v2 = arr_v[3];
                        int v3 = arr_v[1];
                        if(v2 > v3) {
                            int v4 = v - v1 + 1;
                            int v5 = v2 - v3 + 1;
                            if(v4 < v5) {
                                int v6 = v4 + v5;
                                v5 = v6 - v5;
                                v4 = v6 - v5;
                            }
                            b a$b4 = b.a(v4, this.e);
                            b a$b5 = b.a(v5, this.e);
                            arr_hashMap[0].put("ImageWidth", a$b4);
                            arr_hashMap[0].put("ImageLength", a$b5);
                        }
                    }
                }
            }
        }
    }

    public final void o(android.supportv1.d.a.a a$a0) {
        this.f(a$a0);
        HashMap[] arr_hashMap = this.c;
        if(((b)arr_hashMap[0].get("JpgFromRaw")) != null) {
            this.h(a$a0, this.j, 5);
        }
        b a$b0 = (b)arr_hashMap[0].get("ISO");
        if(a$b0 != null && ((b)arr_hashMap[1].get("PhotographicSensitivity")) == null) {
            arr_hashMap[1].put("PhotographicSensitivity", a$b0);
        }
    }

    public final void p(android.supportv1.d.a.a a$a0, int v) {
        b a$b6;
        b a$b5;
        HashMap[] arr_hashMap = this.c;
        b a$b0 = (b)arr_hashMap[v].get("DefaultCropSize");
        b a$b1 = (b)arr_hashMap[v].get("SensorTopBorder");
        b a$b2 = (b)arr_hashMap[v].get("SensorLeftBorder");
        b a$b3 = (b)arr_hashMap[v].get("SensorBottomBorder");
        b a$b4 = (b)arr_hashMap[v].get("SensorRightBorder");
        if(a$b0 != null) {
            if(a$b0.a == 5) {
                d[] arr_a$d = (d[])a$b0.d(this.e);
                if(arr_a$d != null && arr_a$d.length == 2) {
                    a$b5 = b.c(arr_a$d[0], this.e);
                    a$b6 = b.c(arr_a$d[1], this.e);
                    arr_hashMap[v].put("ImageWidth", a$b5);
                    arr_hashMap[v].put("ImageLength", a$b6);
                    return;
                }
                Arrays.toString(arr_a$d);
                return;
            }
            int[] arr_v = (int[])a$b0.d(this.e);
            if(arr_v != null && arr_v.length == 2) {
                a$b5 = b.a(arr_v[0], this.e);
                a$b6 = b.a(arr_v[1], this.e);
                arr_hashMap[v].put("ImageWidth", a$b5);
                arr_hashMap[v].put("ImageLength", a$b6);
                return;
            }
            Arrays.toString(arr_v);
            return;
        }
        if(a$b1 != null && a$b2 != null && a$b3 != null && a$b4 != null) {
            int v1 = a$b1.f(this.e);
            int v2 = a$b3.f(this.e);
            int v3 = a$b4.f(this.e);
            int v4 = a$b2.f(this.e);
            if(v2 > v1 && v3 > v4) {
                b a$b7 = b.a(v2 - v1, this.e);
                b a$b8 = b.a(v3 - v4, this.e);
                arr_hashMap[v].put("ImageLength", a$b7);
                arr_hashMap[v].put("ImageWidth", a$b8);
            }
        }
        else if(((b)arr_hashMap[v].get("ImageLength")) == null || ((b)arr_hashMap[v].get("ImageWidth")) == null) {
            b a$b9 = (b)arr_hashMap[v].get("JPEGInterchangeFormat");
            if(a$b9 != null) {
                this.h(a$a0, a$b9.f(this.e), v);
            }
        }
    }

    public static ByteOrder q(android.supportv1.d.a.a a$a0) {
        int v = a$a0.readShort();
        switch(v) {
            case 0x4949: {
                return ByteOrder.LITTLE_ENDIAN;
            }
            case 0x4D4D: {
                return ByteOrder.BIG_ENDIAN;
            }
            default: {
                throw new IOException("Invalid byte order: " + Integer.toHexString(v));
            }
        }
    }

    public final void r(android.supportv1.d.a.a a$a0) {
        HashMap hashMap0 = this.c[4];
        b a$b0 = (b)hashMap0.get("Compression");
        if(a$b0 == null) {
        label_44:
            b a$b5 = (b)hashMap0.get("JPEGInterchangeFormat");
            b a$b6 = (b)hashMap0.get("JPEGInterchangeFormatLength");
            if(a$b5 != null && a$b6 != null) {
                int v9 = a$b5.f(this.e);
                int v10 = Math.min(a$b6.f(this.e), a$a0.c.available() - v9);
                switch(this.b) {
                    case 7: {
                        v9 += this.g;
                        break;
                    }
                    case 4: 
                    case 9: 
                    case 10: {
                        v9 += this.f;
                    }
                }
                if(v9 > 0 && v10 > 0 && this.a == null) {
                    a$a0.a(((long)v9));
                    a$a0.readFully(new byte[v10]);
                }
            }
        }
        else {
            switch(a$b0.f(this.e)) {
                case 6: {
                    goto label_44;
                }
                case 1: 
                case 7: {
                    b a$b1 = (b)hashMap0.get("BitsPerSample");
                    if(a$b1 != null) {
                        int[] arr_v = (int[])a$b1.d(this.e);
                        int[] arr_v1 = a.o;
                        if(Arrays.equals(arr_v1, arr_v)) {
                        label_15:
                            b a$b3 = (b)hashMap0.get("StripOffsets");
                            b a$b4 = (b)hashMap0.get("StripByteCounts");
                            if(a$b3 != null && a$b4 != null) {
                                long[] arr_v2 = a.i(a$b3.d(this.e));
                                long[] arr_v3 = a.i(a$b4.d(this.e));
                                if(arr_v2 != null && arr_v3 != null) {
                                    long v1 = 0L;
                                    for(int v2 = 0; v2 < arr_v3.length; ++v2) {
                                        v1 += arr_v3[v2];
                                    }
                                    byte[] arr_b = new byte[((int)v1)];
                                    int v4 = 0;
                                    int v5 = 0;
                                    for(int v3 = 0; v3 < arr_v2.length; ++v3) {
                                        int v6 = (int)arr_v2[v3];
                                        int v7 = (int)arr_v3[v3];
                                        int v8 = v6 - v4;
                                        a$a0.a(((long)v8));
                                        byte[] arr_b1 = new byte[v7];
                                        a$a0.read(arr_b1);
                                        v4 = v4 + v8 + v7;
                                        System.arraycopy(arr_b1, 0, arr_b, v5, v7);
                                        v5 += v7;
                                    }
                                    return;
                                }
                            }
                        }
                        else if(this.b == 3) {
                            b a$b2 = (b)hashMap0.get("PhotometricInterpretation");
                            if(a$b2 != null) {
                                int v = a$b2.f(this.e);
                                if(v == 1 && Arrays.equals(arr_v, a.p) || v == 6 && Arrays.equals(arr_v, arr_v1)) {
                                    goto label_15;
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }
    }
}


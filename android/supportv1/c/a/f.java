package android.supportv1.c.a;

import android.animation.TypeEvaluator;

public class f implements TypeEvaluator {
    public static final f a;

    static {
        f.a = new f();  // 初始化器: Ljava/lang/Object;-><init>()V
    }

    @Override  // android.animation.TypeEvaluator
    public final Object evaluate(float f, Object object0, Object object1) {
        int v = (int)(((Integer)object0));
        float f1 = ((float)(v >> 24 & 0xFF)) / 255.0f;
        int v1 = (int)(((Integer)object1));
        float f2 = (float)Math.pow(((float)(v >> 16 & 0xFF)) / 255.0f, 2.2);
        float f3 = (float)Math.pow(((float)(v >> 8 & 0xFF)) / 255.0f, 2.2);
        float f4 = (float)Math.pow(((float)(v & 0xFF)) / 255.0f, 2.2);
        return (int)(Math.round(((((float)(v1 >> 24 & 0xFF)) / 255.0f - f1) * f + f1) * 255.0f) << 24 | Math.round(((float)Math.pow((((float)Math.pow(((float)(v1 >> 16 & 0xFF)) / 255.0f, 2.2)) - f2) * f + f2, 0.454545)) * 255.0f) << 16 | Math.round(((float)Math.pow((((float)Math.pow(((float)(v1 >> 8 & 0xFF)) / 255.0f, 2.2)) - f3) * f + f3, 0.454545)) * 255.0f) << 8 | Math.round(((float)Math.pow((((float)Math.pow(((float)(v1 & 0xFF)) / 255.0f, 2.2)) - f4) * f + f4, 0.454545)) * 255.0f));
    }
}


package android.supportv1.c.a;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.animation.AnimatorSet;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources.NotFoundException;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.PorterDuff.Mode;
import android.graphics.Rect;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.AnimatedVectorDrawable;
import android.graphics.drawable.Drawable.Callback;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.supportv1.v4.content.res.ResourcesCompat;
import android.supportv1.v4.content.res.TypedArrayUtils;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.supportv1.v4.util.ArrayMap;
import android.util.AttributeSet;
import android.util.Xml;
import java.io.IOException;
import java.util.ArrayList;
import jeb.synthetic.TWR;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

public class c extends h implements Animatable {
    static class a extends Drawable.ConstantState {
        public i a;
        public AnimatorSet b;
        public ArrayList c;
        public ArrayMap d;

        public a(Drawable.Callback drawable$Callback0) {
        }

        public final void a() {
            if(this.b == null) {
                this.b = new AnimatorSet();
            }
            this.b.playTogether(this.c);
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final int getChangingConfigurations() {
            return 0;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable() {
            throw new IllegalStateException("No constant state support for SDK < 24.");
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable(Resources resources0) {
            throw new IllegalStateException("No constant state support for SDK < 24.");
        }
    }

    static class b extends Drawable.ConstantState {
        public final Drawable.ConstantState a;

        public b(Drawable.ConstantState drawable$ConstantState0) {
            this.a = drawable$ConstantState0;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final boolean canApplyTheme() {
            return this.a.canApplyTheme();
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final int getChangingConfigurations() {
            return this.a.getChangingConfigurations();
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable() {
            Drawable drawable0 = new c(null);
            Drawable drawable1 = this.a.newDrawable();
            drawable0.a = drawable1;
            drawable1.setCallback(drawable0.b);
            return drawable0;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable(Resources resources0) {
            Drawable drawable0 = new c(null);
            Drawable drawable1 = this.a.newDrawable(resources0);
            drawable0.a = drawable1;
            drawable1.setCallback(drawable0.b);
            return drawable0;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable(Resources resources0, Resources.Theme resources$Theme0) {
            Drawable drawable0 = new c(null);
            Drawable drawable1 = this.a.newDrawable(resources0, resources$Theme0);
            drawable0.a = drawable1;
            drawable1.setCallback(drawable0.b);
            return drawable0;
        }
    }

    public final Drawable.Callback b;
    public final a c;
    public final Context d;

    public c(Context context0) {
        android.supportv1.c.a.c.1 c$10 = new Drawable.Callback() {
            public final c a;

            {
                this.a = c0;
            }

            @Override  // android.graphics.drawable.Drawable$Callback
            public final void invalidateDrawable(Drawable drawable0) {
                this.a.invalidateSelf();
            }

            @Override  // android.graphics.drawable.Drawable$Callback
            public final void scheduleDrawable(Drawable drawable0, Runnable runnable0, long v) {
                this.a.scheduleSelf(runnable0, v);
            }

            @Override  // android.graphics.drawable.Drawable$Callback
            public final void unscheduleDrawable(Drawable drawable0, Runnable runnable0) {
                this.a.unscheduleSelf(runnable0);
            }
        };
        this.b = c$10;
        this.d = context0;
        this.c = new a(c$10);
    }

    public static c a(Context context0, Resources.Theme resources$Theme0, Resources resources0, AttributeSet attributeSet0, XmlResourceParser xmlResourceParser0) {
        c c0 = new c(context0);
        c0.inflate(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
        return c0;
    }

    @Override  // android.supportv1.c.a.h
    public final void applyTheme(Resources.Theme resources$Theme0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.applyTheme(drawable0, resources$Theme0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean canApplyTheme() {
        return this.a == null ? false : DrawableCompat.canApplyTheme(this.a);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void draw(Canvas canvas0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.draw(canvas0);
            return;
        }
        this.c.a.draw(canvas0);
        if(this.c.b.isStarted()) {
            this.invalidateSelf();
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getAlpha() {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.a.getAlpha() : DrawableCompat.getAlpha(drawable0);
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getChangingConfigurations() {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            return drawable0.getChangingConfigurations();
        }
        int v = super.getChangingConfigurations();
        this.c.getClass();
        return v;
    }

    @Override  // android.graphics.drawable.Drawable
    public final Drawable.ConstantState getConstantState() {
        return this.a != null && Build.VERSION.SDK_INT >= 24 ? new b(this.a.getConstantState()) : null;
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getIntrinsicHeight() {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.a.getIntrinsicHeight() : drawable0.getIntrinsicHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getIntrinsicWidth() {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.a.getIntrinsicWidth() : drawable0.getIntrinsicWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getOpacity() {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.a.getOpacity() : drawable0.getOpacity();
    }

    @Override  // android.graphics.drawable.Drawable
    public final void inflate(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0) {
        this.inflate(resources0, xmlPullParser0, attributeSet0, null);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void inflate(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
        Animator animator0;
        i i0;
        TypedArray typedArray0;
        a c$a0;
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.inflate(drawable0, resources0, xmlPullParser0, attributeSet0, resources$Theme0);
            return;
        }
        int v = xmlPullParser0.getEventType();
        int v1 = xmlPullParser0.getDepth();
        while(true) {
            c$a0 = this.c;
            if(v == 1 || xmlPullParser0.getDepth() < v1 + 1 && v == 3) {
                break;
            }
            if(v == 2) {
                String s = xmlPullParser0.getName();
                XmlResourceParser xmlResourceParser0 = null;
                if("animated-vector".equals(s)) {
                    typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, android.supportv1.c.a.a.e);
                    int v2 = typedArray0.getResourceId(0, 0);
                    if(v2 != 0) {
                        if(Build.VERSION.SDK_INT >= 24) {
                            i0 = new i();
                            i0.a = ResourcesCompat.getDrawable(resources0, v2, resources$Theme0);
                            new android.supportv1.c.a.i.h(i0.a.getConstantState());
                        }
                        else {
                            try {
                                XmlResourceParser xmlResourceParser1 = resources0.getXml(v2);
                                AttributeSet attributeSet1 = Xml.asAttributeSet(xmlResourceParser1);
                                do {
                                    int v3 = xmlResourceParser1.next();
                                }
                                while(v3 != 1 && v3 != 2);
                                if(v3 == 2) {
                                    i0 = i.b(resources0, xmlResourceParser1, attributeSet1, resources$Theme0);
                                    goto label_28;
                                }
                            }
                            catch(XmlPullParserException | IOException unused_ex) {
                            }
                            i0 = null;
                        }
                    label_28:
                        i0.f = false;
                        i0.setCallback(this.b);
                        i i1 = c$a0.a;
                        if(i1 != null) {
                            i1.setCallback(null);
                        }
                        c$a0.a = i0;
                    }
                    typedArray0.recycle();
                }
                else if("target".equals(s)) {
                    typedArray0 = resources0.obtainAttributes(attributeSet0, android.supportv1.c.a.a.f);
                    String s1 = typedArray0.getString(0);
                    int v4 = typedArray0.getResourceId(1, 0);
                    if(v4 != 0) {
                        Context context0 = this.d;
                        if(context0 != null) {
                            if(Build.VERSION.SDK_INT >= 24) {
                                animator0 = AnimatorInflater.loadAnimator(context0, v4);
                            }
                            else {
                                Resources resources1 = context0.getResources();
                                Resources.Theme resources$Theme1 = context0.getTheme();
                                try {
                                    try {
                                        xmlResourceParser0 = resources1.getAnimation(v4);
                                        animator0 = e.a(context0, resources1, resources$Theme1, xmlResourceParser0, Xml.asAttributeSet(xmlResourceParser0), null, 0);
                                    }
                                    catch(XmlPullParserException xmlPullParserException0) {
                                        Resources.NotFoundException resources$NotFoundException0 = new Resources.NotFoundException("Can\'t load animation resource ID #0x" + Integer.toHexString(v4));
                                        resources$NotFoundException0.initCause(xmlPullParserException0);
                                        throw resources$NotFoundException0;
                                    }
                                    catch(IOException iOException0) {
                                        Resources.NotFoundException resources$NotFoundException1 = new Resources.NotFoundException("Can\'t load animation resource ID #0x" + Integer.toHexString(v4));
                                        resources$NotFoundException1.initCause(iOException0);
                                        throw resources$NotFoundException1;
                                    }
                                }
                                catch(Throwable throwable0) {
                                    TWR.safeClose$NT(xmlResourceParser0, throwable0);
                                    throw throwable0;
                                }
                                xmlResourceParser0.close();
                            }
                            animator0.setTarget(c$a0.a.c(s1));
                            if(c$a0.c == null) {
                                c$a0.c = new ArrayList();
                                c$a0.d = new ArrayMap();
                            }
                            c$a0.c.add(animator0);
                            c$a0.d.put(animator0, s1);
                            goto label_72;
                        }
                        typedArray0.recycle();
                        throw new IllegalStateException("Context can\'t be null when inflating animators");
                    }
                label_72:
                    typedArray0.recycle();
                }
            }
            v = xmlPullParser0.next();
        }
        c$a0.a();
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean isAutoMirrored() {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.a.isAutoMirrored() : DrawableCompat.isAutoMirrored(drawable0);
    }

    @Override  // android.graphics.drawable.Animatable
    public final boolean isRunning() {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.b.isRunning() : ((AnimatedVectorDrawable)drawable0).isRunning();
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean isStateful() {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.a.isStateful() : drawable0.isStateful();
    }

    @Override  // android.graphics.drawable.Drawable
    public final Drawable mutate() {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.mutate();
        }
        return this;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void onBoundsChange(Rect rect0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.setBounds(rect0);
            return;
        }
        this.c.a.setBounds(rect0);
    }

    @Override  // android.supportv1.c.a.h
    public final boolean onLevelChange(int v) {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.a.setLevel(v) : drawable0.setLevel(v);
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean onStateChange(int[] arr_v) {
        Drawable drawable0 = this.a;
        return drawable0 == null ? this.c.a.setState(arr_v) : drawable0.setState(arr_v);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setAlpha(int v) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.setAlpha(v);
            return;
        }
        this.c.a.setAlpha(v);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setAutoMirrored(boolean z) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.setAutoMirrored(drawable0, z);
            return;
        }
        this.c.a.setAutoMirrored(z);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setColorFilter(ColorFilter colorFilter0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.setColorFilter(colorFilter0);
            return;
        }
        this.c.a.setColorFilter(colorFilter0);
    }

    @Override  // android.graphics.drawable.Drawable, android.supportv1.v4.graphics.drawable.TintAwareDrawable
    public final void setTint(int v) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.setTint(drawable0, v);
            return;
        }
        this.c.a.setTint(v);
    }

    @Override  // android.graphics.drawable.Drawable, android.supportv1.v4.graphics.drawable.TintAwareDrawable
    public final void setTintList(ColorStateList colorStateList0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.setTintList(drawable0, colorStateList0);
            return;
        }
        this.c.a.setTintList(colorStateList0);
    }

    @Override  // android.graphics.drawable.Drawable, android.supportv1.v4.graphics.drawable.TintAwareDrawable
    public final void setTintMode(PorterDuff.Mode porterDuff$Mode0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.setTintMode(drawable0, porterDuff$Mode0);
            return;
        }
        this.c.a.setTintMode(porterDuff$Mode0);
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean setVisible(boolean z, boolean z1) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            return drawable0.setVisible(z, z1);
        }
        this.c.a.setVisible(z, z1);
        return super.setVisible(z, z1);
    }

    @Override  // android.graphics.drawable.Animatable
    public final void start() {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            ((AnimatedVectorDrawable)drawable0).start();
            return;
        }
        a c$a0 = this.c;
        if(c$a0.b.isStarted()) {
            return;
        }
        c$a0.b.start();
        this.invalidateSelf();
    }

    @Override  // android.graphics.drawable.Animatable
    public final void stop() {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            ((AnimatedVectorDrawable)drawable0).stop();
            return;
        }
        this.c.b.end();
    }
}


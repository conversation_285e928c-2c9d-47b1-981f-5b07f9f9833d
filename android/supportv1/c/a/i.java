package android.supportv1.c.a;

import android.content.res.ColorStateList;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.Bitmap.Config;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Matrix;
import android.graphics.Paint.Cap;
import android.graphics.Paint.Join;
import android.graphics.Paint.Style;
import android.graphics.Paint;
import android.graphics.Path.FillType;
import android.graphics.Path;
import android.graphics.PathMeasure;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Rect;
import android.graphics.Shader;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.VectorDrawable;
import android.os.Build.VERSION;
import android.supportv1.v4.content.res.ComplexColorCompat;
import android.supportv1.v4.content.res.TypedArrayUtils;
import android.supportv1.v4.graphics.PathParser.PathDataNode;
import android.supportv1.v4.graphics.PathParser;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.supportv1.v4.util.ArrayMap;
import android.util.AttributeSet;
import java.util.ArrayDeque;
import java.util.ArrayList;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

public class i extends h {
    static class a extends e {
        public a(a i$a0) {
            super(i$a0);
        }

        public final void d(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            if(!TypedArrayUtils.hasAttribute(xmlPullParser0, "pathData")) {
                return;
            }
            TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, android.supportv1.c.a.a.d);
            String s = typedArray0.getString(0);
            if(s != null) {
                this.b = s;
            }
            String s1 = typedArray0.getString(1);
            if(s1 != null) {
                this.a = PathParser.createNodesFromPathData(s1);
            }
            typedArray0.recycle();
        }
    }

    static class b extends e {
        public ComplexColorCompat d;
        public float e;
        public ComplexColorCompat f;
        public float g;
        public int h;
        public float i;
        public float j;
        public float k;
        public float l;
        public Paint.Cap m;
        public Paint.Join n;
        public float o;

        public b() {
            this.e = 0.0f;
            this.g = 1.0f;
            this.h = 0;
            this.i = 1.0f;
            this.j = 0.0f;
            this.k = 1.0f;
            this.l = 0.0f;
            this.m = Paint.Cap.BUTT;
            this.n = Paint.Join.MITER;
            this.o = 4.0f;
        }

        public b(b i$b0) {
            super(i$b0);
            this.d = i$b0.d;
            this.e = i$b0.e;
            this.g = i$b0.g;
            this.f = i$b0.f;
            this.h = i$b0.h;
            this.i = i$b0.i;
            this.j = i$b0.j;
            this.k = i$b0.k;
            this.l = i$b0.l;
            this.m = i$b0.m;
            this.n = i$b0.n;
            this.o = i$b0.o;
        }

        @Override  // android.supportv1.c.a.i$d
        public final boolean a(int[] arr_v) {
            boolean z = this.f.onStateChanged(arr_v);
            return this.d.onStateChanged(arr_v) | z;
        }

        // 去混淆评级： 低(20)
        @Override  // android.supportv1.c.a.i$d
        public final boolean b() {
            return this.f.isStateful() || this.d.isStateful();
        }

        public final void d(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, android.supportv1.c.a.a.c);
            if(TypedArrayUtils.hasAttribute(xmlPullParser0, "pathData")) {
                String s = typedArray0.getString(0);
                if(s != null) {
                    this.b = s;
                }
                String s1 = typedArray0.getString(2);
                if(s1 != null) {
                    this.a = PathParser.createNodesFromPathData(s1);
                }
                this.f = TypedArrayUtils.getNamedComplexColor(typedArray0, xmlPullParser0, resources$Theme0, "fillColor", 1, 0);
                this.i = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "fillAlpha", 12, this.i);
                int v = TypedArrayUtils.getNamedInt(typedArray0, xmlPullParser0, "strokeLineCap", 8, -1);
                Paint.Cap paint$Cap0 = this.m;
                switch(v) {
                    case 0: {
                        paint$Cap0 = Paint.Cap.BUTT;
                        break;
                    }
                    case 1: {
                        paint$Cap0 = Paint.Cap.ROUND;
                        break;
                    }
                    case 2: {
                        paint$Cap0 = Paint.Cap.SQUARE;
                    }
                }
                this.m = paint$Cap0;
                int v1 = TypedArrayUtils.getNamedInt(typedArray0, xmlPullParser0, "strokeLineJoin", 9, -1);
                Paint.Join paint$Join0 = this.n;
                switch(v1) {
                    case 0: {
                        paint$Join0 = Paint.Join.MITER;
                        break;
                    }
                    case 1: {
                        paint$Join0 = Paint.Join.ROUND;
                        break;
                    }
                    case 2: {
                        paint$Join0 = Paint.Join.BEVEL;
                    }
                }
                this.n = paint$Join0;
                this.o = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "strokeMiterLimit", 10, this.o);
                this.d = TypedArrayUtils.getNamedComplexColor(typedArray0, xmlPullParser0, resources$Theme0, "strokeColor", 3, 0);
                this.g = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "strokeAlpha", 11, this.g);
                this.e = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "strokeWidth", 4, this.e);
                this.k = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "trimPathEnd", 6, this.k);
                this.l = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "trimPathOffset", 7, this.l);
                this.j = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "trimPathStart", 5, this.j);
                this.h = TypedArrayUtils.getNamedInt(typedArray0, xmlPullParser0, "fillType", 13, this.h);
            }
            typedArray0.recycle();
        }
    }

    static class c extends d {
        public final Matrix a;
        public final ArrayList b;
        public float c;
        public final Matrix d;
        public final int e;
        public float f;
        public float g;
        public float h;
        public float i;
        public float j;
        public float k;
        public String l;

        public c() {
            this.a = new Matrix();
            this.b = new ArrayList();
            this.c = 0.0f;
            this.f = 0.0f;
            this.g = 0.0f;
            this.h = 1.0f;
            this.i = 1.0f;
            this.j = 0.0f;
            this.k = 0.0f;
            this.d = new Matrix();
            this.l = null;
        }

        public c(c i$c0, ArrayMap arrayMap0) {
            b i$b0;
            this.a = new Matrix();
            this.b = new ArrayList();
            this.c = 0.0f;
            this.f = 0.0f;
            this.g = 0.0f;
            this.h = 1.0f;
            this.i = 1.0f;
            this.j = 0.0f;
            this.k = 0.0f;
            Matrix matrix0 = new Matrix();
            this.d = matrix0;
            this.c = i$c0.c;
            this.f = i$c0.f;
            this.g = i$c0.g;
            this.h = i$c0.h;
            this.i = i$c0.i;
            this.j = i$c0.j;
            this.k = i$c0.k;
            String s = i$c0.l;
            this.l = s;
            this.e = i$c0.e;
            if(s != null) {
                arrayMap0.put(s, this);
            }
            matrix0.set(i$c0.d);
            ArrayList arrayList0 = i$c0.b;
            for(int v = 0; v < arrayList0.size(); ++v) {
                Object object0 = arrayList0.get(v);
                if(object0 instanceof c) {
                    c i$c1 = new c(((c)object0), arrayMap0);
                    this.b.add(i$c1);
                }
                else {
                    if(object0 instanceof b) {
                        i$b0 = new b(((b)object0));
                    }
                    else {
                        if(!(object0 instanceof a)) {
                            throw new IllegalStateException("Unknown object in the tree!");
                        }
                        i$b0 = new a(((a)object0));
                    }
                    this.b.add(i$b0);
                    String s1 = i$b0.b;
                    if(s1 != null) {
                        arrayMap0.put(s1, i$b0);
                    }
                }
            }
        }

        @Override  // android.supportv1.c.a.i$d
        public final boolean a(int[] arr_v) {
            boolean z = false;
            for(int v = 0; true; ++v) {
                ArrayList arrayList0 = this.b;
                if(v >= arrayList0.size()) {
                    break;
                }
                z |= ((d)arrayList0.get(v)).a(arr_v);
            }
            return z;
        }

        @Override  // android.supportv1.c.a.i$d
        public final boolean b() {
            for(int v = 0; true; ++v) {
                ArrayList arrayList0 = this.b;
                if(v >= arrayList0.size()) {
                    break;
                }
                if(((d)arrayList0.get(v)).b()) {
                    return true;
                }
            }
            return false;
        }

        public final void c(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, android.supportv1.c.a.a.b);
            this.c = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "rotation", 5, this.c);
            this.f = typedArray0.getFloat(1, this.f);
            this.g = typedArray0.getFloat(2, this.g);
            this.h = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "scaleX", 3, this.h);
            this.i = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "scaleY", 4, this.i);
            this.j = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "translateX", 6, this.j);
            this.k = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "translateY", 7, this.k);
            String s = typedArray0.getString(0);
            if(s != null) {
                this.l = s;
            }
            this.d.reset();
            this.d.postTranslate(-this.f, -this.g);
            this.d.postScale(this.h, this.i);
            this.d.postRotate(this.c, 0.0f, 0.0f);
            this.d.postTranslate(this.j + this.f, this.k + this.g);
            typedArray0.recycle();
        }

        public final String d() {
            return this.l;
        }
    }

    static abstract class d {
        public boolean a(int[] arr_v) {
            return false;
        }

        public boolean b() {
            return false;
        }
    }

    static abstract class e extends d {
        public PathDataNode[] a;
        public String b;
        public final int c;

        public e() {
            this.a = null;
        }

        public e(e i$e0) {
            this.b = i$e0.b;
            this.c = i$e0.c;
            this.a = PathParser.deepCopyNodes(i$e0.a);
        }

        public final String c() {
            return this.b;
        }
    }

    static class f {
        public Paint a;
        public Paint b;
        public final c c;
        public float d;
        public float e;
        public float f;
        public float g;
        public int h;
        public String i;
        public Boolean j;
        public final ArrayMap k;
        public final Path l;
        public final Path m;
        public final Matrix n;
        public PathMeasure o;
        public static final Matrix p;

        static {
            f.p = new Matrix();
        }

        public f() {
            this.n = new Matrix();
            this.d = 0.0f;
            this.e = 0.0f;
            this.f = 0.0f;
            this.g = 0.0f;
            this.h = 0xFF;
            this.i = null;
            this.j = null;
            this.k = new ArrayMap();
            this.c = new c();
            this.l = new Path();
            this.m = new Path();
        }

        public f(f i$f0) {
            this.n = new Matrix();
            this.d = 0.0f;
            this.e = 0.0f;
            this.f = 0.0f;
            this.g = 0.0f;
            this.h = 0xFF;
            this.i = null;
            this.j = null;
            ArrayMap arrayMap0 = new ArrayMap();
            this.k = arrayMap0;
            this.c = new c(i$f0.c, arrayMap0);
            this.l = new Path(i$f0.l);
            this.m = new Path(i$f0.m);
            this.d = i$f0.d;
            this.e = i$f0.e;
            this.f = i$f0.f;
            this.g = i$f0.g;
            this.h = i$f0.h;
            this.i = i$f0.i;
            String s = i$f0.i;
            if(s != null) {
                arrayMap0.put(s, this);
            }
            this.j = i$f0.j;
        }

        public final void a(c i$c0, Matrix matrix0, Canvas canvas0, int v, int v1) {
            i$c0.a.set(matrix0);
            Matrix matrix1 = i$c0.a;
            matrix1.preConcat(i$c0.d);
            canvas0.save();
            for(int v2 = 0; true; ++v2) {
                ArrayList arrayList0 = i$c0.b;
                if(v2 >= arrayList0.size()) {
                    break;
                }
                d i$d0 = (d)arrayList0.get(v2);
                if(i$d0 instanceof c) {
                    this.a(((c)i$d0), matrix1, canvas0, v, v1);
                }
                else if(i$d0 instanceof e) {
                    float f = ((float)v) / this.f;
                    float f1 = ((float)v1) / this.g;
                    Matrix matrix2 = this.n;
                    matrix2.set(matrix1);
                    matrix2.postScale(f, f1);
                    float[] arr_f = {0.0f, 1.0f, 1.0f, 0.0f};
                    matrix1.mapVectors(arr_f);
                    float f2 = Math.min(f, f1);
                    float f3 = Math.max(((float)Math.hypot(arr_f[0], arr_f[1])), ((float)Math.hypot(arr_f[2], arr_f[3])));
                    float f4 = f3 > 0.0f ? Math.abs(arr_f[0] * arr_f[3] - arr_f[1] * arr_f[2]) / f3 : 0.0f;
                    if(f4 != 0.0f) {
                        ((e)i$d0).getClass();
                        Path path0 = this.l;
                        path0.reset();
                        PathDataNode[] arr_pathParser$PathDataNode = ((e)i$d0).a;
                        if(arr_pathParser$PathDataNode != null) {
                            PathDataNode.nodesToPath(arr_pathParser$PathDataNode, path0);
                        }
                        Path path1 = this.m;
                        path1.reset();
                        if(((e)i$d0) instanceof a) {
                            path1.addPath(path0, matrix2);
                            canvas0.clipPath(path1);
                        }
                        else {
                            float f5 = ((b)(((e)i$d0))).j;
                            if(f5 != 0.0f || ((b)(((e)i$d0))).k != 1.0f) {
                                float f6 = ((b)(((e)i$d0))).l;
                                float f7 = ((b)(((e)i$d0))).k;
                                if(this.o == null) {
                                    this.o = new PathMeasure();
                                }
                                this.o.setPath(path0, false);
                                float f8 = this.o.getLength();
                                float f9 = (f5 + f6) % 1.0f * f8;
                                float f10 = (f7 + f6) % 1.0f * f8;
                                path0.reset();
                                if(f9 > f10) {
                                    this.o.getSegment(f9, f8, path0, true);
                                    this.o.getSegment(0.0f, f10, path0, true);
                                }
                                else {
                                    this.o.getSegment(f9, f10, path0, true);
                                }
                                path0.rLineTo(0.0f, 0.0f);
                            }
                            path1.addPath(path0, matrix2);
                            if(((b)(((e)i$d0))).f.willDraw()) {
                                ComplexColorCompat complexColorCompat0 = ((b)(((e)i$d0))).f;
                                if(this.b == null) {
                                    Paint paint0 = new Paint(1);
                                    this.b = paint0;
                                    paint0.setStyle(Paint.Style.FILL);
                                }
                                Paint paint1 = this.b;
                                if(complexColorCompat0.isGradient()) {
                                    Shader shader0 = complexColorCompat0.getShader();
                                    shader0.setLocalMatrix(matrix2);
                                    paint1.setShader(shader0);
                                    paint1.setAlpha(Math.round(((b)(((e)i$d0))).i * 255.0f));
                                }
                                else {
                                    int v3 = complexColorCompat0.getColor();
                                    float f11 = ((b)(((e)i$d0))).i;
                                    paint1.setColor(v3 & 0xFFFFFF | ((int)(((float)Color.alpha(v3)) * f11)) << 24);
                                }
                                paint1.setColorFilter(null);
                                path1.setFillType((((b)(((e)i$d0))).h == 0 ? Path.FillType.WINDING : Path.FillType.EVEN_ODD));
                                canvas0.drawPath(path1, paint1);
                            }
                            if(((b)(((e)i$d0))).d.willDraw()) {
                                ComplexColorCompat complexColorCompat1 = ((b)(((e)i$d0))).d;
                                if(this.a == null) {
                                    Paint paint2 = new Paint(1);
                                    this.a = paint2;
                                    paint2.setStyle(Paint.Style.STROKE);
                                }
                                Paint paint3 = this.a;
                                Paint.Join paint$Join0 = ((b)(((e)i$d0))).n;
                                if(paint$Join0 != null) {
                                    paint3.setStrokeJoin(paint$Join0);
                                }
                                Paint.Cap paint$Cap0 = ((b)(((e)i$d0))).m;
                                if(paint$Cap0 != null) {
                                    paint3.setStrokeCap(paint$Cap0);
                                }
                                paint3.setStrokeMiter(((b)(((e)i$d0))).o);
                                if(complexColorCompat1.isGradient()) {
                                    Shader shader1 = complexColorCompat1.getShader();
                                    shader1.setLocalMatrix(matrix2);
                                    paint3.setShader(shader1);
                                    paint3.setAlpha(Math.round(((b)(((e)i$d0))).g * 255.0f));
                                }
                                else {
                                    int v4 = complexColorCompat1.getColor();
                                    float f12 = ((b)(((e)i$d0))).g;
                                    paint3.setColor(v4 & 0xFFFFFF | ((int)(((float)Color.alpha(v4)) * f12)) << 24);
                                }
                                paint3.setColorFilter(null);
                                paint3.setStrokeWidth(f2 * f4 * ((b)(((e)i$d0))).e);
                                canvas0.drawPath(path1, paint3);
                            }
                        }
                    }
                }
            }
            canvas0.restore();
        }

        public final float b() {
            return ((float)this.h) / 255.0f;
        }

        public final void c(float f) {
            this.h = (int)(f * 255.0f);
        }
    }

    static class g extends Drawable.ConstantState {
        public int a;
        public f b;
        public ColorStateList c;
        public PorterDuff.Mode d;
        public boolean e;
        public Bitmap f;
        public ColorStateList g;
        public PorterDuff.Mode h;
        public int i;
        public boolean j;
        public boolean k;
        public Paint l;

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final int getChangingConfigurations() {
            return this.a;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable() {
            return new i(this);
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable(Resources resources0) {
            return new i(this);
        }
    }

    static class android.supportv1.c.a.i.h extends Drawable.ConstantState {
        public final Drawable.ConstantState a;

        public android.supportv1.c.a.i.h(Drawable.ConstantState drawable$ConstantState0) {
            this.a = drawable$ConstantState0;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final boolean canApplyTheme() {
            return this.a.canApplyTheme();
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final int getChangingConfigurations() {
            return this.a.getChangingConfigurations();
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable() {
            Drawable drawable0 = new i();
            drawable0.a = (VectorDrawable)this.a.newDrawable();
            return drawable0;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable(Resources resources0) {
            Drawable drawable0 = new i();
            drawable0.a = (VectorDrawable)this.a.newDrawable(resources0);
            return drawable0;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable(Resources resources0, Resources.Theme resources$Theme0) {
            Drawable drawable0 = new i();
            drawable0.a = (VectorDrawable)this.a.newDrawable(resources0, resources$Theme0);
            return drawable0;
        }
    }

    public g b;
    public PorterDuffColorFilter c;
    public ColorFilter d;
    public boolean e;
    public boolean f;
    public final float[] g;
    public final Matrix h;
    public final Rect i;
    public static final PorterDuff.Mode j;

    static {
        i.j = PorterDuff.Mode.SRC_IN;
    }

    public i() {
        this.f = true;
        this.g = new float[9];
        this.h = new Matrix();
        this.i = new Rect();
        g i$g0 = new g();  // 初始化器: Landroid/graphics/drawable/Drawable$ConstantState;-><init>()V
        i$g0.c = null;
        i$g0.d = i.j;
        i$g0.b = new f();
        this.b = i$g0;
    }

    public i(g i$g0) {
        this.f = true;
        this.g = new float[9];
        this.h = new Matrix();
        this.i = new Rect();
        this.b = i$g0;
        this.c = this.a(i$g0.c, i$g0.d);
    }

    public final PorterDuffColorFilter a(ColorStateList colorStateList0, PorterDuff.Mode porterDuff$Mode0) {
        return colorStateList0 == null || porterDuff$Mode0 == null ? null : new PorterDuffColorFilter(colorStateList0.getColorForState(super.getState(), 0), porterDuff$Mode0);
    }

    public static i b(Resources resources0, XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
        i i0 = new i();
        i0.inflate(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
        return i0;
    }

    public final Object c(String s) {
        return this.b.b.k.get(s);
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean canApplyTheme() {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.canApplyTheme(drawable0);
        }
        return false;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void draw(Canvas canvas0) {
        Paint paint0;
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.draw(canvas0);
            return;
        }
        Rect rect0 = this.i;
        this.copyBounds(rect0);
        if(rect0.width() > 0 && rect0.height() > 0) {
            ColorFilter colorFilter0 = this.d;
            if(colorFilter0 == null) {
                colorFilter0 = this.c;
            }
            canvas0.getMatrix(this.h);
            this.h.getValues(this.g);
            float f = Math.abs(this.g[0]);
            float f1 = Math.abs(this.g[4]);
            if(Math.abs(this.g[1]) != 0.0f || Math.abs(this.g[3]) != 0.0f) {
                f = 1.0f;
                f1 = 1.0f;
            }
            int v = Math.min(0x800, ((int)(((float)rect0.width()) * f)));
            int v1 = Math.min(0x800, ((int)(((float)rect0.height()) * f1)));
            if(v > 0 && v1 > 0) {
                int v2 = canvas0.save();
                canvas0.translate(((float)rect0.left), ((float)rect0.top));
                if(this.isAutoMirrored() && DrawableCompat.getLayoutDirection(this) == 1) {
                    canvas0.translate(((float)rect0.width()), 0.0f);
                    canvas0.scale(-1.0f, 1.0f);
                }
                rect0.offsetTo(0, 0);
                g i$g0 = this.b;
                if(i$g0.f == null || v != i$g0.f.getWidth() || v1 != i$g0.f.getHeight()) {
                    i$g0.f = Bitmap.createBitmap(v, v1, Bitmap.Config.ARGB_8888);
                    i$g0.k = true;
                }
                if(this.f) {
                    g i$g2 = this.b;
                    if(i$g2.k || i$g2.g != i$g2.c || i$g2.h != i$g2.d || i$g2.j != i$g2.e || i$g2.i != i$g2.b.h) {
                        i$g2.f.eraseColor(0);
                        Canvas canvas2 = new Canvas(i$g2.f);
                        i$g2.b.a(i$g2.b.c, f.p, canvas2, v, v1);
                        this.b.g = this.b.c;
                        this.b.h = this.b.d;
                        this.b.i = this.b.b.h;
                        this.b.j = this.b.e;
                        this.b.k = false;
                    }
                }
                else {
                    g i$g1 = this.b;
                    i$g1.f.eraseColor(0);
                    Canvas canvas1 = new Canvas(i$g1.f);
                    i$g1.b.a(i$g1.b.c, f.p, canvas1, v, v1);
                }
                g i$g3 = this.b;
                if(i$g3.b.h < 0xFF || colorFilter0 != null) {
                    if(i$g3.l == null) {
                        Paint paint1 = new Paint();
                        i$g3.l = paint1;
                        paint1.setFilterBitmap(true);
                    }
                    i$g3.l.setAlpha(i$g3.b.h);
                    i$g3.l.setColorFilter(colorFilter0);
                    paint0 = i$g3.l;
                }
                else {
                    paint0 = null;
                }
                canvas0.drawBitmap(i$g3.f, null, rect0, paint0);
                canvas0.restoreToCount(v2);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getAlpha() {
        return this.a == null ? this.b.b.h : DrawableCompat.getAlpha(this.a);
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getChangingConfigurations() {
        Drawable drawable0 = this.a;
        return drawable0 == null ? super.getChangingConfigurations() | this.b.a : drawable0.getChangingConfigurations();
    }

    @Override  // android.graphics.drawable.Drawable
    public final Drawable.ConstantState getConstantState() {
        if(this.a != null && Build.VERSION.SDK_INT >= 24) {
            return new android.supportv1.c.a.i.h(this.a.getConstantState());
        }
        g i$g0 = this.b;
        i$g0.a = this.getChangingConfigurations();
        return this.b;
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getIntrinsicHeight() {
        return this.a == null ? ((int)this.b.b.e) : this.a.getIntrinsicHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getIntrinsicWidth() {
        return this.a == null ? ((int)this.b.b.d) : this.a.getIntrinsicWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getOpacity() {
        return this.a == null ? -3 : this.a.getOpacity();
    }

    @Override  // android.graphics.drawable.Drawable
    public final void inflate(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.inflate(resources0, xmlPullParser0, attributeSet0);
            return;
        }
        this.inflate(resources0, xmlPullParser0, attributeSet0, null);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void inflate(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.inflate(drawable0, resources0, xmlPullParser0, attributeSet0, resources$Theme0);
            return;
        }
        g i$g0 = this.b;
        i$g0.b = new f();
        TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, android.supportv1.c.a.a.a);
        g i$g1 = this.b;
        f i$f0 = i$g1.b;
        int v = TypedArrayUtils.getNamedInt(typedArray0, xmlPullParser0, "tintMode", 6, -1);
        PorterDuff.Mode porterDuff$Mode0 = PorterDuff.Mode.SRC_IN;
        switch(v) {
            case 3: {
                porterDuff$Mode0 = PorterDuff.Mode.SRC_OVER;
                break;
            }
            case 9: {
                porterDuff$Mode0 = PorterDuff.Mode.SRC_ATOP;
                break;
            }
            case 14: {
                porterDuff$Mode0 = PorterDuff.Mode.MULTIPLY;
                break;
            }
            case 15: {
                porterDuff$Mode0 = PorterDuff.Mode.SCREEN;
                break;
            }
            case 16: {
                porterDuff$Mode0 = PorterDuff.Mode.ADD;
            }
        }
        i$g1.d = porterDuff$Mode0;
        ColorStateList colorStateList0 = typedArray0.getColorStateList(1);
        if(colorStateList0 != null) {
            i$g1.c = colorStateList0;
        }
        i$g1.e = TypedArrayUtils.getNamedBoolean(typedArray0, xmlPullParser0, "autoMirrored", 5, i$g1.e);
        i$f0.f = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "viewportWidth", 7, i$f0.f);
        float f = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "viewportHeight", 8, i$f0.g);
        i$f0.g = f;
        if(i$f0.f <= 0.0f) {
            throw new XmlPullParserException(typedArray0.getPositionDescription() + "<vector> tag requires viewportWidth > 0");
        }
        if(f <= 0.0f) {
            throw new XmlPullParserException(typedArray0.getPositionDescription() + "<vector> tag requires viewportHeight > 0");
        }
        i$f0.d = typedArray0.getDimension(3, i$f0.d);
        float f1 = typedArray0.getDimension(2, i$f0.e);
        i$f0.e = f1;
        if(i$f0.d <= 0.0f) {
            throw new XmlPullParserException(typedArray0.getPositionDescription() + "<vector> tag requires width > 0");
        }
        if(f1 <= 0.0f) {
            throw new XmlPullParserException(typedArray0.getPositionDescription() + "<vector> tag requires height > 0");
        }
        i$f0.c(TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "alpha", 4, i$f0.b()));
        String s = typedArray0.getString(0);
        if(s != null) {
            i$f0.i = s;
            i$f0.k.put(s, i$f0);
        }
        typedArray0.recycle();
        i$g0.a = this.getChangingConfigurations();
        i$g0.k = true;
        g i$g2 = this.b;
        f i$f1 = i$g2.b;
        ArrayDeque arrayDeque0 = new ArrayDeque();
        arrayDeque0.push(i$f1.c);
        int v1 = xmlPullParser0.getEventType();
        int v2 = xmlPullParser0.getDepth();
        boolean z = true;
        while(v1 != 1 && (xmlPullParser0.getDepth() >= v2 + 1 || v1 != 3)) {
            switch(v1) {
                case 2: {
                    String s1 = xmlPullParser0.getName();
                    c i$c0 = (c)arrayDeque0.peek();
                    ArrayMap arrayMap0 = i$f1.k;
                    if("path".equals(s1)) {
                        b i$b0 = new b();
                        i$b0.d(resources0, xmlPullParser0, attributeSet0, resources$Theme0);
                        i$c0.b.add(i$b0);
                        if(i$b0.c() != null) {
                            arrayMap0.put(i$b0.c(), i$b0);
                        }
                        z = false;
                        i$g2.a |= i$b0.c;
                    }
                    else if("clip-path".equals(s1)) {
                        a i$a0 = new a();  // 初始化器: Landroid/supportv1/c/a/i$e;-><init>()V
                        i$a0.d(resources0, xmlPullParser0, attributeSet0, resources$Theme0);
                        i$c0.b.add(i$a0);
                        if(i$a0.c() != null) {
                            arrayMap0.put(i$a0.c(), i$a0);
                        }
                        i$g2.a |= i$a0.c;
                    }
                    else if("group".equals(s1)) {
                        c i$c1 = new c();
                        i$c1.c(resources0, xmlPullParser0, attributeSet0, resources$Theme0);
                        i$c0.b.add(i$c1);
                        arrayDeque0.push(i$c1);
                        if(i$c1.d() != null) {
                            arrayMap0.put(i$c1.d(), i$c1);
                        }
                        i$g2.a |= i$c1.e;
                    }
                    break;
                }
                case 3: {
                    if("group".equals(xmlPullParser0.getName())) {
                        arrayDeque0.pop();
                    }
                }
            }
            v1 = xmlPullParser0.next();
        }
        if(z) {
            throw new XmlPullParserException("no path defined");
        }
        this.c = this.a(i$g0.c, i$g0.d);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void invalidateSelf() {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.invalidateSelf();
            return;
        }
        super.invalidateSelf();
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean isAutoMirrored() {
        return this.a == null ? this.b.e : DrawableCompat.isAutoMirrored(this.a);
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean isStateful() {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            return drawable0.isStateful();
        }
        if(!super.isStateful()) {
            g i$g0 = this.b;
            if(i$g0 == null) {
                return false;
            }
            f i$f0 = i$g0.b;
            if(i$f0.j == null) {
                i$f0.j = Boolean.valueOf(i$f0.c.b());
            }
            if(!i$f0.j.booleanValue()) {
                ColorStateList colorStateList0 = this.b.c;
                return colorStateList0 != null && colorStateList0.isStateful();
            }
        }
        return true;
    }

    @Override  // android.graphics.drawable.Drawable
    public final Drawable mutate() {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.mutate();
            return this;
        }
        if(!this.e && super.mutate() == this) {
            g i$g0 = this.b;
            g i$g1 = new g();  // 初始化器: Landroid/graphics/drawable/Drawable$ConstantState;-><init>()V
            i$g1.c = null;
            i$g1.d = i.j;
            if(i$g0 != null) {
                i$g1.a = i$g0.a;
                f i$f0 = new f(i$g0.b);
                i$g1.b = i$f0;
                if(i$g0.b.b != null) {
                    i$f0.b = new Paint(i$g0.b.b);
                }
                if(i$g0.b.a != null) {
                    f i$f1 = i$g1.b;
                    i$f1.a = new Paint(i$g0.b.a);
                }
                i$g1.c = i$g0.c;
                i$g1.d = i$g0.d;
                i$g1.e = i$g0.e;
            }
            this.b = i$g1;
            this.e = true;
        }
        return this;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void onBoundsChange(Rect rect0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.setBounds(rect0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean onStateChange(int[] arr_v) {
        boolean z;
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            return drawable0.setState(arr_v);
        }
        g i$g0 = this.b;
        ColorStateList colorStateList0 = i$g0.c;
        if(colorStateList0 == null) {
            z = false;
        }
        else {
            PorterDuff.Mode porterDuff$Mode0 = i$g0.d;
            if(porterDuff$Mode0 == null) {
                z = false;
            }
            else {
                this.c = this.a(colorStateList0, porterDuff$Mode0);
                this.invalidateSelf();
                z = true;
            }
        }
        f i$f0 = i$g0.b;
        if(i$f0.j == null) {
            i$f0.j = Boolean.valueOf(i$f0.c.b());
        }
        if(i$f0.j.booleanValue()) {
            boolean z1 = i$g0.b.c.a(arr_v);
            i$g0.k |= z1;
            if(z1) {
                this.invalidateSelf();
                return true;
            }
        }
        return z;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void scheduleSelf(Runnable runnable0, long v) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.scheduleSelf(runnable0, v);
            return;
        }
        super.scheduleSelf(runnable0, v);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setAlpha(int v) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.setAlpha(v);
            return;
        }
        f i$f0 = this.b.b;
        if(i$f0.h != v) {
            i$f0.h = v;
            this.invalidateSelf();
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setAutoMirrored(boolean z) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.setAutoMirrored(drawable0, z);
            return;
        }
        this.b.e = z;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void setColorFilter(ColorFilter colorFilter0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.setColorFilter(colorFilter0);
            return;
        }
        this.d = colorFilter0;
        this.invalidateSelf();
    }

    @Override  // android.graphics.drawable.Drawable, android.supportv1.v4.graphics.drawable.TintAwareDrawable
    public final void setTint(int v) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.setTint(drawable0, v);
            return;
        }
        this.setTintList(ColorStateList.valueOf(v));
    }

    @Override  // android.graphics.drawable.Drawable, android.supportv1.v4.graphics.drawable.TintAwareDrawable
    public final void setTintList(ColorStateList colorStateList0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.setTintList(drawable0, colorStateList0);
            return;
        }
        g i$g0 = this.b;
        if(i$g0.c != colorStateList0) {
            i$g0.c = colorStateList0;
            this.c = this.a(colorStateList0, i$g0.d);
            this.invalidateSelf();
        }
    }

    @Override  // android.graphics.drawable.Drawable, android.supportv1.v4.graphics.drawable.TintAwareDrawable
    public final void setTintMode(PorterDuff.Mode porterDuff$Mode0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            DrawableCompat.setTintMode(drawable0, porterDuff$Mode0);
            return;
        }
        g i$g0 = this.b;
        if(i$g0.d != porterDuff$Mode0) {
            i$g0.d = porterDuff$Mode0;
            this.c = this.a(i$g0.c, porterDuff$Mode0);
            this.invalidateSelf();
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean setVisible(boolean z, boolean z1) {
        Drawable drawable0 = this.a;
        return drawable0 == null ? super.setVisible(z, z1) : drawable0.setVisible(z, z1);
    }

    @Override  // android.graphics.drawable.Drawable
    public final void unscheduleSelf(Runnable runnable0) {
        Drawable drawable0 = this.a;
        if(drawable0 != null) {
            drawable0.unscheduleSelf(runnable0);
            return;
        }
        super.unscheduleSelf(runnable0);
    }
}


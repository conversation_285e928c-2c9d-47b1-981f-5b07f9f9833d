package android.supportv1.g;

import android.supportv1.v4.util.ArrayMap;
import android.supportv1.v4.view.ViewCompat;
import android.view.View.OnAttachStateChangeListener;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver.OnPreDrawListener;
import java.lang.ref.WeakReference;
import java.util.ArrayList;

public abstract class o {
    static class a implements View.OnAttachStateChangeListener, ViewTreeObserver.OnPreDrawListener {
        public m a;
        public ViewGroup b;

        @Override  // android.view.ViewTreeObserver$OnPreDrawListener
        public final boolean onPreDraw() {
            ViewGroup viewGroup0 = this.b;
            viewGroup0.getViewTreeObserver().removeOnPreDrawListener(this);
            viewGroup0.removeOnAttachStateChangeListener(this);
            ViewGroup viewGroup1 = this.b;
            if(!o.a.remove(viewGroup1)) {
                return true;
            }
            ArrayMap arrayMap0 = o.a();
            ArrayList arrayList0 = (ArrayList)arrayMap0.get(viewGroup1);
            ArrayList arrayList1 = null;
            if(arrayList0 == null) {
                arrayList0 = new ArrayList();
                arrayMap0.put(viewGroup1, arrayList0);
            }
            else if(arrayList0.size() > 0) {
                arrayList1 = new ArrayList(arrayList0);
            }
            m m0 = this.a;
            arrayList0.add(m0);
            m0.h(new o.a.1(this, arrayMap0));
            m0.o(viewGroup1, false);
            if(arrayList1 != null) {
                for(Object object0: arrayList1) {
                    ((m)object0).G(viewGroup1);
                }
            }
            m0.m(viewGroup1);
            return true;
        }

        @Override  // android.view.View$OnAttachStateChangeListener
        public final void onViewAttachedToWindow(View view0) {
        }

        @Override  // android.view.View$OnAttachStateChangeListener
        public final void onViewDetachedFromWindow(View view0) {
            ViewGroup viewGroup0 = this.b;
            viewGroup0.getViewTreeObserver().removeOnPreDrawListener(this);
            viewGroup0.removeOnAttachStateChangeListener(this);
            ViewGroup viewGroup1 = this.b;
            o.a.remove(viewGroup1);
            ArrayList arrayList0 = (ArrayList)o.a().get(viewGroup1);
            if(arrayList0 != null && arrayList0.size() > 0) {
                for(Object object0: arrayList0) {
                    ((m)object0).G(viewGroup1);
                }
            }
            this.a.p(true);
        }
    }

    public static final ArrayList a;
    public static final b b;
    public static final ThreadLocal c;

    static {
        b b0 = new b();  // 初始化器: Landroid/supportv1/g/q;-><init>()V
        b0.A = false;
        b0.L(new d(2));
        b0.L(new c());  // 初始化器: Landroid/supportv1/g/m;-><init>()V
        b0.L(new d(1));
        o.b = b0;
        o.c = new ThreadLocal();
        o.a = new ArrayList();
    }

    public static ArrayMap a() {
        ThreadLocal threadLocal0 = o.c;
        WeakReference weakReference0 = (WeakReference)threadLocal0.get();
        if(weakReference0 != null) {
            ArrayMap arrayMap0 = (ArrayMap)weakReference0.get();
            if(arrayMap0 != null) {
                return arrayMap0;
            }
        }
        ArrayMap arrayMap1 = new ArrayMap();
        threadLocal0.set(new WeakReference(arrayMap1));
        return arrayMap1;
    }

    public static void b(m m0, ViewGroup viewGroup0) {
        ArrayList arrayList0 = o.a;
        if(!arrayList0.contains(viewGroup0) && ViewCompat.isLaidOut(viewGroup0)) {
            arrayList0.add(viewGroup0);
            if(m0 == null) {
                m0 = o.b;
            }
            m m1 = m0.J();
            ArrayList arrayList1 = (ArrayList)o.a().get(viewGroup0);
            if(arrayList1 != null && arrayList1.size() > 0) {
                for(Object object0: arrayList1) {
                    ((m)object0).E(viewGroup0);
                }
            }
            if(m1 != null) {
                m1.o(viewGroup0, true);
            }
            l.a(viewGroup0);
            l.b(viewGroup0);
            if(m1 != null) {
                a o$a0 = new a();  // 初始化器: Ljava/lang/Object;-><init>()V
                o$a0.a = m1;
                o$a0.b = viewGroup0;
                viewGroup0.addOnAttachStateChangeListener(o$a0);
                viewGroup0.getViewTreeObserver().addOnPreDrawListener(o$a0);
            }
        }
    }
}


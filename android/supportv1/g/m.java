package android.supportv1.g;

import android.animation.Animator;
import android.animation.TimeInterpolator;
import android.graphics.Path;
import android.supportv1.v4.util.ArrayMap;
import android.supportv1.v4.util.LongSparseArray;
import android.supportv1.v4.view.ViewCompat;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;
import java.util.ArrayList;
import java.util.Iterator;

public abstract class m implements Cloneable {
    static class a {
        public final View a;
        public final String b;
        public final s c;
        public final al d;
        public final m e;

        public a(View view0, String s, m m0, ak ak0, s s1) {
            this.a = view0;
            this.b = s;
            this.c = s1;
            this.d = ak0;
            this.e = m0;
        }
    }

    public static abstract class b {
    }

    public interface c {
        void a(m arg1);

        void b();

        void c();

        void d();
    }

    public int a;
    public boolean b;
    public boolean c;
    public ArrayList d;
    public ArrayList e;
    public b f;
    public g g;
    public long h;
    public final ArrayList i;
    public final ArrayList j;
    public q k;
    public final ArrayList l;
    public final String m;
    public long n;
    public TimeInterpolator o;
    public t p;
    public t q;
    public final int[] r;
    public ArrayList s;
    public ArrayList t;
    public static final ThreadLocal u;
    public static final int[] v;
    public static final g w;

    static {
        m.v = new int[]{2, 1, 3, 4};
        m.w = new g() {  // 初始化器: Ljava/lang/Object;-><init>()V
            @Override  // android.supportv1.g.g
            public final Path a(float f, float f1, float f2, float f3) {
                Path path0 = new Path();
                path0.moveTo(f, f1);
                path0.lineTo(f2, f3);
                return path0;
            }
        };
        m.u = new ThreadLocal();
    }

    public m() {
        this.m = this.getClass().getName();
        this.n = -1L;
        this.h = -1L;
        this.o = null;
        this.i = new ArrayList();
        this.j = new ArrayList();
        this.p = new t();
        this.q = new t();
        this.k = null;
        this.r = m.v;
        this.l = new ArrayList();
        this.a = 0;
        this.b = false;
        this.c = false;
        this.d = null;
        this.e = new ArrayList();
        this.g = m.w;
    }

    public void A(View view0) {
        this.j.add(view0);
    }

    public void B(s s0) {
    }

    public void C(View view0) {
        this.j.remove(view0);
    }

    public final void D(View view0, boolean z) {
        if(view0 == null) {
            return;
        }
        view0.getId();
        if(view0.getParent() instanceof ViewGroup) {
            s s0 = new s();
            s0.b = view0;
            if(z) {
                this.i(s0);
            }
            else {
                this.y(s0);
            }
            s0.c.add(this);
            this.B(s0);
            m.j((z ? this.p : this.q), view0, s0);
        }
        if(view0 instanceof ViewGroup) {
            for(int v = 0; v < ((ViewGroup)view0).getChildCount(); ++v) {
                this.D(((ViewGroup)view0).getChildAt(v), z);
            }
        }
    }

    public void E(View view0) {
        if(!this.c) {
            ArrayMap arrayMap0 = m.K();
            int v = arrayMap0.size();
            ak ak0 = ad.d(view0);
            while(true) {
                --v;
                if(v < 0) {
                    break;
                }
                a m$a0 = (a)arrayMap0.valueAt(v);
                if(m$a0.a != null && ak0.equals(m$a0.d)) {
                    android.supportv1.g.a.a(((Animator)arrayMap0.keyAt(v)));
                }
            }
            if(this.d != null && this.d.size() > 0) {
                ArrayList arrayList0 = (ArrayList)this.d.clone();
                int v1 = arrayList0.size();
                for(int v2 = 0; v2 < v1; ++v2) {
                    ((c)arrayList0.get(v2)).b();
                }
            }
            this.b = true;
        }
    }

    public void F() {
        this.H();
        ArrayMap arrayMap0 = m.K();
        for(Object object0: this.e) {
            Animator animator0 = (Animator)object0;
            if(arrayMap0.containsKey(animator0)) {
                this.H();
                if(animator0 != null) {
                    animator0.addListener(new m.2(this, arrayMap0));
                    long v = this.h;
                    if(v >= 0L) {
                        animator0.setDuration(v);
                    }
                    long v1 = this.n;
                    if(v1 >= 0L) {
                        animator0.setStartDelay(v1);
                    }
                    TimeInterpolator timeInterpolator0 = this.o;
                    if(timeInterpolator0 != null) {
                        animator0.setInterpolator(timeInterpolator0);
                    }
                    animator0.addListener(new m.3(this));
                    animator0.start();
                }
            }
        }
        this.e.clear();
        this.I();
    }

    public void G(ViewGroup viewGroup0) {
        if(this.b) {
            if(!this.c) {
                ArrayMap arrayMap0 = m.K();
                int v = arrayMap0.size();
                ak ak0 = ad.d(viewGroup0);
                while(true) {
                    --v;
                    if(v < 0) {
                        break;
                    }
                    a m$a0 = (a)arrayMap0.valueAt(v);
                    if(m$a0.a != null && ak0.equals(m$a0.d)) {
                        android.supportv1.g.a.b(((Animator)arrayMap0.keyAt(v)));
                    }
                }
                if(this.d != null && this.d.size() > 0) {
                    ArrayList arrayList0 = (ArrayList)this.d.clone();
                    int v1 = arrayList0.size();
                    for(int v2 = 0; v2 < v1; ++v2) {
                        ((c)arrayList0.get(v2)).c();
                    }
                }
            }
            this.b = false;
        }
    }

    public final void H() {
        if(this.a == 0) {
            if(this.d != null && this.d.size() > 0) {
                ArrayList arrayList0 = (ArrayList)this.d.clone();
                int v = arrayList0.size();
                for(int v1 = 0; v1 < v; ++v1) {
                    ((c)arrayList0.get(v1)).d();
                }
            }
            this.c = false;
        }
        ++this.a;
    }

    public final void I() {
        int v = this.a - 1;
        this.a = v;
        if(v == 0) {
            if(this.d != null && this.d.size() > 0) {
                ArrayList arrayList0 = (ArrayList)this.d.clone();
                int v1 = arrayList0.size();
                for(int v2 = 0; v2 < v1; ++v2) {
                    ((c)arrayList0.get(v2)).a(this);
                }
            }
            for(int v3 = 0; v3 < this.p.c.size(); ++v3) {
                View view0 = (View)this.p.c.valueAt(v3);
                if(view0 != null) {
                    ViewCompat.setHasTransientState(view0, false);
                }
            }
            for(int v4 = 0; v4 < this.q.c.size(); ++v4) {
                View view1 = (View)this.q.c.valueAt(v4);
                if(view1 != null) {
                    ViewCompat.setHasTransientState(view1, false);
                }
            }
            this.c = true;
        }
    }

    public m J() {
        try {
            m m0 = (m)super.clone();
            m0.e = new ArrayList();
            m0.p = new t();
            m0.q = new t();
            m0.s = null;
            m0.t = null;
            return m0;
        }
        catch(CloneNotSupportedException unused_ex) {
            return null;
        }
    }

    public static ArrayMap K() {
        ThreadLocal threadLocal0 = m.u;
        ArrayMap arrayMap0 = (ArrayMap)threadLocal0.get();
        if(arrayMap0 == null) {
            arrayMap0 = new ArrayMap();
            threadLocal0.set(arrayMap0);
        }
        return arrayMap0;
    }

    public Animator a(ViewGroup viewGroup0, s s0, s s1) {
        return null;
    }

    public final s b(View view0, boolean z) {
        q q0 = this.k;
        if(q0 != null) {
            return q0.b(view0, z);
        }
        return z ? ((s)this.p.a.get(view0)) : ((s)this.q.a.get(view0));
    }

    public String c(String s) {
        StringBuilder stringBuilder0 = androidx.work.impl.model.c.n(s);
        stringBuilder0.append(this.getClass().getSimpleName());
        stringBuilder0.append("@");
        stringBuilder0.append(Integer.toHexString(this.hashCode()));
        stringBuilder0.append(": ");
        String s1 = this.h == -1L ? stringBuilder0.toString() : a.a.r(androidx.work.impl.model.c.o(stringBuilder0.toString(), "dur("), this.h, ") ");
        if(this.n != -1L) {
            s1 = a.a.r(androidx.work.impl.model.c.o(s1, "dly("), this.n, ") ");
        }
        if(this.o != null) {
            StringBuilder stringBuilder1 = androidx.work.impl.model.c.o(s1, "interp(");
            stringBuilder1.append(this.o);
            stringBuilder1.append(") ");
            s1 = stringBuilder1.toString();
        }
        ArrayList arrayList0 = this.i;
        ArrayList arrayList1 = this.j;
        if(arrayList0.size() > 0 || arrayList1.size() > 0) {
            String s2 = s1 + "tgts(";
            if(arrayList0.size() > 0) {
                for(int v1 = 0; v1 < arrayList0.size(); ++v1) {
                    if(v1 > 0) {
                        s2 = s2 + ", ";
                    }
                    StringBuilder stringBuilder2 = androidx.work.impl.model.c.n(s2);
                    stringBuilder2.append(arrayList0.get(v1));
                    s2 = stringBuilder2.toString();
                }
            }
            if(arrayList1.size() > 0) {
                for(int v = 0; v < arrayList1.size(); ++v) {
                    if(v > 0) {
                        s2 = s2 + ", ";
                    }
                    StringBuilder stringBuilder3 = androidx.work.impl.model.c.n(s2);
                    stringBuilder3.append(arrayList1.get(v));
                    s2 = stringBuilder3.toString();
                }
            }
            return s2 + ")";
        }
        return s1;
    }

    @Override
    public Object clone() {
        return this.J();
    }

    public void d(long v) {
        this.h = v;
    }

    public void e(TimeInterpolator timeInterpolator0) {
        this.o = timeInterpolator0;
    }

    public void f(g g0) {
        if(g0 == null) {
            g0 = m.w;
        }
        this.g = g0;
    }

    public void g(b m$b0) {
        this.f = m$b0;
    }

    public void h(c m$c0) {
        if(this.d == null) {
            this.d = new ArrayList();
        }
        this.d.add(m$c0);
    }

    public abstract void i(s arg1);

    public static void j(t t0, View view0, s s0) {
        t0.a.put(view0, s0);
        int v = view0.getId();
        if(v >= 0) {
            SparseArray sparseArray0 = t0.b;
            if(sparseArray0.indexOfKey(v) >= 0) {
                sparseArray0.put(v, null);
            }
            else {
                sparseArray0.put(v, view0);
            }
        }
        String s1 = ViewCompat.getTransitionName(view0);
        if(s1 != null) {
            ArrayMap arrayMap0 = t0.d;
            if(arrayMap0.containsKey(s1)) {
                arrayMap0.put(s1, null);
            }
            else {
                arrayMap0.put(s1, view0);
            }
        }
        if(view0.getParent() instanceof ListView) {
            ListView listView0 = (ListView)view0.getParent();
            if(listView0.getAdapter().hasStableIds()) {
                long v1 = listView0.getItemIdAtPosition(listView0.getPositionForView(view0));
                LongSparseArray longSparseArray0 = t0.c;
                if(longSparseArray0.indexOfKey(v1) >= 0) {
                    View view1 = (View)longSparseArray0.get(v1);
                    if(view1 != null) {
                        ViewCompat.setHasTransientState(view1, false);
                        longSparseArray0.put(v1, null);
                    }
                }
                else {
                    ViewCompat.setHasTransientState(view0, true);
                    longSparseArray0.put(v1, view0);
                }
            }
        }
    }

    public final void k(ArrayMap arrayMap0, ArrayMap arrayMap1) {
        for(int v = arrayMap0.size() - 1; v >= 0; --v) {
            View view0 = (View)arrayMap0.keyAt(v);
            if(view0 != null && this.s(view0)) {
                s s0 = (s)arrayMap1.remove(view0);
                if(s0 != null && (s0.b != null && this.s(s0.b))) {
                    s s1 = (s)arrayMap0.removeAt(v);
                    this.s.add(s1);
                    this.t.add(s0);
                }
            }
        }
    }

    public final void l(ArrayMap arrayMap0, ArrayMap arrayMap1, ArrayMap arrayMap2, ArrayMap arrayMap3) {
        int v = arrayMap2.size();
        for(int v1 = 0; v1 < v; ++v1) {
            View view0 = (View)arrayMap2.valueAt(v1);
            if(view0 != null && this.s(view0)) {
                View view1 = (View)arrayMap3.get(arrayMap2.keyAt(v1));
                if(view1 != null && this.s(view1)) {
                    s s0 = (s)arrayMap0.get(view0);
                    s s1 = (s)arrayMap1.get(view1);
                    if(s0 != null && s1 != null) {
                        this.s.add(s0);
                        this.t.add(s1);
                        arrayMap0.remove(view0);
                        arrayMap1.remove(view1);
                    }
                }
            }
        }
    }

    public final void m(ViewGroup viewGroup0) {
        this.s = new ArrayList();
        this.t = new ArrayList();
        t t0 = this.p;
        t t1 = this.q;
        ArrayMap arrayMap0 = new ArrayMap(t0.a);
        ArrayMap arrayMap1 = new ArrayMap(t1.a);
        for(int v = 0; true; ++v) {
            int[] arr_v = this.r;
            if(v >= arr_v.length) {
                break;
            }
            switch(arr_v[v]) {
                case 1: {
                    this.k(arrayMap0, arrayMap1);
                    break;
                }
                case 2: {
                    this.l(arrayMap0, arrayMap1, t0.d, t1.d);
                    break;
                }
                case 3: {
                    SparseArray sparseArray0 = t0.b;
                    SparseArray sparseArray1 = t1.b;
                    int v1 = sparseArray0.size();
                    for(int v2 = 0; v2 < v1; ++v2) {
                        View view0 = (View)sparseArray0.valueAt(v2);
                        if(view0 != null && this.s(view0)) {
                            View view1 = (View)sparseArray1.get(sparseArray0.keyAt(v2));
                            if(view1 != null && this.s(view1)) {
                                s s0 = (s)arrayMap0.get(view0);
                                s s1 = (s)arrayMap1.get(view1);
                                if(s0 != null && s1 != null) {
                                    this.s.add(s0);
                                    this.t.add(s1);
                                    arrayMap0.remove(view0);
                                    arrayMap1.remove(view1);
                                }
                            }
                        }
                    }
                    break;
                }
                case 4: {
                    LongSparseArray longSparseArray0 = t0.c;
                    int v3 = longSparseArray0.size();
                    for(int v4 = 0; v4 < v3; ++v4) {
                        View view2 = (View)longSparseArray0.valueAt(v4);
                        if(view2 != null && this.s(view2)) {
                            long v5 = longSparseArray0.keyAt(v4);
                            View view3 = (View)t1.c.get(v5);
                            if(view3 != null && this.s(view3)) {
                                s s2 = (s)arrayMap0.get(view2);
                                s s3 = (s)arrayMap1.get(view3);
                                if(s2 != null && s3 != null) {
                                    this.s.add(s2);
                                    this.t.add(s3);
                                    arrayMap0.remove(view2);
                                    arrayMap1.remove(view3);
                                }
                            }
                        }
                    }
                }
            }
        }
        this.z(arrayMap0, arrayMap1);
        ArrayMap arrayMap2 = m.K();
        int v6 = arrayMap2.size();
        ak ak0 = ad.d(viewGroup0);
        while(true) {
            --v6;
            if(v6 < 0) {
                break;
            }
            Animator animator0 = (Animator)arrayMap2.keyAt(v6);
            if(animator0 != null) {
                a m$a0 = (a)arrayMap2.get(animator0);
                if(m$a0 != null) {
                    View view4 = m$a0.a;
                    if(view4 != null && ak0.equals(m$a0.d)) {
                        s s4 = this.b(view4, true);
                        s s5 = this.v(view4, true);
                        if(s4 == null && s5 == null || !m$a0.e.q(m$a0.c, s5)) {
                        }
                        else if(!animator0.isRunning() && !animator0.isStarted()) {
                            arrayMap2.remove(animator0);
                        }
                        else {
                            animator0.cancel();
                        }
                    }
                }
            }
        }
        this.n(viewGroup0, this.p, this.q, this.s, this.t);
        this.F();
    }

    public void n(ViewGroup viewGroup0, t t0, t t1, ArrayList arrayList0, ArrayList arrayList1) {
        Animator animator2;
        s s6;
        View view1;
        s s5;
        Animator animator1;
        ArrayMap arrayMap0 = m.K();
        SparseIntArray sparseIntArray0 = new SparseIntArray();
        int v = arrayList0.size();
        int v1 = 0;
        while(v1 < v) {
            s s0 = (s)arrayList0.get(v1);
            s s1 = (s)arrayList1.get(v1);
            if(s0 != null && !s0.c.contains(this)) {
                s0 = null;
            }
            if(s1 != null && !s1.c.contains(this)) {
                s1 = null;
            }
            if((s0 != null || s1 != null) && (s0 == null || s1 == null || this.q(s0, s1))) {
                Animator animator0 = this.a(viewGroup0, s0, s1);
                if(animator0 != null) {
                    if(s1 == null) {
                        view1 = s0.b;
                        animator2 = animator0;
                        s6 = null;
                    }
                    else {
                        View view0 = s1.b;
                        String[] arr_s = this.t();
                        if(view0 == null || arr_s == null || arr_s.length <= 0) {
                            animator1 = animator0;
                            s5 = null;
                        }
                        else {
                            s s2 = new s();
                            s2.b = view0;
                            s s3 = (s)t1.a.get(view0);
                            if(s3 != null) {
                                for(int v2 = 0; v2 < arr_s.length; ++v2) {
                                    String s4 = arr_s[v2];
                                    Object object0 = s3.a.get(s4);
                                    s2.a.put(s4, object0);
                                }
                            }
                            animator1 = animator0;
                            int v3 = arrayMap0.size();
                            int v4 = 0;
                            while(v4 < v3) {
                                a m$a0 = (a)arrayMap0.get(((Animator)arrayMap0.keyAt(v4)));
                                if(m$a0.c != null && m$a0.a == view0 && m$a0.b.equals(this.m) && m$a0.c.equals(s2)) {
                                    s5 = s2;
                                    animator1 = null;
                                    goto label_44;
                                }
                                ++v4;
                            }
                            s5 = s2;
                        }
                    label_44:
                        view1 = view0;
                        s6 = s5;
                        animator2 = animator1;
                    }
                    if(animator2 != null) {
                        ak ak0 = ad.d(viewGroup0);
                        arrayMap0.put(animator2, new a(view1, this.m, this, ak0, s6));
                        this.e.add(animator2);
                    }
                }
            }
            ++v1;
        }
        for(int v5 = 0; v5 < sparseIntArray0.size(); ++v5) {
            int v6 = sparseIntArray0.keyAt(v5);
            Animator animator3 = (Animator)this.e.get(v6);
            long v7 = ((long)sparseIntArray0.valueAt(v5)) - 0x7FFFFFFFFFFFFFFFL;
            animator3.setStartDelay(animator3.getStartDelay() + v7);
        }
    }

    public final void o(ViewGroup viewGroup0, boolean z) {
        this.p(z);
        ArrayList arrayList0 = this.i;
        ArrayList arrayList1 = this.j;
        if(arrayList0.size() <= 0 && arrayList1.size() <= 0) {
            this.D(viewGroup0, z);
            return;
        }
        for(int v1 = 0; v1 < arrayList0.size(); ++v1) {
            View view0 = viewGroup0.findViewById(((int)(((Integer)arrayList0.get(v1)))));
            if(view0 != null) {
                s s0 = new s();
                s0.b = view0;
                if(z) {
                    this.i(s0);
                }
                else {
                    this.y(s0);
                }
                s0.c.add(this);
                this.B(s0);
                m.j((z ? this.p : this.q), view0, s0);
            }
        }
        for(int v = 0; v < arrayList1.size(); ++v) {
            View view1 = (View)arrayList1.get(v);
            s s1 = new s();
            s1.b = view1;
            if(z) {
                this.i(s1);
            }
            else {
                this.y(s1);
            }
            s1.c.add(this);
            this.B(s1);
            m.j((z ? this.p : this.q), view1, s1);
        }
    }

    public final void p(boolean z) {
        t t0;
        if(z) {
            this.p.a.clear();
            this.p.b.clear();
            t0 = this.p;
        }
        else {
            this.q.a.clear();
            this.q.b.clear();
            t0 = this.q;
        }
        t0.c.clear();
    }

    public boolean q(s s0, s s1) {
        if(s0 != null && s1 != null) {
            String[] arr_s = this.t();
            if(arr_s == null) {
                Iterator iterator0 = s0.a.keySet().iterator();
                while(true) {
                    if(!iterator0.hasNext()) {
                        return false;
                    }
                    Object object0 = iterator0.next();
                    if(m.r(s0, s1, ((String)object0))) {
                        break;
                    }
                }
            }
            else {
                for(int v = 0; v < arr_s.length; ++v) {
                    if(m.r(s0, s1, arr_s[v])) {
                        return true;
                    }
                }
                return false;
            }
            return true;
        }
        return false;
    }

    public static boolean r(s s0, s s1, String s2) {
        Object object0 = s0.a.get(s2);
        Object object1 = s1.a.get(s2);
        if(object0 == null && object1 == null) {
            return false;
        }
        return object0 == null || object1 == null ? true : !object0.equals(object1);
    }

    public final boolean s(View view0) {
        int v = view0.getId();
        return this.i.size() != 0 || this.j.size() != 0 ? this.i.contains(v) || this.j.contains(view0) : true;
    }

    public String[] t() {
        return null;
    }

    @Override
    public final String toString() {
        return this.c("");
    }

    public void u() {
    }

    public final s v(View view0, boolean z) {
        q q0 = this.k;
        if(q0 != null) {
            return q0.v(view0, z);
        }
        ArrayList arrayList0 = z ? this.s : this.t;
        if(arrayList0 == null) {
            return null;
        }
        int v = arrayList0.size();
        int v1;
        for(v1 = 0; true; ++v1) {
            if(v1 >= v) {
                v1 = -1;
                break;
            }
            s s0 = (s)arrayList0.get(v1);
            if(s0 == null) {
                return null;
            }
            if(s0.b == view0) {
                break;
            }
        }
        if(v1 >= 0) {
            return z ? ((s)this.t.get(v1)) : ((s)this.s.get(v1));
        }
        return null;
    }

    public void w(long v) {
        this.n = v;
    }

    public void x(c m$c0) {
        ArrayList arrayList0 = this.d;
        if(arrayList0 == null) {
            return;
        }
        arrayList0.remove(m$c0);
        if(this.d.size() == 0) {
            this.d = null;
        }
    }

    public abstract void y(s arg1);

    public final void z(ArrayMap arrayMap0, ArrayMap arrayMap1) {
        for(int v1 = 0; v1 < arrayMap0.size(); ++v1) {
            s s0 = (s)arrayMap0.valueAt(v1);
            if(this.s(s0.b)) {
                this.s.add(s0);
                this.t.add(null);
            }
        }
        for(int v = 0; v < arrayMap1.size(); ++v) {
            s s1 = (s)arrayMap1.valueAt(v);
            if(this.s(s1.b)) {
                this.t.add(s1);
                this.s.add(null);
            }
        }
    }
}


package android.supportv1.g;

import android.view.View;
import android.view.WindowId;

class ak implements al {
    public final WindowId a;

    public ak(View view0) {
        this.a = view0.getWindowId();
    }

    // 去混淆评级： 低(20)
    @Override
    public final boolean equals(Object object0) {
        return object0 instanceof ak && ((ak)object0).a.equals(this.a);
    }

    @Override
    public final int hashCode() {
        return this.a.hashCode();
    }
}


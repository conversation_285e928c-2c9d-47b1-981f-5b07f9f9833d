package android.supportv1.g;

import android.view.View;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

class ag extends af {
    public static Method i;
    public static boolean j;

    @Override  // android.supportv1.g.ah
    public final void b(View view0, int v, int v1, int v2, int v3) {
        if(!ag.j) {
            try {
                Method method0 = View.class.getDeclaredMethod("setLeftTopRightBottom", Integer.TYPE, Integer.TYPE, Integer.TYPE, Integer.TYPE);
                ag.i = method0;
                method0.setAccessible(true);
            }
            catch(NoSuchMethodException unused_ex) {
            }
            ag.j = true;
        }
        Method method1 = ag.i;
        if(method1 != null) {
            try {
                method1.invoke(view0, v, v1, v2, v3);
                return;
            }
            catch(IllegalAccessException invocationTargetException0) {
            }
            catch(InvocationTargetException unused_ex) {
                return;
            }
            throw new RuntimeException(invocationTargetException0.getCause());
        }
    }
}


package android.supportv1.g;

import android.graphics.Matrix;
import android.view.View;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

class af extends ae {
    public static Method e;
    public static boolean f;
    public static Method g;
    public static boolean h;

    public final void c(View view0, Matrix matrix0) {
        if(!af.f) {
            try {
                Method method0 = View.class.getDeclaredMethod("transformMatrixToGlobal", Matrix.class);
                af.e = method0;
                method0.setAccessible(true);
            }
            catch(NoSuchMethodException unused_ex) {
            }
            af.f = true;
        }
        Method method1 = af.e;
        if(method1 != null) {
            try {
                method1.invoke(view0, matrix0);
                return;
            }
            catch(IllegalAccessException invocationTargetException0) {
            }
            catch(InvocationTargetException unused_ex) {
                return;
            }
            throw new RuntimeException(invocationTargetException0.getCause());
        }
    }

    public final void d(View view0, Matrix matrix0) {
        if(!af.h) {
            try {
                Method method0 = View.class.getDeclaredMethod("transformMatrixToLocal", Matrix.class);
                af.g = method0;
                method0.setAccessible(true);
            }
            catch(NoSuchMethodException unused_ex) {
            }
            af.h = true;
        }
        Method method1 = af.g;
        if(method1 != null) {
            try {
                method1.invoke(view0, matrix0);
                return;
            }
            catch(IllegalAccessException invocationTargetException0) {
            }
            catch(InvocationTargetException unused_ex) {
                return;
            }
            throw new RuntimeException(invocationTargetException0.getCause());
        }
    }
}


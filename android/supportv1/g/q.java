package android.supportv1.g;

import android.animation.TimeInterpolator;
import android.view.View;
import android.view.ViewGroup;
import androidx.work.impl.model.c;
import java.util.ArrayList;

public class q extends m {
    static class a extends n {
        public q a;

        @Override  // android.supportv1.g.m$c
        public final void a(m m0) {
            q q0 = this.a;
            int v = q0.x - 1;
            q0.x = v;
            if(v == 0) {
                q0.y = false;
                q0.I();
            }
            m0.x(this);
        }

        @Override  // android.supportv1.g.n
        public final void d() {
            q q0 = this.a;
            if(!q0.y) {
                q0.H();
                q0.y = true;
            }
        }
    }

    public boolean A;
    public int B;
    public int x;
    public boolean y;
    public ArrayList z;

    public q() {
        this.z = new ArrayList();
        this.A = true;
        this.y = false;
        this.B = 0;
    }

    @Override  // android.supportv1.g.m
    public final void A(View view0) {
        for(int v = 0; v < this.z.size(); ++v) {
            ((m)this.z.get(v)).A(view0);
        }
        this.j.add(view0);
    }

    @Override  // android.supportv1.g.m
    public final void B(s s0) {
        int v = this.z.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((m)this.z.get(v1)).B(s0);
        }
    }

    @Override  // android.supportv1.g.m
    public final void C(View view0) {
        for(int v = 0; v < this.z.size(); ++v) {
            ((m)this.z.get(v)).C(view0);
        }
        this.j.remove(view0);
    }

    @Override  // android.supportv1.g.m
    public final void E(View view0) {
        super.E(view0);
        int v = this.z.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((m)this.z.get(v1)).E(view0);
        }
    }

    @Override  // android.supportv1.g.m
    public final void F() {
        if(this.z.isEmpty()) {
            this.H();
            this.I();
            return;
        }
        a q$a0 = new a();  // 初始化器: Ljava/lang/Object;-><init>()V
        q$a0.a = this;
        for(Object object0: this.z) {
            ((m)object0).h(q$a0);
        }
        this.x = this.z.size();
        if(this.A) {
            for(Object object1: this.z) {
                ((m)object1).F();
            }
        }
        else {
            for(int v = 1; v < this.z.size(); ++v) {
                ((m)this.z.get(v - 1)).h(new q.1(((m)this.z.get(v))));
            }
            m m0 = (m)this.z.get(0);
            if(m0 != null) {
                m0.F();
            }
        }
    }

    @Override  // android.supportv1.g.m
    public final void G(ViewGroup viewGroup0) {
        super.G(viewGroup0);
        int v = this.z.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((m)this.z.get(v1)).G(viewGroup0);
        }
    }

    @Override  // android.supportv1.g.m
    public final m J() {
        m m0 = (q)super.J();
        m0.z = new ArrayList();
        int v = this.z.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((q)m0).L(((m)this.z.get(v1)).J());
        }
        return m0;
    }

    public final void L(m m0) {
        this.z.add(m0);
        m0.k = this;
        long v = this.h;
        if(v >= 0L) {
            m0.d(v);
        }
        if((this.B & 1) != 0) {
            m0.e(this.o);
        }
        if((this.B & 2) != 0) {
            m0.u();
        }
        if((this.B & 4) != 0) {
            m0.f(this.g);
        }
        if((this.B & 8) != 0) {
            m0.g(this.f);
        }
    }

    @Override  // android.supportv1.g.m
    public final String c(String s) {
        String s1 = super.c(s);
        for(int v = 0; v < this.z.size(); ++v) {
            StringBuilder stringBuilder0 = c.o(s1, "\n");
            stringBuilder0.append(((m)this.z.get(v)).c(s + "  "));
            s1 = stringBuilder0.toString();
        }
        return s1;
    }

    @Override  // android.supportv1.g.m
    public final Object clone() {
        return this.J();
    }

    @Override  // android.supportv1.g.m
    public final void d(long v) {
        this.h = v;
        if(v >= 0L) {
            int v1 = this.z.size();
            for(int v2 = 0; v2 < v1; ++v2) {
                ((m)this.z.get(v2)).d(v);
            }
        }
    }

    @Override  // android.supportv1.g.m
    public final void e(TimeInterpolator timeInterpolator0) {
        this.B |= 1;
        ArrayList arrayList0 = this.z;
        if(arrayList0 != null) {
            int v = arrayList0.size();
            for(int v1 = 0; v1 < v; ++v1) {
                ((m)this.z.get(v1)).e(timeInterpolator0);
            }
        }
        this.o = timeInterpolator0;
    }

    @Override  // android.supportv1.g.m
    public final void f(g g0) {
        super.f(g0);
        this.B |= 4;
        for(int v = 0; v < this.z.size(); ++v) {
            ((m)this.z.get(v)).f(g0);
        }
    }

    @Override  // android.supportv1.g.m
    public final void g(b m$b0) {
        this.f = m$b0;
        this.B |= 8;
        int v = this.z.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((m)this.z.get(v1)).g(m$b0);
        }
    }

    @Override  // android.supportv1.g.m
    public final void h(android.supportv1.g.m.c m$c0) {
        super.h(m$c0);
    }

    @Override  // android.supportv1.g.m
    public final void i(s s0) {
        if(this.s(s0.b)) {
            for(Object object0: this.z) {
                m m0 = (m)object0;
                if(m0.s(s0.b)) {
                    m0.i(s0);
                    s0.c.add(m0);
                }
            }
        }
    }

    @Override  // android.supportv1.g.m
    public final void n(ViewGroup viewGroup0, t t0, t t1, ArrayList arrayList0, ArrayList arrayList1) {
        long v = this.n;
        int v1 = this.z.size();
        for(int v2 = 0; v2 < v1; ++v2) {
            m m0 = (m)this.z.get(v2);
            if(v > 0L && (this.A || v2 == 0)) {
                long v3 = m0.n;
                if(v3 > 0L) {
                    m0.w(v3 + v);
                }
                else {
                    m0.w(v);
                }
            }
            m0.n(viewGroup0, t0, t1, arrayList0, arrayList1);
        }
    }

    @Override  // android.supportv1.g.m
    public final void u() {
        this.B |= 2;
        int v = this.z.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((m)this.z.get(v1)).u();
        }
    }

    @Override  // android.supportv1.g.m
    public final void w(long v) {
        this.n = v;
    }

    @Override  // android.supportv1.g.m
    public final void x(android.supportv1.g.m.c m$c0) {
        super.x(m$c0);
    }

    @Override  // android.supportv1.g.m
    public final void y(s s0) {
        if(this.s(s0.b)) {
            for(Object object0: this.z) {
                m m0 = (m)object0;
                if(m0.s(s0.b)) {
                    m0.y(s0);
                    s0.c.add(m0);
                }
            }
        }
    }
}


package android.supportv1.g;

import android.view.View;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

abstract class ae extends ah {
    public static Method a;
    public static boolean b;
    public static Method c;
    public static boolean d;

    @Override  // android.supportv1.g.ah
    public final float a(View view0) {
        if(!ae.d) {
            try {
                Method method0 = View.class.getDeclaredMethod("getTransitionAlpha");
                ae.c = method0;
                method0.setAccessible(true);
            }
            catch(NoSuchMethodException unused_ex) {
            }
            ae.d = true;
        }
        Method method1 = ae.c;
        if(method1 != null) {
            try {
                return (float)(((Float)method1.invoke(view0)));
            }
            catch(IllegalAccessException invocationTargetException0) {
            }
            catch(InvocationTargetException unused_ex) {
                return super.a(view0);
            }
            throw new RuntimeException(invocationTargetException0.getCause());
        }
        return super.a(view0);
    }
}


package android.supportv1.g;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.graphics.Bitmap.Config;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Picture;
import android.graphics.RectF;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.ImageView.ScaleType;
import android.widget.ImageView;
import java.util.HashMap;

public abstract class ai extends m {
    static class a extends AnimatorListenerAdapter implements c {
        public boolean a;
        public final View b;
        public final int c;
        public final ViewGroup d;
        public final boolean e;
        public boolean f;

        public a(View view0, int v) {
            this.a = false;
            this.b = view0;
            this.c = v;
            this.d = (ViewGroup)view0.getParent();
            this.e = true;
            this.b(true);
        }

        @Override  // android.supportv1.g.m$c
        public final void a(m m0) {
            if(!this.a) {
                ad.a(this.c, this.b);
                ViewGroup viewGroup0 = this.d;
                if(viewGroup0 != null) {
                    viewGroup0.invalidate();
                }
            }
            this.b(false);
            m0.x(this);
        }

        @Override  // android.supportv1.g.m$c
        public final void b() {
            this.b(false);
        }

        public final void b(boolean z) {
            if(this.e && this.f != z) {
                ViewGroup viewGroup0 = this.d;
                if(viewGroup0 != null) {
                    this.f = z;
                    x.a(viewGroup0, z);
                }
            }
        }

        @Override  // android.supportv1.g.m$c
        public final void c() {
            this.b(true);
        }

        @Override  // android.supportv1.g.m$c
        public final void d() {
        }

        @Override  // android.animation.AnimatorListenerAdapter
        public final void onAnimationCancel(Animator animator0) {
            this.a = true;
        }

        @Override  // android.animation.AnimatorListenerAdapter
        public final void onAnimationEnd(Animator animator0) {
            if(!this.a) {
                ad.a(this.c, this.b);
                ViewGroup viewGroup0 = this.d;
                if(viewGroup0 != null) {
                    viewGroup0.invalidate();
                }
            }
            this.b(false);
        }

        @Override  // android.animation.AnimatorListenerAdapter
        public final void onAnimationPause(Animator animator0) {
            if(!this.a) {
                ad.a(this.c, this.b);
            }
        }

        @Override  // android.animation.AnimatorListenerAdapter
        public final void onAnimationRepeat(Animator animator0) {
        }

        @Override  // android.animation.AnimatorListenerAdapter
        public final void onAnimationResume(Animator animator0) {
            if(!this.a) {
                ad.a(0, this.b);
            }
        }

        @Override  // android.animation.AnimatorListenerAdapter
        public final void onAnimationStart(Animator animator0) {
        }
    }

    static class b {
        public boolean a;
        public boolean b;
        public int c;
        public int d;
        public ViewGroup e;
        public ViewGroup f;

    }

    public int x;
    public static final String[] y;

    static {
        ai.y = new String[]{"android:visibility:visibility", "android:visibility:parent"};
    }

    public static b L(s s0, s s1) {
        b ai$b0 = new b();  // 初始化器: Ljava/lang/Object;-><init>()V
        ai$b0.a = false;
        ai$b0.b = false;
        ViewGroup viewGroup0 = null;
        if(s0 == null) {
            ai$b0.c = -1;
            ai$b0.e = null;
        }
        else {
            HashMap hashMap0 = s0.a;
            if(hashMap0.containsKey("android:visibility:visibility")) {
                ai$b0.c = (int)(((Integer)hashMap0.get("android:visibility:visibility")));
                ai$b0.e = (ViewGroup)hashMap0.get("android:visibility:parent");
            }
            else {
                ai$b0.c = -1;
                ai$b0.e = null;
            }
        }
        if(s1 == null) {
            ai$b0.d = -1;
        }
        else {
            HashMap hashMap1 = s1.a;
            if(hashMap1.containsKey("android:visibility:visibility")) {
                ai$b0.d = (int)(((Integer)hashMap1.get("android:visibility:visibility")));
                viewGroup0 = (ViewGroup)hashMap1.get("android:visibility:parent");
            }
            else {
                ai$b0.d = -1;
            }
        }
        ai$b0.f = viewGroup0;
        if(s0 == null || s1 == null) {
            if(s0 == null && ai$b0.d == 0) {
                ai$b0.b = true;
                ai$b0.a = true;
                return ai$b0;
            }
            if(s1 == null && ai$b0.c == 0) {
                ai$b0.b = false;
                ai$b0.a = true;
            }
        }
        else {
            int v = ai$b0.c;
            int v1 = ai$b0.d;
            if(v == v1 && ai$b0.e == ai$b0.f) {
                return ai$b0;
            }
            if(v == v1) {
                if(ai$b0.f == null) {
                    ai$b0.b = false;
                    ai$b0.a = true;
                    return ai$b0;
                }
                else if(ai$b0.e == null) {
                    ai$b0.b = true;
                    ai$b0.a = true;
                    return ai$b0;
                }
            }
            else if(v == 0) {
                ai$b0.b = false;
                ai$b0.a = true;
                return ai$b0;
            }
            else if(v1 == 0) {
                ai$b0.b = true;
                ai$b0.a = true;
                return ai$b0;
            }
        }
        return ai$b0;
    }

    public static void M(s s0) {
        Integer integer0 = s0.b.getVisibility();
        s0.a.put("android:visibility:visibility", integer0);
        ViewParent viewParent0 = s0.b.getParent();
        s0.a.put("android:visibility:parent", viewParent0);
        int[] arr_v = new int[2];
        s0.b.getLocationOnScreen(arr_v);
        s0.a.put("android:visibility:screenLocation", arr_v);
    }

    @Override  // android.supportv1.g.m
    public final Animator a(ViewGroup viewGroup0, s s0, s s1) {
        float f3;
        Animator animator0;
        View view4;
        int v1;
        int v8;
        ViewGroup viewGroup1;
        Bitmap bitmap0;
        boolean z;
        int v6;
        float f1;
        float f = 0.0f;
        b ai$b0 = ai.L(s0, s1);
        if(!ai$b0.a || ai$b0.e == null && ai$b0.f == null) {
            return null;
        }
        if(ai$b0.b) {
            if((this.x & 1) == 1 && s1 != null) {
                if(s0 != null) {
                    goto label_9;
                }
                View view0 = (View)s1.b.getParent();
                if(!ai.L(this.v(view0, false), this.b(view0, false)).a) {
                label_9:
                    View view1 = s1.b;
                    if(s0 == null) {
                        f1 = 0.0f;
                    }
                    else {
                        Float float0 = (Float)s0.a.get("android:fade:transitionAlpha");
                        f1 = float0 == null ? 0.0f : ((float)float0);
                    }
                    if(f1 != 1.0f) {
                        f = f1;
                    }
                    return ((d)this).N(f, 1.0f, view1);
                }
            }
            return null;
        }
        int v = ai$b0.d;
        if((this.x & 2) == 2) {
            View view2 = s0 == null ? null : s0.b;
            View view3 = s1 == null ? null : s1.b;
            if(view3 == null || view3.getParent() == null) {
                if(view3 != null) {
                    view2 = view3;
                    v1 = v;
                    view3 = null;
                }
                else if(view2 == null) {
                    v1 = v;
                    view3 = null;
                    view2 = null;
                }
                else if(view2.getParent() == null) {
                    v1 = v;
                    view3 = null;
                }
                else if(view2.getParent() instanceof View) {
                    view4 = (View)view2.getParent();
                    if(ai.L(this.b(view4, true), this.v(view4, true)).a) {
                        v1 = v;
                        if(view4.getParent() == null) {
                            int v13 = view4.getId();
                            if(v13 != -1) {
                                viewGroup0.findViewById(v13);
                            }
                        }
                        view2 = null;
                    }
                    else {
                    label_42:
                        Matrix matrix0 = new Matrix();
                        matrix0.setTranslate(((float)(-view4.getScrollX())), ((float)(-view4.getScrollY())));
                        ad.b.c(view2, matrix0);
                        ad.b.d(viewGroup0, matrix0);
                        RectF rectF0 = new RectF(0.0f, 0.0f, ((float)view2.getWidth()), ((float)view2.getHeight()));
                        matrix0.mapRect(rectF0);
                        int v2 = Math.round(rectF0.left);
                        int v3 = Math.round(rectF0.top);
                        int v4 = Math.round(rectF0.right);
                        int v5 = Math.round(rectF0.bottom);
                        ImageView imageView0 = new ImageView(view2.getContext());
                        imageView0.setScaleType(ImageView.ScaleType.CENTER_CROP);
                        if(r.a) {
                            v6 = !view2.isAttachedToWindow();
                            z = viewGroup0 == null ? false : viewGroup0.isAttachedToWindow();
                        }
                        else {
                            v6 = 0;
                            z = false;
                        }
                        boolean z1 = r.b;
                        if(!z1 || v6 == 0) {
                            v8 = 0;
                            viewGroup1 = null;
                        label_76:
                            v1 = v;
                            int v9 = Math.round(rectF0.width());
                            int v10 = Math.round(rectF0.height());
                            if(v9 <= 0 || v10 <= 0) {
                                bitmap0 = null;
                            }
                            else {
                                float f2 = Math.min(1.0f, 1048576.0f / ((float)(v9 * v10)));
                                int v11 = Math.round(((float)v9) * f2);
                                int v12 = Math.round(((float)v10) * f2);
                                matrix0.postTranslate(-rectF0.left, -rectF0.top);
                                matrix0.postScale(f2, f2);
                                if(r.c) {
                                    Picture picture0 = new Picture();
                                    Canvas canvas0 = picture0.beginRecording(v11, v12);
                                    canvas0.concat(matrix0);
                                    view2.draw(canvas0);
                                    picture0.endRecording();
                                    bitmap0 = Bitmap.createBitmap(picture0);
                                }
                                else {
                                    bitmap0 = Bitmap.createBitmap(v11, v12, Bitmap.Config.ARGB_8888);
                                    Canvas canvas1 = new Canvas(bitmap0);
                                    canvas1.concat(matrix0);
                                    view2.draw(canvas1);
                                }
                            }
                            if(z1 && v6 != 0) {
                                viewGroup0.getOverlay().remove(view2);
                                viewGroup1.addView(view2, v8);
                            }
                        }
                        else if(!z) {
                            v1 = v;
                            bitmap0 = null;
                        }
                        else {
                            viewGroup1 = (ViewGroup)view2.getParent();
                            int v7 = viewGroup1.indexOfChild(view2);
                            viewGroup0.getOverlay().add(view2);
                            v8 = v7;
                            goto label_76;
                        }
                        if(bitmap0 != null) {
                            imageView0.setImageBitmap(bitmap0);
                        }
                        imageView0.measure(View.MeasureSpec.makeMeasureSpec(v4 - v2, 0x40000000), View.MeasureSpec.makeMeasureSpec(v5 - v3, 0x40000000));
                        imageView0.layout(v2, v3, v4, v5);
                        view2 = imageView0;
                    }
                    view3 = null;
                }
                else {
                    v1 = v;
                    view3 = null;
                    view2 = null;
                }
            }
            else if(v == 4 || view2 == view3) {
                v1 = v;
                view2 = null;
            }
            else {
                view4 = (View)view2.getParent();
                goto label_42;
            }
            if(view2 != null && s0 != null) {
                int[] arr_v = (int[])s0.a.get("android:visibility:screenLocation");
                int v14 = arr_v[0];
                int v15 = arr_v[1];
                int[] arr_v1 = new int[2];
                viewGroup0.getLocationOnScreen(arr_v1);
                view2.offsetLeftAndRight(v14 - arr_v1[0] - view2.getLeft());
                view2.offsetTopAndBottom(v15 - arr_v1[1] - view2.getTop());
                v v16 = new v(viewGroup0);
                v16.a.add(view2);
                ad.b.getClass();
                Float float1 = (Float)s0.a.get("android:fade:transitionAlpha");
                animator0 = ((d)this).N((float1 == null ? 1.0f : ((float)float1)), 0.0f, view2);
                if(animator0 == null) {
                    v16.a(view2);
                    return null;
                }
                animator0.addListener(new ai.1(v16, view2));
                return animator0;
            }
            if(view3 != null) {
                int v17 = view3.getVisibility();
                ad.a(0, view3);
                ad.b.getClass();
                if(s0 == null) {
                    f3 = 1.0f;
                }
                else {
                    Float float2 = (Float)s0.a.get("android:fade:transitionAlpha");
                    f3 = float2 == null ? 1.0f : ((float)float2);
                }
                animator0 = ((d)this).N(f3, 0.0f, view3);
                if(animator0 != null) {
                    a ai$a0 = new a(view3, v1);
                    animator0.addListener(ai$a0);
                    animator0.addPauseListener(ai$a0);
                    this.h(ai$a0);
                    return animator0;
                }
                ad.a(v17, view3);
                return null;
            }
        }
        return null;
    }

    @Override  // android.supportv1.g.m
    public final boolean q(s s0, s s1) {
        if(s0 == null && s1 == null) {
            return false;
        }
        if(s0 != null && s1 != null && s1.a.containsKey("android:visibility:visibility") != s0.a.containsKey("android:visibility:visibility")) {
            return false;
        }
        b ai$b0 = ai.L(s0, s1);
        return ai$b0.a && (ai$b0.c == 0 || ai$b0.d == 0);
    }

    @Override  // android.supportv1.g.m
    public final String[] t() {
        return ai.y;
    }

    @Override  // android.supportv1.g.m
    public final void y(s s0) {
        ai.M(s0);
    }
}


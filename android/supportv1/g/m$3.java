package android.supportv1.g;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;

class m.3 extends AnimatorListenerAdapter {
    public final m a;

    public m.3(m m0) {
        this.a = m0;
        super();
    }

    @Override  // android.animation.AnimatorListenerAdapter
    public final void onAnimationEnd(Animator animator0) {
        this.a.I();
        animator0.removeListener(this);
    }
}


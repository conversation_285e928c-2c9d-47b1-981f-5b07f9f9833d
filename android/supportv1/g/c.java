package android.supportv1.g;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.supportv1.v4.view.ViewCompat;
import android.util.Property;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import java.util.HashMap;

public class c extends m {
    static class a {
        public int a;
        public int b;
        public int c;
        public int d;
        public View e;
        public int f;
        public int g;

        public final void a() {
            ad.c(this.e, this.a, this.b, this.c, this.d);
            this.f = 0;
            this.g = 0;
        }
    }

    public static final Property A;
    public static final Property B;
    public static final Property C;
    public static final String[] x;
    public static final Property y;
    public static final Property z;

    static {
        c.x = new String[]{"android:changeBounds:bounds", "android:changeBounds:clip", "android:changeBounds:parent", "android:changeBounds:windowX", "android:changeBounds:windowY"};
        android.supportv1.g.c.1 c$10 = new Property(PointF.class, "boundsOrigin") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            public Rect a;

            @Override  // android.util.Property
            public final Object get(Object object0) {
                Rect rect0 = this.a;
                ((Drawable)object0).copyBounds(rect0);
                return new PointF(((float)rect0.left), ((float)rect0.top));
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                Rect rect0 = this.a;
                ((Drawable)object0).copyBounds(rect0);
                rect0.offsetTo(Math.round(((PointF)object1).x), Math.round(((PointF)object1).y));
                ((Drawable)object0).setBounds(rect0);
            }
        };
        c$10.a = new Rect();
        c.y = new Property(PointF.class, "topLeft") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            @Override  // android.util.Property
            public final Object get(Object object0) {
                a c$a0 = (a)object0;
                return null;
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                ((a)object0).getClass();
                ((a)object0).a = Math.round(((PointF)object1).x);
                ((a)object0).b = Math.round(((PointF)object1).y);
                int v = ((a)object0).f + 1;
                ((a)object0).f = v;
                if(v == ((a)object0).g) {
                    ((a)object0).a();
                }
            }
        };
        c.z = new Property(PointF.class, "bottomRight") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            @Override  // android.util.Property
            public final Object get(Object object0) {
                a c$a0 = (a)object0;
                return null;
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                ((a)object0).getClass();
                ((a)object0).c = Math.round(((PointF)object1).x);
                ((a)object0).d = Math.round(((PointF)object1).y);
                int v = ((a)object0).g + 1;
                ((a)object0).g = v;
                if(((a)object0).f == v) {
                    ((a)object0).a();
                }
            }
        };
        c.A = new Property(PointF.class, "bottomRight") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            @Override  // android.util.Property
            public final Object get(Object object0) {
                View view0 = (View)object0;
                return null;
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                ad.c(((View)object0), ((View)object0).getLeft(), ((View)object0).getTop(), Math.round(((PointF)object1).x), Math.round(((PointF)object1).y));
            }
        };
        c.B = new Property(PointF.class, "topLeft") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            @Override  // android.util.Property
            public final Object get(Object object0) {
                View view0 = (View)object0;
                return null;
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                ad.c(((View)object0), Math.round(((PointF)object1).x), Math.round(((PointF)object1).y), ((View)object0).getRight(), ((View)object0).getBottom());
            }
        };
        c.C = new Property(PointF.class, "position") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            @Override  // android.util.Property
            public final Object get(Object object0) {
                View view0 = (View)object0;
                return null;
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                int v = Math.round(((PointF)object1).x);
                int v1 = Math.round(((PointF)object1).y);
                ad.c(((View)object0), v, v1, ((View)object0).getWidth() + v, ((View)object0).getHeight() + v1);
            }
        };
    }

    public static void L(s s0) {
        View view0 = s0.b;
        if(ViewCompat.isLaidOut(view0) || view0.getWidth() != 0 || view0.getHeight() != 0) {
            Rect rect0 = new Rect(view0.getLeft(), view0.getTop(), view0.getRight(), view0.getBottom());
            s0.a.put("android:changeBounds:bounds", rect0);
            ViewParent viewParent0 = s0.b.getParent();
            s0.a.put("android:changeBounds:parent", viewParent0);
        }
    }

    @Override  // android.supportv1.g.m
    public final Animator a(ViewGroup viewGroup0, s s0, s s1) {
        Animator animator0;
        Path path0;
        Property property0;
        int v12;
        if(s0 != null && s1 != null) {
            HashMap hashMap0 = s0.a;
            HashMap hashMap1 = s1.a;
            if(((ViewGroup)hashMap0.get("android:changeBounds:parent")) != null && ((ViewGroup)hashMap1.get("android:changeBounds:parent")) != null) {
                View view0 = s1.b;
                Rect rect0 = (Rect)hashMap0.get("android:changeBounds:bounds");
                Rect rect1 = (Rect)hashMap1.get("android:changeBounds:bounds");
                int v = rect0.left;
                int v1 = rect1.left;
                int v2 = rect0.top;
                int v3 = rect1.top;
                int v4 = rect0.right;
                int v5 = rect1.right;
                int v6 = rect0.bottom;
                int v7 = rect1.bottom;
                int v8 = v4 - v;
                int v9 = v6 - v2;
                int v10 = v5 - v1;
                int v11 = v7 - v3;
                Rect rect2 = (Rect)hashMap0.get("android:changeBounds:clip");
                Rect rect3 = (Rect)hashMap1.get("android:changeBounds:clip");
                if((v8 == 0 || v9 == 0) && (v10 == 0 || v11 == 0)) {
                    v12 = 0;
                }
                else {
                    v12 = v != v1 || v2 != v3 ? 1 : 0;
                    if(v4 != v5 || v6 != v7) {
                        ++v12;
                    }
                }
                if(rect2 != null && !rect2.equals(rect3) || rect2 == null && rect3 != null) {
                    ++v12;
                }
                if(v12 > 0) {
                    ad.c(view0, v, v2, v4, v6);
                    if(v12 != 2) {
                        if(v != v1 || v2 != v3) {
                            path0 = this.g.a(((float)v), ((float)v2), ((float)v1), ((float)v3));
                            property0 = c.B;
                        }
                        else {
                            path0 = this.g.a(((float)v4), ((float)v6), ((float)v5), ((float)v7));
                            property0 = c.A;
                        }
                        animator0 = ObjectAnimator.ofObject(view0, property0, null, path0);
                    }
                    else if(v8 == v10 && v9 == v11) {
                        path0 = this.g.a(((float)v), ((float)v2), ((float)v1), ((float)v3));
                        animator0 = ObjectAnimator.ofObject(view0, c.C, null, path0);
                    }
                    else {
                        a c$a0 = new a();  // 初始化器: Ljava/lang/Object;-><init>()V
                        c$a0.e = view0;
                        Path path1 = this.g.a(((float)v), ((float)v2), ((float)v1), ((float)v3));
                        ObjectAnimator objectAnimator0 = ObjectAnimator.ofObject(c$a0, c.y, null, path1);
                        Path path2 = this.g.a(((float)v4), ((float)v6), ((float)v5), ((float)v7));
                        ObjectAnimator objectAnimator1 = ObjectAnimator.ofObject(c$a0, c.z, null, path2);
                        animator0 = new AnimatorSet();
                        ((AnimatorSet)animator0).playTogether(new Animator[]{objectAnimator0, objectAnimator1});
                        animator0.addListener(new c.8());  // 初始化器: Landroid/animation/AnimatorListenerAdapter;-><init>()V
                    }
                    if(view0.getParent() instanceof ViewGroup) {
                        ViewGroup viewGroup1 = (ViewGroup)view0.getParent();
                        x.a(viewGroup1, true);
                        this.h(new c.10(viewGroup1));
                    }
                    return animator0;
                }
            }
        }
        return null;
    }

    @Override  // android.supportv1.g.m
    public final void i(s s0) {
        c.L(s0);
    }

    @Override  // android.supportv1.g.m
    public final String[] t() {
        return c.x;
    }

    @Override  // android.supportv1.g.m
    public final void y(s s0) {
        c.L(s0);
    }
}


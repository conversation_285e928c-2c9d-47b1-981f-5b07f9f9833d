package android.supportv1.g;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;

class ai.1 extends AnimatorListenerAdapter {
    public final w a;
    public final View b;

    public ai.1(v v0, View view0) {
        this.a = v0;
        this.b = view0;
        super();
    }

    @Override  // android.animation.AnimatorListenerAdapter
    public final void onAnimationEnd(Animator animator0) {
        this.a.a(this.b);
    }
}


package android.supportv1.g;

import java.util.ArrayList;

class e.3 implements c {
    public final Object a;
    public final ArrayList b;
    public final Object c;
    public final ArrayList d;
    public final Object e;
    public final ArrayList f;
    public final e g;

    public e.3(e e0, Object object0, ArrayList arrayList0, Object object1, ArrayList arrayList1, Object object2, ArrayList arrayList2) {
        this.g = e0;
        this.a = object0;
        this.b = arrayList0;
        this.c = object1;
        this.d = arrayList1;
        this.e = object2;
        this.f = arrayList2;
    }

    @Override  // android.supportv1.g.m$c
    public final void a(m m0) {
    }

    @Override  // android.supportv1.g.m$c
    public final void b() {
    }

    @Override  // android.supportv1.g.m$c
    public final void c() {
    }

    @Override  // android.supportv1.g.m$c
    public final void d() {
        e e0 = this.g;
        Object object0 = this.a;
        if(object0 != null) {
            e0.replaceTargets(object0, this.b, null);
        }
        Object object1 = this.c;
        if(object1 != null) {
            e0.replaceTargets(object1, this.d, null);
        }
        Object object2 = this.e;
        if(object2 != null) {
            e0.replaceTargets(object2, this.f, null);
        }
    }
}


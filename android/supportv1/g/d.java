package android.supportv1.g;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.supportv1.v4.view.ViewCompat;
import android.view.View;

public class d extends ai {
    static class a extends AnimatorListenerAdapter {
        public final View a;
        public boolean b;

        public a(View view0) {
            this.b = false;
            this.a = view0;
        }

        @Override  // android.animation.AnimatorListenerAdapter
        public final void onAnimationEnd(Animator animator0) {
            View view0 = this.a;
            ad.b(view0, 1.0f);
            if(this.b) {
                view0.setLayerType(0, null);
            }
        }

        @Override  // android.animation.AnimatorListenerAdapter
        public final void onAnimationStart(Animator animator0) {
            View view0 = this.a;
            if(ViewCompat.hasOverlappingRendering(view0) && view0.getLayerType() == 0) {
                this.b = true;
                view0.setLayerType(2, null);
            }
        }
    }

    public d(int v) {
        this.x = v;
    }

    public final ObjectAnimator N(float f, float f1, View view0) {
        if(f == f1) {
            return null;
        }
        ad.b(view0, f);
        ObjectAnimator objectAnimator0 = ObjectAnimator.ofFloat(view0, ad.a, new float[]{f1});
        objectAnimator0.addListener(new a(view0));
        this.h(new d.1(view0));
        return objectAnimator0;
    }

    @Override  // android.supportv1.g.m
    public final void i(s s0) {
        ai.M(s0);
        Float float0 = ad.b.a(s0.b);
        s0.a.put("android:fade:transitionAlpha", float0);
    }
}


package android.supportv1.g;

import android.graphics.Rect;
import android.os.Build.VERSION;
import android.supportv1.v4.view.ViewCompat;
import android.util.Property;
import android.view.View;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

abstract class ad {
    public static final Property a;
    public static final af b;
    public static Field c;
    public static boolean d;

    static {
        af af0 = Build.VERSION.SDK_INT >= 22 ? new ag() : new af();  // 初始化器: Ljava/lang/Object;-><init>()V / 初始化器: Ljava/lang/Object;-><init>()V
        ad.b = af0;
        ad.a = new Property(Float.class, "translationAlpha") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            @Override  // android.util.Property
            public final Object get(Object object0) {
                return ad.b.a(((View)object0));
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                ad.b(((View)object0), ((float)(((Float)object1))));
            }
        };
        new Property(Rect.class, "clipBounds") {  // 初始化器: Landroid/util/Property;-><init>(Ljava/lang/Class;Ljava/lang/String;)V
            @Override  // android.util.Property
            public final Object get(Object object0) {
                return ViewCompat.getClipBounds(((View)object0));
            }

            @Override  // android.util.Property
            public final void set(Object object0, Object object1) {
                ViewCompat.setClipBounds(((View)object0), ((Rect)object1));
            }
        };
    }

    public static void a(int v, View view0) {
        if(!ad.d) {
            try {
                Field field0 = View.class.getDeclaredField("mViewFlags");
                ad.c = field0;
                field0.setAccessible(true);
            }
            catch(NoSuchFieldException unused_ex) {
            }
            ad.d = true;
        }
        Field field1 = ad.c;
        if(field1 != null) {
            try {
                int v1 = field1.getInt(view0);
                ad.c.setInt(view0, v | v1 & -13);
            }
            catch(IllegalAccessException unused_ex) {
            }
        }
    }

    public static void b(View view0, float f) {
        ad.b.getClass();
        if(!ae.b) {
            try {
                Method method0 = View.class.getDeclaredMethod("setTransitionAlpha", Float.TYPE);
                ae.a = method0;
                method0.setAccessible(true);
            }
            catch(NoSuchMethodException unused_ex) {
            }
            ae.b = true;
        }
        Method method1 = ae.a;
        if(method1 != null) {
            try {
                method1.invoke(view0, f);
                return;
            }
            catch(IllegalAccessException invocationTargetException0) {
            }
            catch(InvocationTargetException unused_ex) {
                return;
            }
            throw new RuntimeException(invocationTargetException0.getCause());
        }
        view0.setAlpha(f);
    }

    public static void c(View view0, int v, int v1, int v2, int v3) {
        ad.b.b(view0, v, v1, v2, v3);
    }

    public static ak d(View view0) {
        return new ak(view0);
    }
}


package android.supportv1.g;

import android.view.ViewGroup;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

abstract class x {
    public static void a(ViewGroup viewGroup0, boolean z) {
        if(!z.b) {
            try {
                Method method0 = ViewGroup.class.getDeclaredMethod("suppressLayout", Boolean.TYPE);
                z.a = method0;
                method0.setAccessible(true);
            }
            catch(NoSuchMethodException unused_ex) {
            }
            z.b = true;
        }
        Method method1 = z.a;
        if(method1 != null) {
            try {
                method1.invoke(viewGroup0, Boolean.valueOf(z));
            }
            catch(IllegalAccessException | InvocationTargetException unused_ex) {
            }
        }
    }
}


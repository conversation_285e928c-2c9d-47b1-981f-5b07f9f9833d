package android.supportv1.g;

import android.view.View;

abstract class ah {
    public float a(View view0) {
        Float float0 = (Float)view0.getTag(0x7F090226);
        float f = view0.getAlpha();
        return float0 == null ? f : f / ((float)float0);
    }

    public void b(View view0, int v, int v1, int v2, int v3) {
        view0.setLeft(v);
        view0.setTop(v1);
        view0.setRight(v2);
        view0.setBottom(v3);
    }
}


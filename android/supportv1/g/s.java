package android.supportv1.g;

import android.view.View;
import androidx.work.impl.model.c;
import java.util.ArrayList;
import java.util.HashMap;

public class s {
    public final HashMap a;
    public View b;
    public final ArrayList c;

    public s() {
        this.a = new HashMap();
        this.c = new ArrayList();
    }

    // 去混淆评级： 低(20)
    @Override
    public final boolean equals(Object object0) {
        return object0 instanceof s && this.b == ((s)object0).b && this.a.equals(((s)object0).a);
    }

    @Override
    public final int hashCode() {
        return this.a.hashCode() + this.b.hashCode() * 0x1F;
    }

    @Override
    public final String toString() {
        StringBuilder stringBuilder0 = c.o(("TransitionValues@" + Integer.toHexString(this.hashCode()) + ":\n"), "    view = ");
        stringBuilder0.append(this.b);
        stringBuilder0.append("\n");
        String s = stringBuilder0.toString() + "    values:";
        HashMap hashMap0 = this.a;
        for(Object object0: hashMap0.keySet()) {
            s = s + "    " + ((String)object0) + ": " + hashMap0.get(((String)object0)) + "\n";
        }
        return s;
    }
}


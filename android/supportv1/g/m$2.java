package android.supportv1.g;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.supportv1.v4.util.ArrayMap;

class m.2 extends AnimatorListenerAdapter {
    public final ArrayMap a;
    public final m b;

    public m.2(m m0, ArrayMap arrayMap0) {
        this.b = m0;
        this.a = arrayMap0;
        super();
    }

    @Override  // android.animation.AnimatorListenerAdapter
    public final void onAnimationEnd(Animator animator0) {
        this.a.remove(animator0);
        this.b.l.remove(animator0);
    }

    @Override  // android.animation.AnimatorListenerAdapter
    public final void onAnimationStart(Animator animator0) {
        this.b.l.add(animator0);
    }
}


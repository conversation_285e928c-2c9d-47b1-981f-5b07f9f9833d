package android.supportv1.g;

import android.graphics.Rect;
import android.supportv1.v4.app.FragmentTransitionImpl;
import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;

public class e extends FragmentTransitionImpl {
    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void addTarget(Object object0, View view0) {
        if(object0 != null) {
            ((m)object0).A(view0);
        }
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void addTargets(Object object0, ArrayList arrayList0) {
        int v = 0;
        if(((m)object0) == null) {
            return;
        }
        if(((m)object0) instanceof q) {
            int v1 = ((q)(((m)object0))).z.size();
            while(v < v1) {
                this.addTargets((v < 0 || v >= ((q)(((m)object0))).z.size() ? null : ((m)((q)(((m)object0))).z.get(v))), arrayList0);
                ++v;
            }
            return;
        }
        if(FragmentTransitionImpl.isNullOrEmpty(((m)object0).i) && FragmentTransitionImpl.isNullOrEmpty(((m)object0).j)) {
            int v2 = arrayList0.size();
            while(v < v2) {
                ((m)object0).A(((View)arrayList0.get(v)));
                ++v;
            }
        }
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void beginDelayedTransition(ViewGroup viewGroup0, Object object0) {
        o.b(((m)object0), viewGroup0);
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final boolean canHandle(Object object0) {
        return object0 instanceof m;
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final Object cloneTransition(Object object0) {
        return object0 != null ? ((m)object0).J() : null;
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final Object mergeTransitionsInSequence(Object object0, Object object1, Object object2) {
        m m0 = (m)object0;
        if(m0 != null && ((m)object1) != null) {
            q q0 = new q();
            q0.L(m0);
            q0.L(((m)object1));
            q0.A = false;
            m0 = q0;
        }
        else if(m0 == null) {
            m0 = ((m)object1) == null ? null : ((m)object1);
        }
        if(((m)object2) != null) {
            q q1 = new q();
            if(m0 != null) {
                q1.L(m0);
            }
            q1.L(((m)object2));
            return q1;
        }
        return m0;
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final Object mergeTransitionsTogether(Object object0, Object object1, Object object2) {
        q q0 = new q();
        if(object0 != null) {
            q0.L(((m)object0));
        }
        if(object1 != null) {
            q0.L(((m)object1));
        }
        if(object2 != null) {
            q0.L(((m)object2));
        }
        return q0;
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void removeTarget(Object object0, View view0) {
        if(object0 != null) {
            ((m)object0).C(view0);
        }
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void replaceTargets(Object object0, ArrayList arrayList0, ArrayList arrayList1) {
        int v = 0;
        if(((m)object0) instanceof q) {
            int v1 = ((q)(((m)object0))).z.size();
            while(v < v1) {
                this.replaceTargets((v < 0 || v >= ((q)(((m)object0))).z.size() ? null : ((m)((q)(((m)object0))).z.get(v))), arrayList0, arrayList1);
                ++v;
            }
            return;
        }
        if(FragmentTransitionImpl.isNullOrEmpty(((m)object0).i)) {
            ArrayList arrayList2 = ((m)object0).j;
            if(arrayList2.size() == arrayList0.size() && arrayList2.containsAll(arrayList0)) {
                int v2 = arrayList1 == null ? 0 : arrayList1.size();
                while(v < v2) {
                    ((m)object0).A(((View)arrayList1.get(v)));
                    ++v;
                }
                for(int v3 = arrayList0.size() - 1; v3 >= 0; --v3) {
                    ((m)object0).C(((View)arrayList0.get(v3)));
                }
            }
        }
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void scheduleHideFragmentView(Object object0, View view0, ArrayList arrayList0) {
        ((m)object0).h(new e.2(view0, arrayList0));
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void scheduleRemoveTargets(Object object0, Object object1, ArrayList arrayList0, Object object2, ArrayList arrayList1, Object object3, ArrayList arrayList2) {
        ((m)object0).h(new e.3(this, object1, arrayList0, object2, arrayList1, object3, arrayList2));
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void setEpicenter(Object object0, Rect rect0) {
        if(object0 != null) {
            ((m)object0).g(new e.4());  // 初始化器: Ljava/lang/Object;-><init>()V
        }
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void setEpicenter(Object object0, View view0) {
        if(view0 != null) {
            this.getBoundsOnScreen(view0, new Rect());
            ((m)object0).g(new e.1());  // 初始化器: Ljava/lang/Object;-><init>()V
        }
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void setSharedElementTargets(Object object0, View view0, ArrayList arrayList0) {
        ArrayList arrayList1 = ((q)object0).j;
        arrayList1.clear();
        int v = arrayList0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            FragmentTransitionImpl.bfsAddViewChildren(arrayList1, ((View)arrayList0.get(v1)));
        }
        arrayList1.add(view0);
        arrayList0.add(view0);
        this.addTargets(((q)object0), arrayList0);
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final void swapSharedElementTargets(Object object0, ArrayList arrayList0, ArrayList arrayList1) {
        if(((q)object0) != null) {
            ((q)object0).j.clear();
            ((q)object0).j.addAll(arrayList1);
            this.replaceTargets(((q)object0), arrayList0, arrayList1);
        }
    }

    @Override  // android.supportv1.v4.app.FragmentTransitionImpl
    public final Object wrapTransitionInSet(Object object0) {
        if(object0 == null) {
            return null;
        }
        q q0 = new q();
        q0.L(((m)object0));
        return q0;
    }
}


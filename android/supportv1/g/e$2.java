package android.supportv1.g;

import android.view.View;
import java.util.ArrayList;

class e.2 implements c {
    public final View a;
    public final ArrayList b;

    public e.2(View view0, ArrayList arrayList0) {
        this.a = view0;
        this.b = arrayList0;
    }

    @Override  // android.supportv1.g.m$c
    public final void a(m m0) {
        m0.x(this);
        this.a.setVisibility(8);
        ArrayList arrayList0 = this.b;
        int v = arrayList0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((View)arrayList0.get(v1)).setVisibility(0);
        }
    }

    @Override  // android.supportv1.g.m$c
    public final void b() {
    }

    @Override  // android.supportv1.g.m$c
    public final void c() {
    }

    @Override  // android.supportv1.g.m$c
    public final void d() {
    }
}


package android.supportv1.v7.app;

import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.os.Bundle;
import android.supportv1.v4.view.KeyEventDispatcher.Component;
import android.supportv1.v4.view.KeyEventDispatcher;
import android.supportv1.v4.view.LayoutInflaterCompat;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v7.appcompat.R.attr;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.widget.TextView;

public abstract class AppCompatDialog extends Dialog implements AppCompatCallback {
    public AppCompatDelegate a;
    public final Component b;

    public AppCompatDialog(Context context0, int v) {
        super(context0, AppCompatDialog.b(v, context0));
        this.b = (KeyEvent keyEvent0) -> super.dispatchKeyEvent(keyEvent0);
        this.a().c(null);
        this.a().b();
    }

    public final AppCompatDelegate a() {
        if(this.a == null) {
            this.a = new AppCompatDelegateImpl(this.getContext(), this.getWindow(), this);
        }
        return this.a;
    }

    @Override  // android.app.Dialog
    public final void addContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        appCompatDelegateImpl0.m();
        ((ViewGroup)appCompatDelegateImpl0.x.findViewById(0x1020002)).addView(view0, viewGroup$LayoutParams0);
        appCompatDelegateImpl0.p.onContentChanged();
    }

    public static int b(int v, Context context0) {
        if(v == 0) {
            TypedValue typedValue0 = new TypedValue();
            context0.getTheme().resolveAttribute(R.attr.dialogTheme, typedValue0, true);
            return typedValue0.resourceId;
        }
        return v;
    }

    // 检测为 Lambda 实现
    public final boolean c(KeyEvent keyEvent0) [...]

    public final void d() {
        this.a().d(1);
    }

    @Override  // android.app.Dialog
    public final boolean dispatchKeyEvent(KeyEvent keyEvent0) {
        View view0 = this.getWindow().getDecorView();
        return KeyEventDispatcher.dispatchKeyEvent(this.b, view0, this, keyEvent0);
    }

    @Override  // android.app.Dialog
    public final View findViewById(int v) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        appCompatDelegateImpl0.m();
        return appCompatDelegateImpl0.D.findViewById(v);
    }

    @Override  // android.app.Dialog
    public final void invalidateOptionsMenu() {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        appCompatDelegateImpl0.o();
        appCompatDelegateImpl0.j |= 1;
        if(!appCompatDelegateImpl0.k) {
            ViewCompat.postOnAnimation(appCompatDelegateImpl0.D.getDecorView(), appCompatDelegateImpl0.l);
            appCompatDelegateImpl0.k = true;
        }
    }

    @Override  // android.app.Dialog
    public void onCreate(Bundle bundle0) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        LayoutInflater layoutInflater0 = LayoutInflater.from(appCompatDelegateImpl0.g);
        if(layoutInflater0.getFactory() == null) {
            LayoutInflaterCompat.setFactory2(layoutInflater0, appCompatDelegateImpl0);
        }
        else {
            layoutInflater0.getFactory2();
        }
        super.onCreate(bundle0);
        this.a().c(bundle0);
    }

    @Override  // android.app.Dialog
    public final void onStop() {
        super.onStop();
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        appCompatDelegateImpl0.o();
        AutoNightModeManager appCompatDelegateImpl$AutoNightModeManager0 = appCompatDelegateImpl0.f;
        if(appCompatDelegateImpl$AutoNightModeManager0 != null) {
            BroadcastReceiver broadcastReceiver0 = appCompatDelegateImpl$AutoNightModeManager0.a;
            if(broadcastReceiver0 != null) {
                appCompatDelegateImpl$AutoNightModeManager0.e.g.unregisterReceiver(broadcastReceiver0);
                appCompatDelegateImpl$AutoNightModeManager0.a = null;
            }
        }
    }

    @Override  // android.app.Dialog
    public final void setContentView(int v) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        appCompatDelegateImpl0.m();
        ViewGroup viewGroup0 = (ViewGroup)appCompatDelegateImpl0.x.findViewById(0x1020002);
        viewGroup0.removeAllViews();
        LayoutInflater.from(appCompatDelegateImpl0.g).inflate(v, viewGroup0);
        appCompatDelegateImpl0.p.onContentChanged();
    }

    @Override  // android.app.Dialog
    public final void setContentView(View view0) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        appCompatDelegateImpl0.m();
        ViewGroup viewGroup0 = (ViewGroup)appCompatDelegateImpl0.x.findViewById(0x1020002);
        viewGroup0.removeAllViews();
        viewGroup0.addView(view0);
        appCompatDelegateImpl0.p.onContentChanged();
    }

    @Override  // android.app.Dialog
    public final void setContentView(View view0, ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        appCompatDelegateImpl0.m();
        ViewGroup viewGroup0 = (ViewGroup)appCompatDelegateImpl0.x.findViewById(0x1020002);
        viewGroup0.removeAllViews();
        viewGroup0.addView(view0, viewGroup$LayoutParams0);
        appCompatDelegateImpl0.p.onContentChanged();
    }

    @Override  // android.app.Dialog
    public final void setTitle(int v) {
        super.setTitle(v);
        AppCompatDelegate appCompatDelegate0 = this.a();
        String s = this.getContext().getString(v);
        ((AppCompatDelegateImpl)appCompatDelegate0).B = s;
        TextView textView0 = ((AppCompatDelegateImpl)appCompatDelegate0).C;
        if(textView0 != null) {
            textView0.setText(s);
        }
    }

    @Override  // android.app.Dialog
    public void setTitle(CharSequence charSequence0) {
        super.setTitle(charSequence0);
        AppCompatDelegateImpl appCompatDelegateImpl0 = (AppCompatDelegateImpl)this.a();
        appCompatDelegateImpl0.B = charSequence0;
        TextView textView0 = appCompatDelegateImpl0.C;
        if(textView0 != null) {
            textView0.setText(charSequence0);
        }
    }

    class android.supportv1.v7.app.AppCompatDialog.1 implements Component {
        public final AppCompatDialog a;

        public android.supportv1.v7.app.AppCompatDialog.1(AlertDialog alertDialog0) {
            this.a = alertDialog0;
        }

        @Override  // android.supportv1.v4.view.KeyEventDispatcher$Component
        public final boolean superDispatchKeyEvent(KeyEvent keyEvent0) {
            return this.a.c(keyEvent0);
        }
    }

}


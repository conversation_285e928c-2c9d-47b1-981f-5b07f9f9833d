package android.supportv1.v7.app;

import android.view.View;

class AlertController.3 implements Runnable {
    public final AlertController a;
    public final View b;
    public final View c;

    public AlertController.3(AlertController alertController0, View view0, View view1) {
        this.a = alertController0;
        this.c = view0;
        this.b = view1;
    }

    @Override
    public final void run() {
        AlertController.b(this.a.A, this.c, this.b);
    }
}


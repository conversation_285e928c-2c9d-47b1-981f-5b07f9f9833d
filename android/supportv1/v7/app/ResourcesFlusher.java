package android.supportv1.v7.app;

import android.util.LongSparseArray;
import java.lang.reflect.Field;

abstract class ResourcesFlusher {
    public static Field a;
    public static boolean b;
    public static Field c;
    public static boolean d;
    public static Class e;
    public static boolean f;
    public static Field g;
    public static boolean h;

    public static void a(Object object0) {
        LongSparseArray longSparseArray0;
        if(!ResourcesFlusher.f) {
            try {
                ResourcesFlusher.e = Class.forName("android.content.res.ThemedResourceCache");
            }
            catch(ClassNotFoundException unused_ex) {
            }
            ResourcesFlusher.f = true;
        }
        Class class0 = ResourcesFlusher.e;
        if(class0 == null) {
            return;
        }
        if(!ResourcesFlusher.h) {
            try {
                Field field0 = class0.getDeclaredField("mUnthemedEntries");
                ResourcesFlusher.g = field0;
                field0.setAccessible(true);
            }
            catch(NoSuchFieldException unused_ex) {
            }
            ResourcesFlusher.h = true;
        }
        Field field1 = ResourcesFlusher.g;
        if(field1 == null) {
            return;
        }
        try {
            longSparseArray0 = null;
            longSparseArray0 = (LongSparseArray)field1.get(object0);
        }
        catch(IllegalAccessException unused_ex) {
        }
        if(longSparseArray0 != null) {
            longSparseArray0.clear();
        }
    }
}


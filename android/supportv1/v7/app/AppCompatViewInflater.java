package android.supportv1.v7.app;

import a.a;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.TypedArray;
import android.supportv1.v4.util.ArrayMap;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.view.ContextThemeWrapper;
import android.supportv1.v7.widget.AppCompatAutoCompleteTextView;
import android.supportv1.v7.widget.AppCompatButton;
import android.supportv1.v7.widget.AppCompatCheckBox;
import android.supportv1.v7.widget.AppCompatCheckedTextView;
import android.supportv1.v7.widget.AppCompatEditText;
import android.supportv1.v7.widget.AppCompatImageButton;
import android.supportv1.v7.widget.AppCompatImageView;
import android.supportv1.v7.widget.AppCompatMultiAutoCompleteTextView;
import android.supportv1.v7.widget.AppCompatRadioButton;
import android.supportv1.v7.widget.AppCompatRatingBar;
import android.supportv1.v7.widget.AppCompatSeekBar;
import android.supportv1.v7.widget.AppCompatSpinner;
import android.supportv1.v7.widget.AppCompatTextView;
import android.util.AttributeSet;
import android.view.View.OnClickListener;
import android.view.View;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class AppCompatViewInflater {
    static class DeclaredOnClickListener implements View.OnClickListener {
        public final View a;
        public final String b;
        public Context c;
        public Method d;

        public DeclaredOnClickListener(View view0, String s) {
            this.a = view0;
            this.b = s;
        }

        @Override  // android.view.View$OnClickListener
        public final void onClick(View view0) {
            if(this.d == null) {
                View view1 = this.a;
                Context context0 = view1.getContext();
                while(true) {
                    String s = this.b;
                    if(context0 != null) {
                        try {
                            if(!context0.isRestricted()) {
                                Method method0 = context0.getClass().getMethod(s, View.class);
                                if(method0 != null) {
                                    this.d = method0;
                                    this.c = context0;
                                    break;
                                }
                            }
                        }
                        catch(NoSuchMethodException unused_ex) {
                        }
                        if(context0 instanceof ContextWrapper) {
                            context0 = ((ContextWrapper)context0).getBaseContext();
                            continue;
                        }
                        context0 = null;
                        continue;
                    }
                    int v = view1.getId();
                    String s1 = v == -1 ? "" : " with id \'" + view1.getContext().getResources().getResourceEntryName(v) + "\'";
                    StringBuilder stringBuilder0 = a.y("Could not find method ", s, "(View) in a parent or ancestor Context for android:onClick attribute defined on view ");
                    stringBuilder0.append(view1.getClass());
                    stringBuilder0.append(s1);
                    throw new IllegalStateException(stringBuilder0.toString());
                }
            }
            try {
                this.d.invoke(this.c, view0);
            }
            catch(IllegalAccessException illegalAccessException0) {
                throw new IllegalStateException("Could not execute non-public method for android:onClick", illegalAccessException0);
            }
            catch(InvocationTargetException invocationTargetException0) {
                throw new IllegalStateException("Could not execute method for android:onClick", invocationTargetException0);
            }
        }
    }

    public final Object[] a;
    public static final String[] b;
    public static final ArrayMap c;
    public static final Class[] d;
    public static final int[] e;

    static {
        AppCompatViewInflater.d = new Class[]{Context.class, AttributeSet.class};
        AppCompatViewInflater.e = new int[]{0x101026F};
        AppCompatViewInflater.b = new String[]{"android.widget.", "android.view.", "android.webkit."};
        AppCompatViewInflater.c = new ArrayMap();
    }

    public AppCompatViewInflater() {
        this.a = new Object[2];
    }

    public final View a(View view0, String s, Context context0, AttributeSet attributeSet0) {
        View view3;
        View view2;
        int v;
        Context context1 = AppCompatViewInflater.c(context0, attributeSet0);
        s.getClass();
        switch(s) {
            case "AutoCompleteTextView": {
                v = 9;
                break;
            }
            case "Button": {
                v = 12;
                break;
            }
            case "CheckBox": {
                v = 10;
                break;
            }
            case "CheckedTextView": {
                v = 1;
                break;
            }
            case "EditText": {
                v = 11;
                break;
            }
            case "ImageButton": {
                v = 4;
                break;
            }
            case "ImageView": {
                v = 8;
                break;
            }
            case "MultiAutoCompleteTextView": {
                v = 2;
                break;
            }
            case "RadioButton": {
                v = 7;
                break;
            }
            case "RatingBar": {
                v = 0;
                break;
            }
            case "SeekBar": {
                v = 5;
                break;
            }
            case "Spinner": {
                v = 6;
                break;
            }
            case "TextView": {
                v = 3;
                break;
            }
            default: {
                v = -1;
            }
        }
        View view1 = null;
        switch(v) {
            case 0: {
                view2 = new AppCompatRatingBar(context1, attributeSet0);
                break;
            }
            case 1: {
                view2 = new AppCompatCheckedTextView(context1, attributeSet0);
                break;
            }
            case 2: {
                view2 = new AppCompatMultiAutoCompleteTextView(context1, attributeSet0);
                break;
            }
            case 3: {
                view2 = new AppCompatTextView(context1, attributeSet0);
                break;
            }
            case 4: {
                view2 = new AppCompatImageButton(context1, attributeSet0);
                break;
            }
            case 5: {
                view2 = new AppCompatSeekBar(context1, attributeSet0);
                break;
            }
            case 6: {
                view2 = new AppCompatSpinner(context1, attributeSet0);
                break;
            }
            case 7: {
                view2 = new AppCompatRadioButton(context1, attributeSet0);
                break;
            }
            case 8: {
                view2 = new AppCompatImageView(context1, attributeSet0, 0);
                break;
            }
            case 9: {
                view2 = new AppCompatAutoCompleteTextView(context1, attributeSet0);
                break;
            }
            case 10: {
                view2 = new AppCompatCheckBox(context1, attributeSet0);
                break;
            }
            case 11: {
                view2 = new AppCompatEditText(context1, attributeSet0);
                break;
            }
            case 12: {
                view2 = new AppCompatButton(context1, attributeSet0);
                break;
            }
            default: {
                view2 = null;
            }
        }
        if(view2 == null && context0 != context1) {
            if(s.equals("view")) {
                s = attributeSet0.getAttributeValue(null, "class");
            }
            Object[] arr_object = this.a;
            arr_object[0] = context1;
            arr_object[1] = attributeSet0;
            try {
                if(-1 == s.indexOf(46)) {
                    int v2 = 0;
                    while(true) {
                        String[] arr_s = AppCompatViewInflater.b;
                        if(v2 >= 3) {
                            break;
                        }
                        view3 = this.b(context1, s, arr_s[v2]);
                        if(view3 != null) {
                            goto label_74;
                        }
                        ++v2;
                    }
                }
                else {
                    goto label_82;
                }
                goto label_88;
            }
            catch(Exception unused_ex) {
                goto label_86;
            }
            finally {
                arr_object[0] = null;
                arr_object[1] = null;
            }
        label_74:
            view1 = view3;
            try {
                goto label_88;
            label_82:
                View view4 = this.b(context1, s, null);
                view1 = view4;
            }
            catch(Exception unused_ex) {
            label_86:
                arr_object[0] = null;
                arr_object[1] = null;
            }
        label_88:
            view2 = view1;
        }
        if(view2 != null) {
            Context context2 = view2.getContext();
            if(context2 instanceof ContextWrapper && ViewCompat.hasOnClickListeners(view2)) {
                TypedArray typedArray0 = context2.obtainStyledAttributes(attributeSet0, AppCompatViewInflater.e);
                String s1 = typedArray0.getString(0);
                if(s1 != null) {
                    view2.setOnClickListener(new DeclaredOnClickListener(view2, s1));
                }
                typedArray0.recycle();
            }
        }
        return view2;
    }

    public final View b(Context context0, String s, String s1) {
        ArrayMap arrayMap0 = AppCompatViewInflater.c;
        Constructor constructor0 = (Constructor)arrayMap0.get(s);
        try {
            if(constructor0 == null) {
                constructor0 = context0.getClassLoader().loadClass((s1 == null ? s : s1 + s)).asSubclass(View.class).getConstructor(AppCompatViewInflater.d);
                arrayMap0.put(s, constructor0);
            }
            constructor0.setAccessible(true);
            return (View)constructor0.newInstance(this.a);
        }
        catch(Exception unused_ex) {
            return null;
        }
    }

    public static Context c(Context context0, AttributeSet attributeSet0) {
        TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.View, 0, 0);
        int v = typedArray0.getResourceId(R.styleable.View_theme, 0);
        typedArray0.recycle();
        return v != 0 && (!(context0 instanceof ContextThemeWrapper) || ((ContextThemeWrapper)context0).a() != v) ? new ContextThemeWrapper(context0, v) : context0;
    }
}


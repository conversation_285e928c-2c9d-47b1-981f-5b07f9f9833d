package android.supportv1.v7.app;

import android.app.Activity;
import android.app.Dialog;
import android.supportv1.v4.view.ViewPropertyAnimatorListener;
import android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter;
import android.supportv1.v4.view.ViewPropertyAnimatorUpdateListener;
import android.supportv1.v7.appcompat.R.id;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import h.a;
import java.util.ArrayList;

public class WindowDecorActionBar extends ActionBar {
    public final boolean a;
    public final ViewPropertyAnimatorListener b;
    public final ViewPropertyAnimatorListener c;
    public final ViewPropertyAnimatorUpdateListener d;

    static {
        new AccelerateInterpolator();
        new DecelerateInterpolator();
    }

    public WindowDecorActionBar(Activity activity0) {
        new ArrayList();
        new ArrayList();
        this.a = true;
        new ViewPropertyAnimatorListenerAdapter() {
            public final WindowDecorActionBar a;

            @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter
            public final void onAnimationEnd(View view0) {
                WindowDecorActionBar windowDecorActionBar0 = WindowDecorActionBar.this;
                if(windowDecorActionBar0.a) {
                    windowDecorActionBar0.getClass();
                }
                windowDecorActionBar0.getClass();
                throw null;
            }
        };
        new ViewPropertyAnimatorListenerAdapter() {
            public final WindowDecorActionBar a;

            @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter
            public final void onAnimationEnd(View view0) {
                WindowDecorActionBar.this.getClass();
                throw null;
            }
        };
        WindowDecorActionBar.a(activity0.getWindow().getDecorView());
        throw null;
    }

    public WindowDecorActionBar(Dialog dialog0) {
        new ArrayList();
        new ArrayList();
        this.a = true;
        new ViewPropertyAnimatorListenerAdapter() {
            public final WindowDecorActionBar a;

            @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter
            public final void onAnimationEnd(View view0) {
                WindowDecorActionBar windowDecorActionBar0 = WindowDecorActionBar.this;
                if(windowDecorActionBar0.a) {
                    windowDecorActionBar0.getClass();
                }
                windowDecorActionBar0.getClass();
                throw null;
            }
        };
        new ViewPropertyAnimatorListenerAdapter() {
            public final WindowDecorActionBar a;

            @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter
            public final void onAnimationEnd(View view0) {
                WindowDecorActionBar.this.getClass();
                throw null;
            }
        };
        WindowDecorActionBar.a(dialog0.getWindow().getDecorView());
        throw null;
    }

    public static void a(View view0) {
        a.f(view0.findViewById(R.id.decor_content_parent));
        View view1 = view0.findViewById(R.id.action_bar);
        throw new IllegalStateException("Can\'t make a decor toolbar out of " + (view1 == null ? "null" : view1.getClass().getSimpleName()));
    }

    class android.supportv1.v7.app.WindowDecorActionBar.3 implements ViewPropertyAnimatorUpdateListener {
        public final WindowDecorActionBar a;

        public android.supportv1.v7.app.WindowDecorActionBar.3() {
            this.a = windowDecorActionBar0;
        }

        @Override  // android.supportv1.v4.view.ViewPropertyAnimatorUpdateListener
        public final void onAnimationUpdate(View view0) {
            this.a.getClass();
            throw null;
        }
    }

}


package android.supportv1.v7.app;

import android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter;
import android.view.View;

class AppCompatDelegateImpl.6.1 extends ViewPropertyAnimatorListenerAdapter {
    public final AppCompatDelegateImpl.6 a;

    public AppCompatDelegateImpl.6.1(AppCompatDelegateImpl.6 appCompatDelegateImpl$60) {
        this.a = appCompatDelegateImpl$60;
        super();
    }

    @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter
    public final void onAnimationEnd(View view0) {
        this.a.a.c.setAlpha(1.0f);
        this.a.a.h.setListener(null);
        this.a.a.h = null;
    }

    @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter
    public final void onAnimationStart(View view0) {
        this.a.a.c.setVisibility(0);
    }
}


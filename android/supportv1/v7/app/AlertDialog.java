package android.supportv1.v7.app;

import android.content.Context;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface.OnKeyListener;
import android.content.DialogInterface;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.supportv1.v4.widget.NestedScrollView;
import android.supportv1.v7.appcompat.R.attr;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.TextView;
import h.a;

public class AlertDialog extends AppCompatDialog implements DialogInterface {
    public static class Builder {
        public final AlertParams a;
        public final int b;

        public Builder(Context context0) {
            int v = AlertDialog.e(0, context0);
            super();
            this.a = new AlertParams(new ContextThemeWrapper(context0, AlertDialog.e(v, context0)));
            this.b = v;
        }

        public final AlertDialog a() {
            AlertParams alertController$AlertParams0 = this.a;
            AlertDialog alertDialog0 = new AlertDialog(alertController$AlertParams0.b, this.b);
            View view0 = alertController$AlertParams0.c;
            AlertController alertController0 = alertDialog0.c;
            if(view0 == null) {
                CharSequence charSequence0 = alertController$AlertParams0.m;
                if(charSequence0 != null) {
                    alertController0.C = charSequence0;
                    TextView textView0 = alertController0.D;
                    if(textView0 != null) {
                        textView0.setText(charSequence0);
                    }
                }
                Drawable drawable0 = alertController$AlertParams0.d;
                if(drawable0 != null) {
                    alertController0.t = drawable0;
                    alertController0.u = 0;
                    ImageView imageView0 = alertController0.v;
                    if(imageView0 != null) {
                        imageView0.setVisibility(0);
                        alertController0.v.setImageDrawable(drawable0);
                    }
                }
            }
            else {
                alertController0.q = view0;
            }
            CharSequence charSequence1 = alertController$AlertParams0.f;
            if(charSequence1 != null) {
                alertController0.y = charSequence1;
                TextView textView1 = alertController0.z;
                if(textView1 != null) {
                    textView1.setText(charSequence1);
                }
            }
            CharSequence charSequence2 = alertController$AlertParams0.l;
            if(charSequence2 != null) {
                alertController0.d(-1, charSequence2, alertController$AlertParams0.k);
            }
            CharSequence charSequence3 = alertController$AlertParams0.h;
            if(charSequence3 != null) {
                alertController0.d(-2, charSequence3, alertController$AlertParams0.g);
            }
            if(alertController$AlertParams0.a != null) {
                a.f(alertController$AlertParams0.e.inflate(alertController0.x, null));
                if(alertController$AlertParams0.a == null) {
                    new CheckedItemAdapter(alertController$AlertParams0.b, alertController0.w, 0x1020014, null);  // 初始化器: Landroid/widget/ArrayAdapter;-><init>(Landroid/content/Context;II[Ljava/lang/Object;)V
                }
                if(alertController$AlertParams0.i != null) {
                    throw null;
                }
            }
            View view1 = alertController$AlertParams0.n;
            if(view1 != null) {
                alertController0.E = view1;
                alertController0.F = 0;
                alertController0.G = false;
            }
            alertDialog0.setCancelable(true);
            alertDialog0.setCanceledOnTouchOutside(true);
            alertDialog0.setOnCancelListener(null);
            alertDialog0.setOnDismissListener(null);
            DialogInterface.OnKeyListener dialogInterface$OnKeyListener0 = alertController$AlertParams0.j;
            if(dialogInterface$OnKeyListener0 != null) {
                alertDialog0.setOnKeyListener(dialogInterface$OnKeyListener0);
            }
            return alertDialog0;
        }

        public final Context b() {
            return this.a.b;
        }

        public final void c(ListAdapter listAdapter0, DialogInterface.OnClickListener dialogInterface$OnClickListener0) {
            this.a.a = listAdapter0;
            this.a.i = dialogInterface$OnClickListener0;
        }

        public final void d(View view0) {
            this.a.c = view0;
        }

        public final void e(Drawable drawable0) {
            this.a.d = drawable0;
        }

        public final void f(DialogInterface.OnKeyListener dialogInterface$OnKeyListener0) {
            this.a.j = dialogInterface$OnKeyListener0;
        }

        public final void g(CharSequence charSequence0) {
            this.a.m = charSequence0;
        }
    }

    public final AlertController c;

    public AlertDialog(Context context0, int v) {
        super(context0, AlertDialog.e(v, context0));
        this.c = new AlertController(this.getContext(), this, this.getWindow());
    }

    public static int e(int v, Context context0) {
        if((v >>> 24 & 0xFF) >= 1) {
            return v;
        }
        TypedValue typedValue0 = new TypedValue();
        context0.getTheme().resolveAttribute(R.attr.alertDialogTheme, typedValue0, true);
        return typedValue0.resourceId;
    }

    @Override  // android.supportv1.v7.app.AppCompatDialog
    public final void onCreate(Bundle bundle0) {
        super.onCreate(bundle0);
        this.c.r.setContentView(this.c.a);
        this.c.i();
    }

    @Override  // android.app.Dialog
    public final boolean onKeyDown(int v, KeyEvent keyEvent0) {
        NestedScrollView nestedScrollView0 = this.c.A;
        return nestedScrollView0 == null || !nestedScrollView0.executeKeyEvent(keyEvent0) ? super.onKeyDown(v, keyEvent0) : true;
    }

    @Override  // android.app.Dialog
    public final boolean onKeyUp(int v, KeyEvent keyEvent0) {
        NestedScrollView nestedScrollView0 = this.c.A;
        return nestedScrollView0 == null || !nestedScrollView0.executeKeyEvent(keyEvent0) ? super.onKeyUp(v, keyEvent0) : true;
    }

    @Override  // android.supportv1.v7.app.AppCompatDialog
    public final void setTitle(CharSequence charSequence0) {
        super.setTitle(charSequence0);
        this.c.C = charSequence0;
        TextView textView0 = this.c.D;
        if(textView0 != null) {
            textView0.setText(charSequence0);
        }
    }
}


package android.supportv1.v7.app;

import android.content.Context;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface.OnKeyListener;
import android.content.DialogInterface;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.Handler;
import android.os.Message;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.widget.NestedScrollView;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.appcompat.R.id;
import android.supportv1.v7.appcompat.R.styleable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View.OnClickListener;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewStub;
import android.view.Window;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout.LayoutParams;
import android.widget.ListAdapter;
import android.widget.TextView;
import java.lang.ref.WeakReference;

class AlertController {
    public static class AlertParams {
        public ListAdapter a;
        public final Context b;
        public View c;
        public Drawable d;
        public final LayoutInflater e;
        public CharSequence f;
        public DialogInterface.OnClickListener g;
        public CharSequence h;
        public DialogInterface.OnClickListener i;
        public DialogInterface.OnKeyListener j;
        public DialogInterface.OnClickListener k;
        public CharSequence l;
        public CharSequence m;
        public View n;

        public AlertParams(ContextThemeWrapper contextThemeWrapper0) {
            this.b = contextThemeWrapper0;
            this.e = (LayoutInflater)contextThemeWrapper0.getSystemService("layout_inflater");
        }
    }

    static final class ButtonHandler extends Handler {
        public final WeakReference a;

        public ButtonHandler(DialogInterface dialogInterface0) {
            this.a = new WeakReference(dialogInterface0);
        }

        @Override  // android.os.Handler
        public final void handleMessage(Message message0) {
            switch(message0.what) {
                case -3: 
                case -2: 
                case -1: {
                    ((DialogInterface.OnClickListener)message0.obj).onClick(((DialogInterface)this.a.get()), message0.what);
                    return;
                }
                case 1: {
                    ((DialogInterface)message0.obj).dismiss();
                }
            }
        }
    }

    static class CheckedItemAdapter extends ArrayAdapter {
        @Override  // android.widget.ArrayAdapter
        public final long getItemId(int v) {
            return (long)v;
        }

        @Override  // android.widget.BaseAdapter, android.widget.Adapter
        public final boolean hasStableIds() {
            return true;
        }
    }

    public NestedScrollView A;
    public final boolean B;
    public CharSequence C;
    public TextView D;
    public View E;
    public int F;
    public boolean G;
    public final Window H;
    public final int a;
    public final View.OnClickListener b;
    public final int c;
    public Button d;
    public Drawable e;
    public Message f;
    public CharSequence g;
    public Button h;
    public Drawable i;
    public Message j;
    public CharSequence k;
    public Button l;
    public Drawable m;
    public Message n;
    public CharSequence o;
    public final Context p;
    public View q;
    public final AppCompatDialog r;
    public final Handler s;
    public Drawable t;
    public int u;
    public ImageView v;
    public final int w;
    public final int x;
    public CharSequence y;
    public TextView z;

    public AlertController(Context context0, AppCompatDialog appCompatDialog0, Window window0) {
        this.G = false;
        this.u = 0;
        this.b = new View.OnClickListener() {
            public final AlertController a;

            {
                this.a = alertController0;
            }

            @Override  // android.view.View$OnClickListener
            public final void onClick(View view0) {
                Message message1;
                Message message0;
                AlertController alertController0 = this.a;
                if(view0 == alertController0.l) {
                    message0 = alertController0.n;
                    if(message0 != null) {
                        message1 = Message.obtain(message0);
                        goto label_19;
                    }
                    goto label_6;
                }
                else {
                label_6:
                    if(view0 == alertController0.d) {
                        message0 = alertController0.f;
                        if(message0 != null) {
                            message1 = Message.obtain(message0);
                            goto label_19;
                        }
                        goto label_11;
                    }
                    else {
                    label_11:
                        if(view0 == alertController0.h) {
                            message0 = alertController0.j;
                            message1 = message0 == null ? null : Message.obtain(message0);
                        }
                        else {
                            message1 = null;
                        }
                    }
                }
            label_19:
                if(message1 != null) {
                    message1.sendToTarget();
                }
                alertController0.s.obtainMessage(1, alertController0.r).sendToTarget();
            }
        };
        this.p = context0;
        this.r = appCompatDialog0;
        this.H = window0;
        this.s = new ButtonHandler(appCompatDialog0);
        TypedArray typedArray0 = context0.obtainStyledAttributes(null, R.styleable.AlertDialog, R.attr.alertDialogStyle, 0);
        this.a = typedArray0.getResourceId(R.styleable.AlertDialog_android_layout, 0);
        typedArray0.getResourceId(R.styleable.AlertDialog_buttonPanelSideLayout, 0);
        this.x = typedArray0.getResourceId(R.styleable.AlertDialog_listLayout, 0);
        typedArray0.getResourceId(R.styleable.AlertDialog_multiChoiceItemLayout, 0);
        typedArray0.getResourceId(R.styleable.AlertDialog_singleChoiceItemLayout, 0);
        this.w = typedArray0.getResourceId(R.styleable.AlertDialog_listItemLayout, 0);
        this.B = typedArray0.getBoolean(R.styleable.AlertDialog_showTitle, true);
        this.c = typedArray0.getDimensionPixelSize(R.styleable.AlertDialog_buttonIconDimen, 0);
        typedArray0.recycle();
        appCompatDialog0.d();
    }

    public static boolean a(View view0) {
        if(view0.onCheckIsTextEditor()) {
            return true;
        }
        if(!(view0 instanceof ViewGroup)) {
            return false;
        }
        int v = ((ViewGroup)view0).getChildCount();
        while(v > 0) {
            --v;
            if(AlertController.a(((ViewGroup)view0).getChildAt(v))) {
                return true;
            }
            if(false) {
                break;
            }
        }
        return false;
    }

    // 检测为 Lambda 实现
    public static void b(View view0, View view1, View view2) [...]

    public static ViewGroup c(View view0, View view1) {
        if(view0 == null) {
            if(view1 instanceof ViewStub) {
                view1 = ((ViewStub)view1).inflate();
            }
            return (ViewGroup)view1;
        }
        if(view1 != null) {
            ViewParent viewParent0 = view1.getParent();
            if(viewParent0 instanceof ViewGroup) {
                ((ViewGroup)viewParent0).removeView(view1);
            }
        }
        if(view0 instanceof ViewStub) {
            view0 = ((ViewStub)view0).inflate();
        }
        return (ViewGroup)view0;
    }

    public final void d(int v, CharSequence charSequence0, DialogInterface.OnClickListener dialogInterface$OnClickListener0) {
        Message message0 = dialogInterface$OnClickListener0 == null ? null : this.s.obtainMessage(v, dialogInterface$OnClickListener0);
        switch(v) {
            case -3: {
                this.k = charSequence0;
                this.j = message0;
                this.i = null;
                return;
            }
            case -2: {
                this.g = charSequence0;
                this.f = message0;
                this.e = null;
                return;
            }
            case -1: {
                this.o = charSequence0;
                this.n = message0;
                this.m = null;
                return;
            }
            default: {
                throw new IllegalArgumentException("Button does not exist");
            }
        }
    }

    public final void e(ViewGroup viewGroup0, NestedScrollView nestedScrollView0, int v) {
        View view0 = this.H.findViewById(R.id.scrollIndicatorUp);
        View view1 = this.H.findViewById(R.id.scrollIndicatorDown);
        if(Build.VERSION.SDK_INT >= 23) {
            ViewCompat.setScrollIndicators(nestedScrollView0, v, 3);
            if(view0 != null) {
                viewGroup0.removeView(view0);
            }
            if(view1 != null) {
                viewGroup0.removeView(view1);
            }
        }
        else {
            if(view0 != null && (v & 1) == 0) {
                viewGroup0.removeView(view0);
                view0 = null;
            }
            if(view1 != null && (v & 2) == 0) {
                viewGroup0.removeView(view1);
                view1 = null;
            }
            if(view0 != null || view1 != null) {
                if(this.y != null) {
                    this.A.setOnScrollChangeListener((View view0, /* 缺少LAMBDA参数 */, /* 缺少LAMBDA参数 */, /* 缺少LAMBDA参数 */, /* 缺少LAMBDA参数 */) -> {
                        int v = 0;
                        if(view0 != null) {
                            view0.setVisibility((view0.canScrollVertically(-1) ? 0 : 4));
                        }
                        if(view1 != null) {
                            if(!view0.canScrollVertically(1)) {
                                v = 4;
                            }
                            view1.setVisibility(v);
                        }
                    });
                    this.A.post(() -> {
                        int v = 0;
                        if(view0 != null) {
                            view0.setVisibility((this.a.A.canScrollVertically(-1) ? 0 : 4));
                        }
                        if(view1 != null) {
                            if(!this.a.A.canScrollVertically(1)) {
                                v = 4;
                            }
                            view1.setVisibility(v);
                        }
                    });
                    return;
                }
                if(view0 != null) {
                    viewGroup0.removeView(view0);
                }
                if(view1 != null) {
                    viewGroup0.removeView(view1);
                }
            }
        }
    }

    public final void f(ViewGroup viewGroup0) {
        NestedScrollView nestedScrollView0 = (NestedScrollView)this.H.findViewById(R.id.scrollView);
        this.A = nestedScrollView0;
        nestedScrollView0.setFocusable(false);
        this.A.setNestedScrollingEnabled(false);
        TextView textView0 = (TextView)viewGroup0.findViewById(0x102000B);
        this.z = textView0;
        if(textView0 == null) {
            return;
        }
        CharSequence charSequence0 = this.y;
        if(charSequence0 != null) {
            textView0.setText(charSequence0);
            return;
        }
        textView0.setVisibility(8);
        this.A.removeView(this.z);
        viewGroup0.setVisibility(8);
    }

    public final void g(ViewGroup viewGroup0) {
        View view0 = this.E;
        if(view0 == null) {
            view0 = this.F == 0 ? null : LayoutInflater.from(this.p).inflate(this.F, viewGroup0, false);
        }
        if(view0 == null || !AlertController.a(view0)) {
            this.H.setFlags(0x20000, 0x20000);
        }
        if(view0 == null) {
            viewGroup0.setVisibility(8);
        }
        else {
            FrameLayout frameLayout0 = (FrameLayout)this.H.findViewById(R.id.custom);
            frameLayout0.addView(view0, new ViewGroup.LayoutParams(-1, -1));
            if(this.G) {
                frameLayout0.setPadding(0, 0, 0, 0);
            }
        }
    }

    public final void h(ViewGroup viewGroup0) {
        if(this.q == null) {
            this.v = (ImageView)this.H.findViewById(0x1020006);
            if(!TextUtils.isEmpty(this.C) != 0 && this.B) {
                TextView textView0 = (TextView)this.H.findViewById(R.id.alertTitle);
                this.D = textView0;
                textView0.setText(this.C);
                int v = this.u;
                if(v != 0) {
                    this.v.setImageResource(v);
                    return;
                }
                Drawable drawable0 = this.t;
                if(drawable0 != null) {
                    this.v.setImageDrawable(drawable0);
                    return;
                }
                this.D.setPadding(this.v.getPaddingLeft(), this.v.getPaddingTop(), this.v.getPaddingRight(), this.v.getPaddingBottom());
                this.v.setVisibility(8);
                return;
            }
            this.H.findViewById(R.id.title_template).setVisibility(8);
            this.v.setVisibility(8);
        }
        else {
            ViewGroup.LayoutParams viewGroup$LayoutParams0 = new ViewGroup.LayoutParams(-1, -2);
            viewGroup0.addView(this.q, 0, viewGroup$LayoutParams0);
            viewGroup0 = this.H.findViewById(R.id.title_template);
        }
        viewGroup0.setVisibility(8);
    }

    public final void i() {
        Button button3;
        int v2;
        View view0 = this.H.findViewById(R.id.parentPanel);
        View view1 = view0.findViewById(R.id.topPanel);
        View view2 = view0.findViewById(R.id.contentPanel);
        View view3 = view0.findViewById(R.id.buttonPanel);
        ViewGroup viewGroup0 = (ViewGroup)view0.findViewById(R.id.customPanel);
        this.g(viewGroup0);
        View view4 = viewGroup0.findViewById(R.id.topPanel);
        View view5 = viewGroup0.findViewById(R.id.contentPanel);
        View view6 = viewGroup0.findViewById(R.id.buttonPanel);
        ViewGroup viewGroup1 = AlertController.c(view4, view1);
        ViewGroup viewGroup2 = AlertController.c(view5, view2);
        ViewGroup viewGroup3 = AlertController.c(view6, view3);
        this.f(viewGroup2);
        Button button0 = (Button)viewGroup3.findViewById(0x1020019);
        this.l = button0;
        View.OnClickListener view$OnClickListener0 = this.b;
        button0.setOnClickListener(view$OnClickListener0);
        boolean z = TextUtils.isEmpty(this.o);
        int v = this.c;
        View view7 = null;
        int v1 = 0;
        if(!z || this.m != null) {
            this.l.setText(this.o);
            Drawable drawable0 = this.m;
            if(drawable0 != null) {
                drawable0.setBounds(0, 0, v, v);
                this.l.setCompoundDrawables(this.m, null, null, null);
            }
            this.l.setVisibility(0);
            v2 = 1;
        }
        else {
            this.l.setVisibility(8);
            v2 = 0;
        }
        Button button1 = (Button)viewGroup3.findViewById(0x102001A);
        this.d = button1;
        button1.setOnClickListener(view$OnClickListener0);
        if(!TextUtils.isEmpty(this.g) || this.e != null) {
            this.d.setText(this.g);
            Drawable drawable1 = this.e;
            if(drawable1 != null) {
                drawable1.setBounds(0, 0, v, v);
                this.d.setCompoundDrawables(this.e, null, null, null);
            }
            this.d.setVisibility(0);
            v2 |= 2;
        }
        else {
            this.d.setVisibility(8);
        }
        Button button2 = (Button)viewGroup3.findViewById(0x102001B);
        this.h = button2;
        button2.setOnClickListener(view$OnClickListener0);
        if(!TextUtils.isEmpty(this.k) || this.i != null) {
            this.h.setText(this.k);
            Drawable drawable2 = this.m;
            if(drawable2 != null) {
                drawable2.setBounds(0, 0, v, v);
                this.l.setCompoundDrawables(this.m, null, null, null);
            }
            this.h.setVisibility(0);
            v2 |= 4;
        }
        else {
            this.h.setVisibility(8);
        }
        if(AlertController.j(this.p)) {
            if(v2 == 1) {
                button3 = this.l;
                goto label_67;
            }
            else if(v2 == 2) {
                button3 = this.d;
                goto label_67;
            }
            else if(v2 == 4) {
                button3 = this.h;
            label_67:
                LinearLayout.LayoutParams linearLayout$LayoutParams0 = (LinearLayout.LayoutParams)button3.getLayoutParams();
                linearLayout$LayoutParams0.gravity = 1;
                linearLayout$LayoutParams0.weight = 0.5f;
                button3.setLayoutParams(linearLayout$LayoutParams0);
            }
        }
        if(v2 == 0) {
            viewGroup3.setVisibility(8);
        }
        this.h(viewGroup1);
        boolean z1 = viewGroup0.getVisibility() != 8;
        int v3 = viewGroup1 == null || viewGroup1.getVisibility() == 8 ? 0 : 1;
        boolean z2 = viewGroup3.getVisibility() != 8;
        if(!z2) {
            View view8 = viewGroup2.findViewById(R.id.textSpacerNoButtons);
            if(view8 != null) {
                view8.setVisibility(0);
            }
        }
        if(v3 == 0) {
            View view9 = viewGroup2.findViewById(R.id.textSpacerNoTitle);
            if(view9 != null) {
                view9.setVisibility(0);
            }
        }
        else {
            NestedScrollView nestedScrollView0 = this.A;
            if(nestedScrollView0 != null) {
                nestedScrollView0.setClipToPadding(true);
            }
            if(this.y != null) {
                view7 = viewGroup1.findViewById(R.id.titleDividerNoCustom);
            }
            if(view7 != null) {
                view7.setVisibility(0);
            }
        }
        if(!z1) {
            NestedScrollView nestedScrollView1 = this.A;
            if(nestedScrollView1 != null) {
                if(z2) {
                    v1 = 2;
                }
                this.e(viewGroup2, nestedScrollView1, v3 | v1);
            }
        }
    }

    public static boolean j(Context context0) {
        TypedValue typedValue0 = new TypedValue();
        context0.getTheme().resolveAttribute(R.attr.alertDialogCenterButtons, typedValue0, true);
        return typedValue0.data != 0;
    }
}


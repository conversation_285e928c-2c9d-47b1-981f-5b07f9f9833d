package android.supportv1.v7.app;

import android.content.Context;
import android.location.Location;
import android.location.LocationManager;
import android.supportv1.v4.content.PermissionChecker;
import java.util.Calendar;

class TwilightManager {
    static class TwilightState {
        public boolean a;
        public long b;

    }

    public final Context a;
    public final LocationManager b;
    public final TwilightState c;
    public static TwilightManager d;

    public TwilightManager(Context context0, LocationManager locationManager0) {
        this.c = new TwilightState();  // 初始化器: Ljava/lang/Object;-><init>()V
        this.a = context0;
        this.b = locationManager0;
    }

    public final boolean a() {
        long v5;
        Location location1;
        TwilightState twilightManager$TwilightState0 = this.c;
        if(twilightManager$TwilightState0.b > System.currentTimeMillis()) {
            return twilightManager$TwilightState0.a;
        }
        Context context0 = this.a;
        int v = PermissionChecker.checkSelfPermission(context0, "android.permission.ACCESS_COARSE_LOCATION");
        LocationManager locationManager0 = this.b;
        Location location0 = null;
        if(v == 0) {
            try {
                location1 = locationManager0.isProviderEnabled("network") ? locationManager0.getLastKnownLocation("network") : null;
            }
            catch(Exception unused_ex) {
                location1 = null;
            }
        }
        else {
            location1 = null;
        }
        if(PermissionChecker.checkSelfPermission(context0, "android.permission.ACCESS_FINE_LOCATION") == 0) {
            try {
                if(locationManager0.isProviderEnabled("gps")) {
                    location0 = locationManager0.getLastKnownLocation("gps");
                }
            }
            catch(Exception unused_ex) {
            }
        }
        if(location0 == null || location1 == null) {
            if(location0 != null) {
                location1 = location0;
            }
        }
        else if(location0.getTime() > location1.getTime()) {
            location1 = location0;
        }
        boolean z = true;
        if(location1 != null) {
            long v1 = System.currentTimeMillis();
            if(TwilightCalculator.d == null) {
                TwilightCalculator.d = new TwilightCalculator();  // 初始化器: Ljava/lang/Object;-><init>()V
            }
            TwilightCalculator twilightCalculator0 = TwilightCalculator.d;
            twilightCalculator0.a(v1 - 86400000L, location1.getLatitude(), location1.getLongitude());
            twilightCalculator0.a(v1, location1.getLatitude(), location1.getLongitude());
            if(twilightCalculator0.a != 1) {
                z = false;
            }
            long v2 = twilightCalculator0.b;
            long v3 = twilightCalculator0.c;
            twilightCalculator0.a(v1 + 86400000L, location1.getLatitude(), location1.getLongitude());
            long v4 = twilightCalculator0.b;
            if(v2 == -1L || v3 == -1L) {
                v5 = v1 + 43200000L;
            }
            else {
                if(v1 > v3) {
                    v2 = v4;
                }
                else if(v1 > v2) {
                    v2 = v3;
                }
                v5 = v2 + 60000L;
            }
            twilightManager$TwilightState0.a = z;
            twilightManager$TwilightState0.b = v5;
            return z;
        }
        int v6 = Calendar.getInstance().get(11);
        return v6 < 6 || v6 >= 22;
    }
}


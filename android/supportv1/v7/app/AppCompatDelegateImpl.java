package android.supportv1.v7.app;

import android.app.Activity;
import android.app.Dialog;
import android.app.UiModeManager;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.IntentFilter;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.location.LocationManager;
import android.media.AudioManager;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.supportv1.v4.app.NavUtils;
import android.supportv1.v4.view.KeyEventDispatcher.Component;
import android.supportv1.v4.view.KeyEventDispatcher;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.view.ViewPropertyAnimatorCompat;
import android.supportv1.v4.widget.PopupWindowCompat;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.appcompat.R.color;
import android.supportv1.v7.appcompat.R.id;
import android.supportv1.v7.appcompat.R.layout;
import android.supportv1.v7.appcompat.R.style;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.content.res.AppCompatResources;
import android.supportv1.v7.view.ActionMode;
import android.supportv1.v7.view.ContextThemeWrapper;
import android.supportv1.v7.view.StandaloneActionMode;
import android.supportv1.v7.view.SupportActionModeWrapper.CallbackWrapper;
import android.supportv1.v7.view.SupportActionModeWrapper;
import android.supportv1.v7.view.WindowCallbackWrapper;
import android.supportv1.v7.view.menu.ListMenuPresenter;
import android.supportv1.v7.view.menu.MenuBuilder.Callback;
import android.supportv1.v7.view.menu.MenuBuilder;
import android.supportv1.v7.widget.ActionBarContextView;
import android.supportv1.v7.widget.ContentFrameLayout;
import android.supportv1.v7.widget.TintTypedArray;
import android.supportv1.v7.widget.ViewUtils;
import android.text.TextUtils;
import android.util.AndroidRuntimeException;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.ActionMode.Callback;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.LayoutInflater.Factory2;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.Window.Callback;
import android.view.Window;
import android.view.WindowManager.LayoutParams;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.PopupWindow;
import android.widget.TextView;
import com.google.android.gms.internal.ads.g;
import h.a;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

class AppCompatDelegateImpl extends AppCompatDelegate implements Callback, LayoutInflater.Factory2 {
    class ActionModeCallbackWrapperV9 implements android.supportv1.v7.view.ActionMode.Callback {
        public final android.supportv1.v7.view.ActionMode.Callback a;
        public final AppCompatDelegateImpl b;

        public ActionModeCallbackWrapperV9(android.supportv1.v7.view.ActionMode.Callback actionMode$Callback0) {
            this.a = actionMode$Callback0;
        }

        @Override  // android.supportv1.v7.view.ActionMode$Callback
        public final void a(ActionMode actionMode0) {
            this.a.a(actionMode0);
            AppCompatDelegateImpl appCompatDelegateImpl0 = this.b;
            if(appCompatDelegateImpl0.b != null) {
                appCompatDelegateImpl0.D.getDecorView().removeCallbacks(appCompatDelegateImpl0.v);
            }
            if(appCompatDelegateImpl0.c != null) {
                ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = appCompatDelegateImpl0.h;
                if(viewPropertyAnimatorCompat0 != null) {
                    viewPropertyAnimatorCompat0.cancel();
                }
                ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1 = ViewCompat.animate(appCompatDelegateImpl0.c).alpha(0.0f);
                appCompatDelegateImpl0.h = viewPropertyAnimatorCompat1;
                viewPropertyAnimatorCompat1.setListener(new AppCompatDelegateImpl.ActionModeCallbackWrapperV9.1(this));
            }
            appCompatDelegateImpl0.a = null;
        }

        @Override  // android.supportv1.v7.view.ActionMode$Callback
        public final boolean b(StandaloneActionMode standaloneActionMode0, MenuBuilder menuBuilder0) {
            return this.a.b(standaloneActionMode0, menuBuilder0);
        }

        @Override  // android.supportv1.v7.view.ActionMode$Callback
        public final boolean c(ActionMode actionMode0, MenuBuilder menuBuilder0) {
            return this.a.c(actionMode0, menuBuilder0);
        }

        @Override  // android.supportv1.v7.view.ActionMode$Callback
        public final boolean d(ActionMode actionMode0, MenuItem menuItem0) {
            return this.a.d(actionMode0, menuItem0);
        }
    }

    class AppCompatWindowCallback extends WindowCallbackWrapper {
        public final AppCompatDelegateImpl b;

        public AppCompatWindowCallback(Window.Callback window$Callback0) {
            super(window$Callback0);
        }

        public final SupportActionModeWrapper a(ActionMode.Callback actionMode$Callback0) {
            AppCompatDelegateImpl appCompatDelegateImpl0 = AppCompatDelegateImpl.this;
            CallbackWrapper supportActionModeWrapper$CallbackWrapper0 = new CallbackWrapper(appCompatDelegateImpl0.g, actionMode$Callback0);
            ActionMode actionMode0 = appCompatDelegateImpl0.a;
            if(actionMode0 != null) {
                actionMode0.b();
            }
            ActionModeCallbackWrapperV9 appCompatDelegateImpl$ActionModeCallbackWrapperV90 = new ActionModeCallbackWrapperV9(appCompatDelegateImpl0, supportActionModeWrapper$CallbackWrapper0);
            appCompatDelegateImpl0.o();
            if(appCompatDelegateImpl0.a == null) {
                appCompatDelegateImpl0.a = appCompatDelegateImpl0.s(appCompatDelegateImpl$ActionModeCallbackWrapperV90);
            }
            return appCompatDelegateImpl0.a == null ? null : supportActionModeWrapper$CallbackWrapper0.e(appCompatDelegateImpl0.a);
        }

        // 去混淆评级： 低(20)
        @Override  // android.view.Window$Callback
        public final boolean dispatchKeyEvent(KeyEvent keyEvent0) {
            return AppCompatDelegateImpl.this.j(keyEvent0) || this.a.dispatchKeyEvent(keyEvent0);
        }

        @Override  // android.view.Window$Callback
        public final boolean dispatchKeyShortcutEvent(KeyEvent keyEvent0) {
            if(!this.a.dispatchKeyShortcutEvent(keyEvent0)) {
                keyEvent0.getKeyCode();
                AppCompatDelegateImpl appCompatDelegateImpl0 = AppCompatDelegateImpl.this;
                appCompatDelegateImpl0.o();
                if(appCompatDelegateImpl0.u == null || !appCompatDelegateImpl0.q(appCompatDelegateImpl0.u, keyEvent0.getKeyCode(), keyEvent0)) {
                    goto label_9;
                }
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = appCompatDelegateImpl0.u;
                if(appCompatDelegateImpl$PanelFeatureState0 != null) {
                    appCompatDelegateImpl$PanelFeatureState0.g = true;
                    return true;
                label_9:
                    if(appCompatDelegateImpl0.u == null) {
                        PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = appCompatDelegateImpl0.n(0);
                        appCompatDelegateImpl0.r(appCompatDelegateImpl$PanelFeatureState1, keyEvent0);
                        boolean z = appCompatDelegateImpl0.q(appCompatDelegateImpl$PanelFeatureState1, keyEvent0.getKeyCode(), keyEvent0);
                        appCompatDelegateImpl$PanelFeatureState1.i = false;
                        return z;
                    }
                    return false;
                }
            }
            return true;
        }

        @Override  // android.view.Window$Callback
        public final void onContentChanged() {
        }

        @Override  // android.view.Window$Callback
        public final boolean onCreatePanelMenu(int v, Menu menu0) {
            return v != 0 || menu0 instanceof MenuBuilder ? this.a.onCreatePanelMenu(v, menu0) : false;
        }

        @Override  // android.supportv1.v7.view.WindowCallbackWrapper
        public final boolean onMenuOpened(int v, Menu menu0) {
            super.onMenuOpened(v, menu0);
            AppCompatDelegateImpl appCompatDelegateImpl0 = AppCompatDelegateImpl.this;
            if(v == 108) {
                appCompatDelegateImpl0.o();
                return true;
            }
            appCompatDelegateImpl0.getClass();
            return true;
        }

        @Override  // android.supportv1.v7.view.WindowCallbackWrapper
        public final void onPanelClosed(int v, Menu menu0) {
            super.onPanelClosed(v, menu0);
            AppCompatDelegateImpl appCompatDelegateImpl0 = AppCompatDelegateImpl.this;
            if(v == 108) {
                appCompatDelegateImpl0.o();
                return;
            }
            if(v == 0) {
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = appCompatDelegateImpl0.n(0);
                if(appCompatDelegateImpl$PanelFeatureState0.h) {
                    appCompatDelegateImpl0.g(appCompatDelegateImpl$PanelFeatureState0, false);
                }
            }
            else {
                appCompatDelegateImpl0.getClass();
            }
        }

        @Override  // android.view.Window$Callback
        public final boolean onPreparePanel(int v, View view0, Menu menu0) {
            MenuBuilder menuBuilder0 = menu0 instanceof MenuBuilder ? ((MenuBuilder)menu0) : null;
            if(v == 0 && menuBuilder0 == null) {
                return false;
            }
            if(menuBuilder0 != null) {
                menuBuilder0.n = true;
            }
            boolean z = this.a.onPreparePanel(v, view0, menu0);
            if(menuBuilder0 != null) {
                menuBuilder0.n = false;
            }
            return z;
        }

        @Override  // android.supportv1.v7.view.WindowCallbackWrapper
        public final void onProvideKeyboardShortcuts(List list0, Menu menu0, int v) {
            MenuBuilder menuBuilder0 = AppCompatDelegateImpl.this.n(0).l;
            if(menuBuilder0 != null) {
                menu0 = menuBuilder0;
            }
            super.onProvideKeyboardShortcuts(list0, menu0, v);
        }

        @Override  // android.view.Window$Callback
        public final android.view.ActionMode onWindowStartingActionMode(ActionMode.Callback actionMode$Callback0) {
            if(Build.VERSION.SDK_INT >= 23) {
                return null;
            }
            AppCompatDelegateImpl.this.getClass();
            return this.a(actionMode$Callback0);
        }

        @Override  // android.view.Window$Callback
        public final android.view.ActionMode onWindowStartingActionMode(ActionMode.Callback actionMode$Callback0, int v) {
            AppCompatDelegateImpl.this.getClass();
            return v != 0 ? g.k(this.a, actionMode$Callback0, v) : this.a(actionMode$Callback0);
        }
    }

    final class AutoNightModeManager {
        public BroadcastReceiver a;
        public IntentFilter b;
        public boolean c;
        public final TwilightManager d;
        public final AppCompatDelegateImpl e;

        public AutoNightModeManager(TwilightManager twilightManager0) {
            this.d = twilightManager0;
            this.c = twilightManager0.a();
        }
    }

    class ListMenuDecorView extends ContentFrameLayout {
        public final AppCompatDelegateImpl i;

        public ListMenuDecorView(ContextThemeWrapper contextThemeWrapper0) {
            super(contextThemeWrapper0);
        }

        // 去混淆评级： 低(20)
        @Override  // android.view.ViewGroup
        public final boolean dispatchKeyEvent(KeyEvent keyEvent0) {
            return AppCompatDelegateImpl.this.j(keyEvent0) || super.dispatchKeyEvent(keyEvent0);
        }

        @Override  // android.view.ViewGroup
        public final boolean onInterceptTouchEvent(MotionEvent motionEvent0) {
            if(motionEvent0.getAction() == 0) {
                int v = (int)motionEvent0.getX();
                int v1 = (int)motionEvent0.getY();
                if(v < -5 || v1 < -5 || v > this.getWidth() + 5 || v1 > this.getHeight() + 5) {
                    PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = AppCompatDelegateImpl.this.n(0);
                    AppCompatDelegateImpl.this.g(appCompatDelegateImpl$PanelFeatureState0, true);
                    return true;
                }
            }
            return super.onInterceptTouchEvent(motionEvent0);
        }

        @Override  // android.view.View
        public final void setBackgroundResource(int v) {
            this.setBackgroundDrawable(AppCompatResources.c(this.getContext(), v));
        }
    }

    public static final class PanelFeatureState {
        public int a;
        public View b;
        public ViewGroup c;
        public int d;
        public Bundle e;
        public int f;
        public boolean g;
        public boolean h;
        public boolean i;
        public ListMenuPresenter j;
        public ContextThemeWrapper k;
        public MenuBuilder l;
        public boolean m;
        public boolean n;
        public View o;
        public int p;

        public final void a(android.supportv1.v7.view.menu.MenuPresenter.Callback menuPresenter$Callback0) {
            if(this.l == null) {
                return;
            }
            if(this.j == null) {
                ListMenuPresenter listMenuPresenter0 = new ListMenuPresenter(this.k, R.layout.abc_list_menu_item_layout);
                this.j = listMenuPresenter0;
                listMenuPresenter0.h(menuPresenter$Callback0);
                this.l.b(this.j);
            }
            this.j.g(this.c);
            throw null;
        }

        public final void b(MenuBuilder menuBuilder0) {
            MenuBuilder menuBuilder1 = this.l;
            if(menuBuilder0 == menuBuilder1) {
                return;
            }
            if(menuBuilder1 != null) {
                menuBuilder1.u(this.j);
            }
            this.l = menuBuilder0;
            if(menuBuilder0 != null) {
                ListMenuPresenter listMenuPresenter0 = this.j;
                if(listMenuPresenter0 != null) {
                    menuBuilder0.b(listMenuPresenter0);
                }
            }
        }

        public final void c(Context context0) {
            TypedValue typedValue0 = new TypedValue();
            Resources.Theme resources$Theme0 = context0.getResources().newTheme();
            resources$Theme0.setTo(context0.getTheme());
            resources$Theme0.resolveAttribute(R.attr.actionBarPopupTheme, typedValue0, true);
            int v = typedValue0.resourceId;
            if(v != 0) {
                resources$Theme0.applyStyle(v, true);
            }
            resources$Theme0.resolveAttribute(R.attr.panelMenuListTheme, typedValue0, true);
            resources$Theme0.applyStyle((typedValue0.resourceId == 0 ? R.style.Theme_AppCompat_CompactMenu : typedValue0.resourceId), true);
            ContextThemeWrapper contextThemeWrapper0 = new ContextThemeWrapper(context0, 0);
            contextThemeWrapper0.getTheme().setTo(resources$Theme0);
            this.k = contextThemeWrapper0;
            TypedArray typedArray0 = contextThemeWrapper0.obtainStyledAttributes(R.styleable.AppCompatTheme);
            this.a = typedArray0.getResourceId(R.styleable.AppCompatTheme_panelBackground, 0);
            this.p = typedArray0.getResourceId(R.styleable.AppCompatTheme_android_windowAnimationStyle, 0);
            typedArray0.recycle();
        }
    }

    final class PanelMenuPresenterCallback implements android.supportv1.v7.view.menu.MenuPresenter.Callback {
        public final AppCompatDelegateImpl a;

        @Override  // android.supportv1.v7.view.menu.MenuPresenter$Callback
        public final void a(MenuBuilder menuBuilder0, boolean z) {
            PanelFeatureState appCompatDelegateImpl$PanelFeatureState0;
            MenuBuilder menuBuilder1 = menuBuilder0.p();
            boolean z1 = menuBuilder1 != menuBuilder0;
            if(z1) {
                menuBuilder0 = menuBuilder1;
            }
            AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
            PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState = appCompatDelegateImpl0.t;
            int v1 = arr_appCompatDelegateImpl$PanelFeatureState == null ? 0 : arr_appCompatDelegateImpl$PanelFeatureState.length;
            for(int v = 0; true; ++v) {
                appCompatDelegateImpl$PanelFeatureState0 = null;
                if(v >= v1) {
                    break;
                }
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = arr_appCompatDelegateImpl$PanelFeatureState[v];
                if(appCompatDelegateImpl$PanelFeatureState1 != null && appCompatDelegateImpl$PanelFeatureState1.l == menuBuilder0) {
                    appCompatDelegateImpl$PanelFeatureState0 = appCompatDelegateImpl$PanelFeatureState1;
                    break;
                }
            }
            if(appCompatDelegateImpl$PanelFeatureState0 != null) {
                if(z1) {
                    appCompatDelegateImpl0.f(appCompatDelegateImpl$PanelFeatureState0.d, appCompatDelegateImpl$PanelFeatureState0, menuBuilder1);
                    appCompatDelegateImpl0.g(appCompatDelegateImpl$PanelFeatureState0, true);
                    return;
                }
                appCompatDelegateImpl0.g(appCompatDelegateImpl$PanelFeatureState0, z);
            }
        }

        @Override  // android.supportv1.v7.view.menu.MenuPresenter$Callback
        public final boolean b(MenuBuilder menuBuilder0) {
            if(menuBuilder0 == null) {
                AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
                if(appCompatDelegateImpl0.i) {
                    Window.Callback window$Callback0 = appCompatDelegateImpl0.D.getCallback();
                    if(window$Callback0 != null) {
                        appCompatDelegateImpl0.getClass();
                        window$Callback0.onMenuOpened(108, null);
                    }
                }
            }
            return true;
        }
    }

    public Rect A;
    public CharSequence B;
    public TextView C;
    public final Window D;
    public boolean E;
    public static final int[] F;
    public ActionMode a;
    public PopupWindow b;
    public ActionBarContextView c;
    public AppCompatViewInflater d;
    public boolean e;
    public AutoNightModeManager f;
    public final Context g;
    public ViewPropertyAnimatorCompat h;
    public boolean i;
    public int j;
    public boolean k;
    public final Runnable l;
    public boolean m;
    public int n;
    public boolean o;
    public final Window.Callback p;
    public boolean q;
    public boolean r;
    public PanelMenuPresenterCallback s;
    public PanelFeatureState[] t;
    public PanelFeatureState u;
    public Runnable v;
    public View w;
    public ViewGroup x;
    public boolean y;
    public Rect z;

    static {
        AppCompatDelegateImpl.F = new int[]{0x1010054};
    }

    public AppCompatDelegateImpl(Context context0, Window window0, AppCompatCallback appCompatCallback0) {
        this.h = null;
        this.n = -100;
        this.l = new Runnable() {
            public final AppCompatDelegateImpl a;

            {
                this.a = appCompatDelegateImpl0;
            }

            @Override
            public final void run() {
                AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
                if((appCompatDelegateImpl0.j & 1) != 0) {
                    appCompatDelegateImpl0.k(0);
                }
                if((appCompatDelegateImpl0.j & 0x1000) != 0) {
                    appCompatDelegateImpl0.k(108);
                }
                appCompatDelegateImpl0.k = false;
                appCompatDelegateImpl0.j = 0;
            }
        };
        this.g = context0;
        this.D = window0;
        Window.Callback window$Callback0 = window0.getCallback();
        this.p = window$Callback0;
        if(window$Callback0 instanceof AppCompatWindowCallback) {
            throw new IllegalStateException("AppCompat has already installed itself into the Window");
        }
        window0.setCallback(new AppCompatWindowCallback(this, window$Callback0));
        TintTypedArray tintTypedArray0 = TintTypedArray.n(context0, null, AppCompatDelegateImpl.F);
        Drawable drawable0 = tintTypedArray0.e(0);
        if(drawable0 != null) {
            window0.setBackgroundDrawable(drawable0);
        }
        tintTypedArray0.p();
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder$Callback
    public final boolean a(MenuBuilder menuBuilder0, MenuItem menuItem0) {
        Window.Callback window$Callback0 = this.D.getCallback();
        if(window$Callback0 != null) {
            MenuBuilder menuBuilder1 = menuBuilder0.p();
            PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState = this.t;
            int v = arr_appCompatDelegateImpl$PanelFeatureState == null ? 0 : arr_appCompatDelegateImpl$PanelFeatureState.length;
            for(int v1 = 0; v1 < v; ++v1) {
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = arr_appCompatDelegateImpl$PanelFeatureState[v1];
                if(appCompatDelegateImpl$PanelFeatureState0 != null && appCompatDelegateImpl$PanelFeatureState0.l == menuBuilder1) {
                    return appCompatDelegateImpl$PanelFeatureState0 == null ? false : window$Callback0.onMenuItemSelected(appCompatDelegateImpl$PanelFeatureState0.d, menuItem0);
                }
            }
            throw new NullPointerException();
        }
        return false;
    }

    @Override  // android.supportv1.v7.app.AppCompatDelegate
    public final boolean b() {
        Map map0;
        Object object0;
        int v1;
        int v = this.n == -100 ? -1 : this.n;
        Context context0 = this.g;
        if(v != 0) {
            v1 = v;
        }
        else if(Build.VERSION.SDK_INT < 23 || ((UiModeManager)g.m(context0)).getNightMode() != 0) {
            this.l();
            AutoNightModeManager appCompatDelegateImpl$AutoNightModeManager0 = this.f;
            boolean z = appCompatDelegateImpl$AutoNightModeManager0.d.a();
            appCompatDelegateImpl$AutoNightModeManager0.c = z;
            v1 = z ? 2 : 1;
        }
        else {
            v1 = -1;
        }
        boolean z1 = false;
        if(v1 != -1) {
            Resources resources0 = context0.getResources();
            Configuration configuration0 = resources0.getConfiguration();
            int v2 = v1 == 2 ? 0x20 : 16;
            if((configuration0.uiMode & 0x30) != v2) {
                if(!this.e || !(context0 instanceof Activity)) {
                label_24:
                    Configuration configuration1 = new Configuration(configuration0);
                    DisplayMetrics displayMetrics0 = resources0.getDisplayMetrics();
                    configuration1.uiMode = v2 | configuration1.uiMode & -49;
                    resources0.updateConfiguration(configuration1, displayMetrics0);
                    int v3 = Build.VERSION.SDK_INT;
                    if(v3 < 26 && v3 < 28) {
                        Class class0 = Resources.class;
                        if(v3 >= 24) {
                            if(!ResourcesFlusher.d) {
                                try {
                                    Field field0 = class0.getDeclaredField("mResourcesImpl");
                                    ResourcesFlusher.c = field0;
                                    field0.setAccessible(true);
                                }
                                catch(NoSuchFieldException unused_ex) {
                                }
                                ResourcesFlusher.d = true;
                            }
                            Field field1 = ResourcesFlusher.c;
                            if(field1 != null) {
                                try {
                                    object0 = null;
                                    object0 = field1.get(resources0);
                                }
                                catch(IllegalAccessException unused_ex) {
                                }
                                if(object0 != null) {
                                    if(!ResourcesFlusher.b) {
                                        try {
                                            Field field2 = object0.getClass().getDeclaredField("mDrawableCache");
                                            ResourcesFlusher.a = field2;
                                            field2.setAccessible(true);
                                        }
                                        catch(NoSuchFieldException unused_ex) {
                                        }
                                        ResourcesFlusher.b = true;
                                    }
                                    Field field3 = ResourcesFlusher.a;
                                    Object object1 = null;
                                    if(field3 != null) {
                                        try {
                                            object1 = field3.get(object0);
                                        }
                                        catch(IllegalAccessException unused_ex) {
                                        }
                                    }
                                    if(object1 != null) {
                                        ResourcesFlusher.a(object1);
                                    }
                                }
                            }
                        }
                        else if(v3 >= 23) {
                            if(!ResourcesFlusher.b) {
                                try {
                                    Field field4 = class0.getDeclaredField("mDrawableCache");
                                    ResourcesFlusher.a = field4;
                                    field4.setAccessible(true);
                                }
                                catch(NoSuchFieldException unused_ex) {
                                }
                                ResourcesFlusher.b = true;
                            }
                            Field field5 = ResourcesFlusher.a;
                            Object object2 = null;
                            if(field5 != null) {
                                try {
                                    object2 = field5.get(resources0);
                                }
                                catch(IllegalAccessException unused_ex) {
                                }
                            }
                            if(object2 != null) {
                                ResourcesFlusher.a(object2);
                            }
                        }
                        else {
                            if(!ResourcesFlusher.b) {
                                try {
                                    Field field6 = class0.getDeclaredField("mDrawableCache");
                                    ResourcesFlusher.a = field6;
                                    field6.setAccessible(true);
                                }
                                catch(NoSuchFieldException unused_ex) {
                                }
                                ResourcesFlusher.b = true;
                            }
                            Field field7 = ResourcesFlusher.a;
                            if(field7 != null) {
                                try {
                                    map0 = null;
                                    map0 = (Map)field7.get(resources0);
                                }
                                catch(IllegalAccessException unused_ex) {
                                }
                                if(map0 != null) {
                                    map0.clear();
                                }
                            }
                        }
                    }
                }
                else {
                    PackageManager packageManager0 = context0.getPackageManager();
                    try {
                        if((packageManager0.getActivityInfo(new ComponentName(context0, context0.getClass()), 0).configChanges & 0x200) == 0) {
                            goto label_22;
                        }
                        else {
                            goto label_24;
                        }
                        goto label_78;
                    }
                    catch(PackageManager.NameNotFoundException unused_ex) {
                    }
                label_22:
                    ((Activity)context0).recreate();
                }
            label_78:
                z1 = true;
            }
        }
        if(v == 0) {
            this.l();
            AutoNightModeManager appCompatDelegateImpl$AutoNightModeManager1 = this.f;
            BroadcastReceiver broadcastReceiver0 = appCompatDelegateImpl$AutoNightModeManager1.a;
            AppCompatDelegateImpl appCompatDelegateImpl0 = appCompatDelegateImpl$AutoNightModeManager1.e;
            if(broadcastReceiver0 != null) {
                appCompatDelegateImpl0.g.unregisterReceiver(broadcastReceiver0);
                appCompatDelegateImpl$AutoNightModeManager1.a = null;
            }
            if(appCompatDelegateImpl$AutoNightModeManager1.a == null) {
                appCompatDelegateImpl$AutoNightModeManager1.a = new AppCompatDelegateImpl.AutoNightModeManager.1(appCompatDelegateImpl$AutoNightModeManager1);
            }
            if(appCompatDelegateImpl$AutoNightModeManager1.b == null) {
                IntentFilter intentFilter0 = new IntentFilter();
                appCompatDelegateImpl$AutoNightModeManager1.b = intentFilter0;
                intentFilter0.addAction("android.intent.action.TIME_SET");
                appCompatDelegateImpl$AutoNightModeManager1.b.addAction("android.intent.action.TIMEZONE_CHANGED");
                appCompatDelegateImpl$AutoNightModeManager1.b.addAction("android.intent.action.TIME_TICK");
            }
            appCompatDelegateImpl0.g.registerReceiver(appCompatDelegateImpl$AutoNightModeManager1.a, appCompatDelegateImpl$AutoNightModeManager1.b);
        }
        this.e = true;
        return z1;
    }

    @Override  // android.supportv1.v7.app.AppCompatDelegate
    public final void c(Bundle bundle0) {
        Window.Callback window$Callback0 = this.p;
        if(window$Callback0 instanceof Activity) {
            try {
                NavUtils.getParentActivityName(((Activity)window$Callback0));
            }
            catch(IllegalArgumentException unused_ex) {
            }
        }
        if(bundle0 != null && this.n == -100) {
            this.n = bundle0.getInt("appcompat:local_night_mode", -100);
        }
    }

    @Override  // android.supportv1.v7.app.AppCompatDelegate
    public final boolean d(int v) {
        switch(v) {
            case 8: {
                v = 108;
                break;
            }
            case 9: {
                v = 109;
            }
        }
        if(this.E && v == 108) {
            return false;
        }
        if(this.i && v == 1) {
            this.i = false;
        }
        switch(v) {
            case 1: {
                this.t();
                this.E = true;
                return true;
            }
            case 2: {
                this.t();
                return true;
            }
            case 5: {
                this.t();
                return true;
            }
            case 10: {
                this.t();
                this.r = true;
                return true;
            }
            case 108: {
                this.t();
                this.i = true;
                return true;
            }
            case 109: {
                this.t();
                this.q = true;
                return true;
            }
            default: {
                return this.D.requestFeature(v);
            }
        }
    }

    public final void e() {
        ContentFrameLayout contentFrameLayout0 = (ContentFrameLayout)this.x.findViewById(0x1020002);
        View view0 = this.D.getDecorView();
        contentFrameLayout0.a(view0.getPaddingLeft(), view0.getPaddingTop(), view0.getPaddingRight(), view0.getPaddingBottom());
        TypedArray typedArray0 = this.g.obtainStyledAttributes(R.styleable.AppCompatTheme);
        typedArray0.getValue(R.styleable.AppCompatTheme_windowMinWidthMajor, contentFrameLayout0.getMinWidthMajor());
        typedArray0.getValue(R.styleable.AppCompatTheme_windowMinWidthMinor, contentFrameLayout0.getMinWidthMinor());
        if(typedArray0.hasValue(R.styleable.AppCompatTheme_windowFixedWidthMajor)) {
            typedArray0.getValue(R.styleable.AppCompatTheme_windowFixedWidthMajor, contentFrameLayout0.getFixedWidthMajor());
        }
        if(typedArray0.hasValue(R.styleable.AppCompatTheme_windowFixedWidthMinor)) {
            typedArray0.getValue(R.styleable.AppCompatTheme_windowFixedWidthMinor, contentFrameLayout0.getFixedWidthMinor());
        }
        if(typedArray0.hasValue(R.styleable.AppCompatTheme_windowFixedHeightMajor)) {
            typedArray0.getValue(R.styleable.AppCompatTheme_windowFixedHeightMajor, contentFrameLayout0.getFixedHeightMajor());
        }
        if(typedArray0.hasValue(R.styleable.AppCompatTheme_windowFixedHeightMinor)) {
            typedArray0.getValue(R.styleable.AppCompatTheme_windowFixedHeightMinor, contentFrameLayout0.getFixedHeightMinor());
        }
        typedArray0.recycle();
        contentFrameLayout0.requestLayout();
    }

    public final void f(int v, PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, MenuBuilder menuBuilder0) {
        if(menuBuilder0 == null) {
            menuBuilder0 = appCompatDelegateImpl$PanelFeatureState0.l;
        }
        if(!appCompatDelegateImpl$PanelFeatureState0.h) {
            return;
        }
        this.p.onPanelClosed(v, menuBuilder0);
    }

    public final void g(PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, boolean z) {
        WindowManager windowManager0 = (WindowManager)this.g.getSystemService("window");
        if(windowManager0 != null && appCompatDelegateImpl$PanelFeatureState0.h) {
            ViewGroup viewGroup0 = appCompatDelegateImpl$PanelFeatureState0.c;
            if(viewGroup0 != null) {
                windowManager0.removeView(viewGroup0);
                if(z) {
                    this.f(appCompatDelegateImpl$PanelFeatureState0.d, appCompatDelegateImpl$PanelFeatureState0, null);
                }
            }
        }
        appCompatDelegateImpl$PanelFeatureState0.i = false;
        appCompatDelegateImpl$PanelFeatureState0.g = false;
        appCompatDelegateImpl$PanelFeatureState0.h = false;
        appCompatDelegateImpl$PanelFeatureState0.o = null;
        appCompatDelegateImpl$PanelFeatureState0.m = true;
        if(this.u == appCompatDelegateImpl$PanelFeatureState0) {
            this.u = null;
        }
    }

    public final ViewGroup h() {
        ViewGroup viewGroup0;
        TypedArray typedArray0 = this.g.obtainStyledAttributes(R.styleable.AppCompatTheme);
        if(typedArray0.hasValue(R.styleable.AppCompatTheme_windowActionBar)) {
            if(typedArray0.getBoolean(R.styleable.AppCompatTheme_windowNoTitle, false)) {
                this.d(1);
            }
            else if(typedArray0.getBoolean(R.styleable.AppCompatTheme_windowActionBar, false)) {
                this.d(108);
            }
            if(typedArray0.getBoolean(R.styleable.AppCompatTheme_windowActionBarOverlay, false)) {
                this.d(109);
            }
            if(typedArray0.getBoolean(R.styleable.AppCompatTheme_windowActionModeOverlay, false)) {
                this.d(10);
            }
            this.m = typedArray0.getBoolean(R.styleable.AppCompatTheme_android_windowIsFloating, false);
            typedArray0.recycle();
            this.D.getDecorView();
            LayoutInflater layoutInflater0 = LayoutInflater.from(this.g);
            if(this.E) {
                viewGroup0 = (ViewGroup)layoutInflater0.inflate((this.r ? R.layout.abc_screen_simple_overlay_action_mode : R.layout.abc_screen_simple), null);
                ViewCompat.setOnApplyWindowInsetsListener(viewGroup0, new AppCompatDelegateImpl.3(this));
            }
            else if(this.m) {
                viewGroup0 = (ViewGroup)layoutInflater0.inflate(R.layout.abc_dialog_title_material, null);
                this.q = false;
                this.i = false;
            }
            else {
                if(this.i) {
                    TypedValue typedValue0 = new TypedValue();
                    this.g.getTheme().resolveAttribute(R.attr.actionBarTheme, typedValue0, true);
                    Context context0 = typedValue0.resourceId == 0 ? this.g : new ContextThemeWrapper(this.g, typedValue0.resourceId);
                    a.f(((ViewGroup)LayoutInflater.from(context0).inflate(R.layout.abc_screen_toolbar, null)).findViewById(R.id.decor_content_parent));
                    this.D.getCallback();
                    throw null;
                }
                viewGroup0 = null;
            }
            if(viewGroup0 == null) {
                throw new IllegalArgumentException("AppCompat does not support the current theme features: { windowActionBar: " + this.i + ", windowActionBarOverlay: " + this.q + ", android:windowIsFloating: " + this.m + ", windowActionModeOverlay: " + this.r + ", windowNoTitle: " + this.E + " }");
            }
            this.C = (TextView)viewGroup0.findViewById(R.id.title);
            ViewUtils.b(viewGroup0);
            ContentFrameLayout contentFrameLayout0 = (ContentFrameLayout)viewGroup0.findViewById(R.id.action_bar_activity_content);
            ViewGroup viewGroup1 = (ViewGroup)this.D.findViewById(0x1020002);
            if(viewGroup1 != null) {
                while(viewGroup1.getChildCount() > 0) {
                    View view0 = viewGroup1.getChildAt(0);
                    viewGroup1.removeViewAt(0);
                    contentFrameLayout0.addView(view0);
                }
                viewGroup1.setId(-1);
                contentFrameLayout0.setId(0x1020002);
                if(viewGroup1 instanceof FrameLayout) {
                    ((FrameLayout)viewGroup1).setForeground(null);
                }
            }
            this.D.setContentView(viewGroup0);
            contentFrameLayout0.setAttachListener(new AppCompatDelegateImpl.5(this));
            return viewGroup0;
        }
        typedArray0.recycle();
        throw new IllegalStateException("You need to use a Theme.AppCompat theme (or descendant) with this activity.");
    }

    public final View i(View view0, String s, Context context0, AttributeSet attributeSet0) {
        AppCompatViewInflater appCompatViewInflater0;
        if(this.d == null) {
            String s1 = this.g.obtainStyledAttributes(R.styleable.AppCompatTheme).getString(R.styleable.AppCompatTheme_viewInflaterClass);
            if(s1 == null || "android.supportv1.v7.app.AppCompatViewInflater".equals(s1)) {
                appCompatViewInflater0 = new AppCompatViewInflater();
            }
            else {
                try {
                    this.d = (AppCompatViewInflater)Class.forName(s1).getDeclaredConstructor().newInstance();
                    return this.d.a(view0, s, context0, attributeSet0);
                }
                catch(Throwable unused_ex) {
                    appCompatViewInflater0 = new AppCompatViewInflater();
                }
            }
            this.d = appCompatViewInflater0;
        }
        return this.d.a(view0, s, context0, attributeSet0);
    }

    public final boolean j(KeyEvent keyEvent0) {
        Window.Callback window$Callback0 = this.p;
        boolean z = true;
        if(window$Callback0 instanceof Component || window$Callback0 instanceof AppCompatDialog) {
            View view0 = this.D.getDecorView();
            if(view0 != null && KeyEventDispatcher.dispatchBeforeHierarchy(view0, keyEvent0)) {
                return true;
            }
        }
        if(keyEvent0.getKeyCode() == 82 && window$Callback0.dispatchKeyEvent(keyEvent0)) {
            return true;
        }
        int v = keyEvent0.getKeyCode();
        if(keyEvent0.getAction() == 0) {
            switch(v) {
                case 4: {
                    goto label_12;
                }
                case 82: {
                    goto label_16;
                }
            }
            return false;
        label_12:
            if((keyEvent0.getFlags() & 0x80) == 0) {
                z = false;
            }
            this.o = z;
            return false;
        label_16:
            if(keyEvent0.getRepeatCount() == 0) {
                PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = this.n(0);
                if(!appCompatDelegateImpl$PanelFeatureState0.h) {
                    this.r(appCompatDelegateImpl$PanelFeatureState0, keyEvent0);
                    return true;
                }
            }
        }
        else {
            switch(v) {
                case 4: {
                    boolean z1 = this.o;
                    this.o = false;
                    PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = this.n(0);
                    if(!appCompatDelegateImpl$PanelFeatureState1.h) {
                        ActionMode actionMode0 = this.a;
                        if(actionMode0 != null) {
                            actionMode0.b();
                            return true;
                        }
                        this.o();
                        return false;
                    }
                    else if(!z1) {
                        this.g(appCompatDelegateImpl$PanelFeatureState1, true);
                        return true;
                    }
                    break;
                }
                case 82: {
                    if(this.a == null) {
                        PanelFeatureState appCompatDelegateImpl$PanelFeatureState2 = this.n(0);
                        boolean z2 = appCompatDelegateImpl$PanelFeatureState2.h;
                        if(z2 || appCompatDelegateImpl$PanelFeatureState2.g) {
                            this.g(appCompatDelegateImpl$PanelFeatureState2, true);
                            if(z2) {
                            label_50:
                                AudioManager audioManager0 = (AudioManager)this.g.getSystemService("audio");
                                if(audioManager0 != null) {
                                    audioManager0.playSoundEffect(0);
                                    return true;
                                }
                            }
                        }
                        else if(appCompatDelegateImpl$PanelFeatureState2.i) {
                            if(appCompatDelegateImpl$PanelFeatureState2.n) {
                                appCompatDelegateImpl$PanelFeatureState2.i = false;
                                if(this.r(appCompatDelegateImpl$PanelFeatureState2, keyEvent0)) {
                                    this.p(appCompatDelegateImpl$PanelFeatureState2, keyEvent0);
                                    goto label_50;
                                }
                            }
                            else {
                                this.p(appCompatDelegateImpl$PanelFeatureState2, keyEvent0);
                                goto label_50;
                            }
                        }
                    }
                    break;
                }
                default: {
                    return false;
                }
            }
        }
        return true;
    }

    public final void k(int v) {
        PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = this.n(v);
        if(appCompatDelegateImpl$PanelFeatureState0.l != null) {
            Bundle bundle0 = new Bundle();
            appCompatDelegateImpl$PanelFeatureState0.l.w(bundle0);
            if(bundle0.size() > 0) {
                appCompatDelegateImpl$PanelFeatureState0.e = bundle0;
            }
            appCompatDelegateImpl$PanelFeatureState0.l.A();
            appCompatDelegateImpl$PanelFeatureState0.l.clear();
        }
        appCompatDelegateImpl$PanelFeatureState0.n = true;
        appCompatDelegateImpl$PanelFeatureState0.m = true;
    }

    public final void l() {
        if(this.f == null) {
            if(TwilightManager.d == null) {
                Context context0 = this.g.getApplicationContext();
                TwilightManager.d = new TwilightManager(context0, ((LocationManager)context0.getSystemService("location")));
            }
            this.f = new AutoNightModeManager(this, TwilightManager.d);
        }
    }

    public final void m() {
        if(!this.y) {
            this.x = this.h();
            CharSequence charSequence0 = this.p instanceof Activity ? ((Activity)this.p).getTitle() : this.B;
            if(!TextUtils.isEmpty(charSequence0)) {
                TextView textView0 = this.C;
                if(textView0 != null) {
                    textView0.setText(charSequence0);
                }
            }
            this.e();
            this.y = true;
            if(this.n(0).l == null) {
                this.j |= 0x1000;
                if(!this.k) {
                    ViewCompat.postOnAnimation(this.D.getDecorView(), this.l);
                    this.k = true;
                }
            }
        }
    }

    public final PanelFeatureState n(int v) {
        PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState = this.t;
        if(arr_appCompatDelegateImpl$PanelFeatureState == null || arr_appCompatDelegateImpl$PanelFeatureState.length <= v) {
            PanelFeatureState[] arr_appCompatDelegateImpl$PanelFeatureState1 = new PanelFeatureState[v + 1];
            if(arr_appCompatDelegateImpl$PanelFeatureState != null) {
                System.arraycopy(arr_appCompatDelegateImpl$PanelFeatureState, 0, arr_appCompatDelegateImpl$PanelFeatureState1, 0, arr_appCompatDelegateImpl$PanelFeatureState.length);
            }
            this.t = arr_appCompatDelegateImpl$PanelFeatureState1;
            arr_appCompatDelegateImpl$PanelFeatureState = arr_appCompatDelegateImpl$PanelFeatureState1;
        }
        PanelFeatureState appCompatDelegateImpl$PanelFeatureState0 = arr_appCompatDelegateImpl$PanelFeatureState[v];
        if(appCompatDelegateImpl$PanelFeatureState0 == null) {
            appCompatDelegateImpl$PanelFeatureState0 = new PanelFeatureState();  // 初始化器: Ljava/lang/Object;-><init>()V
            appCompatDelegateImpl$PanelFeatureState0.d = v;
            appCompatDelegateImpl$PanelFeatureState0.m = false;
            arr_appCompatDelegateImpl$PanelFeatureState[v] = appCompatDelegateImpl$PanelFeatureState0;
        }
        return appCompatDelegateImpl$PanelFeatureState0;
    }

    public final void o() {
        this.m();
        if(this.i) {
            Window.Callback window$Callback0 = this.p;
            if(window$Callback0 instanceof Activity) {
                new WindowDecorActionBar(((Activity)window$Callback0));
                throw null;
            }
            else if(window$Callback0 instanceof Dialog) {
                new WindowDecorActionBar(((Dialog)window$Callback0));
                throw null;
            }
        }
    }

    @Override  // android.view.LayoutInflater$Factory2
    public final View onCreateView(View view0, String s, Context context0, AttributeSet attributeSet0) {
        return this.i(view0, s, context0, attributeSet0);
    }

    @Override  // android.view.LayoutInflater$Factory
    public final View onCreateView(String s, Context context0, AttributeSet attributeSet0) {
        return this.i(null, s, context0, attributeSet0);
    }

    public final void p(PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, KeyEvent keyEvent0) {
        int v1;
        if(!appCompatDelegateImpl$PanelFeatureState0.h) {
            int v = appCompatDelegateImpl$PanelFeatureState0.d;
            Context context0 = this.g;
            if(v == 0 && (context0.getResources().getConfiguration().screenLayout & 15) == 4) {
                return;
            }
            Window.Callback window$Callback0 = this.D.getCallback();
            if(window$Callback0 != null && !window$Callback0.onMenuOpened(v, appCompatDelegateImpl$PanelFeatureState0.l)) {
                this.g(appCompatDelegateImpl$PanelFeatureState0, true);
                return;
            }
            WindowManager windowManager0 = (WindowManager)context0.getSystemService("window");
            if(windowManager0 == null) {
                return;
            }
            if(!this.r(appCompatDelegateImpl$PanelFeatureState0, keyEvent0)) {
                return;
            }
            ViewGroup viewGroup0 = appCompatDelegateImpl$PanelFeatureState0.c;
            if(viewGroup0 != null && !appCompatDelegateImpl$PanelFeatureState0.m) {
                View view0 = appCompatDelegateImpl$PanelFeatureState0.b;
                if(view0 == null) {
                    goto label_43;
                }
                ViewGroup.LayoutParams viewGroup$LayoutParams0 = view0.getLayoutParams();
                if(viewGroup$LayoutParams0 == null || viewGroup$LayoutParams0.width != -1) {
                    goto label_43;
                }
                v1 = -1;
                goto label_44;
            }
            if(viewGroup0 == null) {
                this.o();
                appCompatDelegateImpl$PanelFeatureState0.c(context0);
                appCompatDelegateImpl$PanelFeatureState0.c = new ListMenuDecorView(this, appCompatDelegateImpl$PanelFeatureState0.k);
                appCompatDelegateImpl$PanelFeatureState0.f = 81;
            }
            else if(appCompatDelegateImpl$PanelFeatureState0.m && viewGroup0.getChildCount() > 0) {
                appCompatDelegateImpl$PanelFeatureState0.c.removeAllViews();
            }
            View view1 = appCompatDelegateImpl$PanelFeatureState0.b;
            if(view1 != null) {
                appCompatDelegateImpl$PanelFeatureState0.o = view1;
                ViewGroup.LayoutParams viewGroup$LayoutParams1 = view1.getLayoutParams();
                if(viewGroup$LayoutParams1 == null) {
                    viewGroup$LayoutParams1 = new ViewGroup.LayoutParams(-2, -2);
                }
                appCompatDelegateImpl$PanelFeatureState0.c.setBackgroundResource(appCompatDelegateImpl$PanelFeatureState0.a);
                ViewParent viewParent0 = appCompatDelegateImpl$PanelFeatureState0.o.getParent();
                if(viewParent0 != null && viewParent0 instanceof ViewGroup) {
                    ((ViewGroup)viewParent0).removeView(appCompatDelegateImpl$PanelFeatureState0.o);
                }
                appCompatDelegateImpl$PanelFeatureState0.c.addView(appCompatDelegateImpl$PanelFeatureState0.o, viewGroup$LayoutParams1);
                if(!appCompatDelegateImpl$PanelFeatureState0.o.hasFocus()) {
                    appCompatDelegateImpl$PanelFeatureState0.o.requestFocus();
                }
            label_43:
                v1 = -2;
            label_44:
                appCompatDelegateImpl$PanelFeatureState0.g = false;
                WindowManager.LayoutParams windowManager$LayoutParams0 = new WindowManager.LayoutParams(v1, -2, 0, 0, 1002, 0x820000, -3);
                windowManager$LayoutParams0.gravity = appCompatDelegateImpl$PanelFeatureState0.f;
                windowManager$LayoutParams0.windowAnimations = appCompatDelegateImpl$PanelFeatureState0.p;
                windowManager0.addView(appCompatDelegateImpl$PanelFeatureState0.c, windowManager$LayoutParams0);
                appCompatDelegateImpl$PanelFeatureState0.h = true;
                return;
            }
            if(appCompatDelegateImpl$PanelFeatureState0.l != null) {
                if(this.s == null) {
                    this.s = new PanelMenuPresenterCallback(this);
                }
                appCompatDelegateImpl$PanelFeatureState0.a(this.s);
                appCompatDelegateImpl$PanelFeatureState0.o = null;
            }
        }
    }

    public final boolean q(PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, int v, KeyEvent keyEvent0) {
        if(keyEvent0.isSystem()) {
            return false;
        }
        if(appCompatDelegateImpl$PanelFeatureState0.i || this.r(appCompatDelegateImpl$PanelFeatureState0, keyEvent0)) {
            return appCompatDelegateImpl$PanelFeatureState0.l == null ? false : appCompatDelegateImpl$PanelFeatureState0.l.performShortcut(v, keyEvent0, 1);
        }
        return false;
    }

    public final boolean r(PanelFeatureState appCompatDelegateImpl$PanelFeatureState0, KeyEvent keyEvent0) {
        if(appCompatDelegateImpl$PanelFeatureState0.i) {
            return true;
        }
        PanelFeatureState appCompatDelegateImpl$PanelFeatureState1 = this.u;
        if(appCompatDelegateImpl$PanelFeatureState1 != null && appCompatDelegateImpl$PanelFeatureState1 != appCompatDelegateImpl$PanelFeatureState0) {
            this.g(appCompatDelegateImpl$PanelFeatureState1, false);
        }
        Window.Callback window$Callback0 = this.D.getCallback();
        int v = appCompatDelegateImpl$PanelFeatureState0.d;
        if(window$Callback0 != null) {
            appCompatDelegateImpl$PanelFeatureState0.b = window$Callback0.onCreatePanelView(v);
        }
        if(appCompatDelegateImpl$PanelFeatureState0.b == null) {
            MenuBuilder menuBuilder0 = appCompatDelegateImpl$PanelFeatureState0.l;
            if(menuBuilder0 == null || appCompatDelegateImpl$PanelFeatureState0.n) {
                if(menuBuilder0 == null) {
                    MenuBuilder menuBuilder1 = new MenuBuilder(this.g);
                    menuBuilder1.x(this);
                    appCompatDelegateImpl$PanelFeatureState0.b(menuBuilder1);
                    if(appCompatDelegateImpl$PanelFeatureState0.l == null) {
                        return false;
                    }
                }
                appCompatDelegateImpl$PanelFeatureState0.l.A();
                if(!window$Callback0.onCreatePanelMenu(v, appCompatDelegateImpl$PanelFeatureState0.l)) {
                    appCompatDelegateImpl$PanelFeatureState0.b(null);
                    return false;
                }
                appCompatDelegateImpl$PanelFeatureState0.n = false;
            }
            appCompatDelegateImpl$PanelFeatureState0.l.A();
            Bundle bundle0 = appCompatDelegateImpl$PanelFeatureState0.e;
            if(bundle0 != null) {
                appCompatDelegateImpl$PanelFeatureState0.l.v(bundle0);
                appCompatDelegateImpl$PanelFeatureState0.e = null;
            }
            if(!window$Callback0.onPreparePanel(0, appCompatDelegateImpl$PanelFeatureState0.b, appCompatDelegateImpl$PanelFeatureState0.l)) {
                appCompatDelegateImpl$PanelFeatureState0.l.z();
                return false;
            }
            boolean z = KeyCharacterMap.load((keyEvent0 == null ? -1 : keyEvent0.getDeviceId())).getKeyboardType() != 1;
            appCompatDelegateImpl$PanelFeatureState0.l.setQwertyMode(z);
            appCompatDelegateImpl$PanelFeatureState0.l.z();
        }
        appCompatDelegateImpl$PanelFeatureState0.i = true;
        appCompatDelegateImpl$PanelFeatureState0.g = false;
        this.u = appCompatDelegateImpl$PanelFeatureState0;
        return true;
    }

    public final ActionMode s(android.supportv1.v7.view.ActionMode.Callback actionMode$Callback0) {
        Context context0;
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = this.h;
        if(viewPropertyAnimatorCompat0 != null) {
            viewPropertyAnimatorCompat0.cancel();
        }
        ActionMode actionMode0 = this.a;
        if(actionMode0 != null) {
            actionMode0.b();
        }
        if(this.c == null) {
            if(this.m) {
                TypedValue typedValue0 = new TypedValue();
                Resources.Theme resources$Theme0 = this.g.getTheme();
                resources$Theme0.resolveAttribute(R.attr.actionBarTheme, typedValue0, true);
                if(typedValue0.resourceId == 0) {
                    context0 = this.g;
                }
                else {
                    Resources.Theme resources$Theme1 = this.g.getResources().newTheme();
                    resources$Theme1.setTo(resources$Theme0);
                    resources$Theme1.applyStyle(typedValue0.resourceId, true);
                    context0 = new ContextThemeWrapper(this.g, 0);
                    ((ContextThemeWrapper)context0).getTheme().setTo(resources$Theme1);
                }
                this.c = new ActionBarContextView(context0);
                PopupWindow popupWindow0 = new PopupWindow(context0, null, R.attr.actionModePopupWindowStyle);
                this.b = popupWindow0;
                PopupWindowCompat.setWindowLayoutType(popupWindow0, 2);
                this.b.setContentView(this.c);
                this.b.setWidth(-1);
                context0.getTheme().resolveAttribute(R.attr.actionBarSize, typedValue0, true);
                int v = TypedValue.complexToDimensionPixelSize(typedValue0.data, context0.getResources().getDisplayMetrics());
                this.c.setContentHeight(v);
                this.b.setHeight(-2);
                this.v = new AppCompatDelegateImpl.6(this);
            }
            else {
                a.f(this.x.findViewById(R.id.action_mode_bar_stub));
            }
        }
        if(this.c != null) {
            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1 = this.h;
            if(viewPropertyAnimatorCompat1 != null) {
                viewPropertyAnimatorCompat1.cancel();
            }
            this.c.f();
            StandaloneActionMode standaloneActionMode0 = new StandaloneActionMode(this.c.getContext(), this.c, actionMode$Callback0);
            if(!((ActionModeCallbackWrapperV9)actionMode$Callback0).b(standaloneActionMode0, standaloneActionMode0.o())) {
                this.a = null;
                return null;
            }
            standaloneActionMode0.g();
            this.c.d(standaloneActionMode0);
            throw null;
        }
        return this.a;
    }

    public final void t() {
        if(this.y) {
            throw new AndroidRuntimeException("Window feature must be requested before adding content");
        }
    }

    public final int u(int v) {
        int v3;
        int v2;
        int v1 = 0;
        if(this.c == null || !(this.c.getLayoutParams() instanceof ViewGroup.MarginLayoutParams)) {
            v2 = 0;
        }
        else {
            ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)this.c.getLayoutParams();
            v2 = 1;
            if(this.c.isShown()) {
                if(this.z == null) {
                    this.z = new Rect();
                    this.A = new Rect();
                }
                Rect rect0 = this.z;
                Rect rect1 = this.A;
                rect0.set(0, v, 0, 0);
                ViewUtils.a(this.x, rect0, rect1);
                if(viewGroup$MarginLayoutParams0.topMargin == (rect1.top == 0 ? v : 0)) {
                    v3 = 0;
                }
                else {
                    viewGroup$MarginLayoutParams0.topMargin = v;
                    View view0 = this.w;
                    if(view0 == null) {
                        View view1 = new View(this.g);
                        this.w = view1;
                        view1.setBackgroundColor(this.g.getResources().getColor(R.color.abc_input_method_navigation_guard));
                        this.x.addView(this.w, -1, new ViewGroup.LayoutParams(-1, v));
                    }
                    else {
                        ViewGroup.LayoutParams viewGroup$LayoutParams0 = view0.getLayoutParams();
                        if(viewGroup$LayoutParams0.height != v) {
                            viewGroup$LayoutParams0.height = v;
                            this.w.setLayoutParams(viewGroup$LayoutParams0);
                        }
                    }
                    v3 = 1;
                }
                if(this.w == null) {
                    v2 = 0;
                }
                if(!this.r && v2 != 0) {
                    v = 0;
                }
            }
            else {
                if(viewGroup$MarginLayoutParams0.topMargin == 0) {
                    v2 = 0;
                }
                else {
                    viewGroup$MarginLayoutParams0.topMargin = 0;
                }
                v3 = v2;
                v2 = 0;
            }
            if(v3 != 0) {
                this.c.setLayoutParams(viewGroup$MarginLayoutParams0);
            }
        }
        View view2 = this.w;
        if(view2 != null) {
            if(v2 == 0) {
                v1 = 8;
            }
            view2.setVisibility(v1);
        }
        return v;
    }
}


package android.supportv1.v7.app;

import android.supportv1.v4.view.OnApplyWindowInsetsListener;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.view.WindowInsetsCompat;
import android.view.View;

class AppCompatDelegateImpl.3 implements OnApplyWindowInsetsListener {
    public final AppCompatDelegateImpl a;

    public AppCompatDelegateImpl.3(AppCompatDelegateImpl appCompatDelegateImpl0) {
        this.a = appCompatDelegateImpl0;
    }

    @Override  // android.supportv1.v4.view.OnApplyWindowInsetsListener
    public final WindowInsetsCompat onApplyWindowInsets(View view0, WindowInsetsCompat windowInsetsCompat0) {
        int v = windowInsetsCompat0.getSystemWindowInsetTop();
        int v1 = this.a.u(v);
        if(v != v1) {
            windowInsetsCompat0 = windowInsetsCompat0.replaceSystemWindowInsets(windowInsetsCompat0.getSystemWindowInsetLeft(), v1, windowInsetsCompat0.getSystemWindowInsetRight(), windowInsetsCompat0.getSystemWindowInsetBottom());
        }
        return ViewCompat.onApplyWindowInsets(view0, windowInsetsCompat0);
    }
}


package android.supportv1.v7.app;

import android.supportv1.v4.view.ViewPropertyAnimatorCompat;
import android.supportv1.v7.view.menu.MenuBuilder;
import android.supportv1.v7.widget.ContentFrameLayout.OnAttachListener;

class AppCompatDelegateImpl.5 implements OnAttachListener {
    public final AppCompatDelegateImpl a;

    public AppCompatDelegateImpl.5(AppCompatDelegateImpl appCompatDelegateImpl0) {
        this.a = appCompatDelegateImpl0;
    }

    @Override  // android.supportv1.v7.widget.ContentFrameLayout$OnAttachListener
    public final void onDetachedFromWindow() {
        AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
        if(appCompatDelegateImpl0.b != null) {
            appCompatDelegateImpl0.D.getDecorView().removeCallbacks(appCompatDelegateImpl0.v);
            if(appCompatDelegateImpl0.b.isShowing()) {
                try {
                    appCompatDelegateImpl0.b.dismiss();
                }
                catch(IllegalArgumentException unused_ex) {
                }
            }
            appCompatDelegateImpl0.b = null;
        }
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = appCompatDelegateImpl0.h;
        if(viewPropertyAnimatorCompat0 != null) {
            viewPropertyAnimatorCompat0.cancel();
        }
        MenuBuilder menuBuilder0 = appCompatDelegateImpl0.n(0).l;
        if(menuBuilder0 != null) {
            menuBuilder0.d(true);
        }
    }
}


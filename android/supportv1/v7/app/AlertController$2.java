package android.supportv1.v7.app;

import android.supportv1.v4.widget.NestedScrollView.OnScrollChangeListener;
import android.supportv1.v4.widget.NestedScrollView;
import android.view.View;

class AlertController.2 implements OnScrollChangeListener {
    public final View a;
    public final View b;

    public AlertController.2(View view0, View view1) {
        this.b = view0;
        this.a = view1;
    }

    @Override  // android.supportv1.v4.widget.NestedScrollView$OnScrollChangeListener
    public final void onScrollChange(NestedScrollView nestedScrollView0, int v, int v1, int v2, int v3) {
        AlertController.b(nestedScrollView0, this.b, this.a);
    }
}


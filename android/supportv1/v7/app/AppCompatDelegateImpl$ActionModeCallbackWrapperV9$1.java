package android.supportv1.v7.app;

import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter;
import android.view.View;
import android.widget.PopupWindow;

class AppCompatDelegateImpl.ActionModeCallbackWrapperV9.1 extends ViewPropertyAnimatorListenerAdapter {
    public final ActionModeCallbackWrapperV9 a;

    public AppCompatDelegateImpl.ActionModeCallbackWrapperV9.1(ActionModeCallbackWrapperV9 appCompatDelegateImpl$ActionModeCallbackWrapperV90) {
        this.a = appCompatDelegateImpl$ActionModeCallbackWrapperV90;
        super();
    }

    @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListenerAdapter
    public final void onAnimationEnd(View view0) {
        ActionModeCallbackWrapperV9 appCompatDelegateImpl$ActionModeCallbackWrapperV90 = this.a;
        appCompatDelegateImpl$ActionModeCallbackWrapperV90.b.c.setVisibility(8);
        AppCompatDelegateImpl appCompatDelegateImpl0 = appCompatDelegateImpl$ActionModeCallbackWrapperV90.b;
        PopupWindow popupWindow0 = appCompatDelegateImpl0.b;
        if(popupWindow0 != null) {
            popupWindow0.dismiss();
        }
        else if(appCompatDelegateImpl0.c.getParent() instanceof View) {
            ViewCompat.requestApplyInsets(((View)appCompatDelegateImpl$ActionModeCallbackWrapperV90.b.c.getParent()));
        }
        appCompatDelegateImpl$ActionModeCallbackWrapperV90.b.c.removeAllViews();
        appCompatDelegateImpl$ActionModeCallbackWrapperV90.b.h.setListener(null);
        appCompatDelegateImpl$ActionModeCallbackWrapperV90.b.h = null;
    }
}


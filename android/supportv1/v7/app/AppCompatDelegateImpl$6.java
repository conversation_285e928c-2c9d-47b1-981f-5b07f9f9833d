package android.supportv1.v7.app;

import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.view.ViewPropertyAnimatorCompat;

class AppCompatDelegateImpl.6 implements Runnable {
    public final AppCompatDelegateImpl a;

    public AppCompatDelegateImpl.6(AppCompatDelegateImpl appCompatDelegateImpl0) {
        this.a = appCompatDelegateImpl0;
    }

    @Override
    public final void run() {
        AppCompatDelegateImpl appCompatDelegateImpl0 = this.a;
        appCompatDelegateImpl0.b.showAtLocation(appCompatDelegateImpl0.c, 55, 0, 0);
        ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = appCompatDelegateImpl0.h;
        if(viewPropertyAnimatorCompat0 != null) {
            viewPropertyAnimatorCompat0.cancel();
        }
        if(appCompatDelegateImpl0.y && (appCompatDelegateImpl0.x != null && ViewCompat.isLaidOut(appCompatDelegateImpl0.x))) {
            appCompatDelegateImpl0.c.setAlpha(0.0f);
            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat1 = ViewCompat.animate(appCompatDelegateImpl0.c).alpha(1.0f);
            appCompatDelegateImpl0.h = viewPropertyAnimatorCompat1;
            viewPropertyAnimatorCompat1.setListener(new AppCompatDelegateImpl.6.1(this));
            return;
        }
        appCompatDelegateImpl0.c.setAlpha(1.0f);
        appCompatDelegateImpl0.c.setVisibility(0);
    }
}


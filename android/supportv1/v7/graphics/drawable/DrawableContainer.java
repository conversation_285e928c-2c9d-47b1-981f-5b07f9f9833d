package android.supportv1.v7.graphics.drawable;

import android.content.res.ColorStateList;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.PorterDuff.Mode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable.Callback;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.os.SystemClock;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.util.SparseArray;
import jeb.synthetic.FIN;

abstract class DrawableContainer extends Drawable implements Drawable.Callback {
    static class BlockInvalidateCallback implements Drawable.Callback {
        public Drawable.Callback a;

        @Override  // android.graphics.drawable.Drawable$Callback
        public final void invalidateDrawable(Drawable drawable0) {
        }

        @Override  // android.graphics.drawable.Drawable$Callback
        public final void scheduleDrawable(Drawable drawable0, Runnable runnable0, long v) {
            Drawable.Callback drawable$Callback0 = this.a;
            if(drawable$Callback0 != null) {
                drawable$Callback0.scheduleDrawable(drawable0, runnable0, v);
            }
        }

        @Override  // android.graphics.drawable.Drawable$Callback
        public final void unscheduleDrawable(Drawable drawable0, Runnable runnable0) {
            Drawable.Callback drawable$Callback0 = this.a;
            if(drawable$Callback0 != null) {
                drawable$Callback0.unscheduleDrawable(drawable0, runnable0);
            }
        }
    }

    static abstract class DrawableContainerState extends Drawable.ConstantState {
        public int A;
        public int B;
        public final DrawableContainer C;
        public Resources D;
        public ColorStateList E;
        public PorterDuff.Mode F;
        public boolean G;
        public boolean a;
        public boolean b;
        public int c;
        public boolean d;
        public boolean e;
        public boolean f;
        public boolean g;
        public boolean h;
        public int i;
        public ColorFilter j;
        public int k;
        public int l;
        public int m;
        public Rect n;
        public boolean o;
        public int p;
        public int q;
        public boolean r;
        public SparseArray s;
        public Drawable[] t;
        public int u;
        public int v;
        public boolean w;
        public boolean x;
        public boolean y;
        public int z;

        public DrawableContainerState(DrawableContainerState drawableContainer$DrawableContainerState0, DrawableContainer drawableContainer0, Resources resources0) {
            Resources resources1;
            int v = 0xA0;
            this.q = 0xA0;
            this.G = false;
            this.o = false;
            this.r = true;
            this.u = 0;
            this.v = 0;
            this.C = drawableContainer0;
            if(resources0 == null) {
                resources1 = drawableContainer$DrawableContainerState0 == null ? null : drawableContainer$DrawableContainerState0.D;
            }
            else {
                resources1 = resources0;
            }
            this.D = resources1;
            int v2 = drawableContainer$DrawableContainerState0 == null ? 0 : drawableContainer$DrawableContainerState0.q;
            if(resources0 != null) {
                v2 = resources0.getDisplayMetrics().densityDpi;
            }
            if(v2 != 0) {
                v = v2;
            }
            this.q = v;
            if(drawableContainer$DrawableContainerState0 != null) {
                this.c = drawableContainer$DrawableContainerState0.c;
                this.i = drawableContainer$DrawableContainerState0.i;
                this.e = true;
                this.b = true;
                this.G = drawableContainer$DrawableContainerState0.G;
                this.o = drawableContainer$DrawableContainerState0.o;
                this.r = drawableContainer$DrawableContainerState0.r;
                this.z = drawableContainer$DrawableContainerState0.z;
                this.u = drawableContainer$DrawableContainerState0.u;
                this.v = drawableContainer$DrawableContainerState0.v;
                this.a = drawableContainer$DrawableContainerState0.a;
                this.j = drawableContainer$DrawableContainerState0.j;
                this.w = drawableContainer$DrawableContainerState0.w;
                this.E = drawableContainer$DrawableContainerState0.E;
                this.F = drawableContainer$DrawableContainerState0.F;
                this.x = drawableContainer$DrawableContainerState0.x;
                this.y = drawableContainer$DrawableContainerState0.y;
                if(drawableContainer$DrawableContainerState0.q == v) {
                    if(drawableContainer$DrawableContainerState0.g) {
                        this.n = new Rect(drawableContainer$DrawableContainerState0.n);
                        this.g = true;
                    }
                    if(drawableContainer$DrawableContainerState0.d) {
                        this.p = drawableContainer$DrawableContainerState0.p;
                        this.k = drawableContainer$DrawableContainerState0.k;
                        this.m = drawableContainer$DrawableContainerState0.m;
                        this.l = drawableContainer$DrawableContainerState0.l;
                        this.d = true;
                    }
                }
                if(drawableContainer$DrawableContainerState0.f) {
                    this.B = drawableContainer$DrawableContainerState0.B;
                    this.f = true;
                }
                if(drawableContainer$DrawableContainerState0.h) {
                    this.h = true;
                }
                Drawable[] arr_drawable = drawableContainer$DrawableContainerState0.t;
                this.t = new Drawable[arr_drawable.length];
                this.A = drawableContainer$DrawableContainerState0.A;
                SparseArray sparseArray0 = drawableContainer$DrawableContainerState0.s;
                this.s = sparseArray0 == null ? new SparseArray(this.A) : sparseArray0.clone();
                int v3 = this.A;
                for(int v1 = 0; v1 < v3; ++v1) {
                    Drawable drawable0 = arr_drawable[v1];
                    if(drawable0 != null) {
                        Drawable.ConstantState drawable$ConstantState0 = drawable0.getConstantState();
                        if(drawable$ConstantState0 == null) {
                            this.t[v1] = arr_drawable[v1];
                        }
                        else {
                            this.s.put(v1, drawable$ConstantState0);
                        }
                    }
                }
                return;
            }
            this.t = new Drawable[10];
            this.A = 0;
        }

        public final int a(Drawable drawable0) {
            int v = this.A;
            if(v >= this.t.length) {
                Drawable[] arr_drawable = new Drawable[v + 10];
                System.arraycopy(((StateListState)this).t, 0, arr_drawable, 0, v);
                ((StateListState)this).t = arr_drawable;
                int[][] arr2_v = new int[v + 10][];
                System.arraycopy(((StateListState)this).H, 0, arr2_v, 0, v);
                ((StateListState)this).H = arr2_v;
            }
            drawable0.mutate();
            drawable0.setVisible(false, true);
            drawable0.setCallback(this.C);
            this.t[v] = drawable0;
            ++this.A;
            int v1 = this.i;
            this.i = drawable0.getChangingConfigurations() | v1;
            this.f = false;
            this.h = false;
            this.n = null;
            this.g = false;
            this.d = false;
            this.e = false;
            return v;
        }

        public final void b() {
            this.d = true;
            this.c();
            int v = this.A;
            Drawable[] arr_drawable = this.t;
            this.k = -1;
            this.p = -1;
            this.l = 0;
            this.m = 0;
            for(int v1 = 0; v1 < v; ++v1) {
                Drawable drawable0 = arr_drawable[v1];
                int v2 = drawable0.getIntrinsicWidth();
                if(v2 > this.p) {
                    this.p = v2;
                }
                int v3 = drawable0.getIntrinsicHeight();
                if(v3 > this.k) {
                    this.k = v3;
                }
                int v4 = drawable0.getMinimumWidth();
                if(v4 > this.m) {
                    this.m = v4;
                }
                int v5 = drawable0.getMinimumHeight();
                if(v5 > this.l) {
                    this.l = v5;
                }
            }
        }

        public final void c() {
            SparseArray sparseArray0 = this.s;
            if(sparseArray0 != null) {
                int v = sparseArray0.size();
                for(int v1 = 0; v1 < v; ++v1) {
                    int v2 = this.s.keyAt(v1);
                    Drawable.ConstantState drawable$ConstantState0 = (Drawable.ConstantState)this.s.valueAt(v1);
                    Drawable[] arr_drawable = this.t;
                    Drawable drawable0 = drawable$ConstantState0.newDrawable(this.D);
                    if(Build.VERSION.SDK_INT >= 23) {
                        drawable0.setLayoutDirection(this.z);
                    }
                    Drawable drawable1 = drawable0.mutate();
                    drawable1.setCallback(this.C);
                    arr_drawable[v2] = drawable1;
                }
                this.s = null;
            }
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final boolean canApplyTheme() {
            int v = this.A;
            Drawable[] arr_drawable = this.t;
            for(int v1 = 0; v1 < v; ++v1) {
                Drawable drawable0 = arr_drawable[v1];
                if(drawable0 == null) {
                    Drawable.ConstantState drawable$ConstantState0 = (Drawable.ConstantState)this.s.get(v1);
                    if(drawable$ConstantState0 != null && drawable$ConstantState0.canApplyTheme()) {
                        return true;
                    }
                }
                else if(drawable0.canApplyTheme()) {
                    return true;
                }
            }
            return false;
        }

        public final Drawable d(int v) {
            Drawable drawable0 = this.t[v];
            if(drawable0 != null) {
                return drawable0;
            }
            SparseArray sparseArray0 = this.s;
            if(sparseArray0 != null) {
                int v1 = sparseArray0.indexOfKey(v);
                if(v1 >= 0) {
                    Drawable drawable1 = ((Drawable.ConstantState)this.s.valueAt(v1)).newDrawable(this.D);
                    if(Build.VERSION.SDK_INT >= 23) {
                        drawable1.setLayoutDirection(this.z);
                    }
                    Drawable drawable2 = drawable1.mutate();
                    drawable2.setCallback(this.C);
                    this.t[v] = drawable2;
                    this.s.removeAt(v1);
                    if(this.s.size() == 0) {
                        this.s = null;
                    }
                    return drawable2;
                }
            }
            return null;
        }

        public final void e(boolean z) {
            this.o = z;
        }

        public final void f(int v) {
            this.u = v;
        }

        public final void g(int v) {
            this.v = v;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final int getChangingConfigurations() {
            return this.c | this.i;
        }

        public final void h(boolean z) {
            this.G = z;
        }
    }

    public int a;
    public Runnable b;
    public BlockInvalidateCallback c;
    public int d;
    public Drawable e;
    public DrawableContainerState f;
    public long g;
    public long h;
    public boolean i;
    public Rect j;
    public Drawable k;
    public boolean l;
    public static final int m;

    public final void a(boolean z) {
        int v3;
        int v = 1;
        this.i = true;
        long v1 = SystemClock.uptimeMillis();
        Drawable drawable0 = this.e;
        if(drawable0 == null) {
            this.g = 0L;
            v3 = 0;
        }
        else {
            long v2 = this.g;
            if(v2 == 0L) {
                v3 = 0;
            }
            else if(v2 <= v1) {
                drawable0.setAlpha(this.a);
                this.g = 0L;
                v3 = 0;
            }
            else {
                drawable0.setAlpha((0xFF - ((int)((v2 - v1) * 0xFFL)) / this.f.u) * this.a / 0xFF);
                v3 = 1;
            }
        }
        Drawable drawable1 = this.k;
        if(drawable1 == null) {
            this.h = 0L;
            v = v3;
        }
        else {
            long v4 = this.h;
            if(v4 == 0L) {
                v = v3;
            }
            else if(v4 <= v1) {
                drawable1.setVisible(false, false);
                this.k = null;
                this.h = 0L;
                v = v3;
            }
            else {
                drawable1.setAlpha(((int)((v4 - v1) * 0xFFL)) / this.f.v * this.a / 0xFF);
            }
        }
        if(z && v != 0) {
            this.scheduleSelf(this.b, v1 + 16L);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void applyTheme(Resources.Theme resources$Theme0) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(resources$Theme0 == null) {
            drawableContainer$DrawableContainerState0.getClass();
        }
        else {
            drawableContainer$DrawableContainerState0.c();
            int v = drawableContainer$DrawableContainerState0.A;
            Drawable[] arr_drawable = drawableContainer$DrawableContainerState0.t;
            for(int v1 = 0; v1 < v; ++v1) {
                Drawable drawable0 = arr_drawable[v1];
                if(drawable0 != null && drawable0.canApplyTheme()) {
                    arr_drawable[v1].applyTheme(resources$Theme0);
                    drawableContainer$DrawableContainerState0.i |= arr_drawable[v1].getChangingConfigurations();
                }
            }
            Resources resources0 = resources$Theme0.getResources();
            if(resources0 != null) {
                drawableContainer$DrawableContainerState0.D = resources0;
                int v2 = resources0.getDisplayMetrics().densityDpi;
                if(v2 == 0) {
                    v2 = 0xA0;
                }
                int v3 = drawableContainer$DrawableContainerState0.q;
                drawableContainer$DrawableContainerState0.q = v2;
                if(v3 != v2) {
                    drawableContainer$DrawableContainerState0.d = false;
                    drawableContainer$DrawableContainerState0.g = false;
                }
            }
        }
    }

    public final void b(Drawable drawable0) {
        if(this.c == null) {
            this.c = new BlockInvalidateCallback();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        BlockInvalidateCallback drawableContainer$BlockInvalidateCallback0 = this.c;
        drawableContainer$BlockInvalidateCallback0.a = drawable0.getCallback();
        drawable0.setCallback(drawableContainer$BlockInvalidateCallback0);
        try {
            if(this.f.u <= 0 && this.i) {
                drawable0.setAlpha(this.a);
            }
            DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
            if(drawableContainer$DrawableContainerState0.w) {
                drawable0.setColorFilter(drawableContainer$DrawableContainerState0.j);
            }
            else {
                if(drawableContainer$DrawableContainerState0.x) {
                    DrawableCompat.setTintList(drawable0, drawableContainer$DrawableContainerState0.E);
                }
                DrawableContainerState drawableContainer$DrawableContainerState1 = this.f;
                if(drawableContainer$DrawableContainerState1.y) {
                    DrawableCompat.setTintMode(drawable0, drawableContainer$DrawableContainerState1.F);
                }
            }
            drawable0.setVisible(this.isVisible(), true);
            drawable0.setDither(this.f.r);
            drawable0.setState(this.getState());
            drawable0.setLevel(this.getLevel());
            drawable0.setBounds(this.getBounds());
            if(Build.VERSION.SDK_INT >= 23) {
                drawable0.setLayoutDirection(this.getLayoutDirection());
            }
            drawable0.setAutoMirrored(this.f.a);
            Rect rect0 = this.j;
            if(rect0 != null) {
                drawable0.setHotspotBounds(rect0.left, rect0.top, rect0.right, rect0.bottom);
            }
        }
        finally {
            Drawable.Callback drawable$Callback0 = this.c.a;
            this.c.a = null;
            drawable0.setCallback(drawable$Callback0);
        }
    }

    public final boolean c(int v) {
        if(v == this.d) {
            return false;
        }
        long v1 = SystemClock.uptimeMillis();
        if(this.f.v > 0) {
            Drawable drawable0 = this.k;
            if(drawable0 != null) {
                drawable0.setVisible(false, false);
            }
            Drawable drawable1 = this.e;
            if(drawable1 == null) {
                this.k = null;
                this.h = 0L;
            }
            else {
                this.k = drawable1;
                this.h = ((long)this.f.v) + v1;
            }
        }
        else {
            Drawable drawable2 = this.e;
            if(drawable2 != null) {
                drawable2.setVisible(false, false);
            }
        }
        if(v >= 0) {
            DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
            if(v < drawableContainer$DrawableContainerState0.A) {
                Drawable drawable3 = drawableContainer$DrawableContainerState0.d(v);
                this.e = drawable3;
                this.d = v;
                if(drawable3 != null) {
                    int v2 = this.f.u;
                    if(v2 > 0) {
                        this.g = v1 + ((long)v2);
                    }
                    this.b(drawable3);
                }
            }
            else {
                this.e = null;
                this.d = -1;
            }
        }
        else {
            this.e = null;
            this.d = -1;
        }
        if(this.g != 0L || this.h != 0L) {
            Runnable runnable0 = this.b;
            if(runnable0 == null) {
                this.b = new DrawableContainer.1(((AnimatedStateListDrawableCompat)this));
            }
            else {
                this.unscheduleSelf(runnable0);
            }
            this.a(true);
        }
        this.invalidateSelf();
        return true;
    }

    public abstract void d(AnimatedStateListState arg1);

    @Override  // android.graphics.drawable.Drawable
    public void draw(Canvas canvas0) {
        Drawable drawable0 = this.e;
        if(drawable0 != null) {
            drawable0.draw(canvas0);
        }
        Drawable drawable1 = this.k;
        if(drawable1 != null) {
            drawable1.draw(canvas0);
        }
    }

    public final void e(Resources resources0) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(resources0 == null) {
            drawableContainer$DrawableContainerState0.getClass();
        }
        else {
            drawableContainer$DrawableContainerState0.D = resources0;
            int v = resources0.getDisplayMetrics().densityDpi;
            if(v == 0) {
                v = 0xA0;
            }
            int v1 = drawableContainer$DrawableContainerState0.q;
            drawableContainer$DrawableContainerState0.q = v;
            if(v1 != v) {
                drawableContainer$DrawableContainerState0.d = false;
                drawableContainer$DrawableContainerState0.g = false;
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public int getChangingConfigurations() {
        return super.getChangingConfigurations() | this.f.getChangingConfigurations();
    }

    @Override  // android.graphics.drawable.Drawable
    public final Drawable.ConstantState getConstantState() {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        __monitor_enter(drawableContainer$DrawableContainerState0);
        int v = FIN.finallyOpen$NT();
        if(drawableContainer$DrawableContainerState0.e) {
            FIN.finallyExec$NT(v);
            if(!drawableContainer$DrawableContainerState0.b) {
                return null;
            }
        }
        else {
            drawableContainer$DrawableContainerState0.c();
            drawableContainer$DrawableContainerState0.e = true;
            int v1 = drawableContainer$DrawableContainerState0.A;
            Drawable[] arr_drawable = drawableContainer$DrawableContainerState0.t;
            for(int v2 = 0; v2 < v1; ++v2) {
                if(arr_drawable[v2].getConstantState() == null) {
                    drawableContainer$DrawableContainerState0.b = false;
                    FIN.finallyCodeBegin$NT(v);
                    __monitor_exit(drawableContainer$DrawableContainerState0);
                    FIN.finallyCodeEnd$NT(v);
                    return null;
                }
            }
            drawableContainer$DrawableContainerState0.b = true;
            FIN.finallyExec$NT(v);
        }
        DrawableContainerState drawableContainer$DrawableContainerState1 = this.f;
        drawableContainer$DrawableContainerState1.c = this.getChangingConfigurations();
        return this.f;
    }

    @Override  // android.graphics.drawable.Drawable
    public void getHotspotBounds(Rect rect0) {
        Rect rect1 = this.j;
        if(rect1 != null) {
            rect0.set(rect1);
            return;
        }
        super.getHotspotBounds(rect0);
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicHeight() {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(drawableContainer$DrawableContainerState0.o) {
            if(!drawableContainer$DrawableContainerState0.d) {
                drawableContainer$DrawableContainerState0.b();
            }
            return drawableContainer$DrawableContainerState0.k;
        }
        return this.e == null ? -1 : this.e.getIntrinsicHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getIntrinsicWidth() {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(drawableContainer$DrawableContainerState0.o) {
            if(!drawableContainer$DrawableContainerState0.d) {
                drawableContainer$DrawableContainerState0.b();
            }
            return drawableContainer$DrawableContainerState0.p;
        }
        return this.e == null ? -1 : this.e.getIntrinsicWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getMinimumHeight() {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(drawableContainer$DrawableContainerState0.o) {
            if(!drawableContainer$DrawableContainerState0.d) {
                drawableContainer$DrawableContainerState0.b();
            }
            return drawableContainer$DrawableContainerState0.l;
        }
        return this.e == null ? 0 : this.e.getMinimumHeight();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getMinimumWidth() {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(drawableContainer$DrawableContainerState0.o) {
            if(!drawableContainer$DrawableContainerState0.d) {
                drawableContainer$DrawableContainerState0.b();
            }
            return drawableContainer$DrawableContainerState0.m;
        }
        return this.e == null ? 0 : this.e.getMinimumWidth();
    }

    @Override  // android.graphics.drawable.Drawable
    public int getOpacity() {
        int v = -2;
        if(this.e != null && this.e.isVisible()) {
            DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
            if(drawableContainer$DrawableContainerState0.f) {
                return drawableContainer$DrawableContainerState0.B;
            }
            drawableContainer$DrawableContainerState0.c();
            int v1 = drawableContainer$DrawableContainerState0.A;
            Drawable[] arr_drawable = drawableContainer$DrawableContainerState0.t;
            if(v1 > 0) {
                v = arr_drawable[0].getOpacity();
            }
            for(int v2 = 1; v2 < v1; ++v2) {
                v = Drawable.resolveOpacity(v, arr_drawable[v2].getOpacity());
            }
            drawableContainer$DrawableContainerState0.B = v;
            drawableContainer$DrawableContainerState0.f = true;
        }
        return v;
    }

    @Override  // android.graphics.drawable.Drawable
    public void getOutline(Outline outline0) {
        Drawable drawable0 = this.e;
        if(drawable0 != null) {
            drawable0.getOutline(outline0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public boolean getPadding(Rect rect0) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        boolean z = false;
        Rect rect1 = null;
        if(!drawableContainer$DrawableContainerState0.G) {
            Rect rect2 = drawableContainer$DrawableContainerState0.n;
            if(rect2 != null || drawableContainer$DrawableContainerState0.g) {
                rect1 = rect2;
            }
            else {
                drawableContainer$DrawableContainerState0.c();
                Rect rect3 = new Rect();
                int v = drawableContainer$DrawableContainerState0.A;
                Drawable[] arr_drawable = drawableContainer$DrawableContainerState0.t;
                for(int v1 = 0; v1 < v; ++v1) {
                    if(arr_drawable[v1].getPadding(rect3)) {
                        if(rect1 == null) {
                            rect1 = new Rect(0, 0, 0, 0);
                        }
                        int v2 = rect3.left;
                        if(v2 > rect1.left) {
                            rect1.left = v2;
                        }
                        int v3 = rect3.top;
                        if(v3 > rect1.top) {
                            rect1.top = v3;
                        }
                        int v4 = rect3.right;
                        if(v4 > rect1.right) {
                            rect1.right = v4;
                        }
                        int v5 = rect3.bottom;
                        if(v5 > rect1.bottom) {
                            rect1.bottom = v5;
                        }
                    }
                }
                drawableContainer$DrawableContainerState0.g = true;
                drawableContainer$DrawableContainerState0.n = rect1;
            }
        }
        if(rect1 == null) {
            Drawable drawable0 = this.e;
            z = drawable0 == null ? super.getPadding(rect0) : drawable0.getPadding(rect0);
        }
        else {
            rect0.set(rect1);
            if((rect1.left | rect1.top | rect1.bottom | rect1.right) != 0) {
                z = true;
            }
        }
        if(this.isAutoMirrored() && this.getLayoutDirection() == 1) {
            int v6 = rect0.left;
            rect0.left = rect0.right;
            rect0.right = v6;
        }
        return z;
    }

    @Override  // android.graphics.drawable.Drawable$Callback
    public void invalidateDrawable(Drawable drawable0) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(drawableContainer$DrawableContainerState0 != null) {
            drawableContainer$DrawableContainerState0.f = false;
            drawableContainer$DrawableContainerState0.h = false;
        }
        if(drawable0 == this.e && this.getCallback() != null) {
            this.getCallback().invalidateDrawable(this);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public abstract boolean isAutoMirrored();

    @Override  // android.graphics.drawable.Drawable
    public void jumpToCurrentState() {
        int v1;
        Drawable drawable0 = this.k;
        int v = 1;
        if(drawable0 == null) {
            v1 = 0;
        }
        else {
            drawable0.jumpToCurrentState();
            this.k = null;
            v1 = 1;
        }
        Drawable drawable1 = this.e;
        if(drawable1 != null) {
            drawable1.jumpToCurrentState();
            if(this.i) {
                this.e.setAlpha(this.a);
            }
        }
        if(this.h == 0L) {
            v = v1;
        }
        else {
            this.h = 0L;
        }
        if(this.g != 0L) {
            this.g = 0L;
            this.invalidateSelf();
            return;
        }
        if(v != 0) {
            this.invalidateSelf();
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public Drawable mutate() {
        if(!this.l && super.mutate() == this) {
            AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = new AnimatedStateListState(((AnimatedStateListDrawableCompat)this).q, ((AnimatedStateListDrawableCompat)this), null);
            animatedStateListDrawableCompat$AnimatedStateListState0.J = animatedStateListDrawableCompat$AnimatedStateListState0.J.clone();
            animatedStateListDrawableCompat$AnimatedStateListState0.I = animatedStateListDrawableCompat$AnimatedStateListState0.I.clone();
            this.d(animatedStateListDrawableCompat$AnimatedStateListState0);
            this.l = true;
        }
        return this;
    }

    @Override  // android.graphics.drawable.Drawable
    public final void onBoundsChange(Rect rect0) {
        Drawable drawable0 = this.k;
        if(drawable0 != null) {
            drawable0.setBounds(rect0);
        }
        Drawable drawable1 = this.e;
        if(drawable1 != null) {
            drawable1.setBounds(rect0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public boolean onLayoutDirectionChanged(int v) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        int v1 = this.d;
        int v2 = drawableContainer$DrawableContainerState0.A;
        Drawable[] arr_drawable = drawableContainer$DrawableContainerState0.t;
        boolean z = false;
        for(int v3 = 0; v3 < v2; ++v3) {
            Drawable drawable0 = arr_drawable[v3];
            if(drawable0 != null) {
                boolean z1 = Build.VERSION.SDK_INT < 23 ? false : drawable0.setLayoutDirection(v);
                if(v3 == v1) {
                    z = z1;
                }
            }
        }
        drawableContainer$DrawableContainerState0.z = v;
        return z;
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean onLevelChange(int v) {
        Drawable drawable0 = this.k;
        if(drawable0 != null) {
            return drawable0.setLevel(v);
        }
        return this.e == null ? false : this.e.setLevel(v);
    }

    @Override  // android.graphics.drawable.Drawable$Callback
    public void scheduleDrawable(Drawable drawable0, Runnable runnable0, long v) {
        if(drawable0 == this.e && this.getCallback() != null) {
            this.getCallback().scheduleDrawable(this, runnable0, v);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void setAlpha(int v) {
        if(!this.i || this.a != v) {
            this.i = true;
            this.a = v;
            Drawable drawable0 = this.e;
            if(drawable0 != null) {
                if(this.g == 0L) {
                    drawable0.setAlpha(v);
                    return;
                }
                this.a(false);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void setAutoMirrored(boolean z) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(drawableContainer$DrawableContainerState0.a != z) {
            drawableContainer$DrawableContainerState0.a = z;
            Drawable drawable0 = this.e;
            if(drawable0 != null) {
                DrawableCompat.setAutoMirrored(drawable0, z);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter0) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        drawableContainer$DrawableContainerState0.w = true;
        if(drawableContainer$DrawableContainerState0.j != colorFilter0) {
            drawableContainer$DrawableContainerState0.j = colorFilter0;
            Drawable drawable0 = this.e;
            if(drawable0 != null) {
                drawable0.setColorFilter(colorFilter0);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void setDither(boolean z) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        if(drawableContainer$DrawableContainerState0.r != z) {
            drawableContainer$DrawableContainerState0.r = z;
            Drawable drawable0 = this.e;
            if(drawable0 != null) {
                drawable0.setDither(z);
            }
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void setHotspot(float f, float f1) {
        Drawable drawable0 = this.e;
        if(drawable0 != null) {
            DrawableCompat.setHotspot(drawable0, f, f1);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void setHotspotBounds(int v, int v1, int v2, int v3) {
        Rect rect0 = this.j;
        if(rect0 == null) {
            this.j = new Rect(v, v1, v2, v3);
        }
        else {
            rect0.set(v, v1, v2, v3);
        }
        Drawable drawable0 = this.e;
        if(drawable0 != null) {
            DrawableCompat.setHotspotBounds(drawable0, v, v1, v2, v3);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void setTintList(ColorStateList colorStateList0) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        drawableContainer$DrawableContainerState0.x = true;
        if(drawableContainer$DrawableContainerState0.E != colorStateList0) {
            drawableContainer$DrawableContainerState0.E = colorStateList0;
            DrawableCompat.setTintList(this.e, colorStateList0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public void setTintMode(PorterDuff.Mode porterDuff$Mode0) {
        DrawableContainerState drawableContainer$DrawableContainerState0 = this.f;
        drawableContainer$DrawableContainerState0.y = true;
        if(drawableContainer$DrawableContainerState0.F != porterDuff$Mode0) {
            drawableContainer$DrawableContainerState0.F = porterDuff$Mode0;
            DrawableCompat.setTintMode(this.e, porterDuff$Mode0);
        }
    }

    @Override  // android.graphics.drawable.Drawable
    public boolean setVisible(boolean z, boolean z1) {
        boolean z2 = super.setVisible(z, z1);
        Drawable drawable0 = this.k;
        if(drawable0 != null) {
            drawable0.setVisible(z, z1);
        }
        Drawable drawable1 = this.e;
        if(drawable1 != null) {
            drawable1.setVisible(z, z1);
        }
        return z2;
    }

    @Override  // android.graphics.drawable.Drawable$Callback
    public void unscheduleDrawable(Drawable drawable0, Runnable runnable0) {
        if(drawable0 == this.e && this.getCallback() != null) {
            this.getCallback().unscheduleDrawable(this, runnable0);
        }
    }
}


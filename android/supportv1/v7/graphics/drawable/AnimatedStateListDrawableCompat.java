package android.supportv1.v7.graphics.drawable;

import android.animation.ObjectAnimator;
import android.animation.TimeInterpolator;
import android.content.Context;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.supportv1.c.a.c;
import android.supportv1.c.a.i;
import android.supportv1.v4.content.res.TypedArrayUtils;
import android.supportv1.v4.util.LongSparseArray;
import android.supportv1.v4.util.SparseArrayCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.content.res.AppCompatResources;
import android.util.AttributeSet;
import android.util.StateSet;
import org.xmlpull.v1.XmlPullParserException;

public class AnimatedStateListDrawableCompat extends StateListDrawable {
    static class AnimatableTransition extends Transition {
        public final Animatable a;

        public AnimatableTransition(Animatable animatable0) {
            this.a = animatable0;
        }

        @Override  // android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void c() {
            this.a.start();
        }

        @Override  // android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void d() {
            this.a.stop();
        }
    }

    static class AnimatedStateListState extends StateListState {
        public SparseArrayCompat I;
        public LongSparseArray J;
        public static final int K;

        public AnimatedStateListState(AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0, AnimatedStateListDrawableCompat animatedStateListDrawableCompat0, Resources resources0) {
            super(animatedStateListDrawableCompat$AnimatedStateListState0, animatedStateListDrawableCompat0, resources0);
            SparseArrayCompat sparseArrayCompat0;
            this.H = animatedStateListDrawableCompat$AnimatedStateListState0 == null ? new int[this.t.length][] : animatedStateListDrawableCompat$AnimatedStateListState0.H;
            if(animatedStateListDrawableCompat$AnimatedStateListState0 == null) {
                this.J = new LongSparseArray();
                sparseArrayCompat0 = new SparseArrayCompat();
            }
            else {
                this.J = animatedStateListDrawableCompat$AnimatedStateListState0.J;
                sparseArrayCompat0 = animatedStateListDrawableCompat$AnimatedStateListState0.I;
            }
            this.I = sparseArrayCompat0;
        }

        public final int i(int[] arr_v, Drawable drawable0, int v) {
            int v1 = this.a(drawable0);
            this.H[v1] = arr_v;
            this.I.put(v1, v);
            return v1;
        }

        public final int j(int v, int v1, Drawable drawable0, boolean z) {
            int v2 = this.a(drawable0);
            this.J.append(((long)v) << 0x20 | ((long)v1), ((long)(((long)v2) | (z ? 0x200000000L : 0L))));
            if(z) {
                this.J.append(((long)v) | ((long)v1) << 0x20, ((long)(0x100000000L | ((long)v2) | 0x200000000L)));
            }
            return v2;
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable() {
            return new AnimatedStateListDrawableCompat(this, null);
        }

        @Override  // android.graphics.drawable.Drawable$ConstantState
        public final Drawable newDrawable(Resources resources0) {
            return new AnimatedStateListDrawableCompat(this, resources0);
        }
    }

    static class AnimatedVectorDrawableTransition extends Transition {
        public final c a;

        public AnimatedVectorDrawableTransition(c c0) {
            this.a = c0;
        }

        @Override  // android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void c() {
            this.a.start();
        }

        @Override  // android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void d() {
            this.a.stop();
        }
    }

    static class AnimationDrawableTransition extends Transition {
        public final ObjectAnimator a;
        public final boolean b;

        public AnimationDrawableTransition(AnimationDrawable animationDrawable0, boolean z, boolean z1) {
            int v = animationDrawable0.getNumberOfFrames();
            FrameInterpolator animatedStateListDrawableCompat$FrameInterpolator0 = new FrameInterpolator();  // 初始化器: Ljava/lang/Object;-><init>()V
            int v2 = animationDrawable0.getNumberOfFrames();
            animatedStateListDrawableCompat$FrameInterpolator0.b = v2;
            if(animatedStateListDrawableCompat$FrameInterpolator0.a == null || animatedStateListDrawableCompat$FrameInterpolator0.a.length < v2) {
                animatedStateListDrawableCompat$FrameInterpolator0.a = new int[v2];
            }
            int[] arr_v = animatedStateListDrawableCompat$FrameInterpolator0.a;
            int v3 = 0;
            for(int v1 = 0; v1 < v2; ++v1) {
                int v4 = animationDrawable0.getDuration((z ? v2 - v1 - 1 : v1));
                arr_v[v1] = v4;
                v3 += v4;
            }
            animatedStateListDrawableCompat$FrameInterpolator0.c = v3;
            ObjectAnimator objectAnimator0 = ObjectAnimator.ofInt(animationDrawable0, "currentIndex", new int[]{(z ? v - 1 : 0), (z ? 0 : v - 1)});
            objectAnimator0.setAutoCancel(true);
            objectAnimator0.setDuration(((long)animatedStateListDrawableCompat$FrameInterpolator0.c));
            objectAnimator0.setInterpolator(animatedStateListDrawableCompat$FrameInterpolator0);
            this.b = z1;
            this.a = objectAnimator0;
        }

        @Override  // android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final boolean a() {
            return this.b;
        }

        @Override  // android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void b() {
            this.a.reverse();
        }

        @Override  // android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void c() {
            this.a.start();
        }

        @Override  // android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat$Transition
        public final void d() {
            this.a.cancel();
        }
    }

    static class FrameInterpolator implements TimeInterpolator {
        public int[] a;
        public int b;
        public int c;

        @Override  // android.animation.TimeInterpolator
        public final float getInterpolation(float f) {
            int v = (int)(f * ((float)this.c) + 0.5f);
            int v1 = this.b;
            int[] arr_v = this.a;
            int v2;
            for(v2 = 0; v2 < v1; ++v2) {
                int v3 = arr_v[v2];
                if(v < v3) {
                    break;
                }
                v -= v3;
            }
            return v2 >= v1 ? ((float)v2) / ((float)v1) + 0.0f : ((float)v2) / ((float)v1) + ((float)v) / ((float)this.c);
        }
    }

    static abstract class Transition {
        public boolean a() {
            return false;
        }

        public void b() {
        }

        public abstract void c();

        public abstract void d();
    }

    public boolean p;
    public AnimatedStateListState q;
    public Transition r;
    public int s;
    public int t;

    public AnimatedStateListDrawableCompat(AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0, Resources resources0) {
        this.a = 0xFF;
        this.d = -1;
        this.t = -1;
        this.s = -1;
        this.d(new AnimatedStateListState(animatedStateListDrawableCompat$AnimatedStateListState0, this, resources0));
        this.onStateChange(this.getState());
        this.jumpToCurrentState();
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean canApplyTheme() {
        return this.f.canApplyTheme();
    }

    @Override  // android.supportv1.v7.graphics.drawable.DrawableContainer
    public final void d(AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0) {
        this.f = animatedStateListDrawableCompat$AnimatedStateListState0;
        int v = this.d;
        if(v >= 0) {
            Drawable drawable0 = animatedStateListDrawableCompat$AnimatedStateListState0.d(v);
            this.e = drawable0;
            if(drawable0 != null) {
                this.b(drawable0);
            }
        }
        this.k = null;
        this.o = animatedStateListDrawableCompat$AnimatedStateListState0;
        this.q = animatedStateListDrawableCompat$AnimatedStateListState0;
    }

    public static AnimatedStateListDrawableCompat g(Context context0, Resources.Theme resources$Theme0, Resources resources0, AttributeSet attributeSet0, XmlResourceParser xmlResourceParser0) {
        String s = xmlResourceParser0.getName();
        if(!s.equals("animated-selector")) {
            throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": invalid animated-selector tag " + s);
        }
        AnimatedStateListDrawableCompat animatedStateListDrawableCompat0 = new AnimatedStateListDrawableCompat(null, null);
        animatedStateListDrawableCompat0.h(context0, resources$Theme0, resources0, attributeSet0, xmlResourceParser0);
        return animatedStateListDrawableCompat0;
    }

    @Override  // android.graphics.drawable.Drawable
    public final int getAlpha() {
        return this.a;
    }

    @Override  // android.graphics.drawable.Drawable
    public final Drawable getCurrent() {
        return this.e;
    }

    public final void h(Context context0, Resources.Theme resources$Theme0, Resources resources0, AttributeSet attributeSet0, XmlResourceParser xmlResourceParser0) {
        TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, R.styleable.AnimatedStateListDrawableCompat);
        this.setVisible(typedArray0.getBoolean(R.styleable.AnimatedStateListDrawableCompat_android_visible, true), true);
        this.k(typedArray0);
        this.e(resources0);
        typedArray0.recycle();
        int v = xmlResourceParser0.getDepth();
        int v1;
        while((v1 = xmlResourceParser0.next()) != 1) {
            int v2 = xmlResourceParser0.getDepth();
            if(v2 < v + 1 && v1 == 3) {
                break;
            }
            if(v1 != 2 || v2 > v + 1) {
            }
            else if(xmlResourceParser0.getName().equals("item")) {
                this.i(context0, resources$Theme0, resources0, attributeSet0, xmlResourceParser0);
            }
            else if(xmlResourceParser0.getName().equals("transition")) {
                this.j(context0, resources$Theme0, resources0, attributeSet0, xmlResourceParser0);
            }
        }
        this.onStateChange(this.getState());
    }

    public final void i(Context context0, Resources.Theme resources$Theme0, Resources resources0, AttributeSet attributeSet0, XmlResourceParser xmlResourceParser0) {
        TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, R.styleable.AnimatedStateListDrawableItem);
        int v = typedArray0.getResourceId(R.styleable.AnimatedStateListDrawableItem_android_id, 0);
        int v1 = typedArray0.getResourceId(R.styleable.AnimatedStateListDrawableItem_android_drawable, -1);
        Drawable drawable0 = v1 <= 0 ? null : AppCompatResources.c(context0, v1);
        typedArray0.recycle();
        int[] arr_v = StateListDrawable.f(attributeSet0);
        if(drawable0 == null) {
        alab1:
            while(true) {
                switch(xmlResourceParser0.next()) {
                    case 2: {
                        if(xmlResourceParser0.getName().equals("vector")) {
                            drawable0 = i.b(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                            break alab1;
                        }
                        drawable0 = Drawable.createFromXmlInner(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                        break alab1;
                    }
                    case 4: {
                        break;
                    }
                    default: {
                        throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <item> tag requires a \'drawable\' attribute or child tag defining a drawable");
                    }
                }
            }
        }
        if(drawable0 == null) {
            throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <item> tag requires a \'drawable\' attribute or child tag defining a drawable");
        }
        this.q.i(arr_v, drawable0, v);
    }

    @Override  // android.supportv1.v7.graphics.drawable.DrawableContainer
    public final boolean isAutoMirrored() {
        return this.f.a;
    }

    @Override  // android.graphics.drawable.Drawable
    public final boolean isStateful() {
        return true;
    }

    public final void j(Context context0, Resources.Theme resources$Theme0, Resources resources0, AttributeSet attributeSet0, XmlResourceParser xmlResourceParser0) {
        TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, R.styleable.AnimatedStateListDrawableTransition);
        int v = typedArray0.getResourceId(R.styleable.AnimatedStateListDrawableTransition_android_fromId, -1);
        int v1 = typedArray0.getResourceId(R.styleable.AnimatedStateListDrawableTransition_android_toId, -1);
        int v2 = typedArray0.getResourceId(R.styleable.AnimatedStateListDrawableTransition_android_drawable, -1);
        Drawable drawable0 = v2 <= 0 ? null : AppCompatResources.c(context0, v2);
        boolean z = typedArray0.getBoolean(R.styleable.AnimatedStateListDrawableTransition_android_reversible, false);
        typedArray0.recycle();
        if(drawable0 == null) {
        alab1:
            while(true) {
                switch(xmlResourceParser0.next()) {
                    case 2: {
                        if(xmlResourceParser0.getName().equals("animated-vector")) {
                            drawable0 = c.a(context0, resources$Theme0, resources0, attributeSet0, xmlResourceParser0);
                            break alab1;
                        }
                        drawable0 = Drawable.createFromXmlInner(resources0, xmlResourceParser0, attributeSet0, resources$Theme0);
                        break alab1;
                    }
                    case 4: {
                        break;
                    }
                    default: {
                        throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <transition> tag requires a \'drawable\' attribute or child tag defining a drawable");
                    }
                }
            }
        }
        if(drawable0 == null) {
            throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <transition> tag requires a \'drawable\' attribute or child tag defining a drawable");
        }
        if(v == -1 || v1 == -1) {
            throw new XmlPullParserException(xmlResourceParser0.getPositionDescription() + ": <transition> tag requires \'fromId\' & \'toId\' attributes");
        }
        this.q.j(v, v1, drawable0, z);
    }

    @Override  // android.supportv1.v7.graphics.drawable.DrawableContainer
    public final void jumpToCurrentState() {
        super.jumpToCurrentState();
        Transition animatedStateListDrawableCompat$Transition0 = this.r;
        if(animatedStateListDrawableCompat$Transition0 != null) {
            animatedStateListDrawableCompat$Transition0.d();
            this.r = null;
            this.c(this.t);
            this.t = -1;
            this.s = -1;
        }
    }

    public final void k(TypedArray typedArray0) {
        AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = this.q;
        animatedStateListDrawableCompat$AnimatedStateListState0.c |= typedArray0.getChangingConfigurations();
        animatedStateListDrawableCompat$AnimatedStateListState0.h(typedArray0.getBoolean(R.styleable.AnimatedStateListDrawableCompat_android_variablePadding, animatedStateListDrawableCompat$AnimatedStateListState0.G));
        animatedStateListDrawableCompat$AnimatedStateListState0.e(typedArray0.getBoolean(R.styleable.AnimatedStateListDrawableCompat_android_constantSize, animatedStateListDrawableCompat$AnimatedStateListState0.o));
        animatedStateListDrawableCompat$AnimatedStateListState0.f(typedArray0.getInt(R.styleable.AnimatedStateListDrawableCompat_android_enterFadeDuration, animatedStateListDrawableCompat$AnimatedStateListState0.u));
        animatedStateListDrawableCompat$AnimatedStateListState0.g(typedArray0.getInt(R.styleable.AnimatedStateListDrawableCompat_android_exitFadeDuration, animatedStateListDrawableCompat$AnimatedStateListState0.v));
        super.setDither(typedArray0.getBoolean(R.styleable.AnimatedStateListDrawableCompat_android_dither, animatedStateListDrawableCompat$AnimatedStateListState0.r));
    }

    @Override  // android.supportv1.v7.graphics.drawable.StateListDrawable
    public final Drawable mutate() {
        if(!this.p) {
            super.mutate();
            AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = this.q;
            animatedStateListDrawableCompat$AnimatedStateListState0.J = animatedStateListDrawableCompat$AnimatedStateListState0.J.clone();
            animatedStateListDrawableCompat$AnimatedStateListState0.I = animatedStateListDrawableCompat$AnimatedStateListState0.I.clone();
            this.p = true;
        }
        return this;
    }

    @Override  // android.supportv1.v7.graphics.drawable.StateListDrawable
    public final boolean onStateChange(int[] arr_v) {
        AnimationDrawableTransition animatedStateListDrawableCompat$AnimationDrawableTransition0;
        int v4;
        AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = this.q;
        int[][] arr2_v = animatedStateListDrawableCompat$AnimatedStateListState0.H;
        int v = animatedStateListDrawableCompat$AnimatedStateListState0.A;
        boolean z = false;
        int v1;
        for(v1 = 0; true; ++v1) {
            if(v1 >= v) {
                v1 = -1;
                break;
            }
            if(StateSet.stateSetMatches(arr2_v[v1], arr_v)) {
                break;
            }
        }
        if(v1 < 0) {
            int[] arr_v1 = StateSet.WILD_CARD;
            int[][] arr2_v1 = animatedStateListDrawableCompat$AnimatedStateListState0.H;
            int v2 = animatedStateListDrawableCompat$AnimatedStateListState0.A;
            for(v1 = 0; true; ++v1) {
                if(v1 >= v2) {
                    v1 = -1;
                    break;
                }
                if(StateSet.stateSetMatches(arr2_v1[v1], arr_v1)) {
                    break;
                }
            }
        }
        int v3 = this.d;
        if(v1 != v3) {
            Transition animatedStateListDrawableCompat$Transition0 = this.r;
            if(animatedStateListDrawableCompat$Transition0 == null) {
            label_38:
                this.r = null;
                this.s = -1;
                this.t = -1;
                AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState1 = this.q;
                if(v3 < 0) {
                    animatedStateListDrawableCompat$AnimatedStateListState1.getClass();
                    v4 = 0;
                }
                else {
                    v4 = (int)(((Integer)animatedStateListDrawableCompat$AnimatedStateListState1.I.get(v3, 0)));
                }
                int v5 = v1 >= 0 ? ((int)(((Integer)animatedStateListDrawableCompat$AnimatedStateListState1.I.get(v1, 0)))) : 0;
                if(v5 == 0 || v4 == 0) {
                label_71:
                    if(this.c(v1)) {
                        z = true;
                    }
                }
                else {
                    long v6 = ((long)v5) | ((long)v4) << 0x20;
                    int v7 = (int)(((long)(((Long)animatedStateListDrawableCompat$AnimatedStateListState1.J.get(v6, -1L)))));
                    if(v7 >= 0) {
                        boolean z1 = (((long)(((Long)animatedStateListDrawableCompat$AnimatedStateListState1.J.get(v6, -1L)))) & 0x200000000L) != 0L;
                        this.c(v7);
                        Drawable drawable0 = this.e;
                        if(drawable0 instanceof AnimationDrawable) {
                            if((((long)(((Long)animatedStateListDrawableCompat$AnimatedStateListState1.J.get(v6, -1L)))) & 0x100000000L) != 0L) {
                                z = true;
                            }
                            animatedStateListDrawableCompat$AnimationDrawableTransition0 = new AnimationDrawableTransition(((AnimationDrawable)drawable0), z, z1);
                            goto label_65;
                        }
                        else if(drawable0 instanceof c) {
                            animatedStateListDrawableCompat$AnimationDrawableTransition0 = new AnimatedVectorDrawableTransition(((c)drawable0));
                            goto label_65;
                        }
                        else {
                            if(drawable0 instanceof Animatable) {
                                animatedStateListDrawableCompat$AnimationDrawableTransition0 = new AnimatableTransition(((Animatable)drawable0));
                            label_65:
                                animatedStateListDrawableCompat$AnimationDrawableTransition0.c();
                                this.r = animatedStateListDrawableCompat$AnimationDrawableTransition0;
                                this.s = v3;
                                this.t = v1;
                                z = true;
                                goto label_73;
                            }
                            goto label_71;
                        }
                    }
                    else {
                        goto label_71;
                    }
                }
            }
            else if(v1 == this.t) {
                z = true;
            }
            else if(v1 == this.s && animatedStateListDrawableCompat$Transition0.a()) {
                animatedStateListDrawableCompat$Transition0.b();
                this.t = this.s;
                this.s = v1;
                z = true;
            }
            else {
                v3 = this.t;
                animatedStateListDrawableCompat$Transition0.d();
                goto label_38;
            }
        }
    label_73:
        Drawable drawable1 = this.e;
        return drawable1 != null ? z | drawable1.setState(arr_v) : z;
    }

    @Override  // android.supportv1.v7.graphics.drawable.DrawableContainer
    public final boolean setVisible(boolean z, boolean z1) {
        boolean z2 = super.setVisible(z, z1);
        Transition animatedStateListDrawableCompat$Transition0 = this.r;
        if(animatedStateListDrawableCompat$Transition0 != null && (z2 || z1)) {
            if(z) {
                animatedStateListDrawableCompat$Transition0.c();
                return z2;
            }
            this.jumpToCurrentState();
        }
        return z2;
    }
}


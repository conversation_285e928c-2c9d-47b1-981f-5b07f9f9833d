package android.supportv1.v7.graphics.drawable;

import android.content.res.Resources.Theme;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.StateSet;

abstract class StateListDrawable extends DrawableContainer {
    static abstract class StateListState extends DrawableContainerState {
        public int[][] H;

    }

    public boolean n;
    public StateListState o;

    @Override  // android.supportv1.v7.graphics.drawable.DrawableContainer
    public void applyTheme(Resources.Theme resources$Theme0) {
        super.applyTheme(resources$Theme0);
        this.onStateChange(this.getState());
    }

    public static int[] f(AttributeSet attributeSet0) {
        int v = attributeSet0.getAttributeCount();
        int[] arr_v = new int[v];
        int v2 = 0;
        for(int v1 = 0; v1 < v; ++v1) {
            int v3 = attributeSet0.getAttributeNameResource(v1);
            if(v3 != 0 && v3 != 0x10100D0 && v3 != 0x1010199) {
                if(!attributeSet0.getAttributeBooleanValue(v1, false)) {
                    v3 = -v3;
                }
                arr_v[v2] = v3;
                ++v2;
            }
        }
        return StateSet.trimStateSet(arr_v, v2);
    }

    @Override  // android.supportv1.v7.graphics.drawable.DrawableContainer
    public Drawable mutate() {
        if(!this.n) {
            super.mutate();
            AnimatedStateListState animatedStateListDrawableCompat$AnimatedStateListState0 = (AnimatedStateListState)this.o;
            animatedStateListDrawableCompat$AnimatedStateListState0.J = animatedStateListDrawableCompat$AnimatedStateListState0.J.clone();
            animatedStateListDrawableCompat$AnimatedStateListState0.I = animatedStateListDrawableCompat$AnimatedStateListState0.I.clone();
            this.n = true;
        }
        return this;
    }

    @Override  // android.graphics.drawable.Drawable
    public abstract boolean onStateChange(int[] arg1);
}


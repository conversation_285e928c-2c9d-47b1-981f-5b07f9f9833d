package android.supportv1.v7.view;

import android.supportv1.v7.view.menu.MenuBuilder;
import android.view.MenuItem;
import android.view.View;

public abstract class ActionMode {
    public interface Callback {
        void a(ActionMode arg1);

        boolean b(StandaloneActionMode arg1, MenuBuilder arg2);

        boolean c(ActionMode arg1, MenuBuilder arg2);

        boolean d(ActionMode arg1, MenuItem arg2);
    }

    public Object a;
    public boolean b;

    public abstract void b();

    public abstract View c();

    public abstract SupportMenuInflater d();

    public abstract CharSequence e();

    public abstract CharSequence f();

    public abstract void g();

    public abstract boolean h();

    public abstract void i(View arg1);

    public abstract void j(int arg1);

    public abstract void k(CharSequence arg1);

    public abstract void l(int arg1);

    public abstract void m(CharSequence arg1);

    public abstract void n(boolean arg1);
}


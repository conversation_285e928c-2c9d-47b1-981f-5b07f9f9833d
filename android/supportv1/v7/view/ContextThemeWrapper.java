package android.supportv1.v7.view;

import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.AssetManager;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.supportv1.v7.appcompat.R.style;
import android.view.LayoutInflater;

public class ContextThemeWrapper extends ContextWrapper {
    public LayoutInflater a;
    public Resources b;
    public Resources.Theme c;
    public int d;

    public ContextThemeWrapper(Context context0, int v) {
        super(context0);
        this.d = v;
    }

    public final int a() {
        return this.d;
    }

    @Override  // android.content.ContextWrapper
    public final void attachBaseContext(Context context0) {
        super.attachBaseContext(context0);
    }

    public final void b() {
        if(this.c == null) {
            this.c = this.getResources().newTheme();
            Resources.Theme resources$Theme0 = this.getBaseContext().getTheme();
            if(resources$Theme0 != null) {
                this.c.setTo(resources$Theme0);
            }
        }
        this.c.applyStyle(this.d, true);
    }

    @Override  // android.content.ContextWrapper
    public final AssetManager getAssets() {
        return this.getResources().getAssets();
    }

    @Override  // android.content.ContextWrapper
    public final Resources getResources() {
        if(this.b == null) {
            this.b = super.getResources();
        }
        return this.b;
    }

    @Override  // android.content.ContextWrapper
    public final Object getSystemService(String s) {
        if("layout_inflater".equals(s)) {
            if(this.a == null) {
                this.a = LayoutInflater.from(this.getBaseContext()).cloneInContext(this);
            }
            return this.a;
        }
        return this.getBaseContext().getSystemService(s);
    }

    @Override  // android.content.ContextWrapper
    public final Resources.Theme getTheme() {
        Resources.Theme resources$Theme0 = this.c;
        if(resources$Theme0 != null) {
            return resources$Theme0;
        }
        if(this.d == 0) {
            this.d = R.style.Theme_AppCompat_Light;
        }
        this.b();
        return this.c;
    }

    @Override  // android.content.ContextWrapper
    public final void setTheme(int v) {
        if(this.d != v) {
            this.d = v;
            this.b();
        }
    }
}


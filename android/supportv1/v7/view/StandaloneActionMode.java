package android.supportv1.v7.view;

import android.content.Context;
import android.supportv1.v7.view.menu.MenuBuilder.Callback;
import android.supportv1.v7.view.menu.MenuBuilder;
import android.supportv1.v7.widget.ActionBarContextView;
import android.view.MenuItem;
import android.view.View;
import java.lang.ref.WeakReference;

public class StandaloneActionMode extends ActionMode implements Callback {
    public final android.supportv1.v7.view.ActionMode.Callback c;
    public final Context d;
    public final ActionBarContextView e;
    public WeakReference f;
    public boolean g;
    public final MenuBuilder h;

    public StandaloneActionMode(Context context0, ActionBarContextView actionBarContextView0, android.supportv1.v7.view.ActionMode.Callback actionMode$Callback0) {
        this.d = context0;
        this.e = actionBarContextView0;
        this.c = actionMode$Callback0;
        MenuBuilder menuBuilder0 = new MenuBuilder(actionBarContextView0.getContext());
        menuBuilder0.d = 1;
        this.h = menuBuilder0;
        menuBuilder0.b = this;
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder$Callback
    public final boolean a(MenuBuilder menuBuilder0, MenuItem menuItem0) {
        return this.c.d(this, menuItem0);
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final void b() {
        if(this.g) {
            return;
        }
        this.g = true;
        this.e.sendAccessibilityEvent(0x20);
        this.c.a(this);
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final View c() {
        return this.f == null ? null : ((View)this.f.get());
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final SupportMenuInflater d() {
        return new SupportMenuInflater(this.e.getContext());
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final CharSequence e() {
        return this.e.getSubtitle();
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final CharSequence f() {
        return this.e.getTitle();
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final void g() {
        this.c.c(this, this.h);
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final boolean h() {
        return this.e.p;
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final void i(View view0) {
        this.e.setCustomView(view0);
        this.f = view0 == null ? null : new WeakReference(view0);
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final void j(int v) {
        this.k(this.d.getString(v));
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final void k(CharSequence charSequence0) {
        this.e.setSubtitle(charSequence0);
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final void l(int v) {
        this.m(this.d.getString(v));
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final void m(CharSequence charSequence0) {
        this.e.setTitle(charSequence0);
    }

    @Override  // android.supportv1.v7.view.ActionMode
    public final void n(boolean z) {
        this.b = z;
        this.e.setTitleOptional(z);
    }

    public final MenuBuilder o() {
        return this.h;
    }
}


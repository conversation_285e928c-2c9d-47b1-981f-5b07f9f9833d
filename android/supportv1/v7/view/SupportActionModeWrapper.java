package android.supportv1.v7.view;

import android.content.Context;
import android.supportv1.v4.internal.view.SupportMenuItem;
import android.supportv1.v4.util.SimpleArrayMap;
import android.supportv1.v7.view.menu.MenuBuilder;
import android.supportv1.v7.view.menu.MenuWrapperFactory;
import android.view.ActionMode.Callback;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import java.util.ArrayList;

public class SupportActionModeWrapper extends ActionMode {
    public static class CallbackWrapper implements Callback {
        public final ArrayList a;
        public final Context b;
        public final SimpleArrayMap c;
        public final ActionMode.Callback d;

        public CallbackWrapper(Context context0, ActionMode.Callback actionMode$Callback0) {
            this.b = context0;
            this.d = actionMode$Callback0;
            this.a = new ArrayList();
            this.c = new SimpleArrayMap();
        }

        @Override  // android.supportv1.v7.view.ActionMode$Callback
        public final void a(android.supportv1.v7.view.ActionMode actionMode0) {
            SupportActionModeWrapper supportActionModeWrapper0 = this.e(actionMode0);
            this.d.onDestroyActionMode(supportActionModeWrapper0);
        }

        @Override  // android.supportv1.v7.view.ActionMode$Callback
        public final boolean b(StandaloneActionMode standaloneActionMode0, MenuBuilder menuBuilder0) {
            SupportActionModeWrapper supportActionModeWrapper0 = this.e(standaloneActionMode0);
            Menu menu0 = this.f(menuBuilder0);
            return this.d.onCreateActionMode(supportActionModeWrapper0, menu0);
        }

        @Override  // android.supportv1.v7.view.ActionMode$Callback
        public final boolean c(android.supportv1.v7.view.ActionMode actionMode0, MenuBuilder menuBuilder0) {
            SupportActionModeWrapper supportActionModeWrapper0 = this.e(actionMode0);
            Menu menu0 = this.f(menuBuilder0);
            return this.d.onPrepareActionMode(supportActionModeWrapper0, menu0);
        }

        @Override  // android.supportv1.v7.view.ActionMode$Callback
        public final boolean d(android.supportv1.v7.view.ActionMode actionMode0, MenuItem menuItem0) {
            SupportActionModeWrapper supportActionModeWrapper0 = this.e(actionMode0);
            MenuItem menuItem1 = MenuWrapperFactory.b(this.b, ((SupportMenuItem)menuItem0));
            return this.d.onActionItemClicked(supportActionModeWrapper0, menuItem1);
        }

        public final SupportActionModeWrapper e(android.supportv1.v7.view.ActionMode actionMode0) {
            ArrayList arrayList0 = this.a;
            int v = arrayList0.size();
            for(int v1 = 0; v1 < v; ++v1) {
                SupportActionModeWrapper supportActionModeWrapper0 = (SupportActionModeWrapper)arrayList0.get(v1);
                if(supportActionModeWrapper0 != null && supportActionModeWrapper0.b == actionMode0) {
                    return supportActionModeWrapper0;
                }
            }
            SupportActionModeWrapper supportActionModeWrapper1 = new SupportActionModeWrapper(this.b, actionMode0);
            arrayList0.add(supportActionModeWrapper1);
            return supportActionModeWrapper1;
        }

        public final Menu f(MenuBuilder menuBuilder0) {
            SimpleArrayMap simpleArrayMap0 = this.c;
            Menu menu0 = (Menu)simpleArrayMap0.get(menuBuilder0);
            if(menu0 == null) {
                menu0 = MenuWrapperFactory.a(this.b, menuBuilder0);
                simpleArrayMap0.put(menuBuilder0, menu0);
            }
            return menu0;
        }
    }

    public final Context a;
    public final android.supportv1.v7.view.ActionMode b;

    public SupportActionModeWrapper(Context context0, android.supportv1.v7.view.ActionMode actionMode0) {
        this.a = context0;
        this.b = actionMode0;
    }

    @Override  // android.view.ActionMode
    public final void finish() {
        this.b.b();
    }

    @Override  // android.view.ActionMode
    public final View getCustomView() {
        return this.b.c();
    }

    @Override  // android.view.ActionMode
    public final Menu getMenu() {
        return MenuWrapperFactory.a(this.a, ((StandaloneActionMode)this.b).h);
    }

    @Override  // android.view.ActionMode
    public final MenuInflater getMenuInflater() {
        return this.b.d();
    }

    @Override  // android.view.ActionMode
    public final CharSequence getSubtitle() {
        return this.b.e();
    }

    @Override  // android.view.ActionMode
    public final Object getTag() {
        return this.b.a;
    }

    @Override  // android.view.ActionMode
    public final CharSequence getTitle() {
        return this.b.f();
    }

    @Override  // android.view.ActionMode
    public final boolean getTitleOptionalHint() {
        return this.b.b;
    }

    @Override  // android.view.ActionMode
    public final void invalidate() {
        this.b.g();
    }

    @Override  // android.view.ActionMode
    public final boolean isTitleOptional() {
        return this.b.h();
    }

    @Override  // android.view.ActionMode
    public final void setCustomView(View view0) {
        this.b.i(view0);
    }

    @Override  // android.view.ActionMode
    public final void setSubtitle(int v) {
        this.b.j(v);
    }

    @Override  // android.view.ActionMode
    public final void setSubtitle(CharSequence charSequence0) {
        this.b.k(charSequence0);
    }

    @Override  // android.view.ActionMode
    public final void setTag(Object object0) {
        this.b.a = object0;
    }

    @Override  // android.view.ActionMode
    public final void setTitle(int v) {
        this.b.l(v);
    }

    @Override  // android.view.ActionMode
    public final void setTitle(CharSequence charSequence0) {
        this.b.m(charSequence0);
    }

    @Override  // android.view.ActionMode
    public final void setTitleOptionalHint(boolean z) {
        this.b.n(z);
    }
}


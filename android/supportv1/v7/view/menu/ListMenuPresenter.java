package android.supportv1.v7.view.menu;

import android.content.Context;
import android.supportv1.v7.appcompat.R.layout;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListAdapter;
import h.a;
import java.util.ArrayList;

public class ListMenuPresenter implements MenuPresenter, AdapterView.OnItemClickListener {
    class MenuAdapter extends BaseAdapter {
        public int a;
        public final ListMenuPresenter b;

        public MenuAdapter() {
            this.a = -1;
            this.a();
        }

        public final void a() {
            ListMenuPresenter.this.f.getClass();
            this.a = -1;
        }

        public final MenuItemImpl b(int v) {
            MenuBuilder menuBuilder0 = ListMenuPresenter.this.f;
            menuBuilder0.j();
            ArrayList arrayList0 = menuBuilder0.m;
            ListMenuPresenter.this.getClass();
            if(this.a >= 0 && v >= this.a) {
                ++v;
            }
            return (MenuItemImpl)arrayList0.get(v);
        }

        @Override  // android.widget.Adapter
        public final int getCount() {
            MenuBuilder menuBuilder0 = ListMenuPresenter.this.f;
            menuBuilder0.j();
            int v = menuBuilder0.m.size();
            ListMenuPresenter.this.getClass();
            return this.a >= 0 ? v - 1 : v;
        }

        @Override  // android.widget.Adapter
        public final Object getItem(int v) {
            return this.b(v);
        }

        @Override  // android.widget.Adapter
        public final long getItemId(int v) {
            return (long)v;
        }

        @Override  // android.widget.Adapter
        public final View getView(int v, View view0, ViewGroup viewGroup0) {
            if(view0 == null) {
                view0 = ListMenuPresenter.this.d.inflate(ListMenuPresenter.this.e, viewGroup0, false);
            }
            a.f(view0);
            this.b(v);
            throw null;
        }

        @Override  // android.widget.BaseAdapter
        public final void notifyDataSetChanged() {
            this.a();
            super.notifyDataSetChanged();
        }
    }

    public MenuAdapter a;
    public Callback b;
    public Context c;
    public LayoutInflater d;
    public final int e;
    public MenuBuilder f;

    public ListMenuPresenter(Context context0, int v) {
        this.e = v;
        this.c = context0;
        this.d = LayoutInflater.from(context0);
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final void a(MenuBuilder menuBuilder0, boolean z) {
        Callback menuPresenter$Callback0 = this.b;
        if(menuPresenter$Callback0 != null) {
            menuPresenter$Callback0.a(menuBuilder0, z);
        }
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final boolean b() {
        return false;
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final void c(Context context0, MenuBuilder menuBuilder0) {
        if(this.c != null) {
            this.c = context0;
            if(this.d == null) {
                this.d = LayoutInflater.from(context0);
            }
        }
        this.f = menuBuilder0;
        MenuAdapter listMenuPresenter$MenuAdapter0 = this.a;
        if(listMenuPresenter$MenuAdapter0 != null) {
            listMenuPresenter$MenuAdapter0.notifyDataSetChanged();
        }
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final boolean d(SubMenuBuilder subMenuBuilder0) {
        if(!subMenuBuilder0.hasVisibleItems()) {
            return false;
        }
        MenuDialogHelper menuDialogHelper0 = new MenuDialogHelper();  // 初始化器: Ljava/lang/Object;-><init>()V
        menuDialogHelper0.b = subMenuBuilder0;
        menuDialogHelper0.c();
        Callback menuPresenter$Callback0 = this.b;
        if(menuPresenter$Callback0 != null) {
            menuPresenter$Callback0.b(subMenuBuilder0);
        }
        return true;
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final void e() {
        MenuAdapter listMenuPresenter$MenuAdapter0 = this.a;
        if(listMenuPresenter$MenuAdapter0 != null) {
            listMenuPresenter$MenuAdapter0.notifyDataSetChanged();
        }
    }

    public final ListAdapter f() {
        if(this.a == null) {
            this.a = new MenuAdapter(this);
        }
        return this.a;
    }

    public final void g(ViewGroup viewGroup0) {
        a.f(this.d.inflate(R.layout.abc_expanded_menu_layout, viewGroup0, false));
        if(this.a == null) {
            this.a = new MenuAdapter(this);
        }
        throw null;
    }

    public final void h(Callback menuPresenter$Callback0) {
        this.b = menuPresenter$Callback0;
    }

    @Override  // android.widget.AdapterView$OnItemClickListener
    public final void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
        this.f.t(this.a.b(v), this, 0);
    }
}


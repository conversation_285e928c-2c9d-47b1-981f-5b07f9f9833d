package android.supportv1.v7.view.menu;

import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.supportv1.v4.content.ContextCompat;
import android.supportv1.v4.internal.view.SupportMenu;
import android.supportv1.v4.view.ActionProvider;
import android.supportv1.v4.view.ViewConfigurationCompat;
import android.util.SparseArray;
import android.view.KeyCharacterMap.KeyData;
import android.view.KeyEvent;
import android.view.MenuItem.OnMenuItemClickListener;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewConfiguration;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class MenuBuilder implements SupportMenu {
    public interface Callback {
        boolean a(MenuBuilder arg1, MenuItem arg2);
    }

    public final ArrayList a;
    public Callback b;
    public final Context c;
    public int d;
    public Drawable e;
    public CharSequence f;
    public View g;
    public boolean h;
    public boolean i;
    public boolean j;
    public final ArrayList k;
    public boolean l;
    public final ArrayList m;
    public boolean n;
    public final CopyOnWriteArrayList o;
    public boolean p;
    public boolean q;
    public final Resources r;
    public boolean s;
    public final ArrayList t;
    public final ArrayList u;
    public static final int[] v;

    static {
        MenuBuilder.v = new int[]{1, 4, 5, 3, 2, 0};
    }

    public MenuBuilder(Context context0) {
        this.d = 0;
        this.p = false;
        this.l = false;
        this.s = false;
        this.i = false;
        this.t = new ArrayList();
        this.o = new CopyOnWriteArrayList();
        this.c = context0;
        Resources resources0 = context0.getResources();
        this.r = resources0;
        this.k = new ArrayList();
        this.u = new ArrayList();
        this.j = true;
        this.a = new ArrayList();
        this.m = new ArrayList();
        this.h = true;
        if(resources0.getConfiguration().keyboard != 1) {
            ViewConfigurationCompat.shouldShowMenuShortcutsWhenKeyboardPresent(ViewConfiguration.get(context0), context0);
        }
    }

    public final void A() {
        if(!this.p) {
            this.p = true;
            this.l = false;
            this.s = false;
        }
    }

    public final MenuItemImpl a(int v, int v1, int v2, CharSequence charSequence0) {
        int v6;
        int v3 = (0xFFFF0000 & v2) >> 16;
        if(v3 >= 0) {
            int[] arr_v = MenuBuilder.v;
            if(v3 < 6) {
                int v4 = arr_v[v3] << 16 | 0xFFFF & v2;
                MenuItemImpl menuItemImpl0 = new MenuItemImpl(this, v, v1, v2, v4, charSequence0, this.d);
                ArrayList arrayList0 = this.k;
                for(int v5 = arrayList0.size() - 1; true; --v5) {
                    v6 = 0;
                    if(v5 < 0) {
                        break;
                    }
                    if(((MenuItemImpl)arrayList0.get(v5)).s <= v4) {
                        v6 = v5 + 1;
                        break;
                    }
                }
                arrayList0.add(v6, menuItemImpl0);
                this.s(true);
                return menuItemImpl0;
            }
        }
        throw new IllegalArgumentException("order does not contain a valid category.");
    }

    @Override  // android.view.Menu
    public final MenuItem add(int v) {
        return this.a(0, 0, 0, this.r.getString(v));
    }

    @Override  // android.view.Menu
    public final MenuItem add(int v, int v1, int v2, int v3) {
        return this.a(v, v1, v2, this.r.getString(v3));
    }

    @Override  // android.view.Menu
    public final MenuItem add(int v, int v1, int v2, CharSequence charSequence0) {
        return this.a(v, v1, v2, charSequence0);
    }

    @Override  // android.view.Menu
    public final MenuItem add(CharSequence charSequence0) {
        return this.a(0, 0, 0, charSequence0);
    }

    @Override  // android.view.Menu
    public final int addIntentOptions(int v, int v1, int v2, ComponentName componentName0, Intent[] arr_intent, Intent intent0, int v3, MenuItem[] arr_menuItem) {
        PackageManager packageManager0 = this.c.getPackageManager();
        List list0 = packageManager0.queryIntentActivityOptions(componentName0, arr_intent, intent0, 0);
        int v5 = list0 == null ? 0 : list0.size();
        if((v3 & 1) == 0) {
            this.removeGroup(v);
        }
        for(int v4 = 0; v4 < v5; ++v4) {
            ResolveInfo resolveInfo0 = (ResolveInfo)list0.get(v4);
            Intent intent1 = new Intent((resolveInfo0.specificIndex >= 0 ? arr_intent[resolveInfo0.specificIndex] : intent0));
            intent1.setComponent(new ComponentName(resolveInfo0.activityInfo.applicationInfo.packageName, resolveInfo0.activityInfo.name));
            MenuItemImpl menuItemImpl0 = this.a(v, v1, v2, resolveInfo0.loadLabel(packageManager0));
            menuItemImpl0.setIcon(resolveInfo0.loadIcon(packageManager0));
            menuItemImpl0.o = intent1;
            if(arr_menuItem != null) {
                int v6 = resolveInfo0.specificIndex;
                if(v6 >= 0) {
                    arr_menuItem[v6] = menuItemImpl0;
                }
            }
        }
        return v5;
    }

    @Override  // android.view.Menu
    public final SubMenu addSubMenu(int v) {
        return this.addSubMenu(0, 0, 0, this.r.getString(v));
    }

    @Override  // android.view.Menu
    public final SubMenu addSubMenu(int v, int v1, int v2, int v3) {
        return this.addSubMenu(v, v1, v2, this.r.getString(v3));
    }

    @Override  // android.view.Menu
    public final SubMenu addSubMenu(int v, int v1, int v2, CharSequence charSequence0) {
        MenuItemImpl menuItemImpl0 = this.a(v, v1, v2, charSequence0);
        SubMenuBuilder subMenuBuilder0 = new SubMenuBuilder(this.c, this, menuItemImpl0);
        menuItemImpl0.y = subMenuBuilder0;
        subMenuBuilder0.setHeaderTitle(menuItemImpl0.z);
        return subMenuBuilder0;
    }

    @Override  // android.view.Menu
    public final SubMenu addSubMenu(CharSequence charSequence0) {
        return this.addSubMenu(0, 0, 0, charSequence0);
    }

    public final void b(ListMenuPresenter listMenuPresenter0) {
        this.c(listMenuPresenter0, this.c);
    }

    public final void c(MenuPresenter menuPresenter0, Context context0) {
        WeakReference weakReference0 = new WeakReference(menuPresenter0);
        this.o.add(weakReference0);
        menuPresenter0.c(context0, this);
        this.h = true;
    }

    @Override  // android.view.Menu
    public final void clear() {
        this.k.clear();
        this.s(true);
    }

    public final void clearHeader() {
        this.e = null;
        this.f = null;
        this.g = null;
        this.s(false);
    }

    @Override  // android.view.Menu
    public final void close() {
        this.d(true);
    }

    public final void d(boolean z) {
        if(this.i) {
            return;
        }
        this.i = true;
        CopyOnWriteArrayList copyOnWriteArrayList0 = this.o;
        for(Object object0: copyOnWriteArrayList0) {
            WeakReference weakReference0 = (WeakReference)object0;
            MenuPresenter menuPresenter0 = (MenuPresenter)weakReference0.get();
            if(menuPresenter0 == null) {
                copyOnWriteArrayList0.remove(weakReference0);
            }
            else {
                menuPresenter0.a(this, z);
            }
        }
        this.i = false;
    }

    public boolean e(MenuItemImpl menuItemImpl0) {
        CopyOnWriteArrayList copyOnWriteArrayList0 = this.o;
        if(!copyOnWriteArrayList0.isEmpty() && menuItemImpl0 == null) {
            this.A();
            for(Object object0: copyOnWriteArrayList0) {
                WeakReference weakReference0 = (WeakReference)object0;
                if(((MenuPresenter)weakReference0.get()) == null) {
                    copyOnWriteArrayList0.remove(weakReference0);
                }
            }
            this.z();
        }
        return false;
    }

    public boolean f(MenuBuilder menuBuilder0, MenuItem menuItem0) {
        return this.b != null && this.b.a(menuBuilder0, menuItem0);
    }

    @Override  // android.view.Menu
    public final MenuItem findItem(int v) {
        ArrayList arrayList0 = this.k;
        int v1 = arrayList0.size();
        for(int v2 = 0; v2 < v1; ++v2) {
            MenuItem menuItem0 = (MenuItemImpl)arrayList0.get(v2);
            if(menuItem0.n == v) {
                return menuItem0;
            }
            if(((MenuItemImpl)menuItem0).hasSubMenu()) {
                MenuItem menuItem1 = menuItem0.y.findItem(v);
                if(menuItem1 != null) {
                    return menuItem1;
                }
            }
        }
        return null;
    }

    public boolean g(MenuItemImpl menuItemImpl0) {
        CopyOnWriteArrayList copyOnWriteArrayList0 = this.o;
        if(copyOnWriteArrayList0.isEmpty()) {
            return false;
        }
        this.A();
        for(Object object0: copyOnWriteArrayList0) {
            WeakReference weakReference0 = (WeakReference)object0;
            if(((MenuPresenter)weakReference0.get()) == null) {
                copyOnWriteArrayList0.remove(weakReference0);
            }
        }
        this.z();
        return false;
    }

    @Override  // android.view.Menu
    public final MenuItem getItem(int v) {
        return (MenuItem)this.k.get(v);
    }

    public final MenuItemImpl h(int v, KeyEvent keyEvent0) {
        ArrayList arrayList0 = this.t;
        arrayList0.clear();
        this.i(arrayList0, v, keyEvent0);
        if(arrayList0.isEmpty()) {
            return null;
        }
        int v1 = keyEvent0.getMetaState();
        KeyCharacterMap.KeyData keyCharacterMap$KeyData0 = new KeyCharacterMap.KeyData();
        keyEvent0.getKeyData(keyCharacterMap$KeyData0);
        int v2 = arrayList0.size();
        if(v2 == 1) {
            return (MenuItemImpl)arrayList0.get(0);
        }
        boolean z = this.r();
        for(int v3 = 0; v3 < v2; ++v3) {
            MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList0.get(v3);
            int v4 = z ? menuItemImpl0.t : menuItemImpl0.v;
            if(v4 == keyCharacterMap$KeyData0.meta[0] && (v1 & 2) == 0 || v4 == keyCharacterMap$KeyData0.meta[2] && (v1 & 2) != 0 || z && v4 == 8 && v == 67) {
                return menuItemImpl0;
            }
        }
        return null;
    }

    @Override  // android.view.Menu
    public final boolean hasVisibleItems() {
        if(this.n) {
            return true;
        }
        ArrayList arrayList0 = this.k;
        int v = arrayList0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            if(((MenuItemImpl)arrayList0.get(v1)).isVisible()) {
                return true;
            }
        }
        return false;
    }

    public final void i(ArrayList arrayList0, int v, KeyEvent keyEvent0) {
        boolean z = this.r();
        int v1 = keyEvent0.getModifiers();
        KeyCharacterMap.KeyData keyCharacterMap$KeyData0 = new KeyCharacterMap.KeyData();
        if(!keyEvent0.getKeyData(keyCharacterMap$KeyData0) && v != 67) {
            return;
        }
        ArrayList arrayList1 = this.k;
        int v2 = arrayList1.size();
        for(int v3 = 0; v3 < v2; ++v3) {
            MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList1.get(v3);
            if(menuItemImpl0.hasSubMenu()) {
                menuItemImpl0.y.i(arrayList0, v, keyEvent0);
            }
            int v4 = z ? menuItemImpl0.t : menuItemImpl0.v;
            if((v1 & 0x1100F) == ((z ? menuItemImpl0.u : menuItemImpl0.w) & 0x1100F) && v4 != 0 && ((v4 == keyCharacterMap$KeyData0.meta[0] || v4 == keyCharacterMap$KeyData0.meta[2] || z && v4 == 8 && v == 67) && menuItemImpl0.isEnabled())) {
                arrayList0.add(menuItemImpl0);
            }
        }
    }

    @Override  // android.view.Menu
    public final boolean isShortcutKey(int v, KeyEvent keyEvent0) {
        return this.h(v, keyEvent0) != null;
    }

    public final void j() {
        ArrayList arrayList0 = this.q();
        if(!this.h) {
            return;
        }
        CopyOnWriteArrayList copyOnWriteArrayList0 = this.o;
        boolean z = false;
        for(Object object0: copyOnWriteArrayList0) {
            WeakReference weakReference0 = (WeakReference)object0;
            MenuPresenter menuPresenter0 = (MenuPresenter)weakReference0.get();
            if(menuPresenter0 == null) {
                copyOnWriteArrayList0.remove(weakReference0);
            }
            else {
                z |= menuPresenter0.b();
            }
        }
        ArrayList arrayList1 = this.a;
        ArrayList arrayList2 = this.m;
        arrayList1.clear();
        arrayList2.clear();
        if(z) {
            int v = arrayList0.size();
            for(int v1 = 0; v1 < v; ++v1) {
                MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList0.get(v1);
                (menuItemImpl0.d() ? arrayList1 : arrayList2).add(menuItemImpl0);
            }
        }
        else {
            arrayList2.addAll(this.q());
        }
        this.h = false;
    }

    public String k() [...] // 潜在的解密器

    public final Context l() {
        return this.c;
    }

    public final Drawable m() {
        return this.e;
    }

    public final CharSequence n() {
        return this.f;
    }

    public final View o() {
        return this.g;
    }

    public MenuBuilder p() {
        return this;
    }

    @Override  // android.view.Menu
    public final boolean performIdentifierAction(int v, int v1) {
        return this.t(this.findItem(v), null, v1);
    }

    @Override  // android.view.Menu
    public final boolean performShortcut(int v, KeyEvent keyEvent0, int v1) {
        MenuItemImpl menuItemImpl0 = this.h(v, keyEvent0);
        boolean z = menuItemImpl0 == null ? false : this.t(menuItemImpl0, null, v1);
        if((v1 & 2) != 0) {
            this.d(true);
        }
        return z;
    }

    public final ArrayList q() {
        ArrayList arrayList0 = this.u;
        if(!this.j) {
            return arrayList0;
        }
        arrayList0.clear();
        ArrayList arrayList1 = this.k;
        int v = arrayList1.size();
        for(int v1 = 0; v1 < v; ++v1) {
            MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList1.get(v1);
            if(menuItemImpl0.isVisible()) {
                arrayList0.add(menuItemImpl0);
            }
        }
        this.j = false;
        this.h = true;
        return arrayList0;
    }

    public boolean r() {
        return this.q;
    }

    @Override  // android.view.Menu
    public final void removeGroup(int v) {
        ArrayList arrayList0 = this.k;
        int v1 = arrayList0.size();
        int v3;
        for(v3 = 0; true; ++v3) {
            if(v3 >= v1) {
                v3 = -1;
                break;
            }
            if(((MenuItemImpl)arrayList0.get(v3)).g == v) {
                break;
            }
        }
        if(v3 >= 0) {
            int v4 = arrayList0.size();
            for(int v2 = 0; v2 < v4 - v3 && ((MenuItemImpl)arrayList0.get(v3)).g == v; ++v2) {
                ArrayList arrayList1 = this.k;
                if(v3 < arrayList1.size()) {
                    arrayList1.remove(v3);
                }
            }
            this.s(true);
        }
    }

    @Override  // android.view.Menu
    public final void removeItem(int v) {
        ArrayList arrayList0 = this.k;
        int v1 = arrayList0.size();
        int v2;
        for(v2 = 0; true; ++v2) {
            if(v2 >= v1) {
                v2 = -1;
                break;
            }
            if(((MenuItemImpl)arrayList0.get(v2)).n == v) {
                break;
            }
        }
        if(v2 >= 0) {
            ArrayList arrayList1 = this.k;
            if(v2 < arrayList1.size()) {
                arrayList1.remove(v2);
                this.s(true);
            }
        }
    }

    public final void s(boolean z) {
        if(this.p) {
            this.l = true;
            if(z) {
                this.s = true;
            }
        }
        else {
            if(z) {
                this.j = true;
                this.h = true;
            }
            CopyOnWriteArrayList copyOnWriteArrayList0 = this.o;
            if(!copyOnWriteArrayList0.isEmpty()) {
                this.A();
                for(Object object0: copyOnWriteArrayList0) {
                    WeakReference weakReference0 = (WeakReference)object0;
                    MenuPresenter menuPresenter0 = (MenuPresenter)weakReference0.get();
                    if(menuPresenter0 == null) {
                        copyOnWriteArrayList0.remove(weakReference0);
                    }
                    else {
                        menuPresenter0.e();
                    }
                }
                this.z();
            }
        }
    }

    @Override  // android.view.Menu
    public final void setGroupCheckable(int v, boolean z, boolean z1) {
        ArrayList arrayList0 = this.k;
        int v1 = arrayList0.size();
        for(int v2 = 0; v2 < v1; ++v2) {
            MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList0.get(v2);
            if(menuItemImpl0.g == v) {
                menuItemImpl0.f = menuItemImpl0.f & -5 | (z1 ? 4 : 0);
                menuItemImpl0.setCheckable(z);
            }
        }
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenu
    public void setGroupDividerEnabled(boolean z) {
    }

    @Override  // android.view.Menu
    public final void setGroupEnabled(int v, boolean z) {
        ArrayList arrayList0 = this.k;
        int v1 = arrayList0.size();
        for(int v2 = 0; v2 < v1; ++v2) {
            MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList0.get(v2);
            if(menuItemImpl0.g == v) {
                menuItemImpl0.setEnabled(z);
            }
        }
    }

    @Override  // android.view.Menu
    public final void setGroupVisible(int v, boolean z) {
        ArrayList arrayList0 = this.k;
        int v1 = arrayList0.size();
        boolean z1 = false;
        for(int v2 = 0; v2 < v1; ++v2) {
            MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList0.get(v2);
            if(menuItemImpl0.g == v) {
                int v3 = menuItemImpl0.f;
                int v4 = (z ? 0 : 8) | v3 & -9;
                menuItemImpl0.f = v4;
                if(v3 != v4) {
                    z1 = true;
                }
            }
        }
        if(z1) {
            this.s(true);
        }
    }

    @Override  // android.view.Menu
    public void setQwertyMode(boolean z) {
        this.q = z;
        this.s(false);
    }

    @Override  // android.view.Menu
    public final int size() {
        return this.k.size();
    }

    public final boolean t(MenuItem menuItem0, MenuPresenter menuPresenter0, int v) {
        boolean z1;
        boolean z = false;
        if(((MenuItemImpl)menuItem0) != null && ((MenuItemImpl)menuItem0).isEnabled()) {
            MenuItem.OnMenuItemClickListener menuItem$OnMenuItemClickListener0 = ((MenuItemImpl)menuItem0).d;
            if(menuItem$OnMenuItemClickListener0 == null || !menuItem$OnMenuItemClickListener0.onMenuItemClick(((MenuItemImpl)menuItem0))) {
                MenuBuilder menuBuilder0 = ((MenuItemImpl)menuItem0).p;
                if(menuBuilder0.f(menuBuilder0, ((MenuItemImpl)menuItem0))) {
                    z1 = true;
                }
                else {
                    Intent intent0 = ((MenuItemImpl)menuItem0).o;
                    if(intent0 == null) {
                    label_10:
                        ActionProvider actionProvider0 = ((MenuItemImpl)menuItem0).a;
                        if(actionProvider0 != null && actionProvider0.onPerformDefaultAction()) {
                        label_14:
                            z1 = true;
                        }
                        else {
                            z1 = false;
                        }
                    }
                    else {
                        try {
                            menuBuilder0.c.startActivity(intent0);
                            goto label_14;
                        }
                        catch(ActivityNotFoundException unused_ex) {
                        }
                        goto label_10;
                    }
                }
            }
            else {
                z1 = true;
            }
            ActionProvider actionProvider1 = ((MenuItemImpl)menuItem0).a;
            boolean z2 = actionProvider1 != null && actionProvider1.hasSubMenu();
            if(((MenuItemImpl)menuItem0).c()) {
                z1 |= ((MenuItemImpl)menuItem0).expandActionView();
                if(z1) {
                    this.d(true);
                    return true;
                }
            }
            else if(((MenuItemImpl)menuItem0).hasSubMenu() || z2) {
                if((v & 4) == 0) {
                    this.d(false);
                }
                if(!((MenuItemImpl)menuItem0).hasSubMenu()) {
                    SubMenuBuilder subMenuBuilder0 = new SubMenuBuilder(this.c, this, ((MenuItemImpl)menuItem0));
                    ((MenuItemImpl)menuItem0).y = subMenuBuilder0;
                    subMenuBuilder0.setHeaderTitle(((MenuItemImpl)menuItem0).z);
                }
                SubMenuBuilder subMenuBuilder1 = ((MenuItemImpl)menuItem0).y;
                if(z2) {
                    actionProvider1.onPrepareSubMenu(subMenuBuilder1);
                }
                CopyOnWriteArrayList copyOnWriteArrayList0 = this.o;
                if(!copyOnWriteArrayList0.isEmpty()) {
                    if(menuPresenter0 != null) {
                        z = menuPresenter0.d(subMenuBuilder1);
                    }
                    for(Object object0: copyOnWriteArrayList0) {
                        WeakReference weakReference0 = (WeakReference)object0;
                        MenuPresenter menuPresenter1 = (MenuPresenter)weakReference0.get();
                        if(menuPresenter1 == null) {
                            copyOnWriteArrayList0.remove(weakReference0);
                        }
                        else if(!z) {
                            z = menuPresenter1.d(subMenuBuilder1);
                        }
                    }
                }
                z1 |= z;
                if(!z1) {
                    this.d(true);
                }
            }
            else if((v & 1) == 0) {
                this.d(true);
                return z1;
            }
            return z1;
        }
        return false;
    }

    public final void u(MenuPresenter menuPresenter0) {
        CopyOnWriteArrayList copyOnWriteArrayList0 = this.o;
        for(Object object0: copyOnWriteArrayList0) {
            WeakReference weakReference0 = (WeakReference)object0;
            MenuPresenter menuPresenter1 = (MenuPresenter)weakReference0.get();
            if(menuPresenter1 == null || menuPresenter1 == menuPresenter0) {
                copyOnWriteArrayList0.remove(weakReference0);
            }
        }
    }

    public final void v(Bundle bundle0) {
        if(bundle0 == null) {
            return;
        }
        SparseArray sparseArray0 = bundle0.getSparseParcelableArray("android:menu:actionviewstates");
        int v = this.k.size();
        for(int v1 = 0; v1 < v; ++v1) {
            MenuItem menuItem0 = this.getItem(v1);
            View view0 = menuItem0.getActionView();
            if(view0 != null && view0.getId() != -1) {
                view0.restoreHierarchyState(sparseArray0);
            }
            if(menuItem0.hasSubMenu()) {
                ((SubMenuBuilder)menuItem0.getSubMenu()).v(bundle0);
            }
        }
        int v2 = bundle0.getInt("android:menu:expandedactionview");
        if(v2 > 0) {
            MenuItem menuItem1 = this.findItem(v2);
            if(menuItem1 != null) {
                menuItem1.expandActionView();
            }
        }
    }

    public final void w(Bundle bundle0) {
        int v = this.k.size();
        SparseArray sparseArray0 = null;
        for(int v1 = 0; v1 < v; ++v1) {
            MenuItem menuItem0 = this.getItem(v1);
            View view0 = menuItem0.getActionView();
            if(view0 != null && view0.getId() != -1) {
                if(sparseArray0 == null) {
                    sparseArray0 = new SparseArray();
                }
                view0.saveHierarchyState(sparseArray0);
                if(menuItem0.isActionViewExpanded()) {
                    bundle0.putInt("android:menu:expandedactionview", menuItem0.getItemId());
                }
            }
            if(menuItem0.hasSubMenu()) {
                ((SubMenuBuilder)menuItem0.getSubMenu()).w(bundle0);
            }
        }
        if(sparseArray0 != null) {
            bundle0.putSparseParcelableArray("android:menu:actionviewstates", sparseArray0);
        }
    }

    public void x(Callback menuBuilder$Callback0) {
        this.b = menuBuilder$Callback0;
    }

    public final void y(int v, CharSequence charSequence0, int v1, Drawable drawable0, View view0) {
        if(view0 == null) {
            if(v > 0) {
                this.f = this.r.getText(v);
            }
            else if(charSequence0 != null) {
                this.f = charSequence0;
            }
            if(v1 > 0) {
                this.e = ContextCompat.getDrawable(this.c, v1);
            }
            else if(drawable0 != null) {
                this.e = drawable0;
            }
            this.g = null;
        }
        else {
            this.g = view0;
            this.f = null;
            this.e = null;
        }
        this.s(false);
    }

    public final void z() {
        this.p = false;
        if(this.l) {
            this.l = false;
            this.s(this.s);
        }
    }
}


package android.supportv1.v7.view.menu;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;

public class SubMenuBuilder extends MenuBuilder implements SubMenu {
    public final MenuItemImpl w;
    public final MenuBuilder x;

    public SubMenuBuilder(Context context0, MenuBuilder menuBuilder0, MenuItemImpl menuItemImpl0) {
        super(context0);
        this.x = menuBuilder0;
        this.w = menuItemImpl0;
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder
    public final boolean e(MenuItemImpl menuItemImpl0) {
        return this.x.e(menuItemImpl0);
    }

    // 去混淆评级： 低(20)
    @Override  // android.supportv1.v7.view.menu.MenuBuilder
    public final boolean f(MenuBuilder menuBuilder0, MenuItem menuItem0) {
        return super.f(menuBuilder0, menuItem0) || this.x.f(menuBuilder0, menuItem0);
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder
    public final boolean g(MenuItemImpl menuItemImpl0) {
        return this.x.g(menuItemImpl0);
    }

    @Override  // android.view.SubMenu
    public final MenuItem getItem() {
        return this.w;
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder
    public final String k() {
        int v = this.w == null ? 0 : this.w.n;
        return v == 0 ? null : "android:menu:actionviewstates:" + v;
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder
    public final MenuBuilder p() {
        return this.x.p();
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder
    public final boolean r() {
        return this.x.r();
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder, android.view.Menu
    public final void setGroupDividerEnabled(boolean z) {
        this.x.setGroupDividerEnabled(z);
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderIcon(int v) {
        this.y(0, null, v, null, null);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderIcon(Drawable drawable0) {
        this.y(0, null, 0, drawable0, null);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderTitle(int v) {
        this.y(v, null, 0, null, null);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderTitle(CharSequence charSequence0) {
        this.y(0, charSequence0, 0, null, null);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderView(View view0) {
        this.y(0, null, 0, null, view0);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setIcon(int v) {
        this.w.setIcon(v);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setIcon(Drawable drawable0) {
        this.w.setIcon(drawable0);
        return this;
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder, android.view.Menu
    public final void setQwertyMode(boolean z) {
        this.x.setQwertyMode(z);
    }

    @Override  // android.supportv1.v7.view.menu.MenuBuilder
    public final void x(Callback menuBuilder$Callback0) {
        throw null;
    }
}


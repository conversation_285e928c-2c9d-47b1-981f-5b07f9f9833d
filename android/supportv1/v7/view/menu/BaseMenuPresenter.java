package android.supportv1.v7.view.menu;

import android.content.Context;
import android.view.LayoutInflater;

public abstract class BaseMenuPresenter implements MenuPresenter {
    public Context a;
    public final int b;
    public MenuBuilder c;
    public final int d;
    public final Context e;
    public final LayoutInflater f;

    public BaseMenuPresenter(Context context0, int v, int v1) {
        this.e = context0;
        this.f = LayoutInflater.from(context0);
        this.d = v;
        this.b = v1;
    }
}


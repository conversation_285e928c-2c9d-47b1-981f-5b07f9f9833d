package android.supportv1.v7.view.menu;

import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface.OnDismissListener;
import android.content.DialogInterface.OnKeyListener;
import android.content.DialogInterface;
import android.supportv1.v7.app.AlertDialog.Builder;
import android.supportv1.v7.app.AlertDialog;
import android.supportv1.v7.appcompat.R.layout;
import android.view.KeyEvent.DispatcherState;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager.LayoutParams;

class MenuDialogHelper implements DialogInterface.OnClickListener, DialogInterface.OnDismissListener, DialogInterface.OnKeyListener, Callback {
    public AlertDialog a;
    public MenuBuilder b;
    public ListMenuPresenter c;

    @Override  // android.supportv1.v7.view.menu.MenuPresenter$Callback
    public final void a(MenuBuilder menuBuilder0, boolean z) {
        if(z || menuBuilder0 == this.b) {
            AlertDialog alertDialog0 = this.a;
            if(alertDialog0 != null) {
                alertDialog0.dismiss();
            }
        }
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter$Callback
    public final boolean b(MenuBuilder menuBuilder0) {
        return false;
    }

    public final void c() {
        MenuBuilder menuBuilder0 = this.b;
        Builder alertDialog$Builder0 = new Builder(menuBuilder0.l());
        ListMenuPresenter listMenuPresenter0 = new ListMenuPresenter(alertDialog$Builder0.b(), R.layout.abc_list_menu_item_layout);
        this.c = listMenuPresenter0;
        listMenuPresenter0.h(this);
        this.b.b(this.c);
        alertDialog$Builder0.c(this.c.f(), this);
        View view0 = menuBuilder0.o();
        if(view0 == null) {
            alertDialog$Builder0.e(menuBuilder0.m());
            alertDialog$Builder0.g(menuBuilder0.n());
        }
        else {
            alertDialog$Builder0.d(view0);
        }
        alertDialog$Builder0.f(this);
        AlertDialog alertDialog0 = alertDialog$Builder0.a();
        this.a = alertDialog0;
        alertDialog0.setOnDismissListener(this);
        WindowManager.LayoutParams windowManager$LayoutParams0 = this.a.getWindow().getAttributes();
        windowManager$LayoutParams0.type = 1003;
        windowManager$LayoutParams0.flags |= 0x20000;
        this.a.show();
    }

    @Override  // android.content.DialogInterface$OnClickListener
    public final void onClick(DialogInterface dialogInterface0, int v) {
        MenuItemImpl menuItemImpl0 = (MenuItemImpl)this.c.f().getItem(v);
        this.b.t(menuItemImpl0, null, 0);
    }

    @Override  // android.content.DialogInterface$OnDismissListener
    public final void onDismiss(DialogInterface dialogInterface0) {
        this.c.a(this.b, true);
    }

    @Override  // android.content.DialogInterface$OnKeyListener
    public final boolean onKey(DialogInterface dialogInterface0, int v, KeyEvent keyEvent0) {
        MenuBuilder menuBuilder0 = this.b;
        if(v == 4 || v == 82) {
            if(keyEvent0.getAction() == 0 && keyEvent0.getRepeatCount() == 0) {
                Window window0 = this.a.getWindow();
                if(window0 != null) {
                    View view0 = window0.getDecorView();
                    if(view0 != null) {
                        KeyEvent.DispatcherState keyEvent$DispatcherState0 = view0.getKeyDispatcherState();
                        if(keyEvent$DispatcherState0 != null) {
                            keyEvent$DispatcherState0.startTracking(keyEvent0, this);
                            return true;
                        }
                    }
                }
            }
            else if(keyEvent0.getAction() == 1 && !keyEvent0.isCanceled()) {
                Window window1 = this.a.getWindow();
                if(window1 != null) {
                    View view1 = window1.getDecorView();
                    if(view1 != null) {
                        KeyEvent.DispatcherState keyEvent$DispatcherState1 = view1.getKeyDispatcherState();
                        if(keyEvent$DispatcherState1 != null && keyEvent$DispatcherState1.isTracking(keyEvent0)) {
                            menuBuilder0.d(true);
                            dialogInterface0.dismiss();
                            return true;
                        }
                    }
                }
            }
        }
        return menuBuilder0.performShortcut(v, keyEvent0, 0);
    }
}


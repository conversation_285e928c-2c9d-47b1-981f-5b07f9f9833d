package android.supportv1.v7.view.menu;

import android.supportv1.v4.view.ActionProvider.VisibilityListener;
import android.view.ActionProvider.VisibilityListener;
import android.view.ActionProvider;
import android.view.MenuItem;
import android.view.View;

class MenuItemWrapperJB extends MenuItemWrapperICS {
    class ActionProviderWrapperJB extends Action<PERSON>roviderWrapper implements ActionProvider.VisibilityListener {
        public VisibilityListener c;

        @Override  // android.supportv1.v4.view.ActionProvider
        public final boolean isVisible() {
            return this.a.isVisible();
        }

        @Override  // android.view.ActionProvider$VisibilityListener
        public final void onActionProviderVisibilityChanged(boolean z) {
            VisibilityListener actionProvider$VisibilityListener0 = this.c;
            if(actionProvider$VisibilityListener0 != null) {
                actionProvider$VisibilityListener0.onActionProviderVisibilityChanged(z);
            }
        }

        @Override  // android.supportv1.v4.view.ActionProvider
        public final View onCreateActionView(MenuItem menuItem0) {
            return this.a.onCreateActionView(menuItem0);
        }

        @Override  // android.supportv1.v4.view.ActionProvider
        public final boolean overridesItemVisibility() {
            return this.a.overridesItemVisibility();
        }

        @Override  // android.supportv1.v4.view.ActionProvider
        public final void refreshVisibility() {
            this.a.refreshVisibility();
        }

        @Override  // android.supportv1.v4.view.ActionProvider
        public final void setVisibilityListener(VisibilityListener actionProvider$VisibilityListener0) {
            this.c = actionProvider$VisibilityListener0;
            this.a.setVisibilityListener((actionProvider$VisibilityListener0 == null ? null : this));
        }
    }

    @Override  // android.supportv1.v7.view.menu.MenuItemWrapperICS
    public final ActionProviderWrapper d(ActionProvider actionProvider0) {
        return new ActionProviderWrapperJB(this, this.b, actionProvider0);  // 初始化器: Landroid/supportv1/v7/view/menu/MenuItemWrapperICS$ActionProviderWrapper;-><init>(Landroid/supportv1/v7/view/menu/MenuItemWrapperICS;Landroid/content/Context;Landroid/view/ActionProvider;)V
    }
}


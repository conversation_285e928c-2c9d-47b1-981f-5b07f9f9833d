package android.supportv1.v7.view.menu;

import android.content.Context;
import android.supportv1.v4.internal.view.SupportMenuItem;
import android.supportv1.v4.internal.view.SupportSubMenu;
import android.supportv1.v4.util.ArrayMap;
import android.view.MenuItem;
import android.view.SubMenu;

abstract class BaseMenuWrapper extends BaseWrapper {
    public final Context b;
    public ArrayMap c;
    public ArrayMap d;

    public BaseMenuWrapper(Context context0, Object object0) {
        super(object0);
        this.b = context0;
    }

    public final MenuItem b(MenuItem menuItem0) {
        if(menuItem0 instanceof SupportMenuItem) {
            SupportMenuItem supportMenuItem0 = (SupportMenuItem)menuItem0;
            if(this.c == null) {
                this.c = new ArrayMap();
            }
            menuItem0 = (MenuItem)this.c.get(menuItem0);
            if(menuItem0 == null) {
                menuItem0 = new MenuItemWrapperJB(this.b, supportMenuItem0);  // 初始化器: Landroid/supportv1/v7/view/menu/BaseMenuWrapper;-><init>(Landroid/content/Context;Ljava/lang/Object;)V
                this.c.put(supportMenuItem0, menuItem0);
            }
        }
        return menuItem0;
    }

    public final SubMenu c(SubMenu subMenu0) {
        if(subMenu0 instanceof SupportSubMenu) {
            if(this.d == null) {
                this.d = new ArrayMap();
            }
            SubMenu subMenu1 = (SubMenu)this.d.get(((SupportSubMenu)subMenu0));
            if(subMenu1 == null) {
                subMenu1 = new SubMenuWrapperICS(this.b, ((SupportSubMenu)subMenu0));  // 初始化器: Landroid/supportv1/v7/view/menu/BaseMenuWrapper;-><init>(Landroid/content/Context;Ljava/lang/Object;)V
                this.d.put(((SupportSubMenu)subMenu0), subMenu1);
            }
            return subMenu1;
        }
        return subMenu0;
    }
}


package android.supportv1.v7.view.menu;

import android.graphics.drawable.Drawable;
import android.supportv1.v4.internal.view.SupportSubMenu;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;

class SubMenuWrapperICS extends MenuWrapperICS implements SubMenu {
    @Override  // android.view.SubMenu
    public final void clearHeader() {
        ((SupportSubMenu)this.a).clearHeader();
    }

    @Override  // android.view.SubMenu
    public final MenuItem getItem() {
        return this.b(((SupportSubMenu)this.a).getItem());
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderIcon(int v) {
        ((SupportSubMenu)this.a).setHeaderIcon(v);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderIcon(Drawable drawable0) {
        ((SupportSubMenu)this.a).setHeaderIcon(drawable0);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderTitle(int v) {
        ((SupportSubMenu)this.a).setHeaderTitle(v);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderTitle(CharSequence charSequence0) {
        ((SupportSubMenu)this.a).setHeaderTitle(charSequence0);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setHeaderView(View view0) {
        ((SupportSubMenu)this.a).setHeaderView(view0);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setIcon(int v) {
        ((SupportSubMenu)this.a).setIcon(v);
        return this;
    }

    @Override  // android.view.SubMenu
    public final SubMenu setIcon(Drawable drawable0) {
        ((SupportSubMenu)this.a).setIcon(drawable0);
        return this;
    }
}


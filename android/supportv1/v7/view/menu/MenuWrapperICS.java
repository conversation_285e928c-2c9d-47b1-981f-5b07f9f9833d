package android.supportv1.v7.view.menu;

import android.content.ComponentName;
import android.content.Intent;
import android.supportv1.v4.internal.view.SupportMenu;
import android.supportv1.v4.util.ArrayMap;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.SubMenu;
import java.util.Iterator;

class MenuWrapperICS extends BaseMenuWrapper implements Menu {
    @Override  // android.view.Menu
    public final MenuItem add(int v) {
        return this.b(((SupportMenu)this.a).add(v));
    }

    @Override  // android.view.Menu
    public final MenuItem add(int v, int v1, int v2, int v3) {
        return this.b(((SupportMenu)this.a).add(v, v1, v2, v3));
    }

    @Override  // android.view.Menu
    public final MenuItem add(int v, int v1, int v2, CharSequence charSequence0) {
        return this.b(((SupportMenu)this.a).add(v, v1, v2, charSequence0));
    }

    @Override  // android.view.Menu
    public final MenuItem add(CharSequence charSequence0) {
        return this.b(((SupportMenu)this.a).add(charSequence0));
    }

    @Override  // android.view.Menu
    public final int addIntentOptions(int v, int v1, int v2, ComponentName componentName0, Intent[] arr_intent, Intent intent0, int v3, MenuItem[] arr_menuItem) {
        MenuItem[] arr_menuItem1 = arr_menuItem == null ? null : new MenuItem[arr_menuItem.length];
        int v4 = ((SupportMenu)this.a).addIntentOptions(v, v1, v2, componentName0, arr_intent, intent0, v3, arr_menuItem1);
        if(arr_menuItem1 != null) {
            for(int v5 = 0; v5 < arr_menuItem1.length; ++v5) {
                arr_menuItem[v5] = this.b(arr_menuItem1[v5]);
            }
        }
        return v4;
    }

    @Override  // android.view.Menu
    public final SubMenu addSubMenu(int v) {
        return this.c(((SupportMenu)this.a).addSubMenu(v));
    }

    @Override  // android.view.Menu
    public final SubMenu addSubMenu(int v, int v1, int v2, int v3) {
        return this.c(((SupportMenu)this.a).addSubMenu(v, v1, v2, v3));
    }

    @Override  // android.view.Menu
    public final SubMenu addSubMenu(int v, int v1, int v2, CharSequence charSequence0) {
        return this.c(((SupportMenu)this.a).addSubMenu(v, v1, v2, charSequence0));
    }

    @Override  // android.view.Menu
    public final SubMenu addSubMenu(CharSequence charSequence0) {
        return this.c(((SupportMenu)this.a).addSubMenu(charSequence0));
    }

    @Override  // android.view.Menu
    public final void clear() {
        ArrayMap arrayMap0 = this.c;
        if(arrayMap0 != null) {
            arrayMap0.clear();
        }
        ArrayMap arrayMap1 = this.d;
        if(arrayMap1 != null) {
            arrayMap1.clear();
        }
        ((SupportMenu)this.a).clear();
    }

    @Override  // android.view.Menu
    public final void close() {
        ((SupportMenu)this.a).close();
    }

    @Override  // android.view.Menu
    public final MenuItem findItem(int v) {
        return this.b(((SupportMenu)this.a).findItem(v));
    }

    @Override  // android.view.Menu
    public final MenuItem getItem(int v) {
        return this.b(((SupportMenu)this.a).getItem(v));
    }

    @Override  // android.view.Menu
    public final boolean hasVisibleItems() {
        return ((SupportMenu)this.a).hasVisibleItems();
    }

    @Override  // android.view.Menu
    public final boolean isShortcutKey(int v, KeyEvent keyEvent0) {
        return ((SupportMenu)this.a).isShortcutKey(v, keyEvent0);
    }

    @Override  // android.view.Menu
    public final boolean performIdentifierAction(int v, int v1) {
        return ((SupportMenu)this.a).performIdentifierAction(v, v1);
    }

    @Override  // android.view.Menu
    public final boolean performShortcut(int v, KeyEvent keyEvent0, int v1) {
        return ((SupportMenu)this.a).performShortcut(v, keyEvent0, v1);
    }

    @Override  // android.view.Menu
    public final void removeGroup(int v) {
        ArrayMap arrayMap0 = this.c;
        if(arrayMap0 != null) {
            Iterator iterator0 = arrayMap0.keySet().iterator();
            while(iterator0.hasNext()) {
                Object object0 = iterator0.next();
                if(v == ((MenuItem)object0).getGroupId()) {
                    iterator0.remove();
                }
            }
        }
        ((SupportMenu)this.a).removeGroup(v);
    }

    @Override  // android.view.Menu
    public final void removeItem(int v) {
        ArrayMap arrayMap0 = this.c;
        if(arrayMap0 != null) {
            Iterator iterator0 = arrayMap0.keySet().iterator();
            while(iterator0.hasNext()) {
                Object object0 = iterator0.next();
                if(v == ((MenuItem)object0).getItemId()) {
                    iterator0.remove();
                    break;
                }
                if(false) {
                    break;
                }
            }
        }
        ((SupportMenu)this.a).removeItem(v);
    }

    @Override  // android.view.Menu
    public final void setGroupCheckable(int v, boolean z, boolean z1) {
        ((SupportMenu)this.a).setGroupCheckable(v, z, z1);
    }

    @Override  // android.view.Menu
    public final void setGroupEnabled(int v, boolean z) {
        ((SupportMenu)this.a).setGroupEnabled(v, z);
    }

    @Override  // android.view.Menu
    public final void setGroupVisible(int v, boolean z) {
        ((SupportMenu)this.a).setGroupVisible(v, z);
    }

    @Override  // android.view.Menu
    public final void setQwertyMode(boolean z) {
        ((SupportMenu)this.a).setQwertyMode(z);
    }

    @Override  // android.view.Menu
    public final int size() {
        return ((SupportMenu)this.a).size();
    }
}


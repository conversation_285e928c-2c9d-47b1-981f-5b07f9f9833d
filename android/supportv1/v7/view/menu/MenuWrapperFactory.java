package android.supportv1.v7.view.menu;

import android.content.Context;
import android.supportv1.v4.internal.view.SupportMenu;
import android.supportv1.v4.internal.view.SupportMenuItem;
import android.view.Menu;
import android.view.MenuItem;

public abstract class MenuWrapperFactory {
    public static Menu a(Context context0, SupportMenu supportMenu0) {
        return new MenuWrapperICS(context0, supportMenu0);  // 初始化器: Landroid/supportv1/v7/view/menu/BaseMenuWrapper;-><init>(Landroid/content/Context;Ljava/lang/Object;)V
    }

    public static MenuItem b(Context context0, SupportMenuItem supportMenuItem0) {
        return new MenuItemWrapperJB(context0, supportMenuItem0);  // 初始化器: Landroid/supportv1/v7/view/menu/BaseMenuWrapper;-><init>(Landroid/content/Context;Ljava/lang/Object;)V
    }
}


package android.supportv1.v7.view.menu;

import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.supportv1.v4.internal.view.SupportMenuItem;
import android.supportv1.v4.view.ActionProvider;
import android.supportv1.v7.content.res.AppCompatResources;
import android.view.ContextMenu.ContextMenuInfo;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MenuItem.OnActionExpandListener;
import android.view.MenuItem.OnMenuItemClickListener;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.widget.LinearLayout;
import java.util.ArrayList;

public final class MenuItemImpl implements SupportMenuItem {
    public CharSequence A;
    public CharSequence B;
    public ActionProvider a;
    public View b;
    public final int c;
    public MenuItem.OnMenuItemClickListener d;
    public CharSequence e;
    public int f;
    public final int g;
    public boolean h;
    public boolean i;
    public Drawable j;
    public int k;
    public ColorStateList l;
    public PorterDuff.Mode m;
    public final int n;
    public Intent o;
    public final MenuBuilder p;
    public boolean q;
    public MenuItem.OnActionExpandListener r;
    public final int s;
    public char t;
    public int u;
    public char v;
    public int w;
    public int x;
    public SubMenuBuilder y;
    public CharSequence z;

    public MenuItemImpl(MenuBuilder menuBuilder0, int v, int v1, int v2, int v3, CharSequence charSequence0, int v4) {
        this.w = 0x1000;
        this.u = 0x1000;
        this.k = 0;
        this.l = null;
        this.m = null;
        this.h = false;
        this.i = false;
        this.q = false;
        this.f = 16;
        this.p = menuBuilder0;
        this.n = v1;
        this.g = v;
        this.c = v2;
        this.s = v3;
        this.z = charSequence0;
        this.x = v4;
    }

    public final Drawable b(Drawable drawable0) {
        if(drawable0 != null && this.q && (this.h || this.i)) {
            drawable0 = DrawableCompat.wrap(drawable0).mutate();
            if(this.h) {
                DrawableCompat.setTintList(drawable0, this.l);
            }
            if(this.i) {
                DrawableCompat.setTintMode(drawable0, this.m);
            }
            this.q = false;
        }
        return drawable0;
    }

    public final boolean c() {
        if((this.x & 8) != 0) {
            if(this.b == null) {
                ActionProvider actionProvider0 = this.a;
                if(actionProvider0 != null) {
                    this.b = actionProvider0.onCreateActionView(this);
                }
            }
            return this.b != null;
        }
        return false;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final boolean collapseActionView() {
        if((this.x & 8) == 0) {
            return false;
        }
        if(this.b == null) {
            return true;
        }
        return this.r == null || this.r.onMenuItemActionCollapse(this) ? this.p.e(this) : false;
    }

    public final boolean d() {
        return (this.f & 0x20) == 0x20;
    }

    public final void e(boolean z) {
        this.f = z ? this.f | 0x20 : this.f & -33;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final boolean expandActionView() {
        if(!this.c()) {
            return false;
        }
        return this.r == null || this.r.onMenuItemActionExpand(this) ? this.p.g(this) : false;
    }

    @Override  // android.view.MenuItem
    public final android.view.ActionProvider getActionProvider() {
        throw new UnsupportedOperationException("This is not supported, use MenuItemCompat.getActionProvider()");
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final View getActionView() {
        View view0 = this.b;
        if(view0 != null) {
            return view0;
        }
        ActionProvider actionProvider0 = this.a;
        if(actionProvider0 != null) {
            View view1 = actionProvider0.onCreateActionView(this);
            this.b = view1;
            return view1;
        }
        return null;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final int getAlphabeticModifiers() {
        return this.u;
    }

    @Override  // android.view.MenuItem
    public final char getAlphabeticShortcut() {
        return this.t;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final CharSequence getContentDescription() {
        return this.e;
    }

    @Override  // android.view.MenuItem
    public final int getGroupId() {
        return this.g;
    }

    @Override  // android.view.MenuItem
    public final Drawable getIcon() {
        Drawable drawable0 = this.j;
        if(drawable0 != null) {
            return this.b(drawable0);
        }
        int v = this.k;
        if(v != 0) {
            Drawable drawable1 = AppCompatResources.c(this.p.c, v);
            this.k = 0;
            this.j = drawable1;
            return this.b(drawable1);
        }
        return null;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final ColorStateList getIconTintList() {
        return this.l;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final PorterDuff.Mode getIconTintMode() {
        return this.m;
    }

    @Override  // android.view.MenuItem
    public final Intent getIntent() {
        return this.o;
    }

    @Override  // android.view.MenuItem
    public final int getItemId() {
        return this.n;
    }

    @Override  // android.view.MenuItem
    public final ContextMenu.ContextMenuInfo getMenuInfo() {
        return null;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final int getNumericModifiers() {
        return this.w;
    }

    @Override  // android.view.MenuItem
    public final char getNumericShortcut() {
        return this.v;
    }

    @Override  // android.view.MenuItem
    public final int getOrder() {
        return this.c;
    }

    @Override  // android.view.MenuItem
    public final SubMenu getSubMenu() {
        return this.y;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final ActionProvider getSupportActionProvider() {
        return this.a;
    }

    @Override  // android.view.MenuItem
    public final CharSequence getTitle() {
        return this.z;
    }

    @Override  // android.view.MenuItem
    public final CharSequence getTitleCondensed() {
        return this.A == null ? this.z : this.A;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final CharSequence getTooltipText() {
        return this.B;
    }

    @Override  // android.view.MenuItem
    public final boolean hasSubMenu() {
        return this.y != null;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final boolean isActionViewExpanded() {
        return false;
    }

    @Override  // android.view.MenuItem
    public final boolean isCheckable() {
        return (this.f & 1) == 1;
    }

    @Override  // android.view.MenuItem
    public final boolean isChecked() {
        return (this.f & 2) == 2;
    }

    @Override  // android.view.MenuItem
    public final boolean isEnabled() {
        return (this.f & 16) != 0;
    }

    // 去混淆评级： 低(30)
    @Override  // android.view.MenuItem
    public final boolean isVisible() {
        return this.a == null || !this.a.overridesItemVisibility() ? (this.f & 8) == 0 : (this.f & 8) == 0 && this.a.isVisible();
    }

    @Override  // android.view.MenuItem
    public final MenuItem setActionProvider(android.view.ActionProvider actionProvider0) {
        throw new UnsupportedOperationException("This is not supported, use MenuItemCompat.setActionProvider()");
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final MenuItem setActionView(int v) {
        View view0 = LayoutInflater.from(this.p.c).inflate(v, new LinearLayout(this.p.c), false);
        this.b = view0;
        this.a = null;
        if(view0 != null && view0.getId() == -1) {
            int v1 = this.n;
            if(v1 > 0) {
                view0.setId(v1);
            }
        }
        this.p.h = true;
        this.p.s(true);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final MenuItem setActionView(View view0) {
        this.b = view0;
        this.a = null;
        if(view0 != null && view0.getId() == -1) {
            int v = this.n;
            if(v > 0) {
                view0.setId(v);
            }
        }
        this.p.h = true;
        this.p.s(true);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setAlphabeticShortcut(char c) {
        if(this.t == c) {
            return this;
        }
        this.t = Character.toLowerCase(c);
        this.p.s(false);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final MenuItem setAlphabeticShortcut(char c, int v) {
        if(this.t == c && this.u == v) {
            return this;
        }
        this.t = Character.toLowerCase(c);
        this.u = KeyEvent.normalizeMetaState(v);
        this.p.s(false);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setCheckable(boolean z) {
        int v = this.f;
        int v1 = z | v & -2;
        this.f = v1;
        if(v != v1) {
            this.p.s(false);
        }
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setChecked(boolean z) {
        int v = this.f;
        int v1 = 2;
        if((v & 4) != 0) {
            MenuBuilder menuBuilder0 = this.p;
            menuBuilder0.getClass();
            ArrayList arrayList0 = menuBuilder0.k;
            int v2 = arrayList0.size();
            menuBuilder0.A();
            for(int v3 = 0; v3 < v2; ++v3) {
                MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList0.get(v3);
                if(menuItemImpl0.g == this.g && (menuItemImpl0.f & 4) != 0 && menuItemImpl0.isCheckable()) {
                    int v4 = menuItemImpl0.f;
                    int v5 = (menuItemImpl0 == this ? 0 : 2) | v4 & -3;
                    menuItemImpl0.f = v5;
                    if(v4 != v5) {
                        menuItemImpl0.p.s(false);
                    }
                }
            }
            menuBuilder0.z();
            return this;
        }
        if(!z) {
            v1 = 0;
        }
        int v6 = v & -3 | v1;
        this.f = v6;
        if(v != v6) {
            this.p.s(false);
        }
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final SupportMenuItem setContentDescription(CharSequence charSequence0) {
        this.e = charSequence0;
        this.p.s(false);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setContentDescription(CharSequence charSequence0) {
        this.setContentDescription(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setEnabled(boolean z) {
        this.f = z ? this.f | 16 : this.f & -17;
        this.p.s(false);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIcon(int v) {
        this.j = null;
        this.k = v;
        this.q = true;
        this.p.s(false);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIcon(Drawable drawable0) {
        this.k = 0;
        this.j = drawable0;
        this.q = true;
        this.p.s(false);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final MenuItem setIconTintList(ColorStateList colorStateList0) {
        this.l = colorStateList0;
        this.h = true;
        this.q = true;
        this.p.s(false);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final MenuItem setIconTintMode(PorterDuff.Mode porterDuff$Mode0) {
        this.m = porterDuff$Mode0;
        this.i = true;
        this.q = true;
        this.p.s(false);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIntent(Intent intent0) {
        this.o = intent0;
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setNumericShortcut(char c) {
        if(this.v == c) {
            return this;
        }
        this.v = c;
        this.p.s(false);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final MenuItem setNumericShortcut(char c, int v) {
        if(this.v == c && this.w == v) {
            return this;
        }
        this.v = c;
        this.w = KeyEvent.normalizeMetaState(v);
        this.p.s(false);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setOnActionExpandListener(MenuItem.OnActionExpandListener menuItem$OnActionExpandListener0) {
        this.r = menuItem$OnActionExpandListener0;
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setOnMenuItemClickListener(MenuItem.OnMenuItemClickListener menuItem$OnMenuItemClickListener0) {
        this.d = menuItem$OnMenuItemClickListener0;
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setShortcut(char c, char c1) {
        this.v = c;
        this.t = Character.toLowerCase(c1);
        this.p.s(false);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final MenuItem setShortcut(char c, char c1, int v, int v1) {
        this.v = c;
        this.w = KeyEvent.normalizeMetaState(v);
        this.t = Character.toLowerCase(c1);
        this.u = KeyEvent.normalizeMetaState(v1);
        this.p.s(false);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final void setShowAsAction(int v) {
        if((v & 3) != 0 && (v & 3) != 1 && (v & 3) != 2) {
            throw new IllegalArgumentException("SHOW_AS_ACTION_ALWAYS, SHOW_AS_ACTION_IF_ROOM, and SHOW_AS_ACTION_NEVER are mutually exclusive.");
        }
        this.x = v;
        this.p.h = true;
        this.p.s(true);
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final MenuItem setShowAsActionFlags(int v) {
        this.setShowAsAction(v);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final SupportMenuItem setSupportActionProvider(ActionProvider actionProvider0) {
        ActionProvider actionProvider1 = this.a;
        if(actionProvider1 != null) {
            actionProvider1.reset();
        }
        this.b = null;
        this.a = actionProvider0;
        this.p.s(true);
        ActionProvider actionProvider2 = this.a;
        if(actionProvider2 != null) {
            actionProvider2.setVisibilityListener(new MenuItemImpl.1(this));
        }
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitle(int v) {
        this.setTitle(this.p.c.getString(v));
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitle(CharSequence charSequence0) {
        this.z = charSequence0;
        this.p.s(false);
        SubMenuBuilder subMenuBuilder0 = this.y;
        if(subMenuBuilder0 != null) {
            subMenuBuilder0.setHeaderTitle(charSequence0);
        }
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitleCondensed(CharSequence charSequence0) {
        this.A = charSequence0;
        this.p.s(false);
        return this;
    }

    @Override  // android.supportv1.v4.internal.view.SupportMenuItem
    public final SupportMenuItem setTooltipText(CharSequence charSequence0) {
        this.B = charSequence0;
        this.p.s(false);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTooltipText(CharSequence charSequence0) {
        this.setTooltipText(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setVisible(boolean z) {
        int v = this.f;
        int v1 = (z ? 0 : 8) | v & -9;
        this.f = v1;
        if(v != v1) {
            this.p.j = true;
            this.p.s(true);
        }
        return this;
    }

    @Override
    public final String toString() {
        return this.z == null ? null : this.z.toString();
    }
}


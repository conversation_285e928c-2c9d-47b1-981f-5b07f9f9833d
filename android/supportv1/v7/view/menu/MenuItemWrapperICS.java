package android.supportv1.v7.view.menu;

import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.supportv1.v4.internal.view.SupportMenuItem;
import android.supportv1.v4.view.ActionProvider;
import android.view.CollapsibleActionView;
import android.view.ContextMenu.ContextMenuInfo;
import android.view.MenuItem.OnActionExpandListener;
import android.view.MenuItem.OnMenuItemClickListener;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.widget.FrameLayout;
import java.lang.reflect.Method;

public abstract class MenuItemWrapperICS extends BaseMenuWrapper implements MenuItem {
    abstract class ActionProviderWrapper extends ActionProvider {
        public final android.view.ActionProvider a;
        public final MenuItemWrapperICS b;

        public ActionProviderWrapper(Context context0, android.view.ActionProvider actionProvider0) {
            super(context0);
            this.a = actionProvider0;
        }

        @Override  // android.supportv1.v4.view.ActionProvider
        public final boolean hasSubMenu() {
            return this.a.hasSubMenu();
        }

        @Override  // android.supportv1.v4.view.ActionProvider
        public final View onCreateActionView() {
            return this.a.onCreateActionView();
        }

        @Override  // android.supportv1.v4.view.ActionProvider
        public final boolean onPerformDefaultAction() {
            return this.a.onPerformDefaultAction();
        }

        @Override  // android.supportv1.v4.view.ActionProvider
        public final void onPrepareSubMenu(SubMenu subMenu0) {
            SubMenu subMenu1 = MenuItemWrapperICS.this.c(subMenu0);
            this.a.onPrepareSubMenu(subMenu1);
        }
    }

    static class CollapsibleActionViewWrapper extends FrameLayout {
        public final CollapsibleActionView a;

        public CollapsibleActionViewWrapper(View view0) {
            super(view0.getContext());
            this.a = (CollapsibleActionView)view0;
            this.addView(view0);
        }
    }

    class OnActionExpandListenerWrapper extends BaseWrapper implements MenuItem.OnActionExpandListener {
        public final MenuItemWrapperICS b;

        public OnActionExpandListenerWrapper(MenuItem.OnActionExpandListener menuItem$OnActionExpandListener0) {
            super(menuItem$OnActionExpandListener0);
        }

        @Override  // android.view.MenuItem$OnActionExpandListener
        public final boolean onMenuItemActionCollapse(MenuItem menuItem0) {
            MenuItem menuItem1 = MenuItemWrapperICS.this.b(menuItem0);
            return ((MenuItem.OnActionExpandListener)this.a).onMenuItemActionCollapse(menuItem1);
        }

        @Override  // android.view.MenuItem$OnActionExpandListener
        public final boolean onMenuItemActionExpand(MenuItem menuItem0) {
            MenuItem menuItem1 = MenuItemWrapperICS.this.b(menuItem0);
            return ((MenuItem.OnActionExpandListener)this.a).onMenuItemActionExpand(menuItem1);
        }
    }

    class OnMenuItemClickListenerWrapper extends BaseWrapper implements MenuItem.OnMenuItemClickListener {
        public final MenuItemWrapperICS b;

        public OnMenuItemClickListenerWrapper(MenuItem.OnMenuItemClickListener menuItem$OnMenuItemClickListener0) {
            super(menuItem$OnMenuItemClickListener0);
        }

        @Override  // android.view.MenuItem$OnMenuItemClickListener
        public final boolean onMenuItemClick(MenuItem menuItem0) {
            MenuItem menuItem1 = MenuItemWrapperICS.this.b(menuItem0);
            return ((MenuItem.OnMenuItemClickListener)this.a).onMenuItemClick(menuItem1);
        }
    }

    public Method e;

    @Override  // android.view.MenuItem
    public final boolean collapseActionView() {
        return ((SupportMenuItem)this.a).collapseActionView();
    }

    public abstract ActionProviderWrapper d(android.view.ActionProvider arg1);

    public final void e() {
        try {
            Object object0 = this.a;
            if(this.e == null) {
                this.e = ((SupportMenuItem)object0).getClass().getDeclaredMethod("setExclusiveCheckable", Boolean.TYPE);
            }
            this.e.invoke(object0, Boolean.TRUE);
        }
        catch(Exception unused_ex) {
        }
    }

    @Override  // android.view.MenuItem
    public final boolean expandActionView() {
        return ((SupportMenuItem)this.a).expandActionView();
    }

    @Override  // android.view.MenuItem
    public final android.view.ActionProvider getActionProvider() {
        ActionProvider actionProvider0 = ((SupportMenuItem)this.a).getSupportActionProvider();
        return actionProvider0 instanceof ActionProviderWrapper ? ((ActionProviderWrapper)actionProvider0).a : null;
    }

    @Override  // android.view.MenuItem
    public final View getActionView() {
        View view0 = ((SupportMenuItem)this.a).getActionView();
        return view0 instanceof CollapsibleActionViewWrapper ? ((View)((CollapsibleActionViewWrapper)view0).a) : view0;
    }

    @Override  // android.view.MenuItem
    public final int getAlphabeticModifiers() {
        return ((SupportMenuItem)this.a).getAlphabeticModifiers();
    }

    @Override  // android.view.MenuItem
    public final char getAlphabeticShortcut() {
        return ((SupportMenuItem)this.a).getAlphabeticShortcut();
    }

    @Override  // android.view.MenuItem
    public final CharSequence getContentDescription() {
        return ((SupportMenuItem)this.a).getContentDescription();
    }

    @Override  // android.view.MenuItem
    public final int getGroupId() {
        return ((SupportMenuItem)this.a).getGroupId();
    }

    @Override  // android.view.MenuItem
    public final Drawable getIcon() {
        return ((SupportMenuItem)this.a).getIcon();
    }

    @Override  // android.view.MenuItem
    public final ColorStateList getIconTintList() {
        return ((SupportMenuItem)this.a).getIconTintList();
    }

    @Override  // android.view.MenuItem
    public final PorterDuff.Mode getIconTintMode() {
        return ((SupportMenuItem)this.a).getIconTintMode();
    }

    @Override  // android.view.MenuItem
    public final Intent getIntent() {
        return ((SupportMenuItem)this.a).getIntent();
    }

    @Override  // android.view.MenuItem
    public final int getItemId() {
        return ((SupportMenuItem)this.a).getItemId();
    }

    @Override  // android.view.MenuItem
    public final ContextMenu.ContextMenuInfo getMenuInfo() {
        return ((SupportMenuItem)this.a).getMenuInfo();
    }

    @Override  // android.view.MenuItem
    public final int getNumericModifiers() {
        return ((SupportMenuItem)this.a).getNumericModifiers();
    }

    @Override  // android.view.MenuItem
    public final char getNumericShortcut() {
        return ((SupportMenuItem)this.a).getNumericShortcut();
    }

    @Override  // android.view.MenuItem
    public final int getOrder() {
        return ((SupportMenuItem)this.a).getOrder();
    }

    @Override  // android.view.MenuItem
    public final SubMenu getSubMenu() {
        return this.c(((SupportMenuItem)this.a).getSubMenu());
    }

    @Override  // android.view.MenuItem
    public final CharSequence getTitle() {
        return ((SupportMenuItem)this.a).getTitle();
    }

    @Override  // android.view.MenuItem
    public final CharSequence getTitleCondensed() {
        return ((SupportMenuItem)this.a).getTitleCondensed();
    }

    @Override  // android.view.MenuItem
    public final CharSequence getTooltipText() {
        return ((SupportMenuItem)this.a).getTooltipText();
    }

    @Override  // android.view.MenuItem
    public final boolean hasSubMenu() {
        return ((SupportMenuItem)this.a).hasSubMenu();
    }

    @Override  // android.view.MenuItem
    public final boolean isActionViewExpanded() {
        return ((SupportMenuItem)this.a).isActionViewExpanded();
    }

    @Override  // android.view.MenuItem
    public final boolean isCheckable() {
        return ((SupportMenuItem)this.a).isCheckable();
    }

    @Override  // android.view.MenuItem
    public final boolean isChecked() {
        return ((SupportMenuItem)this.a).isChecked();
    }

    @Override  // android.view.MenuItem
    public final boolean isEnabled() {
        return ((SupportMenuItem)this.a).isEnabled();
    }

    @Override  // android.view.MenuItem
    public final boolean isVisible() {
        return ((SupportMenuItem)this.a).isVisible();
    }

    @Override  // android.view.MenuItem
    public final MenuItem setActionProvider(android.view.ActionProvider actionProvider0) {
        ActionProviderWrapper menuItemWrapperICS$ActionProviderWrapper0 = actionProvider0 == null ? null : this.d(actionProvider0);
        ((SupportMenuItem)this.a).setSupportActionProvider(menuItemWrapperICS$ActionProviderWrapper0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setActionView(int v) {
        Object object0 = this.a;
        ((SupportMenuItem)object0).setActionView(v);
        View view0 = ((SupportMenuItem)object0).getActionView();
        if(view0 instanceof CollapsibleActionView) {
            ((SupportMenuItem)object0).setActionView(new CollapsibleActionViewWrapper(view0));
        }
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setActionView(View view0) {
        if(view0 instanceof CollapsibleActionView) {
            view0 = new CollapsibleActionViewWrapper(view0);
        }
        ((SupportMenuItem)this.a).setActionView(view0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setAlphabeticShortcut(char c) {
        ((SupportMenuItem)this.a).setAlphabeticShortcut(c);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setAlphabeticShortcut(char c, int v) {
        ((SupportMenuItem)this.a).setAlphabeticShortcut(c, v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setCheckable(boolean z) {
        ((SupportMenuItem)this.a).setCheckable(z);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setChecked(boolean z) {
        ((SupportMenuItem)this.a).setChecked(z);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setContentDescription(CharSequence charSequence0) {
        ((SupportMenuItem)this.a).setContentDescription(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setEnabled(boolean z) {
        ((SupportMenuItem)this.a).setEnabled(z);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIcon(int v) {
        ((SupportMenuItem)this.a).setIcon(v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIcon(Drawable drawable0) {
        ((SupportMenuItem)this.a).setIcon(drawable0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIconTintList(ColorStateList colorStateList0) {
        ((SupportMenuItem)this.a).setIconTintList(colorStateList0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIconTintMode(PorterDuff.Mode porterDuff$Mode0) {
        ((SupportMenuItem)this.a).setIconTintMode(porterDuff$Mode0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setIntent(Intent intent0) {
        ((SupportMenuItem)this.a).setIntent(intent0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setNumericShortcut(char c) {
        ((SupportMenuItem)this.a).setNumericShortcut(c);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setNumericShortcut(char c, int v) {
        ((SupportMenuItem)this.a).setNumericShortcut(c, v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setOnActionExpandListener(MenuItem.OnActionExpandListener menuItem$OnActionExpandListener0) {
        OnActionExpandListenerWrapper menuItemWrapperICS$OnActionExpandListenerWrapper0 = menuItem$OnActionExpandListener0 == null ? null : new OnActionExpandListenerWrapper(this, menuItem$OnActionExpandListener0);
        ((SupportMenuItem)this.a).setOnActionExpandListener(menuItemWrapperICS$OnActionExpandListenerWrapper0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setOnMenuItemClickListener(MenuItem.OnMenuItemClickListener menuItem$OnMenuItemClickListener0) {
        OnMenuItemClickListenerWrapper menuItemWrapperICS$OnMenuItemClickListenerWrapper0 = menuItem$OnMenuItemClickListener0 == null ? null : new OnMenuItemClickListenerWrapper(this, menuItem$OnMenuItemClickListener0);
        ((SupportMenuItem)this.a).setOnMenuItemClickListener(menuItemWrapperICS$OnMenuItemClickListenerWrapper0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setShortcut(char c, char c1) {
        ((SupportMenuItem)this.a).setShortcut(c, c1);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setShortcut(char c, char c1, int v, int v1) {
        ((SupportMenuItem)this.a).setShortcut(c, c1, v, v1);
        return this;
    }

    @Override  // android.view.MenuItem
    public final void setShowAsAction(int v) {
        ((SupportMenuItem)this.a).setShowAsAction(v);
    }

    @Override  // android.view.MenuItem
    public final MenuItem setShowAsActionFlags(int v) {
        ((SupportMenuItem)this.a).setShowAsActionFlags(v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitle(int v) {
        ((SupportMenuItem)this.a).setTitle(v);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitle(CharSequence charSequence0) {
        ((SupportMenuItem)this.a).setTitle(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTitleCondensed(CharSequence charSequence0) {
        ((SupportMenuItem)this.a).setTitleCondensed(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setTooltipText(CharSequence charSequence0) {
        ((SupportMenuItem)this.a).setTooltipText(charSequence0);
        return this;
    }

    @Override  // android.view.MenuItem
    public final MenuItem setVisible(boolean z) {
        return ((SupportMenuItem)this.a).setVisible(z);
    }
}


package android.supportv1.v7.view.menu;

import android.supportv1.v4.view.ActionProvider.VisibilityListener;

class MenuItemImpl.1 implements VisibilityListener {
    public final MenuItemImpl a;

    public MenuItemImpl.1(MenuItemImpl menuItemImpl0) {
        this.a = menuItemImpl0;
    }

    @Override  // android.supportv1.v4.view.ActionProvider$VisibilityListener
    public final void onActionProviderVisibilityChanged(boolean z) {
        this.a.p.j = true;
        this.a.p.s(true);
    }
}


package android.supportv1.v7.view;

import a.a;
import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.PorterDuff.Mode;
import android.supportv1.v4.internal.view.SupportMenu;
import android.supportv1.v4.view.ActionProvider;
import android.supportv1.v4.view.MenuItemCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.view.menu.MenuItemImpl;
import android.supportv1.v7.view.menu.MenuItemWrapperICS;
import android.supportv1.v7.widget.DrawableUtils;
import android.util.AttributeSet;
import android.util.Xml;
import android.view.InflateException;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem.OnMenuItemClickListener;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import jeb.synthetic.TWR;
import org.xmlpull.v1.XmlPullParserException;

public class SupportMenuInflater extends MenuInflater {
    static class InflatedOnMenuItemClickListener implements MenuItem.OnMenuItemClickListener {
        public Method a;
        public Object b;
        public static final Class[] c;

        static {
            InflatedOnMenuItemClickListener.c = new Class[]{MenuItem.class};
        }

        @Override  // android.view.MenuItem$OnMenuItemClickListener
        public final boolean onMenuItemClick(MenuItem menuItem0) {
            try {
                Method method0 = this.a;
                Class class0 = method0.getReturnType();
                Object object0 = this.b;
                if(class0 == Boolean.TYPE) {
                    return ((Boolean)method0.invoke(object0, menuItem0)).booleanValue();
                }
                method0.invoke(object0, menuItem0);
                return true;
            }
            catch(Exception exception0) {
            }
            throw new RuntimeException(exception0);
        }
    }

    class MenuState {
        public CharSequence A;
        public CharSequence B;
        public boolean C;
        public final Menu D;
        public final SupportMenuInflater E;
        public int a;
        public int b;
        public boolean c;
        public int d;
        public int e;
        public boolean f;
        public ActionProvider g;
        public String h;
        public int i;
        public boolean j;
        public int k;
        public char l;
        public int m;
        public int n;
        public boolean o;
        public CharSequence p;
        public boolean q;
        public int r;
        public ColorStateList s;
        public PorterDuff.Mode t;
        public int u;
        public String v;
        public int w;
        public char x;
        public int y;
        public CharSequence z;

        public MenuState(Menu menu0) {
            this.s = null;
            this.t = null;
            this.D = menu0;
            this.d = 0;
            this.a = 0;
            this.e = 0;
            this.b = 0;
            this.f = true;
            this.c = true;
        }

        public final Object a(String s, Class[] arr_class, Object[] arr_object) {
            try {
                Constructor constructor0 = this.E.c.getClassLoader().loadClass(s).getConstructor(arr_class);
                constructor0.setAccessible(true);
                return constructor0.newInstance(arr_object);
            }
            catch(Exception unused_ex) {
                return null;
            }
        }

        public final void b(AttributeSet attributeSet0) {
            TypedArray typedArray0 = this.E.c.obtainStyledAttributes(attributeSet0, R.styleable.MenuGroup);
            this.d = typedArray0.getResourceId(R.styleable.MenuGroup_android_id, 0);
            this.a = typedArray0.getInt(R.styleable.MenuGroup_android_menuCategory, 0);
            this.e = typedArray0.getInt(R.styleable.MenuGroup_android_orderInCategory, 0);
            this.b = typedArray0.getInt(R.styleable.MenuGroup_android_checkableBehavior, 0);
            this.f = typedArray0.getBoolean(R.styleable.MenuGroup_android_visible, true);
            this.c = typedArray0.getBoolean(R.styleable.MenuGroup_android_enabled, true);
            typedArray0.recycle();
        }

        public final void c(AttributeSet attributeSet0) {
            TypedArray typedArray0 = this.E.c.obtainStyledAttributes(attributeSet0, R.styleable.MenuItem);
            this.u = typedArray0.getResourceId(R.styleable.MenuItem_android_id, 0);
            this.m = typedArray0.getInt(R.styleable.MenuItem_android_menuCategory, this.a) & 0xFFFF0000 | typedArray0.getInt(R.styleable.MenuItem_android_orderInCategory, this.e) & 0xFFFF;
            this.z = typedArray0.getText(R.styleable.MenuItem_android_title);
            this.A = typedArray0.getText(R.styleable.MenuItem_android_titleCondensed);
            this.r = typedArray0.getResourceId(R.styleable.MenuItem_android_icon, 0);
            String s = typedArray0.getString(R.styleable.MenuItem_android_alphabeticShortcut);
            this.l = s == null ? '\u0000' : s.charAt(0);
            this.k = typedArray0.getInt(R.styleable.MenuItem_alphabeticModifiers, 0x1000);
            String s1 = typedArray0.getString(R.styleable.MenuItem_android_numericShortcut);
            this.x = s1 == null ? '\u0000' : s1.charAt(0);
            this.w = typedArray0.getInt(R.styleable.MenuItem_numericModifiers, 0x1000);
            boolean z = typedArray0.hasValue(R.styleable.MenuItem_android_checkable) ? typedArray0.getBoolean(R.styleable.MenuItem_android_checkable, false) : this.b;
            this.n = z;
            this.o = typedArray0.getBoolean(R.styleable.MenuItem_android_checked, false);
            this.C = typedArray0.getBoolean(R.styleable.MenuItem_android_visible, this.f);
            this.q = typedArray0.getBoolean(R.styleable.MenuItem_android_enabled, this.c);
            this.y = typedArray0.getInt(R.styleable.MenuItem_showAsAction, -1);
            this.v = typedArray0.getString(R.styleable.MenuItem_android_onClick);
            this.i = typedArray0.getResourceId(R.styleable.MenuItem_actionLayout, 0);
            this.h = typedArray0.getString(R.styleable.MenuItem_actionViewClass);
            String s2 = typedArray0.getString(R.styleable.MenuItem_actionProviderClass);
            this.g = s2 == null || this.i != 0 || this.h != null ? null : ((ActionProvider)this.a(s2, SupportMenuInflater.e, this.E.a));
            this.p = typedArray0.getText(R.styleable.MenuItem_contentDescription);
            this.B = typedArray0.getText(R.styleable.MenuItem_tooltipText);
            this.t = typedArray0.hasValue(R.styleable.MenuItem_iconTintMode) ? DrawableUtils.c(typedArray0.getInt(R.styleable.MenuItem_iconTintMode, -1), this.t) : null;
            this.s = typedArray0.hasValue(R.styleable.MenuItem_iconTint) ? typedArray0.getColorStateList(R.styleable.MenuItem_iconTint) : null;
            typedArray0.recycle();
            this.j = false;
        }

        public final void d(MenuItem menuItem0) {
            boolean z = false;
            menuItem0.setChecked(this.o).setVisible(this.C).setEnabled(this.q).setCheckable(this.n >= 1).setTitleCondensed(this.A).setIcon(this.r);
            int v = this.y;
            if(v >= 0) {
                menuItem0.setShowAsAction(v);
            }
            SupportMenuInflater supportMenuInflater0 = this.E;
            if(this.v != null) {
                if(supportMenuInflater0.c.isRestricted()) {
                    throw new IllegalStateException("The android:onClick attribute cannot be used within a restricted context");
                }
                if(supportMenuInflater0.d == null) {
                    supportMenuInflater0.d = SupportMenuInflater.a(supportMenuInflater0.c);
                }
                Object object0 = supportMenuInflater0.d;
                String s = this.v;
                InflatedOnMenuItemClickListener supportMenuInflater$InflatedOnMenuItemClickListener0 = new InflatedOnMenuItemClickListener();  // 初始化器: Ljava/lang/Object;-><init>()V
                supportMenuInflater$InflatedOnMenuItemClickListener0.b = object0;
                Class class0 = object0.getClass();
                try {
                    supportMenuInflater$InflatedOnMenuItemClickListener0.a = class0.getMethod(s, InflatedOnMenuItemClickListener.c);
                }
                catch(Exception exception0) {
                    StringBuilder stringBuilder0 = a.y("Couldn\'t resolve menu item onClick handler ", s, " in class ");
                    stringBuilder0.append(class0.getName());
                    InflateException inflateException0 = new InflateException(stringBuilder0.toString());
                    inflateException0.initCause(exception0);
                    throw inflateException0;
                }
                menuItem0.setOnMenuItemClickListener(supportMenuInflater$InflatedOnMenuItemClickListener0);
            }
            if(menuItem0 instanceof MenuItemImpl) {
                MenuItemImpl menuItemImpl0 = (MenuItemImpl)menuItem0;
            }
            if(this.n >= 2) {
                if(menuItem0 instanceof MenuItemImpl) {
                    ((MenuItemImpl)menuItem0).f |= 4;
                }
                else if(menuItem0 instanceof MenuItemWrapperICS) {
                    ((MenuItemWrapperICS)menuItem0).e();
                }
            }
            String s1 = this.h;
            if(s1 != null) {
                menuItem0.setActionView(((View)this.a(s1, SupportMenuInflater.f, supportMenuInflater0.b)));
                z = true;
            }
            int v1 = this.i;
            if(v1 > 0 && !z) {
                menuItem0.setActionView(v1);
            }
            ActionProvider actionProvider0 = this.g;
            if(actionProvider0 != null) {
                MenuItemCompat.setActionProvider(menuItem0, actionProvider0);
            }
            MenuItemCompat.setContentDescription(menuItem0, this.p);
            MenuItemCompat.setTooltipText(menuItem0, this.B);
            MenuItemCompat.setAlphabeticShortcut(menuItem0, this.l, this.k);
            MenuItemCompat.setNumericShortcut(menuItem0, this.x, this.w);
            PorterDuff.Mode porterDuff$Mode0 = this.t;
            if(porterDuff$Mode0 != null) {
                MenuItemCompat.setIconTintMode(menuItem0, porterDuff$Mode0);
            }
            ColorStateList colorStateList0 = this.s;
            if(colorStateList0 != null) {
                MenuItemCompat.setIconTintList(menuItem0, colorStateList0);
            }
        }
    }

    public final Object[] a;
    public final Object[] b;
    public final Context c;
    public Object d;
    public static final Class[] e;
    public static final Class[] f;

    static {
        Class[] arr_class = {Context.class};
        SupportMenuInflater.f = arr_class;
        SupportMenuInflater.e = arr_class;
    }

    public SupportMenuInflater(Context context0) {
        super(context0);
        this.c = context0;
        Object[] arr_object = {context0};
        this.b = arr_object;
        this.a = arr_object;
    }

    public static Object a(Context context0) {
        if(context0 instanceof Activity) {
            return context0;
        }
        return context0 instanceof ContextWrapper ? SupportMenuInflater.a(((ContextWrapper)context0).getBaseContext()) : context0;
    }

    public final void b(XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Menu menu0) {
        MenuState supportMenuInflater$MenuState0 = new MenuState(this, menu0);
        int v = xmlResourceParser0.getEventType();
        do {
            if(v == 2) {
                String s = xmlResourceParser0.getName();
                if(!s.equals("menu")) {
                    throw new RuntimeException("Expecting menu, got " + s);
                }
                v = xmlResourceParser0.next();
                break;
            }
            v = xmlResourceParser0.next();
        }
        while(v != 1);
        String s1 = null;
        boolean z = false;
        boolean z1 = false;
        while(!z1) {
            switch(v) {
                case 1: {
                    throw new RuntimeException("Unexpected end of document");
                }
                case 2: {
                    if(!z) {
                        String s2 = xmlResourceParser0.getName();
                        if(s2.equals("group")) {
                            supportMenuInflater$MenuState0.b(attributeSet0);
                        }
                        else if(s2.equals("item")) {
                            supportMenuInflater$MenuState0.c(attributeSet0);
                        }
                        else if(s2.equals("menu")) {
                            supportMenuInflater$MenuState0.j = true;
                            SubMenu subMenu0 = supportMenuInflater$MenuState0.D.addSubMenu(supportMenuInflater$MenuState0.d, supportMenuInflater$MenuState0.u, supportMenuInflater$MenuState0.m, supportMenuInflater$MenuState0.z);
                            supportMenuInflater$MenuState0.d(subMenu0.getItem());
                            this.b(xmlResourceParser0, attributeSet0, subMenu0);
                        }
                        else {
                            s1 = s2;
                            z = true;
                        }
                    }
                    break;
                }
                case 3: {
                    String s3 = xmlResourceParser0.getName();
                    if(z && s3.equals(s1)) {
                        s1 = null;
                        z = false;
                    }
                    else if(s3.equals("group")) {
                        supportMenuInflater$MenuState0.d = 0;
                        supportMenuInflater$MenuState0.a = 0;
                        supportMenuInflater$MenuState0.e = 0;
                        supportMenuInflater$MenuState0.b = 0;
                        supportMenuInflater$MenuState0.f = true;
                        supportMenuInflater$MenuState0.c = true;
                    }
                    else if(!s3.equals("item")) {
                        if(s3.equals("menu")) {
                            z1 = true;
                        }
                    }
                    else if(!supportMenuInflater$MenuState0.j) {
                        if(supportMenuInflater$MenuState0.g == null || !supportMenuInflater$MenuState0.g.hasSubMenu()) {
                            supportMenuInflater$MenuState0.j = true;
                            supportMenuInflater$MenuState0.d(supportMenuInflater$MenuState0.D.add(supportMenuInflater$MenuState0.d, supportMenuInflater$MenuState0.u, supportMenuInflater$MenuState0.m, supportMenuInflater$MenuState0.z));
                        }
                        else {
                            supportMenuInflater$MenuState0.j = true;
                            supportMenuInflater$MenuState0.d(supportMenuInflater$MenuState0.D.addSubMenu(supportMenuInflater$MenuState0.d, supportMenuInflater$MenuState0.u, supportMenuInflater$MenuState0.m, supportMenuInflater$MenuState0.z).getItem());
                        }
                    }
                }
            }
            v = xmlResourceParser0.next();
        }
    }

    @Override  // android.view.MenuInflater
    public final void inflate(int v, Menu menu0) {
        XmlResourceParser xmlResourceParser0;
        if(!(menu0 instanceof SupportMenu)) {
            super.inflate(v, menu0);
            return;
        }
        try {
            try {
                xmlResourceParser0 = null;
                xmlResourceParser0 = this.c.getResources().getLayout(v);
                this.b(xmlResourceParser0, Xml.asAttributeSet(xmlResourceParser0), menu0);
            }
            catch(XmlPullParserException xmlPullParserException0) {
                throw new InflateException("Error inflating menu XML", xmlPullParserException0);
            }
            catch(IOException iOException0) {
                throw new InflateException("Error inflating menu XML", iOException0);
            }
        }
        catch(Throwable throwable0) {
            TWR.safeClose$NT(xmlResourceParser0, throwable0);
            throw throwable0;
        }
        xmlResourceParser0.close();
    }
}


package android.supportv1.v7.content.res;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.support.v4.media.a;
import android.supportv1.v4.content.ContextCompat;
import android.supportv1.v4.content.res.ColorStateListInflaterCompat;
import android.supportv1.v7.widget.AppCompatDrawableManager;
import android.util.SparseArray;
import android.util.TypedValue;
import java.util.WeakHashMap;
import jeb.synthetic.FIN;

public abstract class AppCompatResources {
    static class ColorStateListCacheEntry {
        public final Configuration a;
        public final ColorStateList b;

        public ColorStateListCacheEntry(ColorStateList colorStateList0, Configuration configuration0) {
            this.b = colorStateList0;
            this.a = configuration0;
        }
    }

    public static final ThreadLocal a;
    public static final Object b;
    public static final WeakHashMap c;

    static {
        AppCompatResources.a = new ThreadLocal();
        AppCompatResources.c = new WeakHashMap(0);
        AppCompatResources.b = new Object();
    }

    public static void a(Context context0, int v, ColorStateList colorStateList0) {
        synchronized(AppCompatResources.b) {
            WeakHashMap weakHashMap0 = AppCompatResources.c;
            SparseArray sparseArray0 = (SparseArray)weakHashMap0.get(context0);
            if(sparseArray0 == null) {
                sparseArray0 = new SparseArray();
                weakHashMap0.put(context0, sparseArray0);
            }
            sparseArray0.append(v, new ColorStateListCacheEntry(colorStateList0, context0.getResources().getConfiguration()));
        }
    }

    public static ColorStateList b(Context context0, int v) {
        ColorStateList colorStateList1;
        if(Build.VERSION.SDK_INT >= 23) {
            return a.g(v, context0);
        }
        Object object0 = AppCompatResources.b;
        __monitor_enter(object0);
        int v1 = FIN.finallyOpen$NT();
        SparseArray sparseArray0 = (SparseArray)AppCompatResources.c.get(context0);
        ColorStateList colorStateList0 = null;
        if(sparseArray0 == null || sparseArray0.size() <= 0) {
        label_16:
            FIN.finallyCodeBegin$NT(v1);
            __monitor_exit(object0);
            FIN.finallyCodeEnd$NT(v1);
            colorStateList1 = null;
        }
        else {
            ColorStateListCacheEntry appCompatResources$ColorStateListCacheEntry0 = (ColorStateListCacheEntry)sparseArray0.get(v);
            if(appCompatResources$ColorStateListCacheEntry0 == null) {
                goto label_16;
            }
            else {
                Configuration configuration0 = context0.getResources().getConfiguration();
                if(appCompatResources$ColorStateListCacheEntry0.a.equals(configuration0)) {
                    colorStateList1 = appCompatResources$ColorStateListCacheEntry0.b;
                    FIN.finallyExec$NT(v1);
                }
                else {
                    sparseArray0.remove(v);
                    goto label_16;
                }
            }
        }
        if(colorStateList1 != null) {
            return colorStateList1;
        }
        Resources resources0 = context0.getResources();
        ThreadLocal threadLocal0 = AppCompatResources.a;
        TypedValue typedValue0 = (TypedValue)threadLocal0.get();
        if(typedValue0 == null) {
            typedValue0 = new TypedValue();
            threadLocal0.set(typedValue0);
        }
        resources0.getValue(v, typedValue0, true);
        if(typedValue0.type < 28 || typedValue0.type > 0x1F) {
            Resources resources1 = context0.getResources();
            XmlResourceParser xmlResourceParser0 = resources1.getXml(v);
            try {
                colorStateList0 = ColorStateListInflaterCompat.createFromXml(resources1, xmlResourceParser0, context0.getTheme());
            }
            catch(Exception unused_ex) {
            }
        }
        if(colorStateList0 != null) {
            AppCompatResources.a(context0, v, colorStateList0);
            return colorStateList0;
        }
        return ContextCompat.getColorStateList(context0, v);
    }

    public static Drawable c(Context context0, int v) {
        return AppCompatDrawableManager.j().l(context0, v);
    }
}


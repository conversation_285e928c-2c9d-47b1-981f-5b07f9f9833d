package android.supportv1.v7.widget;

import android.graphics.Rect;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v7.view.ContextThemeWrapper;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.View.MeasureSpec;
import android.widget.FrameLayout;

public abstract class ContentFrameLayout extends FrameLayout {
    public interface OnAttachListener {
        void onDetachedFromWindow();
    }

    public OnAttachListener a;
    public final Rect b;
    public TypedValue c;
    public TypedValue d;
    public TypedValue e;
    public TypedValue f;
    public TypedValue g;
    public TypedValue h;

    public ContentFrameLayout(ContextThemeWrapper contextThemeWrapper0) {
        super(contextThemeWrapper0, null, 0);
        this.b = new Rect();
    }

    public final void a(int v, int v1, int v2, int v3) {
        this.b.set(v, v1, v2, v3);
        if(ViewCompat.isLaidOut(this)) {
            this.requestLayout();
        }
    }

    public TypedValue getFixedHeightMajor() {
        if(this.c == null) {
            this.c = new TypedValue();
        }
        return this.c;
    }

    public TypedValue getFixedHeightMinor() {
        if(this.d == null) {
            this.d = new TypedValue();
        }
        return this.d;
    }

    public TypedValue getFixedWidthMajor() {
        if(this.e == null) {
            this.e = new TypedValue();
        }
        return this.e;
    }

    public TypedValue getFixedWidthMinor() {
        if(this.f == null) {
            this.f = new TypedValue();
        }
        return this.f;
    }

    public TypedValue getMinWidthMajor() {
        if(this.g == null) {
            this.g = new TypedValue();
        }
        return this.g;
    }

    public TypedValue getMinWidthMinor() {
        if(this.h == null) {
            this.h = new TypedValue();
        }
        return this.h;
    }

    @Override  // android.view.ViewGroup
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override  // android.view.ViewGroup
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        OnAttachListener contentFrameLayout$OnAttachListener0 = this.a;
        if(contentFrameLayout$OnAttachListener0 != null) {
            contentFrameLayout$OnAttachListener0.onDetachedFromWindow();
        }
    }

    @Override  // android.widget.FrameLayout
    public final void onMeasure(int v, int v1) {
        int v10;
        int v6;
        int v7;
        int v4;
        boolean z2;
        int v5;
        DisplayMetrics displayMetrics0 = this.getContext().getResources().getDisplayMetrics();
        boolean z = false;
        boolean z1 = displayMetrics0.widthPixels < displayMetrics0.heightPixels;
        int v2 = View.MeasureSpec.getMode(v);
        int v3 = View.MeasureSpec.getMode(v1);
        Rect rect0 = this.b;
        if(v2 == 0x80000000) {
            TypedValue typedValue0 = z1 ? this.f : this.e;
            if(typedValue0 == null) {
                v5 = v;
                z2 = false;
            }
            else {
                switch(typedValue0.type) {
                    case 0: {
                        v5 = v;
                        z2 = false;
                        break;
                    }
                    case 5: {
                        v4 = (int)typedValue0.getDimension(displayMetrics0);
                        goto label_18;
                    }
                    case 6: {
                        v4 = (int)typedValue0.getFraction(((float)displayMetrics0.widthPixels), ((float)displayMetrics0.widthPixels));
                    label_18:
                        if(v4 > 0) {
                            v5 = View.MeasureSpec.makeMeasureSpec(Math.min(v4 - (rect0.left + rect0.right), View.MeasureSpec.getSize(v)), 0x40000000);
                            z2 = true;
                        }
                        else {
                            v5 = v;
                            z2 = false;
                        }
                        break;
                    }
                    default: {
                        v4 = 0;
                        goto label_18;
                    }
                }
            }
        }
        else {
            v5 = v;
            z2 = false;
        }
        if(v3 == 0x80000000) {
            TypedValue typedValue1 = z1 ? this.c : this.d;
            if(typedValue1 == null) {
                v7 = v1;
            }
            else {
                switch(typedValue1.type) {
                    case 0: {
                        v7 = v1;
                        break;
                    }
                    case 5: {
                        v6 = (int)typedValue1.getDimension(displayMetrics0);
                        v7 = v6 > 0 ? View.MeasureSpec.makeMeasureSpec(Math.min(v6 - (rect0.top + rect0.bottom), View.MeasureSpec.getSize(v1)), 0x40000000) : v1;
                        break;
                    }
                    case 6: {
                        v6 = (int)typedValue1.getFraction(((float)displayMetrics0.heightPixels), ((float)displayMetrics0.heightPixels));
                        v7 = v6 > 0 ? View.MeasureSpec.makeMeasureSpec(Math.min(v6 - (rect0.top + rect0.bottom), View.MeasureSpec.getSize(v1)), 0x40000000) : v1;
                        break;
                    }
                    default: {
                        v6 = 0;
                        v7 = v6 > 0 ? View.MeasureSpec.makeMeasureSpec(Math.min(v6 - (rect0.top + rect0.bottom), View.MeasureSpec.getSize(v1)), 0x40000000) : v1;
                        break;
                    }
                }
            }
        }
        else {
            v7 = v1;
        }
        super.onMeasure(v5, v7);
        int v8 = this.getMeasuredWidth();
        int v9 = View.MeasureSpec.makeMeasureSpec(v8, 0x40000000);
        if(!z2 && v2 == 0x80000000) {
            TypedValue typedValue2 = z1 ? this.h : this.g;
            if(typedValue2 != null) {
                switch(typedValue2.type) {
                    case 0: {
                        break;
                    }
                    case 5: {
                        v10 = (int)typedValue2.getDimension(displayMetrics0);
                        goto label_61;
                    }
                    case 6: {
                        v10 = (int)typedValue2.getFraction(((float)displayMetrics0.widthPixels), ((float)displayMetrics0.widthPixels));
                    label_61:
                        if(v10 > 0) {
                            v10 -= rect0.left + rect0.right;
                        }
                        if(v8 < v10) {
                            v9 = View.MeasureSpec.makeMeasureSpec(v10, 0x40000000);
                            z = true;
                        }
                        break;
                    }
                    default: {
                        v10 = 0;
                        goto label_61;
                    }
                }
            }
        }
        if(z) {
            super.onMeasure(v9, v7);
        }
    }

    public void setAttachListener(OnAttachListener contentFrameLayout$OnAttachListener0) {
        this.a = contentFrameLayout$OnAttachListener0;
    }
}


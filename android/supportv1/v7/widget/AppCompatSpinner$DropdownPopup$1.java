package android.supportv1.v7.widget;

import android.view.View;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView;

class AppCompatSpinner.DropdownPopup.1 implements AdapterView.OnItemClickListener {
    public final DropdownPopup a;

    public AppCompatSpinner.DropdownPopup.1(DropdownPopup appCompatSpinner$DropdownPopup0) {
        this.a = appCompatSpinner$DropdownPopup0;
    }

    @Override  // android.widget.AdapterView$OnItemClickListener
    public final void onItemClick(AdapterView adapterView0, View view0, int v, long v1) {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.a;
        AppCompatSpinner.this.setSelection(v);
        if(AppCompatSpinner.this.getOnItemClickListener() != null) {
            long v2 = appCompatSpinner$DropdownPopup0.z.getItemId(v);
            AppCompatSpinner.this.performItemClick(view0, v, v2);
        }
        appCompatSpinner$DropdownPopup0.b();
    }
}


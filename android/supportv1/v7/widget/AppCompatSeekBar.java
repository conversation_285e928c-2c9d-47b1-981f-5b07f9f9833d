package android.supportv1.v7.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.supportv1.v7.appcompat.R.attr;
import android.util.AttributeSet;
import android.widget.SeekBar;

public class AppCompatSeekBar extends SeekBar {
    public final AppCompatSeekBarHelper a;

    public AppCompatSeekBar(Context context0, AttributeSet attributeSet0) {
        int v = R.attr.seekBarStyle;
        super(context0, attributeSet0, v);
        AppCompatSeekBarHelper appCompatSeekBarHelper0 = new AppCompatSeekBarHelper(this);
        this.a = appCompatSeekBarHelper0;
        appCompatSeekBarHelper0.a(attributeSet0, v);
    }

    @Override  // android.widget.AbsSeekBar
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatSeekBarHelper appCompatSeekBarHelper0 = this.a;
        Drawable drawable0 = appCompatSeekBarHelper0.f;
        if(drawable0 != null && drawable0.isStateful()) {
            SeekBar seekBar0 = appCompatSeekBarHelper0.i;
            if(drawable0.setState(seekBar0.getDrawableState())) {
                seekBar0.invalidateDrawable(drawable0);
            }
        }
    }

    @Override  // android.widget.AbsSeekBar
    public final void jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState();
        Drawable drawable0 = this.a.f;
        if(drawable0 != null) {
            drawable0.jumpToCurrentState();
        }
    }

    @Override  // android.widget.AbsSeekBar
    public final void onDraw(Canvas canvas0) {
        synchronized(this) {
            super.onDraw(canvas0);
            this.a.d(canvas0);
        }
    }
}


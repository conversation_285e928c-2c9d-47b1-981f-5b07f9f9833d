package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.RippleDrawable;
import android.net.Uri;
import android.supportv1.v4.view.TintableBackgroundView;
import android.supportv1.v4.widget.TintableImageSourceView;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.content.res.AppCompatResources;
import android.util.AttributeSet;
import android.widget.ImageButton;
import android.widget.ImageView;

public class AppCompatImageButton extends ImageButton implements TintableBackgroundView, TintableImageSourceView {
    public final AppCompatBackgroundHelper a;
    public final AppCompatImageHelper b;

    public AppCompatImageButton(Context context0, AttributeSet attributeSet0) {
        int v = R.attr.imageButtonStyle;
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = new AppCompatBackgroundHelper(this);
        this.a = appCompatBackgroundHelper0;
        appCompatBackgroundHelper0.d(attributeSet0, v);
        AppCompatImageHelper appCompatImageHelper0 = new AppCompatImageHelper(this);
        this.b = appCompatImageHelper0;
        appCompatImageHelper0.b(attributeSet0, v);
    }

    @Override  // android.widget.ImageView
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            appCompatImageHelper0.a();
        }
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public ColorStateList getSupportBackgroundTintList() {
        return this.a == null ? null : this.a.b();
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.a == null ? null : this.a.c();
    }

    @Override  // android.supportv1.v4.widget.TintableImageSourceView
    public ColorStateList getSupportImageTintList() {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            return appCompatImageHelper0.a == null ? null : appCompatImageHelper0.a.c;
        }
        return null;
    }

    @Override  // android.supportv1.v4.widget.TintableImageSourceView
    public PorterDuff.Mode getSupportImageTintMode() {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            return appCompatImageHelper0.a == null ? null : appCompatImageHelper0.a.d;
        }
        return null;
    }

    // 去混淆评级： 低(20)
    @Override  // android.widget.ImageView
    public final boolean hasOverlappingRendering() {
        return !(this.b.c.getBackground() instanceof RippleDrawable) != 0 && super.hasOverlappingRendering();
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.ImageView
    public void setImageBitmap(Bitmap bitmap0) {
        super.setImageBitmap(bitmap0);
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            appCompatImageHelper0.a();
        }
    }

    @Override  // android.widget.ImageView
    public void setImageDrawable(Drawable drawable0) {
        super.setImageDrawable(drawable0);
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            appCompatImageHelper0.a();
        }
    }

    @Override  // android.widget.ImageView
    public void setImageResource(int v) {
        Drawable drawable0;
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        ImageView imageView0 = appCompatImageHelper0.c;
        if(v == 0) {
            drawable0 = null;
        }
        else {
            drawable0 = AppCompatResources.c(imageView0.getContext(), v);
            if(drawable0 != null) {
                DrawableUtils.b(drawable0);
            }
        }
        imageView0.setImageDrawable(drawable0);
        appCompatImageHelper0.a();
    }

    @Override  // android.widget.ImageView
    public void setImageURI(Uri uri0) {
        super.setImageURI(uri0);
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            appCompatImageHelper0.a();
        }
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }

    @Override  // android.supportv1.v4.widget.TintableImageSourceView
    public void setSupportImageTintList(ColorStateList colorStateList0) {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            if(appCompatImageHelper0.a == null) {
                appCompatImageHelper0.a = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
            }
            appCompatImageHelper0.a.c = colorStateList0;
            appCompatImageHelper0.a.a = true;
            appCompatImageHelper0.a();
        }
    }

    @Override  // android.supportv1.v4.widget.TintableImageSourceView
    public void setSupportImageTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatImageHelper appCompatImageHelper0 = this.b;
        if(appCompatImageHelper0 != null) {
            if(appCompatImageHelper0.a == null) {
                appCompatImageHelper0.a = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
            }
            appCompatImageHelper0.a.d = porterDuff$Mode0;
            appCompatImageHelper0.a.b = true;
            appCompatImageHelper0.a();
        }
    }
}


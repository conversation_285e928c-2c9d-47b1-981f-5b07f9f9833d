package android.supportv1.v7.widget;

import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.supportv1.v4.widget.CompoundButtonCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.content.res.AppCompatResources;
import android.util.AttributeSet;
import android.widget.CompoundButton;

class AppCompatCompoundButtonHelper {
    public ColorStateList a;
    public PorterDuff.Mode b;
    public boolean c;
    public boolean d;
    public boolean e;
    public final CompoundButton f;

    public AppCompatCompoundButtonHelper(CompoundButton compoundButton0) {
        this.a = null;
        this.b = null;
        this.c = false;
        this.d = false;
        this.f = compoundButton0;
    }

    public final void a() {
        CompoundButton compoundButton0 = this.f;
        Drawable drawable0 = CompoundButtonCompat.getButtonDrawable(compoundButton0);
        if(drawable0 != null && (this.c || this.d)) {
            Drawable drawable1 = DrawableCompat.wrap(drawable0).mutate();
            if(this.c) {
                DrawableCompat.setTintList(drawable1, this.a);
            }
            if(this.d) {
                DrawableCompat.setTintMode(drawable1, this.b);
            }
            if(drawable1.isStateful()) {
                drawable1.setState(compoundButton0.getDrawableState());
            }
            compoundButton0.setButtonDrawable(drawable1);
        }
    }

    public final void b(AttributeSet attributeSet0, int v) {
        TypedArray typedArray0 = this.f.getContext().obtainStyledAttributes(attributeSet0, R.styleable.CompoundButton, v, 0);
        try {
            if(typedArray0.hasValue(R.styleable.CompoundButton_android_button)) {
                int v2 = typedArray0.getResourceId(R.styleable.CompoundButton_android_button, 0);
                if(v2 != 0) {
                    Drawable drawable0 = AppCompatResources.c(this.f.getContext(), v2);
                    this.f.setButtonDrawable(drawable0);
                }
            }
            if(typedArray0.hasValue(R.styleable.CompoundButton_buttonTint)) {
                ColorStateList colorStateList0 = typedArray0.getColorStateList(R.styleable.CompoundButton_buttonTint);
                CompoundButtonCompat.setButtonTintList(this.f, colorStateList0);
            }
            if(typedArray0.hasValue(R.styleable.CompoundButton_buttonTintMode)) {
                PorterDuff.Mode porterDuff$Mode0 = DrawableUtils.c(typedArray0.getInt(R.styleable.CompoundButton_buttonTintMode, -1), null);
                CompoundButtonCompat.setButtonTintMode(this.f, porterDuff$Mode0);
            }
        }
        finally {
            typedArray0.recycle();
        }
    }
}


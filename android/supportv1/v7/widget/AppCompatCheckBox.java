package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.supportv1.v4.widget.TintableCompoundButton;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.content.res.AppCompatResources;
import android.util.AttributeSet;
import android.widget.CheckBox;

public class AppCompatCheckBox extends CheckBox implements TintableCompoundButton {
    public final AppCompatCompoundButtonHelper a;

    public AppCompatCheckBox(Context context0, AttributeSet attributeSet0) {
        int v = R.attr.checkboxStyle;
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, v);
        AppCompatCompoundButtonHelper appCompatCompoundButtonHelper0 = new AppCompatCompoundButtonHelper(this);
        this.a = appCompatCompoundButtonHelper0;
        appCompatCompoundButtonHelper0.b(attributeSet0, v);
    }

    @Override  // android.widget.CompoundButton
    public int getCompoundPaddingLeft() {
        return super.getCompoundPaddingLeft();
    }

    @Override  // android.supportv1.v4.widget.TintableCompoundButton
    public ColorStateList getSupportButtonTintList() {
        return this.a == null ? null : this.a.a;
    }

    @Override  // android.supportv1.v4.widget.TintableCompoundButton
    public PorterDuff.Mode getSupportButtonTintMode() {
        return this.a == null ? null : this.a.b;
    }

    @Override  // android.widget.CompoundButton
    public void setButtonDrawable(int v) {
        this.setButtonDrawable(AppCompatResources.c(this.getContext(), v));
    }

    @Override  // android.widget.CompoundButton
    public void setButtonDrawable(Drawable drawable0) {
        super.setButtonDrawable(drawable0);
        AppCompatCompoundButtonHelper appCompatCompoundButtonHelper0 = this.a;
        if(appCompatCompoundButtonHelper0 != null) {
            if(appCompatCompoundButtonHelper0.e) {
                appCompatCompoundButtonHelper0.e = false;
                return;
            }
            appCompatCompoundButtonHelper0.e = true;
            appCompatCompoundButtonHelper0.a();
        }
    }

    @Override  // android.supportv1.v4.widget.TintableCompoundButton
    public void setSupportButtonTintList(ColorStateList colorStateList0) {
        AppCompatCompoundButtonHelper appCompatCompoundButtonHelper0 = this.a;
        if(appCompatCompoundButtonHelper0 != null) {
            appCompatCompoundButtonHelper0.a = colorStateList0;
            appCompatCompoundButtonHelper0.c = true;
            appCompatCompoundButtonHelper0.a();
        }
    }

    @Override  // android.supportv1.v4.widget.TintableCompoundButton
    public void setSupportButtonTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatCompoundButtonHelper appCompatCompoundButtonHelper0 = this.a;
        if(appCompatCompoundButtonHelper0 != null) {
            appCompatCompoundButtonHelper0.b = porterDuff$Mode0;
            appCompatCompoundButtonHelper0.d = true;
            appCompatCompoundButtonHelper0.a();
        }
    }
}


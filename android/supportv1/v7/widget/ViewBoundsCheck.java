package android.supportv1.v7.widget;

import android.view.View;

class ViewBoundsCheck {
    static class BoundFlags {
        public int a;
        public int b;
        public int c;
        public int d;
        public int e;

        public final boolean a() {
            int v10;
            int v7;
            int v4;
            int v = this.a;
            int v1 = 2;
            if((v & 7) != 0) {
                int v2 = this.c;
                int v3 = this.e;
                if(v2 > v3) {
                    v4 = 1;
                }
                else {
                    v4 = v2 == v3 ? 2 : 4;
                }
                if((v4 & v) == 0) {
                    return false;
                }
            }
            if((v & 0x70) != 0) {
                int v5 = this.c;
                int v6 = this.d;
                if(v5 > v6) {
                    v7 = 1;
                }
                else {
                    v7 = v5 == v6 ? 2 : 4;
                }
                if((v7 << 4 & v) == 0) {
                    return false;
                }
            }
            if((v & 0x700) != 0) {
                int v8 = this.b;
                int v9 = this.e;
                if(v8 > v9) {
                    v10 = 1;
                }
                else {
                    v10 = v8 == v9 ? 2 : 4;
                }
                if((v10 << 8 & v) == 0) {
                    return false;
                }
            }
            if((v & 0x7000) != 0) {
                int v11 = this.b;
                int v12 = this.d;
                if(v11 > v12) {
                    return (v & 0x1000) != 0;
                }
                if(v11 != v12) {
                    v1 = 4;
                }
                return (v & v1 << 12) != 0;
            }
            return true;
        }
    }

    interface Callback {
        int a(View arg1);

        int b();

        int c();

        View d(int arg1);

        int e(View arg1);
    }

    public final BoundFlags a;
    public final Callback b;

    public ViewBoundsCheck(Callback viewBoundsCheck$Callback0) {
        this.b = viewBoundsCheck$Callback0;
        BoundFlags viewBoundsCheck$BoundFlags0 = new BoundFlags();  // 初始化器: Ljava/lang/Object;-><init>()V
        viewBoundsCheck$BoundFlags0.a = 0;
        this.a = viewBoundsCheck$BoundFlags0;
    }

    public final View a(int v, int v1, int v2, int v3) {
        Callback viewBoundsCheck$Callback0 = this.b;
        int v4 = viewBoundsCheck$Callback0.b();
        int v5 = viewBoundsCheck$Callback0.c();
        int v6 = v1 <= v ? -1 : 1;
        View view0 = null;
        while(v != v1) {
            View view1 = viewBoundsCheck$Callback0.d(v);
            int v7 = viewBoundsCheck$Callback0.a(view1);
            int v8 = viewBoundsCheck$Callback0.e(view1);
            BoundFlags viewBoundsCheck$BoundFlags0 = this.a;
            viewBoundsCheck$BoundFlags0.e = v4;
            viewBoundsCheck$BoundFlags0.d = v5;
            viewBoundsCheck$BoundFlags0.c = v7;
            viewBoundsCheck$BoundFlags0.b = v8;
            if(v2 != 0) {
                viewBoundsCheck$BoundFlags0.a = v2;
                if(viewBoundsCheck$BoundFlags0.a()) {
                    return view1;
                }
            }
            if(v3 != 0) {
                viewBoundsCheck$BoundFlags0.a = v3;
                if(viewBoundsCheck$BoundFlags0.a()) {
                    view0 = view1;
                }
            }
            v += v6;
        }
        return view0;
    }

    public final boolean b(View view0) {
        int v = this.b.b();
        int v1 = this.b.c();
        int v2 = this.b.a(view0);
        int v3 = this.b.e(view0);
        this.a.e = v;
        this.a.d = v1;
        this.a.c = v2;
        this.a.b = v3;
        this.a.a = 0x6003;
        return this.a.a();
    }
}


package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.util.AttributeSet;
import android.view.View;

class AppCompatBackgroundHelper {
    public int a;
    public TintInfo b;
    public final AppCompatDrawableManager c;
    public TintInfo d;
    public TintInfo e;
    public final View f;

    public AppCompatBackgroundHelper(View view0) {
        this.a = -1;
        this.f = view0;
        this.c = AppCompatDrawableManager.j();
    }

    public final void a() {
        View view0 = this.f;
        Drawable drawable0 = view0.getBackground();
        if(drawable0 != null) {
            int v = Build.VERSION.SDK_INT;
            if(v <= 21) {
                if(v == 21) {
                label_8:
                    if(this.e == null) {
                        this.e = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
                    }
                    TintInfo tintInfo0 = this.e;
                    tintInfo0.c = null;
                    tintInfo0.a = false;
                    tintInfo0.d = null;
                    tintInfo0.b = false;
                    ColorStateList colorStateList0 = ViewCompat.getBackgroundTintList(view0);
                    if(colorStateList0 != null) {
                        tintInfo0.a = true;
                        tintInfo0.c = colorStateList0;
                    }
                    PorterDuff.Mode porterDuff$Mode0 = ViewCompat.getBackgroundTintMode(view0);
                    if(porterDuff$Mode0 != null) {
                        tintInfo0.b = true;
                        tintInfo0.d = porterDuff$Mode0;
                    }
                    if(tintInfo0.a || tintInfo0.b) {
                        AppCompatDrawableManager.s(drawable0, tintInfo0, view0.getDrawableState());
                        return;
                    }
                }
            }
            else if(this.d != null) {
                goto label_8;
            }
            TintInfo tintInfo1 = this.b;
            if(tintInfo1 != null) {
                AppCompatDrawableManager.s(drawable0, tintInfo1, view0.getDrawableState());
                return;
            }
            TintInfo tintInfo2 = this.d;
            if(tintInfo2 != null) {
                AppCompatDrawableManager.s(drawable0, tintInfo2, view0.getDrawableState());
            }
        }
    }

    public final ColorStateList b() {
        return this.b == null ? null : this.b.c;
    }

    public final PorterDuff.Mode c() {
        return this.b == null ? null : this.b.d;
    }

    public final void d(AttributeSet attributeSet0, int v) {
        TintTypedArray tintTypedArray0 = TintTypedArray.o(this.f.getContext(), attributeSet0, R.styleable.ViewBackgroundHelper, v, 0);
        try {
            if(tintTypedArray0.l(R.styleable.ViewBackgroundHelper_android_background)) {
                this.a = tintTypedArray0.i(R.styleable.ViewBackgroundHelper_android_background, -1);
                Context context0 = this.f.getContext();
                ColorStateList colorStateList0 = this.c.o(this.a, context0);
                if(colorStateList0 != null) {
                    this.g(colorStateList0);
                }
            }
            if(tintTypedArray0.l(R.styleable.ViewBackgroundHelper_backgroundTint)) {
                ColorStateList colorStateList1 = tintTypedArray0.b(R.styleable.ViewBackgroundHelper_backgroundTint);
                ViewCompat.setBackgroundTintList(this.f, colorStateList1);
            }
            if(tintTypedArray0.l(R.styleable.ViewBackgroundHelper_backgroundTintMode)) {
                PorterDuff.Mode porterDuff$Mode0 = DrawableUtils.c(tintTypedArray0.g(R.styleable.ViewBackgroundHelper_backgroundTintMode, -1), null);
                ViewCompat.setBackgroundTintMode(this.f, porterDuff$Mode0);
            }
        }
        finally {
            tintTypedArray0.p();
        }
    }

    public final void e() {
        this.a = -1;
        this.g(null);
        this.a();
    }

    public final void f(int v) {
        this.a = v;
        this.g((this.c == null ? null : this.c.o(v, this.f.getContext())));
        this.a();
    }

    public final void g(ColorStateList colorStateList0) {
        if(colorStateList0 == null) {
            this.d = null;
        }
        else {
            if(this.d == null) {
                this.d = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
            }
            this.d.c = colorStateList0;
            this.d.a = true;
        }
        this.a();
    }

    public final void h(ColorStateList colorStateList0) {
        if(this.b == null) {
            this.b = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        this.b.c = colorStateList0;
        this.b.a = true;
        this.a();
    }

    public final void i(PorterDuff.Mode porterDuff$Mode0) {
        if(this.b == null) {
            this.b = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
        }
        this.b.d = porterDuff$Mode0;
        this.b.b = true;
        this.a();
    }
}


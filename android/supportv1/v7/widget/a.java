package android.supportv1.v7.widget;

import android.adservices.topics.TopicsManager;
import android.app.Activity;
import android.app.NotificationManager;
import android.content.Context;
import android.media.AudioDeviceInfo;
import android.media.AudioManager;
import android.media.MediaDrmResetException;
import android.security.keystore.KeyGenParameterSpec.Builder;

public abstract class a {
    public static KeyGenParameterSpec.Builder A() {
        return new KeyGenParameterSpec.Builder("authorisation_key", 3);
    }

    public static Object D(Context context0) {
        return context0.getSystemService(NotificationManager.class);
    }

    public static int a(Context context0) {
        return context0.checkSelfPermission("android.permission.ACCESS_MEDIA_LOCATION");
    }

    public static KeyGenParameterSpec.Builder g() {
        return new KeyGenParameterSpec.Builder("key2", 4);
    }

    public static KeyGenParameterSpec.Builder h(KeyGenParameterSpec.Builder keyGenParameterSpec$Builder0) {
        return keyGenParameterSpec$Builder0.setUserAuthenticationRequired(true);
    }

    public static Object p(Context context0) {
        return context0.getSystemService(TopicsManager.class);
    }

    public static void q() {
    }

    public static void r(Activity activity0, String[] arr_s) {
        activity0.requestPermissions(arr_s, 0x3039);
    }

    public static boolean x(Throwable throwable0) {
        return throwable0 instanceof MediaDrmResetException;
    }

    public static AudioDeviceInfo[] y(AudioManager audioManager0) {
        return audioManager0.getDevices(2);
    }
}


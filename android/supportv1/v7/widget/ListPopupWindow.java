package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.widget.PopupWindowCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.view.menu.ShowableListMenu;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View.OnTouchListener;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView.OnScrollListener;
import android.widget.AbsListView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.ListAdapter;
import android.widget.PopupWindow.OnDismissListener;
import android.widget.PopupWindow;
import java.lang.reflect.Method;

public abstract class ListPopupWindow implements ShowableListMenu {
    class ListSelectorHider implements Runnable {
        public final ListPopupWindow a;

        @Override
        public final void run() {
            DropDownListView dropDownListView0 = this.a.g;
            if(dropDownListView0 != null) {
                dropDownListView0.c(true);
                dropDownListView0.requestLayout();
            }
        }
    }

    class PopupDataSetObserver extends DataSetObserver {
        public final ListPopupWindow a;

        @Override  // android.database.DataSetObserver
        public final void onChanged() {
            ListPopupWindow listPopupWindow0 = ListPopupWindow.this;
            if(listPopupWindow0.r.isShowing()) {
                listPopupWindow0.j();
            }
        }

        @Override  // android.database.DataSetObserver
        public final void onInvalidated() {
            ListPopupWindow.this.b();
        }
    }

    class PopupScrollListener implements AbsListView.OnScrollListener {
        public final ListPopupWindow a;

        @Override  // android.widget.AbsListView$OnScrollListener
        public final void onScroll(AbsListView absListView0, int v, int v1, int v2) {
        }

        @Override  // android.widget.AbsListView$OnScrollListener
        public final void onScrollStateChanged(AbsListView absListView0, int v) {
            if(v == 1) {
                ListPopupWindow listPopupWindow0 = this.a;
                if(listPopupWindow0.r.getInputMethodMode() != 2 && listPopupWindow0.r.getContentView() != null) {
                    listPopupWindow0.l.removeCallbacks(listPopupWindow0.s);
                    listPopupWindow0.s.run();
                }
            }
        }
    }

    class PopupTouchInterceptor implements View.OnTouchListener {
        public final ListPopupWindow a;

        @Override  // android.view.View$OnTouchListener
        public final boolean onTouch(View view0, MotionEvent motionEvent0) {
            int v = motionEvent0.getAction();
            int v1 = (int)motionEvent0.getX();
            int v2 = (int)motionEvent0.getY();
            ListPopupWindow listPopupWindow0 = this.a;
            if(v == 0 && (listPopupWindow0.r != null && listPopupWindow0.r.isShowing() && v1 >= 0 && v1 < listPopupWindow0.r.getWidth() && v2 >= 0 && v2 < listPopupWindow0.r.getHeight())) {
                listPopupWindow0.l.postDelayed(listPopupWindow0.s, 0xFAL);
                return false;
            }
            if(v == 1) {
                listPopupWindow0.l.removeCallbacks(listPopupWindow0.s);
            }
            return false;
        }
    }

    class ResizePopupRunnable implements Runnable {
        public final ListPopupWindow a;

        @Override
        public final void run() {
            ListPopupWindow listPopupWindow0 = this.a;
            if(listPopupWindow0.g != null && ViewCompat.isAttachedToWindow(listPopupWindow0.g) && listPopupWindow0.g.getCount() > listPopupWindow0.g.getChildCount() && listPopupWindow0.g.getChildCount() <= listPopupWindow0.o) {
                listPopupWindow0.r.setInputMethodMode(2);
                listPopupWindow0.j();
            }
        }
    }

    public ListAdapter a;
    public final Context b;
    public View c;
    public final int d;
    public final int e;
    public int f;
    public DropDownListView g;
    public int h;
    public boolean i;
    public int j;
    public final int k;
    public final Handler l;
    public final ListSelectorHider m;
    public AdapterView.OnItemClickListener n;
    public final int o;
    public boolean p;
    public DataSetObserver q;
    public final PopupWindow r;
    public final ResizePopupRunnable s;
    public final PopupScrollListener t;
    public final Rect u;
    public final PopupTouchInterceptor v;
    public static final Method w;
    public static final Method x;
    public static final Method y;

    static {
        Class class0;
        try {
            class0 = PopupWindow.class;
            ListPopupWindow.w = class0.getDeclaredMethod("setClipToScreenEnabled", Boolean.TYPE);
        }
        catch(NoSuchMethodException unused_ex) {
        }
        try {
            ListPopupWindow.x = class0.getDeclaredMethod("getMaxAvailableHeight", View.class, Integer.TYPE, Boolean.TYPE);
        }
        catch(NoSuchMethodException unused_ex) {
        }
        try {
            ListPopupWindow.y = class0.getDeclaredMethod("setEpicenterBounds", Rect.class);
        }
        catch(NoSuchMethodException unused_ex) {
        }
    }

    public ListPopupWindow(Context context0, AttributeSet attributeSet0, int v, int v1) {
        this.e = -2;
        this.j = -2;
        this.k = 1002;
        this.d = 0;
        this.o = 0x7FFFFFFF;
        this.s = new ResizePopupRunnable(this);
        this.v = new PopupTouchInterceptor(this);
        this.t = new PopupScrollListener(this);
        this.m = new ListSelectorHider(this);
        this.u = new Rect();
        this.b = context0;
        this.l = new Handler(context0.getMainLooper());
        TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.ListPopupWindow, v, v1);
        this.f = typedArray0.getDimensionPixelOffset(R.styleable.ListPopupWindow_android_dropDownHorizontalOffset, 0);
        int v2 = typedArray0.getDimensionPixelOffset(R.styleable.ListPopupWindow_android_dropDownVerticalOffset, 0);
        this.h = v2;
        if(v2 != 0) {
            this.i = true;
        }
        typedArray0.recycle();
        AppCompatPopupWindow appCompatPopupWindow0 = new AppCompatPopupWindow(context0, attributeSet0, v, v1);
        this.r = appCompatPopupWindow0;
        appCompatPopupWindow0.setInputMethodMode(1);
    }

    public DropDownListView a(Context context0, boolean z) {
        return new DropDownListView(context0, z);
    }

    public final void b() {
        this.r.dismiss();
        this.r.setContentView(null);
        this.g = null;
        this.l.removeCallbacks(this.s);
    }

    public void c(ListAdapter listAdapter0) {
        DataSetObserver dataSetObserver0 = this.q;
        if(dataSetObserver0 == null) {
            this.q = new PopupDataSetObserver(this);
        }
        else {
            ListAdapter listAdapter1 = this.a;
            if(listAdapter1 != null) {
                listAdapter1.unregisterDataSetObserver(dataSetObserver0);
            }
        }
        this.a = listAdapter0;
        if(listAdapter0 != null) {
            listAdapter0.registerDataSetObserver(this.q);
        }
        DropDownListView dropDownListView0 = this.g;
        if(dropDownListView0 != null) {
            dropDownListView0.setAdapter(this.a);
        }
    }

    public final void d(Drawable drawable0) {
        this.r.setBackgroundDrawable(drawable0);
    }

    public final void e(int v) {
        Drawable drawable0 = this.r.getBackground();
        if(drawable0 != null) {
            drawable0.getPadding(this.u);
            this.j = this.u.left + this.u.right + v;
            return;
        }
        this.j = v;
    }

    public final void f() {
        this.r.setInputMethodMode(2);
    }

    public final void g() {
        this.p = true;
        this.r.setFocusable(true);
    }

    public final void h(PopupWindow.OnDismissListener popupWindow$OnDismissListener0) {
        this.r.setOnDismissListener(popupWindow$OnDismissListener0);
    }

    public final void i(int v) {
        this.h = v;
        this.i = true;
    }

    public void j() {
        int v11;
        int v8;
        int v6;
        int v4;
        int v2;
        PopupWindow popupWindow0 = this.r;
        Context context0 = this.b;
        if(this.g == null) {
            DropDownListView dropDownListView0 = this.a(context0, !this.p);
            this.g = dropDownListView0;
            dropDownListView0.setAdapter(this.a);
            this.g.setOnItemClickListener(this.n);
            this.g.setFocusable(true);
            this.g.setFocusableInTouchMode(true);
            this.g.setOnItemSelectedListener(new ListPopupWindow.3(this));
            this.g.setOnScrollListener(this.t);
            popupWindow0.setContentView(this.g);
        }
        else {
            ViewGroup viewGroup0 = (ViewGroup)popupWindow0.getContentView();
        }
        Drawable drawable0 = popupWindow0.getBackground();
        int v = 0;
        Rect rect0 = this.u;
        if(drawable0 == null) {
            rect0.setEmpty();
            v2 = 0;
        }
        else {
            drawable0.getPadding(rect0);
            int v1 = rect0.top;
            v2 = rect0.bottom + v1;
            if(!this.i) {
                this.h = -v1;
            }
        }
        boolean z = popupWindow0.getInputMethodMode() == 2;
        View view0 = this.c;
        int v3 = this.h;
        Method method0 = ListPopupWindow.x;
        if(method0 == null) {
        label_33:
            v4 = popupWindow0.getMaxAvailableHeight(view0, v3);
        }
        else {
            try {
                v4 = (int)(((Integer)method0.invoke(popupWindow0, view0, v3, Boolean.valueOf(z))));
                goto label_34;
            }
            catch(Exception unused_ex) {
            }
            goto label_33;
        }
    label_34:
        int v5 = this.e;
        if(v5 == -1) {
            v6 = v4 + v2;
        }
        else {
            int v7 = this.j;
            switch(v7) {
                case -2: {
                    v8 = View.MeasureSpec.makeMeasureSpec(context0.getResources().getDisplayMetrics().widthPixels - (rect0.left + rect0.right), 0x80000000);
                    break;
                }
                case -1: {
                    v8 = View.MeasureSpec.makeMeasureSpec(context0.getResources().getDisplayMetrics().widthPixels - (rect0.left + rect0.right), 0x40000000);
                    break;
                }
                default: {
                    v8 = View.MeasureSpec.makeMeasureSpec(v7, 0x40000000);
                }
            }
            int v9 = this.g.a(v8, v4);
            if(v9 > 0) {
                int v10 = this.g.getPaddingTop();
                v11 = this.g.getPaddingBottom() + v10 + v2;
            }
            else {
                v11 = 0;
            }
            v6 = v9 + v11;
        }
        boolean z1 = this.r.getInputMethodMode() == 2;
        PopupWindowCompat.setWindowLayoutType(popupWindow0, this.k);
        if(popupWindow0.isShowing()) {
            if(!ViewCompat.isAttachedToWindow(this.c)) {
                return;
            }
            int v12 = this.j;
            if(v12 == -1) {
                v12 = -1;
            }
            else if(v12 == -2) {
                v12 = this.c.getWidth();
            }
            if(v5 == -1) {
                v5 = z1 ? v6 : -1;
                int v13 = this.j;
                if(z1) {
                    popupWindow0.setWidth((v13 == -1 ? -1 : 0));
                    popupWindow0.setHeight(0);
                }
                else {
                    if(v13 == -1) {
                        v = -1;
                    }
                    popupWindow0.setWidth(v);
                    popupWindow0.setHeight(-1);
                }
            }
            else if(v5 == -2) {
                v5 = v6;
            }
            popupWindow0.setOutsideTouchable(true);
            View view1 = this.c;
            int v14 = this.f;
            int v15 = this.h;
            if(v12 < 0) {
                v12 = -1;
            }
            popupWindow0.update(view1, v14, v15, v12, (v5 >= 0 ? v5 : -1));
            return;
        }
        int v16 = this.j;
        if(v16 == -1) {
            v16 = -1;
        }
        else if(v16 == -2) {
            v16 = this.c.getWidth();
        }
        if(v5 == -1) {
            v5 = -1;
        }
        else if(v5 == -2) {
            v5 = v6;
        }
        popupWindow0.setWidth(v16);
        popupWindow0.setHeight(v5);
        Method method1 = ListPopupWindow.w;
        if(method1 != null) {
            try {
                method1.invoke(popupWindow0, Boolean.TRUE);
            }
            catch(Exception unused_ex) {
            }
        }
        popupWindow0.setOutsideTouchable(true);
        popupWindow0.setTouchInterceptor(this.v);
        Method method2 = ListPopupWindow.y;
        if(method2 != null) {
            try {
                method2.invoke(popupWindow0, ((Object)null));
            }
            catch(Exception unused_ex) {
            }
        }
        PopupWindowCompat.showAsDropDown(popupWindow0, this.c, this.f, this.h, this.d);
        this.g.setSelection(-1);
        if(!this.p || this.g.isInTouchMode()) {
            DropDownListView dropDownListView1 = this.g;
            if(dropDownListView1 != null) {
                dropDownListView1.c(true);
                dropDownListView1.requestLayout();
            }
        }
        if(!this.p) {
            this.l.post(this.m);
        }
    }
}


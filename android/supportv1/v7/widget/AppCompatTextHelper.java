package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources.NotFoundException;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.supportv1.v4.view.c;
import android.supportv1.v4.widget.AutoSizeableTextView;
import android.supportv1.v4.widget.TextViewCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.text.method.PasswordTransformationMethod;
import android.text.method.TransformationMethod;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.widget.TextView;
import java.lang.ref.WeakReference;
import java.util.Arrays;

class AppCompatTextHelper {
    public boolean a;
    public final AppCompatTextViewAutoSizeHelper b;
    public TintInfo c;
    public TintInfo d;
    public TintInfo e;
    public TintInfo f;
    public TintInfo g;
    public TintInfo h;
    public Typeface i;
    public int j;
    public final TextView k;

    public AppCompatTextHelper(TextView textView0) {
        this.j = 0;
        this.k = textView0;
        this.b = new AppCompatTextViewAutoSizeHelper(textView0);
    }

    public final void a(Drawable drawable0, TintInfo tintInfo0) {
        if(drawable0 != null && tintInfo0 != null) {
            AppCompatDrawableManager.s(drawable0, tintInfo0, this.k.getDrawableState());
        }
    }

    public final void b() {
        TextView textView0 = this.k;
        if(this.e != null || this.h != null || this.f != null || this.c != null) {
            Drawable[] arr_drawable = textView0.getCompoundDrawables();
            this.a(arr_drawable[0], this.e);
            this.a(arr_drawable[1], this.h);
            this.a(arr_drawable[2], this.f);
            this.a(arr_drawable[3], this.c);
        }
        if(this.g != null || this.d != null) {
            Drawable[] arr_drawable1 = textView0.getCompoundDrawablesRelative();
            this.a(arr_drawable1[0], this.g);
            this.a(arr_drawable1[2], this.d);
        }
    }

    public static TintInfo c(Context context0, AppCompatDrawableManager appCompatDrawableManager0, int v) {
        ColorStateList colorStateList0 = appCompatDrawableManager0.o(v, context0);
        if(colorStateList0 != null) {
            TintInfo tintInfo0 = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
            tintInfo0.a = true;
            tintInfo0.c = colorStateList0;
            return tintInfo0;
        }
        return null;
    }

    public final void d(AttributeSet attributeSet0, int v) {
        ColorStateList colorStateList3;
        ColorStateList colorStateList2;
        boolean z1;
        boolean z;
        Context context0 = this.k.getContext();
        AppCompatDrawableManager appCompatDrawableManager0 = AppCompatDrawableManager.j();
        TintTypedArray tintTypedArray0 = TintTypedArray.o(context0, attributeSet0, R.styleable.AppCompatTextHelper, v, 0);
        int v1 = tintTypedArray0.i(R.styleable.AppCompatTextHelper_android_textAppearance, -1);
        if(tintTypedArray0.l(R.styleable.AppCompatTextHelper_android_drawableLeft)) {
            this.e = AppCompatTextHelper.c(context0, appCompatDrawableManager0, tintTypedArray0.i(R.styleable.AppCompatTextHelper_android_drawableLeft, 0));
        }
        if(tintTypedArray0.l(R.styleable.AppCompatTextHelper_android_drawableTop)) {
            this.h = AppCompatTextHelper.c(context0, appCompatDrawableManager0, tintTypedArray0.i(R.styleable.AppCompatTextHelper_android_drawableTop, 0));
        }
        if(tintTypedArray0.l(R.styleable.AppCompatTextHelper_android_drawableRight)) {
            this.f = AppCompatTextHelper.c(context0, appCompatDrawableManager0, tintTypedArray0.i(R.styleable.AppCompatTextHelper_android_drawableRight, 0));
        }
        if(tintTypedArray0.l(R.styleable.AppCompatTextHelper_android_drawableBottom)) {
            this.c = AppCompatTextHelper.c(context0, appCompatDrawableManager0, tintTypedArray0.i(R.styleable.AppCompatTextHelper_android_drawableBottom, 0));
        }
        int v2 = Build.VERSION.SDK_INT;
        if(tintTypedArray0.l(R.styleable.AppCompatTextHelper_android_drawableStart)) {
            this.g = AppCompatTextHelper.c(context0, appCompatDrawableManager0, tintTypedArray0.i(R.styleable.AppCompatTextHelper_android_drawableStart, 0));
        }
        if(tintTypedArray0.l(R.styleable.AppCompatTextHelper_android_drawableEnd)) {
            this.d = AppCompatTextHelper.c(context0, appCompatDrawableManager0, tintTypedArray0.i(R.styleable.AppCompatTextHelper_android_drawableEnd, 0));
        }
        tintTypedArray0.p();
        TransformationMethod transformationMethod0 = this.k.getTransformationMethod();
        ColorStateList colorStateList0 = null;
        if(v1 == -1) {
            colorStateList3 = null;
            colorStateList2 = null;
            z = false;
            z1 = false;
        }
        else {
            TintTypedArray tintTypedArray1 = TintTypedArray.m(context0, v1, R.styleable.TextAppearance);
            if(transformationMethod0 instanceof PasswordTransformationMethod || !tintTypedArray1.l(R.styleable.TextAppearance_textAllCaps)) {
                z = false;
                z1 = false;
            }
            else {
                z = tintTypedArray1.a(R.styleable.TextAppearance_textAllCaps);
                z1 = true;
            }
            this.i(context0, tintTypedArray1);
            if(v2 < 23) {
                ColorStateList colorStateList1 = tintTypedArray1.l(R.styleable.TextAppearance_android_textColor) ? tintTypedArray1.b(R.styleable.TextAppearance_android_textColor) : null;
                colorStateList2 = tintTypedArray1.l(R.styleable.TextAppearance_android_textColorHint) ? tintTypedArray1.b(R.styleable.TextAppearance_android_textColorHint) : null;
                if(tintTypedArray1.l(R.styleable.TextAppearance_android_textColorLink)) {
                    colorStateList0 = tintTypedArray1.b(R.styleable.TextAppearance_android_textColorLink);
                }
                colorStateList3 = colorStateList0;
                colorStateList0 = colorStateList1;
            }
            else {
                colorStateList3 = null;
                colorStateList2 = null;
            }
            tintTypedArray1.p();
        }
        TintTypedArray tintTypedArray2 = TintTypedArray.o(context0, attributeSet0, R.styleable.TextAppearance, v, 0);
        if(!(transformationMethod0 instanceof PasswordTransformationMethod) && tintTypedArray2.l(R.styleable.TextAppearance_textAllCaps)) {
            z = tintTypedArray2.a(R.styleable.TextAppearance_textAllCaps);
            z1 = true;
        }
        if(v2 < 23) {
            if(tintTypedArray2.l(R.styleable.TextAppearance_android_textColor)) {
                colorStateList0 = tintTypedArray2.b(R.styleable.TextAppearance_android_textColor);
            }
            if(tintTypedArray2.l(R.styleable.TextAppearance_android_textColorHint)) {
                colorStateList2 = tintTypedArray2.b(R.styleable.TextAppearance_android_textColorHint);
            }
            if(tintTypedArray2.l(R.styleable.TextAppearance_android_textColorLink)) {
                colorStateList3 = tintTypedArray2.b(R.styleable.TextAppearance_android_textColorLink);
            }
        }
        if(v2 >= 28 && tintTypedArray2.l(R.styleable.TextAppearance_android_textSize) && tintTypedArray2.c(R.styleable.TextAppearance_android_textSize) == 0) {
            this.k.setTextSize(0, 0.0f);
        }
        this.i(context0, tintTypedArray2);
        tintTypedArray2.p();
        if(colorStateList0 != null) {
            this.k.setTextColor(colorStateList0);
        }
        if(colorStateList2 != null) {
            this.k.setHintTextColor(colorStateList2);
        }
        if(colorStateList3 != null) {
            this.k.setLinkTextColor(colorStateList3);
        }
        if(!(transformationMethod0 instanceof PasswordTransformationMethod) && z1) {
            this.k.setAllCaps(z);
        }
        Typeface typeface0 = this.i;
        if(typeface0 != null) {
            this.k.setTypeface(typeface0, this.j);
        }
        this.b.l(attributeSet0, v);
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE && this.b.h() != 0) {
            int[] arr_v = this.b.g();
            if(arr_v.length > 0) {
                if(((float)this.k.getAutoSizeStepGranularity()) == -1.0f) {
                    c.q(this.k, arr_v);
                }
                else {
                    c.o(this.k, this.b.e(), this.b.d(), this.b.f());
                }
            }
        }
        TintTypedArray tintTypedArray3 = TintTypedArray.n(context0, attributeSet0, R.styleable.AppCompatTextView);
        int v3 = tintTypedArray3.c(R.styleable.AppCompatTextView_firstBaselineToTopHeight);
        int v4 = tintTypedArray3.c(R.styleable.AppCompatTextView_lastBaselineToBottomHeight);
        int v5 = tintTypedArray3.c(R.styleable.AppCompatTextView_lineHeight);
        tintTypedArray3.p();
        if(v3 != -1) {
            TextViewCompat.setFirstBaselineToTopHeight(this.k, v3);
        }
        if(v4 != -1) {
            TextViewCompat.setLastBaselineToBottomHeight(this.k, v4);
        }
        if(v5 != -1) {
            TextViewCompat.setLineHeight(this.k, v5);
        }
    }

    public final void e(int v, Context context0) {
        TintTypedArray tintTypedArray0 = TintTypedArray.m(context0, v, R.styleable.TextAppearance);
        if(tintTypedArray0.l(R.styleable.TextAppearance_textAllCaps)) {
            boolean z = tintTypedArray0.a(R.styleable.TextAppearance_textAllCaps);
            this.k.setAllCaps(z);
        }
        if(Build.VERSION.SDK_INT < 23 && tintTypedArray0.l(R.styleable.TextAppearance_android_textColor)) {
            ColorStateList colorStateList0 = tintTypedArray0.b(R.styleable.TextAppearance_android_textColor);
            if(colorStateList0 != null) {
                this.k.setTextColor(colorStateList0);
            }
        }
        if(tintTypedArray0.l(R.styleable.TextAppearance_android_textSize) && tintTypedArray0.c(R.styleable.TextAppearance_android_textSize) == 0) {
            this.k.setTextSize(0, 0.0f);
        }
        this.i(context0, tintTypedArray0);
        tintTypedArray0.p();
        Typeface typeface0 = this.i;
        if(typeface0 != null) {
            this.k.setTypeface(typeface0, this.j);
        }
    }

    public final void f(int v, int v1, int v2, int v3) {
        AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = this.b;
        if(appCompatTextViewAutoSizeHelper0.p()) {
            DisplayMetrics displayMetrics0 = appCompatTextViewAutoSizeHelper0.f.getResources().getDisplayMetrics();
            appCompatTextViewAutoSizeHelper0.q(TypedValue.applyDimension(v3, ((float)v), displayMetrics0), TypedValue.applyDimension(v3, ((float)v1), displayMetrics0), TypedValue.applyDimension(v3, ((float)v2), displayMetrics0));
            if(appCompatTextViewAutoSizeHelper0.n()) {
                appCompatTextViewAutoSizeHelper0.a();
            }
        }
    }

    public final void g(int[] arr_v, int v) {
        AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = this.b;
        if(appCompatTextViewAutoSizeHelper0.p()) {
            if(arr_v.length > 0) {
                int[] arr_v1 = new int[arr_v.length];
                if(v == 0) {
                    arr_v1 = Arrays.copyOf(arr_v, arr_v.length);
                }
                else {
                    DisplayMetrics displayMetrics0 = appCompatTextViewAutoSizeHelper0.f.getResources().getDisplayMetrics();
                    for(int v1 = 0; v1 < arr_v.length; ++v1) {
                        arr_v1[v1] = Math.round(TypedValue.applyDimension(v, ((float)arr_v[v1]), displayMetrics0));
                    }
                }
                appCompatTextViewAutoSizeHelper0.d = AppCompatTextViewAutoSizeHelper.b(arr_v1);
                if(!appCompatTextViewAutoSizeHelper0.o()) {
                    throw new IllegalArgumentException("None of the preset sizes is valid: " + Arrays.toString(arr_v));
                }
            }
            else {
                appCompatTextViewAutoSizeHelper0.g = false;
            }
            if(appCompatTextViewAutoSizeHelper0.n()) {
                appCompatTextViewAutoSizeHelper0.a();
            }
        }
    }

    public final void h(int v) {
        AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = this.b;
        if(appCompatTextViewAutoSizeHelper0.p()) {
            switch(v) {
                case 0: {
                    appCompatTextViewAutoSizeHelper0.e = 0;
                    appCompatTextViewAutoSizeHelper0.b = -1.0f;
                    appCompatTextViewAutoSizeHelper0.a = -1.0f;
                    appCompatTextViewAutoSizeHelper0.c = -1.0f;
                    appCompatTextViewAutoSizeHelper0.d = new int[0];
                    appCompatTextViewAutoSizeHelper0.h = false;
                    return;
                }
                case 1: {
                    DisplayMetrics displayMetrics0 = appCompatTextViewAutoSizeHelper0.f.getResources().getDisplayMetrics();
                    appCompatTextViewAutoSizeHelper0.q(TypedValue.applyDimension(2, 12.0f, displayMetrics0), TypedValue.applyDimension(2, 112.0f, displayMetrics0), 1.0f);
                    if(appCompatTextViewAutoSizeHelper0.n()) {
                        appCompatTextViewAutoSizeHelper0.a();
                        return;
                    }
                    break;
                }
                default: {
                    throw new IllegalArgumentException("Unknown auto-size text type: " + v);
                }
            }
        }
    }

    public final void i(Context context0, TintTypedArray tintTypedArray0) {
        Typeface typeface0;
        this.j = tintTypedArray0.g(R.styleable.TextAppearance_android_textStyle, this.j);
        boolean z = true;
        if(!tintTypedArray0.l(R.styleable.TextAppearance_android_fontFamily) && !tintTypedArray0.l(R.styleable.TextAppearance_fontFamily)) {
            if(tintTypedArray0.l(R.styleable.TextAppearance_android_typeface)) {
                this.a = false;
                switch(tintTypedArray0.g(R.styleable.TextAppearance_android_typeface, 1)) {
                    case 1: {
                        typeface0 = Typeface.SANS_SERIF;
                        break;
                    }
                    case 2: {
                        typeface0 = Typeface.SERIF;
                        break;
                    }
                    case 3: {
                        typeface0 = Typeface.MONOSPACE;
                        break;
                    }
                    default: {
                        return;
                    }
                }
                this.i = typeface0;
            }
            return;
        }
        this.i = null;
        int v = tintTypedArray0.l(R.styleable.TextAppearance_fontFamily) ? R.styleable.TextAppearance_fontFamily : R.styleable.TextAppearance_android_fontFamily;
        if(!context0.isRestricted()) {
            AppCompatTextHelper.1 appCompatTextHelper$10 = new AppCompatTextHelper.1(this, new WeakReference(this.k));
            try {
                Typeface typeface1 = tintTypedArray0.f(v, this.j, appCompatTextHelper$10);
                this.i = typeface1;
                if(typeface1 != null) {
                    z = false;
                }
                this.a = z;
            }
            catch(UnsupportedOperationException | Resources.NotFoundException unused_ex) {
            }
        }
        if(this.i == null) {
            String s = tintTypedArray0.j(v);
            if(s != null) {
                this.i = Typeface.create(s, this.j);
            }
        }
    }
}


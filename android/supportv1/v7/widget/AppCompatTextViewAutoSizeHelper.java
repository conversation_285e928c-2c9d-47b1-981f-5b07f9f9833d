package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.RectF;
import android.os.Build.VERSION;
import android.supportv1.v4.view.b;
import android.supportv1.v7.appcompat.R.styleable;
import android.text.Layout.Alignment;
import android.text.StaticLayout;
import android.text.TextDirectionHeuristic;
import android.text.TextDirectionHeuristics;
import android.text.TextPaint;
import android.text.method.TransformationMethod;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.widget.TextView;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;

class AppCompatTextViewAutoSizeHelper {
    public float a;
    public float b;
    public float c;
    public int[] d;
    public int e;
    public final Context f;
    public boolean g;
    public boolean h;
    public TextPaint i;
    public final TextView j;
    public static final RectF k;
    public static final ConcurrentHashMap l;

    static {
        AppCompatTextViewAutoSizeHelper.k = new RectF();
        AppCompatTextViewAutoSizeHelper.l = new ConcurrentHashMap();
    }

    public AppCompatTextViewAutoSizeHelper(TextView textView0) {
        this.e = 0;
        this.h = false;
        this.c = -1.0f;
        this.b = -1.0f;
        this.a = -1.0f;
        this.d = new int[0];
        this.g = false;
        this.j = textView0;
        this.f = textView0.getContext();
    }

    public final void a() {
        if(!this.k()) {
            return;
        }
        if(this.h) {
            if(this.j.getMeasuredHeight() > 0 && this.j.getMeasuredWidth() > 0) {
                int v = ((Boolean)AppCompatTextViewAutoSizeHelper.j(this.j, "getHorizontallyScrolling", Boolean.FALSE)).booleanValue() ? 0x100000 : this.j.getMeasuredWidth() - this.j.getTotalPaddingLeft() - this.j.getTotalPaddingRight();
                int v1 = this.j.getHeight() - this.j.getCompoundPaddingBottom() - this.j.getCompoundPaddingTop();
                if(v > 0 && v1 > 0) {
                    RectF rectF0 = AppCompatTextViewAutoSizeHelper.k;
                    synchronized(rectF0) {
                        rectF0.setEmpty();
                        rectF0.right = (float)v;
                        rectF0.bottom = (float)v1;
                        float f = (float)this.c(rectF0);
                        if(f != this.j.getTextSize()) {
                            this.m(f, 0);
                        }
                    }
                    this.h = true;
                    return;
                }
            }
            return;
        }
        this.h = true;
    }

    public static int[] b(int[] arr_v) {
        if(arr_v.length == 0) {
            return arr_v;
        }
        Arrays.sort(arr_v);
        ArrayList arrayList0 = new ArrayList();
        for(int v1 = 0; v1 < arr_v.length; ++v1) {
            int v2 = arr_v[v1];
            if(v2 > 0 && Collections.binarySearch(arrayList0, v2) < 0) {
                arrayList0.add(v2);
            }
        }
        if(arr_v.length == arrayList0.size()) {
            return arr_v;
        }
        int v3 = arrayList0.size();
        int[] arr_v1 = new int[v3];
        for(int v = 0; v < v3; ++v) {
            arr_v1[v] = (int)(((Integer)arrayList0.get(v)));
        }
        return arr_v1;
    }

    public final int c(RectF rectF0) {
        int v9;
        StaticLayout staticLayout0;
        int v = this.d.length;
        if(v == 0) {
            throw new IllegalStateException("No available text sizes to choose from.");
        }
        int v1 = v - 1;
        int v2 = 1;
        int v3 = 0;
        while(v2 <= v1) {
            int v4 = (v2 + v1) / 2;
            int v5 = this.d[v4];
            TextView textView0 = this.j;
            CharSequence charSequence0 = textView0.getText();
            TransformationMethod transformationMethod0 = textView0.getTransformationMethod();
            if(transformationMethod0 != null) {
                CharSequence charSequence1 = transformationMethod0.getTransformation(charSequence0, textView0);
                if(charSequence1 != null) {
                    charSequence0 = charSequence1;
                }
            }
            int v6 = Build.VERSION.SDK_INT;
            int v7 = textView0.getMaxLines();
            TextPaint textPaint0 = this.i;
            if(textPaint0 == null) {
                this.i = new TextPaint();
            }
            else {
                textPaint0.reset();
            }
            this.i.set(textView0.getPaint());
            this.i.setTextSize(((float)v5));
            Layout.Alignment layout$Alignment0 = (Layout.Alignment)AppCompatTextViewAutoSizeHelper.j(textView0, "getLayoutAlignment", Layout.Alignment.ALIGN_NORMAL);
            if(v6 >= 23) {
                int v8 = Math.round(rectF0.right);
                TextDirectionHeuristic textDirectionHeuristic0 = (TextDirectionHeuristic)AppCompatTextViewAutoSizeHelper.j(textView0, "getTextDirectionHeuristic", TextDirectionHeuristics.FIRSTSTRONG_LTR);
                staticLayout0 = b.i(charSequence0, charSequence0.length(), this.i, v8).setAlignment(layout$Alignment0).setLineSpacing(textView0.getLineSpacingExtra(), textView0.getLineSpacingMultiplier()).setIncludePad(textView0.getIncludeFontPadding()).setBreakStrategy(textView0.getBreakStrategy()).setHyphenationFrequency(textView0.getHyphenationFrequency()).setMaxLines((v7 == -1 ? 0x7FFFFFFF : v7)).setTextDirection(textDirectionHeuristic0).build();
                v9 = v7;
            }
            else {
                int v10 = Math.round(rectF0.right);
                float f = textView0.getLineSpacingMultiplier();
                float f1 = textView0.getLineSpacingExtra();
                boolean z = textView0.getIncludeFontPadding();
                v9 = v7;
                staticLayout0 = new StaticLayout(charSequence0, this.i, v10, layout$Alignment0, f, f1, z);
            }
            if(v9 != -1 && (staticLayout0.getLineCount() > v9 || staticLayout0.getLineEnd(staticLayout0.getLineCount() - 1) != charSequence0.length()) || ((float)staticLayout0.getHeight()) > rectF0.bottom) {
                v3 = v4 - 1;
                v1 = v3;
            }
            else {
                v3 = v2;
                v2 = v4 + 1;
            }
        }
        return this.d[v3];
    }

    public final int d() {
        return Math.round(this.a);
    }

    public final int e() {
        return Math.round(this.b);
    }

    public final int f() {
        return Math.round(this.c);
    }

    public final int[] g() {
        return this.d;
    }

    public final int h() {
        return this.e;
    }

    public static Method i(String s) {
        try {
            ConcurrentHashMap concurrentHashMap0 = AppCompatTextViewAutoSizeHelper.l;
            Method method0 = (Method)concurrentHashMap0.get(s);
            if(method0 == null) {
                method0 = TextView.class.getDeclaredMethod(s);
                if(method0 != null) {
                    method0.setAccessible(true);
                    concurrentHashMap0.put(s, method0);
                }
            }
            return method0;
        }
        catch(Exception unused_ex) {
            return null;
        }
    }

    public static Object j(TextView textView0, String s, Object object0) {
        try {
            return AppCompatTextViewAutoSizeHelper.i(s).invoke(textView0);
        }
        catch(Exception unused_ex) {
            return object0;
        }
    }

    public final boolean k() {
        return this.p() && this.e != 0;
    }

    public final void l(AttributeSet attributeSet0, int v) {
        TypedArray typedArray0 = this.f.obtainStyledAttributes(attributeSet0, R.styleable.AppCompatTextView, v, 0);
        if(typedArray0.hasValue(R.styleable.AppCompatTextView_autoSizeTextType)) {
            this.e = typedArray0.getInt(R.styleable.AppCompatTextView_autoSizeTextType, 0);
        }
        float f = typedArray0.hasValue(R.styleable.AppCompatTextView_autoSizeStepGranularity) ? typedArray0.getDimension(R.styleable.AppCompatTextView_autoSizeStepGranularity, -1.0f) : -1.0f;
        float f1 = typedArray0.hasValue(R.styleable.AppCompatTextView_autoSizeMinTextSize) ? typedArray0.getDimension(R.styleable.AppCompatTextView_autoSizeMinTextSize, -1.0f) : -1.0f;
        float f2 = typedArray0.hasValue(R.styleable.AppCompatTextView_autoSizeMaxTextSize) ? typedArray0.getDimension(R.styleable.AppCompatTextView_autoSizeMaxTextSize, -1.0f) : -1.0f;
        if(typedArray0.hasValue(R.styleable.AppCompatTextView_autoSizePresetSizes)) {
            int v1 = typedArray0.getResourceId(R.styleable.AppCompatTextView_autoSizePresetSizes, 0);
            if(v1 > 0) {
                TypedArray typedArray1 = typedArray0.getResources().obtainTypedArray(v1);
                int v2 = typedArray1.length();
                int[] arr_v = new int[v2];
                if(v2 > 0) {
                    for(int v3 = 0; v3 < v2; ++v3) {
                        arr_v[v3] = typedArray1.getDimensionPixelSize(v3, -1);
                    }
                    this.d = AppCompatTextViewAutoSizeHelper.b(arr_v);
                    this.o();
                }
                typedArray1.recycle();
            }
        }
        typedArray0.recycle();
        if(!this.p()) {
            this.e = 0;
        }
        else if(this.e == 1) {
            if(!this.g) {
                DisplayMetrics displayMetrics0 = this.f.getResources().getDisplayMetrics();
                if(f1 == -1.0f) {
                    f1 = TypedValue.applyDimension(2, 12.0f, displayMetrics0);
                }
                if(f2 == -1.0f) {
                    f2 = TypedValue.applyDimension(2, 112.0f, displayMetrics0);
                }
                if(f == -1.0f) {
                    f = 1.0f;
                }
                this.q(f1, f2, f);
            }
            this.n();
        }
    }

    public final void m(float f, int v) {
        float f1 = TypedValue.applyDimension(v, f, (this.f == null ? Resources.getSystem() : this.f.getResources()).getDisplayMetrics());
        TextView textView0 = this.j;
        if(f1 != textView0.getPaint().getTextSize()) {
            textView0.getPaint().setTextSize(f1);
            boolean z = textView0.isInLayout();
            if(textView0.getLayout() != null) {
                try {
                    this.h = false;
                    Method method0 = AppCompatTextViewAutoSizeHelper.i("nullLayouts");
                    if(method0 != null) {
                        method0.invoke(textView0);
                    }
                }
                catch(Exception unused_ex) {
                }
                if(z) {
                    textView0.forceLayout();
                }
                else {
                    textView0.requestLayout();
                }
                textView0.invalidate();
            }
        }
    }

    public final boolean n() {
        if(this.p() && this.e == 1) {
            if(!this.g || this.d.length == 0) {
                float f = (float)Math.round(this.b);
                int v1 = 1;
                while(Math.round(this.c + f) <= Math.round(this.a)) {
                    ++v1;
                    f += this.c;
                }
                int[] arr_v = new int[v1];
                float f1 = this.b;
                for(int v = 0; v < v1; ++v) {
                    arr_v[v] = Math.round(f1);
                    f1 += this.c;
                }
                this.d = AppCompatTextViewAutoSizeHelper.b(arr_v);
            }
            this.h = true;
            return true;
        }
        this.h = false;
        return false;
    }

    public final boolean o() {
        int[] arr_v = this.d;
        boolean z = arr_v.length > 0;
        this.g = z;
        if(z) {
            this.e = 1;
            this.b = (float)arr_v[0];
            this.a = (float)arr_v[arr_v.length - 1];
            this.c = -1.0f;
        }
        return z;
    }

    public final boolean p() {
        return !(this.j instanceof AppCompatEditText);
    }

    public final void q(float f, float f1, float f2) {
        if(f <= 0.0f) {
            throw new IllegalArgumentException("Minimum auto-size text size (" + f + "px) is less or equal to (0px)");
        }
        if(f1 <= f) {
            throw new IllegalArgumentException("Maximum auto-size text size (" + f1 + "px) is less or equal to minimum auto-size text size (" + f + "px)");
        }
        if(f2 <= 0.0f) {
            throw new IllegalArgumentException("The auto-size step granularity (" + f2 + "px) is less or equal to (0px)");
        }
        this.e = 1;
        this.b = f;
        this.a = f1;
        this.c = f2;
        this.g = false;
    }
}


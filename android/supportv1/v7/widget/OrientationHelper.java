package android.supportv1.v7.widget;

import android.graphics.Rect;
import android.view.View;

public abstract class OrientationHelper {
    public final LayoutManager a;
    public final Rect b;

    public OrientationHelper(LayoutManager recyclerView$LayoutManager0) {
        this.b = new Rect();
        this.a = recyclerView$LayoutManager0;
    }

    public abstract int a(View arg1);

    public abstract int b(View arg1);

    public abstract int c(View arg1);

    public abstract int d();

    public abstract int e();

    public abstract int f();

    public abstract int g();

    public abstract int h();

    public abstract int i();

    public abstract int j(View arg1);

    public abstract int k(View arg1);

    public abstract void l(int arg1);
}


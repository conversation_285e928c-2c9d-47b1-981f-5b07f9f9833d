package android.supportv1.v7.widget;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.Resources;
import android.graphics.Rect;
import android.supportv1.v7.appcompat.R.dimen;
import android.supportv1.v7.appcompat.R.id;
import android.supportv1.v7.appcompat.R.layout;
import android.supportv1.v7.appcompat.R.style;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.WindowManager.LayoutParams;
import android.widget.TextView;

class TooltipPopup {
    public final View a;
    public final Context b;
    public final WindowManager.LayoutParams c;
    public final TextView d;
    public final int[] e;
    public final int[] f;
    public final Rect g;

    public TooltipPopup(Context context0) {
        WindowManager.LayoutParams windowManager$LayoutParams0 = new WindowManager.LayoutParams();
        this.c = windowManager$LayoutParams0;
        this.g = new Rect();
        this.e = new int[2];
        this.f = new int[2];
        this.b = context0;
        View view0 = LayoutInflater.from(context0).inflate(R.layout.abc_tooltip, null);
        this.a = view0;
        this.d = (TextView)view0.findViewById(R.id.message);
        windowManager$LayoutParams0.setTitle(this.getClass().getSimpleName());
        windowManager$LayoutParams0.packageName = "com.pdf.editor.viewer.pdfreader.pdfviewer";
        windowManager$LayoutParams0.type = 1002;
        windowManager$LayoutParams0.width = -2;
        windowManager$LayoutParams0.height = -2;
        windowManager$LayoutParams0.format = -3;
        windowManager$LayoutParams0.windowAnimations = R.style.Animation_AppCompat_Tooltip;
        windowManager$LayoutParams0.flags = 24;
    }

    public final void a(View view0, int v, int v1, boolean z, WindowManager.LayoutParams windowManager$LayoutParams0) {
        int v5;
        int v4;
        windowManager$LayoutParams0.token = view0.getApplicationWindowToken();
        int v2 = this.b.getResources().getDimensionPixelOffset(R.dimen.tooltip_precise_anchor_threshold);
        if(view0.getWidth() < v2) {
            v = view0.getWidth() / 2;
        }
        if(view0.getHeight() >= v2) {
            int v3 = this.b.getResources().getDimensionPixelOffset(R.dimen.tooltip_precise_anchor_extra_offset);
            v4 = v1 + v3;
            v5 = v1 - v3;
        }
        else {
            v4 = view0.getHeight();
            v5 = 0;
        }
        windowManager$LayoutParams0.gravity = 49;
        int v6 = this.b.getResources().getDimensionPixelOffset((z ? R.dimen.tooltip_y_offset_touch : R.dimen.tooltip_y_offset_non_touch));
        View view1 = view0.getRootView();
        ViewGroup.LayoutParams viewGroup$LayoutParams0 = view1.getLayoutParams();
        if(!(viewGroup$LayoutParams0 instanceof WindowManager.LayoutParams) || ((WindowManager.LayoutParams)viewGroup$LayoutParams0).type != 2) {
            for(Context context0 = view0.getContext(); context0 instanceof ContextWrapper; context0 = ((ContextWrapper)context0).getBaseContext()) {
                if(context0 instanceof Activity) {
                    view1 = ((Activity)context0).getWindow().getDecorView();
                    break;
                }
            }
        }
        if(view1 == null) {
            return;
        }
        view1.getWindowVisibleDisplayFrame(this.g);
        if(this.g.left < 0 && this.g.top < 0) {
            Resources resources0 = this.b.getResources();
            int v7 = resources0.getIdentifier("status_bar_height", "dimen", "android");
            int v8 = v7 == 0 ? 0 : resources0.getDimensionPixelSize(v7);
            DisplayMetrics displayMetrics0 = resources0.getDisplayMetrics();
            this.g.set(0, v8, displayMetrics0.widthPixels, displayMetrics0.heightPixels);
        }
        view1.getLocationOnScreen(this.f);
        view0.getLocationOnScreen(this.e);
        int v9 = this.e[0] - this.f[0];
        this.e[0] = v9;
        this.e[1] -= this.f[1];
        windowManager$LayoutParams0.x = v9 + v - view1.getWidth() / 2;
        this.a.measure(0, 0);
        int v10 = this.a.getMeasuredHeight();
        int v11 = v5 + this.e[1] - v6 - v10;
        int v12 = this.e[1] + v4 + v6;
        if(z) {
            if(v11 < 0) {
                windowManager$LayoutParams0.y = v12;
                return;
            }
        }
        else if(v10 + v12 <= this.g.height()) {
            windowManager$LayoutParams0.y = v12;
            return;
        }
        windowManager$LayoutParams0.y = v11;
    }
}


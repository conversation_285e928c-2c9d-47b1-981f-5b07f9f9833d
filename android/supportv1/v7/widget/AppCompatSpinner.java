package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources.Theme;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.PorterDuff.Mode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.supportv1.v4.view.TintableBackgroundView;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.view.b;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.appcompat.R.layout;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.content.res.AppCompatResources;
import android.supportv1.v7.view.ContextThemeWrapper;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.Adapter;
import android.widget.ArrayAdapter;
import android.widget.ListAdapter;
import android.widget.PopupWindow;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.ThemedSpinnerAdapter;

public class AppCompatSpinner extends Spinner implements TintableBackgroundView {
    static class DropDownAdapter implements ListAdapter, SpinnerAdapter {
        public SpinnerAdapter a;
        public ListAdapter b;

        @Override  // android.widget.ListAdapter
        public final boolean areAllItemsEnabled() {
            return this.b == null ? true : this.b.areAllItemsEnabled();
        }

        @Override  // android.widget.Adapter
        public final int getCount() {
            return this.a == null ? 0 : this.a.getCount();
        }

        @Override  // android.widget.SpinnerAdapter
        public final View getDropDownView(int v, View view0, ViewGroup viewGroup0) {
            return this.a == null ? null : this.a.getDropDownView(v, view0, viewGroup0);
        }

        @Override  // android.widget.Adapter
        public final Object getItem(int v) {
            return this.a == null ? null : this.a.getItem(v);
        }

        @Override  // android.widget.Adapter
        public final long getItemId(int v) {
            return this.a == null ? -1L : this.a.getItemId(v);
        }

        @Override  // android.widget.Adapter
        public final int getItemViewType(int v) {
            return 0;
        }

        @Override  // android.widget.Adapter
        public final View getView(int v, View view0, ViewGroup viewGroup0) {
            return this.getDropDownView(v, view0, viewGroup0);
        }

        @Override  // android.widget.Adapter
        public final int getViewTypeCount() {
            return 1;
        }

        @Override  // android.widget.Adapter
        public final boolean hasStableIds() {
            return this.a != null && this.a.hasStableIds();
        }

        @Override  // android.widget.Adapter
        public final boolean isEmpty() {
            return this.getCount() == 0;
        }

        @Override  // android.widget.ListAdapter
        public final boolean isEnabled(int v) {
            return this.b == null ? true : this.b.isEnabled(v);
        }

        @Override  // android.widget.Adapter
        public final void registerDataSetObserver(DataSetObserver dataSetObserver0) {
            SpinnerAdapter spinnerAdapter0 = this.a;
            if(spinnerAdapter0 != null) {
                spinnerAdapter0.registerDataSetObserver(dataSetObserver0);
            }
        }

        @Override  // android.widget.Adapter
        public final void unregisterDataSetObserver(DataSetObserver dataSetObserver0) {
            SpinnerAdapter spinnerAdapter0 = this.a;
            if(spinnerAdapter0 != null) {
                spinnerAdapter0.unregisterDataSetObserver(dataSetObserver0);
            }
        }
    }

    class DropdownPopup extends ListPopupWindow {
        public CharSequence A;
        public final Rect B;
        public final AppCompatSpinner C;
        public ListAdapter z;

        public DropdownPopup(Context context0, AttributeSet attributeSet0, int v) {
            super(context0, attributeSet0, v, 0);
            this.B = new Rect();
            this.c = appCompatSpinner0;
            this.g();
            this.n = new AppCompatSpinner.DropdownPopup.1(this);
        }

        @Override  // android.supportv1.v7.widget.ListPopupWindow
        public final void c(ListAdapter listAdapter0) {
            super.c(listAdapter0);
            this.z = listAdapter0;
        }

        @Override  // android.supportv1.v7.widget.ListPopupWindow
        public final void j() {
            boolean z = this.r.isShowing();
            this.l();
            this.f();
            super.j();
            this.g.setChoiceMode(1);
            AppCompatSpinner appCompatSpinner0 = AppCompatSpinner.this;
            int v = appCompatSpinner0.getSelectedItemPosition();
            DropDownListView dropDownListView0 = this.g;
            if(this.r.isShowing() && dropDownListView0 != null) {
                dropDownListView0.c(false);
                dropDownListView0.setSelection(v);
                if(dropDownListView0.getChoiceMode() != 0) {
                    dropDownListView0.setItemChecked(v, true);
                }
            }
            if(z) {
                return;
            }
            ViewTreeObserver viewTreeObserver0 = appCompatSpinner0.getViewTreeObserver();
            if(viewTreeObserver0 != null) {
                AppCompatSpinner.DropdownPopup.2 appCompatSpinner$DropdownPopup$20 = new AppCompatSpinner.DropdownPopup.2(this);
                viewTreeObserver0.addOnGlobalLayoutListener(appCompatSpinner$DropdownPopup$20);
                this.h(new AppCompatSpinner.DropdownPopup.3(this, appCompatSpinner$DropdownPopup$20));
            }
        }

        public final void l() {
            int v1;
            PopupWindow popupWindow0 = this.r;
            Drawable drawable0 = popupWindow0.getBackground();
            AppCompatSpinner appCompatSpinner0 = AppCompatSpinner.this;
            if(drawable0 == null) {
                appCompatSpinner0.h.right = 0;
                appCompatSpinner0.h.left = 0;
                v1 = 0;
            }
            else {
                drawable0.getPadding(appCompatSpinner0.h);
                int v = ViewCompat.getLayoutDirection(appCompatSpinner0);
                Rect rect0 = appCompatSpinner0.h;
                v1 = v == 1 ? rect0.right : -rect0.left;
            }
            int v2 = appCompatSpinner0.getPaddingLeft();
            int v3 = appCompatSpinner0.getPaddingRight();
            int v4 = appCompatSpinner0.getWidth();
            int v5 = appCompatSpinner0.b;
            if(v5 == -2) {
                int v6 = appCompatSpinner0.a(((SpinnerAdapter)this.z), popupWindow0.getBackground());
                int v7 = appCompatSpinner0.getContext().getResources().getDisplayMetrics().widthPixels - appCompatSpinner0.h.left - appCompatSpinner0.h.right;
                if(v6 > v7) {
                    v6 = v7;
                }
                v5 = Math.max(v6, v4 - v2 - v3);
            }
            else if(v5 == -1) {
                v5 = v4 - v2 - v3;
            }
            this.e(v5);
            this.f = ViewCompat.getLayoutDirection(appCompatSpinner0) == 1 ? v4 - v3 - this.j + v1 : v1 + v2;
        }

        public final void m(String s) {
            this.A = s;
        }
    }

    public final AppCompatBackgroundHelper a;
    public int b;
    public final ForwardingListener c;
    public final DropdownPopup d;
    public final Context e;
    public final boolean f;
    public SpinnerAdapter g;
    public final Rect h;
    public static final int[] i;

    static {
        AppCompatSpinner.i = new int[]{0x10102F1};
    }

    public AppCompatSpinner(int v, Context context0, AttributeSet attributeSet0) {
        super(context0, attributeSet0, v);
        TypedArray typedArray0;
        this.h = new Rect();
        TintTypedArray tintTypedArray0 = TintTypedArray.o(context0, attributeSet0, R.styleable.Spinner, v, 0);
        this.a = new AppCompatBackgroundHelper(this);
        int v1 = tintTypedArray0.i(R.styleable.Spinner_popupTheme, 0);
        if(v1 == 0) {
            this.e = Build.VERSION.SDK_INT >= 23 ? null : context0;
        }
        else {
            this.e = new ContextThemeWrapper(context0, v1);
        }
        if(this.e != null) {
            try {
                int v2 = -1;
                typedArray0 = null;
                typedArray0 = context0.obtainStyledAttributes(attributeSet0, AppCompatSpinner.i, v, 0);
                if(typedArray0.hasValue(0)) {
                    v2 = typedArray0.getInt(0, 0);
                }
            }
            catch(Exception unused_ex) {
                if(typedArray0 != null) {
                    goto label_22;
                }
                goto label_32;
            }
            catch(Throwable throwable0) {
                if(typedArray0 != null) {
                    typedArray0.recycle();
                }
                throw throwable0;
            }
        label_22:
            typedArray0.recycle();
            if(v2 == 1) {
                DropdownPopup appCompatSpinner$DropdownPopup0 = new DropdownPopup(this, this.e, attributeSet0, v);
                TintTypedArray tintTypedArray1 = TintTypedArray.o(this.e, attributeSet0, R.styleable.Spinner, v, 0);
                this.b = tintTypedArray1.h(R.styleable.Spinner_android_dropDownWidth, -2);
                appCompatSpinner$DropdownPopup0.d(tintTypedArray1.d(R.styleable.Spinner_android_popupBackground));
                appCompatSpinner$DropdownPopup0.m(tintTypedArray0.j(R.styleable.Spinner_android_prompt));
                tintTypedArray1.p();
                this.d = appCompatSpinner$DropdownPopup0;
                this.c = new AppCompatSpinner.1(this, this, appCompatSpinner$DropdownPopup0);
            }
        }
    label_32:
        CharSequence[] arr_charSequence = tintTypedArray0.k(R.styleable.Spinner_android_entries);
        if(arr_charSequence != null) {
            ArrayAdapter arrayAdapter0 = new ArrayAdapter(context0, 0x1090008, arr_charSequence);
            arrayAdapter0.setDropDownViewResource(R.layout.support_simple_spinner_dropdown_item);
            this.setAdapter(arrayAdapter0);
        }
        tintTypedArray0.p();
        this.f = true;
        SpinnerAdapter spinnerAdapter0 = this.g;
        if(spinnerAdapter0 != null) {
            this.setAdapter(spinnerAdapter0);
            this.g = null;
        }
        this.a.d(attributeSet0, v);
    }

    public AppCompatSpinner(Context context0, AttributeSet attributeSet0) {
        this(R.attr.spinnerStyle, context0, attributeSet0);
    }

    public final int a(SpinnerAdapter spinnerAdapter0, Drawable drawable0) {
        int v = 0;
        if(spinnerAdapter0 == null) {
            return 0;
        }
        int v1 = View.MeasureSpec.makeMeasureSpec(this.getMeasuredWidth(), 0);
        int v2 = View.MeasureSpec.makeMeasureSpec(this.getMeasuredHeight(), 0);
        int v3 = Math.max(0, this.getSelectedItemPosition());
        int v4 = Math.min(spinnerAdapter0.getCount(), v3 + 15);
        int v5 = Math.max(0, v4 - 15);
        View view0 = null;
        int v6 = 0;
        while(v5 < v4) {
            int v7 = spinnerAdapter0.getItemViewType(v5);
            if(v7 != v6) {
                view0 = null;
                v6 = v7;
            }
            view0 = spinnerAdapter0.getView(v5, view0, this);
            if(view0.getLayoutParams() == null) {
                view0.setLayoutParams(new ViewGroup.LayoutParams(-2, -2));
            }
            view0.measure(v1, v2);
            v = Math.max(v, view0.getMeasuredWidth());
            ++v5;
        }
        if(drawable0 != null) {
            drawable0.getPadding(this.h);
            return v + (this.h.left + this.h.right);
        }
        return v;
    }

    @Override  // android.view.ViewGroup
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
    }

    @Override  // android.widget.Spinner
    public int getDropDownHorizontalOffset() {
        return this.d == null ? super.getDropDownHorizontalOffset() : this.d.f;
    }

    @Override  // android.widget.Spinner
    public int getDropDownVerticalOffset() {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.d;
        if(appCompatSpinner$DropdownPopup0 != null) {
            return appCompatSpinner$DropdownPopup0.i ? appCompatSpinner$DropdownPopup0.h : 0;
        }
        return super.getDropDownVerticalOffset();
    }

    @Override  // android.widget.Spinner
    public int getDropDownWidth() {
        return this.d == null ? super.getDropDownWidth() : this.b;
    }

    @Override  // android.widget.Spinner
    public Drawable getPopupBackground() {
        return this.d == null ? super.getPopupBackground() : this.d.r.getBackground();
    }

    @Override  // android.widget.Spinner
    public Context getPopupContext() {
        if(this.d != null) {
            return this.e;
        }
        return Build.VERSION.SDK_INT < 23 ? null : super.getPopupContext();
    }

    @Override  // android.widget.Spinner
    public CharSequence getPrompt() {
        return this.d == null ? super.getPrompt() : this.d.A;
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public ColorStateList getSupportBackgroundTintList() {
        return this.a == null ? null : this.a.b();
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.a == null ? null : this.a.c();
    }

    @Override  // android.widget.Spinner
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.d;
        if(appCompatSpinner$DropdownPopup0 != null && appCompatSpinner$DropdownPopup0.r.isShowing()) {
            appCompatSpinner$DropdownPopup0.b();
        }
    }

    @Override  // android.widget.Spinner
    public final void onMeasure(int v, int v1) {
        super.onMeasure(v, v1);
        if(this.d != null && View.MeasureSpec.getMode(v) == 0x80000000) {
            this.setMeasuredDimension(Math.min(Math.max(this.getMeasuredWidth(), this.a(this.getAdapter(), this.getBackground())), View.MeasureSpec.getSize(v)), this.getMeasuredHeight());
        }
    }

    @Override  // android.widget.Spinner
    public final boolean onTouchEvent(MotionEvent motionEvent0) {
        return this.c == null || !this.c.onTouch(this, motionEvent0) ? super.onTouchEvent(motionEvent0) : true;
    }

    @Override  // android.widget.Spinner
    public final boolean performClick() {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.d;
        if(appCompatSpinner$DropdownPopup0 != null) {
            if(!appCompatSpinner$DropdownPopup0.r.isShowing()) {
                appCompatSpinner$DropdownPopup0.j();
            }
            return true;
        }
        return super.performClick();
    }

    @Override  // android.widget.Spinner
    public void setAdapter(Adapter adapter0) {
        this.setAdapter(((SpinnerAdapter)adapter0));
    }

    @Override  // android.widget.Spinner
    public void setAdapter(SpinnerAdapter spinnerAdapter0) {
        if(!this.f) {
            this.g = spinnerAdapter0;
            return;
        }
        super.setAdapter(spinnerAdapter0);
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.d;
        if(appCompatSpinner$DropdownPopup0 != null) {
            Resources.Theme resources$Theme0 = (this.e == null ? this.getContext() : this.e).getTheme();
            DropDownAdapter appCompatSpinner$DropDownAdapter0 = new DropDownAdapter();  // 初始化器: Ljava/lang/Object;-><init>()V
            appCompatSpinner$DropDownAdapter0.a = spinnerAdapter0;
            if(spinnerAdapter0 instanceof ListAdapter) {
                appCompatSpinner$DropDownAdapter0.b = (ListAdapter)spinnerAdapter0;
            }
            if(resources$Theme0 != null && Build.VERSION.SDK_INT >= 23 && b.u(spinnerAdapter0) && ((ThemedSpinnerAdapter)spinnerAdapter0).getDropDownViewTheme() != resources$Theme0) {
                ((ThemedSpinnerAdapter)spinnerAdapter0).setDropDownViewTheme(resources$Theme0);
            }
            appCompatSpinner$DropdownPopup0.c(appCompatSpinner$DropDownAdapter0);
        }
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.Spinner
    public void setDropDownHorizontalOffset(int v) {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.d;
        if(appCompatSpinner$DropdownPopup0 != null) {
            appCompatSpinner$DropdownPopup0.f = v;
            return;
        }
        super.setDropDownHorizontalOffset(v);
    }

    @Override  // android.widget.Spinner
    public void setDropDownVerticalOffset(int v) {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.d;
        if(appCompatSpinner$DropdownPopup0 != null) {
            appCompatSpinner$DropdownPopup0.i(v);
            return;
        }
        super.setDropDownVerticalOffset(v);
    }

    @Override  // android.widget.Spinner
    public void setDropDownWidth(int v) {
        if(this.d != null) {
            this.b = v;
            return;
        }
        super.setDropDownWidth(v);
    }

    @Override  // android.widget.Spinner
    public void setPopupBackgroundDrawable(Drawable drawable0) {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.d;
        if(appCompatSpinner$DropdownPopup0 != null) {
            appCompatSpinner$DropdownPopup0.d(drawable0);
            return;
        }
        super.setPopupBackgroundDrawable(drawable0);
    }

    @Override  // android.widget.Spinner
    public void setPopupBackgroundResource(int v) {
        this.setPopupBackgroundDrawable(AppCompatResources.c(this.getPopupContext(), v));
    }

    @Override  // android.widget.Spinner
    public void setPrompt(CharSequence charSequence0) {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.d;
        if(appCompatSpinner$DropdownPopup0 != null) {
            appCompatSpinner$DropdownPopup0.A = charSequence0;
            return;
        }
        super.setPrompt(charSequence0);
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }
}


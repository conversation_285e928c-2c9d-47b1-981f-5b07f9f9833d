package android.supportv1.v7.widget;

import android.supportv1.v7.view.menu.ShowableListMenu;
import android.view.View;

class AppCompatSpinner.1 extends ForwardingListener {
    public final AppCompatSpinner j;
    public final DropdownPopup k;

    public AppCompatSpinner.1(AppCompatSpinner appCompatSpinner0, View view0, DropdownPopup appCompatSpinner$DropdownPopup0) {
        this.j = appCompatSpinner0;
        this.k = appCompatSpinner$DropdownPopup0;
        super(view0);
    }

    @Override  // android.supportv1.v7.widget.ForwardingListener
    public final ShowableListMenu b() {
        return this.k;
    }

    @Override  // android.supportv1.v7.widget.ForwardingListener
    public final void c() {
        AppCompatSpinner appCompatSpinner0 = this.j;
        if(!appCompatSpinner0.d.r.isShowing()) {
            appCompatSpinner0.d.j();
        }
    }
}


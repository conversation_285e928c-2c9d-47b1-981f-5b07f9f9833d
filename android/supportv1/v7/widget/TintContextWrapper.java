package android.supportv1.v7.widget;

import android.content.Context;
import android.content.ContextWrapper;

public abstract class TintContextWrapper extends ContextWrapper {
    public static final Object a;

    static {
        TintContextWrapper.a = new Object();
    }

    public static void a(Context context0) {
        if(!(context0.getResources() instanceof TintResources)) {
            context0.getResources();
        }
    }
}


package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.supportv1.v4.graphics.ColorUtils;
import android.util.TypedValue;

abstract class ThemeUtils {
    public static final int[] a;
    public static final int[] b;
    public static final int[] c;
    public static final int[] d;
    public static final int[] e;
    public static final int[] f;
    public static final ThreadLocal g;

    static {
        ThemeUtils.g = new ThreadLocal();
        ThemeUtils.b = new int[]{0xFEFEFF62};
        ThemeUtils.d = new int[]{0x101009C};
        ThemeUtils.e = new int[]{0x10100A7};
        ThemeUtils.a = new int[]{0x10100A0};
        ThemeUtils.c = new int[0];
        ThemeUtils.f = new int[1];
    }

    public static int a(int v, Context context0) {
        ColorStateList colorStateList0 = ThemeUtils.c(v, context0);
        if(colorStateList0 != null && colorStateList0.isStateful()) {
            int v1 = colorStateList0.getDefaultColor();
            return colorStateList0.getColorForState(ThemeUtils.b, v1);
        }
        ThreadLocal threadLocal0 = ThemeUtils.g;
        TypedValue typedValue0 = (TypedValue)threadLocal0.get();
        if(typedValue0 == null) {
            typedValue0 = new TypedValue();
            threadLocal0.set(typedValue0);
        }
        context0.getTheme().resolveAttribute(0x1010033, typedValue0, true);
        float f = typedValue0.getFloat();
        int v2 = ThemeUtils.b(v, context0);
        return ColorUtils.setAlphaComponent(v2, Math.round(((float)Color.alpha(v2)) * f));
    }

    public static int b(int v, Context context0) {
        ThemeUtils.f[0] = v;
        TintTypedArray tintTypedArray0 = TintTypedArray.n(context0, null, ThemeUtils.f);
        try {
            return tintTypedArray0.c.getColor(0, 0);
        }
        finally {
            tintTypedArray0.p();
        }
    }

    public static ColorStateList c(int v, Context context0) {
        ThemeUtils.f[0] = v;
        TintTypedArray tintTypedArray0 = TintTypedArray.n(context0, null, ThemeUtils.f);
        try {
            return tintTypedArray0.b(0);
        }
        finally {
            tintTypedArray0.p();
        }
    }
}


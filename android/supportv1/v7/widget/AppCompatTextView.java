package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.supportv1.v4.text.PrecomputedTextCompat.Params;
import android.supportv1.v4.text.PrecomputedTextCompat;
import android.supportv1.v4.view.TintableBackgroundView;
import android.supportv1.v4.widget.AutoSizeableTextView;
import android.supportv1.v4.widget.TextViewCompat;
import android.util.AttributeSet;
import android.view.ActionMode.Callback;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.TextView;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

public class AppCompatTextView extends TextView implements TintableBackgroundView, AutoSizeableTextView {
    public final AppCompatBackgroundHelper a;
    public Future b;
    public final AppCompatTextHelper c;

    public AppCompatTextView(Context context0, AttributeSet attributeSet0) {
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, 0x1010084);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = new AppCompatBackgroundHelper(this);
        this.a = appCompatBackgroundHelper0;
        appCompatBackgroundHelper0.d(attributeSet0, 0x1010084);
        AppCompatTextHelper appCompatTextHelper0 = new AppCompatTextHelper(this);
        this.c = appCompatTextHelper0;
        appCompatTextHelper0.d(attributeSet0, 0x1010084);
        appCompatTextHelper0.b();
    }

    public final void a() {
        Future future0 = this.b;
        if(future0 != null) {
            try {
                this.b = null;
                TextViewCompat.setPrecomputedText(this, ((PrecomputedTextCompat)future0.get()));
            }
            catch(InterruptedException | ExecutionException unused_ex) {
            }
        }
    }

    @Override  // android.widget.TextView
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.a();
        }
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView, android.supportv1.v4.widget.AutoSizeableTextView
    public int getAutoSizeMaxTextSize() {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            return super.getAutoSizeMaxTextSize();
        }
        return this.c == null ? -1 : Math.round(this.c.b.a);
    }

    @Override  // android.widget.TextView, android.supportv1.v4.widget.AutoSizeableTextView
    public int getAutoSizeMinTextSize() {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            return super.getAutoSizeMinTextSize();
        }
        return this.c == null ? -1 : Math.round(this.c.b.b);
    }

    @Override  // android.widget.TextView, android.supportv1.v4.widget.AutoSizeableTextView
    public int getAutoSizeStepGranularity() {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            return super.getAutoSizeStepGranularity();
        }
        return this.c == null ? -1 : Math.round(this.c.b.c);
    }

    @Override  // android.widget.TextView, android.supportv1.v4.widget.AutoSizeableTextView
    public int[] getAutoSizeTextAvailableSizes() {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            return super.getAutoSizeTextAvailableSizes();
        }
        return this.c == null ? new int[0] : this.c.b.d;
    }

    @Override  // android.widget.TextView, android.supportv1.v4.widget.AutoSizeableTextView
    public int getAutoSizeTextType() {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            return super.getAutoSizeTextType() == 1 ? 1 : 0;
        }
        return this.c == null ? 0 : this.c.b.e;
    }

    @Override  // android.widget.TextView
    public int getFirstBaselineToTopHeight() {
        return TextViewCompat.getFirstBaselineToTopHeight(this);
    }

    @Override  // android.widget.TextView
    public int getLastBaselineToBottomHeight() {
        return TextViewCompat.getLastBaselineToBottomHeight(this);
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public ColorStateList getSupportBackgroundTintList() {
        return this.a == null ? null : this.a.b();
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public PorterDuff.Mode getSupportBackgroundTintMode() {
        return this.a == null ? null : this.a.c();
    }

    @Override  // android.widget.TextView
    public CharSequence getText() {
        this.a();
        return super.getText();
    }

    public Params getTextMetricsParamsCompat() {
        return TextViewCompat.getTextMetricsParams(this);
    }

    @Override  // android.widget.TextView
    public final InputConnection onCreateInputConnection(EditorInfo editorInfo0) {
        InputConnection inputConnection0 = super.onCreateInputConnection(editorInfo0);
        AppCompatHintHelper.a(this, editorInfo0, inputConnection0);
        return inputConnection0;
    }

    @Override  // android.widget.TextView
    public final void onLayout(boolean z, int v, int v1, int v2, int v3) {
        super.onLayout(z, v, v1, v2, v3);
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null && !AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            appCompatTextHelper0.b.a();
        }
    }

    @Override  // android.widget.TextView
    public final void onMeasure(int v, int v1) {
        this.a();
        super.onMeasure(v, v1);
    }

    @Override  // android.widget.TextView
    public final void onTextChanged(CharSequence charSequence0, int v, int v1, int v2) {
        super.onTextChanged(charSequence0, v, v1, v2);
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null && !AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = appCompatTextHelper0.b;
            if(appCompatTextViewAutoSizeHelper0.k()) {
                appCompatTextViewAutoSizeHelper0.a();
            }
        }
    }

    @Override  // android.widget.TextView, android.supportv1.v4.widget.AutoSizeableTextView
    public final void setAutoSizeTextTypeUniformWithConfiguration(int v, int v1, int v2, int v3) {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            super.setAutoSizeTextTypeUniformWithConfiguration(v, v1, v2, v3);
            return;
        }
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.f(v, v1, v2, v3);
        }
    }

    @Override  // android.widget.TextView, android.supportv1.v4.widget.AutoSizeableTextView
    public final void setAutoSizeTextTypeUniformWithPresetSizes(int[] arr_v, int v) {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            super.setAutoSizeTextTypeUniformWithPresetSizes(arr_v, v);
            return;
        }
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.g(arr_v, v);
        }
    }

    @Override  // android.widget.TextView, android.supportv1.v4.widget.AutoSizeableTextView
    public void setAutoSizeTextTypeWithDefaults(int v) {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            super.setAutoSizeTextTypeWithDefaults(v);
            return;
        }
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.h(v);
        }
    }

    @Override  // android.view.View
    public void setBackgroundDrawable(Drawable drawable0) {
        super.setBackgroundDrawable(drawable0);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.e();
        }
    }

    @Override  // android.view.View
    public void setBackgroundResource(int v) {
        super.setBackgroundResource(v);
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.f(v);
        }
    }

    @Override  // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback actionMode$Callback0) {
        super.setCustomSelectionActionModeCallback(TextViewCompat.wrapCustomSelectionActionModeCallback(this, actionMode$Callback0));
    }

    @Override  // android.widget.TextView
    public void setFirstBaselineToTopHeight(int v) {
        if(Build.VERSION.SDK_INT >= 28) {
            super.setFirstBaselineToTopHeight(v);
            return;
        }
        TextViewCompat.setFirstBaselineToTopHeight(this, v);
    }

    @Override  // android.widget.TextView
    public void setLastBaselineToBottomHeight(int v) {
        if(Build.VERSION.SDK_INT >= 28) {
            super.setLastBaselineToBottomHeight(v);
            return;
        }
        TextViewCompat.setLastBaselineToBottomHeight(this, v);
    }

    @Override  // android.widget.TextView
    public void setLineHeight(int v) {
        TextViewCompat.setLineHeight(this, v);
    }

    public void setPrecomputedText(PrecomputedTextCompat precomputedTextCompat0) {
        TextViewCompat.setPrecomputedText(this, precomputedTextCompat0);
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public void setSupportBackgroundTintList(ColorStateList colorStateList0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.h(colorStateList0);
        }
    }

    @Override  // android.supportv1.v4.view.TintableBackgroundView
    public void setSupportBackgroundTintMode(PorterDuff.Mode porterDuff$Mode0) {
        AppCompatBackgroundHelper appCompatBackgroundHelper0 = this.a;
        if(appCompatBackgroundHelper0 != null) {
            appCompatBackgroundHelper0.i(porterDuff$Mode0);
        }
    }

    @Override  // android.widget.TextView
    public final void setTextAppearance(Context context0, int v) {
        super.setTextAppearance(context0, v);
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.e(v, context0);
        }
    }

    public void setTextFuture(Future future0) {
        this.b = future0;
        this.requestLayout();
    }

    public void setTextMetricsParamsCompat(Params precomputedTextCompat$Params0) {
        TextViewCompat.setTextMetricsParams(this, precomputedTextCompat$Params0);
    }

    @Override  // android.widget.TextView
    public final void setTextSize(int v, float f) {
        if(AutoSizeableTextView.PLATFORM_SUPPORTS_AUTOSIZE) {
            super.setTextSize(v, f);
            return;
        }
        AppCompatTextHelper appCompatTextHelper0 = this.c;
        if(appCompatTextHelper0 != null) {
            AppCompatTextViewAutoSizeHelper appCompatTextViewAutoSizeHelper0 = appCompatTextHelper0.b;
            if(!appCompatTextViewAutoSizeHelper0.k()) {
                appCompatTextViewAutoSizeHelper0.m(f, v);
            }
        }
    }
}


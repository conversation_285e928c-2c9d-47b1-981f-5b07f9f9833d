package android.supportv1.v7.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.supportv1.v4.widget.ListViewAutoScrollHelper;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.graphics.drawable.DrawableWrapper;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.AbsListView;
import android.widget.ListAdapter;
import android.widget.ListView;
import java.lang.reflect.Field;

class DropDownListView extends ListView {
    static class GateKeeperDrawable extends DrawableWrapper {
        public boolean b;

        @Override  // android.supportv1.v7.graphics.drawable.DrawableWrapper
        public final void draw(Canvas canvas0) {
            if(this.b) {
                super.draw(canvas0);
            }
        }

        @Override  // android.supportv1.v7.graphics.drawable.DrawableWrapper
        public final void setHotspot(float f, float f1) {
            if(this.b) {
                super.setHotspot(f, f1);
            }
        }

        @Override  // android.supportv1.v7.graphics.drawable.DrawableWrapper
        public final void setHotspotBounds(int v, int v1, int v2, int v3) {
            if(this.b) {
                super.setHotspotBounds(v, v1, v2, v3);
            }
        }

        // 去混淆评级： 低(20)
        @Override  // android.graphics.drawable.Drawable
        public final boolean setState(int[] arr_v) {
            return this.b ? this.a.setState(arr_v) : false;
        }

        // 去混淆评级： 低(20)
        @Override  // android.supportv1.v7.graphics.drawable.DrawableWrapper
        public final boolean setVisible(boolean z, boolean z1) {
            return this.b ? super.setVisible(z, z1) : false;
        }
    }

    class ResolveHoverRunnable implements Runnable {
        public final DropDownListView a;

        @Override
        public final void run() {
            this.a.f = null;
            this.a.drawableStateChanged();
        }
    }

    public boolean a;
    public final boolean b;
    public final Field c;
    public boolean d;
    public int e;
    public ResolveHoverRunnable f;
    public ListViewAutoScrollHelper g;
    public int h;
    public int i;
    public int j;
    public int k;
    public GateKeeperDrawable l;
    public final Rect m;

    public DropDownListView(Context context0, boolean z) {
        super(context0, null, R.attr.dropDownListViewStyle);
        this.m = new Rect();
        this.i = 0;
        this.k = 0;
        this.j = 0;
        this.h = 0;
        this.b = z;
        this.setCacheColorHint(0);
        try {
            Field field0 = AbsListView.class.getDeclaredField("mIsChildViewEnabled");
            this.c = field0;
            field0.setAccessible(true);
        }
        catch(NoSuchFieldException noSuchFieldException0) {
            noSuchFieldException0.printStackTrace();
        }
    }

    public int a(int v, int v1) {
        int v2 = this.getListPaddingTop();
        int v3 = this.getListPaddingBottom();
        this.getListPaddingLeft();
        this.getListPaddingRight();
        int v4 = this.getDividerHeight();
        Drawable drawable0 = this.getDivider();
        ListAdapter listAdapter0 = this.getAdapter();
        if(listAdapter0 == null) {
            return v2 + v3;
        }
        if(v4 <= 0 || drawable0 == null) {
            v4 = 0;
        }
        int v5 = listAdapter0.getCount();
        int v6 = v2 + v3;
        View view0 = null;
        int v8 = 0;
        for(int v7 = 0; v7 < v5; ++v7) {
            int v9 = listAdapter0.getItemViewType(v7);
            if(v9 != v8) {
                view0 = null;
                v8 = v9;
            }
            view0 = listAdapter0.getView(v7, view0, this);
            ViewGroup.LayoutParams viewGroup$LayoutParams0 = view0.getLayoutParams();
            if(viewGroup$LayoutParams0 == null) {
                viewGroup$LayoutParams0 = this.generateDefaultLayoutParams();
                view0.setLayoutParams(viewGroup$LayoutParams0);
            }
            view0.measure(v, (viewGroup$LayoutParams0.height <= 0 ? 0 : View.MeasureSpec.makeMeasureSpec(viewGroup$LayoutParams0.height, 0x40000000)));
            view0.forceLayout();
            if(v7 > 0) {
                v6 += v4;
            }
            v6 += view0.getMeasuredHeight();
            if(v6 >= v1) {
                return v1;
            }
        }
        return v6;
    }

    public boolean b(MotionEvent motionEvent0, int v) {
        boolean z1;
        int v1 = motionEvent0.getActionMasked();
        boolean z = false;
        switch(v1) {
            case 1: {
                z1 = false;
                goto label_8;
            }
            case 2: {
                z1 = true;
            label_8:
                int v2 = motionEvent0.findPointerIndex(v);
                if(v2 >= 0) {
                    int v3 = (int)motionEvent0.getX(v2);
                    int v4 = (int)motionEvent0.getY(v2);
                    int v5 = this.pointToPosition(v3, v4);
                    if(v5 == -1) {
                        z = true;
                    }
                    else {
                        View view0 = this.getChildAt(v5 - this.getFirstVisiblePosition());
                        this.a = true;
                        this.drawableHotspotChanged(((float)v3), ((float)v4));
                        if(!this.isPressed()) {
                            this.setPressed(true);
                        }
                        this.layoutChildren();
                        int v6 = this.e;
                        if(v6 != -1) {
                            View view1 = this.getChildAt(v6 - this.getFirstVisiblePosition());
                            if(view1 != null && view1 != view0 && view1.isPressed()) {
                                view1.setPressed(false);
                            }
                        }
                        this.e = v5;
                        view0.drawableHotspotChanged(((float)v3) - ((float)view0.getLeft()), ((float)v4) - ((float)view0.getTop()));
                        if(!view0.isPressed()) {
                            view0.setPressed(true);
                        }
                        Drawable drawable0 = this.getSelector();
                        boolean z2 = drawable0 != null && v5 != -1;
                        if(z2) {
                            drawable0.setVisible(false, false);
                        }
                        Field field0 = this.c;
                        int v7 = view0.getLeft();
                        int v8 = view0.getTop();
                        int v9 = view0.getRight();
                        int v10 = view0.getBottom();
                        Rect rect0 = this.m;
                        rect0.set(v7, v8, v9, v10);
                        rect0.left -= this.i;
                        rect0.top -= this.k;
                        rect0.right += this.j;
                        rect0.bottom += this.h;
                        try {
                            boolean z3 = field0.getBoolean(this);
                            if(view0.isEnabled() != z3) {
                                field0.set(this, Boolean.valueOf(!z3));
                                this.refreshDrawableState();
                            }
                        }
                        catch(IllegalAccessException illegalAccessException0) {
                            illegalAccessException0.printStackTrace();
                        }
                        if(z2) {
                            float f = rect0.exactCenterX();
                            float f1 = rect0.exactCenterY();
                            drawable0.setVisible(this.getVisibility() == 0, false);
                            DrawableCompat.setHotspot(drawable0, f, f1);
                        }
                        Drawable drawable1 = this.getSelector();
                        if(drawable1 != null && v5 != -1) {
                            DrawableCompat.setHotspot(drawable1, ((float)v3), ((float)v4));
                        }
                        GateKeeperDrawable dropDownListView$GateKeeperDrawable0 = this.l;
                        if(dropDownListView$GateKeeperDrawable0 != null) {
                            dropDownListView$GateKeeperDrawable0.b = false;
                        }
                        this.refreshDrawableState();
                        if(v1 == 1) {
                            this.performItemClick(view0, v5, this.getItemIdAtPosition(v5));
                        }
                        z1 = true;
                    }
                }
                else {
                    z1 = false;
                }
                break;
            }
            case 3: {
                z1 = false;
                break;
            }
            default: {
                z1 = true;
            }
        }
        if(!z1 || z) {
            this.a = false;
            this.setPressed(false);
            this.drawableStateChanged();
            View view2 = this.getChildAt(this.e - this.getFirstVisiblePosition());
            if(view2 != null) {
                view2.setPressed(false);
            }
        }
        if(z1) {
            if(this.g == null) {
                this.g = new ListViewAutoScrollHelper(this);
            }
            this.g.setEnabled(true);
            this.g.onTouch(this, motionEvent0);
            return true;
        }
        ListViewAutoScrollHelper listViewAutoScrollHelper0 = this.g;
        if(listViewAutoScrollHelper0 != null) {
            listViewAutoScrollHelper0.setEnabled(false);
        }
        return false;
    }

    public void c(boolean z) {
        this.d = z;
    }

    @Override  // android.widget.ListView
    public final void dispatchDraw(Canvas canvas0) {
        Rect rect0 = this.m;
        if(!rect0.isEmpty()) {
            Drawable drawable0 = this.getSelector();
            if(drawable0 != null) {
                drawable0.setBounds(rect0);
                drawable0.draw(canvas0);
            }
        }
        super.dispatchDraw(canvas0);
    }

    @Override  // android.widget.AbsListView
    public final void drawableStateChanged() {
        if(this.f != null) {
            return;
        }
        super.drawableStateChanged();
        GateKeeperDrawable dropDownListView$GateKeeperDrawable0 = this.l;
        if(dropDownListView$GateKeeperDrawable0 != null) {
            dropDownListView$GateKeeperDrawable0.b = true;
        }
        Drawable drawable0 = this.getSelector();
        if(drawable0 != null && this.a && this.isPressed()) {
            drawable0.setState(this.getDrawableState());
        }
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.ViewGroup
    public boolean hasFocus() {
        return this.b || super.hasFocus();
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.View
    public boolean hasWindowFocus() {
        return this.b || super.hasWindowFocus();
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.View
    public boolean isFocused() {
        return this.b || super.isFocused();
    }

    // 去混淆评级： 低(30)
    @Override  // android.view.View
    public boolean isInTouchMode() {
        return this.b && this.d || super.isInTouchMode();
    }

    @Override  // android.widget.ListView
    public final void onDetachedFromWindow() {
        this.f = null;
        super.onDetachedFromWindow();
    }

    @Override  // android.view.View
    public boolean onHoverEvent(MotionEvent motionEvent0) {
        if(Build.VERSION.SDK_INT < 26) {
            return super.onHoverEvent(motionEvent0);
        }
        int v = motionEvent0.getActionMasked();
        if(v == 10 && this.f == null) {
            ResolveHoverRunnable dropDownListView$ResolveHoverRunnable0 = new ResolveHoverRunnable(this);
            this.f = dropDownListView$ResolveHoverRunnable0;
            this.post(dropDownListView$ResolveHoverRunnable0);
        }
        boolean z = super.onHoverEvent(motionEvent0);
        if(v != 7 && v != 9) {
            this.setSelection(-1);
            return z;
        }
        int v1 = this.pointToPosition(((int)motionEvent0.getX()), ((int)motionEvent0.getY()));
        if(v1 != -1 && v1 != this.getSelectedItemPosition()) {
            View view0 = this.getChildAt(v1 - this.getFirstVisiblePosition());
            if(view0.isEnabled()) {
                this.setSelectionFromTop(v1, view0.getTop() - this.getTop());
            }
            Drawable drawable0 = this.getSelector();
            if(drawable0 != null && this.a && this.isPressed()) {
                drawable0.setState(this.getDrawableState());
            }
        }
        return z;
    }

    @Override  // android.widget.AbsListView
    public boolean onTouchEvent(MotionEvent motionEvent0) {
        if(motionEvent0.getAction() == 0) {
            this.e = this.pointToPosition(((int)motionEvent0.getX()), ((int)motionEvent0.getY()));
        }
        ResolveHoverRunnable dropDownListView$ResolveHoverRunnable0 = this.f;
        if(dropDownListView$ResolveHoverRunnable0 != null) {
            dropDownListView$ResolveHoverRunnable0.a.f = null;
            dropDownListView$ResolveHoverRunnable0.a.removeCallbacks(dropDownListView$ResolveHoverRunnable0);
        }
        return super.onTouchEvent(motionEvent0);
    }

    @Override  // android.widget.AbsListView
    public void setSelector(Drawable drawable0) {
        GateKeeperDrawable dropDownListView$GateKeeperDrawable0 = null;
        if(drawable0 != null) {
            GateKeeperDrawable dropDownListView$GateKeeperDrawable1 = new GateKeeperDrawable();  // 初始化器: Landroid/graphics/drawable/Drawable;-><init>()V
            Drawable drawable1 = dropDownListView$GateKeeperDrawable1.a;
            if(drawable1 != null) {
                drawable1.setCallback(null);
            }
            dropDownListView$GateKeeperDrawable1.a = drawable0;
            drawable0.setCallback(dropDownListView$GateKeeperDrawable1);
            dropDownListView$GateKeeperDrawable1.b = true;
            dropDownListView$GateKeeperDrawable0 = dropDownListView$GateKeeperDrawable1;
        }
        this.l = dropDownListView$GateKeeperDrawable0;
        super.setSelector(dropDownListView$GateKeeperDrawable0);
        Rect rect0 = new Rect();
        if(drawable0 != null) {
            drawable0.getPadding(rect0);
        }
        this.i = rect0.left;
        this.k = rect0.top;
        this.j = rect0.right;
        this.h = rect0.bottom;
    }
}


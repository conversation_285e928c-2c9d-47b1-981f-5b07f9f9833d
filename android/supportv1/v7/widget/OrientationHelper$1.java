package android.supportv1.v7.widget;

import android.view.View;

final class OrientationHelper.1 extends OrientationHelper {
    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int a(View view0) {
        LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        return this.a.getDecoratedRight(view0) + recyclerView$LayoutParams0.rightMargin;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int b(View view0) {
        LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        return this.a.getDecoratedMeasuredWidth(view0) + recyclerView$LayoutParams0.leftMargin + recyclerView$LayoutParams0.rightMargin;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int c(View view0) {
        LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        return this.a.getDecoratedLeft(view0) - recyclerView$LayoutParams0.leftMargin;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int d() {
        return this.a.getWidth();
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int e() {
        return this.a.getWidth();
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int f() {
        return 0;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int g() {
        return this.a.getWidthMode();
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int h() {
        return 0;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int i() {
        return this.a.getWidth();
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int j(View view0) {
        this.a.getTransformedBoundingBox(view0, true, this.b);
        return this.b.right;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int k(View view0) {
        this.a.getTransformedBoundingBox(view0, true, this.b);
        return this.b.left;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final void l(int v) {
    }
}


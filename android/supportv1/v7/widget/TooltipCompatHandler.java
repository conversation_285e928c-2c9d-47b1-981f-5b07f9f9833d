package android.supportv1.v7.widget;

import android.content.Context;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.view.ViewConfigurationCompat;
import android.view.MotionEvent;
import android.view.View.OnAttachStateChangeListener;
import android.view.View.OnHoverListener;
import android.view.View.OnLongClickListener;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewParent;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityManager;

class TooltipCompatHandler implements View.OnAttachStateChangeListener, View.OnHoverListener, View.OnLongClickListener {
    public final View a;
    public int b;
    public int c;
    public boolean d;
    public final Runnable e;
    public final int f;
    public TooltipPopup g;
    public final Runnable h;
    public final CharSequence i;
    public static TooltipCompatHandler j;
    public static TooltipCompatHandler k;

    public TooltipCompatHandler(View view0, CharSequence charSequence0) {
        this.h = () -> {
            View view0 = this.a;
            if(!ViewCompat.isAttachedToWindow(view0)) {
                return;
            }
            TooltipCompatHandler.b(null);
            TooltipCompatHandler tooltipCompatHandler0 = TooltipCompatHandler.j;
            if(tooltipCompatHandler0 != null) {
                tooltipCompatHandler0.a();
            }
            TooltipCompatHandler.j = this;
            this.d = false;
            TooltipPopup tooltipPopup0 = new TooltipPopup(view0.getContext());
            this.g = tooltipPopup0;
            int v = this.b;
            int v1 = this.c;
            boolean z1 = this.d;
            View view1 = tooltipPopup0.a;
            ViewParent viewParent0 = view1.getParent();
            Context context0 = tooltipPopup0.b;
            if(viewParent0 != null && view1.getParent() != null) {
                ((WindowManager)context0.getSystemService("window")).removeView(view1);
            }
            tooltipPopup0.d.setText(this.i);
            tooltipPopup0.a(this.a, v, v1, z1, tooltipPopup0.c);
            ((WindowManager)context0.getSystemService("window")).addView(view1, tooltipPopup0.c);
            view0.addOnAttachStateChangeListener(this);
            long v2 = this.d ? 2500L : ((ViewCompat.getWindowSystemUiVisibility(view0) & 1) == 1 ? 3000L : 15000L) - ((long)ViewConfiguration.getLongPressTimeout());
            view0.removeCallbacks(this.e);
            view0.postDelayed(this.e, v2);
        };
        this.e = () -> {
            View view0 = this.a;
            if(TooltipCompatHandler.j == this) {
                TooltipCompatHandler.j = null;
                TooltipPopup tooltipPopup0 = this.g;
                if(tooltipPopup0 != null) {
                    View view1 = tooltipPopup0.a;
                    if(view1.getParent() != null) {
                        ((WindowManager)tooltipPopup0.b.getSystemService("window")).removeView(view1);
                    }
                    this.g = null;
                    this.b = 0x7FFFFFFF;
                    this.c = 0x7FFFFFFF;
                    view0.removeOnAttachStateChangeListener(this);
                }
            }
            if(TooltipCompatHandler.k == this) {
                TooltipCompatHandler.b(null);
            }
            view0.removeCallbacks(this.e);
        };
        this.a = view0;
        this.i = charSequence0;
        this.f = ViewConfigurationCompat.getScaledHoverSlop(ViewConfiguration.get(view0.getContext()));
        this.b = 0x7FFFFFFF;
        this.c = 0x7FFFFFFF;
        view0.setOnLongClickListener(this);
        view0.setOnHoverListener(this);
    }

    // 检测为 Lambda 实现
    public final void a() [...]

    public static void b(TooltipCompatHandler tooltipCompatHandler0) {
        TooltipCompatHandler tooltipCompatHandler1 = TooltipCompatHandler.k;
        if(tooltipCompatHandler1 != null) {
            tooltipCompatHandler1.a.removeCallbacks(tooltipCompatHandler1.h);
        }
        TooltipCompatHandler.k = tooltipCompatHandler0;
        if(tooltipCompatHandler0 != null) {
            long v = (long)ViewConfiguration.getLongPressTimeout();
            tooltipCompatHandler0.a.postDelayed(tooltipCompatHandler0.h, v);
        }
    }

    // 检测为 Lambda 实现
    public final void c(boolean z) [...]

    @Override  // android.view.View$OnHoverListener
    public final boolean onHover(View view0, MotionEvent motionEvent0) {
        if(this.g != null && this.d) {
            return false;
        }
        View view1 = this.a;
        AccessibilityManager accessibilityManager0 = (AccessibilityManager)view1.getContext().getSystemService("accessibility");
        if(accessibilityManager0.isEnabled() && accessibilityManager0.isTouchExplorationEnabled()) {
            return false;
        }
        switch(motionEvent0.getAction()) {
            case 7: {
                if(view1.isEnabled() && this.g == null) {
                    int v = (int)motionEvent0.getX();
                    int v1 = (int)motionEvent0.getY();
                    if(Math.abs(v - this.b) > this.f || Math.abs(v1 - this.c) > this.f) {
                        this.b = v;
                        this.c = v1;
                        TooltipCompatHandler.b(this);
                    }
                }
                return false;
            }
            case 10: {
                this.b = 0x7FFFFFFF;
                this.c = 0x7FFFFFFF;
                this.a();
                return false;
            }
            default: {
                return false;
            }
        }
    }

    @Override  // android.view.View$OnLongClickListener
    public final boolean onLongClick(View view0) {
        this.b = view0.getWidth() / 2;
        this.c = view0.getHeight() / 2;
        this.c(true);
        return true;
    }

    @Override  // android.view.View$OnAttachStateChangeListener
    public final void onViewAttachedToWindow(View view0) {
    }

    @Override  // android.view.View$OnAttachStateChangeListener
    public final void onViewDetachedFromWindow(View view0) {
        this.a();
    }

    class android.supportv1.v7.widget.TooltipCompatHandler.1 implements Runnable {
        public final TooltipCompatHandler a;

        public android.supportv1.v7.widget.TooltipCompatHandler.1() {
            this.a = tooltipCompatHandler0;
        }

        @Override
        public final void run() {
            this.a.c(false);
        }
    }


    class android.supportv1.v7.widget.TooltipCompatHandler.2 implements Runnable {
        public final TooltipCompatHandler a;

        public android.supportv1.v7.widget.TooltipCompatHandler.2() {
            this.a = tooltipCompatHandler0;
        }

        @Override
        public final void run() {
            this.a.a();
        }
    }

}


package android.supportv1.v7.widget;

import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.view.ViewTreeObserver;
import android.widget.PopupWindow.OnDismissListener;

class AppCompatSpinner.DropdownPopup.3 implements PopupWindow.OnDismissListener {
    public final DropdownPopup a;
    public final ViewTreeObserver.OnGlobalLayoutListener b;

    public AppCompatSpinner.DropdownPopup.3(DropdownPopup appCompatSpinner$DropdownPopup0, ViewTreeObserver.OnGlobalLayoutListener viewTreeObserver$OnGlobalLayoutListener0) {
        this.a = appCompatSpinner$DropdownPopup0;
        this.b = viewTreeObserver$OnGlobalLayoutListener0;
    }

    @Override  // android.widget.PopupWindow$OnDismissListener
    public final void onDismiss() {
        ViewTreeObserver viewTreeObserver0 = AppCompatSpinner.this.getViewTreeObserver();
        if(viewTreeObserver0 != null) {
            viewTreeObserver0.removeGlobalOnLayoutListener(this.b);
        }
    }
}


package android.supportv1.v7.widget;

import android.graphics.PointF;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;

public abstract class LinearLayoutManager extends LayoutManager {
    static class AnchorInfo {
        public int a;
        public boolean b;
        public int c;

        @Override
        public final String toString() {
            return "AnchorInfo{mPosition=" + this.c + ", mCoordinate=" + this.a + ", mLayoutFromEnd=" + this.b + ", mValid=false}";
        }
    }

    public static class LayoutChunkResult {
    }

    static class LayoutState {
        public int a;
        public int b;
        public int c;
        public boolean d;
        public int e;
        public int f;
        public boolean g;
        public int h;

    }

    public static class SavedState implements Parcelable {
        public static final Parcelable.Creator CREATOR;
        public boolean a;
        public int b;
        public int c;

        static {
            SavedState.CREATOR = new Parcelable.Creator() {  // 初始化器: Ljava/lang/Object;-><init>()V
                @Override  // android.os.Parcelable$Creator
                public final Object createFromParcel(Parcel parcel0) {
                    SavedState linearLayoutManager$SavedState0 = new SavedState();  // 初始化器: Ljava/lang/Object;-><init>()V
                    linearLayoutManager$SavedState0.c = parcel0.readInt();
                    linearLayoutManager$SavedState0.b = parcel0.readInt();
                    linearLayoutManager$SavedState0.a = parcel0.readInt() == 1;
                    return linearLayoutManager$SavedState0;
                }

                @Override  // android.os.Parcelable$Creator
                public final Object[] newArray(int v) {
                    return new SavedState[v];
                }
            };
        }

        @Override  // android.os.Parcelable
        public final int describeContents() {
            return 0;
        }

        @Override  // android.os.Parcelable
        public final void writeToParcel(Parcel parcel0, int v) {
            parcel0.writeInt(this.c);
            parcel0.writeInt(this.b);
            parcel0.writeInt(((int)this.a));
        }
    }

    static final boolean DEBUG = false;
    public static final int HORIZONTAL = 0;
    public static final int INVALID_OFFSET = 0x80000000;
    private static final float MAX_SCROLL_FACTOR = 0.333333f;
    private static final String TAG = "LinearLayoutManager";
    public static final int VERTICAL = 1;
    final AnchorInfo mAnchorInfo;
    private int mInitialPrefetchItemCount;
    private boolean mLastStackFromEnd;
    private final LayoutChunkResult mLayoutChunkResult;
    private LayoutState mLayoutState;
    int mOrientation;
    OrientationHelper mOrientationHelper;
    SavedState mPendingSavedState;
    int mPendingScrollPosition;
    int mPendingScrollPositionOffset;
    private boolean mRecycleChildrenOnDetach;
    private boolean mReverseLayout;
    boolean mShouldReverseLayout;
    private boolean mSmoothScrollbarEnabled;
    private boolean mStackFromEnd;

    public LinearLayoutManager() {
        this.mOrientation = 1;
        this.mReverseLayout = false;
        this.mShouldReverseLayout = false;
        this.mStackFromEnd = false;
        this.mSmoothScrollbarEnabled = true;
        this.mPendingScrollPosition = -1;
        this.mPendingScrollPositionOffset = 0x80000000;
        this.mPendingSavedState = null;
        AnchorInfo linearLayoutManager$AnchorInfo0 = new AnchorInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
        linearLayoutManager$AnchorInfo0.c = -1;
        linearLayoutManager$AnchorInfo0.a = 0x80000000;
        linearLayoutManager$AnchorInfo0.b = false;
        this.mAnchorInfo = linearLayoutManager$AnchorInfo0;
        this.mLayoutChunkResult = new LayoutChunkResult();  // 初始化器: Ljava/lang/Object;-><init>()V
        this.mInitialPrefetchItemCount = 2;
        this.setOrientation(0);
        this.setReverseLayout(false);
    }

    public void assertNotInLayoutOrScroll(String s) {
    }

    public final void b() {
    }

    public final void c() {
    }

    @Override  // android.supportv1.v7.widget.RecyclerView$LayoutManager
    public boolean canScrollHorizontally() {
        return this.mOrientation == 0;
    }

    @Override  // android.supportv1.v7.widget.RecyclerView$LayoutManager
    public boolean canScrollVertically() {
        return this.mOrientation == 1;
    }

    public void collectAdjacentPrefetchPositions(int v, int v1, State recyclerView$State0, LayoutPrefetchRegistry recyclerView$LayoutManager$LayoutPrefetchRegistry0) {
    }

    public void collectInitialPrefetchPositions(int v, LayoutPrefetchRegistry recyclerView$LayoutManager$LayoutPrefetchRegistry0) {
        boolean z;
        int v3;
        SavedState linearLayoutManager$SavedState0 = this.mPendingSavedState;
        int v2 = -1;
        if(linearLayoutManager$SavedState0 == null) {
        label_8:
            this.k();
            z = this.mShouldReverseLayout;
            v3 = this.mPendingScrollPosition;
            if(v3 == -1) {
                v3 = z ? v - 1 : 0;
            }
        }
        else {
            v3 = linearLayoutManager$SavedState0.c;
            if(v3 >= 0) {
                z = linearLayoutManager$SavedState0.a;
                goto label_13;
            }
            goto label_8;
        }
    label_13:
        if(!z) {
            v2 = 1;
        }
        for(int v1 = 0; v1 < this.mInitialPrefetchItemCount && v3 >= 0 && v3 < v; ++v1) {
            recyclerView$LayoutManager$LayoutPrefetchRegistry0.a();
            v3 += v2;
        }
    }

    public void collectPrefetchPositionsForLayoutState(State recyclerView$State0, LayoutState linearLayoutManager$LayoutState0, LayoutPrefetchRegistry recyclerView$LayoutManager$LayoutPrefetchRegistry0) {
        if(linearLayoutManager$LayoutState0.b >= 0) {
            throw null;
        }
    }

    public int computeHorizontalScrollExtent(State recyclerView$State0) {
        this.b();
        return 0;
    }

    public int computeHorizontalScrollOffset(State recyclerView$State0) {
        this.c();
        return 0;
    }

    public int computeHorizontalScrollRange(State recyclerView$State0) {
        this.d();
        return 0;
    }

    public PointF computeScrollVectorForPosition(int v) {
        return null;
    }

    public int computeVerticalScrollExtent(State recyclerView$State0) {
        this.b();
        return 0;
    }

    public int computeVerticalScrollOffset(State recyclerView$State0) {
        this.c();
        return 0;
    }

    public int computeVerticalScrollRange(State recyclerView$State0) {
        this.d();
        return 0;
    }

    public int convertFocusDirectionToLayoutDirection(int v) {
        switch(v) {
            case 1: {
                if(this.mOrientation == 1) {
                    return -1;
                }
                return this.isLayoutRTL() ? 1 : -1;
            }
            case 17: {
                return this.mOrientation == 0 ? -1 : 0x80000000;
            }
            case 33: {
                return this.mOrientation == 1 ? -1 : 0x80000000;
            }
            case 66: {
                return this.mOrientation == 0 ? 1 : 0x80000000;
            }
            case 130: {
                return this.mOrientation == 1 ? 1 : 0x80000000;
            }
            default: {
                if(v != 2) {
                    return 0x80000000;
                }
                if(this.mOrientation == 1) {
                    return 1;
                }
                return this.isLayoutRTL() ? -1 : 1;
            }
        }
    }

    public LayoutState createLayoutState() {
        LayoutState linearLayoutManager$LayoutState0 = new LayoutState();  // 初始化器: Ljava/lang/Object;-><init>()V
        linearLayoutManager$LayoutState0.g = true;
        linearLayoutManager$LayoutState0.c = 0;
        return linearLayoutManager$LayoutState0;
    }

    public final void d() {
    }

    public final void e(boolean z) {
        int v1;
        int v;
        if(this.mShouldReverseLayout) {
            v = 0;
            v1 = 0;
        }
        else {
            v1 = -1;
            v = -1;
        }
        this.findOneVisibleChild(v1, v, z, true);
    }

    public void ensureLayoutState() {
        if(this.mLayoutState == null) {
            this.mLayoutState = this.createLayoutState();
        }
    }

    public final void f(boolean z) {
        int v1;
        int v;
        if(this.mShouldReverseLayout) {
            v = -1;
            v1 = -1;
        }
        else {
            v1 = 0;
            v = 0;
        }
        this.findOneVisibleChild(v, v1, z, true);
    }

    public int fill(Recycler recyclerView$Recycler0, LayoutState linearLayoutManager$LayoutState0, State recyclerView$State0, boolean z) {
        int v = linearLayoutManager$LayoutState0.a;
        int v1 = linearLayoutManager$LayoutState0.h;
        if(v1 != 0x80000000) {
            if(v < 0) {
                linearLayoutManager$LayoutState0.h = v1 + v;
            }
            if(linearLayoutManager$LayoutState0.g && !linearLayoutManager$LayoutState0.d) {
                int v2 = linearLayoutManager$LayoutState0.h;
                if(linearLayoutManager$LayoutState0.f == -1 && v2 >= 0) {
                    int v3 = this.mOrientationHelper.d() - v2;
                }
            }
        }
        int v4 = linearLayoutManager$LayoutState0.a;
        if((linearLayoutManager$LayoutState0.d || linearLayoutManager$LayoutState0.c + v4 > 0) && linearLayoutManager$LayoutState0.b >= 0) {
            throw null;
        }
        return v - v4;
    }

    public int findFirstCompletelyVisibleItemPosition() {
        View view0 = this.findOneVisibleChild(0, 0, true, false);
        return view0 == null ? -1 : this.getPosition(view0);
    }

    public int findFirstVisibleItemPosition() {
        View view0 = this.findOneVisibleChild(0, 0, false, true);
        return view0 == null ? -1 : this.getPosition(view0);
    }

    public int findLastCompletelyVisibleItemPosition() {
        View view0 = this.findOneVisibleChild(-1, -1, true, false);
        return view0 == null ? -1 : this.getPosition(view0);
    }

    public int findLastVisibleItemPosition() {
        View view0 = this.findOneVisibleChild(-1, -1, false, true);
        return view0 == null ? -1 : this.getPosition(view0);
    }

    public View findOnePartiallyOrCompletelyInvisibleChild(int v, int v1) {
        this.ensureLayoutState();
        if(v1 > v || v1 < v) {
            if(this.mOrientationHelper.c(null) < this.mOrientationHelper.h()) {
                return this.mOrientation == 0 ? this.mHorizontalBoundCheck.a(v, v1, 0x4104, 0x4004) : this.mVerticalBoundCheck.a(v, v1, 0x4104, 0x4004);
            }
            return this.mOrientation == 0 ? this.mHorizontalBoundCheck.a(v, v1, 0x1041, 0x1001) : this.mVerticalBoundCheck.a(v, v1, 0x1041, 0x1001);
        }
        return null;
    }

    public View findOneVisibleChild(int v, int v1, boolean z, boolean z1) {
        this.ensureLayoutState();
        int v2 = 320;
        int v3 = z ? 0x6003 : 320;
        if(!z1) {
            v2 = 0;
        }
        return this.mOrientation == 0 ? this.mHorizontalBoundCheck.a(v, v1, v3, v2) : this.mVerticalBoundCheck.a(v, v1, v3, v2);
    }

    public View findReferenceChild(Recycler recyclerView$Recycler0, State recyclerView$State0, int v, int v1, int v2) {
        this.ensureLayoutState();
        this.mOrientationHelper.h();
        this.mOrientationHelper.e();
        int v3 = v1 <= v ? -1 : 1;
        while(v != v1) {
            int v4 = this.getPosition(null);
            if(v4 >= 0 && v4 < v2) {
                throw new NullPointerException();
            }
            v += v3;
        }
        return null;
    }

    public View findViewByPosition(int v) {
        return null;
    }

    public final View g() {
        return null;
    }

    public LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams();
    }

    public int getExtraLayoutSpace(State recyclerView$State0) {
        throw null;
    }

    public int getInitialPrefetchItemCount() {
        return this.mInitialPrefetchItemCount;
    }

    public int getOrientation() {
        return this.mOrientation;
    }

    public boolean getRecycleChildrenOnDetach() {
        return this.mRecycleChildrenOnDetach;
    }

    public boolean getReverseLayout() {
        return this.mReverseLayout;
    }

    public boolean getStackFromEnd() {
        return this.mStackFromEnd;
    }

    public final View h() {
        return null;
    }

    public final void i() {
    }

    public boolean isAutoMeasureEnabled() {
        return true;
    }

    public boolean isLayoutRTL() {
        return this.getLayoutDirection() == 1;
    }

    public boolean isSmoothScrollbarEnabled() {
        return this.mSmoothScrollbarEnabled;
    }

    public final void j(int v, int v1) {
        if(v == v1) {
            return;
        }
        if(v1 > v) {
            while(true) {
                --v1;
                if(v1 < v) {
                    break;
                }
                this.removeAndRecycleViewAt(v1, null);
            }
        }
        else {
            while(v > v1) {
                this.removeAndRecycleViewAt(v, null);
                --v;
            }
        }
    }

    public final void k() {
        this.mShouldReverseLayout = this.mOrientation == 1 || !this.isLayoutRTL() ? this.mReverseLayout : !this.mReverseLayout;
    }

    public final void l(int v, int v1, boolean z) {
        int v4;
        LayoutState linearLayoutManager$LayoutState0 = this.mLayoutState;
        linearLayoutManager$LayoutState0.d = this.resolveIsInfinite();
        LayoutState linearLayoutManager$LayoutState1 = this.mLayoutState;
        linearLayoutManager$LayoutState1.c = this.getExtraLayoutSpace(null);
        LayoutState linearLayoutManager$LayoutState2 = this.mLayoutState;
        linearLayoutManager$LayoutState2.f = v;
        int v2 = -1;
        if(v == 1) {
            int v3 = linearLayoutManager$LayoutState2.c;
            linearLayoutManager$LayoutState2.c = this.mOrientationHelper.f() + v3;
            View view0 = this.g();
            LayoutState linearLayoutManager$LayoutState3 = this.mLayoutState;
            if(!this.mShouldReverseLayout) {
                v2 = 1;
            }
            linearLayoutManager$LayoutState3.e = v2;
            linearLayoutManager$LayoutState3.b = this.getPosition(view0) + this.mLayoutState.e;
            this.mOrientationHelper.a(view0);
            v4 = this.mOrientationHelper.a(view0) - this.mOrientationHelper.e();
        }
        else {
            View view1 = this.h();
            LayoutState linearLayoutManager$LayoutState4 = this.mLayoutState;
            int v5 = linearLayoutManager$LayoutState4.c;
            linearLayoutManager$LayoutState4.c = this.mOrientationHelper.h() + v5;
            LayoutState linearLayoutManager$LayoutState5 = this.mLayoutState;
            if(this.mShouldReverseLayout) {
                v2 = 1;
            }
            linearLayoutManager$LayoutState5.e = v2;
            linearLayoutManager$LayoutState5.b = this.getPosition(view1) + this.mLayoutState.e;
            this.mOrientationHelper.c(view1);
            v4 = -this.mOrientationHelper.c(view1) + this.mOrientationHelper.h();
        }
        LayoutState linearLayoutManager$LayoutState6 = this.mLayoutState;
        linearLayoutManager$LayoutState6.a = v1;
        if(z) {
            linearLayoutManager$LayoutState6.a = v1 - v4;
        }
        linearLayoutManager$LayoutState6.h = v4;
    }

    public void layoutChunk(Recycler recyclerView$Recycler0, State recyclerView$State0, LayoutState linearLayoutManager$LayoutState0, LayoutChunkResult linearLayoutManager$LayoutChunkResult0) {
        linearLayoutManager$LayoutState0.getClass();
        throw null;
    }

    public void onAnchorReady(Recycler recyclerView$Recycler0, State recyclerView$State0, AnchorInfo linearLayoutManager$AnchorInfo0, int v) {
    }

    @Override  // android.supportv1.v7.widget.RecyclerView$LayoutManager
    public void onDetachedFromWindow(RecyclerView recyclerView0, Recycler recyclerView$Recycler0) {
        if(!this.mRecycleChildrenOnDetach) {
            return;
        }
        this.removeAndRecycleAllViews(recyclerView$Recycler0);
        throw null;
    }

    public View onFocusSearchFailed(View view0, int v, Recycler recyclerView$Recycler0, State recyclerView$State0) {
        this.k();
        return null;
    }

    public void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent0) {
        throw null;
    }

    public void onLayoutChildren(Recycler recyclerView$Recycler0, State recyclerView$State0) {
        if(this.mPendingSavedState != null || this.mPendingScrollPosition != -1) {
            throw null;
        }
        this.ensureLayoutState();
        this.mLayoutState.g = false;
        this.k();
        this.mAnchorInfo.getClass();
        this.mAnchorInfo.c = -1;
        this.mAnchorInfo.a = 0x80000000;
        this.mAnchorInfo.b = false;
        this.mAnchorInfo.b = this.mShouldReverseLayout ^ this.mStackFromEnd;
        throw null;
    }

    public void onLayoutCompleted(State recyclerView$State0) {
        this.mPendingSavedState = null;
        this.mPendingScrollPosition = -1;
        this.mPendingScrollPositionOffset = 0x80000000;
        this.mAnchorInfo.c = -1;
        this.mAnchorInfo.a = 0x80000000;
        this.mAnchorInfo.b = false;
    }

    public void onRestoreInstanceState(Parcelable parcelable0) {
        if(parcelable0 instanceof SavedState) {
            this.mPendingSavedState = (SavedState)parcelable0;
        }
    }

    public Parcelable onSaveInstanceState() {
        SavedState linearLayoutManager$SavedState0 = this.mPendingSavedState;
        if(linearLayoutManager$SavedState0 != null) {
            Parcelable parcelable0 = new SavedState();  // 初始化器: Ljava/lang/Object;-><init>()V
            parcelable0.c = linearLayoutManager$SavedState0.c;
            parcelable0.b = linearLayoutManager$SavedState0.b;
            parcelable0.a = linearLayoutManager$SavedState0.a;
            return parcelable0;
        }
        Parcelable parcelable1 = new SavedState();  // 初始化器: Ljava/lang/Object;-><init>()V
        parcelable1.c = -1;
        return parcelable1;
    }

    public void prepareForDrop(View view0, View view1, int v, int v1) {
        int v7;
        this.ensureLayoutState();
        this.k();
        int v2 = this.getPosition(view0);
        int v3 = this.getPosition(view1);
        int v4 = v2 >= v3 ? -1 : 1;
        boolean z = false;
        if(this.mShouldReverseLayout) {
            z = true;
            if(v4 == 1) {
                int v5 = this.mOrientationHelper.e();
                int v6 = this.mOrientationHelper.c(view1);
                this.scrollToPositionWithOffset(v3, v5 - (this.mOrientationHelper.b(view0) + v6));
                return;
            }
            v7 = this.mOrientationHelper.e() - this.mOrientationHelper.a(view1);
        }
        else if(v4 == -1) {
            z = true;
            v7 = this.mOrientationHelper.c(view1);
        }
        if(z) {
            this.scrollToPositionWithOffset(v3, v7);
            return;
        }
        this.scrollToPositionWithOffset(v3, this.mOrientationHelper.a(view1) - this.mOrientationHelper.b(view0));
    }

    public boolean resolveIsInfinite() {
        return this.mOrientationHelper.g() == 0 && this.mOrientationHelper.d() == 0;
    }

    public int scrollBy(int v, Recycler recyclerView$Recycler0, State recyclerView$State0) {
        return 0;
    }

    public int scrollHorizontallyBy(int v, Recycler recyclerView$Recycler0, State recyclerView$State0) {
        return this.mOrientation == 1 ? 0 : this.scrollBy(v, recyclerView$Recycler0, recyclerView$State0);
    }

    public void scrollToPosition(int v) {
        this.mPendingScrollPosition = v;
        this.mPendingScrollPositionOffset = 0x80000000;
        SavedState linearLayoutManager$SavedState0 = this.mPendingSavedState;
        if(linearLayoutManager$SavedState0 != null) {
            linearLayoutManager$SavedState0.c = -1;
        }
    }

    public void scrollToPositionWithOffset(int v, int v1) {
        this.mPendingScrollPosition = v;
        this.mPendingScrollPositionOffset = v1;
        SavedState linearLayoutManager$SavedState0 = this.mPendingSavedState;
        if(linearLayoutManager$SavedState0 != null) {
            linearLayoutManager$SavedState0.c = -1;
        }
    }

    public int scrollVerticallyBy(int v, Recycler recyclerView$Recycler0, State recyclerView$State0) {
        return this.mOrientation == 0 ? 0 : this.scrollBy(v, recyclerView$Recycler0, recyclerView$State0);
    }

    public void setInitialPrefetchItemCount(int v) {
        this.mInitialPrefetchItemCount = v;
    }

    public void setOrientation(int v) {
        OrientationHelper.1 orientationHelper$10;
        if(v != 0 && v != 1) {
            throw new IllegalArgumentException("invalid orientation:" + v);
        }
        if(v != this.mOrientation || this.mOrientationHelper == null) {
            switch(v) {
                case 0: {
                    orientationHelper$10 = new OrientationHelper.1(this);  // 初始化器: Landroid/supportv1/v7/widget/OrientationHelper;-><init>(Landroid/supportv1/v7/widget/RecyclerView$LayoutManager;)V
                    break;
                }
                case 1: {
                    orientationHelper$10 = new OrientationHelper.2(this);  // 初始化器: Landroid/supportv1/v7/widget/OrientationHelper;-><init>(Landroid/supportv1/v7/widget/RecyclerView$LayoutManager;)V
                    break;
                }
                default: {
                    throw new IllegalArgumentException("invalid orientation");
                }
            }
            this.mOrientationHelper = orientationHelper$10;
            this.mAnchorInfo.getClass();
            this.mOrientation = v;
        }
    }

    public void setRecycleChildrenOnDetach(boolean z) {
        this.mRecycleChildrenOnDetach = z;
    }

    public void setReverseLayout(boolean z) {
        if(z == this.mReverseLayout) {
            return;
        }
        this.mReverseLayout = z;
    }

    public void setSmoothScrollbarEnabled(boolean z) {
        this.mSmoothScrollbarEnabled = z;
    }

    public void setStackFromEnd(boolean z) {
        if(this.mStackFromEnd == z) {
            return;
        }
        this.mStackFromEnd = z;
    }

    public boolean shouldMeasureTwice() {
        return this.getHeightMode() != 0x40000000 && this.getWidthMode() != 0x40000000 && this.hasFlexibleChildInBothOrientations();
    }

    public void smoothScrollToPosition(RecyclerView recyclerView0, State recyclerView$State0, int v) {
        throw null;
    }

    public boolean supportsPredictiveItemAnimations() {
        return this.mPendingSavedState == null && this.mLastStackFromEnd == this.mStackFromEnd;
    }

    public void validateChildOrder() {
    }
}


package android.supportv1.v7.widget;

import android.supportv1.v7.view.menu.ShowableListMenu;
import android.view.View;

class ActionMenuPresenter.OverflowMenuButton.1 extends ForwardingListener {
    public final OverflowMenuButton j;

    public ActionMenuPresenter.OverflowMenuButton.1(OverflowMenuButton actionMenuPresenter$OverflowMenuButton0, View view0) {
        this.j = actionMenuPresenter$OverflowMenuButton0;
        super(view0);
    }

    @Override  // android.supportv1.v7.widget.ForwardingListener
    public final ShowableListMenu b() {
        ActionMenuPresenter.this.getClass();
        return null;
    }

    @Override  // android.supportv1.v7.widget.ForwardingListener
    public final void c() {
        ActionMenuPresenter.this.getClass();
    }

    @Override  // android.supportv1.v7.widget.ForwardingListener
    public final void d() {
        ActionMenuPresenter.this.getClass();
        ActionMenuPresenter.this.getClass();
    }
}


package android.supportv1.v7.widget;

import android.supportv1.v4.view.ViewCompat;
import android.view.ViewTreeObserver.OnGlobalLayoutListener;

class AppCompatSpinner.DropdownPopup.2 implements ViewTreeObserver.OnGlobalLayoutListener {
    public final DropdownPopup a;

    public AppCompatSpinner.DropdownPopup.2(DropdownPopup appCompatSpinner$DropdownPopup0) {
        this.a = appCompatSpinner$DropdownPopup0;
    }

    @Override  // android.view.ViewTreeObserver$OnGlobalLayoutListener
    public final void onGlobalLayout() {
        DropdownPopup appCompatSpinner$DropdownPopup0 = this.a;
        if(ViewCompat.isAttachedToWindow(AppCompatSpinner.this) && AppCompatSpinner.this.getGlobalVisibleRect(appCompatSpinner$DropdownPopup0.B)) {
            appCompatSpinner$DropdownPopup0.l();
            appCompatSpinner$DropdownPopup0.super.j();
            return;
        }
        appCompatSpinner$DropdownPopup0.b();
    }
}


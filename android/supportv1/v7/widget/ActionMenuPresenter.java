package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.supportv1.v4.view.ActionProvider.SubUiVisibilityListener;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.appcompat.R.layout;
import android.supportv1.v7.view.ActionBarPolicy;
import android.supportv1.v7.view.menu.BaseMenuPresenter;
import android.supportv1.v7.view.menu.MenuBuilder;
import android.supportv1.v7.view.menu.MenuItemImpl;
import android.supportv1.v7.view.menu.SubMenuBuilder;
import android.util.SparseBooleanArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import h.a;
import java.util.ArrayList;

class ActionMenuPresenter extends BaseMenuPresenter implements SubUiVisibilityListener {
    class OverflowMenuButton extends AppCompatImageView {
        public final ActionMenuPresenter c;

        public OverflowMenuButton(Context context0) {
            super(context0, null, R.attr.actionOverflowButtonStyle);
            this.setClickable(true);
            this.setFocusable(true);
            this.setVisibility(0);
            this.setEnabled(true);
            TooltipCompat.a(this, this.getContentDescription());
            this.setOnTouchListener(new ActionMenuPresenter.OverflowMenuButton.1(this, this));
        }

        @Override  // android.view.View
        public final boolean performClick() {
            if(super.performClick()) {
                return true;
            }
            this.playSoundEffect(0);
            ActionMenuPresenter.this.getClass();
            return true;
        }

        @Override  // android.widget.ImageView
        public final boolean setFrame(int v, int v1, int v2, int v3) {
            boolean z = super.setFrame(v, v1, v2, v3);
            Drawable drawable0 = this.getDrawable();
            Drawable drawable1 = this.getBackground();
            if(drawable0 != null && drawable1 != null) {
                int v4 = this.getWidth();
                int v5 = this.getHeight();
                int v6 = Math.max(v4, v5);
                int v7 = (this.getPaddingLeft() - this.getPaddingRight() + v4) / 2;
                int v8 = (this.getPaddingTop() - this.getPaddingBottom() + v5) / 2;
                DrawableCompat.setHotspotBounds(drawable1, v7 - v6 / 2, v8 - v6 / 2, v7 + v6 / 2, v8 + v6 / 2);
            }
            return z;
        }
    }

    public final SparseBooleanArray g;
    public int h;
    public int i;
    public OverflowMenuButton j;
    public boolean k;
    public boolean l;
    public int m;

    public ActionMenuPresenter(Context context0) {
        super(context0, R.layout.abc_action_menu_layout, R.layout.abc_action_menu_item_layout);
        this.g = new SparseBooleanArray();
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final void a(MenuBuilder menuBuilder0, boolean z) {
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final boolean b() {
        int v;
        ArrayList arrayList0;
        MenuBuilder menuBuilder0 = this.c;
        if(menuBuilder0 == null) {
            arrayList0 = null;
            v = 0;
        }
        else {
            arrayList0 = menuBuilder0.q();
            v = arrayList0.size();
        }
        int v1 = this.i;
        int v2 = this.h;
        boolean z = false;
        int v4 = 0;
        int v5 = 0;
        for(int v3 = 0; v3 < v; ++v3) {
            int v6 = ((MenuItemImpl)arrayList0.get(v3)).x;
            if((v6 & 2) == 2) {
                ++v4;
            }
            else if((v6 & 1) == 1) {
                ++v5;
            }
            else {
                z = true;
            }
        }
        if(this.k && (z || v5 + v4 > v1)) {
            --v1;
        }
        int v7 = v1 - v4;
        SparseBooleanArray sparseBooleanArray0 = this.g;
        sparseBooleanArray0.clear();
        int v8 = 0;
        while(v8 < v) {
            MenuItemImpl menuItemImpl0 = (MenuItemImpl)arrayList0.get(v8);
            int v9 = menuItemImpl0.x;
            if((v9 & 2) != 2) {
                if((v9 & 1) == 1) {
                    int v10 = menuItemImpl0.g;
                    boolean z1 = sparseBooleanArray0.get(v10);
                    if(v7 <= 0 && !z1 || v2 <= 0) {
                        if(z1) {
                            sparseBooleanArray0.put(v10, false);
                            for(int v11 = 0; v11 < v8; ++v11) {
                                MenuItemImpl menuItemImpl1 = (MenuItemImpl)arrayList0.get(v11);
                                if(menuItemImpl1.g == v10) {
                                    if(menuItemImpl1.d()) {
                                        ++v7;
                                    }
                                    menuItemImpl1.e(false);
                                }
                            }
                        }
                        menuItemImpl0.e(false);
                        ++v8;
                        continue;
                    }
                    this.f(menuItemImpl0);
                    throw null;
                }
                else {
                    menuItemImpl0.e(false);
                }
                ++v8;
                continue;
            }
            this.f(menuItemImpl0);
            throw null;
        }
        return true;
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final void c(Context context0, MenuBuilder menuBuilder0) {
        this.a = context0;
        LayoutInflater.from(context0);
        this.c = menuBuilder0;
        Resources resources0 = context0.getResources();
        ActionBarPolicy actionBarPolicy0 = new ActionBarPolicy();  // 初始化器: Ljava/lang/Object;-><init>()V
        actionBarPolicy0.a = context0;
        if(!this.l) {
            this.k = true;
        }
        this.m = context0.getResources().getDisplayMetrics().widthPixels / 2;
        this.i = actionBarPolicy0.a();
        int v = this.m;
        if(this.k) {
            if(this.j == null) {
                this.j = new OverflowMenuButton(this, this.e);
                this.j.measure(0, 0);
            }
            v -= this.j.getMeasuredWidth();
        }
        else {
            this.j = null;
        }
        this.h = v;
        resources0.getDisplayMetrics();
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final boolean d(SubMenuBuilder subMenuBuilder0) {
        if(!subMenuBuilder0.hasVisibleItems()) {
            return false;
        }
        MenuBuilder menuBuilder0;
        while((menuBuilder0 = subMenuBuilder0.x) != this.c) {
            subMenuBuilder0 = (SubMenuBuilder)menuBuilder0;
        }
        return false;
    }

    @Override  // android.supportv1.v7.view.menu.MenuPresenter
    public final void e() {
        throw null;
    }

    public final View f(MenuItemImpl menuItemImpl0) {
        View view0 = menuItemImpl0.getActionView();
        if(view0 != null && !menuItemImpl0.c()) {
            view0.setVisibility(0);
            view0.getLayoutParams();
            throw null;
        }
        a.f(this.f.inflate(this.b, null, false));
        throw null;
    }

    public final void g(ViewGroup viewGroup0) {
        a.f(this.f.inflate(this.d, viewGroup0, false));
        throw null;
    }

    public final void h() {
        Context context0 = this.a;
        ActionBarPolicy actionBarPolicy0 = new ActionBarPolicy();  // 初始化器: Ljava/lang/Object;-><init>()V
        actionBarPolicy0.a = context0;
        this.i = actionBarPolicy0.a();
        MenuBuilder menuBuilder0 = this.c;
        if(menuBuilder0 != null) {
            menuBuilder0.s(true);
        }
    }

    public final void i() {
        this.k = true;
        this.l = true;
    }

    @Override  // android.supportv1.v4.view.ActionProvider$SubUiVisibilityListener
    public final void onSubUiVisibilityChanged(boolean z) {
        if(!z) {
            MenuBuilder menuBuilder0 = this.c;
            if(menuBuilder0 != null) {
                menuBuilder0.d(false);
            }
        }
    }
}


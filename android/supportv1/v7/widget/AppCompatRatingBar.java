package android.supportv1.v7.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.supportv1.v7.appcompat.R.attr;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RatingBar;

public class AppCompatRatingBar extends RatingBar {
    public final AppCompatProgressBarHelper a;

    public AppCompatRatingBar(Context context0, AttributeSet attributeSet0) {
        int v = R.attr.ratingBarStyle;
        super(context0, attributeSet0, v);
        AppCompatProgressBarHelper appCompatProgressBarHelper0 = new AppCompatProgressBarHelper(this);
        this.a = appCompatProgressBarHelper0;
        appCompatProgressBarHelper0.a(attributeSet0, v);
    }

    @Override  // android.widget.RatingBar
    public final void onMeasure(int v, int v1) {
        synchronized(this) {
            super.onMeasure(v, v1);
            Bitmap bitmap0 = this.a.a;
            if(bitmap0 != null) {
                this.setMeasuredDimension(View.resolveSizeAndState(bitmap0.getWidth() * this.getNumStars(), v, 0), this.getMeasuredHeight());
            }
        }
    }
}


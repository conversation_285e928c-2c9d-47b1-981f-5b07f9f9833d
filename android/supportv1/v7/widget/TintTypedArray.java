package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.supportv1.v4.content.res.ResourcesCompat.FontCallback;
import android.supportv1.v4.content.res.ResourcesCompat;
import android.supportv1.v7.content.res.AppCompatResources;
import android.util.AttributeSet;
import android.util.TypedValue;

public class TintTypedArray {
    public final Context a;
    public TypedValue b;
    public final TypedArray c;

    public TintTypedArray(Context context0, TypedArray typedArray0) {
        this.a = context0;
        this.c = typedArray0;
    }

    public final boolean a(int v) {
        return this.c.getBoolean(v, false);
    }

    public final ColorStateList b(int v) {
        TypedArray typedArray0 = this.c;
        if(typedArray0.hasValue(v)) {
            int v1 = typedArray0.getResourceId(v, 0);
            if(v1 != 0) {
                ColorStateList colorStateList0 = AppCompatResources.b(this.a, v1);
                return colorStateList0 == null ? typedArray0.getColorStateList(v) : colorStateList0;
            }
        }
        return typedArray0.getColorStateList(v);
    }

    public final int c(int v) {
        return this.c.getDimensionPixelSize(v, -1);
    }

    public final Drawable d(int v) {
        TypedArray typedArray0 = this.c;
        if(typedArray0.hasValue(v)) {
            int v1 = typedArray0.getResourceId(v, 0);
            return v1 == 0 ? typedArray0.getDrawable(v) : AppCompatResources.c(this.a, v1);
        }
        return typedArray0.getDrawable(v);
    }

    public final Drawable e(int v) {
        TypedArray typedArray0 = this.c;
        if(typedArray0.hasValue(v)) {
            int v1 = typedArray0.getResourceId(v, 0);
            return v1 == 0 ? null : AppCompatDrawableManager.j().m(this.a, v1, true);
        }
        return null;
    }

    public final Typeface f(int v, int v1, FontCallback resourcesCompat$FontCallback0) {
        int v2 = this.c.getResourceId(v, 0);
        if(v2 == 0) {
            return null;
        }
        if(this.b == null) {
            this.b = new TypedValue();
        }
        return ResourcesCompat.getFont(this.a, v2, this.b, v1, resourcesCompat$FontCallback0);
    }

    public final int g(int v, int v1) {
        return this.c.getInt(v, v1);
    }

    public final int h(int v, int v1) {
        return this.c.getLayoutDimension(v, v1);
    }

    public final int i(int v, int v1) {
        return this.c.getResourceId(v, v1);
    }

    public final String j(int v) {
        return this.c.getString(v);
    }

    public final CharSequence[] k(int v) {
        return this.c.getTextArray(v);
    }

    public final boolean l(int v) {
        return this.c.hasValue(v);
    }

    public static TintTypedArray m(Context context0, int v, int[] arr_v) {
        return new TintTypedArray(context0, context0.obtainStyledAttributes(v, arr_v));
    }

    public static TintTypedArray n(Context context0, AttributeSet attributeSet0, int[] arr_v) {
        return new TintTypedArray(context0, context0.obtainStyledAttributes(attributeSet0, arr_v));
    }

    public static TintTypedArray o(Context context0, AttributeSet attributeSet0, int[] arr_v, int v, int v1) {
        return new TintTypedArray(context0, context0.obtainStyledAttributes(attributeSet0, arr_v, v, v1));
    }

    public final void p() {
        this.c.recycle();
    }
}


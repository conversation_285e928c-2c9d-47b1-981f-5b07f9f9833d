package android.supportv1.v7.widget;

import android.content.Context;
import android.supportv1.v4.widget.TextViewCompat;
import android.supportv1.v7.content.res.AppCompatResources;
import android.util.AttributeSet;
import android.view.ActionMode.Callback;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.CheckedTextView;

public class AppCompatCheckedTextView extends CheckedTextView {
    public final AppCompatTextHelper a;
    public static final int[] b;

    static {
        AppCompatCheckedTextView.b = new int[]{0x1010108};
    }

    public AppCompatCheckedTextView(Context context0, AttributeSet attributeSet0) {
        TintContextWrapper.a(context0);
        super(context0, attributeSet0, 0x10103C8);
        AppCompatTextHelper appCompatTextHelper0 = new AppCompatTextHelper(this);
        this.a = appCompatTextHelper0;
        appCompatTextHelper0.d(attributeSet0, 0x10103C8);
        appCompatTextHelper0.b();
        TintTypedArray tintTypedArray0 = TintTypedArray.o(this.getContext(), attributeSet0, AppCompatCheckedTextView.b, 0x10103C8, 0);
        this.setCheckMarkDrawable(tintTypedArray0.d(0));
        tintTypedArray0.p();
    }

    @Override  // android.widget.CheckedTextView
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        AppCompatTextHelper appCompatTextHelper0 = this.a;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.b();
        }
    }

    @Override  // android.widget.TextView
    public final InputConnection onCreateInputConnection(EditorInfo editorInfo0) {
        InputConnection inputConnection0 = super.onCreateInputConnection(editorInfo0);
        AppCompatHintHelper.a(this, editorInfo0, inputConnection0);
        return inputConnection0;
    }

    @Override  // android.widget.CheckedTextView
    public void setCheckMarkDrawable(int v) {
        this.setCheckMarkDrawable(AppCompatResources.c(this.getContext(), v));
    }

    @Override  // android.widget.TextView
    public void setCustomSelectionActionModeCallback(ActionMode.Callback actionMode$Callback0) {
        super.setCustomSelectionActionModeCallback(TextViewCompat.wrapCustomSelectionActionModeCallback(this, actionMode$Callback0));
    }

    @Override  // android.widget.TextView
    public final void setTextAppearance(Context context0, int v) {
        super.setTextAppearance(context0, v);
        AppCompatTextHelper appCompatTextHelper0 = this.a;
        if(appCompatTextHelper0 != null) {
            appCompatTextHelper0.e(v, context0);
        }
    }
}


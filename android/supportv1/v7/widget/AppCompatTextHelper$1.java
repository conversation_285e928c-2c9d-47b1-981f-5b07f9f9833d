package android.supportv1.v7.widget;

import android.graphics.Typeface;
import android.supportv1.v4.content.res.ResourcesCompat.FontCallback;
import android.widget.TextView;
import java.lang.ref.WeakReference;

class AppCompatTextHelper.1 extends FontCallback {
    public final AppCompatTextHelper a;
    public final WeakReference b;

    public AppCompatTextHelper.1(AppCompatTextHelper appCompatTextHelper0, WeakReference weakReference0) {
        this.a = appCompatTextHelper0;
        this.b = weakReference0;
        super();
    }

    @Override  // android.supportv1.v4.content.res.ResourcesCompat$FontCallback
    public final void onFontRetrievalFailed(int v) {
    }

    @Override  // android.supportv1.v4.content.res.ResourcesCompat$FontCallback
    public final void onFontRetrieved(Typeface typeface0) {
        AppCompatTextHelper appCompatTextHelper0 = this.a;
        if(appCompatTextHelper0.a) {
            appCompatTextHelper0.i = typeface0;
            TextView textView0 = (TextView)this.b.get();
            if(textView0 != null) {
                textView0.setTypeface(typeface0, appCompatTextHelper0.j);
            }
        }
    }
}


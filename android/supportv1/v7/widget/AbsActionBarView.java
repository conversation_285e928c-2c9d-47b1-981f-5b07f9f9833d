package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.supportv1.v4.view.ViewPropertyAnimatorCompat;
import android.supportv1.v4.view.ViewPropertyAnimatorListener;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.appcompat.R.styleable;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

abstract class AbsActionBarView extends ViewGroup {
    public class VisibilityAnimListener implements ViewPropertyAnimatorListener {
        public boolean a;
        public final AbsActionBarView b;

        public VisibilityAnimListener() {
            this.a = false;
        }

        @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListener
        public final void onAnimationCancel(View view0) {
            this.a = true;
        }

        @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListener
        public final void onAnimationEnd(View view0) {
            if(this.a) {
                return;
            }
            this.b.g = null;
            this.b.super.setVisibility(0);
        }

        @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListener
        public final void onAnimationStart(View view0) {
            AbsActionBarView.a(this.b);
            this.a = false;
        }
    }

    public ActionMenuPresenter a;
    public int b;
    public boolean c;
    public boolean d;
    public final Context e;
    public final VisibilityAnimListener f;
    public ViewPropertyAnimatorCompat g;

    public AbsActionBarView(Context context0, int v) {
        super(context0, null, v);
        this.f = new VisibilityAnimListener(this);
        TypedValue typedValue0 = new TypedValue();
        if(context0.getTheme().resolveAttribute(R.attr.actionBarPopupTheme, typedValue0, true) && typedValue0.resourceId != 0) {
            this.e = new ContextThemeWrapper(context0, typedValue0.resourceId);
            return;
        }
        this.e = context0;
    }

    public static void a(AbsActionBarView absActionBarView0) {
        absActionBarView0.super.setVisibility(0);
    }

    public static int c(View view0, boolean z, int v, int v1, int v2) {
        int v3 = view0.getMeasuredWidth();
        int v4 = view0.getMeasuredHeight();
        int v5 = (v2 - v4) / 2 + v1;
        if(z) {
            view0.layout(v - v3, v5, v, v4 + v5);
            return -v3;
        }
        view0.layout(v, v5, v + v3, v4 + v5);
        return v3;
    }

    @Override  // android.view.View
    public final void onConfigurationChanged(Configuration configuration0) {
        super.onConfigurationChanged(configuration0);
        TypedArray typedArray0 = this.getContext().obtainStyledAttributes(null, R.styleable.ActionBar, R.attr.actionBarStyle, 0);
        this.setContentHeight(typedArray0.getLayoutDimension(R.styleable.ActionBar_height, 0));
        typedArray0.recycle();
        ActionMenuPresenter actionMenuPresenter0 = this.a;
        if(actionMenuPresenter0 != null) {
            actionMenuPresenter0.h();
        }
    }

    @Override  // android.view.View
    public boolean onHoverEvent(MotionEvent motionEvent0) {
        int v = motionEvent0.getActionMasked();
        if(v == 9) {
            this.c = false;
        }
        if(!this.c && (v == 9 && !super.onHoverEvent(motionEvent0))) {
            this.c = true;
        }
        if(v == 3 || v == 10) {
            this.c = false;
        }
        return true;
    }

    @Override  // android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent0) {
        int v = motionEvent0.getActionMasked();
        if(v == 0) {
            this.d = false;
        }
        if(!this.d && (v == 0 && !super.onTouchEvent(motionEvent0))) {
            this.d = true;
        }
        if(v == 1 || v == 3) {
            this.d = false;
        }
        return true;
    }

    public abstract void setContentHeight(int arg1);

    @Override  // android.view.View
    public void setVisibility(int v) {
        if(v != this.getVisibility()) {
            ViewPropertyAnimatorCompat viewPropertyAnimatorCompat0 = this.g;
            if(viewPropertyAnimatorCompat0 != null) {
                viewPropertyAnimatorCompat0.cancel();
            }
            super.setVisibility(v);
        }
    }
}


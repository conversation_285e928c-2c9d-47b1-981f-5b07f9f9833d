package android.supportv1.v7.widget;

import android.os.SystemClock;
import android.supportv1.v7.view.menu.ShowableListMenu;
import android.view.MotionEvent;
import android.view.View.OnAttachStateChangeListener;
import android.view.View.OnTouchListener;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewParent;

public abstract class ForwardingListener implements View.OnAttachStateChangeListener, View.OnTouchListener {
    class DisallowIntercept implements Runnable {
        public final ForwardingListener a;

        @Override
        public final void run() {
            ViewParent viewParent0 = this.a.f.getParent();
            if(viewParent0 != null) {
                viewParent0.requestDisallowInterceptTouchEvent(true);
            }
        }
    }

    class TriggerLongPress implements Runnable {
        public final ForwardingListener a;

        @Override
        public final void run() {
            ForwardingListener forwardingListener0 = this.a;
            forwardingListener0.a();
            View view0 = forwardingListener0.f;
            if(view0.isEnabled() && !view0.isLongClickable()) {
                forwardingListener0.c();
                view0.getParent().requestDisallowInterceptTouchEvent(true);
                long v = SystemClock.uptimeMillis();
                MotionEvent motionEvent0 = MotionEvent.obtain(v, v, 3, 0.0f, 0.0f, 0);
                view0.onTouchEvent(motionEvent0);
                motionEvent0.recycle();
                forwardingListener0.c = true;
            }
        }
    }

    public int a;
    public Runnable b;
    public boolean c;
    public final int d;
    public final float e;
    public final View f;
    public final int g;
    public final int[] h;
    public Runnable i;

    public ForwardingListener(View view0) {
        this.h = new int[2];
        this.f = view0;
        view0.setLongClickable(true);
        view0.addOnAttachStateChangeListener(this);
        this.e = (float)ViewConfiguration.get(view0.getContext()).getScaledTouchSlop();
        this.g = 100;
        this.d = (ViewConfiguration.getLongPressTimeout() + 100) / 2;
    }

    public final void a() {
        Runnable runnable0 = this.i;
        View view0 = this.f;
        if(runnable0 != null) {
            view0.removeCallbacks(runnable0);
        }
        Runnable runnable1 = this.b;
        if(runnable1 != null) {
            view0.removeCallbacks(runnable1);
        }
    }

    public abstract ShowableListMenu b();

    public abstract void c();

    public void d() {
        ShowableListMenu showableListMenu0 = this.b();
        if(showableListMenu0 != null && ((ListPopupWindow)showableListMenu0).r.isShowing()) {
            ((ListPopupWindow)showableListMenu0).b();
        }
    }

    @Override  // android.view.View$OnTouchListener
    public final boolean onTouch(View view0, MotionEvent motionEvent0) {
        boolean z3;
        boolean z2;
        boolean z = this.c;
        View view1 = this.f;
        if(z) {
            ShowableListMenu showableListMenu0 = this.b();
            if(showableListMenu0 == null || !((ListPopupWindow)showableListMenu0).r.isShowing()) {
                this.d();
                z3 = false;
            }
            else {
                DropDownListView dropDownListView0 = ((ListPopupWindow)showableListMenu0).g;
                if(dropDownListView0 == null || !dropDownListView0.isShown()) {
                    this.d();
                    z3 = false;
                }
                else {
                    MotionEvent motionEvent1 = MotionEvent.obtainNoHistory(motionEvent0);
                    view1.getLocationOnScreen(this.h);
                    motionEvent1.offsetLocation(((float)this.h[0]), ((float)this.h[1]));
                    dropDownListView0.getLocationOnScreen(this.h);
                    motionEvent1.offsetLocation(((float)(-this.h[0])), ((float)(-this.h[1])));
                    boolean z1 = dropDownListView0.b(motionEvent1, this.a);
                    motionEvent1.recycle();
                    switch(motionEvent0.getActionMasked()) {
                        case 1: 
                        case 3: {
                            z2 = false;
                            break;
                        }
                        default: {
                            z2 = true;
                        }
                    }
                    if(!z1 || !z2) {
                        this.d();
                        z3 = false;
                    }
                    else {
                        z3 = true;
                    }
                }
            }
        }
        else {
            if(view1.isEnabled()) {
                switch(motionEvent0.getActionMasked()) {
                    case 0: {
                        this.a = motionEvent0.getPointerId(0);
                        if(this.b == null) {
                            this.b = new DisallowIntercept(this);
                        }
                        view1.postDelayed(this.b, ((long)this.g));
                        if(this.i == null) {
                            this.i = new TriggerLongPress(this);
                        }
                        view1.postDelayed(this.i, ((long)this.d));
                        z3 = false;
                        break;
                    }
                    case 2: {
                        int v = motionEvent0.findPointerIndex(this.a);
                        if(v >= 0) {
                            float f = motionEvent0.getX(v);
                            float f1 = motionEvent0.getY(v);
                            if(f >= -this.e && f1 >= -this.e && f < ((float)(view1.getRight() - view1.getLeft())) + this.e && f1 < ((float)(view1.getBottom() - view1.getTop())) + this.e) {
                                z3 = false;
                            }
                            else {
                                this.a();
                                view1.getParent().requestDisallowInterceptTouchEvent(true);
                                this.c();
                                z3 = true;
                            }
                        }
                        else {
                            z3 = false;
                        }
                        break;
                    }
                    case 1: 
                    case 3: {
                        this.a();
                        z3 = false;
                        break;
                    }
                    default: {
                        z3 = false;
                    }
                }
            }
            else {
                z3 = false;
            }
            if(z3) {
                long v1 = SystemClock.uptimeMillis();
                MotionEvent motionEvent2 = MotionEvent.obtain(v1, v1, 3, 0.0f, 0.0f, 0);
                view1.onTouchEvent(motionEvent2);
                motionEvent2.recycle();
            }
        }
        this.c = z3;
        return z3 || z;
    }

    @Override  // android.view.View$OnAttachStateChangeListener
    public final void onViewAttachedToWindow(View view0) {
    }

    @Override  // android.view.View$OnAttachStateChangeListener
    public final void onViewDetachedFromWindow(View view0) {
        this.c = false;
        this.a = -1;
        Runnable runnable0 = this.b;
        if(runnable0 != null) {
            this.f.removeCallbacks(runnable0);
        }
    }
}


package android.supportv1.v7.widget;

import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public abstract class ViewUtils {
    public static final Method a;

    static {
        try {
            Method method0 = View.class.getDeclaredMethod("computeFitSystemWindows", Rect.class, Rect.class);
            ViewUtils.a = method0;
            if(!method0.isAccessible()) {
                method0.setAccessible(true);
            }
        }
        catch(NoSuchMethodException unused_ex) {
        }
    }

    public static void a(ViewGroup viewGroup0, Rect rect0, Rect rect1) {
        Method method0 = ViewUtils.a;
        if(method0 != null) {
            try {
                method0.invoke(viewGroup0, rect0, rect1);
            }
            catch(Exception unused_ex) {
            }
        }
    }

    public static void b(ViewGroup viewGroup0) {
        try {
            Method method0 = viewGroup0.getClass().getMethod("makeOptionalFitsSystemWindows");
            if(!method0.isAccessible()) {
                method0.setAccessible(true);
            }
            method0.invoke(viewGroup0);
        }
        catch(NoSuchMethodException | InvocationTargetException | IllegalAccessException unused_ex) {
        }
    }
}


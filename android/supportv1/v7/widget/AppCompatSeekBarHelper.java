package android.supportv1.v7.widget;

import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.util.AttributeSet;
import android.widget.SeekBar;

class AppCompatSeekBarHelper extends AppCompatProgressBarHelper {
    public boolean d;
    public boolean e;
    public Drawable f;
    public ColorStateList g;
    public PorterDuff.Mode h;
    public final SeekBar i;

    public AppCompatSeekBarHelper(SeekBar seekBar0) {
        super(seekBar0);
        this.g = null;
        this.h = null;
        this.d = false;
        this.e = false;
        this.i = seekBar0;
    }

    @Override  // android.supportv1.v7.widget.AppCompatProgressBarHelper
    public final void a(AttributeSet attributeSet0, int v) {
        super.a(attributeSet0, v);
        TintTypedArray tintTypedArray0 = TintTypedArray.o(this.i.getContext(), attributeSet0, R.styleable.AppCompatSeekBar, v, 0);
        Drawable drawable0 = tintTypedArray0.e(R.styleable.AppCompatSeekBar_android_thumb);
        if(drawable0 != null) {
            this.i.setThumb(drawable0);
        }
        Drawable drawable1 = tintTypedArray0.d(R.styleable.AppCompatSeekBar_tickMark);
        Drawable drawable2 = this.f;
        if(drawable2 != null) {
            drawable2.setCallback(null);
        }
        this.f = drawable1;
        SeekBar seekBar0 = this.i;
        if(drawable1 != null) {
            drawable1.setCallback(seekBar0);
            DrawableCompat.setLayoutDirection(drawable1, ViewCompat.getLayoutDirection(seekBar0));
            if(drawable1.isStateful()) {
                drawable1.setState(seekBar0.getDrawableState());
            }
            this.c();
        }
        seekBar0.invalidate();
        if(tintTypedArray0.l(R.styleable.AppCompatSeekBar_tickMarkTintMode)) {
            this.h = DrawableUtils.c(tintTypedArray0.g(R.styleable.AppCompatSeekBar_tickMarkTintMode, -1), this.h);
            this.e = true;
        }
        if(tintTypedArray0.l(R.styleable.AppCompatSeekBar_tickMarkTint)) {
            this.g = tintTypedArray0.b(R.styleable.AppCompatSeekBar_tickMarkTint);
            this.d = true;
        }
        tintTypedArray0.p();
        this.c();
    }

    public final void c() {
        Drawable drawable0 = this.f;
        if(drawable0 != null && (this.d || this.e)) {
            Drawable drawable1 = DrawableCompat.wrap(drawable0.mutate());
            this.f = drawable1;
            if(this.d) {
                DrawableCompat.setTintList(drawable1, this.g);
            }
            if(this.e) {
                DrawableCompat.setTintMode(this.f, this.h);
            }
            if(this.f.isStateful()) {
                this.f.setState(this.i.getDrawableState());
            }
        }
    }

    public final void d(Canvas canvas0) {
        if(this.f != null) {
            SeekBar seekBar0 = this.i;
            int v = seekBar0.getMax();
            int v1 = 1;
            if(v > 1) {
                int v2 = this.f.getIntrinsicWidth();
                int v3 = this.f.getIntrinsicHeight();
                int v4 = v2 < 0 ? 1 : v2 / 2;
                if(v3 >= 0) {
                    v1 = v3 / 2;
                }
                this.f.setBounds(-v4, -v1, v4, v1);
                int v5 = seekBar0.getWidth();
                int v6 = seekBar0.getPaddingLeft();
                int v7 = seekBar0.getPaddingRight();
                int v8 = canvas0.save();
                canvas0.translate(((float)seekBar0.getPaddingLeft()), ((float)(seekBar0.getHeight() / 2)));
                for(int v9 = 0; v9 <= v; ++v9) {
                    this.f.draw(canvas0);
                    canvas0.translate(((float)(v5 - v6 - v7)) / ((float)v), 0.0f);
                }
                canvas0.restoreToCount(v8);
            }
        }
    }
}


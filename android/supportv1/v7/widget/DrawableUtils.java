package android.supportv1.v7.widget;

import android.graphics.PorterDuff.Mode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.DrawableContainer.DrawableContainerState;
import android.graphics.drawable.DrawableContainer;
import android.graphics.drawable.ScaleDrawable;
import android.os.Build.VERSION;
import android.supportv1.v4.graphics.drawable.WrappedDrawable;
import android.supportv1.v7.graphics.drawable.DrawableWrapper;

public abstract class DrawableUtils {
    static {
        new Rect();
        try {
            Class.forName("android.graphics.Insets");
        }
        catch(ClassNotFoundException unused_ex) {
        }
    }

    public static boolean a(Drawable drawable0) {
        if(drawable0 instanceof DrawableContainer) {
            Drawable.ConstantState drawable$ConstantState0 = drawable0.getConstantState();
            if(drawable$ConstantState0 instanceof DrawableContainer.DrawableContainerState) {
                Drawable[] arr_drawable = ((DrawableContainer.DrawableContainerState)drawable$ConstantState0).getChildren();
                for(int v = 0; v < arr_drawable.length; ++v) {
                    if(!DrawableUtils.a(arr_drawable[v])) {
                        return false;
                    }
                }
            }
            return true;
        }
        if(drawable0 instanceof WrappedDrawable) {
            return DrawableUtils.a(((WrappedDrawable)drawable0).getWrappedDrawable());
        }
        if(drawable0 instanceof DrawableWrapper) {
            return DrawableUtils.a(((DrawableWrapper)drawable0).a);
        }
        return drawable0 instanceof ScaleDrawable ? DrawableUtils.a(((ScaleDrawable)drawable0).getDrawable()) : true;
    }

    public static void b(Drawable drawable0) {
        if(Build.VERSION.SDK_INT == 21 && "android.graphics.drawable.VectorDrawable".equals(drawable0.getClass().getName())) {
            int[] arr_v = drawable0.getState();
            if(arr_v == null || arr_v.length == 0) {
                drawable0.setState(ThemeUtils.a);
            }
            else {
                drawable0.setState(ThemeUtils.c);
            }
            drawable0.setState(arr_v);
        }
    }

    public static PorterDuff.Mode c(int v, PorterDuff.Mode porterDuff$Mode0) {
        switch(v) {
            case 3: {
                return PorterDuff.Mode.SRC_OVER;
            }
            case 5: {
                return PorterDuff.Mode.SRC_IN;
            }
            case 9: {
                return PorterDuff.Mode.SRC_ATOP;
            }
            case 14: {
                return PorterDuff.Mode.MULTIPLY;
            }
            case 15: {
                return PorterDuff.Mode.SCREEN;
            }
            case 16: {
                return PorterDuff.Mode.ADD;
            }
            default: {
                return porterDuff$Mode0;
            }
        }
    }
}


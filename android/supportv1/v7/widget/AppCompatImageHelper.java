package android.supportv1.v7.widget;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.supportv1.v4.widget.ImageViewCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.content.res.AppCompatResources;
import android.util.AttributeSet;
import android.widget.ImageView;

public class AppCompatImageHelper {
    public TintInfo a;
    public TintInfo b;
    public final ImageView c;

    public AppCompatImageHelper(ImageView imageView0) {
        this.c = imageView0;
    }

    public final void a() {
        ImageView imageView0 = this.c;
        Drawable drawable0 = imageView0.getDrawable();
        if(drawable0 != null) {
            DrawableUtils.b(drawable0);
        }
        if(drawable0 != null) {
            if(Build.VERSION.SDK_INT <= 21 && Build.VERSION.SDK_INT == 21) {
                if(this.b == null) {
                    this.b = new TintInfo();  // 初始化器: Ljava/lang/Object;-><init>()V
                }
                TintInfo tintInfo0 = this.b;
                tintInfo0.c = null;
                tintInfo0.a = false;
                tintInfo0.d = null;
                tintInfo0.b = false;
                ColorStateList colorStateList0 = ImageViewCompat.getImageTintList(imageView0);
                if(colorStateList0 != null) {
                    tintInfo0.a = true;
                    tintInfo0.c = colorStateList0;
                }
                PorterDuff.Mode porterDuff$Mode0 = ImageViewCompat.getImageTintMode(imageView0);
                if(porterDuff$Mode0 != null) {
                    tintInfo0.b = true;
                    tintInfo0.d = porterDuff$Mode0;
                }
                if(tintInfo0.a || tintInfo0.b) {
                    AppCompatDrawableManager.s(drawable0, tintInfo0, imageView0.getDrawableState());
                    return;
                }
            }
            TintInfo tintInfo1 = this.a;
            if(tintInfo1 != null) {
                AppCompatDrawableManager.s(drawable0, tintInfo1, imageView0.getDrawableState());
            }
        }
    }

    public final void b(AttributeSet attributeSet0, int v) {
        TintTypedArray tintTypedArray0 = TintTypedArray.o(this.c.getContext(), attributeSet0, R.styleable.AppCompatImageView, v, 0);
        try {
            Drawable drawable0 = this.c.getDrawable();
            if(drawable0 == null) {
                int v2 = tintTypedArray0.i(R.styleable.AppCompatImageView_srcCompat, -1);
                if(v2 != -1) {
                    drawable0 = AppCompatResources.c(this.c.getContext(), v2);
                    if(drawable0 != null) {
                        this.c.setImageDrawable(drawable0);
                    }
                }
            }
            if(drawable0 != null) {
                DrawableUtils.b(drawable0);
            }
            if(tintTypedArray0.l(R.styleable.AppCompatImageView_tint)) {
                ColorStateList colorStateList0 = tintTypedArray0.b(R.styleable.AppCompatImageView_tint);
                ImageViewCompat.setImageTintList(this.c, colorStateList0);
            }
            if(tintTypedArray0.l(R.styleable.AppCompatImageView_tintMode)) {
                PorterDuff.Mode porterDuff$Mode0 = DrawableUtils.c(tintTypedArray0.g(R.styleable.AppCompatImageView_tintMode, -1), null);
                ImageViewCompat.setImageTintMode(this.c, porterDuff$Mode0);
            }
        }
        finally {
            tintTypedArray0.p();
        }
    }
}


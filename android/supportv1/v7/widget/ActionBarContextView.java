package android.supportv1.v7.widget;

import android.content.Context;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.appcompat.R.id;
import android.supportv1.v7.appcompat.R.layout;
import android.supportv1.v7.appcompat.R.styleable;
import android.supportv1.v7.view.StandaloneActionMode;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.accessibility.AccessibilityEvent;
import android.widget.LinearLayout;
import android.widget.TextView;

public class ActionBarContextView extends AbsActionBarView {
    public View h;
    public final int i;
    public View j;
    public CharSequence k;
    public final int l;
    public TextView m;
    public CharSequence n;
    public LinearLayout o;
    public boolean p;
    public final int q;
    public TextView r;

    public ActionBarContextView(Context context0) {
        this(context0, 0);
    }

    public ActionBarContextView(Context context0, int v) {
        this(context0, R.attr.actionModeStyle, 0);
    }

    public ActionBarContextView(Context context0, int v, int v1) {
        super(context0, v);
        TintTypedArray tintTypedArray0 = TintTypedArray.o(context0, null, R.styleable.ActionMode, v, 0);
        ViewCompat.setBackground(this, tintTypedArray0.d(R.styleable.ActionMode_background));
        this.q = tintTypedArray0.i(R.styleable.ActionMode_titleTextStyle, 0);
        this.l = tintTypedArray0.i(R.styleable.ActionMode_subtitleTextStyle, 0);
        this.b = tintTypedArray0.h(R.styleable.ActionMode_height, 0);
        this.i = tintTypedArray0.i(R.styleable.ActionMode_closeItemLayout, R.layout.abc_action_mode_close_item_material);
        tintTypedArray0.p();
    }

    public final void d(StandaloneActionMode standaloneActionMode0) {
        View view0 = this.h;
        if(view0 == null) {
            View view1 = LayoutInflater.from(this.getContext()).inflate(this.i, this, false);
            this.h = view1;
            this.addView(view1);
        }
        else if(view0.getParent() == null) {
            this.addView(this.h);
        }
        this.h.findViewById(R.id.action_mode_close_button).setOnClickListener(new ActionBarContextView.1(standaloneActionMode0));
        ActionMenuPresenter actionMenuPresenter0 = new ActionMenuPresenter(this.getContext());
        this.a = actionMenuPresenter0;
        actionMenuPresenter0.i();
        new ViewGroup.LayoutParams(-2, -1);
        standaloneActionMode0.o().c(this.a, this.e);
        this.a.g(this);
        throw null;
    }

    public final void e() {
        if(this.o == null) {
            LayoutInflater.from(this.getContext()).inflate(R.layout.abc_action_bar_title_item, this);
            LinearLayout linearLayout0 = (LinearLayout)this.getChildAt(this.getChildCount() - 1);
            this.o = linearLayout0;
            this.r = (TextView)linearLayout0.findViewById(R.id.action_bar_title);
            this.m = (TextView)this.o.findViewById(R.id.action_bar_subtitle);
            if(this.q != 0) {
                this.r.setTextAppearance(this.getContext(), this.q);
            }
            if(this.l != 0) {
                this.m.setTextAppearance(this.getContext(), this.l);
            }
        }
        this.r.setText(this.n);
        this.m.setText(this.k);
        boolean z = TextUtils.isEmpty(this.n);
        boolean z1 = TextUtils.isEmpty(this.k);
        int v = 0;
        this.m.setVisibility((!z1 == 0 ? 8 : 0));
        LinearLayout linearLayout1 = this.o;
        if(!z == 0 && !z1 == 0) {
            v = 8;
        }
        linearLayout1.setVisibility(v);
        if(this.o.getParent() == null) {
            this.addView(this.o);
        }
    }

    public final void f() {
        this.removeAllViews();
        this.j = null;
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new ViewGroup.MarginLayoutParams(-1, -2);
    }

    @Override  // android.view.ViewGroup
    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet0) {
        return new ViewGroup.MarginLayoutParams(this.getContext(), attributeSet0);
    }

    public int getAnimatedVisibility() {
        if(this.g != null) {
            this.f.getClass();
            return 0;
        }
        return this.getVisibility();
    }

    public int getContentHeight() {
        return this.b;
    }

    public CharSequence getSubtitle() {
        return this.k;
    }

    public CharSequence getTitle() {
        return this.n;
    }

    @Override  // android.view.ViewGroup
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        ActionMenuPresenter actionMenuPresenter0 = this.a;
        if(actionMenuPresenter0 != null) {
            actionMenuPresenter0.getClass();
        }
    }

    @Override  // android.view.View
    public final void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent0) {
        if(accessibilityEvent0.getEventType() == 0x20) {
            accessibilityEvent0.setSource(this);
            accessibilityEvent0.setClassName(this.getClass().getName());
            accessibilityEvent0.setPackageName("com.pdf.editor.viewer.pdfreader.pdfviewer");
            accessibilityEvent0.setContentDescription(this.n);
            return;
        }
        super.onInitializeAccessibilityEvent(accessibilityEvent0);
    }

    @Override  // android.view.ViewGroup
    public final void onLayout(boolean z, int v, int v1, int v2, int v3) {
        int v4 = ViewCompat.getLayoutDirection(this) == 1 ? v2 - v - this.getPaddingRight() : this.getPaddingLeft();
        int v5 = this.getPaddingTop();
        int v6 = v3 - v1 - this.getPaddingTop() - this.getPaddingBottom();
        if(this.h != null && this.h.getVisibility() != 8) {
            ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)this.h.getLayoutParams();
            int v7 = viewGroup$MarginLayoutParams0.leftMargin;
            v4 = v4 - viewGroup$MarginLayoutParams0.rightMargin + AbsActionBarView.c(this.h, true, v4 - viewGroup$MarginLayoutParams0.rightMargin, v5, v6) - v7;
        }
        if(this.o != null && this.j == null && this.o.getVisibility() != 8) {
            v4 += AbsActionBarView.c(this.o, true, v4, v5, v6);
        }
        View view0 = this.j;
        if(view0 != null) {
            AbsActionBarView.c(view0, true, v4, v5, v6);
        }
        this.getPaddingLeft();
    }

    @Override  // android.view.View
    public final void onMeasure(int v, int v1) {
        int v2 = 0x40000000;
        if(View.MeasureSpec.getMode(v) != 0x40000000) {
            throw new IllegalStateException(this.getClass().getSimpleName() + " can only be used with android:layout_width=\"match_parent\" (or fill_parent)");
        }
        if(View.MeasureSpec.getMode(v1) == 0) {
            throw new IllegalStateException(this.getClass().getSimpleName() + " can only be used with android:layout_height=\"wrap_content\"");
        }
        int v3 = View.MeasureSpec.getSize(v);
        int v4 = this.b > 0 ? this.b : View.MeasureSpec.getSize(v1);
        int v5 = this.getPaddingTop();
        int v6 = this.getPaddingBottom() + v5;
        int v7 = v3 - this.getPaddingLeft() - this.getPaddingRight();
        int v8 = v4 - v6;
        int v9 = View.MeasureSpec.makeMeasureSpec(v8, 0x80000000);
        View view0 = this.h;
        if(view0 != null) {
            view0.measure(View.MeasureSpec.makeMeasureSpec(v7, 0x80000000), v9);
            int v11 = Math.max(0, v7 - view0.getMeasuredWidth());
            ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0 = (ViewGroup.MarginLayoutParams)this.h.getLayoutParams();
            v7 = v11 - (viewGroup$MarginLayoutParams0.leftMargin + viewGroup$MarginLayoutParams0.rightMargin);
        }
        LinearLayout linearLayout0 = this.o;
        if(linearLayout0 != null && this.j == null) {
            if(this.p) {
                this.o.measure(0, v9);
                int v12 = this.o.getMeasuredWidth();
                boolean z = v12 <= v7;
                if(z) {
                    v7 -= v12;
                }
                this.o.setVisibility((z ? 0 : 8));
            }
            else {
                linearLayout0.measure(View.MeasureSpec.makeMeasureSpec(v7, 0x80000000), v9);
                v7 = Math.max(0, v7 - linearLayout0.getMeasuredWidth());
            }
        }
        View view1 = this.j;
        if(view1 != null) {
            ViewGroup.LayoutParams viewGroup$LayoutParams0 = view1.getLayoutParams();
            int v13 = viewGroup$LayoutParams0.width;
            if(v13 >= 0) {
                v7 = Math.min(v13, v7);
            }
            int v14 = viewGroup$LayoutParams0.height;
            if(v14 == -2) {
                v2 = 0x80000000;
            }
            if(v14 >= 0) {
                v8 = Math.min(v14, v8);
            }
            this.j.measure(View.MeasureSpec.makeMeasureSpec(v7, (v13 == -2 ? 0x80000000 : 0x40000000)), View.MeasureSpec.makeMeasureSpec(v8, v2));
        }
        if(this.b <= 0) {
            int v15 = this.getChildCount();
            v4 = 0;
            for(int v10 = 0; v10 < v15; ++v10) {
                int v16 = this.getChildAt(v10).getMeasuredHeight() + v6;
                if(v16 > v4) {
                    v4 = v16;
                }
            }
        }
        this.setMeasuredDimension(v3, v4);
    }

    @Override  // android.supportv1.v7.widget.AbsActionBarView
    public void setContentHeight(int v) {
        this.b = v;
    }

    public void setCustomView(View view0) {
        View view1 = this.j;
        if(view1 != null) {
            this.removeView(view1);
        }
        this.j = view0;
        if(view0 != null) {
            LinearLayout linearLayout0 = this.o;
            if(linearLayout0 != null) {
                this.removeView(linearLayout0);
                this.o = null;
            }
        }
        if(view0 != null) {
            this.addView(view0);
        }
        this.requestLayout();
    }

    public void setSubtitle(CharSequence charSequence0) {
        this.k = charSequence0;
        this.e();
    }

    public void setTitle(CharSequence charSequence0) {
        this.n = charSequence0;
        this.e();
    }

    public void setTitleOptional(boolean z) {
        if(z != this.p) {
            this.requestLayout();
        }
        this.p = z;
    }

    @Override  // android.supportv1.v7.widget.AbsActionBarView
    public void setVisibility(int v) {
        super.setVisibility(v);
    }

    @Override  // android.view.ViewGroup
    public final boolean shouldDelayChildPressedState() {
        return false;
    }
}


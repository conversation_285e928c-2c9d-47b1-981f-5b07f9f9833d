package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.graphics.ColorFilter;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable.ConstantState;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.os.Build.VERSION;
import android.supportv1.c.a.c;
import android.supportv1.c.a.i;
import android.supportv1.v4.content.ContextCompat;
import android.supportv1.v4.graphics.ColorUtils;
import android.supportv1.v4.graphics.drawable.DrawableCompat;
import android.supportv1.v4.util.ArrayMap;
import android.supportv1.v4.util.LongSparseArray;
import android.supportv1.v4.util.LruCache;
import android.supportv1.v4.util.SparseArrayCompat;
import android.supportv1.v7.appcompat.R.attr;
import android.supportv1.v7.appcompat.R.color;
import android.supportv1.v7.appcompat.R.drawable;
import android.supportv1.v7.content.res.AppCompatResources;
import android.supportv1.v7.graphics.drawable.AnimatedStateListDrawableCompat;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.util.Xml;
import java.lang.ref.WeakReference;
import java.util.WeakHashMap;

public final class AppCompatDrawableManager {
    static class AsldcInflateDelegate implements InflateDelegate {
        @Override  // android.supportv1.v7.widget.AppCompatDrawableManager$InflateDelegate
        public final Drawable a(Context context0, XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            try {
                return AnimatedStateListDrawableCompat.g(context0, resources$Theme0, context0.getResources(), attributeSet0, xmlResourceParser0);
            }
            catch(Exception unused_ex) {
                return null;
            }
        }
    }

    static class AvdcInflateDelegate implements InflateDelegate {
        @Override  // android.supportv1.v7.widget.AppCompatDrawableManager$InflateDelegate
        public final Drawable a(Context context0, XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            try {
                return c.a(context0, resources$Theme0, context0.getResources(), attributeSet0, xmlResourceParser0);
            }
            catch(Exception unused_ex) {
                return null;
            }
        }
    }

    static class ColorFilterLruCache extends LruCache {
        public ColorFilterLruCache() {
            super(6);
        }
    }

    interface InflateDelegate {
        Drawable a(Context arg1, XmlResourceParser arg2, AttributeSet arg3, Resources.Theme arg4);
    }

    static class VdcInflateDelegate implements InflateDelegate {
        @Override  // android.supportv1.v7.widget.AppCompatDrawableManager$InflateDelegate
        public final Drawable a(Context context0, XmlResourceParser xmlResourceParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) {
            try {
                return i.b(context0.getResources(), xmlResourceParser0, attributeSet0, resources$Theme0);
            }
            catch(Exception unused_ex) {
                return null;
            }
        }
    }

    public ArrayMap a;
    public final WeakHashMap b;
    public boolean c;
    public SparseArrayCompat d;
    public WeakHashMap e;
    public TypedValue f;
    public static final int[] g;
    public static final int[] h;
    public static final int[] i;
    public static final ColorFilterLruCache j;
    public static final PorterDuff.Mode k;
    public static AppCompatDrawableManager l;
    public static final int[] m;
    public static final int[] n;
    public static final int[] o;

    static {
        AppCompatDrawableManager.k = PorterDuff.Mode.SRC_IN;
        AppCompatDrawableManager.j = new ColorFilterLruCache();
        AppCompatDrawableManager.i = new int[]{R.drawable.abc_textfield_search_default_mtrl_alpha, R.drawable.abc_textfield_default_mtrl_alpha, R.drawable.abc_ab_share_pack_mtrl_alpha};
        AppCompatDrawableManager.n = new int[]{R.drawable.abc_ic_commit_search_api_mtrl_alpha, R.drawable.abc_seekbar_tick_mark_material, R.drawable.abc_ic_menu_share_mtrl_alpha, R.drawable.abc_ic_menu_copy_mtrl_am_alpha, R.drawable.abc_ic_menu_cut_mtrl_alpha, R.drawable.abc_ic_menu_selectall_mtrl_alpha, R.drawable.abc_ic_menu_paste_mtrl_am_alpha};
        AppCompatDrawableManager.h = new int[]{R.drawable.abc_textfield_activated_mtrl_alpha, R.drawable.abc_textfield_search_activated_mtrl_alpha, R.drawable.abc_cab_background_top_mtrl_alpha, R.drawable.abc_text_cursor_material, R.drawable.abc_text_select_handle_left_mtrl_dark, R.drawable.abc_text_select_handle_middle_mtrl_dark, R.drawable.abc_text_select_handle_right_mtrl_dark, R.drawable.abc_text_select_handle_left_mtrl_light, R.drawable.abc_text_select_handle_middle_mtrl_light, R.drawable.abc_text_select_handle_right_mtrl_light};
        AppCompatDrawableManager.g = new int[]{R.drawable.abc_popup_background_mtrl_mult, R.drawable.abc_cab_background_internal_bg, R.drawable.abc_menu_hardkey_panel_mtrl_mult};
        AppCompatDrawableManager.o = new int[]{R.drawable.abc_tab_indicator_material, R.drawable.abc_textfield_search_material};
        AppCompatDrawableManager.m = new int[]{R.drawable.abc_btn_check_material, R.drawable.abc_btn_radio_material};
    }

    public AppCompatDrawableManager() {
        this.b = new WeakHashMap(0);
    }

    public final void a(String s, InflateDelegate appCompatDrawableManager$InflateDelegate0) {
        if(this.a == null) {
            this.a = new ArrayMap();
        }
        this.a.put(s, appCompatDrawableManager$InflateDelegate0);
    }

    public final void b(Context context0, long v, Drawable drawable0) {
        __monitor_enter(this);
        try {
            Drawable.ConstantState drawable$ConstantState0 = drawable0.getConstantState();
            if(drawable$ConstantState0 != null) {
                LongSparseArray longSparseArray0 = (LongSparseArray)this.b.get(context0);
                if(longSparseArray0 == null) {
                    longSparseArray0 = new LongSparseArray();
                    this.b.put(context0, longSparseArray0);
                }
                longSparseArray0.put(v, new WeakReference(drawable$ConstantState0));
                goto label_12;
            }
            goto label_14;
        }
        catch(Throwable throwable0) {
        }
        __monitor_exit(this);
        throw throwable0;
    label_12:
        __monitor_exit(this);
        return;
    label_14:
        __monitor_exit(this);
    }

    public static boolean c(int v, int[] arr_v) {
        for(int v1 = 0; v1 < arr_v.length; ++v1) {
            if(arr_v[v1] == v) {
                return true;
            }
        }
        return false;
    }

    public final void d(Context context0) {
        if(this.c) {
            return;
        }
        this.c = true;
        Drawable drawable0 = this.l(context0, R.drawable.abc_vector_test);
        if(drawable0 != null && (drawable0 instanceof i || "android.graphics.drawable.VectorDrawable".equals(drawable0.getClass().getName()))) {
            return;
        }
        this.c = false;
        throw new IllegalStateException("This app has been built with an incorrect configuration. Please configure your build for VectorDrawableCompat.");
    }

    public static ColorStateList e(int v, Context context0) {
        int v1 = ThemeUtils.b(R.attr.colorControlHighlight, context0);
        int v2 = ThemeUtils.a(R.attr.colorButtonNormal, context0);
        int v3 = ColorUtils.compositeColors(v1, v);
        int v4 = ColorUtils.compositeColors(v1, v);
        return new ColorStateList(new int[][]{ThemeUtils.b, ThemeUtils.e, ThemeUtils.d, ThemeUtils.c}, new int[]{v2, v3, v4, v});
    }

    public static ColorStateList f(Context context0) {
        return AppCompatDrawableManager.e(ThemeUtils.b(R.attr.colorAccent, context0), context0);
    }

    public static ColorStateList g(Context context0) {
        return AppCompatDrawableManager.e(ThemeUtils.b(R.attr.colorButtonNormal, context0), context0);
    }

    public final Drawable h(int v, Context context0) {
        if(this.f == null) {
            this.f = new TypedValue();
        }
        TypedValue typedValue0 = this.f;
        context0.getResources().getValue(v, typedValue0, true);
        long v1 = ((long)typedValue0.assetCookie) << 0x20 | ((long)typedValue0.data);
        Drawable drawable0 = this.k(context0, v1);
        if(drawable0 != null) {
            return drawable0;
        }
        if(v == R.drawable.abc_cab_background_top_material) {
            drawable0 = new LayerDrawable(new Drawable[]{this.l(context0, R.drawable.abc_cab_background_internal_bg), this.l(context0, R.drawable.abc_cab_background_top_mtrl_alpha)});
        }
        if(drawable0 != null) {
            drawable0.setChangingConfigurations(typedValue0.changingConfigurations);
            this.b(context0, v1, drawable0);
        }
        return drawable0;
    }

    public static ColorStateList i(Context context0) {
        int[][] arr2_v = new int[3][];
        int[] arr_v = new int[3];
        ColorStateList colorStateList0 = ThemeUtils.c(R.attr.colorSwitchThumbNormal, context0);
        if(colorStateList0 != null && colorStateList0.isStateful()) {
            arr2_v[0] = ThemeUtils.b;
            arr_v[0] = colorStateList0.getColorForState(ThemeUtils.b, 0);
            arr2_v[1] = ThemeUtils.a;
            arr_v[1] = ThemeUtils.b(R.attr.colorControlActivated, context0);
            arr2_v[2] = ThemeUtils.c;
            arr_v[2] = colorStateList0.getDefaultColor();
            return new ColorStateList(arr2_v, arr_v);
        }
        arr2_v[0] = ThemeUtils.b;
        arr_v[0] = ThemeUtils.a(R.attr.colorSwitchThumbNormal, context0);
        arr2_v[1] = ThemeUtils.a;
        arr_v[1] = ThemeUtils.b(R.attr.colorControlActivated, context0);
        arr2_v[2] = ThemeUtils.c;
        arr_v[2] = ThemeUtils.b(R.attr.colorSwitchThumbNormal, context0);
        return new ColorStateList(arr2_v, arr_v);
    }

    public static AppCompatDrawableManager j() {
        synchronized(AppCompatDrawableManager.class) {
            if(AppCompatDrawableManager.l == null) {
                AppCompatDrawableManager appCompatDrawableManager0 = new AppCompatDrawableManager();
                AppCompatDrawableManager.l = appCompatDrawableManager0;
                if(Build.VERSION.SDK_INT < 24) {
                    appCompatDrawableManager0.a("vector", new VdcInflateDelegate());  // 初始化器: Ljava/lang/Object;-><init>()V
                    appCompatDrawableManager0.a("animated-vector", new AvdcInflateDelegate());  // 初始化器: Ljava/lang/Object;-><init>()V
                    appCompatDrawableManager0.a("animated-selector", new AsldcInflateDelegate());  // 初始化器: Ljava/lang/Object;-><init>()V
                }
            }
            return AppCompatDrawableManager.l;
        }
    }

    public final Drawable k(Context context0, long v) {
        synchronized(this) {
            LongSparseArray longSparseArray0 = (LongSparseArray)this.b.get(context0);
            if(longSparseArray0 == null) {
                return null;
            }
            WeakReference weakReference0 = (WeakReference)longSparseArray0.get(v);
            if(weakReference0 != null) {
                Drawable.ConstantState drawable$ConstantState0 = (Drawable.ConstantState)weakReference0.get();
                if(drawable$ConstantState0 != null) {
                    return drawable$ConstantState0.newDrawable(context0.getResources());
                }
                longSparseArray0.delete(v);
            }
            return null;
        }
    }

    public final Drawable l(Context context0, int v) {
        synchronized(this) {
            return this.m(context0, v, false);
        }
    }

    public final Drawable m(Context context0, int v, boolean z) {
        synchronized(this) {
            this.d(context0);
            Drawable drawable0 = null;
            if(this.a != null && !this.a.isEmpty()) {
                SparseArrayCompat sparseArrayCompat0 = this.d;
                if(sparseArrayCompat0 == null) {
                    this.d = new SparseArrayCompat();
                label_11:
                    if(this.f == null) {
                        this.f = new TypedValue();
                    }
                    TypedValue typedValue0 = this.f;
                    Resources resources0 = context0.getResources();
                    resources0.getValue(v, typedValue0, true);
                    long v2 = ((long)typedValue0.assetCookie) << 0x20 | ((long)typedValue0.data);
                    Drawable drawable1 = this.k(context0, v2);
                    if(drawable1 == null) {
                        if(typedValue0.string != null && typedValue0.string.toString().endsWith(".xml")) {
                            try {
                                XmlResourceParser xmlResourceParser0 = resources0.getXml(v);
                                AttributeSet attributeSet0 = Xml.asAttributeSet(xmlResourceParser0);
                                do {
                                    int v3 = xmlResourceParser0.next();
                                }
                                while(v3 != 1 && v3 != 2);
                                if(v3 == 2) {
                                    String s1 = xmlResourceParser0.getName();
                                    this.d.append(v, s1);
                                    InflateDelegate appCompatDrawableManager$InflateDelegate0 = (InflateDelegate)this.a.get(s1);
                                    if(appCompatDrawableManager$InflateDelegate0 != null) {
                                        drawable1 = appCompatDrawableManager$InflateDelegate0.a(context0, xmlResourceParser0, attributeSet0, context0.getTheme());
                                    }
                                    if(drawable1 != null) {
                                        drawable1.setChangingConfigurations(typedValue0.changingConfigurations);
                                        this.b(context0, v2, drawable1);
                                    }
                                }
                            }
                            catch(Exception unused_ex) {
                            }
                        }
                        drawable0 = drawable1;
                        if(drawable0 == null) {
                            this.d.append(v, "appcompat_skip_skip");
                        }
                    }
                    else {
                        drawable0 = drawable1;
                    }
                }
                else {
                    String s = (String)sparseArrayCompat0.get(v);
                    if(!"appcompat_skip_skip".equals(s) && (s == null || this.a.get(s) != null)) {
                        goto label_11;
                    }
                }
            }
            if(drawable0 == null) {
                drawable0 = this.h(v, context0);
            }
            if(drawable0 == null) {
                drawable0 = ContextCompat.getDrawable(context0, v);
            }
            if(drawable0 != null) {
                drawable0 = this.r(context0, v, z, drawable0);
            }
            if(drawable0 != null) {
                DrawableUtils.b(drawable0);
            }
            return drawable0;
        }
    }

    public static PorterDuffColorFilter n(int v, PorterDuff.Mode porterDuff$Mode0) {
        synchronized(AppCompatDrawableManager.class) {
            ColorFilterLruCache appCompatDrawableManager$ColorFilterLruCache0 = AppCompatDrawableManager.j;
            appCompatDrawableManager$ColorFilterLruCache0.getClass();
            int v2 = (v + 0x1F) * 0x1F;
            PorterDuffColorFilter porterDuffColorFilter0 = (PorterDuffColorFilter)appCompatDrawableManager$ColorFilterLruCache0.get(((int)(porterDuff$Mode0.hashCode() + v2)));
            if(porterDuffColorFilter0 == null) {
                porterDuffColorFilter0 = new PorterDuffColorFilter(v, porterDuff$Mode0);
                PorterDuffColorFilter porterDuffColorFilter1 = (PorterDuffColorFilter)appCompatDrawableManager$ColorFilterLruCache0.put(((int)(porterDuff$Mode0.hashCode() + v2)), porterDuffColorFilter0);
            }
            return porterDuffColorFilter0;
        }
    }

    public final ColorStateList o(int v, Context context0) {
        ColorStateList colorStateList0;
        synchronized(this) {
            WeakHashMap weakHashMap0 = this.e;
            if(weakHashMap0 == null) {
                colorStateList0 = null;
            }
            else {
                SparseArrayCompat sparseArrayCompat0 = (SparseArrayCompat)weakHashMap0.get(context0);
                colorStateList0 = sparseArrayCompat0 == null ? null : ((ColorStateList)sparseArrayCompat0.get(v));
            }
            if(colorStateList0 == null) {
                if(v == R.drawable.abc_edit_text_material) {
                    colorStateList0 = AppCompatResources.b(context0, R.color.abc_tint_edittext);
                }
                else if(v == R.drawable.abc_switch_track_mtrl_alpha) {
                    colorStateList0 = AppCompatResources.b(context0, R.color.abc_tint_switch_track);
                }
                else if(v == R.drawable.abc_switch_thumb_material) {
                    colorStateList0 = AppCompatDrawableManager.i(context0);
                }
                else if(v == R.drawable.abc_btn_default_mtrl_shape) {
                    colorStateList0 = AppCompatDrawableManager.g(context0);
                }
                else if(v == R.drawable.abc_btn_borderless_material) {
                    colorStateList0 = AppCompatDrawableManager.e(0, context0);
                }
                else if(v == R.drawable.abc_btn_colored_material) {
                    colorStateList0 = AppCompatDrawableManager.f(context0);
                }
                else if(v == R.drawable.abc_spinner_mtrl_am_alpha || v == R.drawable.abc_spinner_textfield_background_material) {
                    colorStateList0 = AppCompatResources.b(context0, R.color.abc_tint_spinner);
                }
                else if(AppCompatDrawableManager.c(v, AppCompatDrawableManager.n)) {
                    colorStateList0 = ThemeUtils.c(R.attr.colorControlNormal, context0);
                }
                else if(AppCompatDrawableManager.c(v, AppCompatDrawableManager.o)) {
                    colorStateList0 = AppCompatResources.b(context0, R.color.abc_tint_default);
                }
                else if(AppCompatDrawableManager.c(v, AppCompatDrawableManager.m)) {
                    colorStateList0 = AppCompatResources.b(context0, R.color.abc_tint_btn_checkable);
                }
                else if(v == R.drawable.abc_seekbar_thumb_material) {
                    colorStateList0 = AppCompatResources.b(context0, R.color.abc_tint_seek_thumb);
                }
                if(colorStateList0 != null) {
                    if(this.e == null) {
                        this.e = new WeakHashMap();
                    }
                    SparseArrayCompat sparseArrayCompat1 = (SparseArrayCompat)this.e.get(context0);
                    if(sparseArrayCompat1 == null) {
                        sparseArrayCompat1 = new SparseArrayCompat();
                        this.e.put(context0, sparseArrayCompat1);
                    }
                    sparseArrayCompat1.append(v, colorStateList0);
                }
            }
            return colorStateList0;
        }
    }

    public static PorterDuff.Mode p(int v) {
        return v == R.drawable.abc_switch_thumb_material ? PorterDuff.Mode.MULTIPLY : null;
    }

    public static void q(Drawable drawable0, int v, PorterDuff.Mode porterDuff$Mode0) {
        if(DrawableUtils.a(drawable0)) {
            drawable0 = drawable0.mutate();
        }
        if(porterDuff$Mode0 == null) {
            porterDuff$Mode0 = AppCompatDrawableManager.k;
        }
        drawable0.setColorFilter(AppCompatDrawableManager.n(v, porterDuff$Mode0));
    }

    public final Drawable r(Context context0, int v, boolean z, Drawable drawable0) {
        int v2;
        Drawable drawable2;
        PorterDuff.Mode porterDuff$Mode1;
        LayerDrawable layerDrawable0;
        ColorStateList colorStateList0 = this.o(v, context0);
        if(colorStateList0 == null) {
            if(v == R.drawable.abc_seekbar_track_material) {
                layerDrawable0 = (LayerDrawable)drawable0;
                Drawable drawable1 = layerDrawable0.findDrawableByLayerId(0x1020000);
                int v1 = ThemeUtils.b(R.attr.colorControlNormal, context0);
                porterDuff$Mode1 = AppCompatDrawableManager.k;
                AppCompatDrawableManager.q(drawable1, v1, porterDuff$Mode1);
                drawable2 = layerDrawable0.findDrawableByLayerId(0x102000F);
                v2 = R.attr.colorControlNormal;
            }
            else {
                if(v != R.drawable.abc_ratingbar_material && v != R.drawable.abc_ratingbar_indicator_material && v != R.drawable.abc_ratingbar_small_material) {
                    return AppCompatDrawableManager.t(context0, v, drawable0) || !z ? drawable0 : null;
                }
                layerDrawable0 = (LayerDrawable)drawable0;
                Drawable drawable3 = layerDrawable0.findDrawableByLayerId(0x1020000);
                int v3 = ThemeUtils.a(R.attr.colorControlNormal, context0);
                porterDuff$Mode1 = AppCompatDrawableManager.k;
                AppCompatDrawableManager.q(drawable3, v3, porterDuff$Mode1);
                drawable2 = layerDrawable0.findDrawableByLayerId(0x102000F);
                v2 = R.attr.colorControlActivated;
            }
            AppCompatDrawableManager.q(drawable2, ThemeUtils.b(v2, context0), porterDuff$Mode1);
            AppCompatDrawableManager.q(layerDrawable0.findDrawableByLayerId(0x102000D), ThemeUtils.b(R.attr.colorControlActivated, context0), porterDuff$Mode1);
        }
        else {
            if(DrawableUtils.a(drawable0)) {
                drawable0 = drawable0.mutate();
            }
            drawable0 = DrawableCompat.wrap(drawable0);
            DrawableCompat.setTintList(drawable0, colorStateList0);
            PorterDuff.Mode porterDuff$Mode0 = AppCompatDrawableManager.p(v);
            if(porterDuff$Mode0 != null) {
                DrawableCompat.setTintMode(drawable0, porterDuff$Mode0);
                return drawable0;
            }
        }
        return drawable0;
    }

    public static void s(Drawable drawable0, TintInfo tintInfo0, int[] arr_v) {
        if(DrawableUtils.a(drawable0) && drawable0.mutate() != drawable0) {
            return;
        }
        ColorFilter colorFilter0 = null;
        boolean z = tintInfo0.a;
        if(z || tintInfo0.b) {
            ColorStateList colorStateList0 = z ? tintInfo0.c : null;
            PorterDuff.Mode porterDuff$Mode0 = tintInfo0.b ? tintInfo0.d : AppCompatDrawableManager.k;
            if(colorStateList0 != null && porterDuff$Mode0 != null) {
                colorFilter0 = AppCompatDrawableManager.n(colorStateList0.getColorForState(arr_v, 0), porterDuff$Mode0);
            }
            drawable0.setColorFilter(colorFilter0);
        }
        else {
            drawable0.clearColorFilter();
        }
        if(Build.VERSION.SDK_INT <= 23) {
            drawable0.invalidateSelf();
        }
    }

    public static boolean t(Context context0, int v, Drawable drawable0) {
        boolean z;
        int v2;
        int v1;
        PorterDuff.Mode porterDuff$Mode0 = AppCompatDrawableManager.k;
        if(AppCompatDrawableManager.c(v, AppCompatDrawableManager.i)) {
            v1 = R.attr.colorControlNormal;
            v2 = -1;
            z = true;
        }
        else if(AppCompatDrawableManager.c(v, AppCompatDrawableManager.h)) {
            v1 = R.attr.colorControlActivated;
            v2 = -1;
            z = true;
        }
        else if(AppCompatDrawableManager.c(v, AppCompatDrawableManager.g)) {
            porterDuff$Mode0 = PorterDuff.Mode.MULTIPLY;
            v1 = 0x1010031;
            v2 = -1;
            z = true;
        }
        else if(v == R.drawable.abc_list_divider_mtrl_alpha) {
            v2 = 41;
            v1 = 0x1010030;
            z = true;
        }
        else if(v == R.drawable.abc_dialog_material_background) {
            v1 = 0x1010031;
            v2 = -1;
            z = true;
        }
        else {
            v2 = -1;
            v1 = 0;
            z = false;
        }
        if(z) {
            if(DrawableUtils.a(drawable0)) {
                drawable0 = drawable0.mutate();
            }
            drawable0.setColorFilter(AppCompatDrawableManager.n(ThemeUtils.b(v1, context0), porterDuff$Mode0));
            if(v2 != -1) {
                drawable0.setAlpha(v2);
            }
            return true;
        }
        return false;
    }
}


package android.supportv1.v7.widget;

import android.content.Context;
import android.supportv1.v4.widget.PopupWindowCompat;
import android.supportv1.v7.appcompat.R.styleable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.PopupWindow;

class AppCompatPopupWindow extends PopupWindow {
    public AppCompatPopupWindow(Context context0, AttributeSet attributeSet0, int v, int v1) {
        super(context0, attributeSet0, v, v1);
        this.a(context0, attributeSet0, v, v1);
    }

    public final void a(Context context0, AttributeSet attributeSet0, int v, int v1) {
        TintTypedArray tintTypedArray0 = TintTypedArray.o(context0, attributeSet0, R.styleable.PopupWindow, v, v1);
        if(tintTypedArray0.l(R.styleable.PopupWindow_overlapAnchor)) {
            PopupWindowCompat.setOverlapAnchor(this, tintTypedArray0.a(R.styleable.PopupWindow_overlapAnchor));
        }
        this.setBackgroundDrawable(tintTypedArray0.d(R.styleable.PopupWindow_android_popupBackground));
        tintTypedArray0.p();
    }

    @Override  // android.widget.PopupWindow
    public final void showAsDropDown(View view0, int v, int v1) {
        super.showAsDropDown(view0, v, v1);
    }

    @Override  // android.widget.PopupWindow
    public final void showAsDropDown(View view0, int v, int v1, int v2) {
        super.showAsDropDown(view0, v, v1, v2);
    }

    @Override  // android.widget.PopupWindow
    public final void update(View view0, int v, int v1, int v2, int v3) {
        super.update(view0, v, v1, v2, v3);
    }
}


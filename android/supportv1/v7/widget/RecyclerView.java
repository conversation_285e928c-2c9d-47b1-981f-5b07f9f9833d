package android.supportv1.v7.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.database.Observable;
import android.graphics.Rect;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.supportv1.v4.os.TraceCompat;
import android.supportv1.v4.view.NestedScrollingChild2;
import android.supportv1.v4.view.ScrollingView;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.view.accessibility.AccessibilityNodeInfoCompat.CollectionItemInfoCompat;
import android.supportv1.v4.view.accessibility.AccessibilityNodeInfoCompat;
import android.supportv1.v7.recyclerview.R.styleable;
import android.util.AttributeSet;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import androidx.work.impl.model.c;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public abstract class RecyclerView extends ViewGroup implements NestedScrollingChild2, ScrollingView {
    public static abstract class Adapter {
        private boolean mHasStableIds;
        private final AdapterDataObservable mObservable;

        public Adapter() {
            this.mObservable = new AdapterDataObservable();  // 初始化器: Landroid/database/Observable;-><init>()V
            this.mHasStableIds = false;
        }

        public final void bindViewHolder(ViewHolder recyclerView$ViewHolder0, int v) {
            recyclerView$ViewHolder0.mPosition = v;
            if(this.hasStableIds()) {
                recyclerView$ViewHolder0.mItemId = -1L;
            }
            recyclerView$ViewHolder0.setFlags(1, 0x207);
            TraceCompat.beginSection("RV OnBindView");
            this.onBindViewHolder(recyclerView$ViewHolder0, v, recyclerView$ViewHolder0.getUnmodifiedPayloads());
            recyclerView$ViewHolder0.clearPayload();
            ViewGroup.LayoutParams viewGroup$LayoutParams0 = recyclerView$ViewHolder0.itemView.getLayoutParams();
            if(viewGroup$LayoutParams0 instanceof LayoutParams) {
                ((LayoutParams)viewGroup$LayoutParams0).getClass();
            }
            TraceCompat.endSection();
        }

        public final ViewHolder createViewHolder(ViewGroup viewGroup0, int v) {
            try {
                TraceCompat.beginSection("RV CreateView");
                ViewHolder recyclerView$ViewHolder0 = this.onCreateViewHolder(viewGroup0, v);
                if(recyclerView$ViewHolder0.itemView.getParent() == null) {
                    recyclerView$ViewHolder0.mItemViewType = v;
                    return recyclerView$ViewHolder0;
                }
            }
            finally {
                TraceCompat.endSection();
            }
            throw new IllegalStateException("ViewHolder views must not be attached when created. Ensure that you are not passing \'true\' to the attachToRoot parameter of LayoutInflater.inflate(..., boolean attachToRoot)");
        }

        public long getItemId(int v) [...] // Inlined contents

        public int getItemViewType(int v) {
            return 0;
        }

        public final boolean hasObservers() {
            return this.mObservable.a();
        }

        public final boolean hasStableIds() {
            return this.mHasStableIds;
        }

        public final void notifyDataSetChanged() {
            this.mObservable.b();
        }

        public final void notifyItemChanged(int v) {
            this.mObservable.d();
        }

        public final void notifyItemChanged(int v, Object object0) {
            this.mObservable.d();
        }

        public final void notifyItemInserted(int v) {
            this.mObservable.e();
        }

        public final void notifyItemMoved(int v, int v1) {
            this.mObservable.c();
        }

        public final void notifyItemRangeChanged(int v, int v1) {
            this.mObservable.d();
        }

        public final void notifyItemRangeChanged(int v, int v1, Object object0) {
            this.mObservable.d();
        }

        public final void notifyItemRangeInserted(int v, int v1) {
            this.mObservable.e();
        }

        public final void notifyItemRangeRemoved(int v, int v1) {
            this.mObservable.f();
        }

        public final void notifyItemRemoved(int v) {
            this.mObservable.f();
        }

        public void onAttachedToRecyclerView(RecyclerView recyclerView0) {
        }

        public abstract void onBindViewHolder(ViewHolder arg1, int arg2);

        public void onBindViewHolder(ViewHolder recyclerView$ViewHolder0, int v, List list0) {
            this.onBindViewHolder(recyclerView$ViewHolder0, v);
        }

        public abstract ViewHolder onCreateViewHolder(ViewGroup arg1, int arg2);

        public void onDetachedFromRecyclerView(RecyclerView recyclerView0) {
        }

        public boolean onFailedToRecycleView(ViewHolder recyclerView$ViewHolder0) {
            return false;
        }

        public void onViewAttachedToWindow(ViewHolder recyclerView$ViewHolder0) {
        }

        public void onViewDetachedFromWindow(ViewHolder recyclerView$ViewHolder0) {
        }

        public void onViewRecycled(ViewHolder recyclerView$ViewHolder0) {
        }

        public void registerAdapterDataObserver(AdapterDataObserver recyclerView$AdapterDataObserver0) {
            this.mObservable.registerObserver(recyclerView$AdapterDataObserver0);
        }

        public void setHasStableIds(boolean z) {
            if(this.hasObservers()) {
                throw new IllegalStateException("Cannot change whether this adapter has stable IDs while the adapter has registered observers.");
            }
            this.mHasStableIds = z;
        }

        public void unregisterAdapterDataObserver(AdapterDataObserver recyclerView$AdapterDataObserver0) {
            this.mObservable.unregisterObserver(recyclerView$AdapterDataObserver0);
        }
    }

    static class AdapterDataObservable extends Observable {
        public final boolean a() {
            return !this.mObservers.isEmpty();
        }

        public final void b() {
            int v = this.mObservers.size();
            if(v - 1 < 0) {
                return;
            }
            c.v(this.mObservers.get(v - 1));
            throw null;
        }

        public final void c() {
            int v = this.mObservers.size();
            if(v - 1 < 0) {
                return;
            }
            c.v(this.mObservers.get(v - 1));
            throw null;
        }

        public final void d() {
            int v = this.mObservers.size();
            if(v - 1 < 0) {
                return;
            }
            c.v(this.mObservers.get(v - 1));
            throw null;
        }

        public final void e() {
            int v = this.mObservers.size();
            if(v - 1 < 0) {
                return;
            }
            c.v(this.mObservers.get(v - 1));
            throw null;
        }

        public final void f() {
            int v = this.mObservers.size();
            if(v - 1 < 0) {
                return;
            }
            c.v(this.mObservers.get(v - 1));
            throw null;
        }
    }

    public static abstract class AdapterDataObserver {
    }

    public static abstract class LayoutManager {
        public interface LayoutPrefetchRegistry {
            void a();
        }

        public static class Properties {
        }

        boolean mAutoMeasure;
        ChildHelper mChildHelper;
        private int mHeight;
        private int mHeightMode;
        ViewBoundsCheck mHorizontalBoundCheck;
        private final Callback mHorizontalBoundCheckCallback;
        boolean mIsAttachedToWindow;
        private boolean mItemPrefetchEnabled;
        private boolean mMeasurementCacheEnabled;
        int mPrefetchMaxCountObserved;
        boolean mPrefetchMaxObservedInInitialPrefetch;
        RecyclerView mRecyclerView;
        boolean mRequestedSimpleAnimations;
        SmoothScroller mSmoothScroller;
        ViewBoundsCheck mVerticalBoundCheck;
        private final Callback mVerticalBoundCheckCallback;
        private int mWidth;
        private int mWidthMode;

        public LayoutManager() {
            android.supportv1.v7.widget.RecyclerView.LayoutManager.1 recyclerView$LayoutManager$10 = new Callback() {
                public final LayoutManager a;

                {
                    this.a = recyclerView$LayoutManager0;
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final int a(View view0) {
                    LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                    return this.a.getDecoratedLeft(view0) - recyclerView$LayoutParams0.leftMargin;
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final int b() {
                    return 0;
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final int c() {
                    return this.a.getWidth();
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final View d(int v) {
                    return null;
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final int e(View view0) {
                    LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                    return this.a.getDecoratedRight(view0) + recyclerView$LayoutParams0.rightMargin;
                }
            };
            this.mHorizontalBoundCheckCallback = recyclerView$LayoutManager$10;
            android.supportv1.v7.widget.RecyclerView.LayoutManager.2 recyclerView$LayoutManager$20 = new Callback() {
                public final LayoutManager a;

                {
                    this.a = recyclerView$LayoutManager0;
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final int a(View view0) {
                    LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                    return this.a.getDecoratedTop(view0) - recyclerView$LayoutParams0.topMargin;
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final int b() {
                    return 0;
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final int c() {
                    return this.a.getHeight();
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final View d(int v) {
                    return null;
                }

                @Override  // android.supportv1.v7.widget.ViewBoundsCheck$Callback
                public final int e(View view0) {
                    LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                    return this.a.getDecoratedBottom(view0) + recyclerView$LayoutParams0.bottomMargin;
                }
            };
            this.mVerticalBoundCheckCallback = recyclerView$LayoutManager$20;
            this.mHorizontalBoundCheck = new ViewBoundsCheck(recyclerView$LayoutManager$10);
            this.mVerticalBoundCheck = new ViewBoundsCheck(recyclerView$LayoutManager$20);
            this.mRequestedSimpleAnimations = false;
            this.mIsAttachedToWindow = false;
            this.mAutoMeasure = false;
            this.mMeasurementCacheEnabled = true;
            this.mItemPrefetchEnabled = true;
        }

        public static boolean a(int v, int v1, int v2) {
            int v3 = View.MeasureSpec.getMode(v1);
            int v4 = View.MeasureSpec.getSize(v1);
            if(v2 > 0 && v != v2) {
                return false;
            }
            switch(v3) {
                case 0x80000000: {
                    return v4 >= v;
                }
                case 0: {
                    return true;
                }
                default: {
                    return v3 == 0x40000000 ? v4 == v : false;
                }
            }
        }

        public void addDisappearingView(View view0) {
            this.addDisappearingView(view0, -1);
        }

        public void addDisappearingView(View view0, int v) {
            RecyclerView.a(view0);
            throw null;
        }

        public void addView(View view0) {
            this.addView(view0, -1);
        }

        public void addView(View view0, int v) {
            RecyclerView.a(view0);
            throw null;
        }

        public void assertInLayoutOrScroll(String s) {
        }

        public void attachView(View view0) {
            this.attachView(view0, -1);
        }

        public void attachView(View view0, int v) {
            this.attachView(view0, v, ((LayoutParams)view0.getLayoutParams()));
        }

        public void attachView(View view0, int v, LayoutParams recyclerView$LayoutParams0) {
            RecyclerView.a(view0);
            throw null;
        }

        public void calculateItemDecorationsForChild(View view0, Rect rect0) {
            rect0.set(0, 0, 0, 0);
        }

        public abstract boolean canScrollHorizontally();

        public abstract boolean canScrollVertically();

        public boolean checkLayoutParams(LayoutParams recyclerView$LayoutParams0) {
            return recyclerView$LayoutParams0 != null;
        }

        public static int chooseSize(int v, int v1, int v2) {
            int v3 = View.MeasureSpec.getMode(v);
            int v4 = View.MeasureSpec.getSize(v);
            switch(v3) {
                case 0x80000000: {
                    return Math.min(v4, Math.max(v1, v2));
                }
                case 0x40000000: {
                    return v4;
                }
                default: {
                    return Math.max(v1, v2);
                }
            }
        }

        public void detachAndScrapAttachedViews(Recycler recyclerView$Recycler0) {
        }

        public void detachAndScrapView(View view0, Recycler recyclerView$Recycler0) {
            throw null;
        }

        public void detachAndScrapViewAt(int v, Recycler recyclerView$Recycler0) {
            RecyclerView.a(null);
            throw null;
        }

        public void detachView(View view0) {
            throw null;
        }

        public void detachViewAt(int v) {
            throw null;
        }

        public void dispatchAttachedToWindow(RecyclerView recyclerView0) {
            this.mIsAttachedToWindow = true;
        }

        public void dispatchDetachedFromWindow(RecyclerView recyclerView0, Recycler recyclerView$Recycler0) {
            this.mIsAttachedToWindow = false;
            this.onDetachedFromWindow(recyclerView0, recyclerView$Recycler0);
        }

        public void endAnimation(View view0) {
            throw null;
        }

        public View findContainingItemView(View view0) {
            return null;
        }

        public LayoutParams generateLayoutParams(Context context0, AttributeSet attributeSet0) {
            return new LayoutParams(context0, attributeSet0);
        }

        public LayoutParams generateLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
            if(viewGroup$LayoutParams0 instanceof LayoutParams) {
                return new LayoutParams(((LayoutParams)viewGroup$LayoutParams0));
            }
            return viewGroup$LayoutParams0 instanceof ViewGroup.MarginLayoutParams ? new LayoutParams(((ViewGroup.MarginLayoutParams)viewGroup$LayoutParams0)) : new LayoutParams(viewGroup$LayoutParams0);
        }

        public int getBaseline() {
            return -1;
        }

        public int getBottomDecorationHeight(View view0) {
            return ((LayoutParams)view0.getLayoutParams()).a.bottom;
        }

        public View getChildAt(int v) [...] // Inlined contents

        public int getChildCount() [...] // Inlined contents

        public static int getChildMeasureSpec(int v, int v1, int v2, int v3, boolean z) {
            int v4 = Math.max(0, v - v2);
            if(z) {
                if(v3 >= 0) {
                    return View.MeasureSpec.makeMeasureSpec(v3, 0x40000000);
                }
                return v3 == -1 && (v1 == 0x80000000 || v1 == 0x40000000) ? View.MeasureSpec.makeMeasureSpec(v4, v1) : 0;
            }
            if(v3 >= 0) {
                return View.MeasureSpec.makeMeasureSpec(v3, 0x40000000);
            }
            switch(v3) {
                case -2: {
                    return v1 == 0x80000000 || v1 == 0x40000000 ? View.MeasureSpec.makeMeasureSpec(v4, 0x80000000) : View.MeasureSpec.makeMeasureSpec(v4, 0);
                }
                case -1: {
                    return View.MeasureSpec.makeMeasureSpec(v4, v1);
                }
                default: {
                    return 0;
                }
            }
        }

        @Deprecated
        public static int getChildMeasureSpec(int v, int v1, int v2, boolean z) {
            int v3 = Math.max(0, v - v1);
            if(z) {
                return v2 < 0 ? 0 : View.MeasureSpec.makeMeasureSpec(v2, 0x40000000);
            }
            if(v2 >= 0) {
                return View.MeasureSpec.makeMeasureSpec(v2, 0x40000000);
            }
            switch(v2) {
                case -2: {
                    return View.MeasureSpec.makeMeasureSpec(v3, 0x80000000);
                }
                case -1: {
                    return View.MeasureSpec.makeMeasureSpec(v3, 0x40000000);
                }
                default: {
                    return 0;
                }
            }
        }

        public boolean getClipToPadding() {
            return false;
        }

        public int getColumnCountForAccessibility(Recycler recyclerView$Recycler0, State recyclerView$State0) {
            return 1;
        }

        public int getDecoratedBottom(View view0) {
            int v = view0.getBottom();
            return this.getBottomDecorationHeight(view0) + v;
        }

        public void getDecoratedBoundsWithMargins(View view0, Rect rect0) {
            LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
            rect0.set(view0.getLeft() - recyclerView$LayoutParams0.a.left - recyclerView$LayoutParams0.leftMargin, view0.getTop() - recyclerView$LayoutParams0.a.top - recyclerView$LayoutParams0.topMargin, view0.getRight() + recyclerView$LayoutParams0.a.right + recyclerView$LayoutParams0.rightMargin, view0.getBottom() + recyclerView$LayoutParams0.a.bottom + recyclerView$LayoutParams0.bottomMargin);
        }

        public int getDecoratedLeft(View view0) {
            return view0.getLeft() - this.getLeftDecorationWidth(view0);
        }

        public int getDecoratedMeasuredHeight(View view0) {
            Rect rect0 = ((LayoutParams)view0.getLayoutParams()).a;
            return view0.getMeasuredHeight() + rect0.top + rect0.bottom;
        }

        public int getDecoratedMeasuredWidth(View view0) {
            Rect rect0 = ((LayoutParams)view0.getLayoutParams()).a;
            return view0.getMeasuredWidth() + rect0.left + rect0.right;
        }

        public int getDecoratedRight(View view0) {
            int v = view0.getRight();
            return this.getRightDecorationWidth(view0) + v;
        }

        public int getDecoratedTop(View view0) {
            return view0.getTop() - this.getTopDecorationHeight(view0);
        }

        public View getFocusedChild() [...] // Inlined contents

        public int getHeight() {
            return this.mHeight;
        }

        public int getHeightMode() {
            return this.mHeightMode;
        }

        public int getItemCount() {
            return 0;
        }

        public int getItemViewType(View view0) {
            RecyclerView.a(view0);
            throw null;
        }

        public int getLayoutDirection() {
            return ViewCompat.getLayoutDirection(null);
        }

        public int getLeftDecorationWidth(View view0) {
            return ((LayoutParams)view0.getLayoutParams()).a.left;
        }

        public int getMinimumHeight() {
            return ViewCompat.getMinimumHeight(null);
        }

        public int getMinimumWidth() {
            return ViewCompat.getMinimumWidth(null);
        }

        public int getPaddingBottom() [...] // Inlined contents

        public int getPaddingEnd() {
            return 0;
        }

        public int getPaddingLeft() [...] // Inlined contents

        public int getPaddingRight() [...] // Inlined contents

        public int getPaddingStart() {
            return 0;
        }

        public int getPaddingTop() [...] // Inlined contents

        public int getPosition(View view0) {
            ((LayoutParams)view0.getLayoutParams()).getClass();
            throw null;
        }

        public static Properties getProperties(Context context0, AttributeSet attributeSet0, int v, int v1) {
            Properties recyclerView$LayoutManager$Properties0 = new Properties();  // 初始化器: Ljava/lang/Object;-><init>()V
            TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, R.styleable.a, v, v1);
            typedArray0.getInt(0, 1);
            typedArray0.getInt(9, 1);
            typedArray0.getBoolean(8, false);
            typedArray0.getBoolean(10, false);
            typedArray0.recycle();
            return recyclerView$LayoutManager$Properties0;
        }

        public int getRightDecorationWidth(View view0) {
            return ((LayoutParams)view0.getLayoutParams()).a.right;
        }

        public int getRowCountForAccessibility(Recycler recyclerView$Recycler0, State recyclerView$State0) {
            return 1;
        }

        public int getSelectionModeForAccessibility(Recycler recyclerView$Recycler0, State recyclerView$State0) {
            return 0;
        }

        public int getTopDecorationHeight(View view0) {
            return ((LayoutParams)view0.getLayoutParams()).a.top;
        }

        public void getTransformedBoundingBox(View view0, boolean z, Rect rect0) {
            if(z) {
                Rect rect1 = ((LayoutParams)view0.getLayoutParams()).a;
                rect0.set(-rect1.left, -rect1.top, view0.getWidth() + rect1.right, view0.getHeight() + rect1.bottom);
            }
            else {
                rect0.set(0, 0, view0.getWidth(), view0.getHeight());
            }
            rect0.offset(view0.getLeft(), view0.getTop());
        }

        public int getWidth() {
            return this.mWidth;
        }

        public int getWidthMode() {
            return this.mWidthMode;
        }

        public boolean hasFlexibleChildInBothOrientations() {
            return false;
        }

        public boolean hasFocus() {
            return false;
        }

        public void ignoreView(View view0) {
            view0.getParent();
            throw null;
        }

        public boolean isAttachedToWindow() {
            return this.mIsAttachedToWindow;
        }

        public boolean isFocused() {
            return false;
        }

        public final boolean isItemPrefetchEnabled() {
            return this.mItemPrefetchEnabled;
        }

        public boolean isLayoutHierarchical(Recycler recyclerView$Recycler0, State recyclerView$State0) {
            return false;
        }

        public boolean isMeasurementCacheEnabled() {
            return this.mMeasurementCacheEnabled;
        }

        public boolean isSmoothScrolling() [...] // Inlined contents

        // 去混淆评级： 低(20)
        public boolean isViewPartiallyVisible(View view0, boolean z, boolean z1) {
            boolean z2 = this.mHorizontalBoundCheck.b(view0) && this.mVerticalBoundCheck.b(view0);
            return z ? z2 : !z2;
        }

        public void layoutDecorated(View view0, int v, int v1, int v2, int v3) {
            Rect rect0 = ((LayoutParams)view0.getLayoutParams()).a;
            view0.layout(v + rect0.left, v1 + rect0.top, v2 - rect0.right, v3 - rect0.bottom);
        }

        public void layoutDecoratedWithMargins(View view0, int v, int v1, int v2, int v3) {
            LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
            view0.layout(v + recyclerView$LayoutParams0.a.left + recyclerView$LayoutParams0.leftMargin, v1 + recyclerView$LayoutParams0.a.top + recyclerView$LayoutParams0.topMargin, v2 - recyclerView$LayoutParams0.a.right - recyclerView$LayoutParams0.rightMargin, v3 - recyclerView$LayoutParams0.a.bottom - recyclerView$LayoutParams0.bottomMargin);
        }

        public void measureChild(View view0, int v, int v1) {
            LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
            throw null;
        }

        public void measureChildWithMargins(View view0, int v, int v1) {
            LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
            throw null;
        }

        public void moveView(int v, int v1) {
            new StringBuilder("Cannot move a child from non-existing index:").append(v);
            throw null;
        }

        public void offsetChildrenHorizontal(int v) {
        }

        public void offsetChildrenVertical(int v) {
        }

        public void onAdapterChanged(Adapter recyclerView$Adapter0, Adapter recyclerView$Adapter1) {
        }

        public boolean onAddFocusables(RecyclerView recyclerView0, ArrayList arrayList0, int v, int v1) {
            return false;
        }

        public void onAttachedToWindow(RecyclerView recyclerView0) {
        }

        @Deprecated
        public void onDetachedFromWindow(RecyclerView recyclerView0) {
        }

        public abstract void onDetachedFromWindow(RecyclerView arg1, Recycler arg2);

        public void onInitializeAccessibilityEvent(Recycler recyclerView$Recycler0, State recyclerView$State0, AccessibilityEvent accessibilityEvent0) {
        }

        public void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfoCompat accessibilityNodeInfoCompat0) {
            throw null;
        }

        public void onInitializeAccessibilityNodeInfo(Recycler recyclerView$Recycler0, State recyclerView$State0, AccessibilityNodeInfoCompat accessibilityNodeInfoCompat0) {
            throw null;
        }

        // 去混淆评级： 低(20)
        public void onInitializeAccessibilityNodeInfoForItem(Recycler recyclerView$Recycler0, State recyclerView$State0, View view0, AccessibilityNodeInfoCompat accessibilityNodeInfoCompat0) {
            accessibilityNodeInfoCompat0.setCollectionItemInfo(CollectionItemInfoCompat.obtain((this.canScrollVertically() ? this.getPosition(view0) : 0), 1, (this.canScrollHorizontally() ? this.getPosition(view0) : 0), 1, false, false));
        }

        public void onInitializeAccessibilityNodeInfoForItem(View view0, AccessibilityNodeInfoCompat accessibilityNodeInfoCompat0) {
            RecyclerView.a(view0);
        }

        public View onInterceptFocusSearch(View view0, int v) {
            return null;
        }

        public void onItemsAdded(RecyclerView recyclerView0, int v, int v1) {
        }

        public void onItemsChanged(RecyclerView recyclerView0) {
        }

        public void onItemsMoved(RecyclerView recyclerView0, int v, int v1, int v2) {
        }

        public void onItemsRemoved(RecyclerView recyclerView0, int v, int v1) {
        }

        public void onItemsUpdated(RecyclerView recyclerView0, int v, int v1) {
        }

        public void onItemsUpdated(RecyclerView recyclerView0, int v, int v1, Object object0) {
        }

        public void onMeasure(Recycler recyclerView$Recycler0, State recyclerView$State0, int v, int v1) {
            throw null;
        }

        public boolean onRequestChildFocus(RecyclerView recyclerView0, State recyclerView$State0, View view0, View view1) {
            return this.onRequestChildFocus(recyclerView0, view0, view1);
        }

        @Deprecated
        public boolean onRequestChildFocus(RecyclerView recyclerView0, View view0, View view1) {
            throw null;
        }

        public void onScrollStateChanged(int v) {
        }

        public void onSmoothScrollerStopped(SmoothScroller recyclerView$SmoothScroller0) {
        }

        public boolean performAccessibilityAction(int v, Bundle bundle0) {
            throw null;
        }

        public boolean performAccessibilityAction(Recycler recyclerView$Recycler0, State recyclerView$State0, int v, Bundle bundle0) {
            return false;
        }

        public boolean performAccessibilityActionForItem(Recycler recyclerView$Recycler0, State recyclerView$State0, View view0, int v, Bundle bundle0) {
            return false;
        }

        public boolean performAccessibilityActionForItem(View view0, int v, Bundle bundle0) {
            throw null;
        }

        public void postOnAnimation(Runnable runnable0) {
        }

        public void removeAllViews() {
        }

        public void removeAndRecycleAllViews(Recycler recyclerView$Recycler0) {
        }

        public void removeAndRecycleScrapInt(Recycler recyclerView$Recycler0) {
            throw null;
        }

        public void removeAndRecycleView(View view0, Recycler recyclerView$Recycler0) {
            this.removeView(view0);
            throw null;
        }

        public void removeAndRecycleViewAt(int v, Recycler recyclerView$Recycler0) {
            this.removeViewAt(v);
            throw null;
        }

        public boolean removeCallbacks(Runnable runnable0) {
            return false;
        }

        public void removeDetachedView(View view0) {
            throw null;
        }

        public void removeView(View view0) {
            throw null;
        }

        public void removeViewAt(int v) {
        }

        public boolean requestChildRectangleOnScreen(RecyclerView recyclerView0, View view0, Rect rect0, boolean z) {
            return this.requestChildRectangleOnScreen(recyclerView0, view0, rect0, z, false);
        }

        public boolean requestChildRectangleOnScreen(RecyclerView recyclerView0, View view0, Rect rect0, boolean z, boolean z1) {
            int v = view0.getLeft() + rect0.left - view0.getScrollX();
            int v1 = view0.getTop() + rect0.top - view0.getScrollY();
            int v2 = Math.min(0, v);
            int v3 = Math.min(0, v1);
            int v4 = rect0.width() + v - this.getWidth();
            int v5 = Math.max(0, v4);
            int v6 = Math.max(0, rect0.height() + v1 - this.getHeight());
            if(this.getLayoutDirection() != 1) {
                if(v2 == 0) {
                    v2 = Math.min(v, v5);
                }
            }
            else if(v5 != 0) {
                v2 = v5;
            }
            else {
                v2 = Math.max(v2, v4);
            }
            if(v3 == 0) {
                v3 = Math.min(v1, v6);
            }
            if(z1 || v2 != 0 || v3 != 0) {
                throw null;
            }
            return false;
        }

        public void requestLayout() {
        }

        public void requestSimpleAnimationsInNextLayout() {
            this.mRequestedSimpleAnimations = true;
        }

        @Deprecated
        public void setAutoMeasureEnabled(boolean z) {
            this.mAutoMeasure = z;
        }

        public void setExactMeasureSpecsFrom(RecyclerView recyclerView0) {
            throw null;
        }

        public final void setItemPrefetchEnabled(boolean z) {
            if(z != this.mItemPrefetchEnabled) {
                this.mItemPrefetchEnabled = z;
                this.mPrefetchMaxCountObserved = 0;
            }
        }

        public void setMeasureSpecs(int v, int v1) {
            this.mWidth = View.MeasureSpec.getSize(v);
            int v2 = View.MeasureSpec.getMode(v);
            this.mWidthMode = v2;
            if(v2 == 0 && !RecyclerView.a) {
                this.mWidth = 0;
            }
            this.mHeight = View.MeasureSpec.getSize(v1);
            int v3 = View.MeasureSpec.getMode(v1);
            this.mHeightMode = v3;
            if(v3 == 0 && !RecyclerView.a) {
                this.mHeight = 0;
            }
        }

        public void setMeasuredDimension(int v, int v1) {
            throw null;
        }

        public void setMeasuredDimension(Rect rect0, int v, int v1) {
            int v2 = rect0.width();
            int v3 = rect0.height();
            this.setMeasuredDimension(LayoutManager.chooseSize(v, v2, this.getMinimumWidth()), LayoutManager.chooseSize(v1, v3, this.getMinimumHeight()));
        }

        public void setMeasuredDimensionFromChildren(int v, int v1) {
            throw null;
        }

        public void setMeasurementCacheEnabled(boolean z) {
            this.mMeasurementCacheEnabled = z;
        }

        public void setRecyclerView(RecyclerView recyclerView0) {
            this.mWidth = 0;
            this.mHeight = 0;
            this.mWidthMode = 0x40000000;
            this.mHeightMode = 0x40000000;
        }

        // 去混淆评级： 低(40)
        public boolean shouldMeasureChild(View view0, int v, int v1, LayoutParams recyclerView$LayoutParams0) {
            return view0.isLayoutRequested() || !this.mMeasurementCacheEnabled || !LayoutManager.a(view0.getWidth(), v, recyclerView$LayoutParams0.width) || !LayoutManager.a(view0.getHeight(), v1, recyclerView$LayoutParams0.height);
        }

        // 去混淆评级： 低(30)
        public boolean shouldReMeasureChild(View view0, int v, int v1, LayoutParams recyclerView$LayoutParams0) {
            return !this.mMeasurementCacheEnabled || !LayoutManager.a(view0.getMeasuredWidth(), v, recyclerView$LayoutParams0.width) || !LayoutManager.a(view0.getMeasuredHeight(), v1, recyclerView$LayoutParams0.height);
        }

        public void startSmoothScroll(SmoothScroller recyclerView$SmoothScroller0) {
            throw null;
        }

        public void stopIgnoringView(View view0) {
            RecyclerView.a(view0);
            throw null;
        }

        public void stopSmoothScroller() {
        }
    }

    public static class LayoutParams extends ViewGroup.MarginLayoutParams {
        public final Rect a;

        public LayoutParams() {
            super(-2, -2);
            this.a = new Rect();
        }

        public LayoutParams(Context context0, AttributeSet attributeSet0) {
            super(context0, attributeSet0);
            this.a = new Rect();
        }

        public LayoutParams(LayoutParams recyclerView$LayoutParams0) {
            super(recyclerView$LayoutParams0);
            this.a = new Rect();
        }

        public LayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
            super(viewGroup$LayoutParams0);
            this.a = new Rect();
        }

        public LayoutParams(ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0) {
            super(viewGroup$MarginLayoutParams0);
            this.a = new Rect();
        }
    }

    public static abstract class OnScrollListener {
        public void onScrollStateChanged(RecyclerView recyclerView0, int v) {
        }
    }

    public abstract class Recycler {
    }

    public static abstract class SmoothScroller {
        public abstract void a();
    }

    public static abstract class State {
    }

    public static abstract class ViewHolder {
        static final int FLAG_ADAPTER_FULLUPDATE = 0x400;
        static final int FLAG_ADAPTER_POSITION_UNKNOWN = 0x200;
        static final int FLAG_APPEARED_IN_PRE_LAYOUT = 0x1000;
        static final int FLAG_BOUNCED_FROM_HIDDEN_LIST = 0x2000;
        static final int FLAG_BOUND = 1;
        static final int FLAG_IGNORE = 0x80;
        static final int FLAG_INVALID = 4;
        static final int FLAG_MOVED = 0x800;
        static final int FLAG_NOT_RECYCLABLE = 16;
        static final int FLAG_REMOVED = 8;
        static final int FLAG_RETURNED_FROM_SCRAP = 0x20;
        static final int FLAG_SET_A11Y_ITEM_DELEGATE = 0x4000;
        static final int FLAG_TMP_DETACHED = 0x100;
        static final int FLAG_UPDATE = 2;
        private static final List FULLUPDATE_PAYLOADS = null;
        static final int PENDING_ACCESSIBILITY_STATE_NOT_SET = -1;
        public final View itemView;
        int mFlags;
        boolean mInChangeScrap;
        private int mIsRecyclableCount;
        long mItemId;
        int mItemViewType;
        WeakReference mNestedRecyclerView;
        int mOldPosition;
        RecyclerView mOwnerRecyclerView;
        List mPayloads;
        int mPendingAccessibilityState;
        int mPosition;
        int mPreLayoutPosition;
        Recycler mScrapContainer;
        ViewHolder mShadowedHolder;
        ViewHolder mShadowingHolder;
        List mUnmodifiedPayloads;
        private int mWasImportantForAccessibilityBeforeHidden;

        static {
            ViewHolder.FULLUPDATE_PAYLOADS = Collections.emptyList();
        }

        public ViewHolder(View view0) {
            this.mPosition = -1;
            this.mOldPosition = -1;
            this.mItemId = -1L;
            this.mItemViewType = -1;
            this.mPreLayoutPosition = -1;
            this.mShadowedHolder = null;
            this.mShadowingHolder = null;
            this.mPayloads = null;
            this.mUnmodifiedPayloads = null;
            this.mIsRecyclableCount = 0;
            this.mInChangeScrap = false;
            this.mWasImportantForAccessibilityBeforeHidden = 0;
            this.mPendingAccessibilityState = -1;
            if(view0 == null) {
                throw new IllegalArgumentException("itemView may not be null");
            }
            this.itemView = view0;
        }

        public void addChangePayload(Object object0) {
            if(object0 == null) {
                this.addFlags(0x400);
                return;
            }
            if((0x400 & this.mFlags) == 0) {
                if(this.mPayloads == null) {
                    ArrayList arrayList0 = new ArrayList();
                    this.mPayloads = arrayList0;
                    this.mUnmodifiedPayloads = Collections.unmodifiableList(arrayList0);
                }
                this.mPayloads.add(object0);
            }
        }

        public void addFlags(int v) {
            this.mFlags |= v;
        }

        public void clearOldPosition() {
            this.mOldPosition = -1;
            this.mPreLayoutPosition = -1;
        }

        public void clearPayload() {
            List list0 = this.mPayloads;
            if(list0 != null) {
                list0.clear();
            }
            this.mFlags &= 0xFFFFFBFF;
        }

        public void clearReturnedFromScrapFlag() {
            this.mFlags &= -33;
        }

        public void clearTmpDetachFlag() {
            this.mFlags &= 0xFFFFFEFF;
        }

        public boolean doesTransientStatePreventRecycling() {
            return (this.mFlags & 16) == 0 && ViewCompat.hasTransientState(this.itemView);
        }

        public void flagRemovedAndOffsetPosition(int v, int v1, boolean z) {
            this.addFlags(8);
            this.offsetPosition(v1, z);
            this.mPosition = v;
        }

        public final int getAdapterPosition() [...] // Inlined contents

        public final long getItemId() {
            return this.mItemId;
        }

        public final int getItemViewType() {
            return this.mItemViewType;
        }

        public final int getLayoutPosition() {
            return this.mPreLayoutPosition == -1 ? this.mPosition : this.mPreLayoutPosition;
        }

        public final int getOldPosition() {
            return this.mOldPosition;
        }

        @Deprecated
        public final int getPosition() {
            return this.mPreLayoutPosition == -1 ? this.mPosition : this.mPreLayoutPosition;
        }

        public List getUnmodifiedPayloads() {
            return (this.mFlags & 0x400) != 0 || (this.mPayloads == null || this.mPayloads.size() == 0) ? ViewHolder.FULLUPDATE_PAYLOADS : this.mUnmodifiedPayloads;
        }

        public boolean hasAnyOfTheFlags(int v) {
            return (v & this.mFlags) != 0;
        }

        public boolean isAdapterPositionUnknown() {
            return (this.mFlags & 0x200) != 0 || this.isInvalid();
        }

        public boolean isBound() {
            return (this.mFlags & 1) != 0;
        }

        public boolean isInvalid() {
            return (this.mFlags & 4) != 0;
        }

        public final boolean isRecyclable() {
            return (this.mFlags & 16) == 0 && !ViewCompat.hasTransientState(this.itemView);
        }

        public boolean isRemoved() {
            return (this.mFlags & 8) != 0;
        }

        public boolean isScrap() [...] // Inlined contents

        public boolean isTmpDetached() {
            return (this.mFlags & 0x100) != 0;
        }

        public boolean isUpdated() {
            return (this.mFlags & 2) != 0;
        }

        public boolean needsUpdate() {
            return (this.mFlags & 2) != 0;
        }

        public void offsetPosition(int v, boolean z) {
            if(this.mOldPosition == -1) {
                this.mOldPosition = this.mPosition;
            }
            if(this.mPreLayoutPosition == -1) {
                this.mPreLayoutPosition = this.mPosition;
            }
            if(z) {
                this.mPreLayoutPosition += v;
            }
            this.mPosition += v;
            if(this.itemView.getLayoutParams() != null) {
                ((LayoutParams)this.itemView.getLayoutParams()).getClass();
            }
        }

        public void onEnteredHiddenState(RecyclerView recyclerView0) {
            this.mWasImportantForAccessibilityBeforeHidden = this.mPendingAccessibilityState == -1 ? ViewCompat.getImportantForAccessibility(this.itemView) : this.mPendingAccessibilityState;
            throw null;
        }

        public void onLeftHiddenState(RecyclerView recyclerView0) {
            throw null;
        }

        public void resetInternal() {
            this.mFlags = 0;
            this.mPosition = -1;
            this.mOldPosition = -1;
            this.mItemId = -1L;
            this.mPreLayoutPosition = -1;
            this.mIsRecyclableCount = 0;
            this.mShadowedHolder = null;
            this.mShadowingHolder = null;
            this.clearPayload();
            this.mWasImportantForAccessibilityBeforeHidden = 0;
            this.mPendingAccessibilityState = -1;
            WeakReference weakReference0 = this.mNestedRecyclerView;
            if(weakReference0 != null) {
                ViewParent viewParent0 = weakReference0.get();
            alab1:
                while(true) {
                    View view0 = (View)viewParent0;
                    while(view0 != null) {
                        if(view0 == this.itemView) {
                            break alab1;
                        }
                        ViewParent viewParent1 = view0.getParent();
                        if(!(viewParent1 instanceof View)) {
                            view0 = null;
                            continue;
                        }
                        viewParent0 = viewParent1;
                        continue alab1;
                    }
                    this.mNestedRecyclerView = null;
                    break;
                }
            }
        }

        public void saveOldPosition() {
            if(this.mOldPosition == -1) {
                this.mOldPosition = this.mPosition;
            }
        }

        public void setFlags(int v, int v1) {
            this.mFlags = v & v1 | v1 & this.mFlags;
        }

        public final void setIsRecyclable(boolean z) {
            int v = z ? this.mIsRecyclableCount - 1 : this.mIsRecyclableCount + 1;
            this.mIsRecyclableCount = v;
            if(v < 0) {
                this.mIsRecyclableCount = 0;
                return;
            }
            if(!z && v == 1) {
                this.mFlags |= 16;
                return;
            }
            if(z && v == 0) {
                this.mFlags &= -17;
            }
        }

        public void setScrapContainer(Recycler recyclerView$Recycler0, boolean z) {
            this.mInChangeScrap = z;
        }

        public boolean shouldBeKeptAsChild() {
            return (this.mFlags & 16) != 0;
        }

        public boolean shouldIgnore() {
            return (this.mFlags & 0x80) != 0;
        }

        public void stopIgnoring() {
            this.mFlags &= 0xFFFFFF7F;
        }

        @Override
        public String toString() {
            StringBuilder stringBuilder0 = new StringBuilder("ViewHolder{" + Integer.toHexString(this.hashCode()) + " position=" + this.mPosition + " id=" + this.mItemId + ", oldPos=" + this.mOldPosition + ", pLpos:" + this.mPreLayoutPosition);
            if(this.isInvalid()) {
                stringBuilder0.append(" invalid");
            }
            if(!this.isBound()) {
                stringBuilder0.append(" unbound");
            }
            if(this.needsUpdate()) {
                stringBuilder0.append(" update");
            }
            if(this.isRemoved()) {
                stringBuilder0.append(" removed");
            }
            if(this.shouldIgnore()) {
                stringBuilder0.append(" ignored");
            }
            if(this.isTmpDetached()) {
                stringBuilder0.append(" tmpDetached");
            }
            if(!this.isRecyclable()) {
                stringBuilder0.append(" not recyclable(" + this.mIsRecyclableCount + ")");
            }
            if(this.isAdapterPositionUnknown()) {
                stringBuilder0.append(" undefined adapter position");
            }
            if(this.itemView.getParent() == null) {
                stringBuilder0.append(" no parent");
            }
            stringBuilder0.append("}");
            return stringBuilder0.toString();
        }

        public void unScrap() {
            throw null;
        }

        public boolean wasReturnedFromScrap() {
            return (this.mFlags & 0x20) != 0;
        }
    }

    public static final boolean a;

    static {
        RecyclerView.a = Build.VERSION.SDK_INT >= 23;
    }

    public static void a(View view0) {
        if(view0 == null) {
            return;
        }
        ((LayoutParams)view0.getLayoutParams()).getClass();
    }
}


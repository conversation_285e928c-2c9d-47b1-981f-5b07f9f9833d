package android.supportv1.v7.widget;

import android.view.View;

final class OrientationHelper.2 extends OrientationHelper {
    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int a(View view0) {
        LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        return this.a.getDecoratedBottom(view0) + recyclerView$LayoutParams0.bottomMargin;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int b(View view0) {
        LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        return this.a.getDecoratedMeasuredHeight(view0) + recyclerView$LayoutParams0.topMargin + recyclerView$LayoutParams0.bottomMargin;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int c(View view0) {
        LayoutParams recyclerView$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        return this.a.getDecoratedTop(view0) - recyclerView$LayoutParams0.topMargin;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int d() {
        return this.a.getHeight();
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int e() {
        return this.a.getHeight();
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int f() {
        return 0;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int g() {
        return this.a.getHeightMode();
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int h() {
        return 0;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int i() {
        return this.a.getHeight();
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int j(View view0) {
        this.a.getTransformedBoundingBox(view0, true, this.b);
        return this.b.bottom;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final int k(View view0) {
        this.a.getTransformedBoundingBox(view0, true, this.b);
        return this.b.top;
    }

    @Override  // android.supportv1.v7.widget.OrientationHelper
    public final void l(int v) {
    }
}


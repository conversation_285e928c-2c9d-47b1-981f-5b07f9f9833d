package android.supportv1.v4.content.pm;

import android.content.Context;
import android.content.pm.ShortcutInfo.Builder;
import android.content.pm.ShortcutManager;
import android.view.inputmethod.EditorInfo;

public abstract class a {
    public static ShortcutInfo.Builder b(Context context0, String s) {
        return new ShortcutInfo.Builder(context0, s);
    }

    public static ShortcutManager f(Object object0) {
        return (ShortcutManager)object0;
    }

    public static void i() {
    }

    public static void j(ShortcutInfo.Builder shortcutInfo$Builder0) {
        shortcutInfo$Builder0.setDisabledMessage(null);
    }

    public static String[] s(EditorInfo editorInfo0) {
        return editorInfo0.contentMimeTypes;
    }

    public static void u(ShortcutInfo.Builder shortcutInfo$Builder0) {
        shortcutInfo$Builder0.setRank(0);
    }
}


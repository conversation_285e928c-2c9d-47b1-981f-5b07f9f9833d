package android.supportv1.v4.content;

import android.content.SharedPreferences.Editor;

@Deprecated
public final class SharedPreferencesCompat {
    @Deprecated
    public static final class EditorCompat {
        private final SharedPreferencesCompat.EditorCompat.Helper mHelper;
        private static EditorCompat sInstance;

        private EditorCompat() {
            this.mHelper = new SharedPreferencesCompat.EditorCompat.Helper();
        }

        @Deprecated
        public void apply(SharedPreferences.Editor sharedPreferences$Editor0) {
            this.mHelper.apply(sharedPreferences$Editor0);
        }

        @Deprecated
        public static EditorCompat getInstance() {
            if(EditorCompat.sInstance == null) {
                EditorCompat.sInstance = new EditorCompat();
            }
            return EditorCompat.sInstance;
        }
    }

}


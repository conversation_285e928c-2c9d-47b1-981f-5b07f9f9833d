package android.supportv1.v4.content;

import android.content.ContentResolver;
import android.database.Cursor;
import android.net.Uri;
import android.os.OperationCanceledException;
import android.supportv1.v4.os.CancellationSignal;

public final class ContentResolverCompat {
    public static Cursor query(ContentResolver contentResolver0, Uri uri0, String[] arr_s, String s, String[] arr_s1, String s1, CancellationSignal cancellationSignal0) {
        try {
            return contentResolver0.query(uri0, arr_s, s, arr_s1, s1, ((android.os.CancellationSignal)(cancellationSignal0 == null ? null : cancellationSignal0.getCancellationSignalObject())));
        label_2:
            if(!(exception0 instanceof OperationCanceledException)) {
                throw exception0;
            }
            throw new android.supportv1.v4.os.OperationCanceledException();
        }
        catch(Exception exception0) {
            goto label_2;
        }
        throw new android.supportv1.v4.os.OperationCanceledException();
    }
}


package android.supportv1.v4.content.res;

import android.content.res.Resources.Theme;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.LinearGradient;
import android.graphics.RadialGradient;
import android.graphics.Shader.TileMode;
import android.graphics.Shader;
import android.graphics.SweepGradient;
import android.supportv1.a.a.i;
import android.util.AttributeSet;
import android.util.Xml;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

final class GradientColorInflaterCompat {
    static final class ColorStops {
        final int[] mColors;
        final float[] mOffsets;

        public ColorStops(int v, int v1) {
            this.mColors = new int[]{v, v1};
            this.mOffsets = new float[]{0.0f, 1.0f};
        }

        public ColorStops(int v, int v1, int v2) {
            this.mColors = new int[]{v, v1, v2};
            this.mOffsets = new float[]{0.0f, 0.5f, 1.0f};
        }

        public ColorStops(List list0, List list1) {
            int v = list0.size();
            this.mColors = new int[v];
            this.mOffsets = new float[v];
            for(int v1 = 0; v1 < v; ++v1) {
                this.mColors[v1] = (int)(((Integer)list0.get(v1)));
                this.mOffsets[v1] = (float)(((Float)list1.get(v1)));
            }
        }
    }

    private static final int TILE_MODE_CLAMP = 0;
    private static final int TILE_MODE_MIRROR = 2;
    private static final int TILE_MODE_REPEAT = 1;

    private static ColorStops checkColors(ColorStops gradientColorInflaterCompat$ColorStops0, int v, int v1, boolean z, int v2) {
        if(gradientColorInflaterCompat$ColorStops0 != null) {
            return gradientColorInflaterCompat$ColorStops0;
        }
        return z ? new ColorStops(v, v2, v1) : new ColorStops(v, v1);
    }

    public static Shader createFromXml(Resources resources0, XmlPullParser xmlPullParser0, Resources.Theme resources$Theme0) throws XmlPullParserException, IOException {
        AttributeSet attributeSet0 = Xml.asAttributeSet(xmlPullParser0);
        do {
            int v = xmlPullParser0.next();
        }
        while(v != 1 && v != 2);
        if(v != 2) {
            throw new XmlPullParserException("No start tag found");
        }
        return GradientColorInflaterCompat.createFromXmlInner(resources0, xmlPullParser0, attributeSet0, resources$Theme0);
    }

    public static Shader createFromXmlInner(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) throws IOException, XmlPullParserException {
        String s = xmlPullParser0.getName();
        if(s.equals("gradient")) {
            TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, a.i.d);
            float f = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "startX", 8, 0.0f);
            float f1 = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "startY", 9, 0.0f);
            float f2 = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "endX", 10, 0.0f);
            float f3 = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "endY", 11, 0.0f);
            float f4 = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "centerX", 3, 0.0f);
            float f5 = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "centerY", 4, 0.0f);
            int v = TypedArrayUtils.getNamedInt(typedArray0, xmlPullParser0, "type", 2, 0);
            int v1 = TypedArrayUtils.getNamedColor(typedArray0, xmlPullParser0, "startColor", 0, 0);
            boolean z = TypedArrayUtils.hasAttribute(xmlPullParser0, "centerColor");
            int v2 = TypedArrayUtils.getNamedColor(typedArray0, xmlPullParser0, "centerColor", 7, 0);
            int v3 = TypedArrayUtils.getNamedColor(typedArray0, xmlPullParser0, "endColor", 1, 0);
            int v4 = TypedArrayUtils.getNamedInt(typedArray0, xmlPullParser0, "tileMode", 6, 0);
            float f6 = TypedArrayUtils.getNamedFloat(typedArray0, xmlPullParser0, "gradientRadius", 5, 0.0f);
            typedArray0.recycle();
            ColorStops gradientColorInflaterCompat$ColorStops0 = GradientColorInflaterCompat.checkColors(GradientColorInflaterCompat.inflateChildElements(resources0, xmlPullParser0, attributeSet0, resources$Theme0), v1, v3, z, v2);
            switch(v) {
                case 1: {
                    if(f6 <= 0.0f) {
                        throw new XmlPullParserException("<gradient> tag requires \'gradientRadius\' attribute with radial type");
                    }
                    Shader.TileMode shader$TileMode1 = GradientColorInflaterCompat.parseTileMode(v4);
                    return new RadialGradient(f4, f5, f6, gradientColorInflaterCompat$ColorStops0.mColors, gradientColorInflaterCompat$ColorStops0.mOffsets, shader$TileMode1);
                }
                case 2: {
                    return new SweepGradient(f4, f5, gradientColorInflaterCompat$ColorStops0.mColors, gradientColorInflaterCompat$ColorStops0.mOffsets);
                }
                default: {
                    Shader.TileMode shader$TileMode0 = GradientColorInflaterCompat.parseTileMode(v4);
                    return new LinearGradient(f, f1, f2, f3, gradientColorInflaterCompat$ColorStops0.mColors, gradientColorInflaterCompat$ColorStops0.mOffsets, shader$TileMode0);
                }
            }
        }
        throw new XmlPullParserException(xmlPullParser0.getPositionDescription() + ": invalid gradient color tag " + s);
    }

    private static ColorStops inflateChildElements(Resources resources0, XmlPullParser xmlPullParser0, AttributeSet attributeSet0, Resources.Theme resources$Theme0) throws XmlPullParserException, IOException {
        int v = xmlPullParser0.getDepth();
        ArrayList arrayList0 = new ArrayList(20);
        ArrayList arrayList1 = new ArrayList(20);
        int v1;
        while((v1 = xmlPullParser0.next()) != 1) {
            int v2 = xmlPullParser0.getDepth();
            if(v2 < v + 1 && v1 == 3) {
                break;
            }
            if(v1 == 2 && v2 <= v + 1 && xmlPullParser0.getName().equals("item")) {
                TypedArray typedArray0 = TypedArrayUtils.obtainAttributes(resources0, resources$Theme0, attributeSet0, a.i.e);
                if(!typedArray0.hasValue(0) || !typedArray0.hasValue(1)) {
                    throw new XmlPullParserException(xmlPullParser0.getPositionDescription() + ": <item> tag requires a \'color\' attribute and a \'offset\' attribute!");
                }
                int v3 = typedArray0.getColor(0, 0);
                float f = typedArray0.getFloat(1, 0.0f);
                typedArray0.recycle();
                arrayList1.add(v3);
                arrayList0.add(f);
            }
        }
        return arrayList1.size() <= 0 ? null : new ColorStops(arrayList1, arrayList0);
    }

    private static Shader.TileMode parseTileMode(int v) {
        switch(v) {
            case 1: {
                return Shader.TileMode.REPEAT;
            }
            case 2: {
                return Shader.TileMode.MIRROR;
            }
            default: {
                return Shader.TileMode.CLAMP;
            }
        }
    }
}


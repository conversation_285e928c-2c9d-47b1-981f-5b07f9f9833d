package android.supportv1.v4.provider;

import android.content.Context;
import android.net.Uri;
import android.provider.DocumentsContract;

class SingleDocumentFile extends DocumentFile {
    private Context mContext;
    private Uri mUri;

    public SingleDocumentFile(DocumentFile documentFile0, Context context0, Uri uri0) {
        super(documentFile0);
        this.mContext = context0;
        this.mUri = uri0;
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public boolean canRead() {
        return DocumentsContractApi19.canRead(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public boolean canWrite() {
        return DocumentsContractApi19.canWrite(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public DocumentFile createDirectory(String s) {
        throw new UnsupportedOperationException();
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public DocumentFile createFile(String s, String s1) {
        throw new UnsupportedOperationException();
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public boolean delete() {
        try {
            return DocumentsContract.deleteDocument(this.mContext.getContentResolver(), this.mUri);
        }
        catch(Exception unused_ex) {
            return false;
        }
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public boolean exists() {
        return DocumentsContractApi19.exists(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public String getName() {
        return DocumentsContractApi19.getName(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public String getType() {
        return DocumentsContractApi19.getType(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public Uri getUri() {
        return this.mUri;
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public boolean isDirectory() {
        return DocumentsContractApi19.isDirectory(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public boolean isFile() {
        return DocumentsContractApi19.isFile(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public boolean isVirtual() {
        return DocumentsContractApi19.isVirtual(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public long lastModified() {
        return DocumentsContractApi19.lastModified(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public long length() {
        return DocumentsContractApi19.length(this.mContext, this.mUri);
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public DocumentFile[] listFiles() {
        throw new UnsupportedOperationException();
    }

    @Override  // android.supportv1.v4.provider.DocumentFile
    public boolean renameTo(String s) {
        throw new UnsupportedOperationException();
    }
}


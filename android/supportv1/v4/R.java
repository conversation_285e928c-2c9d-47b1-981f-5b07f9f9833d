package android.supportv1.v4;

public final class R {
    public static final class attr {
        public static final int alpha = 0x7F040027;  // attr:actionModeWebSearchDrawable
        public static final int coordinatorLayoutStyle = 0x7F0400A4;  // attr:buttonBarStyle
        public static final int font = 0x7F0400D8;  // attr:cameraPreviewFrameRate
        public static final int fontProviderAuthority = 0x7F0400DA;  // attr:cameraRequestPermissions
        public static final int fontProviderCerts = 0x7F0400DB;  // attr:cameraSnapshotMaxHeight
        public static final int fontProviderFetchStrategy = 0x7F0400DC;  // attr:cameraSnapshotMaxWidth
        public static final int fontProviderFetchTimeout = 0x7F0400DD;  // attr:cameraUseDeviceOrientation
        public static final int fontProviderPackage = 0x7F0400DE;  // attr:cameraVideoBitRate
        public static final int fontProviderQuery = 0x7F0400DF;  // attr:cameraVideoCodec
        public static final int fontStyle = 0x7F0400E0;  // attr:cameraVideoMaxDuration
        public static final int fontVariationSettings = 0x7F0400E1;  // attr:cameraVideoMaxSize
        public static final int fontWeight = 0x7F0400E2;  // attr:cameraVideoSizeAspectRatio
        public static final int keylines = 0x7F040110;  // attr:checkedTextViewStyle
        public static final int layout_anchor = 0x7F040115;  // attr:chipIcon
        public static final int layout_anchorGravity = 0x7F040116;  // attr:chipIconEnabled
        public static final int layout_behavior = 0x7F040117;  // attr:chipIconSize
        public static final int layout_dodgeInsetEdges = 0x7F04011A;  // attr:chipMinHeight
        public static final int layout_insetEdge = 0x7F04011B;  // attr:chipMinTouchTargetSize
        public static final int layout_keyline = 0x7F04011C;  // attr:chipSpacing
        public static final int statusBarBackground = 0x7F04017C;  // attr:colorSurfaceContainerLowest
        public static final int ttcIndex = 0x7F0401E2;  // attr:deriveConstraintsFrom

    }

    public static final class color {
        public static final int notification_action_color_filter = 0x7F06007E;  // color:color_all_file
        public static final int notification_icon_bg_color = 0x7F06007F;  // color:color_bg_ads
        public static final int notification_material_background_media_default_color = 0x7F060080;  // color:color_bgr_main
        public static final int primary_text_default_material_dark = 0x7F060085;  // color:color_cardBg_htmlDoc_lower
        public static final int ripple_material_light = 0x7F06008A;  // color:color_cardBg_pptDoc_upper
        public static final int secondary_text_default_material_dark = 0x7F06008B;  // color:color_cardBg_rtfDoc_lower
        public static final int secondary_text_default_material_light = 0x7F06008C;  // color:color_cardBg_rtfDoc_upper

    }

    public static final class dimen {
        public static final int compat_button_inset_horizontal_material = 0x7F07005D;  // dimen:_178sdp
        public static final int compat_button_inset_vertical_material = 0x7F07005E;  // dimen:_179sdp
        public static final int compat_button_padding_horizontal_material = 0x7F07005F;  // dimen:_17sdp
        public static final int compat_button_padding_vertical_material = 0x7F070060;  // dimen:_17ssp
        public static final int compat_control_corner_material = 0x7F070061;  // dimen:_180sdp
        public static final int compat_notification_large_icon_max_height = 0x7F070062;  // dimen:_181sdp
        public static final int compat_notification_large_icon_max_width = 0x7F070063;  // dimen:_182sdp
        public static final int notification_action_icon_size = 0x7F0700D1;
        public static final int notification_action_text_size = 0x7F0700D2;
        public static final int notification_big_circle_margin = 0x7F0700D3;  // dimen:_274sdp
        public static final int notification_content_margin_start = 0x7F0700D4;
        public static final int notification_large_icon_height = 0x7F0700D5;
        public static final int notification_large_icon_width = 0x7F0700D6;  // dimen:_277sdp
        public static final int notification_main_column_padding_top = 0x7F0700D7;
        public static final int notification_media_narrow_margin = 0x7F0700D8;
        public static final int notification_right_icon_size = 0x7F0700D9;  // dimen:_27sdp
        public static final int notification_right_side_padding_top = 0x7F0700DA;
        public static final int notification_small_icon_background_padding = 0x7F0700DB;  // dimen:_280sdp
        public static final int notification_small_icon_size_as_large = 0x7F0700DC;  // dimen:_281sdp
        public static final int notification_subtext_size = 0x7F0700DD;  // dimen:_282sdp
        public static final int notification_top_pad = 0x7F0700DE;  // dimen:_283sdp
        public static final int notification_top_pad_large_text = 0x7F0700DF;  // dimen:_284sdp
        public static final int subtitle_corner_radius = 0x7F070107;
        public static final int subtitle_outline_width = 0x7F070108;
        public static final int subtitle_shadow_offset = 0x7F070109;
        public static final int subtitle_shadow_radius = 0x7F07010A;

    }

    public static final class drawable {
        public static final int notification_action_background = 0x7F080095;
        public static final int notification_bg = 0x7F080096;
        public static final int notification_bg_low = 0x7F080097;
        public static final int notification_bg_low_normal = 0x7F080098;
        public static final int notification_bg_low_pressed = 0x7F080099;
        public static final int notification_bg_normal = 0x7F08009A;  // drawable:abc_list_selector_background_transition_holo_dark
        public static final int notification_bg_normal_pressed = 0x7F08009B;  // drawable:abc_list_selector_background_transition_holo_light
        public static final int notification_icon_background = 0x7F08009C;
        public static final int notification_template_icon_bg = 0x7F08009D;
        public static final int notification_template_icon_low_bg = 0x7F08009E;  // drawable:abc_list_selector_holo_dark
        public static final int notification_tile_bg = 0x7F08009F;  // drawable:abc_list_selector_holo_light
        public static final int notify_panel_notification_icon_bg = 0x7F0800A0;

    }

    public static final class id {
        public static final int action0 = 0x7F090008;  // font:gilroy_regular
        public static final int action_container = 0x7F090010;  // font:plus_medium
        public static final int action_divider = 0x7F090012;  // font:plus_semibold
        public static final int action_image = 0x7F090013;
        public static final int action_text = 0x7F090019;
        public static final int actions = 0x7F09001A;
        public static final int async = 0x7F090041;
        public static final int blocking = 0x7F090052;
        public static final int bottom = 0x7F090055;
        public static final int cancel_action = 0x7F090073;
        public static final int chronometer = 0x7F090088;
        public static final int end = 0x7F0900D8;
        public static final int end_padder = 0x7F0900D9;
        public static final int forever = 0x7F090102;
        public static final int icon = 0x7F09012B;
        public static final int icon_group = 0x7F09012C;
        public static final int info = 0x7F090137;
        public static final int italic = 0x7F090149;
        public static final int left = 0x7F09014F;
        public static final int line1 = 0x7F090152;
        public static final int line3 = 0x7F090153;
        public static final int media_actions = 0x7F090169;
        public static final int none = 0x7F090184;
        public static final int normal = 0x7F090185;
        public static final int notification_background = 0x7F090188;
        public static final int notification_main_column = 0x7F090189;
        public static final int notification_main_column_container = 0x7F09018A;
        public static final int right = 0x7F09021B;
        public static final int right_icon = 0x7F09021C;
        public static final int right_side = 0x7F09021E;
        public static final int start = 0x7F090270;
        public static final int status_bar_latest_event_content = 0x7F090271;
        public static final int tag_transition_group = 0x7F090282;
        public static final int tag_unhandled_key_event_manager = 0x7F090283;
        public static final int tag_unhandled_key_listeners = 0x7F090284;
        public static final int text = 0x7F090288;
        public static final int text2 = 0x7F090289;
        public static final int time = 0x7F090293;
        public static final int title = 0x7F090294;
        public static final int top = 0x7F0902A1;

    }

    public static final class integer {
        public static final int cancel_button_image_alpha = 0x7F0A0008;  // id:ChasingDots
        public static final int status_bar_notification_info_maxnum = 0x7F0A001E;  // id:SHOW_PATH

    }

    public static final class layout {
        public static final int notification_action = 0x7F0C003B;
        public static final int notification_action_tombstone = 0x7F0C003C;
        public static final int notification_media_action = 0x7F0C003D;
        public static final int notification_media_cancel_action = 0x7F0C003E;
        public static final int notification_template_big_media = 0x7F0C003F;
        public static final int notification_template_big_media_custom = 0x7F0C0040;
        public static final int notification_template_big_media_narrow = 0x7F0C0041;
        public static final int notification_template_big_media_narrow_custom = 0x7F0C0042;
        public static final int notification_template_custom_big = 0x7F0C0043;
        public static final int notification_template_icon_group = 0x7F0C0044;
        public static final int notification_template_lines_media = 0x7F0C0045;
        public static final int notification_template_media = 0x7F0C0046;
        public static final int notification_template_media_custom = 0x7F0C0047;
        public static final int notification_template_part_chronometer = 0x7F0C0048;
        public static final int notification_template_part_time = 0x7F0C0049;

    }

    public static final class string {
        public static final int status_bar_notification_info_overflow = 0x7F0E020B;

    }

    public static final class style {
        public static final int TextAppearance_Compat_Notification = 0x7F0F0118;
        public static final int TextAppearance_Compat_Notification_Info = 0x7F0F0119;
        public static final int TextAppearance_Compat_Notification_Info_Media = 0x7F0F011A;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7F0F011B;
        public static final int TextAppearance_Compat_Notification_Line2_Media = 0x7F0F011C;
        public static final int TextAppearance_Compat_Notification_Media = 0x7F0F011D;
        public static final int TextAppearance_Compat_Notification_Time = 0x7F0F011E;
        public static final int TextAppearance_Compat_Notification_Time_Media = 0x7F0F011F;
        public static final int TextAppearance_Compat_Notification_Title = 0x7F0F0120;
        public static final int TextAppearance_Compat_Notification_Title_Media = 0x7F0F0121;
        public static final int Widget_Compat_NotificationActionContainer = 0x7F0F01CE;
        public static final int Widget_Compat_NotificationActionText = 0x7F0F01CF;
        public static final int Widget_Support_CoordinatorLayout = 0x7F0F01FE;

    }

    public static final class styleable {
        public static final int[] ColorStateListItem = null;
        public static final int ColorStateListItem_alpha = 2;
        public static final int ColorStateListItem_android_alpha = 1;
        public static final int ColorStateListItem_android_color = 0;
        public static final int[] CoordinatorLayout = null;
        public static final int[] CoordinatorLayout_Layout = null;
        public static final int CoordinatorLayout_Layout_android_layout_gravity = 0;
        public static final int CoordinatorLayout_Layout_layout_anchor = 1;
        public static final int CoordinatorLayout_Layout_layout_anchorGravity = 2;
        public static final int CoordinatorLayout_Layout_layout_behavior = 3;
        public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 4;
        public static final int CoordinatorLayout_Layout_layout_insetEdge = 5;
        public static final int CoordinatorLayout_Layout_layout_keyline = 6;
        public static final int CoordinatorLayout_keylines = 0;
        public static final int CoordinatorLayout_statusBarBackground = 1;
        public static final int[] FontFamily = null;
        public static final int[] FontFamilyFont = null;
        public static final int FontFamilyFont_android_font = 0;
        public static final int FontFamilyFont_android_fontStyle = 2;
        public static final int FontFamilyFont_android_fontVariationSettings = 4;
        public static final int FontFamilyFont_android_fontWeight = 1;
        public static final int FontFamilyFont_android_ttcIndex = 3;
        public static final int FontFamilyFont_font = 5;
        public static final int FontFamilyFont_fontStyle = 6;
        public static final int FontFamilyFont_fontVariationSettings = 7;
        public static final int FontFamilyFont_fontWeight = 8;
        public static final int FontFamilyFont_ttcIndex = 9;
        public static final int FontFamily_fontProviderAuthority = 0;
        public static final int FontFamily_fontProviderCerts = 1;
        public static final int FontFamily_fontProviderFetchStrategy = 2;
        public static final int FontFamily_fontProviderFetchTimeout = 3;
        public static final int FontFamily_fontProviderPackage = 4;
        public static final int FontFamily_fontProviderQuery = 5;
        public static final int[] GradientColor = null;
        public static final int[] GradientColorItem = null;
        public static final int GradientColorItem_android_color = 0;
        public static final int GradientColorItem_android_offset = 1;
        public static final int GradientColor_android_centerColor = 7;
        public static final int GradientColor_android_centerX = 3;
        public static final int GradientColor_android_centerY = 4;
        public static final int GradientColor_android_endColor = 1;
        public static final int GradientColor_android_endX = 10;
        public static final int GradientColor_android_endY = 11;
        public static final int GradientColor_android_gradientRadius = 5;
        public static final int GradientColor_android_startColor = 0;
        public static final int GradientColor_android_startX = 8;
        public static final int GradientColor_android_startY = 9;
        public static final int GradientColor_android_tileMode = 6;
        public static final int GradientColor_android_type = 2;

        static {
            styleable.ColorStateListItem = new int[]{0x10101A5, 0x101031F, 0x7F040027};  // attr:actionModeWebSearchDrawable
            styleable.CoordinatorLayout = new int[]{0x7F040110, 0x7F04017C};  // attr:checkedTextViewStyle
            styleable.CoordinatorLayout_Layout = new int[]{0x10100B3, 0x7F040115, 0x7F040116, 0x7F040117, 0x7F04011A, 0x7F04011B, 0x7F04011C};  // attr:chipIcon
            styleable.FontFamily = new int[]{0x7F0400DA, 0x7F0400DB, 0x7F0400DC, 0x7F0400DD, 0x7F0400DE, 0x7F0400DF};  // attr:cameraRequestPermissions
            styleable.FontFamilyFont = new int[]{0x1010532, 0x1010533, 0x101053F, 0x101056F, 0x1010570, 0x7F0400D8, 0x7F0400E0, 0x7F0400E1, 0x7F0400E2, 0x7F0401E2};  // attr:cameraPreviewFrameRate
            styleable.GradientColor = new int[]{0x101019D, 0x101019E, 0x10101A1, 0x10101A2, 0x10101A3, 0x10101A4, 0x1010201, 0x101020B, 0x1010510, 0x1010511, 0x1010512, 0x1010513};
            styleable.GradientColorItem = new int[]{0x10101A5, 0x1010514};
        }
    }

}


package android.supportv1.v4.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable.ClassLoaderCreator;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.supportv1.v4.content.ContextCompat;
import android.supportv1.v4.view.AbsSavedState;
import android.supportv1.v4.view.AccessibilityDelegateCompat;
import android.supportv1.v4.view.ViewCompat;
import android.supportv1.v4.view.accessibility.AccessibilityNodeInfoCompat;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View.MeasureSpec;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.ViewGroup.MarginLayoutParams;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;

public class SlidingPaneLayout extends ViewGroup {
    class AccessibilityDelegate extends AccessibilityDelegateCompat {
        private final Rect mTmpRect;

        public AccessibilityDelegate() {
            this.mTmpRect = new Rect();
        }

        private void copyNodeInfoNoChildren(AccessibilityNodeInfoCompat accessibilityNodeInfoCompat0, AccessibilityNodeInfoCompat accessibilityNodeInfoCompat1) {
            accessibilityNodeInfoCompat1.getBoundsInParent(this.mTmpRect);
            accessibilityNodeInfoCompat0.setBoundsInParent(this.mTmpRect);
            accessibilityNodeInfoCompat1.getBoundsInScreen(this.mTmpRect);
            accessibilityNodeInfoCompat0.setBoundsInScreen(this.mTmpRect);
            accessibilityNodeInfoCompat0.setVisibleToUser(accessibilityNodeInfoCompat1.isVisibleToUser());
            accessibilityNodeInfoCompat0.setPackageName(accessibilityNodeInfoCompat1.getPackageName());
            accessibilityNodeInfoCompat0.setClassName(accessibilityNodeInfoCompat1.getClassName());
            accessibilityNodeInfoCompat0.setContentDescription(accessibilityNodeInfoCompat1.getContentDescription());
            accessibilityNodeInfoCompat0.setEnabled(accessibilityNodeInfoCompat1.isEnabled());
            accessibilityNodeInfoCompat0.setClickable(accessibilityNodeInfoCompat1.isClickable());
            accessibilityNodeInfoCompat0.setFocusable(accessibilityNodeInfoCompat1.isFocusable());
            accessibilityNodeInfoCompat0.setFocused(accessibilityNodeInfoCompat1.isFocused());
            accessibilityNodeInfoCompat0.setAccessibilityFocused(accessibilityNodeInfoCompat1.isAccessibilityFocused());
            accessibilityNodeInfoCompat0.setSelected(accessibilityNodeInfoCompat1.isSelected());
            accessibilityNodeInfoCompat0.setLongClickable(accessibilityNodeInfoCompat1.isLongClickable());
            accessibilityNodeInfoCompat0.addAction(accessibilityNodeInfoCompat1.getActions());
            accessibilityNodeInfoCompat0.setMovementGranularities(accessibilityNodeInfoCompat1.getMovementGranularities());
        }

        public boolean filter(View view0) {
            return SlidingPaneLayout.this.isDimmed(view0);
        }

        @Override  // android.supportv1.v4.view.AccessibilityDelegateCompat
        public void onInitializeAccessibilityEvent(View view0, AccessibilityEvent accessibilityEvent0) {
            super.onInitializeAccessibilityEvent(view0, accessibilityEvent0);
            accessibilityEvent0.setClassName("android.supportv1.v4.widget.SlidingPaneLayout");
        }

        @Override  // android.supportv1.v4.view.AccessibilityDelegateCompat
        public void onInitializeAccessibilityNodeInfo(View view0, AccessibilityNodeInfoCompat accessibilityNodeInfoCompat0) {
            AccessibilityNodeInfoCompat accessibilityNodeInfoCompat1 = AccessibilityNodeInfoCompat.obtain(accessibilityNodeInfoCompat0);
            super.onInitializeAccessibilityNodeInfo(view0, accessibilityNodeInfoCompat1);
            this.copyNodeInfoNoChildren(accessibilityNodeInfoCompat0, accessibilityNodeInfoCompat1);
            accessibilityNodeInfoCompat1.recycle();
            accessibilityNodeInfoCompat0.setClassName("android.supportv1.v4.widget.SlidingPaneLayout");
            accessibilityNodeInfoCompat0.setSource(view0);
            ViewParent viewParent0 = ViewCompat.getParentForAccessibility(view0);
            if(viewParent0 instanceof View) {
                accessibilityNodeInfoCompat0.setParent(((View)viewParent0));
            }
            int v = SlidingPaneLayout.this.getChildCount();
            for(int v1 = 0; v1 < v; ++v1) {
                View view1 = SlidingPaneLayout.this.getChildAt(v1);
                if(!this.filter(view1) && view1.getVisibility() == 0) {
                    ViewCompat.setImportantForAccessibility(view1, 1);
                    accessibilityNodeInfoCompat0.addChild(view1);
                }
            }
        }

        @Override  // android.supportv1.v4.view.AccessibilityDelegateCompat
        public boolean onRequestSendAccessibilityEvent(ViewGroup viewGroup0, View view0, AccessibilityEvent accessibilityEvent0) {
            return this.filter(view0) ? false : super.onRequestSendAccessibilityEvent(viewGroup0, view0, accessibilityEvent0);
        }
    }

    class DisableLayerRunnable implements Runnable {
        final View mChildView;

        public DisableLayerRunnable(View view0) {
            this.mChildView = view0;
        }

        @Override
        public void run() {
            if(this.mChildView.getParent() == SlidingPaneLayout.this) {
                this.mChildView.setLayerType(0, null);
                SlidingPaneLayout.this.invalidateChildRegion(this.mChildView);
            }
            SlidingPaneLayout.this.mPostedRunnables.remove(this);
        }
    }

    class DragHelperCallback extends Callback {
        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public int clampViewPositionHorizontal(View view0, int v, int v1) {
            LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)SlidingPaneLayout.this.mSlideableView.getLayoutParams();
            if(SlidingPaneLayout.this.isLayoutRtlSupport()) {
                int v2 = SlidingPaneLayout.this.getWidth();
                int v3 = SlidingPaneLayout.this.getPaddingRight() + slidingPaneLayout$LayoutParams0.rightMargin;
                int v4 = v2 - (SlidingPaneLayout.this.mSlideableView.getWidth() + v3);
                return Math.max(Math.min(v, v4), v4 - SlidingPaneLayout.this.mSlideRange);
            }
            int v5 = SlidingPaneLayout.this.getPaddingLeft() + slidingPaneLayout$LayoutParams0.leftMargin;
            return Math.min(Math.max(v, v5), SlidingPaneLayout.this.mSlideRange + v5);
        }

        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public int clampViewPositionVertical(View view0, int v, int v1) {
            return view0.getTop();
        }

        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public int getViewHorizontalDragRange(View view0) {
            return SlidingPaneLayout.this.mSlideRange;
        }

        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public void onEdgeDragStarted(int v, int v1) {
            SlidingPaneLayout.this.mDragHelper.captureChildView(SlidingPaneLayout.this.mSlideableView, v1);
        }

        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public void onViewCaptured(View view0, int v) {
            SlidingPaneLayout.this.setAllChildrenVisible();
        }

        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public void onViewDragStateChanged(int v) {
            boolean z;
            SlidingPaneLayout slidingPaneLayout1;
            if(SlidingPaneLayout.this.mDragHelper.getViewDragState() == 0) {
                SlidingPaneLayout slidingPaneLayout0 = SlidingPaneLayout.this;
                if(slidingPaneLayout0.mSlideOffset == 0.0f) {
                    slidingPaneLayout0.updateObscuredViewsVisibility(slidingPaneLayout0.mSlideableView);
                    SlidingPaneLayout.this.dispatchOnPanelClosed(SlidingPaneLayout.this.mSlideableView);
                    slidingPaneLayout1 = SlidingPaneLayout.this;
                    z = false;
                }
                else {
                    slidingPaneLayout0.dispatchOnPanelOpened(slidingPaneLayout0.mSlideableView);
                    slidingPaneLayout1 = SlidingPaneLayout.this;
                    z = true;
                }
                slidingPaneLayout1.mPreservedOpenState = z;
            }
        }

        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public void onViewPositionChanged(View view0, int v, int v1, int v2, int v3) {
            SlidingPaneLayout.this.onPanelDragged(v);
            SlidingPaneLayout.this.invalidate();
        }

        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public void onViewReleased(View view0, float f, float f1) {
            int v2;
            LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
            if(SlidingPaneLayout.this.isLayoutRtlSupport()) {
                int v = SlidingPaneLayout.this.getPaddingRight() + slidingPaneLayout$LayoutParams0.rightMargin;
                if(f < 0.0f || f == 0.0f && SlidingPaneLayout.this.mSlideOffset > 0.5f) {
                    v += SlidingPaneLayout.this.mSlideRange;
                }
                int v1 = SlidingPaneLayout.this.mSlideableView.getWidth();
                v2 = SlidingPaneLayout.this.getWidth() - v - v1;
            }
            else {
                int v3 = SlidingPaneLayout.this.getPaddingLeft();
                v2 = slidingPaneLayout$LayoutParams0.leftMargin + v3;
                int v4 = Float.compare(f, 0.0f);
                if(v4 > 0 || v4 == 0 && SlidingPaneLayout.this.mSlideOffset > 0.5f) {
                    v2 += SlidingPaneLayout.this.mSlideRange;
                }
            }
            int v5 = view0.getTop();
            SlidingPaneLayout.this.mDragHelper.settleCapturedViewAt(v2, v5);
            SlidingPaneLayout.this.invalidate();
        }

        // 去混淆评级： 低(20)
        @Override  // android.supportv1.v4.widget.ViewDragHelper$Callback
        public boolean tryCaptureView(View view0, int v) {
            return SlidingPaneLayout.this.mIsUnableToDrag ? false : ((LayoutParams)view0.getLayoutParams()).slideable;
        }
    }

    public static class LayoutParams extends ViewGroup.MarginLayoutParams {
        private static final int[] ATTRS;
        Paint dimPaint;
        boolean dimWhenOffset;
        boolean slideable;
        public float weight;

        static {
            LayoutParams.ATTRS = new int[]{0x1010181};
        }

        public LayoutParams() {
            super(-1, -1);
            this.weight = 0.0f;
        }

        public LayoutParams(int v, int v1) {
            super(v, v1);
            this.weight = 0.0f;
        }

        public LayoutParams(Context context0, AttributeSet attributeSet0) {
            super(context0, attributeSet0);
            this.weight = 0.0f;
            TypedArray typedArray0 = context0.obtainStyledAttributes(attributeSet0, LayoutParams.ATTRS);
            this.weight = typedArray0.getFloat(0, 0.0f);
            typedArray0.recycle();
        }

        public LayoutParams(LayoutParams slidingPaneLayout$LayoutParams0) {
            super(slidingPaneLayout$LayoutParams0);
            this.weight = slidingPaneLayout$LayoutParams0.weight;
        }

        public LayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
            super(viewGroup$LayoutParams0);
            this.weight = 0.0f;
        }

        public LayoutParams(ViewGroup.MarginLayoutParams viewGroup$MarginLayoutParams0) {
            super(viewGroup$MarginLayoutParams0);
            this.weight = 0.0f;
        }
    }

    public interface PanelSlideListener {
        void onPanelClosed(View arg1);

        void onPanelOpened(View arg1);

        void onPanelSlide(View arg1, float arg2);
    }

    static class SavedState extends AbsSavedState {
        public static final Parcelable.Creator CREATOR;
        boolean isOpen;

        static {
            SavedState.CREATOR = new Parcelable.ClassLoaderCreator() {
                public SavedState createFromParcel(Parcel parcel0) {
                    return new SavedState(parcel0, null);
                }

                public SavedState createFromParcel(Parcel parcel0, ClassLoader classLoader0) {
                    return new SavedState(parcel0, null);
                }

                @Override  // android.os.Parcelable$Creator
                public Object createFromParcel(Parcel parcel0) {
                    return this.createFromParcel(parcel0);
                }

                @Override  // android.os.Parcelable$ClassLoaderCreator
                public Object createFromParcel(Parcel parcel0, ClassLoader classLoader0) {
                    return this.createFromParcel(parcel0, classLoader0);
                }

                public SavedState[] newArray(int v) {
                    return new SavedState[v];
                }

                @Override  // android.os.Parcelable$Creator
                public Object[] newArray(int v) {
                    return this.newArray(v);
                }
            };
        }

        public SavedState(Parcel parcel0, ClassLoader classLoader0) {
            super(parcel0, classLoader0);
            this.isOpen = parcel0.readInt() != 0;
        }

        public SavedState(Parcelable parcelable0) {
            super(parcelable0);
        }

        @Override  // android.supportv1.v4.view.AbsSavedState
        public void writeToParcel(Parcel parcel0, int v) {
            super.writeToParcel(parcel0, v);
            parcel0.writeInt(((int)this.isOpen));
        }
    }

    public static class SimplePanelSlideListener implements PanelSlideListener {
        @Override  // android.supportv1.v4.widget.SlidingPaneLayout$PanelSlideListener
        public void onPanelClosed(View view0) {
        }

        @Override  // android.supportv1.v4.widget.SlidingPaneLayout$PanelSlideListener
        public void onPanelOpened(View view0) {
        }

        @Override  // android.supportv1.v4.widget.SlidingPaneLayout$PanelSlideListener
        public void onPanelSlide(View view0, float f) {
        }
    }

    private static final int DEFAULT_FADE_COLOR = -858993460;
    private static final int DEFAULT_OVERHANG_SIZE = 0x20;
    private static final int MIN_FLING_VELOCITY = 400;
    private static final String TAG = "SlidingPaneLayout";
    private boolean mCanSlide;
    private int mCoveredFadeColor;
    private boolean mDisplayListReflectionLoaded;
    final ViewDragHelper mDragHelper;
    private boolean mFirstLayout;
    private Method mGetDisplayList;
    private float mInitialMotionX;
    private float mInitialMotionY;
    boolean mIsUnableToDrag;
    private final int mOverhangSize;
    private PanelSlideListener mPanelSlideListener;
    private int mParallaxBy;
    private float mParallaxOffset;
    final ArrayList mPostedRunnables;
    boolean mPreservedOpenState;
    private Field mRecreateDisplayList;
    private Drawable mShadowDrawableLeft;
    private Drawable mShadowDrawableRight;
    float mSlideOffset;
    int mSlideRange;
    View mSlideableView;
    private int mSliderFadeColor;
    private final Rect mTmpRect;

    public SlidingPaneLayout(Context context0) {
        this(context0, null);
    }

    public SlidingPaneLayout(Context context0, AttributeSet attributeSet0) {
        this(context0, attributeSet0, 0);
    }

    public SlidingPaneLayout(Context context0, AttributeSet attributeSet0, int v) {
        super(context0, attributeSet0, v);
        this.mSliderFadeColor = -858993460;
        this.mFirstLayout = true;
        this.mTmpRect = new Rect();
        this.mPostedRunnables = new ArrayList();
        float f = context0.getResources().getDisplayMetrics().density;
        this.mOverhangSize = (int)(32.0f * f + 0.5f);
        this.setWillNotDraw(false);
        ViewCompat.setAccessibilityDelegate(this, new AccessibilityDelegate(this));
        ViewCompat.setImportantForAccessibility(this, 1);
        ViewDragHelper viewDragHelper0 = ViewDragHelper.create(this, 0.5f, new DragHelperCallback(this));
        this.mDragHelper = viewDragHelper0;
        viewDragHelper0.setMinVelocity(f * 400.0f);
    }

    public boolean canScroll(View view0, boolean z, int v, int v1, int v2) {
        if(view0 instanceof ViewGroup) {
            int v3 = view0.getScrollX();
            int v4 = view0.getScrollY();
            for(int v5 = ((ViewGroup)view0).getChildCount() - 1; v5 >= 0; --v5) {
                View view1 = ((ViewGroup)view0).getChildAt(v5);
                int v6 = v1 + v3;
                if(v6 >= view1.getLeft() && v6 < view1.getRight()) {
                    int v7 = v2 + v4;
                    if(v7 >= view1.getTop() && v7 < view1.getBottom() && this.canScroll(view1, true, v, v6 - view1.getLeft(), v7 - view1.getTop())) {
                        return true;
                    }
                }
            }
        }
        return z ? view0.canScrollHorizontally((this.isLayoutRtlSupport() ? v : -v)) : false;
    }

    @Deprecated
    public boolean canSlide() {
        return this.mCanSlide;
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.ViewGroup
    public boolean checkLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return viewGroup$LayoutParams0 instanceof LayoutParams && super.checkLayoutParams(viewGroup$LayoutParams0);
    }

    private boolean closePane(View view0, int v) {
        if(!this.mFirstLayout && !this.smoothSlideTo(0.0f, v)) {
            return false;
        }
        this.mPreservedOpenState = false;
        return true;
    }

    public boolean closePane() {
        return this.closePane(this.mSlideableView, 0);
    }

    @Override  // android.view.View
    public void computeScroll() {
        if(this.mDragHelper.continueSettling(true)) {
            if(!this.mCanSlide) {
                this.mDragHelper.abort();
                return;
            }
            ViewCompat.postInvalidateOnAnimation(this);
        }
    }

    private void dimChildView(View view0, float f, int v) {
        LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        if(f > 0.0f && v != 0) {
            if(slidingPaneLayout$LayoutParams0.dimPaint == null) {
                slidingPaneLayout$LayoutParams0.dimPaint = new Paint();
            }
            slidingPaneLayout$LayoutParams0.dimPaint.setColorFilter(new PorterDuffColorFilter(((int)(((float)((0xFF000000 & v) >>> 24)) * f)) << 24 | v & 0xFFFFFF, PorterDuff.Mode.SRC_OVER));
            if(view0.getLayerType() != 2) {
                view0.setLayerType(2, slidingPaneLayout$LayoutParams0.dimPaint);
            }
            this.invalidateChildRegion(view0);
            return;
        }
        if(view0.getLayerType() != 0) {
            Paint paint0 = slidingPaneLayout$LayoutParams0.dimPaint;
            if(paint0 != null) {
                paint0.setColorFilter(null);
            }
            DisableLayerRunnable slidingPaneLayout$DisableLayerRunnable0 = new DisableLayerRunnable(this, view0);
            this.mPostedRunnables.add(slidingPaneLayout$DisableLayerRunnable0);
            ViewCompat.postOnAnimation(this, slidingPaneLayout$DisableLayerRunnable0);
        }
    }

    public void dispatchOnPanelClosed(View view0) {
        PanelSlideListener slidingPaneLayout$PanelSlideListener0 = this.mPanelSlideListener;
        if(slidingPaneLayout$PanelSlideListener0 != null) {
            slidingPaneLayout$PanelSlideListener0.onPanelClosed(view0);
        }
        this.sendAccessibilityEvent(0x20);
    }

    public void dispatchOnPanelOpened(View view0) {
        PanelSlideListener slidingPaneLayout$PanelSlideListener0 = this.mPanelSlideListener;
        if(slidingPaneLayout$PanelSlideListener0 != null) {
            slidingPaneLayout$PanelSlideListener0.onPanelOpened(view0);
        }
        this.sendAccessibilityEvent(0x20);
    }

    public void dispatchOnPanelSlide(View view0) {
        PanelSlideListener slidingPaneLayout$PanelSlideListener0 = this.mPanelSlideListener;
        if(slidingPaneLayout$PanelSlideListener0 != null) {
            slidingPaneLayout$PanelSlideListener0.onPanelSlide(view0, this.mSlideOffset);
        }
    }

    @Override  // android.view.View
    public void draw(Canvas canvas0) {
        int v4;
        int v3;
        super.draw(canvas0);
        Drawable drawable0 = this.isLayoutRtlSupport() ? this.mShadowDrawableRight : this.mShadowDrawableLeft;
        View view0 = this.getChildCount() <= 1 ? null : this.getChildAt(1);
        if(view0 != null && drawable0 != null) {
            int v = view0.getTop();
            int v1 = view0.getBottom();
            int v2 = drawable0.getIntrinsicWidth();
            if(this.isLayoutRtlSupport()) {
                v3 = view0.getRight();
                v4 = v2 + v3;
            }
            else {
                int v5 = view0.getLeft();
                v4 = v5;
                v3 = v5 - v2;
            }
            drawable0.setBounds(v3, v, v4, v1);
            drawable0.draw(canvas0);
        }
    }

    @Override  // android.view.ViewGroup
    public boolean drawChild(Canvas canvas0, View view0, long v) {
        LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        int v1 = canvas0.save();
        if(this.mCanSlide && !slidingPaneLayout$LayoutParams0.slideable && this.mSlideableView != null) {
            canvas0.getClipBounds(this.mTmpRect);
            if(this.isLayoutRtlSupport()) {
                this.mTmpRect.left = Math.max(this.mTmpRect.left, this.mSlideableView.getRight());
            }
            else {
                this.mTmpRect.right = Math.min(this.mTmpRect.right, this.mSlideableView.getLeft());
            }
            canvas0.clipRect(this.mTmpRect);
        }
        boolean z = super.drawChild(canvas0, view0, v);
        canvas0.restoreToCount(v1);
        return z;
    }

    @Override  // android.view.ViewGroup
    public ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams();
    }

    @Override  // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet0) {
        return new LayoutParams(this.getContext(), attributeSet0);
    }

    // 去混淆评级： 低(20)
    @Override  // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams viewGroup$LayoutParams0) {
        return viewGroup$LayoutParams0 instanceof ViewGroup.MarginLayoutParams ? new LayoutParams(((ViewGroup.MarginLayoutParams)viewGroup$LayoutParams0)) : new LayoutParams(viewGroup$LayoutParams0);
    }

    public int getCoveredFadeColor() {
        return this.mCoveredFadeColor;
    }

    public int getParallaxDistance() {
        return this.mParallaxBy;
    }

    public int getSliderFadeColor() {
        return this.mSliderFadeColor;
    }

    public void invalidateChildRegion(View view0) {
        ViewCompat.setLayerPaint(view0, ((LayoutParams)view0.getLayoutParams()).dimPaint);
    }

    public boolean isDimmed(View view0) {
        if(view0 == null) {
            return false;
        }
        LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
        return this.mCanSlide && slidingPaneLayout$LayoutParams0.dimWhenOffset && this.mSlideOffset > 0.0f;
    }

    public boolean isLayoutRtlSupport() {
        return ViewCompat.getLayoutDirection(this) == 1;
    }

    public boolean isOpen() {
        return !this.mCanSlide || this.mSlideOffset == 1.0f;
    }

    public boolean isSlideable() {
        return this.mCanSlide;
    }

    @Override  // android.view.ViewGroup
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.mFirstLayout = true;
    }

    @Override  // android.view.ViewGroup
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        this.mFirstLayout = true;
        int v = this.mPostedRunnables.size();
        for(int v1 = 0; v1 < v; ++v1) {
            ((DisableLayerRunnable)this.mPostedRunnables.get(v1)).run();
        }
        this.mPostedRunnables.clear();
    }

    @Override  // android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent0) {
        int v = motionEvent0.getActionMasked();
        if(!this.mCanSlide && v == 0 && this.getChildCount() > 1) {
            View view0 = this.getChildAt(1);
            if(view0 != null) {
                int v1 = (int)motionEvent0.getX();
                int v2 = (int)motionEvent0.getY();
                this.mPreservedOpenState = !this.mDragHelper.isViewUnder(view0, v1, v2);
            }
        }
        if(this.mCanSlide && (!this.mIsUnableToDrag || v == 0)) {
            if(v != 1 && v != 3) {
                switch(v) {
                    case 0: {
                        this.mIsUnableToDrag = false;
                        float f = motionEvent0.getX();
                        float f1 = motionEvent0.getY();
                        this.mInitialMotionX = f;
                        this.mInitialMotionY = f1;
                        return !this.mDragHelper.isViewUnder(this.mSlideableView, ((int)f), ((int)f1)) || !this.isDimmed(this.mSlideableView) ? this.mDragHelper.shouldInterceptTouchEvent(motionEvent0) : this.mDragHelper.shouldInterceptTouchEvent(motionEvent0) || true;
                    }
                    case 2: {
                        float f2 = motionEvent0.getX();
                        float f3 = motionEvent0.getY();
                        float f4 = Math.abs(f2 - this.mInitialMotionX);
                        if(f4 > ((float)this.mDragHelper.getTouchSlop()) && Math.abs(f3 - this.mInitialMotionY) > f4) {
                            this.mDragHelper.cancel();
                            this.mIsUnableToDrag = true;
                            return false;
                        }
                        return this.mDragHelper.shouldInterceptTouchEvent(motionEvent0);
                    }
                    default: {
                        return this.mDragHelper.shouldInterceptTouchEvent(motionEvent0);
                    }
                }
            }
            this.mDragHelper.cancel();
            return false;
        }
        this.mDragHelper.cancel();
        return super.onInterceptTouchEvent(motionEvent0);
    }

    @Override  // android.view.ViewGroup
    public void onLayout(boolean z, int v, int v1, int v2, int v3) {
        int v20;
        int v19;
        int v17;
        int v16;
        boolean z1 = this.isLayoutRtlSupport();
        ViewDragHelper viewDragHelper0 = this.mDragHelper;
        if(z1) {
            viewDragHelper0.setEdgeTrackingEnabled(2);
        }
        else {
            viewDragHelper0.setEdgeTrackingEnabled(1);
        }
        int v4 = v2 - v;
        int v5 = z1 ? this.getPaddingRight() : this.getPaddingLeft();
        int v6 = z1 ? this.getPaddingLeft() : this.getPaddingRight();
        int v7 = this.getPaddingTop();
        int v8 = this.getChildCount();
        if(this.mFirstLayout) {
            this.mSlideOffset = !this.mCanSlide || !this.mPreservedOpenState ? 0.0f : 1.0f;
        }
        int v9 = v5;
        int v10 = 0;
        while(v10 < v8) {
            View view0 = this.getChildAt(v10);
            if(view0.getVisibility() != 8) {
                LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
                int v11 = view0.getMeasuredWidth();
                if(slidingPaneLayout$LayoutParams0.slideable) {
                    int v12 = v4 - v6;
                    int v13 = Math.min(v5, v12 - this.mOverhangSize) - v9 - (slidingPaneLayout$LayoutParams0.leftMargin + slidingPaneLayout$LayoutParams0.rightMargin);
                    this.mSlideRange = v13;
                    int v14 = z1 ? slidingPaneLayout$LayoutParams0.rightMargin : slidingPaneLayout$LayoutParams0.leftMargin;
                    slidingPaneLayout$LayoutParams0.dimWhenOffset = v11 / 2 + (v9 + v14 + v13) > v12;
                    int v15 = (int)(((float)v13) * this.mSlideOffset);
                    v16 = v14 + v15 + v9;
                    this.mSlideOffset = ((float)v15) / ((float)v13);
                }
                else {
                    if(this.mCanSlide) {
                        int v18 = this.mParallaxBy;
                        if(v18 != 0) {
                            v17 = (int)((1.0f - this.mSlideOffset) * ((float)v18));
                            v16 = v5;
                            goto label_39;
                        }
                    }
                    v16 = v5;
                }
                v17 = 0;
            label_39:
                if(z1) {
                    v19 = v4 - v16 + v17;
                    v20 = v19 - v11;
                }
                else {
                    v20 = v16 - v17;
                    v19 = v20 + v11;
                }
                view0.layout(v20, v7, v19, view0.getMeasuredHeight() + v7);
                v9 = v16;
                v5 = view0.getWidth() + v5;
            }
            ++v10;
        }
        if(this.mFirstLayout) {
            if(this.mCanSlide) {
                if(this.mParallaxBy != 0) {
                    this.parallaxOtherViews(this.mSlideOffset);
                }
                if(((LayoutParams)this.mSlideableView.getLayoutParams()).dimWhenOffset) {
                    this.dimChildView(this.mSlideableView, this.mSlideOffset, this.mSliderFadeColor);
                }
            }
            else {
                for(int v21 = 0; v21 < v8; ++v21) {
                    this.dimChildView(this.getChildAt(v21), 0.0f, this.mSliderFadeColor);
                }
            }
            this.updateObscuredViewsVisibility(this.mSlideableView);
        }
        this.mFirstLayout = false;
    }

    @Override  // android.view.View
    public void onMeasure(int v, int v1) {
        int v26;
        int v25;
        int v24;
        int v23;
        int v22;
        int v16;
        int v14;
        int v7;
        int v6;
        int v2 = View.MeasureSpec.getMode(v);
        int v3 = View.MeasureSpec.getSize(v);
        int v4 = View.MeasureSpec.getMode(v1);
        int v5 = View.MeasureSpec.getSize(v1);
        if(v2 != 0x40000000) {
            if(!this.isInEditMode()) {
                throw new IllegalStateException("Width must have an exact value or MATCH_PARENT");
            }
            if(v2 != 0x80000000 && v2 == 0) {
                v3 = 300;
            }
        }
        else if(v4 == 0) {
            if(!this.isInEditMode()) {
                throw new IllegalStateException("Height must not be UNSPECIFIED");
            }
            v4 = 0x80000000;
            v5 = 300;
        }
        switch(v4) {
            case 0x80000000: {
                v7 = v5 - this.getPaddingTop() - this.getPaddingBottom();
                v6 = 0;
                break;
            }
            case 0x40000000: {
                v6 = v5 - this.getPaddingTop() - this.getPaddingBottom();
                v7 = v6;
                break;
            }
            default: {
                v6 = 0;
                v7 = 0;
            }
        }
        int v8 = v3 - this.getPaddingLeft() - this.getPaddingRight();
        int v9 = this.getChildCount();
        this.mSlideableView = null;
        int v10 = v8;
        int v11 = 0;
        boolean z = false;
        float f = 0.0f;
        while(v11 < v9) {
            View view0 = this.getChildAt(v11);
            LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)view0.getLayoutParams();
            if(view0.getVisibility() == 8) {
                slidingPaneLayout$LayoutParams0.dimWhenOffset = false;
            }
            else {
                float f1 = slidingPaneLayout$LayoutParams0.weight;
                if(f1 > 0.0f) {
                    f += f1;
                    if(slidingPaneLayout$LayoutParams0.width != 0) {
                        goto label_42;
                    }
                }
                else {
                label_42:
                    int v12 = slidingPaneLayout$LayoutParams0.leftMargin + slidingPaneLayout$LayoutParams0.rightMargin;
                    int v13 = slidingPaneLayout$LayoutParams0.width;
                    if(v13 == -2) {
                        v14 = View.MeasureSpec.makeMeasureSpec(v8 - v12, 0x80000000);
                    }
                    else {
                        if(v13 == -1) {
                            v13 = v8 - v12;
                        }
                        v14 = View.MeasureSpec.makeMeasureSpec(v13, 0x40000000);
                    }
                    int v15 = slidingPaneLayout$LayoutParams0.height;
                    if(v15 == -2) {
                        v16 = View.MeasureSpec.makeMeasureSpec(v7, 0x80000000);
                    }
                    else {
                        v16 = v15 == -1 ? View.MeasureSpec.makeMeasureSpec(v7, 0x40000000) : View.MeasureSpec.makeMeasureSpec(v15, 0x40000000);
                    }
                    view0.measure(v14, v16);
                    int v17 = view0.getMeasuredWidth();
                    int v18 = view0.getMeasuredHeight();
                    if(v4 == 0x80000000 && v18 > v6) {
                        v6 = Math.min(v18, v7);
                    }
                    v10 -= v17;
                    slidingPaneLayout$LayoutParams0.slideable = v10 < 0;
                    if(v10 < 0) {
                        this.mSlideableView = view0;
                    }
                    z |= v10 < 0;
                }
            }
            ++v11;
        }
        if(z || f > 0.0f) {
            int v19 = v8 - this.mOverhangSize;
            int v20 = 0;
            while(v20 < v9) {
                View view1 = this.getChildAt(v20);
                if(view1.getVisibility() == 8) {
                    v24 = v19;
                }
                else {
                    LayoutParams slidingPaneLayout$LayoutParams1 = (LayoutParams)view1.getLayoutParams();
                    if(view1.getVisibility() == 8) {
                        v24 = v19;
                    }
                    else {
                        boolean z1 = slidingPaneLayout$LayoutParams1.width == 0 && slidingPaneLayout$LayoutParams1.weight > 0.0f;
                        int v21 = z1 ? 0 : view1.getMeasuredWidth();
                        if(z && view1 != this.mSlideableView) {
                            if(slidingPaneLayout$LayoutParams1.width < 0 && (v21 > v19 || slidingPaneLayout$LayoutParams1.weight > 0.0f)) {
                                if(z1) {
                                    v22 = slidingPaneLayout$LayoutParams1.height;
                                    if(v22 == -2) {
                                        v23 = View.MeasureSpec.makeMeasureSpec(v7, 0x80000000);
                                        goto label_89;
                                    }
                                    else if(v22 == -1) {
                                        v23 = View.MeasureSpec.makeMeasureSpec(v7, 0x40000000);
                                        goto label_89;
                                    }
                                }
                                else {
                                    v22 = view1.getMeasuredHeight();
                                }
                                v23 = View.MeasureSpec.makeMeasureSpec(v22, 0x40000000);
                            label_89:
                                view1.measure(View.MeasureSpec.makeMeasureSpec(v19, 0x40000000), v23);
                            }
                            v24 = v19;
                        }
                        else if(slidingPaneLayout$LayoutParams1.weight > 0.0f) {
                            if(slidingPaneLayout$LayoutParams1.width == 0) {
                                v25 = slidingPaneLayout$LayoutParams1.height;
                                if(v25 == -2) {
                                    v26 = View.MeasureSpec.makeMeasureSpec(v7, 0x80000000);
                                    goto label_103;
                                }
                                else if(v25 == -1) {
                                    v26 = View.MeasureSpec.makeMeasureSpec(v7, 0x40000000);
                                    goto label_103;
                                }
                            }
                            else {
                                v25 = view1.getMeasuredHeight();
                            }
                            v26 = View.MeasureSpec.makeMeasureSpec(v25, 0x40000000);
                        label_103:
                            if(z) {
                                int v27 = v8 - (slidingPaneLayout$LayoutParams1.leftMargin + slidingPaneLayout$LayoutParams1.rightMargin);
                                v24 = v19;
                                int v28 = View.MeasureSpec.makeMeasureSpec(v27, 0x40000000);
                                if(v21 != v27) {
                                    view1.measure(v28, v26);
                                }
                            }
                            else {
                                v24 = v19;
                                view1.measure(View.MeasureSpec.makeMeasureSpec(v21 + ((int)(slidingPaneLayout$LayoutParams1.weight * ((float)Math.max(0, v10)) / f)), 0x40000000), v26);
                            }
                        }
                        else {
                            v24 = v19;
                        }
                    }
                }
                ++v20;
                v19 = v24;
            }
        }
        int v29 = this.getPaddingTop();
        this.setMeasuredDimension(v3, this.getPaddingBottom() + (v29 + v6));
        this.mCanSlide = z;
        if(this.mDragHelper.getViewDragState() != 0 && !z) {
            this.mDragHelper.abort();
        }
    }

    public void onPanelDragged(int v) {
        if(this.mSlideableView == null) {
            this.mSlideOffset = 0.0f;
            return;
        }
        boolean z = this.isLayoutRtlSupport();
        LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)this.mSlideableView.getLayoutParams();
        int v1 = this.mSlideableView.getWidth();
        if(z) {
            v = this.getWidth() - v - v1;
        }
        float f = ((float)(v - ((z ? this.getPaddingRight() : this.getPaddingLeft()) + (z ? slidingPaneLayout$LayoutParams0.rightMargin : slidingPaneLayout$LayoutParams0.leftMargin)))) / ((float)this.mSlideRange);
        this.mSlideOffset = f;
        if(this.mParallaxBy != 0) {
            this.parallaxOtherViews(f);
        }
        if(slidingPaneLayout$LayoutParams0.dimWhenOffset) {
            this.dimChildView(this.mSlideableView, this.mSlideOffset, this.mSliderFadeColor);
        }
        this.dispatchOnPanelSlide(this.mSlideableView);
    }

    @Override  // android.view.View
    public void onRestoreInstanceState(Parcelable parcelable0) {
        if(!(parcelable0 instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable0);
            return;
        }
        super.onRestoreInstanceState(((SavedState)parcelable0).getSuperState());
        if(((SavedState)parcelable0).isOpen) {
            this.openPane();
        }
        else {
            this.closePane();
        }
        this.mPreservedOpenState = ((SavedState)parcelable0).isOpen;
    }

    @Override  // android.view.View
    public Parcelable onSaveInstanceState() {
        Parcelable parcelable0 = new SavedState(super.onSaveInstanceState());
        parcelable0.isOpen = this.isSlideable() ? this.isOpen() : this.mPreservedOpenState;
        return parcelable0;
    }

    @Override  // android.view.View
    public void onSizeChanged(int v, int v1, int v2, int v3) {
        super.onSizeChanged(v, v1, v2, v3);
        if(v != v2) {
            this.mFirstLayout = true;
        }
    }

    @Override  // android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent0) {
        if(!this.mCanSlide) {
            return super.onTouchEvent(motionEvent0);
        }
        this.mDragHelper.processTouchEvent(motionEvent0);
        switch(motionEvent0.getActionMasked()) {
            case 0: {
                float f = motionEvent0.getX();
                float f1 = motionEvent0.getY();
                this.mInitialMotionX = f;
                this.mInitialMotionY = f1;
                return true;
            }
            case 1: {
                if(this.isDimmed(this.mSlideableView)) {
                    float f2 = motionEvent0.getX();
                    float f3 = motionEvent0.getY();
                    float f4 = f2 - this.mInitialMotionX;
                    float f5 = f3 - this.mInitialMotionY;
                    int v = this.mDragHelper.getTouchSlop();
                    if(f5 * f5 + f4 * f4 < ((float)(v * v)) && this.mDragHelper.isViewUnder(this.mSlideableView, ((int)f2), ((int)f3))) {
                        this.closePane(this.mSlideableView, 0);
                        return true;
                    }
                }
                return true;
            }
            default: {
                return true;
            }
        }
    }

    private boolean openPane(View view0, int v) {
        if(!this.mFirstLayout && !this.smoothSlideTo(1.0f, v)) {
            return false;
        }
        this.mPreservedOpenState = true;
        return true;
    }

    public boolean openPane() {
        return this.openPane(this.mSlideableView, 0);
    }

    private void parallaxOtherViews(float f) {
        boolean z = this.isLayoutRtlSupport();
        LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)this.mSlideableView.getLayoutParams();
        boolean z1 = slidingPaneLayout$LayoutParams0.dimWhenOffset && (z ? slidingPaneLayout$LayoutParams0.rightMargin : slidingPaneLayout$LayoutParams0.leftMargin) <= 0;
        int v1 = this.getChildCount();
        for(int v = 0; v < v1; ++v) {
            View view0 = this.getChildAt(v);
            if(view0 != this.mSlideableView) {
                int v2 = (int)((1.0f - this.mParallaxOffset) * ((float)this.mParallaxBy));
                this.mParallaxOffset = f;
                view0.offsetLeftAndRight((z ? -(v2 - ((int)((1.0f - f) * ((float)this.mParallaxBy)))) : v2 - ((int)((1.0f - f) * ((float)this.mParallaxBy)))));
                if(z1) {
                    this.dimChildView(view0, (z ? this.mParallaxOffset - 1.0f : 1.0f - this.mParallaxOffset), this.mCoveredFadeColor);
                }
            }
        }
    }

    @Override  // android.view.ViewGroup
    public void requestChildFocus(View view0, View view1) {
        super.requestChildFocus(view0, view1);
        if(!this.isInTouchMode() && !this.mCanSlide) {
            this.mPreservedOpenState = view0 == this.mSlideableView;
        }
    }

    public void setAllChildrenVisible() {
        int v = this.getChildCount();
        for(int v1 = 0; v1 < v; ++v1) {
            View view0 = this.getChildAt(v1);
            if(view0.getVisibility() == 4) {
                view0.setVisibility(0);
            }
        }
    }

    public void setCoveredFadeColor(int v) {
        this.mCoveredFadeColor = v;
    }

    public void setPanelSlideListener(PanelSlideListener slidingPaneLayout$PanelSlideListener0) {
        this.mPanelSlideListener = slidingPaneLayout$PanelSlideListener0;
    }

    public void setParallaxDistance(int v) {
        this.mParallaxBy = v;
        this.requestLayout();
    }

    @Deprecated
    public void setShadowDrawable(Drawable drawable0) {
        this.setShadowDrawableLeft(drawable0);
    }

    public void setShadowDrawableLeft(Drawable drawable0) {
        this.mShadowDrawableLeft = drawable0;
    }

    public void setShadowDrawableRight(Drawable drawable0) {
        this.mShadowDrawableRight = drawable0;
    }

    @Deprecated
    public void setShadowResource(int v) {
        this.setShadowDrawable(this.getResources().getDrawable(v));
    }

    public void setShadowResourceLeft(int v) {
        this.setShadowDrawableLeft(ContextCompat.getDrawable(this.getContext(), v));
    }

    public void setShadowResourceRight(int v) {
        this.setShadowDrawableRight(ContextCompat.getDrawable(this.getContext(), v));
    }

    public void setSliderFadeColor(int v) {
        this.mSliderFadeColor = v;
    }

    @Deprecated
    public void smoothSlideClosed() {
        this.closePane();
    }

    @Deprecated
    public void smoothSlideOpen() {
        this.openPane();
    }

    public boolean smoothSlideTo(float f, int v) {
        int v4;
        if(!this.mCanSlide) {
            return false;
        }
        boolean z = this.isLayoutRtlSupport();
        LayoutParams slidingPaneLayout$LayoutParams0 = (LayoutParams)this.mSlideableView.getLayoutParams();
        if(z) {
            int v1 = this.getPaddingRight();
            int v2 = slidingPaneLayout$LayoutParams0.rightMargin;
            int v3 = this.mSlideableView.getWidth();
            v4 = (int)(((float)this.getWidth()) - (f * ((float)this.mSlideRange) + ((float)(v1 + v2)) + ((float)v3)));
        }
        else {
            int v5 = this.getPaddingLeft();
            v4 = (int)(f * ((float)this.mSlideRange) + ((float)(v5 + slidingPaneLayout$LayoutParams0.leftMargin)));
        }
        View view0 = this.mSlideableView;
        int v6 = view0.getTop();
        if(this.mDragHelper.smoothSlideViewTo(view0, v4, v6)) {
            this.setAllChildrenVisible();
            ViewCompat.postInvalidateOnAnimation(this);
            return true;
        }
        return false;
    }

    public void updateObscuredViewsVisibility(View view0) {
        int v14;
        int v11;
        int v8;
        int v7;
        int v6;
        int v5;
        boolean z = this.isLayoutRtlSupport();
        int v = z ? this.getWidth() - this.getPaddingRight() : this.getPaddingLeft();
        int v1 = z ? this.getPaddingLeft() : this.getWidth() - this.getPaddingRight();
        int v2 = this.getPaddingTop();
        int v3 = this.getHeight();
        int v4 = this.getPaddingBottom();
        if(view0 == null || !SlidingPaneLayout.viewIsOpaque(view0)) {
            v5 = 0;
            v6 = 0;
            v7 = 0;
            v8 = 0;
        }
        else {
            v5 = view0.getLeft();
            v6 = view0.getRight();
            v7 = view0.getTop();
            v8 = view0.getBottom();
        }
        int v9 = this.getChildCount();
        int v10 = 0;
        while(v10 < v9) {
            View view1 = this.getChildAt(v10);
            if(view1 == view0) {
                break;
            }
            if(view1.getVisibility() == 8) {
                v11 = v;
            }
            else {
                int v12 = Math.max((z ? v1 : v), view1.getLeft());
                int v13 = Math.max(v2, view1.getTop());
                if(z) {
                    v14 = v;
                    v11 = v14;
                }
                else {
                    v11 = v;
                    v14 = v1;
                }
                view1.setVisibility((v12 < v5 || v13 < v7 || Math.min(v14, view1.getRight()) > v6 || Math.min(v3 - v4, view1.getBottom()) > v8 ? 0 : 4));
            }
            ++v10;
            v = v11;
        }
    }

    private static boolean viewIsOpaque(View view0) {
        return view0.isOpaque();
    }
}


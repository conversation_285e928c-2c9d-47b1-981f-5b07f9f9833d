package android.supportv1.v4.widget;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.widget.ImageView;

public class ImageViewCompat {
    public static ColorStateList getImageTintList(ImageView imageView0) {
        return imageView0.getImageTintList();
    }

    public static PorterDuff.Mode getImageTintMode(ImageView imageView0) {
        return imageView0.getImageTintMode();
    }

    public static void setImageTintList(ImageView imageView0, ColorStateList colorStateList0) {
        int v = Build.VERSION.SDK_INT;
        imageView0.setImageTintList(colorStateList0);
        if(v == 21) {
            Drawable drawable0 = imageView0.getDrawable();
            if(drawable0 != null && (imageView0.getImageTintList() != null && imageView0.getImageTintMode() != null)) {
                if(drawable0.isStateful()) {
                    drawable0.setState(imageView0.getDrawableState());
                }
                imageView0.setImageDrawable(drawable0);
            }
        }
    }

    public static void setImageTintMode(ImageView imageView0, PorterDuff.Mode porterDuff$Mode0) {
        int v = Build.VERSION.SDK_INT;
        imageView0.setImageTintMode(porterDuff$Mode0);
        if(v == 21) {
            Drawable drawable0 = imageView0.getDrawable();
            if(drawable0 != null && (imageView0.getImageTintList() != null && imageView0.getImageTintMode() != null)) {
                if(drawable0.isStateful()) {
                    drawable0.setState(imageView0.getDrawableState());
                }
                imageView0.setImageDrawable(drawable0);
            }
        }
    }
}


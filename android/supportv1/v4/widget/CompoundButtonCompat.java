package android.supportv1.v4.widget;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff.Mode;
import android.graphics.drawable.Drawable;
import android.os.Build.VERSION;
import android.widget.CompoundButton;
import java.lang.reflect.Field;

public final class CompoundButtonCompat {
    private static final String TAG = "CompoundButtonCompat";
    private static Field sButtonDrawableField;
    private static boolean sButtonDrawableFieldFetched;

    public static Drawable getButtonDrawable(CompoundButton compoundButton0) {
        if(Build.VERSION.SDK_INT >= 23) {
            return compoundButton0.getButtonDrawable();
        }
        if(!CompoundButtonCompat.sButtonDrawableFieldFetched) {
            try {
                Field field0 = CompoundButton.class.getDeclaredField("mButtonDrawable");
                CompoundButtonCompat.sButtonDrawableField = field0;
                field0.setAccessible(true);
            }
            catch(NoSuchFieldException unused_ex) {
            }
            CompoundButtonCompat.sButtonDrawableFieldFetched = true;
        }
        Field field1 = CompoundButtonCompat.sButtonDrawableField;
        if(field1 != null) {
            try {
                return (Drawable)field1.get(compoundButton0);
            }
            catch(IllegalAccessException unused_ex) {
                CompoundButtonCompat.sButtonDrawableField = null;
            }
        }
        return null;
    }

    public static ColorStateList getButtonTintList(CompoundButton compoundButton0) {
        return compoundButton0.getButtonTintList();
    }

    public static PorterDuff.Mode getButtonTintMode(CompoundButton compoundButton0) {
        return compoundButton0.getButtonTintMode();
    }

    public static void setButtonTintList(CompoundButton compoundButton0, ColorStateList colorStateList0) {
        compoundButton0.setButtonTintList(colorStateList0);
    }

    public static void setButtonTintMode(CompoundButton compoundButton0, PorterDuff.Mode porterDuff$Mode0) {
        compoundButton0.setButtonTintMode(porterDuff$Mode0);
    }
}


package android.supportv1.v4.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Objects;

public class AtomicFile {
    private final File mBackupName;
    private final File mBaseName;

    public AtomicFile(File file0) {
        this.mBaseName = file0;
        this.mBackupName = new File(file0.getPath() + ".bak");
    }

    public void delete() {
        this.mBaseName.delete();
        this.mBackupName.delete();
    }

    public void failWrite(FileOutputStream fileOutputStream0) {
        if(fileOutputStream0 != null) {
            AtomicFile.sync(fileOutputStream0);
            try {
                fileOutputStream0.close();
                this.mBaseName.delete();
                this.mBackupName.renameTo(this.mBaseName);
            }
            catch(IOException unused_ex) {
            }
        }
    }

    public void finishWrite(FileOutputStream fileOutputStream0) {
        if(fileOutputStream0 != null) {
            AtomicFile.sync(fileOutputStream0);
            try {
                fileOutputStream0.close();
                this.mBackupName.delete();
            }
            catch(IOException unused_ex) {
            }
        }
    }

    public File getBaseFile() {
        return this.mBaseName;
    }

    public FileInputStream openRead() throws FileNotFoundException {
        if(this.mBackupName.exists()) {
            this.mBaseName.delete();
            this.mBackupName.renameTo(this.mBaseName);
        }
        return new FileInputStream(this.mBaseName);
    }

    public byte[] readFully() throws IOException {
        try(FileInputStream fileInputStream0 = this.openRead()) {
            byte[] arr_b = new byte[fileInputStream0.available()];
            int v = 0;
            int v1;
            while((v1 = fileInputStream0.read(arr_b, v, arr_b.length - v)) > 0) {
                v += v1;
                int v2 = fileInputStream0.available();
                if(v2 <= arr_b.length - v) {
                    continue;
                }
                byte[] arr_b1 = new byte[v2 + v];
                System.arraycopy(arr_b, 0, arr_b1, 0, v);
                arr_b = arr_b1;
            }
            return arr_b;
        }
    }

    public FileOutputStream startWrite() throws IOException {
        if(this.mBaseName.exists()) {
            if(this.mBackupName.exists()) {
                this.mBaseName.delete();
            }
            else if(!this.mBaseName.renameTo(this.mBackupName)) {
                Objects.toString(this.mBaseName);
                Objects.toString(this.mBackupName);
            }
        }
        try {
            return new FileOutputStream(this.mBaseName);
        }
        catch(FileNotFoundException unused_ex) {
            if(this.mBaseName.getParentFile().mkdirs()) {
                try {
                    return new FileOutputStream(this.mBaseName);
                }
                catch(FileNotFoundException unused_ex) {
                    throw new IOException("Couldn\'t create " + this.mBaseName);
                }
            }
            throw new IOException("Couldn\'t create directory " + this.mBaseName);
        }
    }

    private static boolean sync(FileOutputStream fileOutputStream0) {
        try {
            fileOutputStream0.getFD().sync();
            return true;
        }
        catch(IOException unused_ex) {
            return false;
        }
    }
}


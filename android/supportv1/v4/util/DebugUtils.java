package android.supportv1.v4.util;

public class DebugUtils {
    public static void buildShortClassTag(Object object0, StringBuilder stringBuilder0) {
        String s;
        if(object0 == null) {
            s = "null";
        }
        else {
            String s1 = object0.getClass().getSimpleName();
            if(s1.length() <= 0) {
                s1 = object0.getClass().getName();
                int v = s1.lastIndexOf(46);
                if(v > 0) {
                    s1 = s1.substring(v + 1);
                }
            }
            stringBuilder0.append(s1);
            stringBuilder0.append('{');
            s = Integer.toHexString(System.identityHashCode(object0));
        }
        stringBuilder0.append(s);
    }
}


package android.supportv1.v4.util;

import java.util.Objects;

public class ObjectsCompat {
    public static boolean equals(Object object0, Object object1) {
        return Objects.equals(object0, object1);
    }

    public static int hash(Object[] arr_object) {
        return Objects.hash(arr_object);
    }

    public static int hashCode(Object object0) {
        return object0 == null ? 0 : object0.hashCode();
    }
}


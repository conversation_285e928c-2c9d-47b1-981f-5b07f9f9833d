package android.supportv1.v4.util;

import java.util.Collection;
import java.util.Map.Entry;
import java.util.Map;
import java.util.Set;

public class ArrayMap extends SimpleArrayMap implements Map {
    MapCollections mCollections;

    public ArrayMap() {
    }

    public ArrayMap(int v) {
        super(v);
    }

    public ArrayMap(SimpleArrayMap simpleArrayMap0) {
        super(simpleArrayMap0);
    }

    public boolean containsAll(Collection collection0) {
        return MapCollections.containsAllHelper(this, collection0);
    }

    @Override
    public Set entrySet() {
        return this.getCollection().getEntrySet();
    }

    private MapCollections getCollection() {
        if(this.mCollections == null) {
            this.mCollections = new MapCollections() {
                @Override  // android.supportv1.v4.util.MapCollections
                public void colClear() {
                    ArrayMap.this.clear();
                }

                @Override  // android.supportv1.v4.util.MapCollections
                public Object colGetEntry(int v, int v1) {
                    return ArrayMap.this.mArray[(v << 1) + v1];
                }

                @Override  // android.supportv1.v4.util.MapCollections
                public Map colGetMap() {
                    return ArrayMap.this;
                }

                @Override  // android.supportv1.v4.util.MapCollections
                public int colGetSize() {
                    return ArrayMap.this.mSize;
                }

                @Override  // android.supportv1.v4.util.MapCollections
                public int colIndexOfKey(Object object0) {
                    return ArrayMap.this.indexOfKey(object0);
                }

                @Override  // android.supportv1.v4.util.MapCollections
                public int colIndexOfValue(Object object0) {
                    return ArrayMap.this.indexOfValue(object0);
                }

                @Override  // android.supportv1.v4.util.MapCollections
                public void colPut(Object object0, Object object1) {
                    ArrayMap.this.put(object0, object1);
                }

                @Override  // android.supportv1.v4.util.MapCollections
                public void colRemoveAt(int v) {
                    ArrayMap.this.removeAt(v);
                }

                @Override  // android.supportv1.v4.util.MapCollections
                public Object colSetValue(int v, Object object0) {
                    return ArrayMap.this.setValueAt(v, object0);
                }
            };
        }
        return this.mCollections;
    }

    @Override
    public Set keySet() {
        return this.getCollection().getKeySet();
    }

    @Override
    public void putAll(Map map0) {
        this.ensureCapacity(map0.size() + this.mSize);
        for(Object object0: map0.entrySet()) {
            this.put(((Map.Entry)object0).getKey(), ((Map.Entry)object0).getValue());
        }
    }

    public boolean removeAll(Collection collection0) {
        return MapCollections.removeAllHelper(this, collection0);
    }

    public boolean retainAll(Collection collection0) {
        return MapCollections.retainAllHelper(this, collection0);
    }

    @Override
    public Collection values() {
        return this.getCollection().getValues();
    }
}


package android.supportv1.v4.graphics.drawable;

import android.content.Context;
import android.graphics.drawable.Icon;
import android.hardware.fingerprint.FingerprintManager.CryptoObject;
import android.hardware.fingerprint.FingerprintManager;
import android.media.browse.MediaBrowser.ItemCallback;
import android.os.UserManager;
import android.text.StaticLayout.Builder;
import android.text.TextPaint;
import java.security.Signature;
import javax.crypto.Cipher;
import javax.crypto.Mac;

public abstract class a {
    public static Icon d(Object object0) [...] // Inlined contents

    public static FingerprintManager.CryptoObject f(Signature signature0) {
        return new FingerprintManager.CryptoObject(signature0);
    }

    public static FingerprintManager.CryptoObject g(Cipher cipher0) {
        return new FingerprintManager.CryptoObject(cipher0);
    }

    public static FingerprintManager.CryptoObject h(Mac mac0) {
        return new FingerprintManager.CryptoObject(mac0);
    }

    public static FingerprintManager i(Object object0) {
        return (FingerprintManager)object0;
    }

    public static MediaBrowser.ItemCallback j(Object object0) [...] // Inlined contents

    public static StaticLayout.Builder n(CharSequence charSequence0, int v, TextPaint textPaint0) {
        return StaticLayout.Builder.obtain(charSequence0, 0, v, textPaint0, 0x7FFFFFFF);
    }

    public static Object p(Context context0) {
        return context0.getSystemService(UserManager.class);
    }

    public static void t() {
    }
}


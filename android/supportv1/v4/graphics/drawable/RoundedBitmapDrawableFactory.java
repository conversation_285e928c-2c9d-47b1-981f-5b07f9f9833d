package android.supportv1.v4.graphics.drawable;

import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Rect;
import android.supportv1.v4.graphics.BitmapCompat;
import android.supportv1.v4.view.GravityCompat;
import java.io.InputStream;
import java.util.Objects;

public final class RoundedBitmapDrawableFactory {
    static class DefaultRoundedBitmapDrawable extends RoundedBitmapDrawable {
        public DefaultRoundedBitmapDrawable(Resources resources0, Bitmap bitmap0) {
            super(resources0, bitmap0);
        }

        @Override  // android.supportv1.v4.graphics.drawable.RoundedBitmapDrawable
        public void gravityCompatApply(int v, int v1, int v2, Rect rect0, Rect rect1) {
            GravityCompat.apply(v, v1, v2, rect0, rect1, 0);
        }

        @Override  // android.supportv1.v4.graphics.drawable.RoundedBitmapDrawable
        public boolean hasMipMap() {
            return this.mBitmap != null && BitmapCompat.hasMipMap(this.mBitmap);
        }

        @Override  // android.supportv1.v4.graphics.drawable.RoundedBitmapDrawable
        public void setMipMap(boolean z) {
            Bitmap bitmap0 = this.mBitmap;
            if(bitmap0 != null) {
                BitmapCompat.setHasMipMap(bitmap0, z);
                this.invalidateSelf();
            }
        }
    }

    private static final String TAG = "RoundedBitmapDrawableFa";

    public static RoundedBitmapDrawable create(Resources resources0, Bitmap bitmap0) {
        return new RoundedBitmapDrawable21(resources0, bitmap0);
    }

    public static RoundedBitmapDrawable create(Resources resources0, InputStream inputStream0) {
        RoundedBitmapDrawable roundedBitmapDrawable0 = RoundedBitmapDrawableFactory.create(resources0, BitmapFactory.decodeStream(inputStream0));
        if(roundedBitmapDrawable0.getBitmap() == null) {
            Objects.toString(inputStream0);
        }
        return roundedBitmapDrawable0;
    }

    public static RoundedBitmapDrawable create(Resources resources0, String s) {
        return RoundedBitmapDrawableFactory.create(resources0, BitmapFactory.decodeFile(s));
    }
}


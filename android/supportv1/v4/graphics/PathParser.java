package android.supportv1.v4.graphics;

import a.a;
import android.graphics.Path;
import java.util.ArrayList;

public class PathParser {
    static class ExtractFloatResult {
        int mEndPosition;
        boolean mEndWithNegOrDot;

    }

    public static class PathDataNode {
        public float[] mParams;
        public char mType;

        public PathDataNode(char c, float[] arr_f) {
            this.mType = c;
            this.mParams = arr_f;
        }

        public PathDataNode(PathDataNode pathParser$PathDataNode0) {
            this.mType = pathParser$PathDataNode0.mType;
            this.mParams = PathParser.copyOfRange(pathParser$PathDataNode0.mParams, 0, pathParser$PathDataNode0.mParams.length);
        }

        private static void addCommand(Path path0, float[] arr_f, char c, char c1, float[] arr_f1) {
            float f33;
            float f32;
            float f29;
            float f28;
            float f25;
            float f24;
            float f23;
            float f22;
            Path path3;
            float f17;
            float f16;
            float f15;
            Path path2;
            int v4;
            int v1;
            int v = c1;
            float f = arr_f[0];
            float f1 = arr_f[1];
            float f2 = arr_f[2];
            float f3 = arr_f[3];
            float f4 = arr_f[4];
            float f5 = arr_f[5];
            switch(v) {
                case 65: 
                case 97: {
                    v1 = 7;
                    break;
                }
                case 67: 
                case 99: {
                    v1 = 6;
                    break;
                }
                case 81: 
                case 83: 
                case 0x71: 
                case 0x73: {
                    v1 = 4;
                    break;
                }
                case 72: 
                case 86: 
                case 104: 
                case 0x76: {
                    v1 = 1;
                    break;
                }
                case 90: 
                case 0x7A: {
                    path0.close();
                    path0.moveTo(f4, f5);
                    f = f4;
                    f2 = f;
                    f1 = f5;
                    f3 = f1;
                    v1 = 2;
                    break;
                }
                default: {
                    v1 = 2;
                }
            }
            float f6 = f;
            float f7 = f1;
            float f8 = f4;
            float f9 = f5;
            Path path1 = path0;
            int v2 = 0;
            int v3 = c;
            while(v2 < arr_f1.length) {
                switch(v) {
                    case 65: {
                        v4 = v2;
                        PathDataNode.drawArc(path0, f6, f7, arr_f1[v4 + 5], arr_f1[v4 + 6], arr_f1[v4], arr_f1[v4 + 1], arr_f1[v4 + 2], arr_f1[v4 + 3] != 0.0f, arr_f1[v4 + 4] != 0.0f);
                        f6 = arr_f1[v4 + 5];
                        f7 = arr_f1[v4 + 6];
                        f3 = f7;
                        f2 = f6;
                        break;
                    }
                    case 67: {
                        v4 = v2;
                        path0.cubicTo(arr_f1[v4], arr_f1[v4 + 1], arr_f1[v4 + 2], arr_f1[v4 + 3], arr_f1[v4 + 4], arr_f1[v4 + 5]);
                        float f10 = arr_f1[v4 + 4];
                        float f11 = arr_f1[v4 + 5];
                        f2 = arr_f1[v4 + 2];
                        f3 = arr_f1[v4 + 3];
                        f6 = f10;
                        f7 = f11;
                        break;
                    }
                    case 72: {
                        v4 = v2;
                        path1.lineTo(arr_f1[v4], f7);
                        f6 = arr_f1[v4];
                        break;
                    }
                    case 76: {
                        path1.lineTo(arr_f1[v2], arr_f1[v2 + 1]);
                        float f12 = arr_f1[v2];
                        f7 = arr_f1[v2 + 1];
                        f6 = f12;
                        v4 = v2;
                        break;
                    }
                    case 77: {
                        f6 = arr_f1[v2];
                        f7 = arr_f1[v2 + 1];
                        if(v2 > 0) {
                            path1.lineTo(f6, f7);
                            v4 = v2;
                        }
                        else {
                            path1.moveTo(f6, f7);
                            v4 = v2;
                            f9 = f7;
                            f8 = f6;
                        }
                        break;
                    }
                    case 81: {
                        path2 = path1;
                        v4 = v2;
                        path2.quadTo(arr_f1[v4], arr_f1[v4 + 1], arr_f1[v4 + 2], arr_f1[v4 + 3]);
                        float f13 = arr_f1[v4];
                        float f14 = arr_f1[v4 + 1];
                        f15 = arr_f1[v4 + 2];
                        f7 = f14;
                        f16 = arr_f1[v4 + 3];
                        f17 = f13;
                        f6 = f15;
                        goto label_108;
                    }
                    case 83: {
                        path2 = path1;
                        if(v3 == 99 || v3 == 0x73 || v3 == 67 || v3 == 83) {
                            f6 = f6 * 2.0f - f2;
                            f7 = f7 * 2.0f - f3;
                        }
                        path0.cubicTo(f6, f7, arr_f1[v2], arr_f1[v2 + 1], arr_f1[v2 + 2], arr_f1[v2 + 3]);
                        float f18 = arr_f1[v2];
                        float f19 = arr_f1[v2 + 1];
                        f15 = arr_f1[v2 + 2];
                        f7 = f19;
                        f16 = arr_f1[v2 + 3];
                        v4 = v2;
                        f17 = f18;
                        f6 = f15;
                        goto label_108;
                    }
                    case 84: {
                        path2 = path1;
                        if(v3 == 81 || v3 == 84 || v3 == 0x71 || v3 == 0x74) {
                            f7 = f7 * 2.0f - f3;
                            f6 = f6 * 2.0f - f2;
                        }
                        path2.quadTo(f6, f7, arr_f1[v2], arr_f1[v2 + 1]);
                        float f20 = arr_f1[v2];
                        f16 = arr_f1[v2 + 1];
                        v4 = v2;
                        float f21 = f6;
                        f6 = f20;
                        f17 = f21;
                    label_108:
                        f2 = f17;
                        f3 = f7;
                        path1 = path2;
                        f7 = f16;
                        break;
                    }
                    case 86: {
                        v4 = v2;
                        path0.lineTo(f6, arr_f1[v4]);
                        f7 = arr_f1[v4];
                        path1 = path0;
                        break;
                    }
                    case 97: {
                        v4 = v2;
                        PathDataNode.drawArc(path0, f6, f7, arr_f1[v2 + 5] + f6, arr_f1[v2 + 6] + f7, arr_f1[v2], arr_f1[v2 + 1], arr_f1[v2 + 2], arr_f1[v2 + 3] != 0.0f, arr_f1[v2 + 4] != 0.0f);
                        f6 += arr_f1[v2 + 5];
                        f7 += arr_f1[v2 + 6];
                        f3 = f7;
                        f2 = f6;
                        path1 = path0;
                        break;
                    }
                    case 99: {
                        path3 = path1;
                        path0.rCubicTo(arr_f1[v2], arr_f1[v2 + 1], arr_f1[v2 + 2], arr_f1[v2 + 3], arr_f1[v2 + 4], arr_f1[v2 + 5]);
                        f22 = arr_f1[v2 + 2];
                        f23 = arr_f1[v2 + 3];
                        f24 = arr_f1[v2 + 4] + f6;
                        f25 = arr_f1[v2 + 5];
                        goto label_173;
                    }
                    case 104: {
                        path1.rLineTo(arr_f1[v2], 0.0f);
                        f6 += arr_f1[v2];
                        v4 = v2;
                        break;
                    }
                    case 108: {
                        path1.rLineTo(arr_f1[v2], arr_f1[v2 + 1]);
                        f6 += arr_f1[v2];
                        f7 += arr_f1[v2 + 1];
                        v4 = v2;
                        break;
                    }
                    case 109: {
                        float f26 = arr_f1[v2];
                        f6 += f26;
                        float f27 = arr_f1[v2 + 1];
                        f7 += f27;
                        if(v2 > 0) {
                            path1.rLineTo(f26, f27);
                        }
                        else {
                            path1.rMoveTo(f26, f27);
                            f9 = f7;
                            f8 = f6;
                        }
                        v4 = v2;
                        break;
                    }
                    case 0x71: {
                        path3 = path1;
                        path3.rQuadTo(arr_f1[v2], arr_f1[v2 + 1], arr_f1[v2 + 2], arr_f1[v2 + 3]);
                        f22 = arr_f1[v2];
                        f23 = arr_f1[v2 + 1];
                        f24 = arr_f1[v2 + 2] + f6;
                        f25 = arr_f1[v2 + 3];
                        goto label_173;
                    }
                    case 0x73: {
                        if(v3 == 99 || v3 == 0x73 || v3 == 67 || v3 == 83) {
                            f29 = f7 - f3;
                            f28 = f6 - f2;
                        }
                        else {
                            f28 = 0.0f;
                            f29 = 0.0f;
                        }
                        path3 = path1;
                        path0.rCubicTo(f28, f29, arr_f1[v2], arr_f1[v2 + 1], arr_f1[v2 + 2], arr_f1[v2 + 3]);
                        f22 = arr_f1[v2];
                        f23 = arr_f1[v2 + 1];
                        f24 = arr_f1[v2 + 2] + f6;
                        f25 = arr_f1[v2 + 3];
                    label_173:
                        float f30 = f22 + f6;
                        float f31 = f23 + f7;
                        f6 = f24;
                        f7 = f25 + f7;
                        v4 = v2;
                        path1 = path3;
                        f2 = f30;
                        f3 = f31;
                        break;
                    }
                    case 0x74: {
                        if(v3 == 81 || v3 == 84 || v3 == 0x71 || v3 == 0x74) {
                            f33 = f6 - f2;
                            f32 = f7 - f3;
                        }
                        else {
                            f32 = 0.0f;
                            f33 = 0.0f;
                        }
                        path1.rQuadTo(f33, f32, arr_f1[v2], arr_f1[v2 + 1]);
                        float f34 = f33 + f6;
                        f3 = f32 + f7;
                        f6 = arr_f1[v2] + f6;
                        f7 = arr_f1[v2 + 1] + f7;
                        f2 = f34;
                        v4 = v2;
                        break;
                    }
                    case 0x76: {
                        path1.rLineTo(0.0f, arr_f1[v2]);
                        f7 += arr_f1[v2];
                        v4 = v2;
                        break;
                    }
                    default: {
                        v4 = v2;
                        break;
                    }
                }
                v2 = v4 + v1;
                v3 = c1;
                v = v3;
            }
            arr_f[0] = f6;
            arr_f[1] = f7;
            arr_f[2] = f2;
            arr_f[3] = f3;
            arr_f[4] = f8;
            arr_f[5] = f9;
        }

        private static void arcToBezier(Path path0, double f, double f1, double f2, double f3, double f4, double f5, double f6, double f7, double f8) {
            int v = (int)Math.ceil(Math.abs(f8 * 4.0 / 3.141593));
            double f9 = Math.cos(f6);
            double f10 = Math.sin(f6);
            double f11 = Math.cos(f7);
            double f12 = Math.sin(f7);
            double f13 = -f2 * f9;
            double f14 = f3 * f10;
            double f15 = -f2 * f10;
            double f16 = f3 * f9;
            double f17 = f5;
            double f18 = f7;
            double f19 = f11 * f16 + f12 * f15;
            int v1 = 0;
            double f20 = f12 * f13 - f11 * f14;
            double f21 = f4;
            while(v1 < v) {
                double f22 = f18 + f8 / ((double)v);
                double f23 = Math.sin(f22);
                double f24 = Math.cos(f22);
                double f25 = f2 * f9 * f24 + f - f14 * f23;
                double f26 = f16 * f23 + (f2 * f10 * f24 + f1);
                double f27 = f13 * f23 - f14 * f24;
                double f28 = f24 * f16 + f23 * f15;
                double f29 = f22 - f18;
                double f30 = Math.tan(f29 / 2.0);
                double f31 = (Math.sqrt(f30 * 3.0 * f30 + 4.0) - 1.0) * Math.sin(f29) / 3.0;
                path0.rLineTo(0.0f, 0.0f);
                path0.cubicTo(((float)(f20 * f31 + f21)), ((float)(f19 * f31 + f17)), ((float)(f25 - f31 * f27)), ((float)(f26 - f31 * f28)), ((float)f25), ((float)f26));
                ++v1;
                f17 = f26;
                f21 = f25;
                f18 = f22;
                f19 = f28;
                f20 = f27;
            }
        }

        private static void drawArc(Path path0, float f, float f1, float f2, float f3, float f4, float f5, float f6, boolean z, boolean z1) {
            double f25;
            double f24;
            double f7 = Math.toRadians(f6);
            double f8 = Math.cos(f7);
            double f9 = Math.sin(f7);
            double f10 = (((double)f1) * f9 + ((double)f) * f8) / ((double)f4);
            double f11 = (((double)f1) * f8 + ((double)(-f)) * f9) / ((double)f5);
            double f12 = (((double)f3) * f9 + ((double)f2) * f8) / ((double)f4);
            double f13 = (((double)f3) * f8 + ((double)(-f2)) * f9) / ((double)f5);
            double f14 = f10 - f12;
            double f15 = f11 - f13;
            double f16 = (f10 + f12) / 2.0;
            double f17 = (f11 + f13) / 2.0;
            double f18 = f15 * f15 + f14 * f14;
            if(f18 == 0.0) {
                return;
            }
            double f19 = 1.0 / f18 - 0.25;
            if(f19 < 0.0) {
                double f20 = Math.sqrt(f18);
                PathDataNode.drawArc(path0, f, f1, f2, f3, f4 * ((float)(f20 / 1.99999)), f5 * ((float)(f20 / 1.99999)), f6, z, z1);
                return;
            }
            double f21 = Math.sqrt(f19);
            double f22 = f14 * f21;
            double f23 = f21 * f15;
            if(z == z1) {
                f24 = f16 - f23;
                f25 = f17 + f22;
            }
            else {
                f24 = f16 + f23;
                f25 = f17 - f22;
            }
            double f26 = Math.atan2(f11 - f25, f10 - f24);
            double f27 = Math.atan2(f13 - f25, f12 - f24) - f26;
            int v = Double.compare(f27, 0.0);
            if(z1 != v >= 0) {
                f27 = v <= 0 ? f27 + 6.283185 : f27 - 6.283185;
            }
            double f28 = f24 * ((double)f4);
            double f29 = f25 * ((double)f5);
            PathDataNode.arcToBezier(path0, f28 * f8 - f29 * f9, f28 * f9 + f29 * f8, ((double)f4), ((double)f5), ((double)f), ((double)f1), f7, f26, f27);
        }

        public void interpolatePathDataNode(PathDataNode pathParser$PathDataNode0, PathDataNode pathParser$PathDataNode1, float f) {
            for(int v = 0; true; ++v) {
                float[] arr_f = pathParser$PathDataNode0.mParams;
                if(v >= arr_f.length) {
                    break;
                }
                this.mParams[v] = pathParser$PathDataNode1.mParams[v] * f + (1.0f - f) * arr_f[v];
            }
        }

        public static void nodesToPath(PathDataNode[] arr_pathParser$PathDataNode, Path path0) {
            int v = 109;
            for(int v1 = 0; v1 < arr_pathParser$PathDataNode.length; ++v1) {
                PathDataNode pathParser$PathDataNode0 = arr_pathParser$PathDataNode[v1];
                PathDataNode.addCommand(path0, new float[6], ((char)v), pathParser$PathDataNode0.mType, pathParser$PathDataNode0.mParams);
                v = arr_pathParser$PathDataNode[v1].mType;
            }
        }
    }

    private static final String LOGTAG = "PathParser";

    private static void addNode(ArrayList arrayList0, char c, float[] arr_f) {
        arrayList0.add(new PathDataNode(c, arr_f));
    }

    public static boolean canMorph(PathDataNode[] arr_pathParser$PathDataNode, PathDataNode[] arr_pathParser$PathDataNode1) {
        if(arr_pathParser$PathDataNode == null || arr_pathParser$PathDataNode1 == null || arr_pathParser$PathDataNode.length != arr_pathParser$PathDataNode1.length) {
            return false;
        }
        int v = 0;
        while(v < arr_pathParser$PathDataNode.length) {
            PathDataNode pathParser$PathDataNode0 = arr_pathParser$PathDataNode[v];
            int v1 = pathParser$PathDataNode0.mType;
            PathDataNode pathParser$PathDataNode1 = arr_pathParser$PathDataNode1[v];
            if(v1 == pathParser$PathDataNode1.mType && pathParser$PathDataNode0.mParams.length == pathParser$PathDataNode1.mParams.length) {
                ++v;
                continue;
            }
            return false;
        }
        return true;
    }

    public static float[] copyOfRange(float[] arr_f, int v, int v1) {
        if(v > v1) {
            throw new IllegalArgumentException();
        }
        if(v < 0 || v > arr_f.length) {
            throw new ArrayIndexOutOfBoundsException();
        }
        int v2 = v1 - v;
        float[] arr_f1 = new float[v2];
        System.arraycopy(arr_f, v, arr_f1, 0, Math.min(v2, arr_f.length - v));
        return arr_f1;
    }

    public static PathDataNode[] createNodesFromPathData(String s) {
        if(s == null) {
            return null;
        }
        ArrayList arrayList0 = new ArrayList();
        int v = 1;
        int v1 = 0;
        while(v < s.length()) {
            int v2 = PathParser.nextStart(s, v);
            String s1 = s.substring(v1, v2).trim();
            if(s1.length() > 0) {
                PathParser.addNode(arrayList0, s1.charAt(0), PathParser.getFloats(s1));
            }
            v1 = v2;
            v = v2 + 1;
        }
        if(v - v1 == 1 && v1 < s.length()) {
            PathParser.addNode(arrayList0, s.charAt(v1), new float[0]);
        }
        return (PathDataNode[])arrayList0.toArray(new PathDataNode[arrayList0.size()]);
    }

    public static Path createPathFromPathData(String s) {
        Path path0 = new Path();
        PathDataNode[] arr_pathParser$PathDataNode = PathParser.createNodesFromPathData(s);
        if(arr_pathParser$PathDataNode != null) {
            try {
                PathDataNode.nodesToPath(arr_pathParser$PathDataNode, path0);
                return path0;
            }
            catch(RuntimeException runtimeException0) {
                throw new RuntimeException("Error in parsing " + s, runtimeException0);
            }
        }
        return null;
    }

    public static PathDataNode[] deepCopyNodes(PathDataNode[] arr_pathParser$PathDataNode) {
        if(arr_pathParser$PathDataNode == null) {
            return null;
        }
        PathDataNode[] arr_pathParser$PathDataNode1 = new PathDataNode[arr_pathParser$PathDataNode.length];
        for(int v = 0; v < arr_pathParser$PathDataNode.length; ++v) {
            arr_pathParser$PathDataNode1[v] = new PathDataNode(arr_pathParser$PathDataNode[v]);
        }
        return arr_pathParser$PathDataNode1;
    }

    private static void extract(String s, int v, ExtractFloatResult pathParser$ExtractFloatResult0) {
        pathParser$ExtractFloatResult0.mEndWithNegOrDot = false;
        int v1 = v;
        boolean z = false;
        boolean z1 = false;
        boolean z2 = false;
        while(v1 < s.length()) {
            switch(s.charAt(v1)) {
                case 0x20: 
                case 44: {
                    z = true;
                    z2 = false;
                    break;
                }
                case 45: {
                    if(v1 != v && !z2) {
                        pathParser$ExtractFloatResult0.mEndWithNegOrDot = true;
                        z = true;
                    }
                    z2 = false;
                    break;
                }
                case 46: {
                    if(z1) {
                        pathParser$ExtractFloatResult0.mEndWithNegOrDot = true;
                        z = true;
                    }
                    else {
                        z1 = true;
                    }
                    z2 = false;
                    break;
                }
                case 69: 
                case 101: {
                    z2 = true;
                    break;
                }
                default: {
                    z2 = false;
                    break;
                }
            }
            if(z) {
                break;
            }
            ++v1;
        }
        pathParser$ExtractFloatResult0.mEndPosition = v1;
    }

    private static float[] getFloats(String s) {
        switch(s.charAt(0)) {
            case 90: 
            case 0x7A: {
                return new float[0];
            }
            default: {
                try {
                    float[] arr_f = new float[s.length()];
                    ExtractFloatResult pathParser$ExtractFloatResult0 = new ExtractFloatResult();
                    int v = s.length();
                    int v2 = 0;
                    for(int v1 = 1; v1 < v; v1 = pathParser$ExtractFloatResult0.mEndWithNegOrDot ? v3 : v3 + 1) {
                        PathParser.extract(s, v1, pathParser$ExtractFloatResult0);
                        int v3 = pathParser$ExtractFloatResult0.mEndPosition;
                        if(v1 < v3) {
                            arr_f[v2] = Float.parseFloat(s.substring(v1, v3));
                            ++v2;
                        }
                    }
                    return PathParser.copyOfRange(arr_f, 0, v2);
                }
                catch(NumberFormatException numberFormatException0) {
                }
                throw new RuntimeException(a.y("error in parsing \"", s, "\"").toString(), numberFormatException0);
            }
        }
    }

    private static int nextStart(String s, int v) {
        while(v < s.length()) {
            int v1 = s.charAt(v);
            if(((v1 - 90) * (v1 - 65) <= 0 || (v1 - 0x7A) * (v1 - 97) <= 0) && v1 != 101 && v1 != 69) {
                return v;
            }
            ++v;
        }
        return v;
    }

    public static void updateNodes(PathDataNode[] arr_pathParser$PathDataNode, PathDataNode[] arr_pathParser$PathDataNode1) {
        for(int v = 0; v < arr_pathParser$PathDataNode1.length; ++v) {
            arr_pathParser$PathDataNode[v].mType = arr_pathParser$PathDataNode1[v].mType;
            for(int v1 = 0; true; ++v1) {
                float[] arr_f = arr_pathParser$PathDataNode1[v].mParams;
                if(v1 >= arr_f.length) {
                    break;
                }
                arr_pathParser$PathDataNode[v].mParams[v1] = arr_f[v1];
            }
        }
    }
}


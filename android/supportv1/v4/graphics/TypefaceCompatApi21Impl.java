package android.supportv1.v4.graphics;

import android.content.ContentResolver;
import android.content.Context;
import android.graphics.Typeface;
import android.os.CancellationSignal;
import android.os.ParcelFileDescriptor;
import android.supportv1.v4.provider.FontsContractCompat.FontInfo;
import android.system.ErrnoException;
import android.system.Os;
import android.system.OsConstants;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import jeb.synthetic.TWR;

class TypefaceCompatApi21Impl extends TypefaceCompatBaseImpl {
    private static final String TAG = "TypefaceCompatApi21Impl";

    @Override  // android.supportv1.v4.graphics.TypefaceCompatBaseImpl
    public Typeface createFromFontInfo(Context context0, CancellationSignal cancellationSignal0, FontInfo[] arr_fontsContractCompat$FontInfo, int v) {
        Typeface typeface1;
        Typeface typeface0;
        ParcelFileDescriptor parcelFileDescriptor0;
        if(arr_fontsContractCompat$FontInfo.length < 1) {
            return null;
        }
        FontInfo fontsContractCompat$FontInfo0 = this.findBestInfo(arr_fontsContractCompat$FontInfo, v);
        ContentResolver contentResolver0 = context0.getContentResolver();
        try {
            parcelFileDescriptor0 = contentResolver0.openFileDescriptor(fontsContractCompat$FontInfo0.getUri(), "r", cancellationSignal0);
        }
        catch(IOException unused_ex) {
            return null;
        }
        try {
            File file0 = this.getFile(parcelFileDescriptor0);
            if(file0 != null && file0.canRead()) {
                typeface0 = Typeface.createFromFile(file0);
                if(parcelFileDescriptor0 != null) {
                    goto label_9;
                }
                return typeface0;
            }
            goto label_11;
        }
        catch(Throwable throwable0) {
            goto label_17;
        }
        try {
        label_9:
            parcelFileDescriptor0.close();
            return typeface0;
        }
        catch(IOException unused_ex) {
            return null;
        }
        try {
        label_11:
            try(FileInputStream fileInputStream0 = new FileInputStream(parcelFileDescriptor0.getFileDescriptor())) {
                typeface1 = super.createFromInputStream(context0, fileInputStream0);
            }
            goto label_19;
        }
        catch(Throwable throwable0) {
            try {
            label_17:
                TWR.safeClose$NT(parcelFileDescriptor0, throwable0);
                throw throwable0;
            label_19:
                parcelFileDescriptor0.close();
                return typeface1;
            }
            catch(IOException unused_ex) {
            }
        }
        return null;
    }

    private File getFile(ParcelFileDescriptor parcelFileDescriptor0) {
        try {
            String s = Os.readlink(("/proc/self/fd/" + parcelFileDescriptor0.getFd()));
            return OsConstants.S_ISREG(Os.stat(s).st_mode) ? new File(s) : null;
        }
        catch(ErrnoException unused_ex) {
        }
        return null;
    }
}


package android.supportv1.v4.graphics;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.os.Build.VERSION;
import android.os.CancellationSignal;
import android.os.Handler;
import android.supportv1.v4.content.res.FontResourcesParserCompat.FamilyResourceEntry;
import android.supportv1.v4.content.res.FontResourcesParserCompat.FontFamilyFilesResourceEntry;
import android.supportv1.v4.content.res.FontResourcesParserCompat.ProviderResourceEntry;
import android.supportv1.v4.content.res.ResourcesCompat.FontCallback;
import android.supportv1.v4.provider.FontsContractCompat.FontInfo;
import android.supportv1.v4.provider.FontsContractCompat;
import android.supportv1.v4.util.LruCache;

public class TypefaceCompat {
    private static final String TAG = "TypefaceCompat";
    private static final LruCache sTypefaceCache;
    private static final TypefaceCompatBaseImpl sTypefaceCompatImpl;

    static {
        TypefaceCompatApi21Impl typefaceCompatApi21Impl0;
        int v = Build.VERSION.SDK_INT;
        if(v >= 28) {
            typefaceCompatApi21Impl0 = new TypefaceCompatApi28Impl();
        }
        else if(v >= 26) {
            typefaceCompatApi21Impl0 = new TypefaceCompatApi26Impl();
        }
        else {
            typefaceCompatApi21Impl0 = new TypefaceCompatApi21Impl();
        }
        TypefaceCompat.sTypefaceCompatImpl = typefaceCompatApi21Impl0;
        TypefaceCompat.sTypefaceCache = new LruCache(16);
    }

    public static Typeface createFromFontInfo(Context context0, CancellationSignal cancellationSignal0, FontInfo[] arr_fontsContractCompat$FontInfo, int v) {
        return TypefaceCompat.sTypefaceCompatImpl.createFromFontInfo(context0, cancellationSignal0, arr_fontsContractCompat$FontInfo, v);
    }

    public static Typeface createFromResourcesFamilyXml(Context context0, FamilyResourceEntry fontResourcesParserCompat$FamilyResourceEntry0, Resources resources0, int v, int v1, FontCallback resourcesCompat$FontCallback0, Handler handler0, boolean z) {
        Typeface typeface0;
        boolean z1;
        if(fontResourcesParserCompat$FamilyResourceEntry0 instanceof ProviderResourceEntry) {
            if(!z) {
                z1 = resourcesCompat$FontCallback0 == null;
            }
            else if(((ProviderResourceEntry)fontResourcesParserCompat$FamilyResourceEntry0).getFetchStrategy() == 0) {
                z1 = true;
            }
            else {
                z1 = false;
            }
            typeface0 = FontsContractCompat.getFontSync(context0, ((ProviderResourceEntry)fontResourcesParserCompat$FamilyResourceEntry0).getRequest(), resourcesCompat$FontCallback0, handler0, z1, (z ? ((ProviderResourceEntry)fontResourcesParserCompat$FamilyResourceEntry0).getTimeout() : -1), v1);
        }
        else {
            typeface0 = TypefaceCompat.sTypefaceCompatImpl.createFromFontFamilyFilesResourceEntry(context0, ((FontFamilyFilesResourceEntry)fontResourcesParserCompat$FamilyResourceEntry0), resources0, v1);
            if(resourcesCompat$FontCallback0 != null) {
                if(typeface0 == null) {
                    resourcesCompat$FontCallback0.callbackFailAsync(-3, handler0);
                }
                else {
                    resourcesCompat$FontCallback0.callbackSuccessAsync(typeface0, handler0);
                }
            }
        }
        if(typeface0 != null) {
            String s = TypefaceCompat.createResourceUid(resources0, v, v1);
            TypefaceCompat.sTypefaceCache.put(s, typeface0);
        }
        return typeface0;
    }

    public static Typeface createFromResourcesFontFile(Context context0, Resources resources0, int v, String s, int v1) {
        Typeface typeface0 = TypefaceCompat.sTypefaceCompatImpl.createFromResourcesFontFile(context0, resources0, v, s, v1);
        if(typeface0 != null) {
            String s1 = TypefaceCompat.createResourceUid(resources0, v, v1);
            TypefaceCompat.sTypefaceCache.put(s1, typeface0);
        }
        return typeface0;
    }

    private static String createResourceUid(Resources resources0, int v, int v1) {
        return resources0.getResourcePackageName(v) + "-" + v + "-" + v1;
    }

    public static Typeface findFromCache(Resources resources0, int v, int v1) {
        String s = TypefaceCompat.createResourceUid(resources0, v, v1);
        return (Typeface)TypefaceCompat.sTypefaceCache.get(s);
    }
}


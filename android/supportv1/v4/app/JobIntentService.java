package android.supportv1.v4.app;

import a.a;
import android.app.Service;
import android.app.job.JobInfo.Builder;
import android.app.job.JobInfo;
import android.app.job.JobParameters;
import android.app.job.JobScheduler;
import android.app.job.JobServiceEngine;
import android.app.job.JobWorkItem;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Build.VERSION;
import android.os.IBinder;
import android.os.PowerManager.WakeLock;
import android.os.PowerManager;
import java.util.ArrayList;
import java.util.HashMap;

public abstract class JobIntentService extends Service {
    final class CommandProcessor extends AsyncTask {
        @Override  // android.os.AsyncTask
        public Object doInBackground(Object[] arr_object) {
            return this.doInBackground(((Void[])arr_object));
        }

        public Void doInBackground(Void[] arr_void) {
            GenericWorkItem jobIntentService$GenericWorkItem0;
            while((jobIntentService$GenericWorkItem0 = JobIntentService.this.dequeueWork()) != null) {
                Intent intent0 = jobIntentService$GenericWorkItem0.getIntent();
                JobIntentService.this.onHandleWork(intent0);
                jobIntentService$GenericWorkItem0.complete();
            }
            return null;
        }

        @Override  // android.os.AsyncTask
        public void onCancelled(Object object0) {
            this.onCancelled(((Void)object0));
        }

        public void onCancelled(Void void0) {
            JobIntentService.this.processorFinished();
        }

        @Override  // android.os.AsyncTask
        public void onPostExecute(Object object0) {
            this.onPostExecute(((Void)object0));
        }

        public void onPostExecute(Void void0) {
            JobIntentService.this.processorFinished();
        }
    }

    interface CompatJobEngine {
        IBinder compatGetBinder();

        GenericWorkItem dequeueWork();
    }

    static final class CompatWorkEnqueuer extends WorkEnqueuer {
        private final Context mContext;
        private final PowerManager.WakeLock mLaunchWakeLock;
        boolean mLaunchingService;
        private final PowerManager.WakeLock mRunWakeLock;
        boolean mServiceProcessing;

        public CompatWorkEnqueuer(Context context0, ComponentName componentName0) {
            super(context0, componentName0);
            this.mContext = context0.getApplicationContext();
            PowerManager powerManager0 = (PowerManager)context0.getSystemService("power");
            PowerManager.WakeLock powerManager$WakeLock0 = powerManager0.newWakeLock(1, componentName0.getClassName() + ":launch");
            this.mLaunchWakeLock = powerManager$WakeLock0;
            powerManager$WakeLock0.setReferenceCounted(false);
            PowerManager.WakeLock powerManager$WakeLock1 = powerManager0.newWakeLock(1, componentName0.getClassName() + ":run");
            this.mRunWakeLock = powerManager$WakeLock1;
            powerManager$WakeLock1.setReferenceCounted(false);
        }

        @Override  // android.supportv1.v4.app.JobIntentService$WorkEnqueuer
        public void enqueueWork(Intent intent0) {
            Intent intent1 = new Intent(intent0);
            intent1.setComponent(this.mComponentName);
            if(this.mContext.startService(intent1) != null) {
                synchronized(this) {
                    if(!this.mLaunchingService) {
                        this.mLaunchingService = true;
                        if(!this.mServiceProcessing) {
                            this.mLaunchWakeLock.acquire(60000L);
                        }
                    }
                }
            }
        }

        @Override  // android.supportv1.v4.app.JobIntentService$WorkEnqueuer
        public void serviceProcessingFinished() {
            synchronized(this) {
                if(this.mServiceProcessing) {
                    if(this.mLaunchingService) {
                        this.mLaunchWakeLock.acquire(60000L);
                    }
                    this.mServiceProcessing = false;
                    this.mRunWakeLock.release();
                }
            }
        }

        @Override  // android.supportv1.v4.app.JobIntentService$WorkEnqueuer
        public void serviceProcessingStarted() {
            synchronized(this) {
                if(!this.mServiceProcessing) {
                    this.mServiceProcessing = true;
                    this.mRunWakeLock.acquire(600000L);
                    this.mLaunchWakeLock.release();
                }
            }
        }

        @Override  // android.supportv1.v4.app.JobIntentService$WorkEnqueuer
        public void serviceStartReceived() {
            synchronized(this) {
                this.mLaunchingService = false;
            }
        }
    }

    final class CompatWorkItem implements GenericWorkItem {
        final Intent mIntent;
        final int mStartId;

        public CompatWorkItem(Intent intent0, int v) {
            this.mIntent = intent0;
            this.mStartId = v;
        }

        @Override  // android.supportv1.v4.app.JobIntentService$GenericWorkItem
        public void complete() {
            JobIntentService.this.stopSelf(this.mStartId);
        }

        @Override  // android.supportv1.v4.app.JobIntentService$GenericWorkItem
        public Intent getIntent() {
            return this.mIntent;
        }
    }

    interface GenericWorkItem {
        void complete();

        Intent getIntent();
    }

    static final class JobServiceEngineImpl extends JobServiceEngine implements CompatJobEngine {
        static final boolean DEBUG = false;
        static final String TAG = "JobServiceEngineImpl";
        final Object mLock;
        JobParameters mParams;
        final JobIntentService mService;

        public JobServiceEngineImpl(JobIntentService jobIntentService0) {
            super(jobIntentService0);
            this.mLock = new Object();
            this.mService = jobIntentService0;
        }

        @Override  // android.supportv1.v4.app.JobIntentService$CompatJobEngine
        public IBinder compatGetBinder() {
            return this.getBinder();
        }

        @Override  // android.supportv1.v4.app.JobIntentService$CompatJobEngine
        public GenericWorkItem dequeueWork() {
            JobWorkItem jobWorkItem0;
            synchronized(this.mLock) {
                JobParameters jobParameters0 = this.mParams;
                if(jobParameters0 == null) {
                    return null;
                }
                jobWorkItem0 = jobParameters0.dequeueWork();
            }
            if(jobWorkItem0 != null) {
                jobWorkItem0.getIntent().setExtrasClassLoader(this.mService.getClassLoader());
                return new JobIntentService.JobServiceEngineImpl.WrapperWorkItem(this, jobWorkItem0);
            }
            return null;
        }

        @Override  // android.app.job.JobServiceEngine
        public boolean onStartJob(JobParameters jobParameters0) {
            this.mParams = jobParameters0;
            this.mService.ensureProcessorRunningLocked(false);
            return true;
        }

        @Override  // android.app.job.JobServiceEngine
        public boolean onStopJob(JobParameters jobParameters0) {
            boolean z = this.mService.doStopCurrentWork();
            synchronized(this.mLock) {
                this.mParams = null;
            }
            return z;
        }
    }

    static final class JobWorkEnqueuer extends WorkEnqueuer {
        private final JobInfo mJobInfo;
        private final JobScheduler mJobScheduler;

        public JobWorkEnqueuer(Context context0, ComponentName componentName0, int v) {
            super(context0, componentName0);
            this.ensureJobId(v);
            this.mJobInfo = new JobInfo.Builder(v, this.mComponentName).setOverrideDeadline(0L).build();
            this.mJobScheduler = (JobScheduler)context0.getApplicationContext().getSystemService("jobscheduler");
        }

        @Override  // android.supportv1.v4.app.JobIntentService$WorkEnqueuer
        public void enqueueWork(Intent intent0) {
            JobWorkItem jobWorkItem0 = c.i(intent0);
            this.mJobScheduler.enqueue(this.mJobInfo, jobWorkItem0);
        }
    }

    static abstract class WorkEnqueuer {
        final ComponentName mComponentName;
        boolean mHasJobId;
        int mJobId;

        public WorkEnqueuer(Context context0, ComponentName componentName0) {
            this.mComponentName = componentName0;
        }

        public abstract void enqueueWork(Intent arg1);

        public void ensureJobId(int v) {
            if(!this.mHasJobId) {
                this.mHasJobId = true;
                this.mJobId = v;
                return;
            }
            if(this.mJobId == v) {
                return;
            }
            StringBuilder stringBuilder0 = a.u("Given job ID ", v, " is different than previous ");
            stringBuilder0.append(this.mJobId);
            throw new IllegalArgumentException(stringBuilder0.toString());
        }

        public void serviceProcessingFinished() {
        }

        public void serviceProcessingStarted() {
        }

        public void serviceStartReceived() {
        }
    }

    static final boolean DEBUG = false;
    static final String TAG = "JobIntentService";
    final ArrayList mCompatQueue;
    WorkEnqueuer mCompatWorkEnqueuer;
    CommandProcessor mCurProcessor;
    boolean mDestroyed;
    boolean mInterruptIfStopped;
    CompatJobEngine mJobImpl;
    boolean mStopped;
    static final HashMap sClassWorkEnqueuer;
    static final Object sLock;

    static {
        JobIntentService.sLock = new Object();
        JobIntentService.sClassWorkEnqueuer = new HashMap();
    }

    public JobIntentService() {
        this.mInterruptIfStopped = false;
        this.mStopped = false;
        this.mDestroyed = false;
        this.mCompatQueue = Build.VERSION.SDK_INT < 26 ? new ArrayList() : null;
    }

    public GenericWorkItem dequeueWork() {
        CompatJobEngine jobIntentService$CompatJobEngine0 = this.mJobImpl;
        if(jobIntentService$CompatJobEngine0 != null) {
            return jobIntentService$CompatJobEngine0.dequeueWork();
        }
        synchronized(this.mCompatQueue) {
            return this.mCompatQueue.size() > 0 ? ((GenericWorkItem)this.mCompatQueue.remove(0)) : null;
        }
    }

    public boolean doStopCurrentWork() {
        CommandProcessor jobIntentService$CommandProcessor0 = this.mCurProcessor;
        if(jobIntentService$CommandProcessor0 != null) {
            jobIntentService$CommandProcessor0.cancel(this.mInterruptIfStopped);
        }
        this.mStopped = true;
        return true;
    }

    public static void enqueueWork(Context context0, ComponentName componentName0, int v, Intent intent0) {
        if(intent0 == null) {
            throw new IllegalArgumentException("work must not be null");
        }
        synchronized(JobIntentService.sLock) {
            WorkEnqueuer jobIntentService$WorkEnqueuer0 = JobIntentService.getWorkEnqueuer(context0, componentName0, true, v);
            jobIntentService$WorkEnqueuer0.ensureJobId(v);
            jobIntentService$WorkEnqueuer0.enqueueWork(intent0);
        }
    }

    public static void enqueueWork(Context context0, Class class0, int v, Intent intent0) {
        JobIntentService.enqueueWork(context0, new ComponentName(context0, class0), v, intent0);
    }

    public void ensureProcessorRunningLocked(boolean z) {
        if(this.mCurProcessor == null) {
            this.mCurProcessor = new CommandProcessor(this);
            WorkEnqueuer jobIntentService$WorkEnqueuer0 = this.mCompatWorkEnqueuer;
            if(jobIntentService$WorkEnqueuer0 != null && z) {
                jobIntentService$WorkEnqueuer0.serviceProcessingStarted();
            }
            this.mCurProcessor.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Void[0]);
        }
    }

    public static WorkEnqueuer getWorkEnqueuer(Context context0, ComponentName componentName0, boolean z, int v) {
        CompatWorkEnqueuer jobIntentService$CompatWorkEnqueuer0;
        HashMap hashMap0 = JobIntentService.sClassWorkEnqueuer;
        WorkEnqueuer jobIntentService$WorkEnqueuer0 = (WorkEnqueuer)hashMap0.get(componentName0);
        if(jobIntentService$WorkEnqueuer0 == null) {
            if(Build.VERSION.SDK_INT >= 26) {
                if(!z) {
                    throw new IllegalArgumentException("Can\'t be here without a job id");
                }
                jobIntentService$CompatWorkEnqueuer0 = new JobWorkEnqueuer(context0, componentName0, v);
            }
            else {
                jobIntentService$CompatWorkEnqueuer0 = new CompatWorkEnqueuer(context0, componentName0);
            }
            jobIntentService$WorkEnqueuer0 = jobIntentService$CompatWorkEnqueuer0;
            hashMap0.put(componentName0, jobIntentService$WorkEnqueuer0);
        }
        return jobIntentService$WorkEnqueuer0;
    }

    public boolean isStopped() {
        return this.mStopped;
    }

    @Override  // android.app.Service
    public IBinder onBind(Intent intent0) {
        return this.mJobImpl == null ? null : this.mJobImpl.compatGetBinder();
    }

    @Override  // android.app.Service
    public void onCreate() {
        super.onCreate();
        if(Build.VERSION.SDK_INT >= 26) {
            this.mJobImpl = new JobServiceEngineImpl(this);
            this.mCompatWorkEnqueuer = null;
            return;
        }
        this.mJobImpl = null;
        this.mCompatWorkEnqueuer = JobIntentService.getWorkEnqueuer(this, new ComponentName(this, this.getClass()), false, 0);
    }

    @Override  // android.app.Service
    public void onDestroy() {
        super.onDestroy();
        ArrayList arrayList0 = this.mCompatQueue;
        if(arrayList0 != null) {
            synchronized(arrayList0) {
                this.mDestroyed = true;
                this.mCompatWorkEnqueuer.serviceProcessingFinished();
            }
        }
    }

    public abstract void onHandleWork(Intent arg1);

    @Override  // android.app.Service
    public int onStartCommand(Intent intent0, int v, int v1) {
        if(this.mCompatQueue != null) {
            this.mCompatWorkEnqueuer.serviceStartReceived();
            ArrayList arrayList0 = this.mCompatQueue;
            synchronized(arrayList0) {
                ArrayList arrayList1 = this.mCompatQueue;
                if(intent0 == null) {
                    intent0 = new Intent();
                }
                arrayList1.add(new CompatWorkItem(this, intent0, v1));
                this.ensureProcessorRunningLocked(true);
                return 3;
            }
        }
        return 2;
    }

    public boolean onStopCurrentWork() [...] // Inlined contents

    public void processorFinished() {
        ArrayList arrayList0 = this.mCompatQueue;
        if(arrayList0 != null) {
            synchronized(arrayList0) {
                this.mCurProcessor = null;
                if(this.mCompatQueue != null && this.mCompatQueue.size() > 0) {
                    this.ensureProcessorRunningLocked(false);
                }
                else if(!this.mDestroyed) {
                    this.mCompatWorkEnqueuer.serviceProcessingFinished();
                }
            }
        }
    }

    public void setInterruptIfStopped(boolean z) {
        this.mInterruptIfStopped = z;
    }
}


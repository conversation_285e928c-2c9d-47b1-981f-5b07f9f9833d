package android.supportv1.v4.app;

import android.app.Notification.MessagingStyle.Message;
import android.app.Notification.MessagingStyle;
import android.app.Person.Builder;
import android.app.Person;
import android.os.Parcelable;

public abstract class a {
    public static Notification.MessagingStyle.Message e(CharSequence charSequence0, long v, Person person0) {
        return new Notification.MessagingStyle.Message(charSequence0, v, person0);
    }

    public static Notification.MessagingStyle f(Person person0) {
        return new Notification.MessagingStyle(person0);
    }

    public static Person.Builder g() {
        return new Person.Builder();
    }

    public static Person m(Parcelable parcelable0) {
        return (Person)parcelable0;
    }
}


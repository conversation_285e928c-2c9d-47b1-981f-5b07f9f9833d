package android.supportv1.v4.app;

import android.content.ComponentName;
import java.util.ArrayDeque;

class NotificationManagerCompat.SideChannelManager.ListenerRecord {
    boolean bound;
    final ComponentName componentName;
    int retryCount;
    INotificationSideChannel service;
    ArrayDeque taskQueue;

    public NotificationManagerCompat.SideChannelManager.ListenerRecord(ComponentName componentName0) {
        this.bound = false;
        this.taskQueue = new ArrayDeque();
        this.retryCount = 0;
        this.componentName = componentName0;
    }
}


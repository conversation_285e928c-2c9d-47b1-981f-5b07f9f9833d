package android.supportv1.v4.app;

import android.os.Bundle;

public final class NotificationCompat.Action.WearableExtender implements NotificationCompat.Action.Extender {
    private static final int DEFAULT_FLAGS = 1;
    private static final String EXTRA_WEARABLE_EXTENSIONS = "android.wearable.EXTENSIONS";
    private static final int FLAG_AVAILABLE_OFFLINE = 1;
    private static final int FLAG_HINT_DISPLAY_INLINE = 4;
    private static final int FLAG_HINT_LAUNCHES_ACTIVITY = 2;
    private static final String KEY_CANCEL_LABEL = "cancelLabel";
    private static final String KEY_CONFIRM_LABEL = "confirmLabel";
    private static final String KEY_FLAGS = "flags";
    private static final String KEY_IN_PROGRESS_LABEL = "inProgressLabel";
    private CharSequence mCancelLabel;
    private CharSequence mConfirmLabel;
    private int mFlags;
    private CharSequence mInProgressLabel;

    public NotificationCompat.Action.WearableExtender() {
        this.mFlags = 1;
    }

    public NotificationCompat.Action.WearableExtender(Action notificationCompat$Action0) {
        this.mFlags = 1;
        Bundle bundle0 = notificationCompat$Action0.getExtras().getBundle("android.wearable.EXTENSIONS");
        if(bundle0 != null) {
            this.mFlags = bundle0.getInt("flags", 1);
            this.mInProgressLabel = bundle0.getCharSequence("inProgressLabel");
            this.mConfirmLabel = bundle0.getCharSequence("confirmLabel");
            this.mCancelLabel = bundle0.getCharSequence("cancelLabel");
        }
    }

    public NotificationCompat.Action.WearableExtender clone() {
        NotificationCompat.Action.WearableExtender notificationCompat$Action$WearableExtender0 = new NotificationCompat.Action.WearableExtender();
        notificationCompat$Action$WearableExtender0.mFlags = this.mFlags;
        notificationCompat$Action$WearableExtender0.mInProgressLabel = this.mInProgressLabel;
        notificationCompat$Action$WearableExtender0.mConfirmLabel = this.mConfirmLabel;
        notificationCompat$Action$WearableExtender0.mCancelLabel = this.mCancelLabel;
        return notificationCompat$Action$WearableExtender0;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return this.clone();
    }

    @Override  // android.supportv1.v4.app.NotificationCompat$Action$Extender
    public NotificationCompat.Action.Builder extend(NotificationCompat.Action.Builder notificationCompat$Action$Builder0) {
        Bundle bundle0 = new Bundle();
        int v = this.mFlags;
        if(v != 1) {
            bundle0.putInt("flags", v);
        }
        CharSequence charSequence0 = this.mInProgressLabel;
        if(charSequence0 != null) {
            bundle0.putCharSequence("inProgressLabel", charSequence0);
        }
        CharSequence charSequence1 = this.mConfirmLabel;
        if(charSequence1 != null) {
            bundle0.putCharSequence("confirmLabel", charSequence1);
        }
        CharSequence charSequence2 = this.mCancelLabel;
        if(charSequence2 != null) {
            bundle0.putCharSequence("cancelLabel", charSequence2);
        }
        notificationCompat$Action$Builder0.getExtras().putBundle("android.wearable.EXTENSIONS", bundle0);
        return notificationCompat$Action$Builder0;
    }

    @Deprecated
    public CharSequence getCancelLabel() {
        return this.mCancelLabel;
    }

    @Deprecated
    public CharSequence getConfirmLabel() {
        return this.mConfirmLabel;
    }

    public boolean getHintDisplayActionInline() {
        return (this.mFlags & 4) != 0;
    }

    public boolean getHintLaunchesActivity() {
        return (this.mFlags & 2) != 0;
    }

    @Deprecated
    public CharSequence getInProgressLabel() {
        return this.mInProgressLabel;
    }

    public boolean isAvailableOffline() {
        return (this.mFlags & 1) != 0;
    }

    public NotificationCompat.Action.WearableExtender setAvailableOffline(boolean z) {
        this.setFlag(1, z);
        return this;
    }

    @Deprecated
    public NotificationCompat.Action.WearableExtender setCancelLabel(CharSequence charSequence0) {
        this.mCancelLabel = charSequence0;
        return this;
    }

    @Deprecated
    public NotificationCompat.Action.WearableExtender setConfirmLabel(CharSequence charSequence0) {
        this.mConfirmLabel = charSequence0;
        return this;
    }

    private void setFlag(int v, boolean z) {
        this.mFlags = z ? v | this.mFlags : v & this.mFlags;
    }

    public NotificationCompat.Action.WearableExtender setHintDisplayActionInline(boolean z) {
        this.setFlag(4, z);
        return this;
    }

    public NotificationCompat.Action.WearableExtender setHintLaunchesActivity(boolean z) {
        this.setFlag(2, z);
        return this;
    }

    @Deprecated
    public NotificationCompat.Action.WearableExtender setInProgressLabel(CharSequence charSequence0) {
        this.mInProgressLabel = charSequence0;
        return this;
    }
}


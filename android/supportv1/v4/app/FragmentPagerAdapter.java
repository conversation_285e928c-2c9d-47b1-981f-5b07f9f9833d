package android.supportv1.v4.app;

import android.os.Parcelable;
import android.supportv1.v4.view.PagerAdapter;
import android.view.View;
import android.view.ViewGroup;

public abstract class FragmentPagerAdapter extends PagerAdapter {
    private static final boolean DEBUG = false;
    private static final String TAG = "FragmentPagerAdapter";
    private FragmentTransaction mCurTransaction;
    private Fragment mCurrentPrimaryItem;
    private final FragmentManager mFragmentManager;

    public FragmentPagerAdapter(FragmentManager fragmentManager0) {
        this.mCurTransaction = null;
        this.mCurrentPrimaryItem = null;
        this.mFragmentManager = fragmentManager0;
    }

    @Override  // android.supportv1.v4.view.PagerAdapter
    public void destroyItem(ViewGroup viewGroup0, int v, Object object0) {
        if(this.mCurTransaction == null) {
            this.mCurTransaction = this.mFragmentManager.beginTransaction();
        }
        this.mCurTransaction.detach(((Fragment)object0));
    }

    @Override  // android.supportv1.v4.view.PagerAdapter
    public void finishUpdate(ViewGroup viewGroup0) {
        FragmentTransaction fragmentTransaction0 = this.mCurTransaction;
        if(fragmentTransaction0 != null) {
            fragmentTransaction0.commitNowAllowingStateLoss();
            this.mCurTransaction = null;
        }
    }

    public abstract Fragment getItem(int arg1);

    public long getItemId(int v) [...] // Inlined contents

    @Override  // android.supportv1.v4.view.PagerAdapter
    public Object instantiateItem(ViewGroup viewGroup0, int v) {
        if(this.mCurTransaction == null) {
            this.mCurTransaction = this.mFragmentManager.beginTransaction();
        }
        String s = FragmentPagerAdapter.makeFragmentName(viewGroup0.getId(), ((long)v));
        Fragment fragment0 = this.mFragmentManager.findFragmentByTag(s);
        if(fragment0 == null) {
            fragment0 = this.getItem(v);
            this.mCurTransaction.add(viewGroup0.getId(), fragment0, FragmentPagerAdapter.makeFragmentName(viewGroup0.getId(), ((long)v)));
        }
        else {
            this.mCurTransaction.attach(fragment0);
        }
        if(fragment0 != this.mCurrentPrimaryItem) {
            fragment0.setMenuVisibility(false);
            fragment0.setUserVisibleHint(false);
        }
        return fragment0;
    }

    @Override  // android.supportv1.v4.view.PagerAdapter
    public boolean isViewFromObject(View view0, Object object0) {
        return ((Fragment)object0).getView() == view0;
    }

    private static String makeFragmentName(int v, long v1) {
        return "android:switcher:" + v + ":" + v1;
    }

    @Override  // android.supportv1.v4.view.PagerAdapter
    public void restoreState(Parcelable parcelable0, ClassLoader classLoader0) {
    }

    @Override  // android.supportv1.v4.view.PagerAdapter
    public Parcelable saveState() {
        return null;
    }

    @Override  // android.supportv1.v4.view.PagerAdapter
    public void setPrimaryItem(ViewGroup viewGroup0, int v, Object object0) {
        Fragment fragment0 = this.mCurrentPrimaryItem;
        if(((Fragment)object0) != fragment0) {
            if(fragment0 != null) {
                fragment0.setMenuVisibility(false);
                this.mCurrentPrimaryItem.setUserVisibleHint(false);
            }
            ((Fragment)object0).setMenuVisibility(true);
            ((Fragment)object0).setUserVisibleHint(true);
            this.mCurrentPrimaryItem = (Fragment)object0;
        }
    }

    @Override  // android.supportv1.v4.view.PagerAdapter
    public void startUpdate(ViewGroup viewGroup0) {
        if(viewGroup0.getId() == -1) {
            throw new IllegalStateException("ViewPager with adapter " + this + " requires a view id");
        }
    }
}


package android.supportv1.v4.app;

import android.app.Activity;
import android.app.FragmentManager;
import android.archv1.lifecycle.c.b;
import android.archv1.lifecycle.c;
import android.archv1.lifecycle.e;
import android.archv1.lifecycle.f;
import android.archv1.lifecycle.m;
import android.os.Bundle;
import android.supportv1.v4.util.SimpleArrayMap;
import android.supportv1.v4.view.KeyEventDispatcher.Component;
import android.supportv1.v4.view.KeyEventDispatcher;
import android.view.KeyEvent;
import android.view.View;

public class SupportActivity extends Activity implements e, Component {
    public static class ExtraData {
    }

    private SimpleArrayMap mExtraDataMap;
    private f mLifecycleRegistry;

    public SupportActivity() {
        this.mExtraDataMap = new SimpleArrayMap();
        this.mLifecycleRegistry = new f(this);
    }

    @Override  // android.app.Activity
    public boolean dispatchKeyEvent(KeyEvent keyEvent0) {
        View view0 = this.getWindow().getDecorView();
        return view0 == null || !KeyEventDispatcher.dispatchBeforeHierarchy(view0, keyEvent0) ? KeyEventDispatcher.dispatchKeyEvent(this, view0, this, keyEvent0) : true;
    }

    @Override  // android.app.Activity
    public boolean dispatchKeyShortcutEvent(KeyEvent keyEvent0) {
        View view0 = this.getWindow().getDecorView();
        return view0 == null || !KeyEventDispatcher.dispatchBeforeHierarchy(view0, keyEvent0) ? super.dispatchKeyShortcutEvent(keyEvent0) : true;
    }

    public ExtraData getExtraData(Class class0) {
        return (ExtraData)this.mExtraDataMap.get(class0);
    }

    @Override  // android.archv1.lifecycle.e
    public c getLifecycle() {
        return this.mLifecycleRegistry;
    }

    @Override  // android.app.Activity
    public void onCreate(Bundle bundle0) {
        super.onCreate(bundle0);
        FragmentManager fragmentManager0 = this.getFragmentManager();
        if(fragmentManager0.findFragmentByTag("android.archv1.lifecycle.LifecycleDispatcher.report_fragment_tag") == null) {
            fragmentManager0.beginTransaction().add(new m(), "android.archv1.lifecycle.LifecycleDispatcher.report_fragment_tag").commit();  // 初始化器: Landroid/app/Fragment;-><init>()V
            fragmentManager0.executePendingTransactions();
        }
    }

    @Override  // android.app.Activity
    public void onSaveInstanceState(Bundle bundle0) {
        this.mLifecycleRegistry.c(b.c);
        super.onSaveInstanceState(bundle0);
    }

    public void putExtraData(ExtraData supportActivity$ExtraData0) {
        this.mExtraDataMap.put(supportActivity$ExtraData0.getClass(), supportActivity$ExtraData0);
    }

    @Override  // android.supportv1.v4.view.KeyEventDispatcher$Component
    public boolean superDispatchKeyEvent(KeyEvent keyEvent0) {
        return super.dispatchKeyEvent(keyEvent0);
    }
}


package android.supportv1.v4.app;

import android.app.RemoteInput.Builder;
import android.content.ClipData;
import android.content.ClipDescription;
import android.content.Intent;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map.Entry;
import java.util.Map;
import java.util.Set;

public final class RemoteInput {
    public static final class Builder {
        private boolean mAllowFreeFormTextInput;
        private final Set mAllowedDataTypes;
        private CharSequence[] mChoices;
        private final Bundle mExtras;
        private CharSequence mLabel;
        private final String mResultKey;

        public Builder(String s) {
            this.mAllowedDataTypes = new HashSet();
            this.mExtras = new Bundle();
            this.mAllowFreeFormTextInput = true;
            if(s == null) {
                throw new IllegalArgumentException("Result key can\'t be null");
            }
            this.mResultKey = s;
        }

        public Builder addExtras(Bundle bundle0) {
            if(bundle0 != null) {
                this.mExtras.putAll(bundle0);
            }
            return this;
        }

        public RemoteInput build() {
            return new RemoteInput(this.mResultKey, this.mLabel, this.mChoices, this.mAllowFreeFormTextInput, this.mExtras, this.mAllowedDataTypes);
        }

        public Bundle getExtras() {
            return this.mExtras;
        }

        public Builder setAllowDataType(String s, boolean z) {
            if(z) {
                this.mAllowedDataTypes.add(s);
                return this;
            }
            this.mAllowedDataTypes.remove(s);
            return this;
        }

        public Builder setAllowFreeFormInput(boolean z) {
            this.mAllowFreeFormTextInput = z;
            return this;
        }

        public Builder setChoices(CharSequence[] arr_charSequence) {
            this.mChoices = arr_charSequence;
            return this;
        }

        public Builder setLabel(CharSequence charSequence0) {
            this.mLabel = charSequence0;
            return this;
        }
    }

    private static final String EXTRA_DATA_TYPE_RESULTS_DATA = "android.remoteinput.dataTypeResultsData";
    public static final String EXTRA_RESULTS_DATA = "android.remoteinput.resultsData";
    public static final String RESULTS_CLIP_LABEL = "android.remoteinput.results";
    private static final String TAG = "RemoteInput";
    private final boolean mAllowFreeFormTextInput;
    private final Set mAllowedDataTypes;
    private final CharSequence[] mChoices;
    private final Bundle mExtras;
    private final CharSequence mLabel;
    private final String mResultKey;

    public RemoteInput(String s, CharSequence charSequence0, CharSequence[] arr_charSequence, boolean z, Bundle bundle0, Set set0) {
        this.mResultKey = s;
        this.mLabel = charSequence0;
        this.mChoices = arr_charSequence;
        this.mAllowFreeFormTextInput = z;
        this.mExtras = bundle0;
        this.mAllowedDataTypes = set0;
    }

    public static void addDataResultToIntent(RemoteInput remoteInput0, Intent intent0, Map map0) {
        if(Build.VERSION.SDK_INT >= 26) {
            android.app.RemoteInput.addDataResultToIntent(RemoteInput.fromCompat(remoteInput0), intent0, map0);
            return;
        }
        Intent intent1 = RemoteInput.getClipDataIntentFromIntent(intent0);
        if(intent1 == null) {
            intent1 = new Intent();
        }
        for(Object object0: map0.entrySet()) {
            String s = (String)((Map.Entry)object0).getKey();
            Uri uri0 = (Uri)((Map.Entry)object0).getValue();
            if(s != null) {
                Bundle bundle0 = intent1.getBundleExtra(RemoteInput.getExtraResultsKeyForData(s));
                if(bundle0 == null) {
                    bundle0 = new Bundle();
                }
                bundle0.putString(remoteInput0.getResultKey(), uri0.toString());
                intent1.putExtra(RemoteInput.getExtraResultsKeyForData(s), bundle0);
            }
        }
        intent0.setClipData(ClipData.newIntent("android.remoteinput.results", intent1));
    }

    public static void addResultsToIntent(RemoteInput[] arr_remoteInput, Intent intent0, Bundle bundle0) {
        if(Build.VERSION.SDK_INT >= 26) {
            android.app.RemoteInput.addResultsToIntent(RemoteInput.fromCompat(arr_remoteInput), intent0, bundle0);
            return;
        }
        Bundle bundle1 = RemoteInput.getResultsFromIntent(intent0);
        if(bundle1 != null) {
            bundle1.putAll(bundle0);
            bundle0 = bundle1;
        }
        for(int v = 0; v < arr_remoteInput.length; ++v) {
            RemoteInput remoteInput0 = arr_remoteInput[v];
            Map map0 = RemoteInput.getDataResultsFromIntent(intent0, remoteInput0.getResultKey());
            android.app.RemoteInput.addResultsToIntent(RemoteInput.fromCompat(new RemoteInput[]{remoteInput0}), intent0, bundle0);
            if(map0 != null) {
                RemoteInput.addDataResultToIntent(remoteInput0, intent0, map0);
            }
        }
    }

    public static android.app.RemoteInput fromCompat(RemoteInput remoteInput0) {
        return new RemoteInput.Builder(remoteInput0.getResultKey()).setLabel(remoteInput0.getLabel()).setChoices(remoteInput0.getChoices()).setAllowFreeFormInput(remoteInput0.getAllowFreeFormInput()).addExtras(remoteInput0.getExtras()).build();
    }

    public static android.app.RemoteInput[] fromCompat(RemoteInput[] arr_remoteInput) {
        if(arr_remoteInput == null) {
            return null;
        }
        android.app.RemoteInput[] arr_remoteInput1 = new android.app.RemoteInput[arr_remoteInput.length];
        for(int v = 0; v < arr_remoteInput.length; ++v) {
            arr_remoteInput1[v] = RemoteInput.fromCompat(arr_remoteInput[v]);
        }
        return arr_remoteInput1;
    }

    public boolean getAllowFreeFormInput() {
        return this.mAllowFreeFormTextInput;
    }

    public Set getAllowedDataTypes() {
        return this.mAllowedDataTypes;
    }

    public CharSequence[] getChoices() {
        return this.mChoices;
    }

    private static Intent getClipDataIntentFromIntent(Intent intent0) {
        ClipData clipData0 = intent0.getClipData();
        if(clipData0 == null) {
            return null;
        }
        ClipDescription clipDescription0 = clipData0.getDescription();
        if(!clipDescription0.hasMimeType("text/vnd.android.intent")) {
            return null;
        }
        return clipDescription0.getLabel().equals("android.remoteinput.results") ? clipData0.getItemAt(0).getIntent() : null;
    }

    public static Map getDataResultsFromIntent(Intent intent0, String s) {
        if(Build.VERSION.SDK_INT >= 26) {
            return android.app.RemoteInput.getDataResultsFromIntent(intent0, s);
        }
        Intent intent1 = RemoteInput.getClipDataIntentFromIntent(intent0);
        if(intent1 == null) {
            return null;
        }
        HashMap hashMap0 = new HashMap();
        for(Object object0: intent1.getExtras().keySet()) {
            String s1 = (String)object0;
            if(s1.startsWith("android.remoteinput.dataTypeResultsData")) {
                String s2 = s1.substring(39);
                if(!s2.isEmpty()) {
                    String s3 = intent1.getBundleExtra(s1).getString(s);
                    if(s3 != null && !s3.isEmpty()) {
                        hashMap0.put(s2, Uri.parse(s3));
                    }
                }
            }
        }
        return !hashMap0.isEmpty() ? hashMap0 : null;
    }

    private static String getExtraResultsKeyForData(String s) {
        return "android.remoteinput.dataTypeResultsData" + s;
    }

    public Bundle getExtras() {
        return this.mExtras;
    }

    public CharSequence getLabel() {
        return this.mLabel;
    }

    public String getResultKey() {
        return this.mResultKey;
    }

    public static Bundle getResultsFromIntent(Intent intent0) {
        return android.app.RemoteInput.getResultsFromIntent(intent0);
    }

    // 去混淆评级： 低(30)
    public boolean isDataOnly() {
        return !this.getAllowFreeFormInput() && (this.getChoices() == null || this.getChoices().length == 0) && this.getAllowedDataTypes() != null && !this.getAllowedDataTypes().isEmpty();
    }
}


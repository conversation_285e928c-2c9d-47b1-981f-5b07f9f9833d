package android.supportv1.v4.app;

import android.app.Notification.Builder;
import android.app.job.JobWorkItem;
import android.content.Context;
import android.content.Intent;

public abstract class c {
    public static Notification.Builder g(Context context0, String s) {
        return new Notification.Builder(context0, s);
    }

    public static JobWorkItem i(Intent intent0) {
        return new JobWorkItem(intent0);
    }
}


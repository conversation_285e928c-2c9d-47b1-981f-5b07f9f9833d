package android.supportv1.v4.app;

import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Parcelable;
import java.util.ArrayList;
import java.util.List;

public final class NotificationCompat.MessagingStyle.Message {
    static final String KEY_DATA_MIME_TYPE = "type";
    static final String KEY_DATA_URI = "uri";
    static final String KEY_EXTRAS_BUNDLE = "extras";
    static final String KEY_NOTIFICATION_PERSON = "sender_person";
    static final String KEY_PERSON = "person";
    static final String KEY_SENDER = "sender";
    static final String KEY_TEXT = "text";
    static final String KEY_TIMESTAMP = "time";
    private String mDataMimeType;
    private Uri mDataUri;
    private Bundle mExtras;
    private final Person mPerson;
    private final CharSequence mText;
    private final long mTimestamp;

    public NotificationCompat.MessagingStyle.Message(CharSequence charSequence0, long v, Person person0) {
        this.mExtras = new Bundle();
        this.mText = charSequence0;
        this.mTimestamp = v;
        this.mPerson = person0;
    }

    @Deprecated
    public NotificationCompat.MessagingStyle.Message(CharSequence charSequence0, long v, CharSequence charSequence1) {
        this(charSequence0, v, new Builder().setName(charSequence1).build());
    }

    public static Bundle[] getBundleArrayForMessages(List list0) {
        Bundle[] arr_bundle = new Bundle[list0.size()];
        int v = list0.size();
        for(int v1 = 0; v1 < v; ++v1) {
            arr_bundle[v1] = ((NotificationCompat.MessagingStyle.Message)list0.get(v1)).toBundle();
        }
        return arr_bundle;
    }

    public String getDataMimeType() {
        return this.mDataMimeType;
    }

    public Uri getDataUri() {
        return this.mDataUri;
    }

    public Bundle getExtras() {
        return this.mExtras;
    }

    public static NotificationCompat.MessagingStyle.Message getMessageFromBundle(Bundle bundle0) {
        Person person0;
        try {
            if(bundle0.containsKey("text") && bundle0.containsKey("time")) {
                if(bundle0.containsKey("person")) {
                    person0 = Person.fromBundle(bundle0.getBundle("person"));
                }
                else if(bundle0.containsKey("sender_person") && Build.VERSION.SDK_INT >= 28) {
                    person0 = Person.fromAndroidPerson(a.m(bundle0.getParcelable("sender_person")));
                }
                else {
                    person0 = bundle0.containsKey("sender") ? new Builder().setName(bundle0.getCharSequence("sender")).build() : null;
                }
                NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message0 = new NotificationCompat.MessagingStyle.Message(bundle0.getCharSequence("text"), bundle0.getLong("time"), person0);
                if(bundle0.containsKey("type") && bundle0.containsKey("uri")) {
                    notificationCompat$MessagingStyle$Message0.setData(bundle0.getString("type"), ((Uri)bundle0.getParcelable("uri")));
                }
                if(bundle0.containsKey("extras")) {
                    notificationCompat$MessagingStyle$Message0.getExtras().putAll(bundle0.getBundle("extras"));
                }
                return notificationCompat$MessagingStyle$Message0;
            }
        }
        catch(ClassCastException unused_ex) {
        }
        return null;
    }

    public static List getMessagesFromBundleArray(Parcelable[] arr_parcelable) {
        List list0 = new ArrayList(arr_parcelable.length);
        for(int v = 0; v < arr_parcelable.length; ++v) {
            Parcelable parcelable0 = arr_parcelable[v];
            if(parcelable0 instanceof Bundle) {
                NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message0 = NotificationCompat.MessagingStyle.Message.getMessageFromBundle(((Bundle)parcelable0));
                if(notificationCompat$MessagingStyle$Message0 != null) {
                    ((ArrayList)list0).add(notificationCompat$MessagingStyle$Message0);
                }
            }
        }
        return list0;
    }

    public Person getPerson() {
        return this.mPerson;
    }

    @Deprecated
    public CharSequence getSender() {
        return this.mPerson == null ? null : this.mPerson.getName();
    }

    public CharSequence getText() {
        return this.mText;
    }

    public long getTimestamp() {
        return this.mTimestamp;
    }

    public NotificationCompat.MessagingStyle.Message setData(String s, Uri uri0) {
        this.mDataMimeType = s;
        this.mDataUri = uri0;
        return this;
    }

    private Bundle toBundle() {
        Bundle bundle0 = new Bundle();
        CharSequence charSequence0 = this.mText;
        if(charSequence0 != null) {
            bundle0.putCharSequence("text", charSequence0);
        }
        bundle0.putLong("time", this.mTimestamp);
        Person person0 = this.mPerson;
        if(person0 != null) {
            bundle0.putCharSequence("sender", person0.getName());
            if(Build.VERSION.SDK_INT >= 28) {
                bundle0.putParcelable("sender_person", this.mPerson.toAndroidPerson());
            }
            else {
                bundle0.putBundle("person", this.mPerson.toBundle());
            }
        }
        String s = this.mDataMimeType;
        if(s != null) {
            bundle0.putString("type", s);
        }
        Uri uri0 = this.mDataUri;
        if(uri0 != null) {
            bundle0.putParcelable("uri", uri0);
        }
        Bundle bundle1 = this.mExtras;
        if(bundle1 != null) {
            bundle0.putBundle("extras", bundle1);
        }
        return bundle0;
    }
}


package android.supportv1.v4.app;

import android.app.PendingIntent;

public class NotificationCompat.CarExtender.UnreadConversation {
    private final long mLatestTimestamp;
    private final String[] mMessages;
    private final String[] mParticipants;
    private final PendingIntent mReadPendingIntent;
    private final RemoteInput mRemoteInput;
    private final PendingIntent mReplyPendingIntent;

    public NotificationCompat.CarExtender.UnreadConversation(String[] arr_s, RemoteInput remoteInput0, PendingIntent pendingIntent0, PendingIntent pendingIntent1, String[] arr_s1, long v) {
        this.mMessages = arr_s;
        this.mRemoteInput = remoteInput0;
        this.mReadPendingIntent = pendingIntent1;
        this.mReplyPendingIntent = pendingIntent0;
        this.mParticipants = arr_s1;
        this.mLatestTimestamp = v;
    }

    public long getLatestTimestamp() {
        return this.mLatestTimestamp;
    }

    public String[] getMessages() {
        return this.mMessages;
    }

    public String getParticipant() {
        return this.mParticipants.length <= 0 ? null : this.mParticipants[0];
    }

    public String[] getParticipants() {
        return this.mParticipants;
    }

    public PendingIntent getReadPendingIntent() {
        return this.mReadPendingIntent;
    }

    public RemoteInput getRemoteInput() {
        return this.mRemoteInput;
    }

    public PendingIntent getReplyPendingIntent() {
        return this.mReplyPendingIntent;
    }
}


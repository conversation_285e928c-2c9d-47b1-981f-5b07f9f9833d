package android.supportv1.v4.app;

import android.archv1.lifecycle.e;
import android.archv1.lifecycle.q;
import android.os.Bundle;
import android.supportv1.v4.content.Loader;
import java.io.FileDescriptor;
import java.io.PrintWriter;

public abstract class LoaderManager {
    public interface LoaderCallbacks {
        Loader onCreateLoader(int arg1, Bundle arg2);

        void onLoadFinished(Loader arg1, Object arg2);

        void onLoaderReset(Loader arg1);
    }

    public abstract void destroyLoader(int arg1);

    @Deprecated
    public abstract void dump(String arg1, FileDescriptor arg2, PrintWriter arg3, String[] arg4);

    public static void enableDebugLogging(boolean z) {
        LoaderManagerImpl.DEBUG = z;
    }

    public static LoaderManager getInstance(e e0) {
        return new LoaderManagerImpl(e0, ((q)e0).getViewModelStore());
    }

    public abstract Loader getLoader(int arg1);

    public boolean hasRunningLoaders() {
        return false;
    }

    public abstract Loader initLoader(int arg1, Bundle arg2, LoaderCallbacks arg3);

    public abstract void markForRedelivery();

    public abstract Loader restartLoader(int arg1, Bundle arg2, LoaderCallbacks arg3);
}


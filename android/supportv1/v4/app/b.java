package android.supportv1.v4.app;

import android.os.Message;
import android.os.PersistableBundle;
import android.view.View;
import android.view.accessibility.AccessibilityNodeInfo;

public abstract class b {
    public static void h(Message message0) {
        message0.setAsynchronous(true);
    }

    public static void i(PersistableBundle persistableBundle0) {
        persistableBundle0.putBoolean("extraLongLived", false);
    }

    public static void j(PersistableBundle persistableBundle0, boolean z) {
        persistableBundle0.putBoolean("EXTRA_IS_PERIODIC", z);
    }

    public static void k(View view0, AccessibilityNodeInfo accessibilityNodeInfo0) {
        accessibilityNodeInfo0.setTraversalBefore(view0);
    }

    public static void n(Message message0) {
        message0.setAsynchronous(true);
    }

    public static void o(View view0, AccessibilityNodeInfo accessibilityNodeInfo0) {
        accessibilityNodeInfo0.setTraversalAfter(view0);
    }
}


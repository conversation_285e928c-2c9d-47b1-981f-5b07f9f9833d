package android.supportv1.v4.app;

import android.app.job.JobParameters;
import android.app.job.JobWorkItem;
import android.content.Intent;

final class JobIntentService.JobServiceEngineImpl.WrapperWorkItem implements GenericWorkItem {
    final JobWorkItem mJobWork;

    public JobIntentService.JobServiceEngineImpl.WrapperWorkItem(JobServiceEngineImpl jobIntentService$JobServiceEngineImpl0, JobWorkItem jobWorkItem0) {
        JobServiceEngineImpl.this = jobIntentService$JobServiceEngineImpl0;
        super();
        this.mJobWork = jobWorkItem0;
    }

    @Override  // android.supportv1.v4.app.JobIntentService$GenericWorkItem
    public void complete() {
        synchronized(JobServiceEngineImpl.this.mLock) {
            JobParameters jobParameters0 = JobServiceEngineImpl.this.mParams;
            if(jobParameters0 != null) {
                jobParameters0.completeWork(this.mJobWork);
            }
        }
    }

    @Override  // android.supportv1.v4.app.JobIntentService$GenericWorkItem
    public Intent getIntent() {
        return this.mJobWork.getIntent();
    }
}


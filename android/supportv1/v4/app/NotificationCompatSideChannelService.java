package android.supportv1.v4.app;

import android.app.Notification;
import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.IBinder;
import android.os.RemoteException;

public abstract class NotificationCompatSideChannelService extends Service {
    class NotificationSideChannelStub extends Stub {
        @Override  // android.supportv1.v4.app.INotificationSideChannel
        public void cancel(String s, int v, String s1) throws RemoteException {
            int v1 = Binder.getCallingUid();
            NotificationCompatSideChannelService.this.checkPermission(v1, s);
            long v2 = Binder.clearCallingIdentity();
            try {
                NotificationCompatSideChannelService.this.cancel(s, v, s1);
            }
            finally {
                Binder.restoreCallingIdentity(v2);
            }
        }

        @Override  // android.supportv1.v4.app.INotificationSideChannel
        public void cancelAll(String s) {
            int v = Binder.getCallingUid();
            NotificationCompatSideChannelService.this.checkPermission(v, s);
            long v1 = Binder.clearCallingIdentity();
            try {
                NotificationCompatSideChannelService.this.cancelAll(s);
            }
            finally {
                Binder.restoreCallingIdentity(v1);
            }
        }

        @Override  // android.supportv1.v4.app.INotificationSideChannel
        public void notify(String s, int v, String s1, Notification notification0) throws RemoteException {
            int v1 = Binder.getCallingUid();
            NotificationCompatSideChannelService.this.checkPermission(v1, s);
            long v2 = Binder.clearCallingIdentity();
            try {
                NotificationCompatSideChannelService.this.notify(s, v, s1, notification0);
            }
            finally {
                Binder.restoreCallingIdentity(v2);
            }
        }
    }

    public abstract void cancel(String arg1, int arg2, String arg3);

    public abstract void cancelAll(String arg1);

    public void checkPermission(int v, String s) {
        String[] arr_s = this.getPackageManager().getPackagesForUid(v);
        for(int v1 = 0; v1 < arr_s.length; ++v1) {
            if(arr_s[v1].equals(s)) {
                return;
            }
        }
        throw new SecurityException("NotificationSideChannelService: Uid " + v + " is not authorized for package " + s);
    }

    public abstract void notify(String arg1, int arg2, String arg3, Notification arg4);

    @Override  // android.app.Service
    public IBinder onBind(Intent intent0) {
        intent0.getAction().equals("android.supportv1.BIND_NOTIFICATION_SIDE_CHANNEL");
        return null;
    }
}


package android.supportv1.v4.app;

import a3.a;
import android.app.Activity;
import android.os.Build.VERSION;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.SparseIntArray;
import android.view.FrameMetrics;
import android.view.Window.OnFrameMetricsAvailableListener;
import android.view.Window;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.ref.WeakReference;
import java.util.ArrayList;

public class FrameMetricsAggregator {
    static class FrameMetricsApi24Impl extends FrameMetricsBaseImpl {
        private static final int NANOS_PER_MS = 1000000;
        private static final int NANOS_ROUNDING_VALUE = 500000;
        private ArrayList mActivities;
        Window.OnFrameMetricsAvailableListener mListener;
        SparseIntArray[] mMetrics;
        int mTrackingFlags;
        private static Handler sHandler;
        private static HandlerThread sHandlerThread;

        public FrameMetricsApi24Impl(int v) {
            this.mMetrics = new SparseIntArray[9];
            this.mActivities = new ArrayList();
            this.mListener = new Window.OnFrameMetricsAvailableListener() {
                @Override  // android.view.Window$OnFrameMetricsAvailableListener
                public void onFrameMetricsAvailable(Window window0, FrameMetrics frameMetrics0, int v) {
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl0 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl0.mTrackingFlags & 1) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl0.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl0.mMetrics[0], a.b(frameMetrics0));
                    }
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl1 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl1.mTrackingFlags & 2) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl1.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl1.mMetrics[1], a.t(frameMetrics0));
                    }
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl2 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl2.mTrackingFlags & 4) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl2.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl2.mMetrics[2], a.w(frameMetrics0));
                    }
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl3 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl3.mTrackingFlags & 8) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl3.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl3.mMetrics[3], a.y(frameMetrics0));
                    }
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl4 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl4.mTrackingFlags & 16) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl4.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl4.mMetrics[4], a.z(frameMetrics0));
                    }
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl5 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl5.mTrackingFlags & 0x40) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl5.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl5.mMetrics[6], a.A(frameMetrics0));
                    }
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl6 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl6.mTrackingFlags & 0x20) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl6.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl6.mMetrics[5], a.B(frameMetrics0));
                    }
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl7 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl7.mTrackingFlags & 0x80) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl7.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl7.mMetrics[7], a.C(frameMetrics0));
                    }
                    FrameMetricsApi24Impl frameMetricsAggregator$FrameMetricsApi24Impl8 = FrameMetricsApi24Impl.this;
                    if((frameMetricsAggregator$FrameMetricsApi24Impl8.mTrackingFlags & 0x100) != 0) {
                        frameMetricsAggregator$FrameMetricsApi24Impl8.addDurationItem(frameMetricsAggregator$FrameMetricsApi24Impl8.mMetrics[8], a.D(frameMetrics0));
                    }
                }
            };
            this.mTrackingFlags = v;
        }

        @Override  // android.supportv1.v4.app.FrameMetricsAggregator$FrameMetricsBaseImpl
        public void add(Activity activity0) {
            if(FrameMetricsApi24Impl.sHandlerThread == null) {
                HandlerThread handlerThread0 = new HandlerThread("FrameMetricsAggregator");
                FrameMetricsApi24Impl.sHandlerThread = handlerThread0;
                handlerThread0.start();
                FrameMetricsApi24Impl.sHandler = new Handler(FrameMetricsApi24Impl.sHandlerThread.getLooper());
            }
            for(int v = 0; v <= 8; ++v) {
                SparseIntArray[] arr_sparseIntArray = this.mMetrics;
                if(arr_sparseIntArray[v] == null && (this.mTrackingFlags & 1 << v) != 0) {
                    arr_sparseIntArray[v] = new SparseIntArray();
                }
            }
            activity0.getWindow().addOnFrameMetricsAvailableListener(this.mListener, FrameMetricsApi24Impl.sHandler);
            this.mActivities.add(new WeakReference(activity0));
        }

        public void addDurationItem(SparseIntArray sparseIntArray0, long v) {
            if(sparseIntArray0 != null) {
                int v1 = (int)((v + 500000L) / 1000000L);
                if(v >= 0L) {
                    sparseIntArray0.put(v1, sparseIntArray0.get(v1) + 1);
                }
            }
        }

        @Override  // android.supportv1.v4.app.FrameMetricsAggregator$FrameMetricsBaseImpl
        public SparseIntArray[] getMetrics() {
            return this.mMetrics;
        }

        @Override  // android.supportv1.v4.app.FrameMetricsAggregator$FrameMetricsBaseImpl
        public SparseIntArray[] remove(Activity activity0) {
            for(Object object0: this.mActivities) {
                WeakReference weakReference0 = (WeakReference)object0;
                if(weakReference0.get() == activity0) {
                    this.mActivities.remove(weakReference0);
                    break;
                }
                if(false) {
                    break;
                }
            }
            activity0.getWindow().removeOnFrameMetricsAvailableListener(this.mListener);
            return this.mMetrics;
        }

        @Override  // android.supportv1.v4.app.FrameMetricsAggregator$FrameMetricsBaseImpl
        public SparseIntArray[] reset() {
            SparseIntArray[] arr_sparseIntArray = this.mMetrics;
            this.mMetrics = new SparseIntArray[9];
            return arr_sparseIntArray;
        }

        @Override  // android.supportv1.v4.app.FrameMetricsAggregator$FrameMetricsBaseImpl
        public SparseIntArray[] stop() {
            for(int v = this.mActivities.size() - 1; v >= 0; --v) {
                WeakReference weakReference0 = (WeakReference)this.mActivities.get(v);
                Activity activity0 = (Activity)weakReference0.get();
                if(weakReference0.get() != null) {
                    activity0.getWindow().removeOnFrameMetricsAvailableListener(this.mListener);
                    this.mActivities.remove(v);
                }
            }
            return this.mMetrics;
        }
    }

    static class FrameMetricsBaseImpl {
        public void add(Activity activity0) {
        }

        public SparseIntArray[] getMetrics() {
            return null;
        }

        public SparseIntArray[] remove(Activity activity0) {
            return null;
        }

        public SparseIntArray[] reset() {
            return null;
        }

        public SparseIntArray[] stop() {
            return null;
        }
    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface MetricType {
    }

    public static final int ANIMATION_DURATION = 0x100;
    public static final int ANIMATION_INDEX = 8;
    public static final int COMMAND_DURATION = 0x20;
    public static final int COMMAND_INDEX = 5;
    private static final boolean DBG = false;
    public static final int DELAY_DURATION = 0x80;
    public static final int DELAY_INDEX = 7;
    public static final int DRAW_DURATION = 8;
    public static final int DRAW_INDEX = 3;
    public static final int EVERY_DURATION = 0x1FF;
    public static final int INPUT_DURATION = 2;
    public static final int INPUT_INDEX = 1;
    private static final int LAST_INDEX = 8;
    public static final int LAYOUT_MEASURE_DURATION = 4;
    public static final int LAYOUT_MEASURE_INDEX = 2;
    public static final int SWAP_DURATION = 0x40;
    public static final int SWAP_INDEX = 6;
    public static final int SYNC_DURATION = 16;
    public static final int SYNC_INDEX = 4;
    private static final String TAG = "FrameMetrics";
    public static final int TOTAL_DURATION = 1;
    public static final int TOTAL_INDEX;
    private FrameMetricsBaseImpl mInstance;

    public FrameMetricsAggregator() {
        this(1);
    }

    public FrameMetricsAggregator(int v) {
        if(Build.VERSION.SDK_INT >= 24) {
            this.mInstance = new FrameMetricsApi24Impl(v);
            return;
        }
        this.mInstance = new FrameMetricsBaseImpl();
    }

    public void add(Activity activity0) {
        this.mInstance.add(activity0);
    }

    public SparseIntArray[] getMetrics() {
        return this.mInstance.getMetrics();
    }

    public SparseIntArray[] remove(Activity activity0) {
        return this.mInstance.remove(activity0);
    }

    public SparseIntArray[] reset() {
        return this.mInstance.reset();
    }

    public SparseIntArray[] stop() {
        return this.mInstance.stop();
    }
}


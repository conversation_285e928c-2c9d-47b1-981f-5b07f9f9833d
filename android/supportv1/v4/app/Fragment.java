package android.supportv1.v4.app;

import a.a;
import android.animation.Animator;
import android.app.Activity;
import android.archv1.lifecycle.LiveData;
import android.archv1.lifecycle.c;
import android.archv1.lifecycle.e;
import android.archv1.lifecycle.f;
import android.archv1.lifecycle.j;
import android.archv1.lifecycle.n;
import android.archv1.lifecycle.p;
import android.archv1.lifecycle.q;
import android.content.ComponentCallbacks;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender.SendIntentException;
import android.content.IntentSender;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Looper;
import android.os.Parcel;
import android.os.Parcelable.ClassLoaderCreator;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.supportv1.v4.util.SimpleArrayMap;
import android.supportv1.v4.view.LayoutInflaterCompat;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.ContextMenu.ContextMenuInfo;
import android.view.ContextMenu;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View.OnCreateContextMenuListener;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;

public class Fragment implements e, q, ComponentCallbacks, View.OnCreateContextMenuListener {
    static class AnimationInfo {
        Boolean mAllowEnterTransitionOverlap;
        Boolean mAllowReturnTransitionOverlap;
        View mAnimatingAway;
        Animator mAnimator;
        Object mEnterTransition;
        SharedElementCallback mEnterTransitionCallback;
        boolean mEnterTransitionPostponed;
        Object mExitTransition;
        SharedElementCallback mExitTransitionCallback;
        boolean mIsHideReplaced;
        int mNextAnim;
        int mNextTransition;
        int mNextTransitionStyle;
        Object mReenterTransition;
        Object mReturnTransition;
        Object mSharedElementEnterTransition;
        Object mSharedElementReturnTransition;
        OnStartEnterTransitionListener mStartEnterTransitionListener;
        int mStateAfterAnimating;

        public AnimationInfo() {
            this.mEnterTransition = null;
            this.mReturnTransition = Fragment.USE_DEFAULT_TRANSITION;
            this.mExitTransition = null;
            this.mReenterTransition = Fragment.USE_DEFAULT_TRANSITION;
            this.mSharedElementEnterTransition = null;
            this.mSharedElementReturnTransition = Fragment.USE_DEFAULT_TRANSITION;
            this.mEnterTransitionCallback = null;
            this.mExitTransitionCallback = null;
        }
    }

    public static class InstantiationException extends RuntimeException {
        public InstantiationException(String s, Exception exception0) {
            super(s, exception0);
        }
    }

    interface OnStartEnterTransitionListener {
        void onStartEnterTransition();

        void startListening();
    }

    public static class SavedState implements Parcelable {
        public static final Parcelable.Creator CREATOR;
        final Bundle mState;

        static {
            SavedState.CREATOR = new Parcelable.ClassLoaderCreator() {
                public SavedState createFromParcel(Parcel parcel0) {
                    return new SavedState(parcel0, null);
                }

                public SavedState createFromParcel(Parcel parcel0, ClassLoader classLoader0) {
                    return new SavedState(parcel0, classLoader0);
                }

                @Override  // android.os.Parcelable$Creator
                public Object createFromParcel(Parcel parcel0) {
                    return this.createFromParcel(parcel0);
                }

                @Override  // android.os.Parcelable$ClassLoaderCreator
                public Object createFromParcel(Parcel parcel0, ClassLoader classLoader0) {
                    return this.createFromParcel(parcel0, classLoader0);
                }

                public SavedState[] newArray(int v) {
                    return new SavedState[v];
                }

                @Override  // android.os.Parcelable$Creator
                public Object[] newArray(int v) {
                    return this.newArray(v);
                }
            };
        }

        public SavedState(Bundle bundle0) {
            this.mState = bundle0;
        }

        public SavedState(Parcel parcel0, ClassLoader classLoader0) {
            Bundle bundle0 = parcel0.readBundle();
            this.mState = bundle0;
            if(classLoader0 != null && bundle0 != null) {
                bundle0.setClassLoader(classLoader0);
            }
        }

        @Override  // android.os.Parcelable
        public int describeContents() {
            return 0;
        }

        @Override  // android.os.Parcelable
        public void writeToParcel(Parcel parcel0, int v) {
            parcel0.writeBundle(this.mState);
        }
    }

    static final int ACTIVITY_CREATED = 2;
    static final int CREATED = 1;
    static final int INITIALIZING = 0;
    static final int RESUMED = 4;
    static final int STARTED = 3;
    static final Object USE_DEFAULT_TRANSITION;
    boolean mAdded;
    AnimationInfo mAnimationInfo;
    Bundle mArguments;
    int mBackStackNesting;
    boolean mCalled;
    FragmentManagerImpl mChildFragmentManager;
    FragmentManagerNonConfig mChildNonConfig;
    ViewGroup mContainer;
    int mContainerId;
    boolean mDeferStart;
    boolean mDetached;
    int mFragmentId;
    FragmentManagerImpl mFragmentManager;
    boolean mFromLayout;
    boolean mHasMenu;
    boolean mHidden;
    boolean mHiddenChanged;
    FragmentHostCallback mHost;
    boolean mInLayout;
    int mIndex;
    View mInnerView;
    boolean mIsCreated;
    boolean mIsNewlyAdded;
    LayoutInflater mLayoutInflater;
    f mLifecycleRegistry;
    boolean mMenuVisible;
    Fragment mParentFragment;
    boolean mPerformedCreateView;
    float mPostponedAlpha;
    boolean mRemoving;
    boolean mRestored;
    boolean mRetainInstance;
    boolean mRetaining;
    Bundle mSavedFragmentState;
    Boolean mSavedUserVisibleHint;
    SparseArray mSavedViewState;
    int mState;
    String mTag;
    Fragment mTarget;
    int mTargetIndex;
    int mTargetRequestCode;
    boolean mUserVisibleHint;
    View mView;
    e mViewLifecycleOwner;
    j mViewLifecycleOwnerLiveData;
    f mViewLifecycleRegistry;
    p mViewModelStore;
    String mWho;
    private static final SimpleArrayMap sClassMap;

    static {
        Fragment.sClassMap = new SimpleArrayMap();
        Fragment.USE_DEFAULT_TRANSITION = new Object();
    }

    public Fragment() {
        this.mState = 0;
        this.mIndex = -1;
        this.mTargetIndex = -1;
        this.mMenuVisible = true;
        this.mUserVisibleHint = true;
        this.mLifecycleRegistry = new f(this);
        this.mViewLifecycleOwnerLiveData = new j();  // 初始化器: Landroid/archv1/lifecycle/LiveData;-><init>()V
    }

    // 检测为 Lambda 实现
    public void callStartTransitionListener() [...]

    public void dump(String s, FileDescriptor fileDescriptor0, PrintWriter printWriter0, String[] arr_s) {
        printWriter0.print(s);
        printWriter0.print("mFragmentId=#");
        printWriter0.print(Integer.toHexString(this.mFragmentId));
        printWriter0.print(" mContainerId=#");
        printWriter0.print(Integer.toHexString(this.mContainerId));
        printWriter0.print(" mTag=");
        printWriter0.println(this.mTag);
        printWriter0.print(s);
        printWriter0.print("mState=");
        printWriter0.print(this.mState);
        printWriter0.print(" mIndex=");
        printWriter0.print(this.mIndex);
        printWriter0.print(" mWho=");
        printWriter0.print(this.mWho);
        printWriter0.print(" mBackStackNesting=");
        printWriter0.println(this.mBackStackNesting);
        printWriter0.print(s);
        printWriter0.print("mAdded=");
        printWriter0.print(this.mAdded);
        printWriter0.print(" mRemoving=");
        printWriter0.print(this.mRemoving);
        printWriter0.print(" mFromLayout=");
        printWriter0.print(this.mFromLayout);
        printWriter0.print(" mInLayout=");
        printWriter0.println(this.mInLayout);
        printWriter0.print(s);
        printWriter0.print("mHidden=");
        printWriter0.print(this.mHidden);
        printWriter0.print(" mDetached=");
        printWriter0.print(this.mDetached);
        printWriter0.print(" mMenuVisible=");
        printWriter0.print(this.mMenuVisible);
        printWriter0.print(" mHasMenu=");
        printWriter0.println(this.mHasMenu);
        printWriter0.print(s);
        printWriter0.print("mRetainInstance=");
        printWriter0.print(this.mRetainInstance);
        printWriter0.print(" mRetaining=");
        printWriter0.print(this.mRetaining);
        printWriter0.print(" mUserVisibleHint=");
        printWriter0.println(this.mUserVisibleHint);
        if(this.mFragmentManager != null) {
            printWriter0.print(s);
            printWriter0.print("mFragmentManager=");
            printWriter0.println(this.mFragmentManager);
        }
        if(this.mHost != null) {
            printWriter0.print(s);
            printWriter0.print("mHost=");
            printWriter0.println(this.mHost);
        }
        if(this.mParentFragment != null) {
            printWriter0.print(s);
            printWriter0.print("mParentFragment=");
            printWriter0.println(this.mParentFragment);
        }
        if(this.mArguments != null) {
            printWriter0.print(s);
            printWriter0.print("mArguments=");
            printWriter0.println(this.mArguments);
        }
        if(this.mSavedFragmentState != null) {
            printWriter0.print(s);
            printWriter0.print("mSavedFragmentState=");
            printWriter0.println(this.mSavedFragmentState);
        }
        if(this.mSavedViewState != null) {
            printWriter0.print(s);
            printWriter0.print("mSavedViewState=");
            printWriter0.println(this.mSavedViewState);
        }
        if(this.mTarget != null) {
            printWriter0.print(s);
            printWriter0.print("mTarget=");
            printWriter0.print(this.mTarget);
            printWriter0.print(" mTargetRequestCode=");
            printWriter0.println(this.mTargetRequestCode);
        }
        if(this.getNextAnim() != 0) {
            printWriter0.print(s);
            printWriter0.print("mNextAnim=");
            printWriter0.println(this.getNextAnim());
        }
        if(this.mContainer != null) {
            printWriter0.print(s);
            printWriter0.print("mContainer=");
            printWriter0.println(this.mContainer);
        }
        if(this.mView != null) {
            printWriter0.print(s);
            printWriter0.print("mView=");
            printWriter0.println(this.mView);
        }
        if(this.mInnerView != null) {
            printWriter0.print(s);
            printWriter0.print("mInnerView=");
            printWriter0.println(this.mView);
        }
        if(this.getAnimatingAway() != null) {
            printWriter0.print(s);
            printWriter0.print("mAnimatingAway=");
            printWriter0.println(this.getAnimatingAway());
            printWriter0.print(s);
            printWriter0.print("mStateAfterAnimating=");
            printWriter0.println(this.getStateAfterAnimating());
        }
        if(this.getContext() != null) {
            LoaderManager.getInstance(this).dump(s, fileDescriptor0, printWriter0, arr_s);
        }
        if(this.mChildFragmentManager != null) {
            printWriter0.print(s);
            printWriter0.println("Child " + this.mChildFragmentManager + ":");
            this.mChildFragmentManager.dump(s + "  ", fileDescriptor0, printWriter0, arr_s);
        }
    }

    private AnimationInfo ensureAnimationInfo() {
        if(this.mAnimationInfo == null) {
            this.mAnimationInfo = new AnimationInfo();
        }
        return this.mAnimationInfo;
    }

    @Override
    public final boolean equals(Object object0) {
        return super.equals(object0);
    }

    public Fragment findFragmentByWho(String s) {
        if(s.equals(this.mWho)) {
            return this;
        }
        return this.mChildFragmentManager == null ? null : this.mChildFragmentManager.findFragmentByWho(s);
    }

    public final FragmentActivity getActivity() {
        return this.mHost == null ? null : ((FragmentActivity)this.mHost.getActivity());
    }

    public boolean getAllowEnterTransitionOverlap() {
        AnimationInfo fragment$AnimationInfo0 = this.mAnimationInfo;
        if(fragment$AnimationInfo0 != null) {
            return fragment$AnimationInfo0.mAllowEnterTransitionOverlap == null ? true : fragment$AnimationInfo0.mAllowEnterTransitionOverlap.booleanValue();
        }
        return true;
    }

    public boolean getAllowReturnTransitionOverlap() {
        AnimationInfo fragment$AnimationInfo0 = this.mAnimationInfo;
        if(fragment$AnimationInfo0 != null) {
            return fragment$AnimationInfo0.mAllowReturnTransitionOverlap == null ? true : fragment$AnimationInfo0.mAllowReturnTransitionOverlap.booleanValue();
        }
        return true;
    }

    public View getAnimatingAway() {
        return this.mAnimationInfo == null ? null : this.mAnimationInfo.mAnimatingAway;
    }

    public Animator getAnimator() {
        return this.mAnimationInfo == null ? null : this.mAnimationInfo.mAnimator;
    }

    public final Bundle getArguments() {
        return this.mArguments;
    }

    public final FragmentManager getChildFragmentManager() {
        if(this.mChildFragmentManager == null) {
            this.instantiateChildFragmentManager();
            int v = this.mState;
            if(v >= 4) {
                this.mChildFragmentManager.dispatchResume();
                return this.mChildFragmentManager;
            }
            if(v >= 3) {
                this.mChildFragmentManager.dispatchStart();
                return this.mChildFragmentManager;
            }
            if(v >= 2) {
                this.mChildFragmentManager.dispatchActivityCreated();
                return this.mChildFragmentManager;
            }
            if(v >= 1) {
                this.mChildFragmentManager.dispatchCreate();
            }
        }
        return this.mChildFragmentManager;
    }

    public Context getContext() {
        return this.mHost == null ? null : this.mHost.getContext();
    }

    public Object getEnterTransition() {
        return this.mAnimationInfo == null ? null : this.mAnimationInfo.mEnterTransition;
    }

    public SharedElementCallback getEnterTransitionCallback() {
        return this.mAnimationInfo == null ? null : this.mAnimationInfo.mEnterTransitionCallback;
    }

    public Object getExitTransition() {
        return this.mAnimationInfo == null ? null : this.mAnimationInfo.mExitTransition;
    }

    public SharedElementCallback getExitTransitionCallback() {
        return this.mAnimationInfo == null ? null : this.mAnimationInfo.mExitTransitionCallback;
    }

    public final FragmentManager getFragmentManager() {
        return this.mFragmentManager;
    }

    public final Object getHost() {
        return this.mHost == null ? null : this.mHost.onGetHost();
    }

    public final int getId() {
        return this.mFragmentId;
    }

    public final LayoutInflater getLayoutInflater() {
        return this.mLayoutInflater == null ? this.performGetLayoutInflater(null) : this.mLayoutInflater;
    }

    @Deprecated
    public LayoutInflater getLayoutInflater(Bundle bundle0) {
        FragmentHostCallback fragmentHostCallback0 = this.mHost;
        if(fragmentHostCallback0 == null) {
            throw new IllegalStateException("onGetLayoutInflater() cannot be executed until the Fragment is attached to the FragmentManager.");
        }
        LayoutInflater layoutInflater0 = fragmentHostCallback0.onGetLayoutInflater();
        this.getChildFragmentManager();
        LayoutInflaterCompat.setFactory2(layoutInflater0, this.mChildFragmentManager);
        return layoutInflater0;
    }

    @Override  // android.archv1.lifecycle.e
    public c getLifecycle() {
        return this.mLifecycleRegistry;
    }

    @Deprecated
    public LoaderManager getLoaderManager() {
        return LoaderManager.getInstance(this);
    }

    public int getNextAnim() {
        return this.mAnimationInfo == null ? 0 : this.mAnimationInfo.mNextAnim;
    }

    public int getNextTransition() {
        return this.mAnimationInfo == null ? 0 : this.mAnimationInfo.mNextTransition;
    }

    public int getNextTransitionStyle() {
        return this.mAnimationInfo == null ? 0 : this.mAnimationInfo.mNextTransitionStyle;
    }

    public final Fragment getParentFragment() {
        return this.mParentFragment;
    }

    public Object getReenterTransition() {
        AnimationInfo fragment$AnimationInfo0 = this.mAnimationInfo;
        if(fragment$AnimationInfo0 == null) {
            return null;
        }
        return fragment$AnimationInfo0.mReenterTransition == Fragment.USE_DEFAULT_TRANSITION ? this.getExitTransition() : fragment$AnimationInfo0.mReenterTransition;
    }

    public final Resources getResources() {
        return this.requireContext().getResources();
    }

    public final boolean getRetainInstance() {
        return this.mRetainInstance;
    }

    public Object getReturnTransition() {
        AnimationInfo fragment$AnimationInfo0 = this.mAnimationInfo;
        if(fragment$AnimationInfo0 == null) {
            return null;
        }
        return fragment$AnimationInfo0.mReturnTransition == Fragment.USE_DEFAULT_TRANSITION ? this.getEnterTransition() : fragment$AnimationInfo0.mReturnTransition;
    }

    public Object getSharedElementEnterTransition() {
        return this.mAnimationInfo == null ? null : this.mAnimationInfo.mSharedElementEnterTransition;
    }

    public Object getSharedElementReturnTransition() {
        AnimationInfo fragment$AnimationInfo0 = this.mAnimationInfo;
        if(fragment$AnimationInfo0 == null) {
            return null;
        }
        return fragment$AnimationInfo0.mSharedElementReturnTransition == Fragment.USE_DEFAULT_TRANSITION ? this.getSharedElementEnterTransition() : fragment$AnimationInfo0.mSharedElementReturnTransition;
    }

    public int getStateAfterAnimating() {
        return this.mAnimationInfo == null ? 0 : this.mAnimationInfo.mStateAfterAnimating;
    }

    public final String getString(int v) {
        return this.getResources().getString(v);
    }

    public final String getString(int v, Object[] arr_object) {
        return this.getResources().getString(v, arr_object);
    }

    public final String getTag() {
        return this.mTag;
    }

    public final Fragment getTargetFragment() {
        return this.mTarget;
    }

    public final int getTargetRequestCode() {
        return this.mTargetRequestCode;
    }

    public final CharSequence getText(int v) {
        return this.getResources().getText(v);
    }

    public boolean getUserVisibleHint() {
        return this.mUserVisibleHint;
    }

    public View getView() {
        return this.mView;
    }

    public e getViewLifecycleOwner() {
        e e0 = this.mViewLifecycleOwner;
        if(e0 == null) {
            throw new IllegalStateException("Can\'t access the Fragment View\'s LifecycleOwner when getView() is null i.e., before onCreateView() or after onDestroyView()");
        }
        return e0;
    }

    public LiveData getViewLifecycleOwnerLiveData() {
        return this.mViewLifecycleOwnerLiveData;
    }

    @Override  // android.archv1.lifecycle.q
    public p getViewModelStore() {
        if(this.getContext() == null) {
            throw new IllegalStateException("Can\'t access ViewModels from detached fragment");
        }
        if(this.mViewModelStore == null) {
            this.mViewModelStore = new p();
        }
        return this.mViewModelStore;
    }

    public final boolean hasOptionsMenu() {
        return this.mHasMenu;
    }

    @Override
    public final int hashCode() {
        return super.hashCode();
    }

    public void initState() {
        this.mIndex = -1;
        this.mWho = null;
        this.mAdded = false;
        this.mRemoving = false;
        this.mFromLayout = false;
        this.mInLayout = false;
        this.mRestored = false;
        this.mBackStackNesting = 0;
        this.mFragmentManager = null;
        this.mChildFragmentManager = null;
        this.mHost = null;
        this.mFragmentId = 0;
        this.mContainerId = 0;
        this.mTag = null;
        this.mHidden = false;
        this.mDetached = false;
        this.mRetaining = false;
    }

    public static Fragment instantiate(Context context0, String s) {
        return Fragment.instantiate(context0, s, null);
    }

    public static Fragment instantiate(Context context0, String s, Bundle bundle0) {
        try {
            SimpleArrayMap simpleArrayMap0 = Fragment.sClassMap;
            Class class0 = (Class)simpleArrayMap0.get(s);
            if(class0 == null) {
                class0 = context0.getClassLoader().loadClass(s);
                simpleArrayMap0.put(s, class0);
            }
            Fragment fragment0 = (Fragment)class0.getConstructor().newInstance();
            if(bundle0 != null) {
                bundle0.setClassLoader(fragment0.getClass().getClassLoader());
                fragment0.setArguments(bundle0);
            }
            return fragment0;
        }
        catch(ClassNotFoundException classNotFoundException0) {
        }
        catch(java.lang.InstantiationException instantiationException0) {
            throw new InstantiationException(a.y("Unable to instantiate fragment ", s, ": make sure class name exists, is public, and has an empty constructor that is public").toString(), instantiationException0);
        }
        catch(IllegalAccessException illegalAccessException0) {
            throw new InstantiationException(a.y("Unable to instantiate fragment ", s, ": make sure class name exists, is public, and has an empty constructor that is public").toString(), illegalAccessException0);
        }
        catch(NoSuchMethodException noSuchMethodException0) {
            throw new InstantiationException(a.y("Unable to instantiate fragment ", s, ": could not find Fragment constructor").toString(), noSuchMethodException0);
        }
        catch(InvocationTargetException invocationTargetException0) {
            throw new InstantiationException(a.y("Unable to instantiate fragment ", s, ": calling Fragment constructor caused an exception").toString(), invocationTargetException0);
        }
        throw new InstantiationException(a.y("Unable to instantiate fragment ", s, ": make sure class name exists, is public, and has an empty constructor that is public").toString(), classNotFoundException0);
    }

    public void instantiateChildFragmentManager() {
        if(this.mHost == null) {
            throw new IllegalStateException("Fragment has not been attached yet.");
        }
        FragmentManagerImpl fragmentManagerImpl0 = new FragmentManagerImpl();
        this.mChildFragmentManager = fragmentManagerImpl0;
        fragmentManagerImpl0.attachController(this.mHost, new FragmentContainer() {
            @Override  // android.supportv1.v4.app.FragmentContainer
            public Fragment instantiate(Context context0, String s, Bundle bundle0) {
                return Fragment.this.mHost.instantiate(context0, s, bundle0);
            }

            @Override  // android.supportv1.v4.app.FragmentContainer
            public View onFindViewById(int v) {
                View view0 = Fragment.this.mView;
                if(view0 == null) {
                    throw new IllegalStateException("Fragment does not have a view");
                }
                return view0.findViewById(v);
            }

            @Override  // android.supportv1.v4.app.FragmentContainer
            public boolean onHasView() {
                return Fragment.this.mView != null;
            }
        }, this);
    }

    public final boolean isAdded() {
        return this.mHost != null && this.mAdded;
    }

    public final boolean isDetached() {
        return this.mDetached;
    }

    public final boolean isHidden() {
        return this.mHidden;
    }

    public boolean isHideReplaced() {
        return this.mAnimationInfo == null ? false : this.mAnimationInfo.mIsHideReplaced;
    }

    public final boolean isInBackStack() {
        return this.mBackStackNesting > 0;
    }

    public final boolean isInLayout() {
        return this.mInLayout;
    }

    public final boolean isMenuVisible() {
        return this.mMenuVisible;
    }

    public boolean isPostponed() {
        return this.mAnimationInfo == null ? false : this.mAnimationInfo.mEnterTransitionPostponed;
    }

    public final boolean isRemoving() {
        return this.mRemoving;
    }

    public final boolean isResumed() {
        return this.mState >= 4;
    }

    public final boolean isStateSaved() {
        return this.mFragmentManager == null ? false : this.mFragmentManager.isStateSaved();
    }

    public static boolean isSupportFragmentClass(Context context0, String s) {
        try {
            SimpleArrayMap simpleArrayMap0 = Fragment.sClassMap;
            Class class0 = (Class)simpleArrayMap0.get(s);
            if(class0 == null) {
                class0 = context0.getClassLoader().loadClass(s);
                simpleArrayMap0.put(s, class0);
            }
            return Fragment.class.isAssignableFrom(class0);
        }
        catch(ClassNotFoundException unused_ex) {
            return false;
        }
    }

    // 去混淆评级： 低(20)
    public final boolean isVisible() {
        return this.isAdded() && !this.isHidden() && (this.mView != null && this.mView.getWindowToken() != null && this.mView.getVisibility() == 0);
    }

    public void noteStateNotSaved() {
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.noteStateNotSaved();
        }
    }

    public void onActivityCreated(Bundle bundle0) {
        this.mCalled = true;
    }

    public void onActivityResult(int v, int v1, Intent intent0) {
    }

    @Deprecated
    public void onAttach(Activity activity0) {
        this.mCalled = true;
    }

    public void onAttach(Context context0) {
        this.mCalled = true;
        Activity activity0 = this.mHost == null ? null : this.mHost.getActivity();
        if(activity0 != null) {
            this.mCalled = false;
            this.onAttach(activity0);
        }
    }

    public void onAttachFragment(Fragment fragment0) {
    }

    @Override  // android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration configuration0) {
        this.mCalled = true;
    }

    public boolean onContextItemSelected(MenuItem menuItem0) [...] // Inlined contents

    public void onCreate(Bundle bundle0) {
        this.mCalled = true;
        this.restoreChildFragmentState(bundle0);
        if(this.mChildFragmentManager != null && !this.mChildFragmentManager.isStateAtLeast(1)) {
            this.mChildFragmentManager.dispatchCreate();
        }
    }

    public Animation onCreateAnimation(int v, boolean z, int v1) [...] // Inlined contents

    public Animator onCreateAnimator(int v, boolean z, int v1) [...] // Inlined contents

    @Override  // android.view.View$OnCreateContextMenuListener
    public void onCreateContextMenu(ContextMenu contextMenu0, View view0, ContextMenu.ContextMenuInfo contextMenu$ContextMenuInfo0) {
        this.getActivity().onCreateContextMenu(contextMenu0, view0, contextMenu$ContextMenuInfo0);
    }

    public void onCreateOptionsMenu(Menu menu0, MenuInflater menuInflater0) {
    }

    public View onCreateView(LayoutInflater layoutInflater0, ViewGroup viewGroup0, Bundle bundle0) {
        return null;
    }

    public void onDestroy() {
        boolean z = true;
        this.mCalled = true;
        FragmentActivity fragmentActivity0 = this.getActivity();
        if(fragmentActivity0 == null || !fragmentActivity0.isChangingConfigurations()) {
            z = false;
        }
        p p0 = this.mViewModelStore;
        if(p0 != null && !z) {
            HashMap hashMap0 = p0.a;
            for(Object object0: hashMap0.values()) {
                ((n)object0).onCleared();
            }
            hashMap0.clear();
        }
    }

    public void onDestroyOptionsMenu() {
    }

    public void onDestroyView() {
        this.mCalled = true;
    }

    public void onDetach() {
        this.mCalled = true;
    }

    public LayoutInflater onGetLayoutInflater(Bundle bundle0) {
        return this.getLayoutInflater(bundle0);
    }

    public void onHiddenChanged(boolean z) {
    }

    @Deprecated
    public void onInflate(Activity activity0, AttributeSet attributeSet0, Bundle bundle0) {
        this.mCalled = true;
    }

    public void onInflate(Context context0, AttributeSet attributeSet0, Bundle bundle0) {
        this.mCalled = true;
        Activity activity0 = this.mHost == null ? null : this.mHost.getActivity();
        if(activity0 != null) {
            this.mCalled = false;
            this.onInflate(activity0, attributeSet0, bundle0);
        }
    }

    @Override  // android.content.ComponentCallbacks
    public void onLowMemory() {
        this.mCalled = true;
    }

    public void onMultiWindowModeChanged(boolean z) {
    }

    public boolean onOptionsItemSelected(MenuItem menuItem0) [...] // Inlined contents

    public void onOptionsMenuClosed(Menu menu0) {
    }

    public void onPause() {
        this.mCalled = true;
    }

    public void onPictureInPictureModeChanged(boolean z) {
    }

    public void onPrepareOptionsMenu(Menu menu0) {
    }

    public void onRequestPermissionsResult(int v, String[] arr_s, int[] arr_v) {
    }

    public void onResume() {
        this.mCalled = true;
    }

    public void onSaveInstanceState(Bundle bundle0) {
    }

    public void onStart() {
        this.mCalled = true;
    }

    public void onStop() {
        this.mCalled = true;
    }

    public void onViewCreated(View view0, Bundle bundle0) {
    }

    public void onViewStateRestored(Bundle bundle0) {
        this.mCalled = true;
    }

    public FragmentManager peekChildFragmentManager() {
        return this.mChildFragmentManager;
    }

    public void performActivityCreated(Bundle bundle0) {
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.noteStateNotSaved();
        }
        this.mState = 2;
        this.mCalled = false;
        this.onActivityCreated(bundle0);
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onActivityCreated()").toString());
        }
        FragmentManagerImpl fragmentManagerImpl1 = this.mChildFragmentManager;
        if(fragmentManagerImpl1 != null) {
            fragmentManagerImpl1.dispatchActivityCreated();
        }
    }

    public void performConfigurationChanged(Configuration configuration0) {
        this.onConfigurationChanged(configuration0);
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.dispatchConfigurationChanged(configuration0);
        }
    }

    // 去混淆评级： 低(20)
    public boolean performContextItemSelected(MenuItem menuItem0) {
        return !this.mHidden && (this.mChildFragmentManager != null && this.mChildFragmentManager.dispatchContextItemSelected(menuItem0));
    }

    public void performCreate(Bundle bundle0) {
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.noteStateNotSaved();
        }
        this.mState = 1;
        this.mCalled = false;
        this.onCreate(bundle0);
        this.mIsCreated = true;
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onCreate()").toString());
        }
        this.mLifecycleRegistry.a(android.archv1.lifecycle.c.a.b);
    }

    public boolean performCreateOptionsMenu(Menu menu0, MenuInflater menuInflater0) {
        boolean z = false;
        if(!this.mHidden) {
            if(this.mHasMenu && this.mMenuVisible) {
                z = true;
            }
            FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
            if(fragmentManagerImpl0 != null) {
                return z | fragmentManagerImpl0.dispatchCreateOptionsMenu(menu0, menuInflater0);
            }
        }
        return z;
    }

    public void performCreateView(LayoutInflater layoutInflater0, ViewGroup viewGroup0, Bundle bundle0) {
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.noteStateNotSaved();
        }
        this.mPerformedCreateView = true;
        this.mViewLifecycleOwner = new e() {
            @Override  // android.archv1.lifecycle.e
            public c getLifecycle() {
                Fragment fragment0 = Fragment.this;
                if(fragment0.mViewLifecycleRegistry == null) {
                    fragment0.mViewLifecycleRegistry = new f(fragment0.mViewLifecycleOwner);
                }
                return Fragment.this.mViewLifecycleRegistry;
            }
        };
        this.mViewLifecycleRegistry = null;
        View view0 = this.onCreateView(layoutInflater0, viewGroup0, bundle0);
        this.mView = view0;
        if(view0 != null) {
            this.mViewLifecycleOwner.getLifecycle();
            this.mViewLifecycleOwnerLiveData.setValue(this.mViewLifecycleOwner);
            return;
        }
        if(this.mViewLifecycleRegistry != null) {
            throw new IllegalStateException("Called getViewLifecycleOwner() but onCreateView() returned null");
        }
        this.mViewLifecycleOwner = null;
    }

    public void performDestroy() {
        this.mLifecycleRegistry.a(android.archv1.lifecycle.c.a.c);
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.dispatchDestroy();
        }
        this.mState = 0;
        this.mCalled = false;
        this.mIsCreated = false;
        this.onDestroy();
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onDestroy()").toString());
        }
        this.mChildFragmentManager = null;
    }

    public void performDestroyView() {
        if(this.mView != null) {
            this.mViewLifecycleRegistry.a(android.archv1.lifecycle.c.a.c);
        }
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.dispatchDestroyView();
        }
        this.mState = 1;
        this.mCalled = false;
        this.onDestroyView();
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onDestroyView()").toString());
        }
        LoaderManager.getInstance(this).markForRedelivery();
        this.mPerformedCreateView = false;
    }

    public void performDetach() {
        this.mCalled = false;
        this.onDetach();
        this.mLayoutInflater = null;
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onDetach()").toString());
        }
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            if(!this.mRetaining) {
                throw new IllegalStateException(a.x("Child FragmentManager of ", this, " was not  destroyed and this fragment is not retaining instance").toString());
            }
            fragmentManagerImpl0.dispatchDestroy();
            this.mChildFragmentManager = null;
        }
    }

    public LayoutInflater performGetLayoutInflater(Bundle bundle0) {
        LayoutInflater layoutInflater0 = this.onGetLayoutInflater(bundle0);
        this.mLayoutInflater = layoutInflater0;
        return layoutInflater0;
    }

    public void performLowMemory() {
        this.onLowMemory();
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.dispatchLowMemory();
        }
    }

    public void performMultiWindowModeChanged(boolean z) {
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.dispatchMultiWindowModeChanged(z);
        }
    }

    // 去混淆评级： 低(20)
    public boolean performOptionsItemSelected(MenuItem menuItem0) {
        return !this.mHidden && (this.mChildFragmentManager != null && this.mChildFragmentManager.dispatchOptionsItemSelected(menuItem0));
    }

    public void performOptionsMenuClosed(Menu menu0) {
        if(!this.mHidden) {
            FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
            if(fragmentManagerImpl0 != null) {
                fragmentManagerImpl0.dispatchOptionsMenuClosed(menu0);
            }
        }
    }

    public void performPause() {
        android.archv1.lifecycle.c.a c$a0 = android.archv1.lifecycle.c.a.d;
        if(this.mView != null) {
            this.mViewLifecycleRegistry.a(c$a0);
        }
        this.mLifecycleRegistry.a(c$a0);
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.dispatchPause();
        }
        this.mState = 3;
        this.mCalled = false;
        this.onPause();
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onPause()").toString());
        }
    }

    public void performPictureInPictureModeChanged(boolean z) {
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.dispatchPictureInPictureModeChanged(z);
        }
    }

    public boolean performPrepareOptionsMenu(Menu menu0) {
        boolean z = false;
        if(!this.mHidden) {
            if(this.mHasMenu && this.mMenuVisible) {
                z = true;
            }
            FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
            if(fragmentManagerImpl0 != null) {
                return z | fragmentManagerImpl0.dispatchPrepareOptionsMenu(menu0);
            }
        }
        return z;
    }

    public void performResume() {
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.noteStateNotSaved();
            this.mChildFragmentManager.execPendingActions();
        }
        this.mState = 4;
        this.mCalled = false;
        this.onResume();
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onResume()").toString());
        }
        FragmentManagerImpl fragmentManagerImpl1 = this.mChildFragmentManager;
        if(fragmentManagerImpl1 != null) {
            fragmentManagerImpl1.dispatchResume();
            this.mChildFragmentManager.execPendingActions();
        }
        android.archv1.lifecycle.c.a c$a0 = android.archv1.lifecycle.c.a.e;
        this.mLifecycleRegistry.a(c$a0);
        if(this.mView != null) {
            this.mViewLifecycleRegistry.a(c$a0);
        }
    }

    public void performSaveInstanceState(Bundle bundle0) {
        this.onSaveInstanceState(bundle0);
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            Parcelable parcelable0 = fragmentManagerImpl0.saveAllState();
            if(parcelable0 != null) {
                bundle0.putParcelable("android:support:fragments", parcelable0);
            }
        }
    }

    public void performStart() {
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.noteStateNotSaved();
            this.mChildFragmentManager.execPendingActions();
        }
        this.mState = 3;
        this.mCalled = false;
        this.onStart();
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onStart()").toString());
        }
        FragmentManagerImpl fragmentManagerImpl1 = this.mChildFragmentManager;
        if(fragmentManagerImpl1 != null) {
            fragmentManagerImpl1.dispatchStart();
        }
        android.archv1.lifecycle.c.a c$a0 = android.archv1.lifecycle.c.a.f;
        this.mLifecycleRegistry.a(c$a0);
        if(this.mView != null) {
            this.mViewLifecycleRegistry.a(c$a0);
        }
    }

    public void performStop() {
        android.archv1.lifecycle.c.a c$a0 = android.archv1.lifecycle.c.a.g;
        if(this.mView != null) {
            this.mViewLifecycleRegistry.a(c$a0);
        }
        this.mLifecycleRegistry.a(c$a0);
        FragmentManagerImpl fragmentManagerImpl0 = this.mChildFragmentManager;
        if(fragmentManagerImpl0 != null) {
            fragmentManagerImpl0.dispatchStop();
        }
        this.mState = 2;
        this.mCalled = false;
        this.onStop();
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onStop()").toString());
        }
    }

    public void postponeEnterTransition() {
        this.ensureAnimationInfo().mEnterTransitionPostponed = true;
    }

    public void registerForContextMenu(View view0) {
        view0.setOnCreateContextMenuListener(this);
    }

    public final void requestPermissions(String[] arr_s, int v) {
        FragmentHostCallback fragmentHostCallback0 = this.mHost;
        if(fragmentHostCallback0 == null) {
            throw new IllegalStateException(a.x("Fragment ", this, " not attached to Activity").toString());
        }
        fragmentHostCallback0.onRequestPermissionsFromFragment(this, arr_s, v);
    }

    public final FragmentActivity requireActivity() {
        FragmentActivity fragmentActivity0 = this.getActivity();
        if(fragmentActivity0 == null) {
            throw new IllegalStateException(a.x("Fragment ", this, " not attached to an activity.").toString());
        }
        return fragmentActivity0;
    }

    public final Context requireContext() {
        Context context0 = this.getContext();
        if(context0 == null) {
            throw new IllegalStateException(a.x("Fragment ", this, " not attached to a context.").toString());
        }
        return context0;
    }

    public final FragmentManager requireFragmentManager() {
        FragmentManager fragmentManager0 = this.getFragmentManager();
        if(fragmentManager0 == null) {
            throw new IllegalStateException(a.x("Fragment ", this, " not associated with a fragment manager.").toString());
        }
        return fragmentManager0;
    }

    public final Object requireHost() {
        Object object0 = this.getHost();
        if(object0 == null) {
            throw new IllegalStateException(a.x("Fragment ", this, " not attached to a host.").toString());
        }
        return object0;
    }

    public void restoreChildFragmentState(Bundle bundle0) {
        if(bundle0 != null) {
            Parcelable parcelable0 = bundle0.getParcelable("android:support:fragments");
            if(parcelable0 != null) {
                if(this.mChildFragmentManager == null) {
                    this.instantiateChildFragmentManager();
                }
                this.mChildFragmentManager.restoreAllState(parcelable0, this.mChildNonConfig);
                this.mChildNonConfig = null;
                this.mChildFragmentManager.dispatchCreate();
            }
        }
    }

    public final void restoreViewState(Bundle bundle0) {
        SparseArray sparseArray0 = this.mSavedViewState;
        if(sparseArray0 != null) {
            this.mInnerView.restoreHierarchyState(sparseArray0);
            this.mSavedViewState = null;
        }
        this.mCalled = false;
        this.onViewStateRestored(bundle0);
        if(!this.mCalled) {
            throw new SuperNotCalledException(a.x("Fragment ", this, " did not call through to super.onViewStateRestored()").toString());
        }
        if(this.mView != null) {
            this.mViewLifecycleRegistry.a(android.archv1.lifecycle.c.a.b);
        }
    }

    public void setAllowEnterTransitionOverlap(boolean z) {
        this.ensureAnimationInfo().mAllowEnterTransitionOverlap = Boolean.valueOf(z);
    }

    public void setAllowReturnTransitionOverlap(boolean z) {
        this.ensureAnimationInfo().mAllowReturnTransitionOverlap = Boolean.valueOf(z);
    }

    public void setAnimatingAway(View view0) {
        this.ensureAnimationInfo().mAnimatingAway = view0;
    }

    public void setAnimator(Animator animator0) {
        this.ensureAnimationInfo().mAnimator = animator0;
    }

    public void setArguments(Bundle bundle0) {
        if(this.mIndex >= 0 && this.isStateSaved()) {
            throw new IllegalStateException("Fragment already active and state has been saved");
        }
        this.mArguments = bundle0;
    }

    public void setEnterSharedElementCallback(SharedElementCallback sharedElementCallback0) {
        this.ensureAnimationInfo().mEnterTransitionCallback = sharedElementCallback0;
    }

    public void setEnterTransition(Object object0) {
        this.ensureAnimationInfo().mEnterTransition = object0;
    }

    public void setExitSharedElementCallback(SharedElementCallback sharedElementCallback0) {
        this.ensureAnimationInfo().mExitTransitionCallback = sharedElementCallback0;
    }

    public void setExitTransition(Object object0) {
        this.ensureAnimationInfo().mExitTransition = object0;
    }

    public void setHasOptionsMenu(boolean z) {
        if(this.mHasMenu != z) {
            this.mHasMenu = z;
            if(this.isAdded() && !this.isHidden()) {
                this.mHost.onSupportInvalidateOptionsMenu();
            }
        }
    }

    public void setHideReplaced(boolean z) {
        this.ensureAnimationInfo().mIsHideReplaced = z;
    }

    public final void setIndex(int v, Fragment fragment0) {
        String s;
        this.mIndex = v;
        StringBuilder stringBuilder0 = new StringBuilder();
        if(fragment0 == null) {
            s = "android:fragment:";
        }
        else {
            stringBuilder0.append(fragment0.mWho);
            s = ":";
        }
        stringBuilder0.append(s);
        stringBuilder0.append(this.mIndex);
        this.mWho = stringBuilder0.toString();
    }

    public void setInitialSavedState(SavedState fragment$SavedState0) {
        Bundle bundle0;
        if(this.mIndex >= 0) {
            throw new IllegalStateException("Fragment already active");
        }
        if(fragment$SavedState0 == null) {
            bundle0 = null;
        }
        else {
            bundle0 = fragment$SavedState0.mState;
            if(bundle0 == null) {
                bundle0 = null;
            }
        }
        this.mSavedFragmentState = bundle0;
    }

    public void setMenuVisibility(boolean z) {
        if(this.mMenuVisible != z) {
            this.mMenuVisible = z;
            if(this.mHasMenu && this.isAdded() && !this.isHidden()) {
                this.mHost.onSupportInvalidateOptionsMenu();
            }
        }
    }

    public void setNextAnim(int v) {
        if(this.mAnimationInfo == null && v == 0) {
            return;
        }
        this.ensureAnimationInfo().mNextAnim = v;
    }

    public void setNextTransition(int v, int v1) {
        if(this.mAnimationInfo == null && v == 0 && v1 == 0) {
            return;
        }
        this.ensureAnimationInfo();
        this.mAnimationInfo.mNextTransition = v;
        this.mAnimationInfo.mNextTransitionStyle = v1;
    }

    public void setOnStartEnterTransitionListener(OnStartEnterTransitionListener fragment$OnStartEnterTransitionListener0) {
        this.ensureAnimationInfo();
        AnimationInfo fragment$AnimationInfo0 = this.mAnimationInfo;
        OnStartEnterTransitionListener fragment$OnStartEnterTransitionListener1 = fragment$AnimationInfo0.mStartEnterTransitionListener;
        if(fragment$OnStartEnterTransitionListener0 == fragment$OnStartEnterTransitionListener1) {
            return;
        }
        if(fragment$OnStartEnterTransitionListener0 != null && fragment$OnStartEnterTransitionListener1 != null) {
            throw new IllegalStateException("Trying to set a replacement startPostponedEnterTransition on " + this);
        }
        if(fragment$AnimationInfo0.mEnterTransitionPostponed) {
            fragment$AnimationInfo0.mStartEnterTransitionListener = fragment$OnStartEnterTransitionListener0;
        }
        if(fragment$OnStartEnterTransitionListener0 != null) {
            fragment$OnStartEnterTransitionListener0.startListening();
        }
    }

    public void setReenterTransition(Object object0) {
        this.ensureAnimationInfo().mReenterTransition = object0;
    }

    public void setRetainInstance(boolean z) {
        this.mRetainInstance = z;
    }

    public void setReturnTransition(Object object0) {
        this.ensureAnimationInfo().mReturnTransition = object0;
    }

    public void setSharedElementEnterTransition(Object object0) {
        this.ensureAnimationInfo().mSharedElementEnterTransition = object0;
    }

    public void setSharedElementReturnTransition(Object object0) {
        this.ensureAnimationInfo().mSharedElementReturnTransition = object0;
    }

    public void setStateAfterAnimating(int v) {
        this.ensureAnimationInfo().mStateAfterAnimating = v;
    }

    public void setTargetFragment(Fragment fragment0, int v) {
        FragmentManager fragmentManager0 = this.getFragmentManager();
        FragmentManager fragmentManager1 = fragment0 == null ? null : fragment0.getFragmentManager();
        if(fragmentManager0 != null && fragmentManager1 != null && fragmentManager0 != fragmentManager1) {
            throw new IllegalArgumentException(a.x("Fragment ", fragment0, " must share the same FragmentManager to be set as a target fragment").toString());
        }
        for(Fragment fragment1 = fragment0; fragment1 != null; fragment1 = fragment1.getTargetFragment()) {
            if(fragment1 == this) {
                throw new IllegalArgumentException("Setting " + fragment0 + " as the target of " + this + " would create a target cycle");
            }
        }
        this.mTarget = fragment0;
        this.mTargetRequestCode = v;
    }

    public void setUserVisibleHint(boolean z) {
        if(!this.mUserVisibleHint && z && this.mState < 3 && this.mFragmentManager != null && this.isAdded() && this.mIsCreated) {
            this.mFragmentManager.performPendingDeferredStart(this);
        }
        this.mUserVisibleHint = z;
        this.mDeferStart = this.mState < 3 && !z;
        if(this.mSavedFragmentState != null) {
            this.mSavedUserVisibleHint = Boolean.valueOf(z);
        }
    }

    public boolean shouldShowRequestPermissionRationale(String s) {
        return this.mHost == null ? false : this.mHost.onShouldShowRequestPermissionRationale(s);
    }

    public void startActivity(Intent intent0) {
        this.startActivity(intent0, null);
    }

    public void startActivity(Intent intent0, Bundle bundle0) {
        FragmentHostCallback fragmentHostCallback0 = this.mHost;
        if(fragmentHostCallback0 == null) {
            throw new IllegalStateException(a.x("Fragment ", this, " not attached to Activity").toString());
        }
        fragmentHostCallback0.onStartActivityFromFragment(this, intent0, -1, bundle0);
    }

    public void startActivityForResult(Intent intent0, int v) {
        this.startActivityForResult(intent0, v, null);
    }

    public void startActivityForResult(Intent intent0, int v, Bundle bundle0) {
        FragmentHostCallback fragmentHostCallback0 = this.mHost;
        if(fragmentHostCallback0 == null) {
            throw new IllegalStateException(a.x("Fragment ", this, " not attached to Activity").toString());
        }
        fragmentHostCallback0.onStartActivityFromFragment(this, intent0, v, bundle0);
    }

    public void startIntentSenderForResult(IntentSender intentSender0, int v, Intent intent0, int v1, int v2, int v3, Bundle bundle0) throws IntentSender.SendIntentException {
        FragmentHostCallback fragmentHostCallback0 = this.mHost;
        if(fragmentHostCallback0 == null) {
            throw new IllegalStateException(a.x("Fragment ", this, " not attached to Activity").toString());
        }
        fragmentHostCallback0.onStartIntentSenderFromFragment(this, intentSender0, v, intent0, v1, v2, v3, bundle0);
    }

    public void startPostponedEnterTransition() {
        if(this.mFragmentManager != null && this.mFragmentManager.mHost != null) {
            if(Looper.myLooper() != this.mFragmentManager.mHost.getHandler().getLooper()) {
                this.mFragmentManager.mHost.getHandler().postAtFrontOfQueue(() -> {
                    AnimationInfo fragment$AnimationInfo0 = Fragment.this.mAnimationInfo;
                    OnStartEnterTransitionListener fragment$OnStartEnterTransitionListener0 = null;
                    if(fragment$AnimationInfo0 != null) {
                        fragment$AnimationInfo0.mEnterTransitionPostponed = false;
                        OnStartEnterTransitionListener fragment$OnStartEnterTransitionListener1 = fragment$AnimationInfo0.mStartEnterTransitionListener;
                        fragment$AnimationInfo0.mStartEnterTransitionListener = null;
                        fragment$OnStartEnterTransitionListener0 = fragment$OnStartEnterTransitionListener1;
                    }
                    if(fragment$OnStartEnterTransitionListener0 != null) {
                        fragment$OnStartEnterTransitionListener0.onStartEnterTransition();
                    }
                });
                return;
            }
            this.callStartTransitionListener();
            return;
        }
        this.ensureAnimationInfo().mEnterTransitionPostponed = false;

        class android.supportv1.v4.app.Fragment.1 implements Runnable {
            @Override
            public void run() {
                Fragment.this.callStartTransitionListener();
            }
        }

    }

    @Override
    public String toString() [...] // 潜在的解密器

    public void unregisterForContextMenu(View view0) {
        view0.setOnCreateContextMenuListener(null);
    }
}


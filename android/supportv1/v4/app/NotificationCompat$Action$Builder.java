package android.supportv1.v4.app;

import android.app.PendingIntent;
import android.os.Bundle;
import java.util.ArrayList;
import java.util.Arrays;

public final class NotificationCompat.Action.Builder {
    private boolean mAllowGeneratedReplies;
    private final Bundle mExtras;
    private final int mIcon;
    private final PendingIntent mIntent;
    private ArrayList mRemoteInputs;
    private int mSemanticAction;
    private boolean mShowsUserInterface;
    private final CharSequence mTitle;

    public NotificationCompat.Action.Builder(int v, CharSequence charSequence0, PendingIntent pendingIntent0) {
        this(v, charSequence0, pendingIntent0, new Bundle(), null, true, 0, true);
    }

    private NotificationCompat.Action.Builder(int v, CharSequence charSequence0, PendingIntent pendingIntent0, Bundle bundle0, RemoteInput[] arr_remoteInput, boolean z, int v1, boolean z1) {
        this.mAllowGeneratedReplies = true;
        this.mShowsUserInterface = true;
        this.mIcon = v;
        this.mTitle = Builder.limitCharSequenceLength(charSequence0);
        this.mIntent = pendingIntent0;
        this.mExtras = bundle0;
        this.mRemoteInputs = arr_remoteInput == null ? null : new ArrayList(Arrays.asList(arr_remoteInput));
        this.mAllowGeneratedReplies = z;
        this.mSemanticAction = v1;
        this.mShowsUserInterface = z1;
    }

    public NotificationCompat.Action.Builder(Action notificationCompat$Action0) {
        this(notificationCompat$Action0.icon, notificationCompat$Action0.title, notificationCompat$Action0.actionIntent, new Bundle(notificationCompat$Action0.mExtras), notificationCompat$Action0.getRemoteInputs(), notificationCompat$Action0.getAllowGeneratedReplies(), notificationCompat$Action0.getSemanticAction(), notificationCompat$Action0.mShowsUserInterface);
    }

    public NotificationCompat.Action.Builder addExtras(Bundle bundle0) {
        if(bundle0 != null) {
            this.mExtras.putAll(bundle0);
        }
        return this;
    }

    public NotificationCompat.Action.Builder addRemoteInput(RemoteInput remoteInput0) {
        if(this.mRemoteInputs == null) {
            this.mRemoteInputs = new ArrayList();
        }
        this.mRemoteInputs.add(remoteInput0);
        return this;
    }

    public Action build() {
        ArrayList arrayList0 = new ArrayList();
        ArrayList arrayList1 = new ArrayList();
        ArrayList arrayList2 = this.mRemoteInputs;
        if(arrayList2 != null) {
            for(Object object0: arrayList2) {
                RemoteInput remoteInput0 = (RemoteInput)object0;
                if(remoteInput0.isDataOnly()) {
                    arrayList0.add(remoteInput0);
                }
                else {
                    arrayList1.add(remoteInput0);
                }
            }
        }
        RemoteInput[] arr_remoteInput = arrayList0.isEmpty() ? null : ((RemoteInput[])arrayList0.toArray(new RemoteInput[arrayList0.size()]));
        if(arrayList1.isEmpty()) {
            return new Action(this.mIcon, this.mTitle, this.mIntent, this.mExtras, null, arr_remoteInput, this.mAllowGeneratedReplies, this.mSemanticAction, this.mShowsUserInterface);
        }
        RemoteInput[] arr_remoteInput1 = (RemoteInput[])arrayList1.toArray(new RemoteInput[arrayList1.size()]);
        return new Action(this.mIcon, this.mTitle, this.mIntent, this.mExtras, arr_remoteInput1, arr_remoteInput, this.mAllowGeneratedReplies, this.mSemanticAction, this.mShowsUserInterface);
    }

    public NotificationCompat.Action.Builder extend(NotificationCompat.Action.Extender notificationCompat$Action$Extender0) {
        notificationCompat$Action$Extender0.extend(this);
        return this;
    }

    public Bundle getExtras() {
        return this.mExtras;
    }

    public NotificationCompat.Action.Builder setAllowGeneratedReplies(boolean z) {
        this.mAllowGeneratedReplies = z;
        return this;
    }

    public NotificationCompat.Action.Builder setSemanticAction(int v) {
        this.mSemanticAction = v;
        return this;
    }

    public NotificationCompat.Action.Builder setShowsUserInterface(boolean z) {
        this.mShowsUserInterface = z;
        return this;
    }
}


package android.supportv1.v4.app;

import java.util.List;

public class FragmentManagerNonConfig {
    private final List mChildNonConfigs;
    private final List mFragments;
    private final List mViewModelStores;

    public FragmentManagerNonConfig(List list0, List list1, List list2) {
        this.mFragments = list0;
        this.mChildNonConfigs = list1;
        this.mViewModelStores = list2;
    }

    public List getChildNonConfigs() {
        return this.mChildNonConfigs;
    }

    public List getFragments() {
        return this.mFragments;
    }

    public List getViewModelStores() {
        return this.mViewModelStores;
    }
}


package android.supportv1.v4.app;

import android.archv1.lifecycle.e;
import android.archv1.lifecycle.j;
import android.archv1.lifecycle.k;
import android.archv1.lifecycle.n;
import android.archv1.lifecycle.o.a;
import android.archv1.lifecycle.p;
import android.os.Bundle;
import android.os.Looper;
import android.supportv1.v4.content.Loader.OnLoadCompleteListener;
import android.supportv1.v4.content.Loader;
import android.supportv1.v4.util.DebugUtils;
import android.supportv1.v4.util.SparseArrayCompat;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.lang.reflect.Modifier;
import java.util.Objects;

class LoaderManagerImpl extends LoaderManager {
    public static class LoaderInfo extends j implements OnLoadCompleteListener {
        private final Bundle mArgs;
        private final int mId;
        private e mLifecycleOwner;
        private final Loader mLoader;
        private LoaderObserver mObserver;
        private Loader mPriorLoader;

        public LoaderInfo(int v, Bundle bundle0, Loader loader0, Loader loader1) {
            this.mId = v;
            this.mArgs = bundle0;
            this.mLoader = loader0;
            this.mPriorLoader = loader1;
            loader0.registerListener(v, this);
        }

        public Loader destroy(boolean z) {
            this.mLoader.cancelLoad();
            this.mLoader.abandon();
            LoaderObserver loaderManagerImpl$LoaderObserver0 = this.mObserver;
            if(loaderManagerImpl$LoaderObserver0 != null) {
                this.removeObserver(loaderManagerImpl$LoaderObserver0);
                if(z) {
                    loaderManagerImpl$LoaderObserver0.reset();
                }
            }
            this.mLoader.unregisterListener(this);
            if(loaderManagerImpl$LoaderObserver0 != null && !loaderManagerImpl$LoaderObserver0.hasDeliveredData() || z) {
                this.mLoader.reset();
                return this.mPriorLoader;
            }
            return this.mLoader;
        }

        public void dump(String s, FileDescriptor fileDescriptor0, PrintWriter printWriter0, String[] arr_s) {
            printWriter0.print(s);
            printWriter0.print("mId=");
            printWriter0.print(this.mId);
            printWriter0.print(" mArgs=");
            printWriter0.println(this.mArgs);
            printWriter0.print(s);
            printWriter0.print("mLoader=");
            printWriter0.println(this.mLoader);
            this.mLoader.dump(s + "  ", fileDescriptor0, printWriter0, arr_s);
            if(this.mObserver != null) {
                printWriter0.print(s);
                printWriter0.print("mCallbacks=");
                printWriter0.println(this.mObserver);
                this.mObserver.dump(s + "  ", printWriter0);
            }
            printWriter0.print(s);
            printWriter0.print("mData=");
            printWriter0.println(this.getLoader().dataToString(this.getValue()));
            printWriter0.print(s);
            printWriter0.print("mStarted=");
            printWriter0.println(this.hasActiveObservers());
        }

        public Loader getLoader() {
            return this.mLoader;
        }

        // 去混淆评级： 低(20)
        public boolean isCallbackWaitingForData() {
            return this.hasActiveObservers() ? this.mObserver != null && !this.mObserver.hasDeliveredData() : false;
        }

        public void markForRedelivery() {
            e e0 = this.mLifecycleOwner;
            LoaderObserver loaderManagerImpl$LoaderObserver0 = this.mObserver;
            if(e0 != null && loaderManagerImpl$LoaderObserver0 != null) {
                super.removeObserver(loaderManagerImpl$LoaderObserver0);
                this.observe(e0, loaderManagerImpl$LoaderObserver0);
            }
        }

        @Override  // android.archv1.lifecycle.LiveData
        public void onActive() {
            this.mLoader.startLoading();
        }

        @Override  // android.archv1.lifecycle.LiveData
        public void onInactive() {
            this.mLoader.stopLoading();
        }

        @Override  // android.supportv1.v4.content.Loader$OnLoadCompleteListener
        public void onLoadComplete(Loader loader0, Object object0) {
            if(Looper.myLooper() == Looper.getMainLooper()) {
                this.setValue(object0);
                return;
            }
            this.postValue(object0);
        }

        @Override  // android.archv1.lifecycle.LiveData
        public void removeObserver(k k0) {
            super.removeObserver(k0);
            this.mLifecycleOwner = null;
            this.mObserver = null;
        }

        public Loader setCallback(e e0, LoaderCallbacks loaderManager$LoaderCallbacks0) {
            LoaderObserver loaderManagerImpl$LoaderObserver0 = new LoaderObserver(this.mLoader, loaderManager$LoaderCallbacks0);
            this.observe(e0, loaderManagerImpl$LoaderObserver0);
            LoaderObserver loaderManagerImpl$LoaderObserver1 = this.mObserver;
            if(loaderManagerImpl$LoaderObserver1 != null) {
                this.removeObserver(loaderManagerImpl$LoaderObserver1);
            }
            this.mLifecycleOwner = e0;
            this.mObserver = loaderManagerImpl$LoaderObserver0;
            return this.mLoader;
        }

        @Override  // android.archv1.lifecycle.j
        public void setValue(Object object0) {
            super.setValue(object0);
            Loader loader0 = this.mPriorLoader;
            if(loader0 != null) {
                loader0.reset();
                this.mPriorLoader = null;
            }
        }

        @Override
        public String toString() {
            StringBuilder stringBuilder0 = new StringBuilder(0x40);
            stringBuilder0.append("LoaderInfo{");
            stringBuilder0.append(Integer.toHexString(System.identityHashCode(this)));
            stringBuilder0.append(" #");
            stringBuilder0.append(this.mId);
            stringBuilder0.append(" : ");
            DebugUtils.buildShortClassTag(this.mLoader, stringBuilder0);
            stringBuilder0.append("}}");
            return stringBuilder0.toString();
        }
    }

    static class LoaderObserver implements k {
        private final LoaderCallbacks mCallback;
        private boolean mDeliveredData;
        private final Loader mLoader;

        public LoaderObserver(Loader loader0, LoaderCallbacks loaderManager$LoaderCallbacks0) {
            this.mDeliveredData = false;
            this.mLoader = loader0;
            this.mCallback = loaderManager$LoaderCallbacks0;
        }

        public void dump(String s, PrintWriter printWriter0) {
            printWriter0.print(s);
            printWriter0.print("mDeliveredData=");
            printWriter0.println(this.mDeliveredData);
        }

        public boolean hasDeliveredData() {
            return this.mDeliveredData;
        }

        @Override  // android.archv1.lifecycle.k
        public void onChanged(Object object0) {
            if(LoaderManagerImpl.DEBUG) {
                Objects.toString(this.mLoader);
                this.mLoader.dataToString(object0);
            }
            this.mCallback.onLoadFinished(this.mLoader, object0);
            this.mDeliveredData = true;
        }

        public void reset() {
            if(this.mDeliveredData) {
                if(LoaderManagerImpl.DEBUG) {
                    Objects.toString(this.mLoader);
                }
                this.mCallback.onLoaderReset(this.mLoader);
            }
        }

        @Override
        public String toString() {
            return this.mCallback.toString();
        }
    }

    static class LoaderViewModel extends n {
        private static final o.a FACTORY;
        private boolean mCreatingLoader;
        private SparseArrayCompat mLoaders;

        static {
            LoaderViewModel.FACTORY = new o.a() {
                @Override  // android.archv1.lifecycle.o$a
                public n create(Class class0) {
                    return new LoaderViewModel();
                }
            };
        }

        public LoaderViewModel() {
            this.mLoaders = new SparseArrayCompat();
            this.mCreatingLoader = false;
        }

        public void dump(String s, FileDescriptor fileDescriptor0, PrintWriter printWriter0, String[] arr_s) {
            if(this.mLoaders.size() > 0) {
                printWriter0.print(s);
                printWriter0.println("Loaders:");
                for(int v = 0; v < this.mLoaders.size(); ++v) {
                    LoaderInfo loaderManagerImpl$LoaderInfo0 = (LoaderInfo)this.mLoaders.valueAt(v);
                    printWriter0.print(s);
                    printWriter0.print("  #");
                    printWriter0.print(this.mLoaders.keyAt(v));
                    printWriter0.print(": ");
                    printWriter0.println(loaderManagerImpl$LoaderInfo0.toString());
                    loaderManagerImpl$LoaderInfo0.dump(s + "    ", fileDescriptor0, printWriter0, arr_s);
                }
            }
        }

        public void finishCreatingLoader() {
            this.mCreatingLoader = false;
        }

        public static LoaderViewModel getInstance(p p0) {
            o.a o$a0 = LoaderViewModel.FACTORY;
            Class class0 = LoaderViewModel.class;
            String s = class0.getCanonicalName();
            if(s == null) {
                throw new IllegalArgumentException("Local and anonymous classes can not be ViewModels");
            }
            n n0 = (n)p0.a.get("android.archv1.lifecycle.ViewModelProvider.DefaultKey:" + s);
            if(!class0.isInstance(n0)) {
                n0 = o$a0.create(class0);
                n n1 = (n)p0.a.put("android.archv1.lifecycle.ViewModelProvider.DefaultKey:" + s, n0);
                if(n1 != null) {
                    n1.onCleared();
                }
            }
            return (LoaderViewModel)n0;
        }

        public LoaderInfo getLoader(int v) {
            return (LoaderInfo)this.mLoaders.get(v);
        }

        public boolean hasRunningLoaders() {
            int v = this.mLoaders.size();
            for(int v1 = 0; v1 < v; ++v1) {
                if(((LoaderInfo)this.mLoaders.valueAt(v1)).isCallbackWaitingForData()) {
                    return true;
                }
            }
            return false;
        }

        public boolean isCreatingLoader() {
            return this.mCreatingLoader;
        }

        public void markForRedelivery() {
            int v = this.mLoaders.size();
            for(int v1 = 0; v1 < v; ++v1) {
                ((LoaderInfo)this.mLoaders.valueAt(v1)).markForRedelivery();
            }
        }

        @Override  // android.archv1.lifecycle.n
        public void onCleared() {
            int v = this.mLoaders.size();
            for(int v1 = 0; v1 < v; ++v1) {
                ((LoaderInfo)this.mLoaders.valueAt(v1)).destroy(true);
            }
            this.mLoaders.clear();
        }

        public void putLoader(int v, LoaderInfo loaderManagerImpl$LoaderInfo0) {
            this.mLoaders.put(v, loaderManagerImpl$LoaderInfo0);
        }

        public void removeLoader(int v) {
            this.mLoaders.remove(v);
        }

        public void startCreatingLoader() {
            this.mCreatingLoader = true;
        }
    }

    static boolean DEBUG = false;
    static final String TAG = "LoaderManager";
    private final e mLifecycleOwner;
    private final LoaderViewModel mLoaderViewModel;

    public LoaderManagerImpl(e e0, p p0) {
        this.mLifecycleOwner = e0;
        this.mLoaderViewModel = LoaderViewModel.getInstance(p0);
    }

    private Loader createAndInstallLoader(int v, Bundle bundle0, LoaderCallbacks loaderManager$LoaderCallbacks0, Loader loader0) {
        try {
            this.mLoaderViewModel.startCreatingLoader();
            Loader loader1 = loaderManager$LoaderCallbacks0.onCreateLoader(v, bundle0);
            if(loader1 != null) {
                if(loader1.getClass().isMemberClass() && !Modifier.isStatic(loader1.getClass().getModifiers())) {
                    throw new IllegalArgumentException("Object returned from onCreateLoader must not be a non-static inner member class: " + loader1);
                }
                LoaderInfo loaderManagerImpl$LoaderInfo0 = new LoaderInfo(v, bundle0, loader1, loader0);
                this.mLoaderViewModel.putLoader(v, loaderManagerImpl$LoaderInfo0);
                return loaderManagerImpl$LoaderInfo0.setCallback(this.mLifecycleOwner, loaderManager$LoaderCallbacks0);
            }
        }
        finally {
            this.mLoaderViewModel.finishCreatingLoader();
        }
        throw new IllegalArgumentException("Object returned from onCreateLoader must not be null");
    }

    @Override  // android.supportv1.v4.app.LoaderManager
    public void destroyLoader(int v) {
        if(this.mLoaderViewModel.isCreatingLoader()) {
            throw new IllegalStateException("Called while creating a loader");
        }
        if(Looper.getMainLooper() != Looper.myLooper()) {
            throw new IllegalStateException("destroyLoader must be called on the main thread");
        }
        LoaderInfo loaderManagerImpl$LoaderInfo0 = this.mLoaderViewModel.getLoader(v);
        if(loaderManagerImpl$LoaderInfo0 != null) {
            loaderManagerImpl$LoaderInfo0.destroy(true);
            this.mLoaderViewModel.removeLoader(v);
        }
    }

    @Override  // android.supportv1.v4.app.LoaderManager
    @Deprecated
    public void dump(String s, FileDescriptor fileDescriptor0, PrintWriter printWriter0, String[] arr_s) {
        this.mLoaderViewModel.dump(s, fileDescriptor0, printWriter0, arr_s);
    }

    @Override  // android.supportv1.v4.app.LoaderManager
    public Loader getLoader(int v) {
        if(this.mLoaderViewModel.isCreatingLoader()) {
            throw new IllegalStateException("Called while creating a loader");
        }
        LoaderInfo loaderManagerImpl$LoaderInfo0 = this.mLoaderViewModel.getLoader(v);
        return loaderManagerImpl$LoaderInfo0 == null ? null : loaderManagerImpl$LoaderInfo0.getLoader();
    }

    @Override  // android.supportv1.v4.app.LoaderManager
    public boolean hasRunningLoaders() {
        return this.mLoaderViewModel.hasRunningLoaders();
    }

    @Override  // android.supportv1.v4.app.LoaderManager
    public Loader initLoader(int v, Bundle bundle0, LoaderCallbacks loaderManager$LoaderCallbacks0) {
        if(this.mLoaderViewModel.isCreatingLoader()) {
            throw new IllegalStateException("Called while creating a loader");
        }
        if(Looper.getMainLooper() != Looper.myLooper()) {
            throw new IllegalStateException("initLoader must be called on the main thread");
        }
        LoaderInfo loaderManagerImpl$LoaderInfo0 = this.mLoaderViewModel.getLoader(v);
        if(LoaderManagerImpl.DEBUG) {
            Objects.toString(bundle0);
        }
        if(loaderManagerImpl$LoaderInfo0 == null) {
            return this.createAndInstallLoader(v, bundle0, loaderManager$LoaderCallbacks0, null);
        }
        if(LoaderManagerImpl.DEBUG) {
            loaderManagerImpl$LoaderInfo0.toString();
        }
        return loaderManagerImpl$LoaderInfo0.setCallback(this.mLifecycleOwner, loaderManager$LoaderCallbacks0);
    }

    @Override  // android.supportv1.v4.app.LoaderManager
    public void markForRedelivery() {
        this.mLoaderViewModel.markForRedelivery();
    }

    @Override  // android.supportv1.v4.app.LoaderManager
    public Loader restartLoader(int v, Bundle bundle0, LoaderCallbacks loaderManager$LoaderCallbacks0) {
        if(this.mLoaderViewModel.isCreatingLoader()) {
            throw new IllegalStateException("Called while creating a loader");
        }
        if(Looper.getMainLooper() != Looper.myLooper()) {
            throw new IllegalStateException("restartLoader must be called on the main thread");
        }
        if(LoaderManagerImpl.DEBUG) {
            Objects.toString(bundle0);
        }
        LoaderInfo loaderManagerImpl$LoaderInfo0 = this.mLoaderViewModel.getLoader(v);
        return loaderManagerImpl$LoaderInfo0 == null ? this.createAndInstallLoader(v, bundle0, loaderManager$LoaderCallbacks0, null) : this.createAndInstallLoader(v, bundle0, loaderManager$LoaderCallbacks0, loaderManagerImpl$LoaderInfo0.destroy(false));
    }

    @Override
    public String toString() {
        StringBuilder stringBuilder0 = new StringBuilder(0x80);
        stringBuilder0.append("LoaderManager{");
        stringBuilder0.append(Integer.toHexString(System.identityHashCode(this)));
        stringBuilder0.append(" in ");
        DebugUtils.buildShortClassTag(this.mLifecycleOwner, stringBuilder0);
        stringBuilder0.append("}}");
        return stringBuilder0.toString();
    }
}


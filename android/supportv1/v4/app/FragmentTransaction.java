package android.supportv1.v4.app;

import android.view.View;

public abstract class FragmentTransaction {
    public static final int TRANSIT_ENTER_MASK = 0x1000;
    public static final int TRANSIT_EXIT_MASK = 0x2000;
    public static final int TRANSIT_FRAGMENT_CLOSE = 0x2002;
    public static final int TRANSIT_FRAGMENT_FADE = 0x1003;
    public static final int TRANSIT_FRAGMENT_OPEN = 0x1001;
    public static final int TRANSIT_NONE = 0;
    public static final int TRANSIT_UNSET = -1;

    public abstract FragmentTransaction add(int arg1, Fragment arg2);

    public abstract FragmentTransaction add(int arg1, Fragment arg2, String arg3);

    public abstract FragmentTransaction add(Fragment arg1, String arg2);

    public abstract FragmentTransaction addSharedElement(View arg1, String arg2);

    public abstract FragmentTransaction addToBackStack(String arg1);

    public abstract FragmentTransaction attach(Fragment arg1);

    public abstract int commit();

    public abstract int commitAllowingStateLoss();

    public abstract void commitNow();

    public abstract void commitNowAllowingStateLoss();

    public abstract FragmentTransaction detach(Fragment arg1);

    public abstract FragmentTransaction disallowAddToBackStack();

    public abstract FragmentTransaction hide(Fragment arg1);

    public abstract boolean isAddToBackStackAllowed();

    public abstract boolean isEmpty();

    public abstract FragmentTransaction remove(Fragment arg1);

    public abstract FragmentTransaction replace(int arg1, Fragment arg2);

    public abstract FragmentTransaction replace(int arg1, Fragment arg2, String arg3);

    public abstract FragmentTransaction runOnCommit(Runnable arg1);

    @Deprecated
    public abstract FragmentTransaction setAllowOptimization(boolean arg1);

    public abstract FragmentTransaction setBreadCrumbShortTitle(int arg1);

    public abstract FragmentTransaction setBreadCrumbShortTitle(CharSequence arg1);

    public abstract FragmentTransaction setBreadCrumbTitle(int arg1);

    public abstract FragmentTransaction setBreadCrumbTitle(CharSequence arg1);

    public abstract FragmentTransaction setCustomAnimations(int arg1, int arg2);

    public abstract FragmentTransaction setCustomAnimations(int arg1, int arg2, int arg3, int arg4);

    public abstract FragmentTransaction setPrimaryNavigationFragment(Fragment arg1);

    public abstract FragmentTransaction setReorderingAllowed(boolean arg1);

    public abstract FragmentTransaction setTransition(int arg1);

    public abstract FragmentTransaction setTransitionStyle(int arg1);

    public abstract FragmentTransaction show(Fragment arg1);
}


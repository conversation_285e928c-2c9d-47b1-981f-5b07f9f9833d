package android.supportv1.v4.app;

import android.app.PendingIntent;
import java.util.ArrayList;
import java.util.List;

public class NotificationCompat.CarExtender.UnreadConversation.Builder {
    private long mLatestTimestamp;
    private final List mMessages;
    private final String mParticipant;
    private PendingIntent mReadPendingIntent;
    private RemoteInput mRemoteInput;
    private PendingIntent mReplyPendingIntent;

    public NotificationCompat.CarExtender.UnreadConversation.Builder(String s) {
        this.mMessages = new ArrayList();
        this.mParticipant = s;
    }

    public NotificationCompat.CarExtender.UnreadConversation.Builder addMessage(String s) {
        this.mMessages.add(s);
        return this;
    }

    public NotificationCompat.CarExtender.UnreadConversation build() {
        return new NotificationCompat.CarExtender.UnreadConversation(((String[])this.mMessages.toArray(new String[this.mMessages.size()])), this.mRemoteInput, this.mReplyPendingIntent, this.mReadPendingIntent, new String[]{this.mParticipant}, this.mLatestTimestamp);
    }

    public NotificationCompat.CarExtender.UnreadConversation.Builder setLatestTimestamp(long v) {
        this.mLatestTimestamp = v;
        return this;
    }

    public NotificationCompat.CarExtender.UnreadConversation.Builder setReadPendingIntent(PendingIntent pendingIntent0) {
        this.mReadPendingIntent = pendingIntent0;
        return this;
    }

    public NotificationCompat.CarExtender.UnreadConversation.Builder setReplyAction(PendingIntent pendingIntent0, RemoteInput remoteInput0) {
        this.mRemoteInput = remoteInput0;
        this.mReplyPendingIntent = pendingIntent0;
        return this;
    }
}


package android.supportv1.v4.app;

import a3.a;
import android.app.Notification.Action.Builder;
import android.app.Notification.Action;
import android.app.Notification.Builder;
import android.app.Notification;
import android.app.PendingIntent;
import android.app.RemoteInput;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.RemoteViews;
import java.util.ArrayList;
import java.util.List;

class NotificationCompatBuilder implements NotificationBuilderWithBuilderAccessor {
    private final List mActionExtrasList;
    private RemoteViews mBigContentView;
    private final Notification.Builder mBuilder;
    private final Builder mBuilderCompat;
    private RemoteViews mContentView;
    private final Bundle mExtras;
    private int mGroupAlertBehavior;
    private RemoteViews mHeadsUpContentView;

    public NotificationCompatBuilder(Builder notificationCompat$Builder0) {
        this.mActionExtrasList = new ArrayList();
        this.mExtras = new Bundle();
        this.mBuilderCompat = notificationCompat$Builder0;
        Notification.Builder notification$Builder0 = Build.VERSION.SDK_INT < 26 ? new Notification.Builder(notificationCompat$Builder0.mContext) : c.g(notificationCompat$Builder0.mContext, notificationCompat$Builder0.mChannelId);
        this.mBuilder = notification$Builder0;
        Notification notification0 = notificationCompat$Builder0.mNotification;
        boolean z = true;
        Notification.Builder notification$Builder1 = notification$Builder0.setWhen(notification0.when).setSmallIcon(notification0.icon, notification0.iconLevel).setContent(notification0.contentView).setTicker(notification0.tickerText, notificationCompat$Builder0.mTickerView).setVibrate(notification0.vibrate).setLights(notification0.ledARGB, notification0.ledOnMS, notification0.ledOffMS).setOngoing((notification0.flags & 2) != 0).setOnlyAlertOnce((notification0.flags & 8) != 0).setAutoCancel((notification0.flags & 16) != 0).setDefaults(notification0.defaults).setContentTitle(notificationCompat$Builder0.mContentTitle).setContentText(notificationCompat$Builder0.mContentText).setContentInfo(notificationCompat$Builder0.mContentInfo).setContentIntent(notificationCompat$Builder0.mContentIntent).setDeleteIntent(notification0.deleteIntent);
        PendingIntent pendingIntent0 = notificationCompat$Builder0.mFullScreenIntent;
        if((notification0.flags & 0x80) == 0) {
            z = false;
        }
        notification$Builder1.setFullScreenIntent(pendingIntent0, z).setLargeIcon(notificationCompat$Builder0.mLargeIcon).setNumber(notificationCompat$Builder0.mNumber).setProgress(notificationCompat$Builder0.mProgressMax, notificationCompat$Builder0.mProgress, notificationCompat$Builder0.mProgressIndeterminate);
        notification$Builder0.setSubText(notificationCompat$Builder0.mSubText).setUsesChronometer(notificationCompat$Builder0.mUseChronometer).setPriority(notificationCompat$Builder0.mPriority);
        for(Object object0: notificationCompat$Builder0.mActions) {
            this.addAction(((Action)object0));
        }
        Bundle bundle0 = notificationCompat$Builder0.mExtras;
        if(bundle0 != null) {
            this.mExtras.putAll(bundle0);
        }
        this.mContentView = notificationCompat$Builder0.mContentView;
        this.mBigContentView = notificationCompat$Builder0.mBigContentView;
        this.mBuilder.setShowWhen(notificationCompat$Builder0.mShowWhen);
        this.mBuilder.setLocalOnly(notificationCompat$Builder0.mLocalOnly).setGroup(notificationCompat$Builder0.mGroupKey).setGroupSummary(notificationCompat$Builder0.mGroupSummary).setSortKey(notificationCompat$Builder0.mSortKey);
        this.mGroupAlertBehavior = notificationCompat$Builder0.mGroupAlertBehavior;
        this.mBuilder.setCategory(notificationCompat$Builder0.mCategory).setColor(notificationCompat$Builder0.mColor).setVisibility(notificationCompat$Builder0.mVisibility).setPublicVersion(notificationCompat$Builder0.mPublicVersion).setSound(notification0.sound, notification0.audioAttributes);
        for(Object object1: notificationCompat$Builder0.mPeople) {
            this.mBuilder.addPerson(((String)object1));
        }
        this.mHeadsUpContentView = notificationCompat$Builder0.mHeadsUpContentView;
        if(notificationCompat$Builder0.mInvisibleActions.size() > 0) {
            Bundle bundle1 = notificationCompat$Builder0.getExtras().getBundle("android.car.EXTENSIONS");
            if(bundle1 == null) {
                bundle1 = new Bundle();
            }
            Bundle bundle2 = new Bundle();
            for(int v = 0; v < notificationCompat$Builder0.mInvisibleActions.size(); ++v) {
                bundle2.putBundle(Integer.toString(v), NotificationCompatJellybean.getBundleForAction(((Action)notificationCompat$Builder0.mInvisibleActions.get(v))));
            }
            bundle1.putBundle("invisible_actions", bundle2);
            notificationCompat$Builder0.getExtras().putBundle("android.car.EXTENSIONS", bundle1);
            this.mExtras.putBundle("android.car.EXTENSIONS", bundle1);
        }
        int v1 = Build.VERSION.SDK_INT;
        if(v1 >= 24) {
            a.l(this.mBuilder.setExtras(notificationCompat$Builder0.mExtras), notificationCompat$Builder0.mRemoteInputHistory);
            RemoteViews remoteViews0 = notificationCompat$Builder0.mContentView;
            if(remoteViews0 != null) {
                a.k(this.mBuilder, remoteViews0);
            }
            RemoteViews remoteViews1 = notificationCompat$Builder0.mBigContentView;
            if(remoteViews1 != null) {
                a.v(this.mBuilder, remoteViews1);
            }
            RemoteViews remoteViews2 = notificationCompat$Builder0.mHeadsUpContentView;
            if(remoteViews2 != null) {
                a.x(this.mBuilder, remoteViews2);
            }
        }
        if(v1 >= 26) {
            this.mBuilder.setBadgeIconType(notificationCompat$Builder0.mBadgeIcon).setShortcutId(notificationCompat$Builder0.mShortcutId).setTimeoutAfter(notificationCompat$Builder0.mTimeout).setGroupAlertBehavior(notificationCompat$Builder0.mGroupAlertBehavior);
            if(notificationCompat$Builder0.mColorizedSet) {
                this.mBuilder.setColorized(notificationCompat$Builder0.mColorized);
            }
            if(!TextUtils.isEmpty(notificationCompat$Builder0.mChannelId)) {
                this.mBuilder.setSound(null).setDefaults(0).setLights(0, 0, 0).setVibrate(null);
            }
        }
    }

    private void addAction(Action notificationCompat$Action0) {
        Notification.Action.Builder notification$Action$Builder0 = new Notification.Action.Builder(notificationCompat$Action0.getIcon(), notificationCompat$Action0.getTitle(), notificationCompat$Action0.getActionIntent());
        if(notificationCompat$Action0.getRemoteInputs() != null) {
            RemoteInput[] arr_remoteInput = android.supportv1.v4.app.RemoteInput.fromCompat(notificationCompat$Action0.getRemoteInputs());
            for(int v = 0; v < arr_remoteInput.length; ++v) {
                notification$Action$Builder0.addRemoteInput(arr_remoteInput[v]);
            }
        }
        Bundle bundle0 = notificationCompat$Action0.getExtras() == null ? new Bundle() : new Bundle(notificationCompat$Action0.getExtras());
        bundle0.putBoolean("android.supportv1.allowGeneratedReplies", notificationCompat$Action0.getAllowGeneratedReplies());
        int v1 = Build.VERSION.SDK_INT;
        if(v1 >= 24) {
            notification$Action$Builder0.setAllowGeneratedReplies(notificationCompat$Action0.getAllowGeneratedReplies());
        }
        bundle0.putInt("android.supportv1.action.semanticAction", notificationCompat$Action0.getSemanticAction());
        if(v1 >= 28) {
            notification$Action$Builder0.setSemanticAction(notificationCompat$Action0.getSemanticAction());
        }
        bundle0.putBoolean("android.supportv1.action.showsUserInterface", notificationCompat$Action0.getShowsUserInterface());
        notification$Action$Builder0.addExtras(bundle0);
        Notification.Action notification$Action0 = notification$Action$Builder0.build();
        this.mBuilder.addAction(notification$Action0);
    }

    public Notification build() {
        Style notificationCompat$Style0 = this.mBuilderCompat.mStyle;
        if(notificationCompat$Style0 != null) {
            notificationCompat$Style0.apply(this);
        }
        RemoteViews remoteViews0 = notificationCompat$Style0 == null ? null : notificationCompat$Style0.makeContentView(this);
        Notification notification0 = this.buildInternal();
        if(remoteViews0 == null) {
            remoteViews0 = this.mBuilderCompat.mContentView;
            if(remoteViews0 != null) {
                notification0.contentView = remoteViews0;
            }
        }
        else {
            notification0.contentView = remoteViews0;
        }
        if(notificationCompat$Style0 != null) {
            RemoteViews remoteViews1 = notificationCompat$Style0.makeBigContentView(this);
            if(remoteViews1 != null) {
                notification0.bigContentView = remoteViews1;
            }
        }
        if(notificationCompat$Style0 != null) {
            RemoteViews remoteViews2 = this.mBuilderCompat.mStyle.makeHeadsUpContentView(this);
            if(remoteViews2 != null) {
                notification0.headsUpContentView = remoteViews2;
            }
        }
        if(notificationCompat$Style0 != null) {
            Bundle bundle0 = NotificationCompat.getExtras(notification0);
            if(bundle0 != null) {
                notificationCompat$Style0.addCompatExtras(bundle0);
            }
        }
        return notification0;
    }

    public Notification buildInternal() {
        int v = Build.VERSION.SDK_INT;
        if(v >= 26) {
            return this.mBuilder.build();
        }
        if(v >= 24) {
            Notification notification0 = this.mBuilder.build();
            if(this.mGroupAlertBehavior != 0) {
                if(notification0.getGroup() != null && (notification0.flags & 0x200) != 0 && this.mGroupAlertBehavior == 2) {
                    this.removeSoundAndVibration(notification0);
                }
                if(notification0.getGroup() != null && (notification0.flags & 0x200) == 0 && this.mGroupAlertBehavior == 1) {
                    this.removeSoundAndVibration(notification0);
                }
            }
            return notification0;
        }
        this.mBuilder.setExtras(this.mExtras);
        Notification notification1 = this.mBuilder.build();
        RemoteViews remoteViews0 = this.mContentView;
        if(remoteViews0 != null) {
            notification1.contentView = remoteViews0;
        }
        RemoteViews remoteViews1 = this.mBigContentView;
        if(remoteViews1 != null) {
            notification1.bigContentView = remoteViews1;
        }
        RemoteViews remoteViews2 = this.mHeadsUpContentView;
        if(remoteViews2 != null) {
            notification1.headsUpContentView = remoteViews2;
        }
        if(this.mGroupAlertBehavior != 0) {
            if(notification1.getGroup() != null && (notification1.flags & 0x200) != 0 && this.mGroupAlertBehavior == 2) {
                this.removeSoundAndVibration(notification1);
            }
            if(notification1.getGroup() != null && (notification1.flags & 0x200) == 0 && this.mGroupAlertBehavior == 1) {
                this.removeSoundAndVibration(notification1);
            }
        }
        return notification1;
    }

    @Override  // android.supportv1.v4.app.NotificationBuilderWithBuilderAccessor
    public Notification.Builder getBuilder() {
        return this.mBuilder;
    }

    private void removeSoundAndVibration(Notification notification0) {
        notification0.sound = null;
        notification0.vibrate = null;
        notification0.defaults &= -4;
    }
}


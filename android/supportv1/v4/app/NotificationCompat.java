package android.supportv1.v4.app;

import a3.a;
import android.app.Notification.Action.Builder;
import android.app.Notification.Action;
import android.app.Notification.BigPictureStyle;
import android.app.Notification.BigTextStyle;
import android.app.Notification.InboxStyle;
import android.app.Notification.MessagingStyle.Message;
import android.app.Notification.MessagingStyle;
import android.app.Notification;
import android.app.PendingIntent;
import android.app.RemoteInput.Builder;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.Bitmap.Config;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.media.AudioAttributes.Builder;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Parcelable;
import android.os.SystemClock;
import android.supportv1.v4.text.BidiFormatter;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.TextAppearanceSpan;
import android.widget.RemoteViews;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class NotificationCompat {
    public static class Action {
        static final String EXTRA_SEMANTIC_ACTION = "android.supportv1.action.semanticAction";
        static final String EXTRA_SHOWS_USER_INTERFACE = "android.supportv1.action.showsUserInterface";
        public static final int SEMANTIC_ACTION_ARCHIVE = 5;
        public static final int SEMANTIC_ACTION_CALL = 10;
        public static final int SEMANTIC_ACTION_DELETE = 4;
        public static final int SEMANTIC_ACTION_MARK_AS_READ = 2;
        public static final int SEMANTIC_ACTION_MARK_AS_UNREAD = 3;
        public static final int SEMANTIC_ACTION_MUTE = 6;
        public static final int SEMANTIC_ACTION_NONE = 0;
        public static final int SEMANTIC_ACTION_REPLY = 1;
        public static final int SEMANTIC_ACTION_THUMBS_DOWN = 9;
        public static final int SEMANTIC_ACTION_THUMBS_UP = 8;
        public static final int SEMANTIC_ACTION_UNMUTE = 7;
        public PendingIntent actionIntent;
        public int icon;
        private boolean mAllowGeneratedReplies;
        private final RemoteInput[] mDataOnlyRemoteInputs;
        final Bundle mExtras;
        private final RemoteInput[] mRemoteInputs;
        private final int mSemanticAction;
        boolean mShowsUserInterface;
        public CharSequence title;

        public Action(int v, CharSequence charSequence0, PendingIntent pendingIntent0) {
            this(v, charSequence0, pendingIntent0, new Bundle(), null, null, true, 0, true);
        }

        public Action(int v, CharSequence charSequence0, PendingIntent pendingIntent0, Bundle bundle0, RemoteInput[] arr_remoteInput, RemoteInput[] arr_remoteInput1, boolean z, int v1, boolean z1) {
            this.mShowsUserInterface = true;
            this.icon = v;
            this.title = Builder.limitCharSequenceLength(charSequence0);
            this.actionIntent = pendingIntent0;
            if(bundle0 == null) {
                bundle0 = new Bundle();
            }
            this.mExtras = bundle0;
            this.mRemoteInputs = arr_remoteInput;
            this.mDataOnlyRemoteInputs = arr_remoteInput1;
            this.mAllowGeneratedReplies = z;
            this.mSemanticAction = v1;
            this.mShowsUserInterface = z1;
        }

        public PendingIntent getActionIntent() {
            return this.actionIntent;
        }

        public boolean getAllowGeneratedReplies() {
            return this.mAllowGeneratedReplies;
        }

        public RemoteInput[] getDataOnlyRemoteInputs() {
            return this.mDataOnlyRemoteInputs;
        }

        public Bundle getExtras() {
            return this.mExtras;
        }

        public int getIcon() {
            return this.icon;
        }

        public RemoteInput[] getRemoteInputs() {
            return this.mRemoteInputs;
        }

        public int getSemanticAction() {
            return this.mSemanticAction;
        }

        public boolean getShowsUserInterface() {
            return this.mShowsUserInterface;
        }

        public CharSequence getTitle() {
            return this.title;
        }
    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface BadgeIconType {
    }

    public static class BigPictureStyle extends Style {
        private Bitmap mBigLargeIcon;
        private boolean mBigLargeIconSet;
        private Bitmap mPicture;

        public BigPictureStyle() {
        }

        public BigPictureStyle(Builder notificationCompat$Builder0) {
            this.setBuilder(notificationCompat$Builder0);
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public void apply(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            Notification.BigPictureStyle notification$BigPictureStyle0 = new Notification.BigPictureStyle(notificationBuilderWithBuilderAccessor0.getBuilder()).setBigContentTitle(this.mBigContentTitle).bigPicture(this.mPicture);
            if(this.mBigLargeIconSet) {
                notification$BigPictureStyle0.bigLargeIcon(this.mBigLargeIcon);
            }
            if(this.mSummaryTextSet) {
                notification$BigPictureStyle0.setSummaryText(this.mSummaryText);
            }
        }

        public BigPictureStyle bigLargeIcon(Bitmap bitmap0) {
            this.mBigLargeIcon = bitmap0;
            this.mBigLargeIconSet = true;
            return this;
        }

        public BigPictureStyle bigPicture(Bitmap bitmap0) {
            this.mPicture = bitmap0;
            return this;
        }

        public BigPictureStyle setBigContentTitle(CharSequence charSequence0) {
            this.mBigContentTitle = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public BigPictureStyle setSummaryText(CharSequence charSequence0) {
            this.mSummaryText = Builder.limitCharSequenceLength(charSequence0);
            this.mSummaryTextSet = true;
            return this;
        }
    }

    public static class BigTextStyle extends Style {
        private CharSequence mBigText;

        public BigTextStyle() {
        }

        public BigTextStyle(Builder notificationCompat$Builder0) {
            this.setBuilder(notificationCompat$Builder0);
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public void apply(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            Notification.BigTextStyle notification$BigTextStyle0 = new Notification.BigTextStyle(notificationBuilderWithBuilderAccessor0.getBuilder()).setBigContentTitle(this.mBigContentTitle).bigText(this.mBigText);
            if(this.mSummaryTextSet) {
                notification$BigTextStyle0.setSummaryText(this.mSummaryText);
            }
        }

        public BigTextStyle bigText(CharSequence charSequence0) {
            this.mBigText = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public BigTextStyle setBigContentTitle(CharSequence charSequence0) {
            this.mBigContentTitle = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public BigTextStyle setSummaryText(CharSequence charSequence0) {
            this.mSummaryText = Builder.limitCharSequenceLength(charSequence0);
            this.mSummaryTextSet = true;
            return this;
        }
    }

    public static class Builder {
        private static final int MAX_CHARSEQUENCE_LENGTH = 0x1400;
        public ArrayList mActions;
        int mBadgeIcon;
        RemoteViews mBigContentView;
        String mCategory;
        String mChannelId;
        int mColor;
        boolean mColorized;
        boolean mColorizedSet;
        CharSequence mContentInfo;
        PendingIntent mContentIntent;
        CharSequence mContentText;
        CharSequence mContentTitle;
        RemoteViews mContentView;
        public Context mContext;
        Bundle mExtras;
        PendingIntent mFullScreenIntent;
        int mGroupAlertBehavior;
        String mGroupKey;
        boolean mGroupSummary;
        RemoteViews mHeadsUpContentView;
        ArrayList mInvisibleActions;
        Bitmap mLargeIcon;
        boolean mLocalOnly;
        Notification mNotification;
        int mNumber;
        @Deprecated
        public ArrayList mPeople;
        int mPriority;
        int mProgress;
        boolean mProgressIndeterminate;
        int mProgressMax;
        Notification mPublicVersion;
        CharSequence[] mRemoteInputHistory;
        String mShortcutId;
        boolean mShowWhen;
        String mSortKey;
        Style mStyle;
        CharSequence mSubText;
        RemoteViews mTickerView;
        long mTimeout;
        boolean mUseChronometer;
        int mVisibility;

        @Deprecated
        public Builder(Context context0) {
            this(context0, null);
        }

        public Builder(Context context0, String s) {
            this.mActions = new ArrayList();
            this.mInvisibleActions = new ArrayList();
            this.mShowWhen = true;
            this.mLocalOnly = false;
            this.mColor = 0;
            this.mVisibility = 0;
            this.mBadgeIcon = 0;
            this.mGroupAlertBehavior = 0;
            Notification notification0 = new Notification();
            this.mNotification = notification0;
            this.mContext = context0;
            this.mChannelId = s;
            notification0.when = System.currentTimeMillis();
            this.mNotification.audioStreamType = -1;
            this.mPriority = 0;
            this.mPeople = new ArrayList();
        }

        public Builder addAction(int v, CharSequence charSequence0, PendingIntent pendingIntent0) {
            this.mActions.add(new Action(v, charSequence0, pendingIntent0));
            return this;
        }

        public Builder addAction(Action notificationCompat$Action0) {
            this.mActions.add(notificationCompat$Action0);
            return this;
        }

        public Builder addExtras(Bundle bundle0) {
            if(bundle0 != null) {
                Bundle bundle1 = this.mExtras;
                if(bundle1 == null) {
                    this.mExtras = new Bundle(bundle0);
                    return this;
                }
                bundle1.putAll(bundle0);
            }
            return this;
        }

        public Builder addInvisibleAction(int v, CharSequence charSequence0, PendingIntent pendingIntent0) {
            return this.addInvisibleAction(new Action(v, charSequence0, pendingIntent0));
        }

        public Builder addInvisibleAction(Action notificationCompat$Action0) {
            this.mInvisibleActions.add(notificationCompat$Action0);
            return this;
        }

        public Builder addPerson(String s) {
            this.mPeople.add(s);
            return this;
        }

        public Notification build() {
            return new NotificationCompatBuilder(this).build();
        }

        public Builder extend(Extender notificationCompat$Extender0) {
            notificationCompat$Extender0.extend(this);
            return this;
        }

        public RemoteViews getBigContentView() {
            return this.mBigContentView;
        }

        public int getColor() {
            return this.mColor;
        }

        public RemoteViews getContentView() {
            return this.mContentView;
        }

        public Bundle getExtras() {
            if(this.mExtras == null) {
                this.mExtras = new Bundle();
            }
            return this.mExtras;
        }

        public RemoteViews getHeadsUpContentView() {
            return this.mHeadsUpContentView;
        }

        @Deprecated
        public Notification getNotification() {
            return this.build();
        }

        public int getPriority() {
            return this.mPriority;
        }

        // 去混淆评级： 低(20)
        public long getWhenIfShowing() {
            return this.mShowWhen ? this.mNotification.when : 0L;
        }

        public static CharSequence limitCharSequenceLength(CharSequence charSequence0) {
            if(charSequence0 == null) {
                return null;
            }
            return charSequence0.length() <= 0x1400 ? charSequence0 : charSequence0.subSequence(0, 0x1400);
        }

        private Bitmap reduceLargeIconSize(Bitmap bitmap0) {
            if(bitmap0 != null && Build.VERSION.SDK_INT < 27) {
                Resources resources0 = this.mContext.getResources();
                int v = resources0.getDimensionPixelSize(0x7F070063);  // dimen:_182sdp
                int v1 = resources0.getDimensionPixelSize(0x7F070062);  // dimen:_181sdp
                if(bitmap0.getWidth() <= v && bitmap0.getHeight() <= v1) {
                    return bitmap0;
                }
                double f = Math.min(((double)v) / ((double)Math.max(1, bitmap0.getWidth())), ((double)v1) / ((double)Math.max(1, bitmap0.getHeight())));
                return Bitmap.createScaledBitmap(bitmap0, ((int)Math.ceil(((double)bitmap0.getWidth()) * f)), ((int)Math.ceil(((double)bitmap0.getHeight()) * f)), true);
            }
            return bitmap0;
        }

        public Builder setAutoCancel(boolean z) {
            this.setFlag(16, z);
            return this;
        }

        public Builder setBadgeIconType(int v) {
            this.mBadgeIcon = v;
            return this;
        }

        public Builder setCategory(String s) {
            this.mCategory = s;
            return this;
        }

        public Builder setChannelId(String s) {
            this.mChannelId = s;
            return this;
        }

        public Builder setColor(int v) {
            this.mColor = v;
            return this;
        }

        public Builder setColorized(boolean z) {
            this.mColorized = z;
            this.mColorizedSet = true;
            return this;
        }

        public Builder setContent(RemoteViews remoteViews0) {
            this.mNotification.contentView = remoteViews0;
            return this;
        }

        public Builder setContentInfo(CharSequence charSequence0) {
            this.mContentInfo = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public Builder setContentIntent(PendingIntent pendingIntent0) {
            this.mContentIntent = pendingIntent0;
            return this;
        }

        public Builder setContentText(CharSequence charSequence0) {
            this.mContentText = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public Builder setContentTitle(CharSequence charSequence0) {
            this.mContentTitle = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public Builder setCustomBigContentView(RemoteViews remoteViews0) {
            this.mBigContentView = remoteViews0;
            return this;
        }

        public Builder setCustomContentView(RemoteViews remoteViews0) {
            this.mContentView = remoteViews0;
            return this;
        }

        public Builder setCustomHeadsUpContentView(RemoteViews remoteViews0) {
            this.mHeadsUpContentView = remoteViews0;
            return this;
        }

        public Builder setDefaults(int v) {
            Notification notification0 = this.mNotification;
            notification0.defaults = v;
            if((v & 4) != 0) {
                notification0.flags |= 1;
            }
            return this;
        }

        public Builder setDeleteIntent(PendingIntent pendingIntent0) {
            this.mNotification.deleteIntent = pendingIntent0;
            return this;
        }

        public Builder setExtras(Bundle bundle0) {
            this.mExtras = bundle0;
            return this;
        }

        private void setFlag(int v, boolean z) {
            int v1;
            Notification notification0;
            if(z) {
                notification0 = this.mNotification;
                v1 = v | notification0.flags;
            }
            else {
                notification0 = this.mNotification;
                v1 = v & notification0.flags;
            }
            notification0.flags = v1;
        }

        public Builder setFullScreenIntent(PendingIntent pendingIntent0, boolean z) {
            this.mFullScreenIntent = pendingIntent0;
            this.setFlag(0x80, z);
            return this;
        }

        public Builder setGroup(String s) {
            this.mGroupKey = s;
            return this;
        }

        public Builder setGroupAlertBehavior(int v) {
            this.mGroupAlertBehavior = v;
            return this;
        }

        public Builder setGroupSummary(boolean z) {
            this.mGroupSummary = z;
            return this;
        }

        public Builder setLargeIcon(Bitmap bitmap0) {
            this.mLargeIcon = this.reduceLargeIconSize(bitmap0);
            return this;
        }

        public Builder setLights(int v, int v1, int v2) {
            this.mNotification.ledARGB = v;
            this.mNotification.ledOnMS = v1;
            this.mNotification.ledOffMS = v2;
            this.mNotification.flags = (v1 == 0 || v2 == 0 ? 0 : 1) | this.mNotification.flags & -2;
            return this;
        }

        public Builder setLocalOnly(boolean z) {
            this.mLocalOnly = z;
            return this;
        }

        public Builder setNumber(int v) {
            this.mNumber = v;
            return this;
        }

        public Builder setOngoing(boolean z) {
            this.setFlag(2, z);
            return this;
        }

        public Builder setOnlyAlertOnce(boolean z) {
            this.setFlag(8, z);
            return this;
        }

        public Builder setPriority(int v) {
            this.mPriority = v;
            return this;
        }

        public Builder setProgress(int v, int v1, boolean z) {
            this.mProgressMax = v;
            this.mProgress = v1;
            this.mProgressIndeterminate = z;
            return this;
        }

        public Builder setPublicVersion(Notification notification0) {
            this.mPublicVersion = notification0;
            return this;
        }

        public Builder setRemoteInputHistory(CharSequence[] arr_charSequence) {
            this.mRemoteInputHistory = arr_charSequence;
            return this;
        }

        public Builder setShortcutId(String s) {
            this.mShortcutId = s;
            return this;
        }

        public Builder setShowWhen(boolean z) {
            this.mShowWhen = z;
            return this;
        }

        public Builder setSmallIcon(int v) {
            this.mNotification.icon = v;
            return this;
        }

        public Builder setSmallIcon(int v, int v1) {
            this.mNotification.icon = v;
            this.mNotification.iconLevel = v1;
            return this;
        }

        public Builder setSortKey(String s) {
            this.mSortKey = s;
            return this;
        }

        public Builder setSound(Uri uri0) {
            Notification notification0 = this.mNotification;
            notification0.sound = uri0;
            notification0.audioStreamType = -1;
            notification0.audioAttributes = new AudioAttributes.Builder().setContentType(4).setUsage(5).build();
            return this;
        }

        public Builder setSound(Uri uri0, int v) {
            Notification notification0 = this.mNotification;
            notification0.sound = uri0;
            notification0.audioStreamType = v;
            notification0.audioAttributes = new AudioAttributes.Builder().setContentType(4).setLegacyStreamType(v).build();
            return this;
        }

        public Builder setStyle(Style notificationCompat$Style0) {
            if(this.mStyle != notificationCompat$Style0) {
                this.mStyle = notificationCompat$Style0;
                if(notificationCompat$Style0 != null) {
                    notificationCompat$Style0.setBuilder(this);
                }
            }
            return this;
        }

        public Builder setSubText(CharSequence charSequence0) {
            this.mSubText = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public Builder setTicker(CharSequence charSequence0) {
            Notification notification0 = this.mNotification;
            notification0.tickerText = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public Builder setTicker(CharSequence charSequence0, RemoteViews remoteViews0) {
            Notification notification0 = this.mNotification;
            notification0.tickerText = Builder.limitCharSequenceLength(charSequence0);
            this.mTickerView = remoteViews0;
            return this;
        }

        public Builder setTimeoutAfter(long v) {
            this.mTimeout = v;
            return this;
        }

        public Builder setUsesChronometer(boolean z) {
            this.mUseChronometer = z;
            return this;
        }

        public Builder setVibrate(long[] arr_v) {
            this.mNotification.vibrate = arr_v;
            return this;
        }

        public Builder setVisibility(int v) {
            this.mVisibility = v;
            return this;
        }

        public Builder setWhen(long v) {
            this.mNotification.when = v;
            return this;
        }
    }

    public static final class CarExtender implements Extender {
        static final String EXTRA_CAR_EXTENDER = "android.car.EXTENSIONS";
        private static final String EXTRA_COLOR = "app_color";
        private static final String EXTRA_CONVERSATION = "car_conversation";
        static final String EXTRA_INVISIBLE_ACTIONS = "invisible_actions";
        private static final String EXTRA_LARGE_ICON = "large_icon";
        private static final String KEY_AUTHOR = "author";
        private static final String KEY_MESSAGES = "messages";
        private static final String KEY_ON_READ = "on_read";
        private static final String KEY_ON_REPLY = "on_reply";
        private static final String KEY_PARTICIPANTS = "participants";
        private static final String KEY_REMOTE_INPUT = "remote_input";
        private static final String KEY_TEXT = "text";
        private static final String KEY_TIMESTAMP = "timestamp";
        private int mColor;
        private Bitmap mLargeIcon;
        private NotificationCompat.CarExtender.UnreadConversation mUnreadConversation;

        public CarExtender() {
            this.mColor = 0;
        }

        public CarExtender(Notification notification0) {
            this.mColor = 0;
            Bundle bundle0 = NotificationCompat.getExtras(notification0) == null ? null : NotificationCompat.getExtras(notification0).getBundle("android.car.EXTENSIONS");
            if(bundle0 != null) {
                this.mLargeIcon = (Bitmap)bundle0.getParcelable("large_icon");
                this.mColor = bundle0.getInt("app_color", 0);
                this.mUnreadConversation = CarExtender.getUnreadConversationFromBundle(bundle0.getBundle("car_conversation"));
            }
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Extender
        public Builder extend(Builder notificationCompat$Builder0) {
            Bundle bundle0 = new Bundle();
            Bitmap bitmap0 = this.mLargeIcon;
            if(bitmap0 != null) {
                bundle0.putParcelable("large_icon", bitmap0);
            }
            int v = this.mColor;
            if(v != 0) {
                bundle0.putInt("app_color", v);
            }
            NotificationCompat.CarExtender.UnreadConversation notificationCompat$CarExtender$UnreadConversation0 = this.mUnreadConversation;
            if(notificationCompat$CarExtender$UnreadConversation0 != null) {
                bundle0.putBundle("car_conversation", CarExtender.getBundleForUnreadConversation(notificationCompat$CarExtender$UnreadConversation0));
            }
            notificationCompat$Builder0.getExtras().putBundle("android.car.EXTENSIONS", bundle0);
            return notificationCompat$Builder0;
        }

        private static Bundle getBundleForUnreadConversation(NotificationCompat.CarExtender.UnreadConversation notificationCompat$CarExtender$UnreadConversation0) {
            Bundle bundle0 = new Bundle();
            String s = notificationCompat$CarExtender$UnreadConversation0.getParticipants() == null || notificationCompat$CarExtender$UnreadConversation0.getParticipants().length <= 1 ? null : notificationCompat$CarExtender$UnreadConversation0.getParticipants()[0];
            String[] arr_s = notificationCompat$CarExtender$UnreadConversation0.getMessages();
            Parcelable[] arr_parcelable = new Parcelable[arr_s.length];
            for(int v = 0; v < arr_s.length; ++v) {
                Bundle bundle1 = new Bundle();
                bundle1.putString("text", notificationCompat$CarExtender$UnreadConversation0.getMessages()[v]);
                bundle1.putString("author", s);
                arr_parcelable[v] = bundle1;
            }
            bundle0.putParcelableArray("messages", arr_parcelable);
            RemoteInput remoteInput0 = notificationCompat$CarExtender$UnreadConversation0.getRemoteInput();
            if(remoteInput0 != null) {
                bundle0.putParcelable("remote_input", new RemoteInput.Builder(remoteInput0.getResultKey()).setLabel(remoteInput0.getLabel()).setChoices(remoteInput0.getChoices()).setAllowFreeFormInput(remoteInput0.getAllowFreeFormInput()).addExtras(remoteInput0.getExtras()).build());
            }
            bundle0.putParcelable("on_reply", notificationCompat$CarExtender$UnreadConversation0.getReplyPendingIntent());
            bundle0.putParcelable("on_read", notificationCompat$CarExtender$UnreadConversation0.getReadPendingIntent());
            bundle0.putStringArray("participants", notificationCompat$CarExtender$UnreadConversation0.getParticipants());
            bundle0.putLong("timestamp", notificationCompat$CarExtender$UnreadConversation0.getLatestTimestamp());
            return bundle0;
        }

        public int getColor() {
            return this.mColor;
        }

        public Bitmap getLargeIcon() {
            return this.mLargeIcon;
        }

        public NotificationCompat.CarExtender.UnreadConversation getUnreadConversation() {
            return this.mUnreadConversation;
        }

        private static NotificationCompat.CarExtender.UnreadConversation getUnreadConversationFromBundle(Bundle bundle0) {
            String[] arr_s1;
            NotificationCompat.CarExtender.UnreadConversation notificationCompat$CarExtender$UnreadConversation0 = null;
            if(bundle0 == null) {
                return null;
            }
            Parcelable[] arr_parcelable = bundle0.getParcelableArray("messages");
            if(arr_parcelable == null) {
                arr_s1 = null;
            }
            else {
                String[] arr_s = new String[arr_parcelable.length];
                int v = 0;
                while(v < arr_parcelable.length) {
                    Parcelable parcelable0 = arr_parcelable[v];
                    if(parcelable0 instanceof Bundle) {
                        String s = ((Bundle)parcelable0).getString("text");
                        arr_s[v] = s;
                        if(s == null) {
                            return null;
                        }
                        ++v;
                        continue;
                    }
                    return null;
                }
                arr_s1 = arr_s;
            }
            Parcelable parcelable1 = bundle0.getParcelable("on_read");
            Parcelable parcelable2 = bundle0.getParcelable("on_reply");
            android.app.RemoteInput remoteInput0 = (android.app.RemoteInput)bundle0.getParcelable("remote_input");
            String[] arr_s2 = bundle0.getStringArray("participants");
            if(arr_s2 != null && arr_s2.length == 1) {
                if(remoteInput0 != null) {
                    notificationCompat$CarExtender$UnreadConversation0 = new RemoteInput(remoteInput0.getResultKey(), remoteInput0.getLabel(), remoteInput0.getChoices(), remoteInput0.getAllowFreeFormInput(), remoteInput0.getExtras(), null);
                }
                return new NotificationCompat.CarExtender.UnreadConversation(arr_s1, ((RemoteInput)notificationCompat$CarExtender$UnreadConversation0), ((PendingIntent)parcelable2), ((PendingIntent)parcelable1), arr_s2, bundle0.getLong("timestamp"));
            }
            return null;
        }

        public CarExtender setColor(int v) {
            this.mColor = v;
            return this;
        }

        public CarExtender setLargeIcon(Bitmap bitmap0) {
            this.mLargeIcon = bitmap0;
            return this;
        }

        public CarExtender setUnreadConversation(NotificationCompat.CarExtender.UnreadConversation notificationCompat$CarExtender$UnreadConversation0) {
            this.mUnreadConversation = notificationCompat$CarExtender$UnreadConversation0;
            return this;
        }
    }

    public static class DecoratedCustomViewStyle extends Style {
        private static final int MAX_ACTION_BUTTONS = 3;

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public void apply(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            if(Build.VERSION.SDK_INT >= 24) {
                notificationBuilderWithBuilderAccessor0.getBuilder().setStyle(a.d());
            }
        }

        private RemoteViews createRemoteViews(RemoteViews remoteViews0, boolean z) {
            int v = 0;
            RemoteViews remoteViews1 = this.applyStandardTemplate(true, 0x7F0C0043, false);
            remoteViews1.removeAllViews(0x7F09001A);
            if(z) {
                ArrayList arrayList0 = this.mBuilder.mActions;
                if(arrayList0 == null) {
                    v = 8;
                }
                else {
                    int v1 = Math.min(arrayList0.size(), 3);
                    if(v1 > 0) {
                        for(int v2 = 0; v2 < v1; ++v2) {
                            remoteViews1.addView(0x7F09001A, this.generateActionButton(((Action)this.mBuilder.mActions.get(v2))));
                        }
                    }
                    else {
                        v = 8;
                    }
                }
            }
            else {
                v = 8;
            }
            remoteViews1.setViewVisibility(0x7F09001A, v);
            remoteViews1.setViewVisibility(0x7F090012, v);  // font:plus_semibold
            this.buildIntoRemoteViews(remoteViews1, remoteViews0);
            return remoteViews1;
        }

        private RemoteViews generateActionButton(Action notificationCompat$Action0) {
            boolean z = notificationCompat$Action0.actionIntent == null;
            RemoteViews remoteViews0 = new RemoteViews("com.pdf.editor.viewer.pdfreader.pdfviewer", (z ? 0x7F0C003C : 0x7F0C003B));
            remoteViews0.setImageViewBitmap(0x7F090013, this.createColoredBitmap(notificationCompat$Action0.getIcon(), this.mBuilder.mContext.getResources().getColor(0x7F06007E)));  // color:color_all_file
            remoteViews0.setTextViewText(0x7F090019, notificationCompat$Action0.title);
            if(!z) {
                remoteViews0.setOnClickPendingIntent(0x7F090010, notificationCompat$Action0.actionIntent);  // font:plus_medium
            }
            remoteViews0.setContentDescription(0x7F090010, notificationCompat$Action0.title);  // font:plus_medium
            return remoteViews0;
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public RemoteViews makeBigContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            if(Build.VERSION.SDK_INT >= 24) {
                return null;
            }
            RemoteViews remoteViews0 = this.mBuilder.getBigContentView();
            if(remoteViews0 == null) {
                remoteViews0 = this.mBuilder.getContentView();
            }
            return remoteViews0 == null ? null : this.createRemoteViews(remoteViews0, true);
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public RemoteViews makeContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            if(Build.VERSION.SDK_INT >= 24) {
                return null;
            }
            return this.mBuilder.getContentView() == null ? null : this.createRemoteViews(this.mBuilder.getContentView(), false);
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public RemoteViews makeHeadsUpContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            if(Build.VERSION.SDK_INT >= 24) {
                return null;
            }
            RemoteViews remoteViews0 = this.mBuilder.getHeadsUpContentView();
            return remoteViews0 == null ? null : this.createRemoteViews((remoteViews0 == null ? this.mBuilder.getContentView() : remoteViews0), true);
        }
    }

    public interface Extender {
        Builder extend(Builder arg1);
    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface GroupAlertBehavior {
    }

    public static class InboxStyle extends Style {
        private ArrayList mTexts;

        public InboxStyle() {
            this.mTexts = new ArrayList();
        }

        public InboxStyle(Builder notificationCompat$Builder0) {
            this.mTexts = new ArrayList();
            this.setBuilder(notificationCompat$Builder0);
        }

        public InboxStyle addLine(CharSequence charSequence0) {
            this.mTexts.add(Builder.limitCharSequenceLength(charSequence0));
            return this;
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public void apply(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            Notification.InboxStyle notification$InboxStyle0 = new Notification.InboxStyle(notificationBuilderWithBuilderAccessor0.getBuilder()).setBigContentTitle(this.mBigContentTitle);
            if(this.mSummaryTextSet) {
                notification$InboxStyle0.setSummaryText(this.mSummaryText);
            }
            for(Object object0: this.mTexts) {
                notification$InboxStyle0.addLine(((CharSequence)object0));
            }
        }

        public InboxStyle setBigContentTitle(CharSequence charSequence0) {
            this.mBigContentTitle = Builder.limitCharSequenceLength(charSequence0);
            return this;
        }

        public InboxStyle setSummaryText(CharSequence charSequence0) {
            this.mSummaryText = Builder.limitCharSequenceLength(charSequence0);
            this.mSummaryTextSet = true;
            return this;
        }
    }

    public static class MessagingStyle extends Style {
        public static final int MAXIMUM_RETAINED_MESSAGES = 25;
        private CharSequence mConversationTitle;
        private Boolean mIsGroupConversation;
        private final List mMessages;
        private Person mUser;

        private MessagingStyle() {
            this.mMessages = new ArrayList();
        }

        public MessagingStyle(Person person0) {
            this.mMessages = new ArrayList();
            if(TextUtils.isEmpty(person0.getName())) {
                throw new IllegalArgumentException("User\'s name must not be empty.");
            }
            this.mUser = person0;
        }

        @Deprecated
        public MessagingStyle(CharSequence charSequence0) {
            this.mMessages = new ArrayList();
            this.mUser = new android.supportv1.v4.app.Person.Builder().setName(charSequence0).build();
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public void addCompatExtras(Bundle bundle0) {
            super.addCompatExtras(bundle0);
            bundle0.putCharSequence("android.selfDisplayName", this.mUser.getName());
            bundle0.putBundle("android.messagingStyleUser", this.mUser.toBundle());
            bundle0.putCharSequence("android.hiddenConversationTitle", this.mConversationTitle);
            if(this.mConversationTitle != null && this.mIsGroupConversation.booleanValue()) {
                bundle0.putCharSequence("android.conversationTitle", this.mConversationTitle);
            }
            if(!this.mMessages.isEmpty()) {
                bundle0.putParcelableArray("android.messages", NotificationCompat.MessagingStyle.Message.getBundleArrayForMessages(this.mMessages));
            }
            Boolean boolean0 = this.mIsGroupConversation;
            if(boolean0 != null) {
                bundle0.putBoolean("android.isGroupConversation", boolean0.booleanValue());
            }
        }

        public MessagingStyle addMessage(NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message0) {
            this.mMessages.add(notificationCompat$MessagingStyle$Message0);
            if(this.mMessages.size() > 25) {
                this.mMessages.remove(0);
            }
            return this;
        }

        public MessagingStyle addMessage(CharSequence charSequence0, long v, Person person0) {
            this.addMessage(new NotificationCompat.MessagingStyle.Message(charSequence0, v, person0));
            return this;
        }

        @Deprecated
        public MessagingStyle addMessage(CharSequence charSequence0, long v, CharSequence charSequence1) {
            NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message0 = new NotificationCompat.MessagingStyle.Message(charSequence0, v, new android.supportv1.v4.app.Person.Builder().setName(charSequence1).build());
            this.mMessages.add(notificationCompat$MessagingStyle$Message0);
            if(this.mMessages.size() > 25) {
                this.mMessages.remove(0);
            }
            return this;
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public void apply(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            Notification.MessagingStyle.Message notification$MessagingStyle$Message0;
            this.setGroupConversation(this.isGroupConversation());
            int v = Build.VERSION.SDK_INT;
            if(v >= 24) {
                Person person0 = this.mUser;
                Notification.MessagingStyle notification$MessagingStyle0 = v < 28 ? a.f(person0.getName()) : android.supportv1.v4.app.a.f(person0.toAndroidPerson());
                if(this.mIsGroupConversation.booleanValue() || v >= 28) {
                    notification$MessagingStyle0.setConversationTitle(this.mConversationTitle);
                }
                if(v >= 28) {
                    notification$MessagingStyle0.setGroupConversation(this.mIsGroupConversation.booleanValue());
                }
                for(Object object0: this.mMessages) {
                    NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message0 = (NotificationCompat.MessagingStyle.Message)object0;
                    if(Build.VERSION.SDK_INT >= 28) {
                        Person person1 = notificationCompat$MessagingStyle$Message0.getPerson();
                        notification$MessagingStyle$Message0 = android.supportv1.v4.app.a.e(notificationCompat$MessagingStyle$Message0.getText(), notificationCompat$MessagingStyle$Message0.getTimestamp(), (person1 == null ? null : person1.toAndroidPerson()));
                    }
                    else {
                        notification$MessagingStyle$Message0 = a.e(notificationCompat$MessagingStyle$Message0.getText(), notificationCompat$MessagingStyle$Message0.getTimestamp(), (notificationCompat$MessagingStyle$Message0.getPerson() == null ? null : notificationCompat$MessagingStyle$Message0.getPerson().getName()));
                    }
                    if(notificationCompat$MessagingStyle$Message0.getDataMimeType() != null) {
                        notification$MessagingStyle$Message0.setData(notificationCompat$MessagingStyle$Message0.getDataMimeType(), notificationCompat$MessagingStyle$Message0.getDataUri());
                    }
                    notification$MessagingStyle0.addMessage(notification$MessagingStyle$Message0);
                }
                notification$MessagingStyle0.setBuilder(notificationBuilderWithBuilderAccessor0.getBuilder());
                return;
            }
            NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message1 = this.findLatestIncomingMessage();
            if(this.mConversationTitle != null && this.mIsGroupConversation.booleanValue()) {
                notificationBuilderWithBuilderAccessor0.getBuilder().setContentTitle(this.mConversationTitle);
            }
            else if(notificationCompat$MessagingStyle$Message1 != null) {
                notificationBuilderWithBuilderAccessor0.getBuilder().setContentTitle("");
                if(notificationCompat$MessagingStyle$Message1.getPerson() != null) {
                    notificationBuilderWithBuilderAccessor0.getBuilder().setContentTitle(notificationCompat$MessagingStyle$Message1.getPerson().getName());
                }
            }
            if(notificationCompat$MessagingStyle$Message1 != null) {
                notificationBuilderWithBuilderAccessor0.getBuilder().setContentText((this.mConversationTitle == null ? notificationCompat$MessagingStyle$Message1.getText() : this.makeMessageLine(notificationCompat$MessagingStyle$Message1)));
            }
            SpannableStringBuilder spannableStringBuilder0 = new SpannableStringBuilder();
            boolean z = this.mConversationTitle != null || this.hasMessagesWithoutSender();
            for(int v1 = this.mMessages.size() - 1; v1 >= 0; --v1) {
                NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message2 = (NotificationCompat.MessagingStyle.Message)this.mMessages.get(v1);
                CharSequence charSequence0 = z ? this.makeMessageLine(notificationCompat$MessagingStyle$Message2) : notificationCompat$MessagingStyle$Message2.getText();
                if(v1 != this.mMessages.size() - 1) {
                    spannableStringBuilder0.insert(0, "\n");
                }
                spannableStringBuilder0.insert(0, charSequence0);
            }
            new Notification.BigTextStyle(notificationBuilderWithBuilderAccessor0.getBuilder()).setBigContentTitle(null).bigText(spannableStringBuilder0);
        }

        public static MessagingStyle extractMessagingStyleFromNotification(Notification notification0) {
            Bundle bundle0 = NotificationCompat.getExtras(notification0);
            if(bundle0 != null && !bundle0.containsKey("android.selfDisplayName") && !bundle0.containsKey("android.messagingStyleUser")) {
                return null;
            }
            try {
                MessagingStyle notificationCompat$MessagingStyle0 = new MessagingStyle();
                notificationCompat$MessagingStyle0.restoreFromCompatExtras(bundle0);
                return notificationCompat$MessagingStyle0;
            }
            catch(ClassCastException unused_ex) {
                return null;
            }
        }

        private NotificationCompat.MessagingStyle.Message findLatestIncomingMessage() {
            for(int v = this.mMessages.size() - 1; v >= 0; --v) {
                NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message0 = (NotificationCompat.MessagingStyle.Message)this.mMessages.get(v);
                if(notificationCompat$MessagingStyle$Message0.getPerson() != null && !TextUtils.isEmpty(notificationCompat$MessagingStyle$Message0.getPerson().getName())) {
                    return notificationCompat$MessagingStyle$Message0;
                }
            }
            return this.mMessages.isEmpty() ? null : ((NotificationCompat.MessagingStyle.Message)this.mMessages.get(this.mMessages.size() - 1));
        }

        public CharSequence getConversationTitle() {
            return this.mConversationTitle;
        }

        public List getMessages() {
            return this.mMessages;
        }

        public Person getUser() {
            return this.mUser;
        }

        @Deprecated
        public CharSequence getUserDisplayName() {
            return this.mUser.getName();
        }

        private boolean hasMessagesWithoutSender() {
            for(int v = this.mMessages.size() - 1; v >= 0; --v) {
                NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message0 = (NotificationCompat.MessagingStyle.Message)this.mMessages.get(v);
                if(notificationCompat$MessagingStyle$Message0.getPerson() != null && notificationCompat$MessagingStyle$Message0.getPerson().getName() == null) {
                    return true;
                }
            }
            return false;
        }

        public boolean isGroupConversation() {
            return this.mIsGroupConversation == null ? false : this.mIsGroupConversation.booleanValue();
        }

        private TextAppearanceSpan makeFontColorSpan(int v) {
            return new TextAppearanceSpan(null, 0, 0, ColorStateList.valueOf(v), null);
        }

        private CharSequence makeMessageLine(NotificationCompat.MessagingStyle.Message notificationCompat$MessagingStyle$Message0) {
            BidiFormatter bidiFormatter0 = BidiFormatter.getInstance();
            CharSequence charSequence0 = new SpannableStringBuilder();
            CharSequence charSequence1 = "";
            CharSequence charSequence2 = notificationCompat$MessagingStyle$Message0.getPerson() == null ? "" : notificationCompat$MessagingStyle$Message0.getPerson().getName();
            int v = 0xFF000000;
            if(TextUtils.isEmpty(charSequence2)) {
                charSequence2 = this.mUser.getName();
                if(this.mBuilder.getColor() != 0) {
                    v = this.mBuilder.getColor();
                }
            }
            CharSequence charSequence3 = bidiFormatter0.unicodeWrap(charSequence2);
            ((SpannableStringBuilder)charSequence0).append(charSequence3);
            ((SpannableStringBuilder)charSequence0).setSpan(this.makeFontColorSpan(v), ((SpannableStringBuilder)charSequence0).length() - charSequence3.length(), ((SpannableStringBuilder)charSequence0).length(), 33);
            if(notificationCompat$MessagingStyle$Message0.getText() != null) {
                charSequence1 = notificationCompat$MessagingStyle$Message0.getText();
            }
            ((SpannableStringBuilder)charSequence0).append("  ").append(bidiFormatter0.unicodeWrap(charSequence1));
            return charSequence0;
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public void restoreFromCompatExtras(Bundle bundle0) {
            this.mMessages.clear();
            this.mUser = bundle0.containsKey("android.messagingStyleUser") ? Person.fromBundle(bundle0.getBundle("android.messagingStyleUser")) : new android.supportv1.v4.app.Person.Builder().setName(bundle0.getString("android.selfDisplayName")).build();
            CharSequence charSequence0 = bundle0.getCharSequence("android.conversationTitle");
            this.mConversationTitle = charSequence0;
            if(charSequence0 == null) {
                this.mConversationTitle = bundle0.getCharSequence("android.hiddenConversationTitle");
            }
            Parcelable[] arr_parcelable = bundle0.getParcelableArray("android.messages");
            if(arr_parcelable != null) {
                List list0 = NotificationCompat.MessagingStyle.Message.getMessagesFromBundleArray(arr_parcelable);
                this.mMessages.addAll(list0);
            }
            if(bundle0.containsKey("android.isGroupConversation")) {
                this.mIsGroupConversation = Boolean.valueOf(bundle0.getBoolean("android.isGroupConversation"));
            }
        }

        public MessagingStyle setConversationTitle(CharSequence charSequence0) {
            this.mConversationTitle = charSequence0;
            return this;
        }

        public MessagingStyle setGroupConversation(boolean z) {
            this.mIsGroupConversation = Boolean.valueOf(z);
            return this;
        }
    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface NotificationVisibility {
    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface StreamType {
    }

    public static abstract class Style {
        CharSequence mBigContentTitle;
        protected Builder mBuilder;
        CharSequence mSummaryText;
        boolean mSummaryTextSet;

        public Style() {
            this.mSummaryTextSet = false;
        }

        public void addCompatExtras(Bundle bundle0) {
        }

        public void apply(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
        }

        public RemoteViews applyStandardTemplate(boolean z, int v, boolean z1) {
            int v9;
            boolean z2;
            Resources resources0 = this.mBuilder.mContext.getResources();
            RemoteViews remoteViews0 = new RemoteViews("com.pdf.editor.viewer.pdfreader.pdfviewer", v);
            Builder notificationCompat$Builder0 = this.mBuilder;
            int v1 = 0;
            if(notificationCompat$Builder0.mLargeIcon != null) {
                remoteViews0.setViewVisibility(0x7F09012B, 0);
                remoteViews0.setImageViewBitmap(0x7F09012B, this.mBuilder.mLargeIcon);
                if(z && this.mBuilder.mNotification.icon != 0) {
                    int v2 = resources0.getDimensionPixelSize(0x7F0700D9);  // dimen:_27sdp
                    int v3 = resources0.getDimensionPixelSize(0x7F0700DB);  // dimen:_280sdp
                    remoteViews0.setImageViewBitmap(0x7F09021C, this.createIconWithBackground(this.mBuilder.mNotification.icon, v2, v2 - v3 * 2, this.mBuilder.getColor()));
                    remoteViews0.setViewVisibility(0x7F09021C, 0);
                }
            }
            else if(z && notificationCompat$Builder0.mNotification.icon != 0) {
                remoteViews0.setViewVisibility(0x7F09012B, 0);
                int v4 = resources0.getDimensionPixelSize(0x7F0700D6);  // dimen:_277sdp
                int v5 = resources0.getDimensionPixelSize(0x7F0700D3);  // dimen:_274sdp
                int v6 = resources0.getDimensionPixelSize(0x7F0700DC);  // dimen:_281sdp
                remoteViews0.setImageViewBitmap(0x7F09012B, this.createIconWithBackground(this.mBuilder.mNotification.icon, v4 - v5, v6, this.mBuilder.getColor()));
            }
            CharSequence charSequence0 = this.mBuilder.mContentTitle;
            if(charSequence0 != null) {
                remoteViews0.setTextViewText(0x7F090294, charSequence0);
            }
            CharSequence charSequence1 = this.mBuilder.mContentText;
            int v7 = 1;
            if(charSequence1 == null) {
                z2 = false;
            }
            else {
                remoteViews0.setTextViewText(0x7F090288, charSequence1);
                z2 = true;
            }
            Builder notificationCompat$Builder1 = this.mBuilder;
            CharSequence charSequence2 = notificationCompat$Builder1.mContentInfo;
            if(charSequence2 != null) {
                remoteViews0.setTextViewText(0x7F090137, charSequence2);
                remoteViews0.setViewVisibility(0x7F090137, 0);
                z2 = true;
                v9 = 1;
            }
            else if(notificationCompat$Builder1.mNumber > 0) {
                int v8 = resources0.getInteger(0x7F0A001E);  // id:SHOW_PATH
                if(this.mBuilder.mNumber > v8) {
                    remoteViews0.setTextViewText(0x7F090137, resources0.getString(0x7F0E020B));
                }
                else {
                    remoteViews0.setTextViewText(0x7F090137, NumberFormat.getIntegerInstance().format(((long)this.mBuilder.mNumber)));
                }
                remoteViews0.setViewVisibility(0x7F090137, 0);
                z2 = true;
                v9 = 1;
            }
            else {
                remoteViews0.setViewVisibility(0x7F090137, 8);
                v9 = 0;
            }
            CharSequence charSequence3 = this.mBuilder.mSubText;
            if(charSequence3 != null) {
                remoteViews0.setTextViewText(0x7F090288, charSequence3);
                CharSequence charSequence4 = this.mBuilder.mContentText;
                if(charSequence4 == null) {
                    remoteViews0.setViewVisibility(0x7F090289, 8);
                }
                else {
                    remoteViews0.setTextViewText(0x7F090289, charSequence4);
                    remoteViews0.setViewVisibility(0x7F090289, 0);
                    if(z1) {
                        remoteViews0.setTextViewTextSize(0x7F090288, 0, ((float)resources0.getDimensionPixelSize(0x7F0700DD)));  // dimen:_282sdp
                    }
                    remoteViews0.setViewPadding(0x7F090152, 0, 0, 0, 0);
                }
            }
            if(this.mBuilder.getWhenIfShowing() == 0L) {
                v7 = v9;
            }
            else if(this.mBuilder.mUseChronometer) {
                remoteViews0.setViewVisibility(0x7F090088, 0);
                long v10 = this.mBuilder.getWhenIfShowing();
                remoteViews0.setLong(0x7F090088, "setBase", SystemClock.elapsedRealtime() - System.currentTimeMillis() + v10);
                remoteViews0.setBoolean(0x7F090088, "setStarted", true);
            }
            else {
                remoteViews0.setViewVisibility(0x7F090293, 0);
                remoteViews0.setLong(0x7F090293, "setTime", this.mBuilder.getWhenIfShowing());
            }
            remoteViews0.setViewVisibility(0x7F09021E, (v7 == 0 ? 8 : 0));
            if(!z2) {
                v1 = 8;
            }
            remoteViews0.setViewVisibility(0x7F090153, v1);
            return remoteViews0;
        }

        public Notification build() {
            return this.mBuilder == null ? null : this.mBuilder.build();
        }

        public void buildIntoRemoteViews(RemoteViews remoteViews0, RemoteViews remoteViews1) {
            this.hideNormalContent(remoteViews0);
            remoteViews0.removeAllViews(0x7F090189);
            remoteViews0.addView(0x7F090189, remoteViews1.clone());
            remoteViews0.setViewVisibility(0x7F090189, 0);
            remoteViews0.setViewPadding(0x7F09018A, 0, this.calculateTopPadding(), 0, 0);
        }

        private int calculateTopPadding() {
            Resources resources0 = this.mBuilder.mContext.getResources();
            int v = resources0.getDimensionPixelSize(0x7F0700DE);  // dimen:_283sdp
            int v1 = resources0.getDimensionPixelSize(0x7F0700DF);  // dimen:_284sdp
            float f = (Style.constrain(resources0.getConfiguration().fontScale, 1.0f, 1.3f) - 1.0f) / 0.3f;
            return Math.round(f * ((float)v1) + (1.0f - f) * ((float)v));
        }

        private static float constrain(float f, float f1, float f2) {
            if(f < f1) {
                return f1;
            }
            return f > f2 ? f2 : f;
        }

        private Bitmap createColoredBitmap(int v, int v1, int v2) {
            Drawable drawable0 = this.mBuilder.mContext.getResources().getDrawable(v);
            int v3 = v2 == 0 ? drawable0.getIntrinsicWidth() : v2;
            if(v2 == 0) {
                v2 = drawable0.getIntrinsicHeight();
            }
            Bitmap bitmap0 = Bitmap.createBitmap(v3, v2, Bitmap.Config.ARGB_8888);
            drawable0.setBounds(0, 0, v3, v2);
            if(v1 != 0) {
                drawable0.mutate().setColorFilter(new PorterDuffColorFilter(v1, PorterDuff.Mode.SRC_IN));
            }
            drawable0.draw(new Canvas(bitmap0));
            return bitmap0;
        }

        public Bitmap createColoredBitmap(int v, int v1) {
            return this.createColoredBitmap(v, v1, 0);
        }

        private Bitmap createIconWithBackground(int v, int v1, int v2, int v3) {
            if(v3 == 0) {
                v3 = 0;
            }
            Bitmap bitmap0 = this.createColoredBitmap(0x7F08009C, v3, v1);
            Canvas canvas0 = new Canvas(bitmap0);
            Drawable drawable0 = this.mBuilder.mContext.getResources().getDrawable(v).mutate();
            drawable0.setFilterBitmap(true);
            int v4 = (v1 - v2) / 2;
            drawable0.setBounds(v4, v4, v2 + v4, v2 + v4);
            drawable0.setColorFilter(new PorterDuffColorFilter(-1, PorterDuff.Mode.SRC_ATOP));
            drawable0.draw(canvas0);
            return bitmap0;
        }

        private void hideNormalContent(RemoteViews remoteViews0) {
            remoteViews0.setViewVisibility(0x7F090294, 8);
            remoteViews0.setViewVisibility(0x7F090289, 8);
            remoteViews0.setViewVisibility(0x7F090288, 8);
        }

        public RemoteViews makeBigContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            return null;
        }

        public RemoteViews makeContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            return null;
        }

        public RemoteViews makeHeadsUpContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            return null;
        }

        public void restoreFromCompatExtras(Bundle bundle0) {
        }

        public void setBuilder(Builder notificationCompat$Builder0) {
            if(this.mBuilder != notificationCompat$Builder0) {
                this.mBuilder = notificationCompat$Builder0;
                if(notificationCompat$Builder0 != null) {
                    notificationCompat$Builder0.setStyle(this);
                }
            }
        }
    }

    public static final class WearableExtender implements Extender {
        private static final int DEFAULT_CONTENT_ICON_GRAVITY = 0x800005;
        private static final int DEFAULT_FLAGS = 1;
        private static final int DEFAULT_GRAVITY = 80;
        private static final String EXTRA_WEARABLE_EXTENSIONS = "android.wearable.EXTENSIONS";
        private static final int FLAG_BIG_PICTURE_AMBIENT = 0x20;
        private static final int FLAG_CONTENT_INTENT_AVAILABLE_OFFLINE = 1;
        private static final int FLAG_HINT_AVOID_BACKGROUND_CLIPPING = 16;
        private static final int FLAG_HINT_CONTENT_INTENT_LAUNCHES_ACTIVITY = 0x40;
        private static final int FLAG_HINT_HIDE_ICON = 2;
        private static final int FLAG_HINT_SHOW_BACKGROUND_ONLY = 4;
        private static final int FLAG_START_SCROLL_BOTTOM = 8;
        private static final String KEY_ACTIONS = "actions";
        private static final String KEY_BACKGROUND = "background";
        private static final String KEY_BRIDGE_TAG = "bridgeTag";
        private static final String KEY_CONTENT_ACTION_INDEX = "contentActionIndex";
        private static final String KEY_CONTENT_ICON = "contentIcon";
        private static final String KEY_CONTENT_ICON_GRAVITY = "contentIconGravity";
        private static final String KEY_CUSTOM_CONTENT_HEIGHT = "customContentHeight";
        private static final String KEY_CUSTOM_SIZE_PRESET = "customSizePreset";
        private static final String KEY_DISMISSAL_ID = "dismissalId";
        private static final String KEY_DISPLAY_INTENT = "displayIntent";
        private static final String KEY_FLAGS = "flags";
        private static final String KEY_GRAVITY = "gravity";
        private static final String KEY_HINT_SCREEN_TIMEOUT = "hintScreenTimeout";
        private static final String KEY_PAGES = "pages";
        public static final int SCREEN_TIMEOUT_LONG = -1;
        public static final int SCREEN_TIMEOUT_SHORT = 0;
        public static final int SIZE_DEFAULT = 0;
        public static final int SIZE_FULL_SCREEN = 5;
        public static final int SIZE_LARGE = 4;
        public static final int SIZE_MEDIUM = 3;
        public static final int SIZE_SMALL = 2;
        public static final int SIZE_XSMALL = 1;
        public static final int UNSET_ACTION_INDEX = -1;
        private ArrayList mActions;
        private Bitmap mBackground;
        private String mBridgeTag;
        private int mContentActionIndex;
        private int mContentIcon;
        private int mContentIconGravity;
        private int mCustomContentHeight;
        private int mCustomSizePreset;
        private String mDismissalId;
        private PendingIntent mDisplayIntent;
        private int mFlags;
        private int mGravity;
        private int mHintScreenTimeout;
        private ArrayList mPages;

        public WearableExtender() {
            this.mActions = new ArrayList();
            this.mFlags = 1;
            this.mPages = new ArrayList();
            this.mContentIconGravity = 0x800005;
            this.mContentActionIndex = -1;
            this.mCustomSizePreset = 0;
            this.mGravity = 80;
        }

        public WearableExtender(Notification notification0) {
            this.mActions = new ArrayList();
            this.mFlags = 1;
            this.mPages = new ArrayList();
            this.mContentIconGravity = 0x800005;
            this.mContentActionIndex = -1;
            this.mCustomSizePreset = 0;
            this.mGravity = 80;
            Bundle bundle0 = NotificationCompat.getExtras(notification0);
            Bundle bundle1 = bundle0 == null ? null : bundle0.getBundle("android.wearable.EXTENSIONS");
            if(bundle1 != null) {
                ArrayList arrayList0 = bundle1.getParcelableArrayList("actions");
                if(arrayList0 != null) {
                    int v = arrayList0.size();
                    Action[] arr_notificationCompat$Action = new Action[v];
                    for(int v1 = 0; v1 < v; ++v1) {
                        arr_notificationCompat$Action[v1] = NotificationCompat.getActionCompatFromAction(((Notification.Action)arrayList0.get(v1)));
                    }
                    Collections.addAll(this.mActions, arr_notificationCompat$Action);
                }
                this.mFlags = bundle1.getInt("flags", 1);
                this.mDisplayIntent = (PendingIntent)bundle1.getParcelable("displayIntent");
                Notification[] arr_notification = NotificationCompat.getNotificationArrayFromBundle(bundle1, "pages");
                if(arr_notification != null) {
                    Collections.addAll(this.mPages, arr_notification);
                }
                this.mBackground = (Bitmap)bundle1.getParcelable("background");
                this.mContentIcon = bundle1.getInt("contentIcon");
                this.mContentIconGravity = bundle1.getInt("contentIconGravity", 0x800005);
                this.mContentActionIndex = bundle1.getInt("contentActionIndex", -1);
                this.mCustomSizePreset = bundle1.getInt("customSizePreset", 0);
                this.mCustomContentHeight = bundle1.getInt("customContentHeight");
                this.mGravity = bundle1.getInt("gravity", 80);
                this.mHintScreenTimeout = bundle1.getInt("hintScreenTimeout");
                this.mDismissalId = bundle1.getString("dismissalId");
                this.mBridgeTag = bundle1.getString("bridgeTag");
            }
        }

        public WearableExtender addAction(Action notificationCompat$Action0) {
            this.mActions.add(notificationCompat$Action0);
            return this;
        }

        public WearableExtender addActions(List list0) {
            this.mActions.addAll(list0);
            return this;
        }

        public WearableExtender addPage(Notification notification0) {
            this.mPages.add(notification0);
            return this;
        }

        public WearableExtender addPages(List list0) {
            this.mPages.addAll(list0);
            return this;
        }

        public WearableExtender clearActions() {
            this.mActions.clear();
            return this;
        }

        public WearableExtender clearPages() {
            this.mPages.clear();
            return this;
        }

        public WearableExtender clone() {
            WearableExtender notificationCompat$WearableExtender0 = new WearableExtender();
            notificationCompat$WearableExtender0.mActions = new ArrayList(this.mActions);
            notificationCompat$WearableExtender0.mFlags = this.mFlags;
            notificationCompat$WearableExtender0.mDisplayIntent = this.mDisplayIntent;
            notificationCompat$WearableExtender0.mPages = new ArrayList(this.mPages);
            notificationCompat$WearableExtender0.mBackground = this.mBackground;
            notificationCompat$WearableExtender0.mContentIcon = this.mContentIcon;
            notificationCompat$WearableExtender0.mContentIconGravity = this.mContentIconGravity;
            notificationCompat$WearableExtender0.mContentActionIndex = this.mContentActionIndex;
            notificationCompat$WearableExtender0.mCustomSizePreset = this.mCustomSizePreset;
            notificationCompat$WearableExtender0.mCustomContentHeight = this.mCustomContentHeight;
            notificationCompat$WearableExtender0.mGravity = this.mGravity;
            notificationCompat$WearableExtender0.mHintScreenTimeout = this.mHintScreenTimeout;
            notificationCompat$WearableExtender0.mDismissalId = this.mDismissalId;
            notificationCompat$WearableExtender0.mBridgeTag = this.mBridgeTag;
            return notificationCompat$WearableExtender0;
        }

        @Override
        public Object clone() throws CloneNotSupportedException {
            return this.clone();
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Extender
        public Builder extend(Builder notificationCompat$Builder0) {
            Bundle bundle0 = new Bundle();
            if(!this.mActions.isEmpty()) {
                ArrayList arrayList0 = new ArrayList(this.mActions.size());
                for(Object object0: this.mActions) {
                    arrayList0.add(WearableExtender.getActionFromActionCompat(((Action)object0)));
                }
                bundle0.putParcelableArrayList("actions", arrayList0);
            }
            int v = this.mFlags;
            if(v != 1) {
                bundle0.putInt("flags", v);
            }
            PendingIntent pendingIntent0 = this.mDisplayIntent;
            if(pendingIntent0 != null) {
                bundle0.putParcelable("displayIntent", pendingIntent0);
            }
            if(!this.mPages.isEmpty()) {
                bundle0.putParcelableArray("pages", ((Parcelable[])this.mPages.toArray(new Notification[this.mPages.size()])));
            }
            Bitmap bitmap0 = this.mBackground;
            if(bitmap0 != null) {
                bundle0.putParcelable("background", bitmap0);
            }
            int v1 = this.mContentIcon;
            if(v1 != 0) {
                bundle0.putInt("contentIcon", v1);
            }
            int v2 = this.mContentIconGravity;
            if(v2 != 0x800005) {
                bundle0.putInt("contentIconGravity", v2);
            }
            int v3 = this.mContentActionIndex;
            if(v3 != -1) {
                bundle0.putInt("contentActionIndex", v3);
            }
            int v4 = this.mCustomSizePreset;
            if(v4 != 0) {
                bundle0.putInt("customSizePreset", v4);
            }
            int v5 = this.mCustomContentHeight;
            if(v5 != 0) {
                bundle0.putInt("customContentHeight", v5);
            }
            int v6 = this.mGravity;
            if(v6 != 80) {
                bundle0.putInt("gravity", v6);
            }
            int v7 = this.mHintScreenTimeout;
            if(v7 != 0) {
                bundle0.putInt("hintScreenTimeout", v7);
            }
            String s = this.mDismissalId;
            if(s != null) {
                bundle0.putString("dismissalId", s);
            }
            String s1 = this.mBridgeTag;
            if(s1 != null) {
                bundle0.putString("bridgeTag", s1);
            }
            notificationCompat$Builder0.getExtras().putBundle("android.wearable.EXTENSIONS", bundle0);
            return notificationCompat$Builder0;
        }

        private static Notification.Action getActionFromActionCompat(Action notificationCompat$Action0) {
            Notification.Action.Builder notification$Action$Builder0 = new Notification.Action.Builder(notificationCompat$Action0.getIcon(), notificationCompat$Action0.getTitle(), notificationCompat$Action0.getActionIntent());
            Bundle bundle0 = notificationCompat$Action0.getExtras() == null ? new Bundle() : new Bundle(notificationCompat$Action0.getExtras());
            bundle0.putBoolean("android.supportv1.allowGeneratedReplies", notificationCompat$Action0.getAllowGeneratedReplies());
            if(Build.VERSION.SDK_INT >= 24) {
                notification$Action$Builder0.setAllowGeneratedReplies(notificationCompat$Action0.getAllowGeneratedReplies());
            }
            notification$Action$Builder0.addExtras(bundle0);
            RemoteInput[] arr_remoteInput = notificationCompat$Action0.getRemoteInputs();
            if(arr_remoteInput != null) {
                android.app.RemoteInput[] arr_remoteInput1 = RemoteInput.fromCompat(arr_remoteInput);
                for(int v = 0; v < arr_remoteInput1.length; ++v) {
                    notification$Action$Builder0.addRemoteInput(arr_remoteInput1[v]);
                }
            }
            return notification$Action$Builder0.build();
        }

        public List getActions() {
            return this.mActions;
        }

        public Bitmap getBackground() {
            return this.mBackground;
        }

        public String getBridgeTag() {
            return this.mBridgeTag;
        }

        public int getContentAction() {
            return this.mContentActionIndex;
        }

        @Deprecated
        public int getContentIcon() {
            return this.mContentIcon;
        }

        @Deprecated
        public int getContentIconGravity() {
            return this.mContentIconGravity;
        }

        public boolean getContentIntentAvailableOffline() {
            return (this.mFlags & 1) != 0;
        }

        @Deprecated
        public int getCustomContentHeight() {
            return this.mCustomContentHeight;
        }

        @Deprecated
        public int getCustomSizePreset() {
            return this.mCustomSizePreset;
        }

        public String getDismissalId() {
            return this.mDismissalId;
        }

        public PendingIntent getDisplayIntent() {
            return this.mDisplayIntent;
        }

        @Deprecated
        public int getGravity() {
            return this.mGravity;
        }

        public boolean getHintAmbientBigPicture() {
            return (this.mFlags & 0x20) != 0;
        }

        @Deprecated
        public boolean getHintAvoidBackgroundClipping() {
            return (this.mFlags & 16) != 0;
        }

        public boolean getHintContentIntentLaunchesActivity() {
            return (this.mFlags & 0x40) != 0;
        }

        @Deprecated
        public boolean getHintHideIcon() {
            return (this.mFlags & 2) != 0;
        }

        @Deprecated
        public int getHintScreenTimeout() {
            return this.mHintScreenTimeout;
        }

        @Deprecated
        public boolean getHintShowBackgroundOnly() {
            return (this.mFlags & 4) != 0;
        }

        public List getPages() {
            return this.mPages;
        }

        public boolean getStartScrollBottom() {
            return (this.mFlags & 8) != 0;
        }

        public WearableExtender setBackground(Bitmap bitmap0) {
            this.mBackground = bitmap0;
            return this;
        }

        public WearableExtender setBridgeTag(String s) {
            this.mBridgeTag = s;
            return this;
        }

        public WearableExtender setContentAction(int v) {
            this.mContentActionIndex = v;
            return this;
        }

        @Deprecated
        public WearableExtender setContentIcon(int v) {
            this.mContentIcon = v;
            return this;
        }

        @Deprecated
        public WearableExtender setContentIconGravity(int v) {
            this.mContentIconGravity = v;
            return this;
        }

        public WearableExtender setContentIntentAvailableOffline(boolean z) {
            this.setFlag(1, z);
            return this;
        }

        @Deprecated
        public WearableExtender setCustomContentHeight(int v) {
            this.mCustomContentHeight = v;
            return this;
        }

        @Deprecated
        public WearableExtender setCustomSizePreset(int v) {
            this.mCustomSizePreset = v;
            return this;
        }

        public WearableExtender setDismissalId(String s) {
            this.mDismissalId = s;
            return this;
        }

        public WearableExtender setDisplayIntent(PendingIntent pendingIntent0) {
            this.mDisplayIntent = pendingIntent0;
            return this;
        }

        private void setFlag(int v, boolean z) {
            this.mFlags = z ? v | this.mFlags : v & this.mFlags;
        }

        @Deprecated
        public WearableExtender setGravity(int v) {
            this.mGravity = v;
            return this;
        }

        public WearableExtender setHintAmbientBigPicture(boolean z) {
            this.setFlag(0x20, z);
            return this;
        }

        @Deprecated
        public WearableExtender setHintAvoidBackgroundClipping(boolean z) {
            this.setFlag(16, z);
            return this;
        }

        public WearableExtender setHintContentIntentLaunchesActivity(boolean z) {
            this.setFlag(0x40, z);
            return this;
        }

        @Deprecated
        public WearableExtender setHintHideIcon(boolean z) {
            this.setFlag(2, z);
            return this;
        }

        @Deprecated
        public WearableExtender setHintScreenTimeout(int v) {
            this.mHintScreenTimeout = v;
            return this;
        }

        @Deprecated
        public WearableExtender setHintShowBackgroundOnly(boolean z) {
            this.setFlag(4, z);
            return this;
        }

        public WearableExtender setStartScrollBottom(boolean z) {
            this.setFlag(8, z);
            return this;
        }
    }

    public static final int BADGE_ICON_LARGE = 2;
    public static final int BADGE_ICON_NONE = 0;
    public static final int BADGE_ICON_SMALL = 1;
    public static final String CATEGORY_ALARM = "alarm";
    public static final String CATEGORY_CALL = "call";
    public static final String CATEGORY_EMAIL = "email";
    public static final String CATEGORY_ERROR = "err";
    public static final String CATEGORY_EVENT = "event";
    public static final String CATEGORY_MESSAGE = "msg";
    public static final String CATEGORY_PROGRESS = "progress";
    public static final String CATEGORY_PROMO = "promo";
    public static final String CATEGORY_RECOMMENDATION = "recommendation";
    public static final String CATEGORY_REMINDER = "reminder";
    public static final String CATEGORY_SERVICE = "service";
    public static final String CATEGORY_SOCIAL = "social";
    public static final String CATEGORY_STATUS = "status";
    public static final String CATEGORY_SYSTEM = "sys";
    public static final String CATEGORY_TRANSPORT = "transport";
    public static final int COLOR_DEFAULT = 0;
    public static final int DEFAULT_ALL = -1;
    public static final int DEFAULT_LIGHTS = 4;
    public static final int DEFAULT_SOUND = 1;
    public static final int DEFAULT_VIBRATE = 2;
    public static final String EXTRA_AUDIO_CONTENTS_URI = "android.audioContents";
    public static final String EXTRA_BACKGROUND_IMAGE_URI = "android.backgroundImageUri";
    public static final String EXTRA_BIG_TEXT = "android.bigText";
    public static final String EXTRA_COMPACT_ACTIONS = "android.compactActions";
    public static final String EXTRA_CONVERSATION_TITLE = "android.conversationTitle";
    public static final String EXTRA_HIDDEN_CONVERSATION_TITLE = "android.hiddenConversationTitle";
    public static final String EXTRA_INFO_TEXT = "android.infoText";
    public static final String EXTRA_IS_GROUP_CONVERSATION = "android.isGroupConversation";
    public static final String EXTRA_LARGE_ICON = "android.largeIcon";
    public static final String EXTRA_LARGE_ICON_BIG = "android.largeIcon.big";
    public static final String EXTRA_MEDIA_SESSION = "android.mediaSession";
    public static final String EXTRA_MESSAGES = "android.messages";
    public static final String EXTRA_MESSAGING_STYLE_USER = "android.messagingStyleUser";
    public static final String EXTRA_PEOPLE = "android.people";
    public static final String EXTRA_PICTURE = "android.picture";
    public static final String EXTRA_PROGRESS = "android.progress";
    public static final String EXTRA_PROGRESS_INDETERMINATE = "android.progressIndeterminate";
    public static final String EXTRA_PROGRESS_MAX = "android.progressMax";
    public static final String EXTRA_REMOTE_INPUT_HISTORY = "android.remoteInputHistory";
    public static final String EXTRA_SELF_DISPLAY_NAME = "android.selfDisplayName";
    public static final String EXTRA_SHOW_CHRONOMETER = "android.showChronometer";
    public static final String EXTRA_SHOW_WHEN = "android.showWhen";
    public static final String EXTRA_SMALL_ICON = "android.icon";
    public static final String EXTRA_SUB_TEXT = "android.subText";
    public static final String EXTRA_SUMMARY_TEXT = "android.summaryText";
    public static final String EXTRA_TEMPLATE = "android.template";
    public static final String EXTRA_TEXT = "android.text";
    public static final String EXTRA_TEXT_LINES = "android.textLines";
    public static final String EXTRA_TITLE = "android.title";
    public static final String EXTRA_TITLE_BIG = "android.title.big";
    public static final int FLAG_AUTO_CANCEL = 16;
    public static final int FLAG_FOREGROUND_SERVICE = 0x40;
    public static final int FLAG_GROUP_SUMMARY = 0x200;
    @Deprecated
    public static final int FLAG_HIGH_PRIORITY = 0x80;
    public static final int FLAG_INSISTENT = 4;
    public static final int FLAG_LOCAL_ONLY = 0x100;
    public static final int FLAG_NO_CLEAR = 0x20;
    public static final int FLAG_ONGOING_EVENT = 2;
    public static final int FLAG_ONLY_ALERT_ONCE = 8;
    public static final int FLAG_SHOW_LIGHTS = 1;
    public static final int GROUP_ALERT_ALL = 0;
    public static final int GROUP_ALERT_CHILDREN = 2;
    public static final int GROUP_ALERT_SUMMARY = 1;
    public static final int PRIORITY_DEFAULT = 0;
    public static final int PRIORITY_HIGH = 1;
    public static final int PRIORITY_LOW = -1;
    public static final int PRIORITY_MAX = 2;
    public static final int PRIORITY_MIN = -2;
    public static final int STREAM_DEFAULT = -1;
    public static final int VISIBILITY_PRIVATE = 0;
    public static final int VISIBILITY_PUBLIC = 1;
    public static final int VISIBILITY_SECRET = -1;

    public static Action getAction(Notification notification0, int v) {
        return NotificationCompat.getActionCompatFromAction(notification0.actions[v]);
    }

    public static Action getActionCompatFromAction(Notification.Action notification$Action0) {
        int v2;
        boolean z;
        RemoteInput[] arr_remoteInput1;
        android.app.RemoteInput[] arr_remoteInput = notification$Action0.getRemoteInputs();
        if(arr_remoteInput == null) {
            arr_remoteInput1 = null;
        }
        else {
            RemoteInput[] arr_remoteInput2 = new RemoteInput[arr_remoteInput.length];
            for(int v = 0; v < arr_remoteInput.length; ++v) {
                android.app.RemoteInput remoteInput0 = arr_remoteInput[v];
                arr_remoteInput2[v] = new RemoteInput(remoteInput0.getResultKey(), remoteInput0.getLabel(), remoteInput0.getChoices(), remoteInput0.getAllowFreeFormInput(), remoteInput0.getExtras(), null);
            }
            arr_remoteInput1 = arr_remoteInput2;
        }
        int v1 = Build.VERSION.SDK_INT;
        if(v1 < 24) {
            z = notification$Action0.getExtras().getBoolean("android.supportv1.allowGeneratedReplies");
        }
        else if(!notification$Action0.getExtras().getBoolean("android.supportv1.allowGeneratedReplies") && !notification$Action0.getAllowGeneratedReplies()) {
            z = false;
        }
        else {
            z = true;
        }
        boolean z1 = notification$Action0.getExtras().getBoolean("android.supportv1.action.showsUserInterface", true);
        if(v1 >= 28) {
            v2 = notification$Action0.getSemanticAction();
            return new Action(notification$Action0.icon, notification$Action0.title, notification$Action0.actionIntent, notification$Action0.getExtras(), arr_remoteInput1, null, z, v2, z1);
        }
        v2 = notification$Action0.getExtras().getInt("android.supportv1.action.semanticAction", 0);
        return new Action(notification$Action0.icon, notification$Action0.title, notification$Action0.actionIntent, notification$Action0.getExtras(), arr_remoteInput1, null, z, v2, z1);
    }

    public static int getActionCount(Notification notification0) {
        return notification0.actions == null ? 0 : notification0.actions.length;
    }

    public static int getBadgeIconType(Notification notification0) {
        return Build.VERSION.SDK_INT < 26 ? 0 : notification0.getBadgeIconType();
    }

    public static String getCategory(Notification notification0) {
        return notification0.category;
    }

    public static String getChannelId(Notification notification0) {
        return Build.VERSION.SDK_INT < 26 ? null : notification0.getChannelId();
    }

    public static CharSequence getContentTitle(Notification notification0) {
        return notification0.extras.getCharSequence("android.title");
    }

    public static Bundle getExtras(Notification notification0) {
        return notification0.extras;
    }

    public static String getGroup(Notification notification0) {
        return notification0.getGroup();
    }

    public static int getGroupAlertBehavior(Notification notification0) {
        return Build.VERSION.SDK_INT < 26 ? 0 : notification0.getGroupAlertBehavior();
    }

    public static List getInvisibleActions(Notification notification0) {
        List list0 = new ArrayList();
        Bundle bundle0 = notification0.extras.getBundle("android.car.EXTENSIONS");
        if(bundle0 == null) {
            return list0;
        }
        Bundle bundle1 = bundle0.getBundle("invisible_actions");
        if(bundle1 != null) {
            for(int v = 0; v < bundle1.size(); ++v) {
                ((ArrayList)list0).add(NotificationCompatJellybean.getActionFromBundle(bundle1.getBundle(Integer.toString(v))));
            }
        }
        return list0;
    }

    public static boolean getLocalOnly(Notification notification0) {
        return (notification0.flags & 0x100) != 0;
    }

    public static Notification[] getNotificationArrayFromBundle(Bundle bundle0, String s) {
        Parcelable[] arr_parcelable = bundle0.getParcelableArray(s);
        if(!(arr_parcelable instanceof Notification[]) && arr_parcelable != null) {
            Notification[] arr_notification = new Notification[arr_parcelable.length];
            for(int v = 0; v < arr_parcelable.length; ++v) {
                arr_notification[v] = (Notification)arr_parcelable[v];
            }
            bundle0.putParcelableArray(s, arr_notification);
            return arr_notification;
        }
        return (Notification[])arr_parcelable;
    }

    public static String getShortcutId(Notification notification0) {
        return Build.VERSION.SDK_INT < 26 ? null : notification0.getShortcutId();
    }

    public static String getSortKey(Notification notification0) {
        return notification0.getSortKey();
    }

    public static long getTimeoutAfter(Notification notification0) {
        return Build.VERSION.SDK_INT < 26 ? 0L : notification0.getTimeoutAfter();
    }

    public static boolean isGroupSummary(Notification notification0) {
        return (notification0.flags & 0x200) != 0;
    }
}


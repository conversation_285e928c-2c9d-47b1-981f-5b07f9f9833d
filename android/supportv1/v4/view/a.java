package android.supportv1.v4.view;

import android.graphics.Rect;
import android.view.DisplayCutout;
import android.view.View.OnUnhandledKeyEventListener;
import android.view.View;
import java.util.List;

public abstract class a {
    public static DisplayCutout f(Rect rect0, List list0) {
        return new DisplayCutout(rect0, list0);
    }

    public static View.OnUnhandledKeyEventListener h(Object object0) {
        return (View.OnUnhandledKeyEventListener)object0;
    }

    public static View i(int v, View view0) {
        return view0.requireViewById(v);
    }
}


package android.supportv1.v4.view;

import android.os.Handler;
import android.os.Message;
import android.view.GestureDetector.OnDoubleTapListener;

class GestureDetectorCompat.GestureDetectorCompatImplBase.GestureHandler extends Handler {
    public GestureDetectorCompat.GestureDetectorCompatImplBase.GestureHandler(GestureDetectorCompatImplBase gestureDetectorCompat$GestureDetectorCompatImplBase0) {
        GestureDetectorCompatImplBase.this = gestureDetectorCompat$GestureDetectorCompatImplBase0;
        super();
    }

    public GestureDetectorCompat.GestureDetectorCompatImplBase.GestureHandler(GestureDetectorCompatImplBase gestureDetectorCompat$GestureDetectorCompatImplBase0, Handler handler0) {
        GestureDetectorCompatImplBase.this = gestureDetectorCompat$GestureDetectorCompatImplBase0;
        super(handler0.getLooper());
    }

    @Override  // android.os.Handler
    public void handleMessage(Message message0) {
        switch(message0.what) {
            case 1: {
                GestureDetectorCompatImplBase.this.mListener.onShowPress(GestureDetectorCompatImplBase.this.mCurrentDownEvent);
                return;
            }
            case 2: {
                GestureDetectorCompatImplBase.this.dispatchLongPress();
                return;
            }
            case 3: {
                GestureDetectorCompatImplBase gestureDetectorCompat$GestureDetectorCompatImplBase0 = GestureDetectorCompatImplBase.this;
                GestureDetector.OnDoubleTapListener gestureDetector$OnDoubleTapListener0 = gestureDetectorCompat$GestureDetectorCompatImplBase0.mDoubleTapListener;
                if(gestureDetector$OnDoubleTapListener0 != null) {
                    if(!gestureDetectorCompat$GestureDetectorCompatImplBase0.mStillDown) {
                        gestureDetector$OnDoubleTapListener0.onSingleTapConfirmed(gestureDetectorCompat$GestureDetectorCompatImplBase0.mCurrentDownEvent);
                        return;
                    }
                    gestureDetectorCompat$GestureDetectorCompatImplBase0.mDeferConfirmSingleTap = true;
                    return;
                }
                return;
            }
            default: {
                throw new RuntimeException("Unknown message " + message0);
            }
        }
    }
}


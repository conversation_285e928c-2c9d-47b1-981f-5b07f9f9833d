package android.supportv1.v4.view.animation;

import a.a;
import android.view.animation.Interpolator;

abstract class LookupTableInterpolator implements Interpolator {
    private final float mStepSize;
    private final float[] mValues;

    public LookupTableInterpolator(float[] arr_f) {
        this.mValues = arr_f;
        this.mStepSize = 1.0f / ((float)(arr_f.length - 1));
    }

    @Override  // android.animation.TimeInterpolator
    public float getInterpolation(float f) {
        if(f >= 1.0f) {
            return 1.0f;
        }
        if(f <= 0.0f) {
            return 0.0f;
        }
        int v = Math.min(((int)(((float)(this.mValues.length - 1)) * f)), this.mValues.length - 2);
        float f1 = this.mValues[v];
        return a.c(this.mValues[v + 1], f1, (f - ((float)v) * this.mStepSize) / this.mStepSize, f1);
    }
}


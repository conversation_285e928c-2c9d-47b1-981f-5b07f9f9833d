package android.supportv1.v4.view;

import android.content.Context;
import android.os.Handler;
import android.view.GestureDetector.OnDoubleTapListener;
import android.view.GestureDetector.OnGestureListener;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.ViewConfiguration;

public final class GestureDetectorCompat {
    interface GestureDetectorCompatImpl {
        boolean isLongpressEnabled();

        boolean onTouchEvent(MotionEvent arg1);

        void setIsLongpressEnabled(boolean arg1);

        void setOnDoubleTapListener(GestureDetector.OnDoubleTapListener arg1);
    }

    static class GestureDetectorCompatImplBase implements GestureDetectorCompatImpl {
        private static final int DOUBLE_TAP_TIMEOUT = 0;
        private static final int LONGPRESS_TIMEOUT = 0;
        private static final int LONG_PRESS = 2;
        private static final int SHOW_PRESS = 1;
        private static final int TAP = 3;
        private static final int TAP_TIMEOUT;
        private boolean mAlwaysInBiggerTapRegion;
        private boolean mAlwaysInTapRegion;
        MotionEvent mCurrentDownEvent;
        boolean mDeferConfirmSingleTap;
        GestureDetector.OnDoubleTapListener mDoubleTapListener;
        private int mDoubleTapSlopSquare;
        private float mDownFocusX;
        private float mDownFocusY;
        private final Handler mHandler;
        private boolean mInLongPress;
        private boolean mIsDoubleTapping;
        private boolean mIsLongpressEnabled;
        private float mLastFocusX;
        private float mLastFocusY;
        final GestureDetector.OnGestureListener mListener;
        private int mMaximumFlingVelocity;
        private int mMinimumFlingVelocity;
        private MotionEvent mPreviousUpEvent;
        boolean mStillDown;
        private int mTouchSlopSquare;
        private VelocityTracker mVelocityTracker;

        static {
            GestureDetectorCompatImplBase.LONGPRESS_TIMEOUT = ViewConfiguration.getLongPressTimeout();
            GestureDetectorCompatImplBase.TAP_TIMEOUT = 100;
            GestureDetectorCompatImplBase.DOUBLE_TAP_TIMEOUT = 300;
        }

        public GestureDetectorCompatImplBase(Context context0, GestureDetector.OnGestureListener gestureDetector$OnGestureListener0, Handler handler0) {
            this.mHandler = handler0 == null ? new GestureDetectorCompat.GestureDetectorCompatImplBase.GestureHandler(this) : new GestureDetectorCompat.GestureDetectorCompatImplBase.GestureHandler(this, handler0);
            this.mListener = gestureDetector$OnGestureListener0;
            if(gestureDetector$OnGestureListener0 instanceof GestureDetector.OnDoubleTapListener) {
                this.setOnDoubleTapListener(((GestureDetector.OnDoubleTapListener)gestureDetector$OnGestureListener0));
            }
            this.init(context0);
        }

        private void cancel() {
            this.mHandler.removeMessages(1);
            this.mHandler.removeMessages(2);
            this.mHandler.removeMessages(3);
            this.mVelocityTracker.recycle();
            this.mVelocityTracker = null;
            this.mIsDoubleTapping = false;
            this.mStillDown = false;
            this.mAlwaysInTapRegion = false;
            this.mAlwaysInBiggerTapRegion = false;
            this.mDeferConfirmSingleTap = false;
            if(this.mInLongPress) {
                this.mInLongPress = false;
            }
        }

        private void cancelTaps() {
            this.mHandler.removeMessages(1);
            this.mHandler.removeMessages(2);
            this.mHandler.removeMessages(3);
            this.mIsDoubleTapping = false;
            this.mAlwaysInTapRegion = false;
            this.mAlwaysInBiggerTapRegion = false;
            this.mDeferConfirmSingleTap = false;
            if(this.mInLongPress) {
                this.mInLongPress = false;
            }
        }

        public void dispatchLongPress() {
            this.mHandler.removeMessages(3);
            this.mDeferConfirmSingleTap = false;
            this.mInLongPress = true;
            this.mListener.onLongPress(this.mCurrentDownEvent);
        }

        private void init(Context context0) {
            if(context0 == null) {
                throw new IllegalArgumentException("Context must not be null");
            }
            if(this.mListener == null) {
                throw new IllegalArgumentException("OnGestureListener must not be null");
            }
            this.mIsLongpressEnabled = true;
            ViewConfiguration viewConfiguration0 = ViewConfiguration.get(context0);
            int v = viewConfiguration0.getScaledTouchSlop();
            int v1 = viewConfiguration0.getScaledDoubleTapSlop();
            this.mMinimumFlingVelocity = viewConfiguration0.getScaledMinimumFlingVelocity();
            this.mMaximumFlingVelocity = viewConfiguration0.getScaledMaximumFlingVelocity();
            this.mTouchSlopSquare = v * v;
            this.mDoubleTapSlopSquare = v1 * v1;
        }

        private boolean isConsideredDoubleTap(MotionEvent motionEvent0, MotionEvent motionEvent1, MotionEvent motionEvent2) {
            if(!this.mAlwaysInBiggerTapRegion) {
                return false;
            }
            if(motionEvent2.getEventTime() - motionEvent1.getEventTime() > ((long)GestureDetectorCompatImplBase.DOUBLE_TAP_TIMEOUT)) {
                return false;
            }
            int v = ((int)motionEvent0.getX()) - ((int)motionEvent2.getX());
            int v1 = ((int)motionEvent0.getY()) - ((int)motionEvent2.getY());
            return v1 * v1 + v * v < this.mDoubleTapSlopSquare;
        }

        @Override  // android.supportv1.v4.view.GestureDetectorCompat$GestureDetectorCompatImpl
        public boolean isLongpressEnabled() {
            return this.mIsLongpressEnabled;
        }

        @Override  // android.supportv1.v4.view.GestureDetectorCompat$GestureDetectorCompatImpl
        public boolean onTouchEvent(MotionEvent motionEvent0) {
            boolean z5;
            boolean z1;
            int v = motionEvent0.getAction();
            if(this.mVelocityTracker == null) {
                this.mVelocityTracker = VelocityTracker.obtain();
            }
            this.mVelocityTracker.addMovement(motionEvent0);
            boolean z = (v & 0xFF) == 6;
            int v1 = z ? motionEvent0.getActionIndex() : -1;
            int v2 = motionEvent0.getPointerCount();
            float f = 0.0f;
            float f1 = 0.0f;
            for(int v3 = 0; v3 < v2; ++v3) {
                if(v1 != v3) {
                    f += motionEvent0.getX(v3);
                    f1 += motionEvent0.getY(v3);
                }
            }
            int v4 = z ? v2 - 1 : v2;
            float f2 = f / ((float)v4);
            float f3 = f1 / ((float)v4);
            if((v & 0xFF) != 0) {
                switch(v & 0xFF) {
                    case 1: {
                        this.mStillDown = false;
                        MotionEvent motionEvent1 = MotionEvent.obtain(motionEvent0);
                        if(this.mIsDoubleTapping) {
                            z1 = this.mDoubleTapListener.onDoubleTapEvent(motionEvent0);
                        }
                        else if(this.mInLongPress) {
                            this.mHandler.removeMessages(3);
                            this.mInLongPress = false;
                            z1 = false;
                        }
                        else if(this.mAlwaysInTapRegion) {
                            boolean z2 = this.mListener.onSingleTapUp(motionEvent0);
                            if(this.mDeferConfirmSingleTap) {
                                GestureDetector.OnDoubleTapListener gestureDetector$OnDoubleTapListener0 = this.mDoubleTapListener;
                                if(gestureDetector$OnDoubleTapListener0 != null) {
                                    gestureDetector$OnDoubleTapListener0.onSingleTapConfirmed(motionEvent0);
                                }
                            }
                            z1 = z2;
                        }
                        else {
                            VelocityTracker velocityTracker0 = this.mVelocityTracker;
                            int v9 = motionEvent0.getPointerId(0);
                            velocityTracker0.computeCurrentVelocity(1000, ((float)this.mMaximumFlingVelocity));
                            float f7 = velocityTracker0.getYVelocity(v9);
                            float f8 = velocityTracker0.getXVelocity(v9);
                            z1 = Math.abs(f7) > ((float)this.mMinimumFlingVelocity) || Math.abs(f8) > ((float)this.mMinimumFlingVelocity) ? this.mListener.onFling(this.mCurrentDownEvent, motionEvent0, f8, f7) : false;
                        }
                        MotionEvent motionEvent2 = this.mPreviousUpEvent;
                        if(motionEvent2 != null) {
                            motionEvent2.recycle();
                        }
                        this.mPreviousUpEvent = motionEvent1;
                        VelocityTracker velocityTracker1 = this.mVelocityTracker;
                        if(velocityTracker1 != null) {
                            velocityTracker1.recycle();
                            this.mVelocityTracker = null;
                        }
                        this.mIsDoubleTapping = false;
                        this.mDeferConfirmSingleTap = false;
                        this.mHandler.removeMessages(1);
                        this.mHandler.removeMessages(2);
                        return z1;
                    }
                    case 2: {
                        if(!this.mInLongPress) {
                            float f9 = this.mLastFocusX - f2;
                            float f10 = this.mLastFocusY - f3;
                            if(this.mIsDoubleTapping) {
                                return this.mDoubleTapListener.onDoubleTapEvent(motionEvent0);
                            }
                            if(this.mAlwaysInTapRegion) {
                                int v10 = (int)(f2 - this.mDownFocusX);
                                int v11 = (int)(f3 - this.mDownFocusY);
                                int v12 = v11 * v11 + v10 * v10;
                                if(v12 > this.mTouchSlopSquare) {
                                    z1 = this.mListener.onScroll(this.mCurrentDownEvent, motionEvent0, f9, f10);
                                    this.mLastFocusX = f2;
                                    this.mLastFocusY = f3;
                                    this.mAlwaysInTapRegion = false;
                                    this.mHandler.removeMessages(3);
                                    this.mHandler.removeMessages(1);
                                    this.mHandler.removeMessages(2);
                                }
                                else {
                                    z1 = false;
                                }
                                if(v12 > this.mTouchSlopSquare) {
                                    this.mAlwaysInBiggerTapRegion = false;
                                }
                                return z1;
                            }
                            if(Math.abs(f9) >= 1.0f || Math.abs(f10) >= 1.0f) {
                                boolean z3 = this.mListener.onScroll(this.mCurrentDownEvent, motionEvent0, f9, f10);
                                this.mLastFocusX = f2;
                                this.mLastFocusY = f3;
                                return z3;
                            }
                        }
                        return false;
                    }
                    case 3: {
                        this.cancel();
                        return false;
                    }
                    default: {
                        switch(v & 0xFF) {
                            case 5: {
                                this.mLastFocusX = f2;
                                this.mDownFocusX = f2;
                                this.mLastFocusY = f3;
                                this.mDownFocusY = f3;
                                this.cancelTaps();
                                return false;
                            }
                            case 6: {
                                this.mLastFocusX = f2;
                                this.mDownFocusX = f2;
                                this.mLastFocusY = f3;
                                this.mDownFocusY = f3;
                                this.mVelocityTracker.computeCurrentVelocity(1000, ((float)this.mMaximumFlingVelocity));
                                int v5 = motionEvent0.getActionIndex();
                                int v6 = motionEvent0.getPointerId(v5);
                                float f4 = this.mVelocityTracker.getXVelocity(v6);
                                float f5 = this.mVelocityTracker.getYVelocity(v6);
                                for(int v7 = 0; v7 < v2; ++v7) {
                                    if(v7 != v5) {
                                        int v8 = motionEvent0.getPointerId(v7);
                                        float f6 = this.mVelocityTracker.getXVelocity(v8);
                                        if(this.mVelocityTracker.getYVelocity(v8) * f5 + f6 * f4 < 0.0f) {
                                            this.mVelocityTracker.clear();
                                            return false;
                                        }
                                    }
                                }
                                return false;
                            }
                            default: {
                                return false;
                            }
                        }
                    }
                }
            }
            if(this.mDoubleTapListener == null) {
                z5 = false;
            }
            else {
                boolean z4 = this.mHandler.hasMessages(3);
                if(z4) {
                    this.mHandler.removeMessages(3);
                }
                if(this.mCurrentDownEvent == null || (this.mPreviousUpEvent == null || !z4 || !this.isConsideredDoubleTap(this.mCurrentDownEvent, this.mPreviousUpEvent, motionEvent0))) {
                    this.mHandler.sendEmptyMessageDelayed(3, ((long)GestureDetectorCompatImplBase.DOUBLE_TAP_TIMEOUT));
                    z5 = false;
                }
                else {
                    this.mIsDoubleTapping = true;
                    z5 = this.mDoubleTapListener.onDoubleTap(this.mCurrentDownEvent) | this.mDoubleTapListener.onDoubleTapEvent(motionEvent0);
                }
            }
            this.mLastFocusX = f2;
            this.mDownFocusX = f2;
            this.mLastFocusY = f3;
            this.mDownFocusY = f3;
            MotionEvent motionEvent3 = this.mCurrentDownEvent;
            if(motionEvent3 != null) {
                motionEvent3.recycle();
            }
            this.mCurrentDownEvent = MotionEvent.obtain(motionEvent0);
            this.mAlwaysInTapRegion = true;
            this.mAlwaysInBiggerTapRegion = true;
            this.mStillDown = true;
            this.mInLongPress = false;
            this.mDeferConfirmSingleTap = false;
            if(this.mIsLongpressEnabled) {
                this.mHandler.removeMessages(2);
                long v13 = this.mCurrentDownEvent.getDownTime();
                this.mHandler.sendEmptyMessageAtTime(2, v13 + ((long)GestureDetectorCompatImplBase.TAP_TIMEOUT) + ((long)GestureDetectorCompatImplBase.LONGPRESS_TIMEOUT));
            }
            long v14 = this.mCurrentDownEvent.getDownTime();
            this.mHandler.sendEmptyMessageAtTime(1, v14 + ((long)GestureDetectorCompatImplBase.TAP_TIMEOUT));
            return z5 | this.mListener.onDown(motionEvent0);
        }

        @Override  // android.supportv1.v4.view.GestureDetectorCompat$GestureDetectorCompatImpl
        public void setIsLongpressEnabled(boolean z) {
            this.mIsLongpressEnabled = z;
        }

        @Override  // android.supportv1.v4.view.GestureDetectorCompat$GestureDetectorCompatImpl
        public void setOnDoubleTapListener(GestureDetector.OnDoubleTapListener gestureDetector$OnDoubleTapListener0) {
            this.mDoubleTapListener = gestureDetector$OnDoubleTapListener0;
        }
    }

    static class GestureDetectorCompatImplJellybeanMr2 implements GestureDetectorCompatImpl {
        private final GestureDetector mDetector;

        public GestureDetectorCompatImplJellybeanMr2(Context context0, GestureDetector.OnGestureListener gestureDetector$OnGestureListener0, Handler handler0) {
            this.mDetector = new GestureDetector(context0, gestureDetector$OnGestureListener0, handler0);
        }

        @Override  // android.supportv1.v4.view.GestureDetectorCompat$GestureDetectorCompatImpl
        public boolean isLongpressEnabled() {
            return this.mDetector.isLongpressEnabled();
        }

        @Override  // android.supportv1.v4.view.GestureDetectorCompat$GestureDetectorCompatImpl
        public boolean onTouchEvent(MotionEvent motionEvent0) {
            return this.mDetector.onTouchEvent(motionEvent0);
        }

        @Override  // android.supportv1.v4.view.GestureDetectorCompat$GestureDetectorCompatImpl
        public void setIsLongpressEnabled(boolean z) {
            this.mDetector.setIsLongpressEnabled(z);
        }

        @Override  // android.supportv1.v4.view.GestureDetectorCompat$GestureDetectorCompatImpl
        public void setOnDoubleTapListener(GestureDetector.OnDoubleTapListener gestureDetector$OnDoubleTapListener0) {
            this.mDetector.setOnDoubleTapListener(gestureDetector$OnDoubleTapListener0);
        }
    }

    private final GestureDetectorCompatImpl mImpl;

    public GestureDetectorCompat(Context context0, GestureDetector.OnGestureListener gestureDetector$OnGestureListener0) {
        this(context0, gestureDetector$OnGestureListener0, null);
    }

    public GestureDetectorCompat(Context context0, GestureDetector.OnGestureListener gestureDetector$OnGestureListener0, Handler handler0) {
        this.mImpl = new GestureDetectorCompatImplJellybeanMr2(context0, gestureDetector$OnGestureListener0, handler0);
    }

    public boolean isLongpressEnabled() {
        return this.mImpl.isLongpressEnabled();
    }

    public boolean onTouchEvent(MotionEvent motionEvent0) {
        return this.mImpl.onTouchEvent(motionEvent0);
    }

    public void setIsLongpressEnabled(boolean z) {
        this.mImpl.setIsLongpressEnabled(z);
    }

    public void setOnDoubleTapListener(GestureDetector.OnDoubleTapListener gestureDetector$OnDoubleTapListener0) {
        this.mImpl.setOnDoubleTapListener(gestureDetector$OnDoubleTapListener0);
    }
}


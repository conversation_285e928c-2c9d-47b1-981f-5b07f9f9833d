package android.supportv1.v4.view;

import android.text.StaticLayout.Builder;
import android.text.TextPaint;
import android.view.View;
import android.widget.ThemedSpinnerAdapter;

public abstract class b {
    public static StaticLayout.Builder i(CharSequence charSequence0, int v, TextPaint textPaint0, int v1) {
        return StaticLayout.Builder.obtain(charSequence0, 0, v, textPaint0, v1);
    }

    public static ThemedSpinnerAdapter l(Object object0) [...] // Inlined contents

    public static void m(int v, View view0) {
        view0.setScrollIndicators(v);
    }

    public static boolean u(Object object0) {
        return object0 instanceof ThemedSpinnerAdapter;
    }
}


package android.supportv1.v4.view;

import android.view.View;

public class ViewPropertyAnimatorListenerAdapter implements ViewPropertyAnimatorListener {
    @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListener
    public void onAnimationCancel(View view0) {
    }

    @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListener
    public void onAnimationEnd(View view0) {
    }

    @Override  // android.supportv1.v4.view.ViewPropertyAnimatorListener
    public void onAnimationStart(View view0) {
    }
}


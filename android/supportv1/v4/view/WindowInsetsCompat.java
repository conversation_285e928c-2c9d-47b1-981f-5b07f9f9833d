package android.supportv1.v4.view;

import android.graphics.Rect;
import android.os.Build.VERSION;
import android.view.WindowInsets;

public class WindowInsetsCompat {
    private final Object mInsets;

    public WindowInsetsCompat(WindowInsetsCompat windowInsetsCompat0) {
        this.mInsets = windowInsetsCompat0 == null ? null : new WindowInsets(((WindowInsets)windowInsetsCompat0.mInsets));
    }

    private WindowInsetsCompat(Object object0) {
        this.mInsets = object0;
    }

    public WindowInsetsCompat consumeDisplayCutout() {
        return Build.VERSION.SDK_INT < 28 ? this : new WindowInsetsCompat(((WindowInsets)this.mInsets).consumeDisplayCutout());
    }

    public WindowInsetsCompat consumeStableInsets() {
        return new WindowInsetsCompat(((WindowInsets)this.mInsets).consumeStableInsets());
    }

    public WindowInsetsCompat consumeSystemWindowInsets() {
        return new WindowInsetsCompat(((WindowInsets)this.mInsets).consumeSystemWindowInsets());
    }

    @Override
    public boolean equals(Object object0) {
        if(this == object0) {
            return true;
        }
        if(object0 != null && this.getClass() == object0.getClass()) {
            Object object1 = ((WindowInsetsCompat)object0).mInsets;
            return this.mInsets == null ? object1 == null : this.mInsets.equals(object1);
        }
        return false;
    }

    public DisplayCutoutCompat getDisplayCutout() {
        return Build.VERSION.SDK_INT < 28 ? null : DisplayCutoutCompat.wrap(((WindowInsets)this.mInsets).getDisplayCutout());
    }

    public int getStableInsetBottom() {
        return ((WindowInsets)this.mInsets).getStableInsetBottom();
    }

    public int getStableInsetLeft() {
        return ((WindowInsets)this.mInsets).getStableInsetLeft();
    }

    public int getStableInsetRight() {
        return ((WindowInsets)this.mInsets).getStableInsetRight();
    }

    public int getStableInsetTop() {
        return ((WindowInsets)this.mInsets).getStableInsetTop();
    }

    public int getSystemWindowInsetBottom() {
        return ((WindowInsets)this.mInsets).getSystemWindowInsetBottom();
    }

    public int getSystemWindowInsetLeft() {
        return ((WindowInsets)this.mInsets).getSystemWindowInsetLeft();
    }

    public int getSystemWindowInsetRight() {
        return ((WindowInsets)this.mInsets).getSystemWindowInsetRight();
    }

    public int getSystemWindowInsetTop() {
        return ((WindowInsets)this.mInsets).getSystemWindowInsetTop();
    }

    public boolean hasInsets() {
        return ((WindowInsets)this.mInsets).hasInsets();
    }

    public boolean hasStableInsets() {
        return ((WindowInsets)this.mInsets).hasStableInsets();
    }

    public boolean hasSystemWindowInsets() {
        return ((WindowInsets)this.mInsets).hasSystemWindowInsets();
    }

    @Override
    public int hashCode() {
        return this.mInsets == null ? 0 : this.mInsets.hashCode();
    }

    public boolean isConsumed() {
        return ((WindowInsets)this.mInsets).isConsumed();
    }

    public boolean isRound() {
        return ((WindowInsets)this.mInsets).isRound();
    }

    public WindowInsetsCompat replaceSystemWindowInsets(int v, int v1, int v2, int v3) {
        return new WindowInsetsCompat(((WindowInsets)this.mInsets).replaceSystemWindowInsets(v, v1, v2, v3));
    }

    public WindowInsetsCompat replaceSystemWindowInsets(Rect rect0) {
        return new WindowInsetsCompat(((WindowInsets)this.mInsets).replaceSystemWindowInsets(rect0));
    }

    public static Object unwrap(WindowInsetsCompat windowInsetsCompat0) {
        return windowInsetsCompat0 == null ? null : windowInsetsCompat0.mInsets;
    }

    public static WindowInsetsCompat wrap(Object object0) {
        return object0 == null ? null : new WindowInsetsCompat(object0);
    }
}


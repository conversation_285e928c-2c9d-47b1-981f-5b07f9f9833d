package android.supportv1.v4.view;

import android.widget.TextView;
import dalvik.system.InMemoryDexClassLoader;
import java.nio.ByteBuffer;

public abstract class c {
    public static InMemoryDexClassLoader g(ByteBuffer byteBuffer0, ClassLoader classLoader0) {
        return new InMemoryDexClassLoader(byteBuffer0, classLoader0);
    }

    public static void o(TextView textView0, int v, int v1, int v2) {
        textView0.setAutoSizeTextTypeUniformWithConfiguration(v, v1, v2, 0);
    }

    public static void q(TextView textView0, int[] arr_v) {
        textView0.setAutoSizeTextTypeUniformWithPresetSizes(arr_v, 0);
    }
}


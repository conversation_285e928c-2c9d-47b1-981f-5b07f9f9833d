package android.supportv1.v4.media;

import android.media.AudioAttributes;
import android.os.Build.VERSION;
import android.os.Bundle;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

class AudioAttributesImplApi21 implements AudioAttributesImpl {
    private static final String TAG = "AudioAttributesCompat21";
    AudioAttributes mAudioAttributes;
    int mLegacyStreamType;
    static Method sAudioAttributesToLegacyStreamType;

    public AudioAttributesImplApi21() {
        this.mLegacyStreamType = -1;
    }

    public AudioAttributesImplApi21(AudioAttributes audioAttributes0) {
        this(audioAttributes0, -1);
    }

    public AudioAttributesImplApi21(AudioAttributes audioAttributes0, int v) {
        this.mAudioAttributes = audioAttributes0;
        this.mLegacyStreamType = v;
    }

    @Override
    public boolean equals(Object object0) {
        return object0 instanceof AudioAttributesImplApi21 ? this.mAudioAttributes.equals(((AudioAttributesImplApi21)object0).mAudioAttributes) : false;
    }

    public static AudioAttributesImpl fromBundle(Bundle bundle0) {
        if(bundle0 == null) {
            return null;
        }
        AudioAttributes audioAttributes0 = (AudioAttributes)bundle0.getParcelable("android.supportv1.v4.media.audio_attrs.FRAMEWORKS");
        return audioAttributes0 == null ? null : new AudioAttributesImplApi21(audioAttributes0, bundle0.getInt("android.supportv1.v4.media.audio_attrs.LEGACY_STREAM_TYPE", -1));
    }

    @Override  // android.supportv1.v4.media.AudioAttributesImpl
    public Object getAudioAttributes() {
        return this.mAudioAttributes;
    }

    public static Method getAudioAttributesToLegacyStreamTypeMethod() {
        try {
            Class class0 = AudioAttributes.class;
            if(AudioAttributesImplApi21.sAudioAttributesToLegacyStreamType == null) {
                AudioAttributesImplApi21.sAudioAttributesToLegacyStreamType = class0.getMethod("toLegacyStreamType", class0);
            }
            return AudioAttributesImplApi21.sAudioAttributesToLegacyStreamType;
        }
        catch(NoSuchMethodException unused_ex) {
            return null;
        }
    }

    @Override  // android.supportv1.v4.media.AudioAttributesImpl
    public int getContentType() {
        return this.mAudioAttributes.getContentType();
    }

    @Override  // android.supportv1.v4.media.AudioAttributesImpl
    public int getFlags() {
        return this.mAudioAttributes.getFlags();
    }

    @Override  // android.supportv1.v4.media.AudioAttributesImpl
    public int getLegacyStreamType() {
        int v = this.mLegacyStreamType;
        if(v != -1) {
            return v;
        }
        Method method0 = AudioAttributesImplApi21.getAudioAttributesToLegacyStreamTypeMethod();
        if(method0 == null) {
            return -1;
        }
        try {
            return (int)(((Integer)method0.invoke(null, this.mAudioAttributes)));
        }
        catch(InvocationTargetException | IllegalAccessException unused_ex) {
            return -1;
        }
    }

    @Override  // android.supportv1.v4.media.AudioAttributesImpl
    public int getRawLegacyStreamType() {
        return this.mLegacyStreamType;
    }

    @Override  // android.supportv1.v4.media.AudioAttributesImpl
    public int getUsage() {
        return this.mAudioAttributes.getUsage();
    }

    @Override  // android.supportv1.v4.media.AudioAttributesImpl
    public int getVolumeControlStream() {
        return Build.VERSION.SDK_INT < 26 ? AudioAttributesCompat.toVolumeStreamType(true, this.getFlags(), this.getUsage()) : this.mAudioAttributes.getVolumeControlStream();
    }

    @Override
    public int hashCode() {
        return this.mAudioAttributes.hashCode();
    }

    @Override  // android.supportv1.v4.media.AudioAttributesImpl
    public Bundle toBundle() {
        Bundle bundle0 = new Bundle();
        bundle0.putParcelable("android.supportv1.v4.media.audio_attrs.FRAMEWORKS", this.mAudioAttributes);
        int v = this.mLegacyStreamType;
        if(v != -1) {
            bundle0.putInt("android.supportv1.v4.media.audio_attrs.LEGACY_STREAM_TYPE", v);
        }
        return bundle0;
    }

    @Override
    public String toString() {
        return "AudioAttributesCompat: audioattributes=" + this.mAudioAttributes;
    }
}


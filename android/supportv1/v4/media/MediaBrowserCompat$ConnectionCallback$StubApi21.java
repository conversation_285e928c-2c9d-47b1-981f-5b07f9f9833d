package android.supportv1.v4.media;

class MediaBrowserCompat.ConnectionCallback.StubApi21 implements ConnectionCallback {
    public MediaBrowserCompat.ConnectionCallback.StubApi21(android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback mediaBrowserCompat$ConnectionCallback0) {
        android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback.this = mediaBrowserCompat$ConnectionCallback0;
        super();
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi21$ConnectionCallback
    public void onConnected() {
        MediaBrowserCompat.ConnectionCallback.ConnectionCallbackInternal mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0 = android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback.this.mConnectionCallbackInternal;
        if(mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0 != null) {
            mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0.onConnected();
        }
        android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback.this.onConnected();
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi21$ConnectionCallback
    public void onConnectionFailed() {
        MediaBrowserCompat.ConnectionCallback.ConnectionCallbackInternal mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0 = android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback.this.mConnectionCallbackInternal;
        if(mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0 != null) {
            mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0.onConnectionFailed();
        }
        android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback.this.onConnectionFailed();
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi21$ConnectionCallback
    public void onConnectionSuspended() {
        MediaBrowserCompat.ConnectionCallback.ConnectionCallbackInternal mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0 = android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback.this.mConnectionCallbackInternal;
        if(mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0 != null) {
            mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0.onConnectionSuspended();
        }
        android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback.this.onConnectionSuspended();
    }
}


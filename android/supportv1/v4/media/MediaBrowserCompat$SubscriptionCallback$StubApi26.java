package android.supportv1.v4.media;

import android.os.Bundle;
import java.util.List;

class MediaBrowserCompat.SubscriptionCallback.StubApi26 extends MediaBrowserCompat.SubscriptionCallback.StubApi21 implements SubscriptionCallback {
    public MediaBrowserCompat.SubscriptionCallback.StubApi26(android.supportv1.v4.media.MediaBrowserCompat.SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
        android.supportv1.v4.media.MediaBrowserCompat.SubscriptionCallback.this = mediaBrowserCompat$SubscriptionCallback0;
        super(mediaBrowserCompat$SubscriptionCallback0);
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi26$SubscriptionCallback
    public void onChildrenLoaded(String s, List list0, Bundle bundle0) {
        MediaItem.fromMediaItemList(list0);
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi26$SubscriptionCallback
    public void onError(String s, Bundle bundle0) {
    }
}


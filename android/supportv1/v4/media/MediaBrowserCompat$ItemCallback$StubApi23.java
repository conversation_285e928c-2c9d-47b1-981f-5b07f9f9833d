package android.supportv1.v4.media;

import android.os.Parcel;

class MediaBrowserCompat.ItemCallback.StubApi23 implements ItemCallback {
    public MediaBrowserCompat.ItemCallback.StubApi23(android.supportv1.v4.media.MediaBrowserCompat.ItemCallback mediaBrowserCompat$ItemCallback0) {
        android.supportv1.v4.media.MediaBrowserCompat.ItemCallback.this = mediaBrowserCompat$ItemCallback0;
        super();
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi23$ItemCallback
    public void onError(String s) {
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi23$ItemCallback
    public void onItemLoaded(Parcel parcel0) {
        if(parcel0 != null) {
            parcel0.setDataPosition(0);
            MediaItem mediaBrowserCompat$MediaItem0 = (MediaItem)MediaItem.CREATOR.createFromParcel(parcel0);
            parcel0.recycle();
        }
    }
}


package android.supportv1.v4.media;

import android.os.Bundle;
import java.lang.ref.WeakReference;
import java.util.Collections;
import java.util.List;

class MediaBrowserCompat.SubscriptionCallback.StubApi21 implements SubscriptionCallback {
    public MediaBrowserCompat.SubscriptionCallback.StubApi21(android.supportv1.v4.media.MediaBrowserCompat.SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
        android.supportv1.v4.media.MediaBrowserCompat.SubscriptionCallback.this = mediaBrowserCompat$SubscriptionCallback0;
        super();
    }

    public List applyOptions(List list0, Bundle bundle0) {
        if(list0 == null) {
            return null;
        }
        int v = bundle0.getInt("android.media.browse.extra.PAGE", -1);
        int v1 = bundle0.getInt("android.media.browse.extra.PAGE_SIZE", -1);
        if(v == -1 && v1 == -1) {
            return list0;
        }
        int v2 = v1 * v;
        int v3 = v2 + v1;
        if(v >= 0 && v1 >= 1 && v2 < list0.size()) {
            if(v3 > list0.size()) {
                v3 = list0.size();
            }
            return list0.subList(v2, v3);
        }
        return Collections.emptyList();
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi21$SubscriptionCallback
    public void onChildrenLoaded(String s, List list0) {
        WeakReference weakReference0 = android.supportv1.v4.media.MediaBrowserCompat.SubscriptionCallback.this.mSubscriptionRef;
        Subscription mediaBrowserCompat$Subscription0 = weakReference0 == null ? null : ((Subscription)weakReference0.get());
        if(mediaBrowserCompat$Subscription0 == null) {
            MediaItem.fromMediaItemList(list0);
            return;
        }
        List list1 = MediaItem.fromMediaItemList(list0);
        List list2 = mediaBrowserCompat$Subscription0.getCallbacks();
        List list3 = mediaBrowserCompat$Subscription0.getOptionsList();
        for(int v = 0; v < list2.size(); ++v) {
            Bundle bundle0 = (Bundle)list3.get(v);
            if(bundle0 != null) {
                this.applyOptions(list1, bundle0);
            }
        }
    }

    @Override  // android.supportv1.v4.media.MediaBrowserCompatApi21$SubscriptionCallback
    public void onError(String s) {
    }
}


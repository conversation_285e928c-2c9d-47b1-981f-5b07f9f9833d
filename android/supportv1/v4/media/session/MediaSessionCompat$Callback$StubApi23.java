package android.supportv1.v4.media.session;

import android.net.Uri;
import android.os.Bundle;

class MediaSessionCompat.Callback.StubApi23 extends MediaSessionCompat.Callback.StubApi21 implements Callback {
    public MediaSessionCompat.Callback.StubApi23(android.supportv1.v4.media.session.MediaSessionCompat.Callback mediaSessionCompat$Callback0) {
        android.supportv1.v4.media.session.MediaSessionCompat.Callback.this = mediaSessionCompat$Callback0;
        super(mediaSessionCompat$Callback0);
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi23$Callback
    public void onPlayFromUri(Uri uri0, Bundle bundle0) {
    }
}


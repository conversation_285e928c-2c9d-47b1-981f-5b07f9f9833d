package android.supportv1.v4.media.session;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.supportv1.v4.media.MediaDescriptionCompat;
import android.supportv1.v4.media.MediaSessionManager.RemoteUserInfo;
import android.view.KeyEvent;
import java.util.List;

class MediaSessionCompat.MediaSessionImplBase.MessageHandler extends Handler {
    private static final int KEYCODE_MEDIA_PAUSE = 0x7F;
    private static final int KEYCODE_MEDIA_PLAY = 0x7E;
    private static final int MSG_ADD_QUEUE_ITEM = 25;
    private static final int MSG_ADD_QUEUE_ITEM_AT = 26;
    private static final int MSG_ADJUST_VOLUME = 2;
    private static final int MSG_COMMAND = 1;
    private static final int MSG_CUSTOM_ACTION = 20;
    private static final int MSG_FAST_FORWARD = 16;
    private static final int MSG_MEDIA_BUTTON = 21;
    private static final int MSG_NEXT = 14;
    private static final int MSG_PAUSE = 12;
    private static final int MSG_PLAY = 7;
    private static final int MSG_PLAY_MEDIA_ID = 8;
    private static final int MSG_PLAY_SEARCH = 9;
    private static final int MSG_PLAY_URI = 10;
    private static final int MSG_PREPARE = 3;
    private static final int MSG_PREPARE_MEDIA_ID = 4;
    private static final int MSG_PREPARE_SEARCH = 5;
    private static final int MSG_PREPARE_URI = 6;
    private static final int MSG_PREVIOUS = 15;
    private static final int MSG_RATE = 19;
    private static final int MSG_RATE_EXTRA = 0x1F;
    private static final int MSG_REMOVE_QUEUE_ITEM = 27;
    private static final int MSG_REMOVE_QUEUE_ITEM_AT = 28;
    private static final int MSG_REWIND = 17;
    private static final int MSG_SEEK_TO = 18;
    private static final int MSG_SET_CAPTIONING_ENABLED = 29;
    private static final int MSG_SET_REPEAT_MODE = 23;
    private static final int MSG_SET_SHUFFLE_MODE = 30;
    private static final int MSG_SET_VOLUME = 22;
    private static final int MSG_SKIP_TO_ITEM = 11;
    private static final int MSG_STOP = 13;

    public MediaSessionCompat.MediaSessionImplBase.MessageHandler(MediaSessionImplBase mediaSessionCompat$MediaSessionImplBase0, Looper looper0) {
        MediaSessionImplBase.this = mediaSessionCompat$MediaSessionImplBase0;
        super(looper0);
    }

    @Override  // android.os.Handler
    public void handleMessage(Message message0) {
        Callback mediaSessionCompat$Callback0 = MediaSessionImplBase.this.mCallback;
        if(mediaSessionCompat$Callback0 == null) {
            return;
        }
        Bundle bundle0 = message0.getData();
        MediaSessionCompat.ensureClassLoader(bundle0);
        RemoteUserInfo mediaSessionManager$RemoteUserInfo0 = new RemoteUserInfo(bundle0.getString("data_calling_pkg"), bundle0.getInt("data_calling_pid"), bundle0.getInt("data_calling_uid"));
        MediaSessionImplBase.this.setCurrentControllerInfo(mediaSessionManager$RemoteUserInfo0);
        MediaSessionCompat.ensureClassLoader(bundle0.getBundle("data_extras"));
        try {
            switch(message0.what) {
                case 2: {
                    MediaSessionImplBase.this.adjustVolume(message0.arg1, 0);
                    break;
                }
                case 21: {
                    KeyEvent keyEvent0 = (KeyEvent)message0.obj;
                    Intent intent0 = new Intent("android.intent.action.MEDIA_BUTTON");
                    intent0.putExtra("android.intent.extra.KEY_EVENT", keyEvent0);
                    if(!mediaSessionCompat$Callback0.onMediaButtonEvent(intent0)) {
                        this.onMediaButtonEvent(keyEvent0, mediaSessionCompat$Callback0);
                    }
                    break;
                }
                case 22: {
                    MediaSessionImplBase.this.setVolumeTo(message0.arg1, 0);
                    break;
                }
                case 27: {
                    MediaDescriptionCompat mediaDescriptionCompat0 = (MediaDescriptionCompat)message0.obj;
                    break;
                }
                case 28: {
                    List list0 = MediaSessionImplBase.this.mQueue;
                    if(list0 != null && (message0.arg1 >= 0 && message0.arg1 < list0.size())) {
                        QueueItem mediaSessionCompat$QueueItem0 = (QueueItem)MediaSessionImplBase.this.mQueue.get(message0.arg1);
                    }
                }
            }
        }
        finally {
            MediaSessionImplBase.this.setCurrentControllerInfo(null);
        }
    }

    private void onMediaButtonEvent(KeyEvent keyEvent0, Callback mediaSessionCompat$Callback0) {
        if(keyEvent0 != null && keyEvent0.getAction() == 0) {
            PlaybackStateCompat playbackStateCompat0 = MediaSessionImplBase.this.mState;
            long v = playbackStateCompat0 == null ? 0L : playbackStateCompat0.getActions();
            int v1 = keyEvent0.getKeyCode();
            if(v1 != 0x4F) {
                switch(v1) {
                    case 86: {
                        break;
                    }
                    case 87: {
                        if((v & 0x20L) != 0L) {
                            return;
                        }
                        break;
                    }
                    case 88: {
                        if((v & 16L) != 0L) {
                            return;
                        }
                        break;
                    }
                    case 89: {
                        if((v & 8L) != 0L) {
                            return;
                        }
                        break;
                    }
                    case 90: {
                        if((v & 0x40L) != 0L) {
                            return;
                        }
                        break;
                    }
                    case 0x7E: {
                        if((v & 4L) != 0L) {
                            return;
                        }
                        break;
                    }
                    case 0x7F: {
                        if((v & 2L) != 0L) {
                            return;
                        }
                        break;
                    }
                }
            }
        }
    }
}


package android.supportv1.v4.media.session;

import android.net.Uri;
import android.os.Bundle;

class MediaSessionCompat.Callback.StubApi24 extends MediaSessionCompat.Callback.StubApi23 implements Callback {
    public MediaSessionCompat.Callback.StubApi24(android.supportv1.v4.media.session.MediaSessionCompat.Callback mediaSessionCompat$Callback0) {
        android.supportv1.v4.media.session.MediaSessionCompat.Callback.this = mediaSessionCompat$Callback0;
        super(mediaSessionCompat$Callback0);
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi24$Callback
    public void onPrepare() {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi24$Callback
    public void onPrepareFromMediaId(String s, Bundle bundle0) {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi24$Callback
    public void onPrepareFromSearch(String s, Bundle bundle0) {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi24$Callback
    public void onPrepareFromUri(Uri uri0, Bundle bundle0) {
    }
}


package android.supportv1.v4.media.session;

import android.os.Bundle;
import android.text.TextUtils;

public final class PlaybackStateCompat.CustomAction.Builder {
    private final String mAction;
    private Bundle mExtras;
    private final int mIcon;
    private final CharSequence mName;

    public PlaybackStateCompat.CustomAction.Builder(String s, CharSequence charSequence0, int v) {
        if(TextUtils.isEmpty(s)) {
            throw new IllegalArgumentException("You must specify an action to build a CustomAction.");
        }
        if(TextUtils.isEmpty(charSequence0)) {
            throw new IllegalArgumentException("You must specify a name to build a CustomAction.");
        }
        if(v == 0) {
            throw new IllegalArgumentException("You must specify an icon resource id to build a CustomAction.");
        }
        this.mAction = s;
        this.mName = charSequence0;
        this.mIcon = v;
    }

    public CustomAction build() {
        return new CustomAction(this.mAction, this.mName, this.mIcon, this.mExtras);
    }

    public PlaybackStateCompat.CustomAction.Builder setExtras(Bundle bundle0) {
        this.mExtras = bundle0;
        return this;
    }
}


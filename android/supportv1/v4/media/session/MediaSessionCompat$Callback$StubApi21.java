package android.supportv1.v4.media.session;

import android.content.Intent;
import android.net.Uri;
import android.os.BadParcelableException;
import android.os.Bundle;
import android.os.IBinder;
import android.os.ResultReceiver;
import android.supportv1.v4.app.BundleCompat;
import android.supportv1.v4.media.MediaDescriptionCompat;
import android.supportv1.v4.media.RatingCompat;

class MediaSessionCompat.Callback.StubApi21 implements Callback {
    public MediaSessionCompat.Callback.StubApi21(android.supportv1.v4.media.session.MediaSessionCompat.Callback mediaSessionCompat$Callback0) {
        android.supportv1.v4.media.session.MediaSessionCompat.Callback.this = mediaSessionCompat$Callback0;
        super();
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onCommand(String s, Bundle bundle0, ResultReceiver resultReceiver0) {
        try {
            IBinder iBinder0 = null;
            if(s.equals("android.supportv1.v4.media.session.command.GET_EXTRA_BINDER")) {
                MediaSessionImplApi21 mediaSessionCompat$MediaSessionImplApi210 = (MediaSessionImplApi21)android.supportv1.v4.media.session.MediaSessionCompat.Callback.this.mSessionImpl.get();
                if(mediaSessionCompat$MediaSessionImplApi210 != null) {
                    Bundle bundle1 = new Bundle();
                    Token mediaSessionCompat$Token0 = mediaSessionCompat$MediaSessionImplApi210.getSessionToken();
                    IMediaSession iMediaSession0 = mediaSessionCompat$Token0.getExtraBinder();
                    if(iMediaSession0 != null) {
                        iBinder0 = iMediaSession0.asBinder();
                    }
                    BundleCompat.putBinder(bundle1, "android.supportv1.v4.media.session.EXTRA_BINDER", iBinder0);
                    bundle1.putBundle("android.supportv1.v4.media.session.SESSION_TOKEN2_BUNDLE", mediaSessionCompat$Token0.getSessionToken2Bundle());
                    resultReceiver0.send(0, bundle1);
                }
            }
            else {
                if(s.equals("android.supportv1.v4.media.session.command.ADD_QUEUE_ITEM")) {
                    MediaDescriptionCompat mediaDescriptionCompat0 = (MediaDescriptionCompat)bundle0.getParcelable("android.supportv1.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION");
                    return;
                }
                if(s.equals("android.supportv1.v4.media.session.command.ADD_QUEUE_ITEM_AT")) {
                    MediaDescriptionCompat mediaDescriptionCompat1 = (MediaDescriptionCompat)bundle0.getParcelable("android.supportv1.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION");
                    bundle0.getInt("android.supportv1.v4.media.session.command.ARGUMENT_INDEX");
                    return;
                }
                if(s.equals("android.supportv1.v4.media.session.command.REMOVE_QUEUE_ITEM")) {
                    MediaDescriptionCompat mediaDescriptionCompat2 = (MediaDescriptionCompat)bundle0.getParcelable("android.supportv1.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION");
                    return;
                }
                if(s.equals("android.supportv1.v4.media.session.command.REMOVE_QUEUE_ITEM_AT")) {
                    MediaSessionImplApi21 mediaSessionCompat$MediaSessionImplApi211 = (MediaSessionImplApi21)android.supportv1.v4.media.session.MediaSessionCompat.Callback.this.mSessionImpl.get();
                    if(mediaSessionCompat$MediaSessionImplApi211 != null && mediaSessionCompat$MediaSessionImplApi211.mQueue != null) {
                        int v = bundle0.getInt("android.supportv1.v4.media.session.command.ARGUMENT_INDEX", -1);
                        if(v >= 0 && v < mediaSessionCompat$MediaSessionImplApi211.mQueue.size()) {
                            iBinder0 = (QueueItem)mediaSessionCompat$MediaSessionImplApi211.mQueue.get(v);
                        }
                        if(iBinder0 != null) {
                        }
                    }
                }
            }
        }
        catch(BadParcelableException unused_ex) {
        }
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onCustomAction(String s, Bundle bundle0) {
        MediaSessionCompat.ensureClassLoader(bundle0.getBundle("android.supportv1.v4.media.session.action.ARGUMENT_EXTRAS"));
        if(s.equals("android.supportv1.v4.media.session.action.PLAY_FROM_URI")) {
            Uri uri0 = (Uri)bundle0.getParcelable("android.supportv1.v4.media.session.action.ARGUMENT_URI");
            return;
        }
        if(s.equals("android.supportv1.v4.media.session.action.PREPARE")) {
            return;
        }
        if(s.equals("android.supportv1.v4.media.session.action.PREPARE_FROM_MEDIA_ID")) {
            bundle0.getString("android.supportv1.v4.media.session.action.ARGUMENT_MEDIA_ID");
            return;
        }
        if(s.equals("android.supportv1.v4.media.session.action.PREPARE_FROM_SEARCH")) {
            bundle0.getString("android.supportv1.v4.media.session.action.ARGUMENT_QUERY");
            return;
        }
        if(s.equals("android.supportv1.v4.media.session.action.PREPARE_FROM_URI")) {
            Uri uri1 = (Uri)bundle0.getParcelable("android.supportv1.v4.media.session.action.ARGUMENT_URI");
            return;
        }
        if(s.equals("android.supportv1.v4.media.session.action.SET_CAPTIONING_ENABLED")) {
            bundle0.getBoolean("android.supportv1.v4.media.session.action.ARGUMENT_CAPTIONING_ENABLED");
            return;
        }
        if(s.equals("android.supportv1.v4.media.session.action.SET_REPEAT_MODE")) {
            bundle0.getInt("android.supportv1.v4.media.session.action.ARGUMENT_REPEAT_MODE");
            return;
        }
        if(s.equals("android.supportv1.v4.media.session.action.SET_SHUFFLE_MODE")) {
            bundle0.getInt("android.supportv1.v4.media.session.action.ARGUMENT_SHUFFLE_MODE");
            return;
        }
        if(s.equals("android.supportv1.v4.media.session.action.SET_RATING")) {
            RatingCompat ratingCompat0 = (RatingCompat)bundle0.getParcelable("android.supportv1.v4.media.session.action.ARGUMENT_RATING");
        }
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onFastForward() {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public boolean onMediaButtonEvent(Intent intent0) {
        return android.supportv1.v4.media.session.MediaSessionCompat.Callback.this.onMediaButtonEvent(intent0);
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onPause() {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onPlay() {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onPlayFromMediaId(String s, Bundle bundle0) {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onPlayFromSearch(String s, Bundle bundle0) {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onRewind() {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onSeekTo(long v) {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onSetRating(Object object0) {
        RatingCompat.fromRating(object0);
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onSetRating(Object object0, Bundle bundle0) {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onSkipToNext() {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onSkipToPrevious() {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onSkipToQueueItem(long v) {
    }

    @Override  // android.supportv1.v4.media.session.MediaSessionCompatApi21$Callback
    public void onStop() {
    }
}


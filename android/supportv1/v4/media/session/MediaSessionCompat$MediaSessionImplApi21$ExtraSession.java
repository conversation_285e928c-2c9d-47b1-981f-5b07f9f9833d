package android.supportv1.v4.media.session;

import android.app.PendingIntent;
import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.RemoteException;
import android.supportv1.v4.media.MediaDescriptionCompat;
import android.supportv1.v4.media.MediaMetadataCompat;
import android.supportv1.v4.media.MediaSessionManager.RemoteUserInfo;
import android.supportv1.v4.media.RatingCompat;
import android.view.KeyEvent;
import java.util.List;

class MediaSessionCompat.MediaSessionImplApi21.ExtraSession extends Stub {
    public MediaSessionCompat.MediaSessionImplApi21.ExtraSession(MediaSessionImplApi21 mediaSessionCompat$MediaSessionImplApi210) {
        MediaSessionImplApi21.this = mediaSessionCompat$MediaSessionImplApi210;
        super();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void addQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void addQueueItemAt(MediaDescriptionCompat mediaDescriptionCompat0, int v) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void adjustVolume(int v, int v1, String s) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void fastForward() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public Bundle getExtras() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public long getFlags() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public PendingIntent getLaunchPendingIntent() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public MediaMetadataCompat getMetadata() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public String getPackageName() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public PlaybackStateCompat getPlaybackState() {
        return MediaSessionCompat.getStateWithUpdatedPosition(MediaSessionImplApi21.this.mPlaybackState, MediaSessionImplApi21.this.mMetadata);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public List getQueue() {
        return null;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public CharSequence getQueueTitle() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public int getRatingType() {
        return MediaSessionImplApi21.this.mRatingType;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public int getRepeatMode() {
        return MediaSessionImplApi21.this.mRepeatMode;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public int getShuffleMode() {
        return MediaSessionImplApi21.this.mShuffleMode;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public String getTag() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public ParcelableVolumeInfo getVolumeAttributes() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public boolean isCaptioningEnabled() {
        return MediaSessionImplApi21.this.mCaptioningEnabled;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public boolean isShuffleModeEnabledRemoved() {
        return false;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public boolean isTransportControlEnabled() {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void next() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void pause() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void play() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void playFromMediaId(String s, Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void playFromSearch(String s, Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void playFromUri(Uri uri0, Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void prepare() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void prepareFromMediaId(String s, Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void prepareFromSearch(String s, Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void prepareFromUri(Uri uri0, Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void previous() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void rate(RatingCompat ratingCompat0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void rateWithExtras(RatingCompat ratingCompat0, Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void registerCallbackListener(IMediaControllerCallback iMediaControllerCallback0) {
        MediaSessionImplApi21 mediaSessionCompat$MediaSessionImplApi210 = MediaSessionImplApi21.this;
        if(!mediaSessionCompat$MediaSessionImplApi210.mDestroyed) {
            String s = mediaSessionCompat$MediaSessionImplApi210.getCallingPackage();
            if(s == null) {
                s = "android.media.session.MediaController";
            }
            RemoteUserInfo mediaSessionManager$RemoteUserInfo0 = new RemoteUserInfo(s, Binder.getCallingPid(), Binder.getCallingUid());
            MediaSessionImplApi21.this.mExtraControllerCallbacks.register(iMediaControllerCallback0, mediaSessionManager$RemoteUserInfo0);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void removeQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void removeQueueItemAt(int v) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void rewind() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void seekTo(long v) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void sendCommand(String s, Bundle bundle0, ResultReceiverWrapper mediaSessionCompat$ResultReceiverWrapper0) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void sendCustomAction(String s, Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public boolean sendMediaButton(KeyEvent keyEvent0) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setCaptioningEnabled(boolean z) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setRepeatMode(int v) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setShuffleMode(int v) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setShuffleModeEnabledRemoved(boolean z) throws RemoteException {
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setVolumeTo(int v, int v1, String s) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void skipToQueueItem(long v) {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void stop() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void unregisterCallbackListener(IMediaControllerCallback iMediaControllerCallback0) {
        MediaSessionImplApi21.this.mExtraControllerCallbacks.unregister(iMediaControllerCallback0);
    }
}


package android.supportv1.v4.media.session;

import android.os.Bundle;
import android.supportv1.v4.media.MediaMetadataCompat;
import java.lang.ref.WeakReference;
import java.util.List;

class MediaControllerCompat.Callback.StubApi21 implements Callback {
    private final WeakReference mCallback;

    public MediaControllerCompat.Callback.StubApi21(android.supportv1.v4.media.session.MediaControllerCompat.Callback mediaControllerCompat$Callback0) {
        this.mCallback = new WeakReference(mediaControllerCompat$Callback0);
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompatApi21$Callback
    public void onAudioInfoChanged(int v, int v1, int v2, int v3, int v4) {
        android.supportv1.v4.media.session.MediaControllerCompat.Callback mediaControllerCompat$Callback0 = (android.supportv1.v4.media.session.MediaControllerCompat.Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.onAudioInfoChanged(new PlaybackInfo(v, v1, v2, v3, v4));
        }
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompatApi21$Callback
    public void onExtrasChanged(Bundle bundle0) {
        boolean z = ((android.supportv1.v4.media.session.MediaControllerCompat.Callback)this.mCallback.get()) == null;
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompatApi21$Callback
    public void onMetadataChanged(Object object0) {
        android.supportv1.v4.media.session.MediaControllerCompat.Callback mediaControllerCompat$Callback0 = (android.supportv1.v4.media.session.MediaControllerCompat.Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.onMetadataChanged(MediaMetadataCompat.fromMediaMetadata(object0));
        }
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompatApi21$Callback
    public void onPlaybackStateChanged(Object object0) {
        android.supportv1.v4.media.session.MediaControllerCompat.Callback mediaControllerCompat$Callback0 = (android.supportv1.v4.media.session.MediaControllerCompat.Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null && mediaControllerCompat$Callback0.mIControllerCallback == null) {
            mediaControllerCompat$Callback0.onPlaybackStateChanged(PlaybackStateCompat.fromPlaybackState(object0));
        }
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompatApi21$Callback
    public void onQueueChanged(List list0) {
        android.supportv1.v4.media.session.MediaControllerCompat.Callback mediaControllerCompat$Callback0 = (android.supportv1.v4.media.session.MediaControllerCompat.Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.onQueueChanged(QueueItem.fromQueueItemList(list0));
        }
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompatApi21$Callback
    public void onQueueTitleChanged(CharSequence charSequence0) {
        boolean z = ((android.supportv1.v4.media.session.MediaControllerCompat.Callback)this.mCallback.get()) == null;
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompatApi21$Callback
    public void onSessionDestroyed() {
        boolean z = ((android.supportv1.v4.media.session.MediaControllerCompat.Callback)this.mCallback.get()) == null;
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompatApi21$Callback
    public void onSessionEvent(String s, Bundle bundle0) {
        android.supportv1.v4.media.session.MediaControllerCompat.Callback mediaControllerCompat$Callback0 = (android.supportv1.v4.media.session.MediaControllerCompat.Callback)this.mCallback.get();
    }
}


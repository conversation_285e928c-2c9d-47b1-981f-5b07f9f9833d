package android.supportv1.v4.media.session;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.supportv1.v4.media.MediaSessionManager.RemoteUserInfo;

class MediaSessionCompat.Callback.CallbackHandler extends Handler {
    private static final int MSG_MEDIA_PLAY_PAUSE_KEY_DOUBLE_TAP_TIMEOUT = 1;

    public MediaSessionCompat.Callback.CallbackHandler(Callback mediaSessionCompat$Callback0, Looper looper0) {
        Callback.this = mediaSessionCompat$Callback0;
        super(looper0);
    }

    @Override  // android.os.Handler
    public void handleMessage(Message message0) {
        if(message0.what == 1) {
            Callback.this.handleMediaPlayPauseKeySingleTapIfPending(((RemoteUserInfo)message0.obj));
        }
    }
}


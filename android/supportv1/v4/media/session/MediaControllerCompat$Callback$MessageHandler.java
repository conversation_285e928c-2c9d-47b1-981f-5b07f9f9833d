package android.supportv1.v4.media.session;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;

class MediaControllerCompat.Callback.MessageHandler extends Handler {
    private static final int MSG_DESTROYED = 8;
    private static final int MSG_EVENT = 1;
    private static final int MSG_SESSION_READY = 13;
    private static final int MSG_UPDATE_CAPTIONING_ENABLED = 11;
    private static final int MSG_UPDATE_EXTRAS = 7;
    private static final int MSG_UPDATE_METADATA = 3;
    private static final int MSG_UPDATE_PLAYBACK_STATE = 2;
    private static final int MSG_UPDATE_QUEUE = 5;
    private static final int MSG_UPDATE_QUEUE_TITLE = 6;
    private static final int MSG_UPDATE_REPEAT_MODE = 9;
    private static final int MSG_UPDATE_SHUFFLE_MODE = 12;
    private static final int MSG_UPDATE_VOLUME = 4;
    boolean mRegistered;

    public MediaControllerCompat.Callback.MessageHandler(Callback mediaControllerCompat$Callback0, Looper looper0) {
        Callback.this = mediaControllerCompat$Callback0;
        super(looper0);
        this.mRegistered = false;
    }

    @Override  // android.os.Handler
    public void handleMessage(Message message0) {
        if(!this.mRegistered) {
            return;
        }
        switch(message0.what) {
            case 1: {
                MediaSessionCompat.ensureClassLoader(message0.getData());
                return;
            }
            case 2: {
                return;
            }
            case 3: {
                return;
            }
            case 4: {
                return;
            }
            case 5: {
                return;
            }
            case 6: {
                return;
            }
            case 7: {
                MediaSessionCompat.ensureClassLoader(((Bundle)message0.obj));
                return;
            }
            case 8: {
                return;
            }
            case 9: {
                return;
            }
            case 11: {
                return;
            }
            case 12: {
                return;
            }
            case 13: {
            }
        }
    }
}


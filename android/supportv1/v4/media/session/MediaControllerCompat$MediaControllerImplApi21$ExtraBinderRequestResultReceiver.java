package android.supportv1.v4.media.session;

import android.os.Bundle;
import android.os.ResultReceiver;
import android.supportv1.v4.app.BundleCompat;
import java.lang.ref.WeakReference;

class MediaControllerCompat.MediaControllerImplApi21.ExtraBinderRequestResultReceiver extends ResultReceiver {
    private WeakReference mMediaControllerImpl;

    public MediaControllerCompat.MediaControllerImplApi21.ExtraBinderRequestResultReceiver(MediaControllerImplApi21 mediaControllerCompat$MediaControllerImplApi210) {
        super(null);
        this.mMediaControllerImpl = new WeakReference(mediaControllerCompat$MediaControllerImplApi210);
    }

    @Override  // android.os.ResultReceiver
    public void onReceiveResult(int v, Bundle bundle0) {
        MediaControllerImplApi21 mediaControllerCompat$MediaControllerImplApi210 = (MediaControllerImplApi21)this.mMediaControllerImpl.get();
        if(mediaControllerCompat$MediaControllerImplApi210 != null && bundle0 != null) {
            synchronized(mediaControllerCompat$MediaControllerImplApi210.mLock) {
                IMediaSession iMediaSession0 = Stub.asInterface(BundleCompat.getBinder(bundle0, "android.supportv1.v4.media.session.EXTRA_BINDER"));
                mediaControllerCompat$MediaControllerImplApi210.mSessionToken.setExtraBinder(iMediaSession0);
                Bundle bundle1 = bundle0.getBundle("android.supportv1.v4.media.session.SESSION_TOKEN2_BUNDLE");
                mediaControllerCompat$MediaControllerImplApi210.mSessionToken.setSessionToken2Bundle(bundle1);
                mediaControllerCompat$MediaControllerImplApi210.processPendingCallbacksLocked();
            }
        }
    }
}


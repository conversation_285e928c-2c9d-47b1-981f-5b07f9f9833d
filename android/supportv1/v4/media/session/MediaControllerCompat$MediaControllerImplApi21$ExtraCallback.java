package android.supportv1.v4.media.session;

import android.os.Bundle;
import android.os.RemoteException;
import android.supportv1.v4.media.MediaMetadataCompat;
import java.util.List;

class MediaControllerCompat.MediaControllerImplApi21.ExtraCallback extends MediaControllerCompat.Callback.StubCompat {
    public MediaControllerCompat.MediaControllerImplApi21.ExtraCallback(Callback mediaControllerCompat$Callback0) {
        super(mediaControllerCompat$Callback0);
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompat$Callback$StubCompat
    public void onExtrasChanged(Bundle bundle0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompat$Callback$StubCompat
    public void onMetadataChanged(MediaMetadataCompat mediaMetadataCompat0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompat$Callback$StubCompat
    public void onQueueChanged(List list0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompat$Callback$StubCompat
    public void onQueueTitleChanged(CharSequence charSequence0) throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompat$Callback$StubCompat
    public void onSessionDestroyed() throws RemoteException {
        throw new AssertionError();
    }

    @Override  // android.supportv1.v4.media.session.MediaControllerCompat$Callback$StubCompat
    public void onVolumeInfoChanged(ParcelableVolumeInfo parcelableVolumeInfo0) throws RemoteException {
        throw new AssertionError();
    }
}


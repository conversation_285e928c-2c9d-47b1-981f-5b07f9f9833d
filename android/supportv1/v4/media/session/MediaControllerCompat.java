package android.supportv1.v4.media.session;

import a.a;
import android.app.Activity;
import android.app.PendingIntent;
import android.content.Context;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder.DeathRecipient;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.os.ResultReceiver;
import android.supportv1.v4.app.SupportActivity.ExtraData;
import android.supportv1.v4.app.SupportActivity;
import android.supportv1.v4.media.MediaDescriptionCompat;
import android.supportv1.v4.media.MediaMetadataCompat;
import android.supportv1.v4.media.RatingCompat;
import android.text.TextUtils;
import android.view.KeyEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

public final class MediaControllerCompat {
    public static abstract class Callback implements IBinder.DeathRecipient {
        final Object mCallbackObj;
        MediaControllerCompat.Callback.MessageHandler mHandler;
        IMediaControllerCallback mIControllerCallback;

        public Callback() {
            this.mCallbackObj = MediaControllerCompatApi21.createCallback(new MediaControllerCompat.Callback.StubApi21(this));
        }

        @Override  // android.os.IBinder$DeathRecipient
        public void binderDied() {
            this.postToHandler(8, null, null);
        }

        public IMediaControllerCallback getIControllerCallback() {
            return this.mIControllerCallback;
        }

        public void onAudioInfoChanged(PlaybackInfo mediaControllerCompat$PlaybackInfo0) {
        }

        public void onCaptioningEnabledChanged(boolean z) {
        }

        public void onExtrasChanged(Bundle bundle0) {
        }

        public void onMetadataChanged(MediaMetadataCompat mediaMetadataCompat0) {
        }

        public void onPlaybackStateChanged(PlaybackStateCompat playbackStateCompat0) {
        }

        public void onQueueChanged(List list0) {
        }

        public void onQueueTitleChanged(CharSequence charSequence0) {
        }

        public void onRepeatModeChanged(int v) {
        }

        public void onSessionDestroyed() {
        }

        public void onSessionEvent(String s, Bundle bundle0) {
        }

        public void onSessionReady() {
        }

        public void onShuffleModeChanged(int v) {
        }

        public void postToHandler(int v, Object object0, Bundle bundle0) {
            MediaControllerCompat.Callback.MessageHandler mediaControllerCompat$Callback$MessageHandler0 = this.mHandler;
            if(mediaControllerCompat$Callback$MessageHandler0 != null) {
                Message message0 = mediaControllerCompat$Callback$MessageHandler0.obtainMessage(v, object0);
                message0.setData(bundle0);
                message0.sendToTarget();
            }
        }

        public void setHandler(Handler handler0) {
            if(handler0 == null) {
                MediaControllerCompat.Callback.MessageHandler mediaControllerCompat$Callback$MessageHandler0 = this.mHandler;
                if(mediaControllerCompat$Callback$MessageHandler0 != null) {
                    mediaControllerCompat$Callback$MessageHandler0.mRegistered = false;
                    mediaControllerCompat$Callback$MessageHandler0.removeCallbacksAndMessages(null);
                    this.mHandler = null;
                }
            }
            else {
                MediaControllerCompat.Callback.MessageHandler mediaControllerCompat$Callback$MessageHandler1 = new MediaControllerCompat.Callback.MessageHandler(this, handler0.getLooper());
                this.mHandler = mediaControllerCompat$Callback$MessageHandler1;
                mediaControllerCompat$Callback$MessageHandler1.mRegistered = true;
            }
        }
    }

    static class MediaControllerExtraData extends ExtraData {
        private final MediaControllerCompat mMediaController;

        public MediaControllerExtraData(MediaControllerCompat mediaControllerCompat0) {
            this.mMediaController = mediaControllerCompat0;
        }

        public MediaControllerCompat getMediaController() {
            return this.mMediaController;
        }
    }

    interface MediaControllerImpl {
        void addQueueItem(MediaDescriptionCompat arg1);

        void addQueueItem(MediaDescriptionCompat arg1, int arg2);

        void adjustVolume(int arg1, int arg2);

        boolean dispatchMediaButtonEvent(KeyEvent arg1);

        Bundle getExtras();

        long getFlags();

        Object getMediaController();

        MediaMetadataCompat getMetadata();

        String getPackageName();

        PlaybackInfo getPlaybackInfo();

        PlaybackStateCompat getPlaybackState();

        List getQueue();

        CharSequence getQueueTitle();

        int getRatingType();

        int getRepeatMode();

        PendingIntent getSessionActivity();

        int getShuffleMode();

        TransportControls getTransportControls();

        boolean isCaptioningEnabled();

        boolean isSessionReady();

        void registerCallback(Callback arg1, Handler arg2);

        void removeQueueItem(MediaDescriptionCompat arg1);

        void sendCommand(String arg1, Bundle arg2, ResultReceiver arg3);

        void setVolumeTo(int arg1, int arg2);

        void unregisterCallback(Callback arg1);
    }

    static class MediaControllerImplApi21 implements MediaControllerImpl {
        private HashMap mCallbackMap;
        protected final Object mControllerObj;
        final Object mLock;
        private final List mPendingCallbacks;
        final Token mSessionToken;

        public MediaControllerImplApi21(Context context0, Token mediaSessionCompat$Token0) throws RemoteException {
            this.mLock = new Object();
            this.mPendingCallbacks = new ArrayList();
            this.mCallbackMap = new HashMap();
            this.mSessionToken = mediaSessionCompat$Token0;
            Object object0 = MediaControllerCompatApi21.fromToken(context0, mediaSessionCompat$Token0.getToken());
            this.mControllerObj = object0;
            if(object0 == null) {
                throw new RemoteException();
            }
            if(mediaSessionCompat$Token0.getExtraBinder() == null) {
                this.requestExtraBinder();
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void addQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
            if((this.getFlags() & 4L) == 0L) {
                throw new UnsupportedOperationException("This session doesn\'t support queue management operations");
            }
            Bundle bundle0 = new Bundle();
            bundle0.putParcelable("android.supportv1.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION", mediaDescriptionCompat0);
            this.sendCommand("android.supportv1.v4.media.session.command.ADD_QUEUE_ITEM", bundle0, null);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void addQueueItem(MediaDescriptionCompat mediaDescriptionCompat0, int v) {
            if((this.getFlags() & 4L) == 0L) {
                throw new UnsupportedOperationException("This session doesn\'t support queue management operations");
            }
            Bundle bundle0 = new Bundle();
            bundle0.putParcelable("android.supportv1.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION", mediaDescriptionCompat0);
            bundle0.putInt("android.supportv1.v4.media.session.command.ARGUMENT_INDEX", v);
            this.sendCommand("android.supportv1.v4.media.session.command.ADD_QUEUE_ITEM_AT", bundle0, null);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void adjustVolume(int v, int v1) {
            MediaControllerCompatApi21.adjustVolume(this.mControllerObj, v, v1);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public boolean dispatchMediaButtonEvent(KeyEvent keyEvent0) {
            return MediaControllerCompatApi21.dispatchMediaButtonEvent(this.mControllerObj, keyEvent0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public Bundle getExtras() {
            return MediaControllerCompatApi21.getExtras(this.mControllerObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public long getFlags() {
            return MediaControllerCompatApi21.getFlags(this.mControllerObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public Object getMediaController() {
            return this.mControllerObj;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public MediaMetadataCompat getMetadata() {
            Object object0 = MediaControllerCompatApi21.getMetadata(this.mControllerObj);
            return object0 == null ? null : MediaMetadataCompat.fromMediaMetadata(object0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public String getPackageName() {
            return MediaControllerCompatApi21.getPackageName(this.mControllerObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public PlaybackInfo getPlaybackInfo() {
            Object object0 = MediaControllerCompatApi21.getPlaybackInfo(this.mControllerObj);
            return object0 == null ? null : new PlaybackInfo(android.supportv1.v4.media.session.MediaControllerCompatApi21.PlaybackInfo.getPlaybackType(object0), android.supportv1.v4.media.session.MediaControllerCompatApi21.PlaybackInfo.getLegacyAudioStream(object0), android.supportv1.v4.media.session.MediaControllerCompatApi21.PlaybackInfo.getVolumeControl(object0), android.supportv1.v4.media.session.MediaControllerCompatApi21.PlaybackInfo.getMaxVolume(object0), android.supportv1.v4.media.session.MediaControllerCompatApi21.PlaybackInfo.getCurrentVolume(object0));
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public PlaybackStateCompat getPlaybackState() {
            if(this.mSessionToken.getExtraBinder() != null) {
                try {
                    return this.mSessionToken.getExtraBinder().getPlaybackState();
                }
                catch(RemoteException unused_ex) {
                }
            }
            Object object0 = MediaControllerCompatApi21.getPlaybackState(this.mControllerObj);
            return object0 == null ? null : PlaybackStateCompat.fromPlaybackState(object0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public List getQueue() {
            List list0 = MediaControllerCompatApi21.getQueue(this.mControllerObj);
            return list0 == null ? null : QueueItem.fromQueueItemList(list0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public CharSequence getQueueTitle() {
            return MediaControllerCompatApi21.getQueueTitle(this.mControllerObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public int getRatingType() {
            if(Build.VERSION.SDK_INT < 22 && this.mSessionToken.getExtraBinder() != null) {
                try {
                    return this.mSessionToken.getExtraBinder().getRatingType();
                }
                catch(RemoteException unused_ex) {
                }
            }
            return MediaControllerCompatApi21.getRatingType(this.mControllerObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public int getRepeatMode() {
            if(this.mSessionToken.getExtraBinder() != null) {
                try {
                    return this.mSessionToken.getExtraBinder().getRepeatMode();
                }
                catch(RemoteException unused_ex) {
                }
            }
            return -1;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public PendingIntent getSessionActivity() {
            return MediaControllerCompatApi21.getSessionActivity(this.mControllerObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public int getShuffleMode() {
            if(this.mSessionToken.getExtraBinder() != null) {
                try {
                    return this.mSessionToken.getExtraBinder().getShuffleMode();
                }
                catch(RemoteException unused_ex) {
                }
            }
            return -1;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public TransportControls getTransportControls() {
            Object object0 = MediaControllerCompatApi21.getTransportControls(this.mControllerObj);
            return object0 != null ? new TransportControlsApi21(object0) : null;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public boolean isCaptioningEnabled() {
            if(this.mSessionToken.getExtraBinder() != null) {
                try {
                    return this.mSessionToken.getExtraBinder().isCaptioningEnabled();
                }
                catch(RemoteException unused_ex) {
                }
            }
            return false;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public boolean isSessionReady() {
            return this.mSessionToken.getExtraBinder() != null;
        }

        public void processPendingCallbacksLocked() {
            if(this.mSessionToken.getExtraBinder() == null) {
                return;
            }
            for(Object object0: this.mPendingCallbacks) {
                MediaControllerCompat.MediaControllerImplApi21.ExtraCallback mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0 = new MediaControllerCompat.MediaControllerImplApi21.ExtraCallback(((Callback)object0));
                this.mCallbackMap.put(((Callback)object0), mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0);
                ((Callback)object0).mIControllerCallback = mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0;
                try {
                    this.mSessionToken.getExtraBinder().registerCallbackListener(mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0);
                }
                catch(RemoteException unused_ex) {
                    break;
                }
                ((Callback)object0).postToHandler(13, null, null);
            }
            this.mPendingCallbacks.clear();
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public final void registerCallback(Callback mediaControllerCompat$Callback0, Handler handler0) {
            MediaControllerCompatApi21.registerCallback(this.mControllerObj, mediaControllerCompat$Callback0.mCallbackObj, handler0);
            synchronized(this.mLock) {
                if(this.mSessionToken.getExtraBinder() == null) {
                    mediaControllerCompat$Callback0.mIControllerCallback = null;
                    this.mPendingCallbacks.add(mediaControllerCompat$Callback0);
                }
                else {
                    MediaControllerCompat.MediaControllerImplApi21.ExtraCallback mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0 = new MediaControllerCompat.MediaControllerImplApi21.ExtraCallback(mediaControllerCompat$Callback0);
                    this.mCallbackMap.put(mediaControllerCompat$Callback0, mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0);
                    mediaControllerCompat$Callback0.mIControllerCallback = mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0;
                    try {
                        this.mSessionToken.getExtraBinder().registerCallbackListener(mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0);
                        mediaControllerCompat$Callback0.postToHandler(13, null, null);
                    }
                    catch(RemoteException unused_ex) {
                    }
                }
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void removeQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
            if((this.getFlags() & 4L) == 0L) {
                throw new UnsupportedOperationException("This session doesn\'t support queue management operations");
            }
            Bundle bundle0 = new Bundle();
            bundle0.putParcelable("android.supportv1.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION", mediaDescriptionCompat0);
            this.sendCommand("android.supportv1.v4.media.session.command.REMOVE_QUEUE_ITEM", bundle0, null);
        }

        private void requestExtraBinder() {
            this.sendCommand("android.supportv1.v4.media.session.command.GET_EXTRA_BINDER", null, new MediaControllerCompat.MediaControllerImplApi21.ExtraBinderRequestResultReceiver(this));
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void sendCommand(String s, Bundle bundle0, ResultReceiver resultReceiver0) {
            MediaControllerCompatApi21.sendCommand(this.mControllerObj, s, bundle0, resultReceiver0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void setVolumeTo(int v, int v1) {
            MediaControllerCompatApi21.setVolumeTo(this.mControllerObj, v, v1);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public final void unregisterCallback(Callback mediaControllerCompat$Callback0) {
            MediaControllerCompatApi21.unregisterCallback(this.mControllerObj, mediaControllerCompat$Callback0.mCallbackObj);
            synchronized(this.mLock) {
                if(this.mSessionToken.getExtraBinder() == null) {
                    this.mPendingCallbacks.remove(mediaControllerCompat$Callback0);
                }
                else {
                    try {
                        MediaControllerCompat.MediaControllerImplApi21.ExtraCallback mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0 = (MediaControllerCompat.MediaControllerImplApi21.ExtraCallback)this.mCallbackMap.remove(mediaControllerCompat$Callback0);
                        if(mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0 != null) {
                            mediaControllerCompat$Callback0.mIControllerCallback = null;
                            this.mSessionToken.getExtraBinder().unregisterCallbackListener(mediaControllerCompat$MediaControllerImplApi21$ExtraCallback0);
                        }
                    }
                    catch(RemoteException unused_ex) {
                    }
                }
            }
        }
    }

    static class MediaControllerImplApi23 extends MediaControllerImplApi21 {
        public MediaControllerImplApi23(Context context0, Token mediaSessionCompat$Token0) throws RemoteException {
            super(context0, mediaSessionCompat$Token0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImplApi21
        public TransportControls getTransportControls() {
            Object object0 = MediaControllerCompatApi21.getTransportControls(this.mControllerObj);
            return object0 != null ? new TransportControlsApi23(object0) : null;
        }
    }

    static class MediaControllerImplApi24 extends MediaControllerImplApi23 {
        public MediaControllerImplApi24(Context context0, Token mediaSessionCompat$Token0) throws RemoteException {
            super(context0, mediaSessionCompat$Token0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImplApi23
        public TransportControls getTransportControls() {
            Object object0 = MediaControllerCompatApi21.getTransportControls(this.mControllerObj);
            return object0 != null ? new TransportControlsApi24(object0) : null;
        }
    }

    static class MediaControllerImplBase implements MediaControllerImpl {
        private IMediaSession mBinder;
        private TransportControls mTransportControls;

        public MediaControllerImplBase(Token mediaSessionCompat$Token0) {
            this.mBinder = Stub.asInterface(((IBinder)mediaSessionCompat$Token0.getToken()));
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void addQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
            try {
                if((this.mBinder.getFlags() & 4L) == 0L) {
                    throw new UnsupportedOperationException("This session doesn\'t support queue management operations");
                }
                this.mBinder.addQueueItem(mediaDescriptionCompat0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void addQueueItem(MediaDescriptionCompat mediaDescriptionCompat0, int v) {
            try {
                if((this.mBinder.getFlags() & 4L) == 0L) {
                    throw new UnsupportedOperationException("This session doesn\'t support queue management operations");
                }
                this.mBinder.addQueueItemAt(mediaDescriptionCompat0, v);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void adjustVolume(int v, int v1) {
            try {
                this.mBinder.adjustVolume(v, v1, null);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public boolean dispatchMediaButtonEvent(KeyEvent keyEvent0) {
            if(keyEvent0 == null) {
                throw new IllegalArgumentException("event may not be null.");
            }
            try {
                this.mBinder.sendMediaButton(keyEvent0);
            }
            catch(RemoteException unused_ex) {
            }
            return false;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public Bundle getExtras() {
            try {
                return this.mBinder.getExtras();
            }
            catch(RemoteException unused_ex) {
                return null;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public long getFlags() {
            try {
                return this.mBinder.getFlags();
            }
            catch(RemoteException unused_ex) {
                return 0L;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public Object getMediaController() {
            return null;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public MediaMetadataCompat getMetadata() {
            try {
                return this.mBinder.getMetadata();
            }
            catch(RemoteException unused_ex) {
                return null;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public String getPackageName() {
            try {
                return this.mBinder.getPackageName();
            }
            catch(RemoteException unused_ex) {
                return null;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public PlaybackInfo getPlaybackInfo() {
            try {
                ParcelableVolumeInfo parcelableVolumeInfo0 = this.mBinder.getVolumeAttributes();
                return new PlaybackInfo(parcelableVolumeInfo0.volumeType, parcelableVolumeInfo0.audioStream, parcelableVolumeInfo0.controlType, parcelableVolumeInfo0.maxVolume, parcelableVolumeInfo0.currentVolume);
            }
            catch(RemoteException unused_ex) {
                return null;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public PlaybackStateCompat getPlaybackState() {
            try {
                return this.mBinder.getPlaybackState();
            }
            catch(RemoteException unused_ex) {
                return null;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public List getQueue() {
            try {
                return this.mBinder.getQueue();
            }
            catch(RemoteException unused_ex) {
                return null;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public CharSequence getQueueTitle() {
            try {
                return this.mBinder.getQueueTitle();
            }
            catch(RemoteException unused_ex) {
                return null;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public int getRatingType() {
            try {
                return this.mBinder.getRatingType();
            }
            catch(RemoteException unused_ex) {
                return 0;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public int getRepeatMode() {
            try {
                return this.mBinder.getRepeatMode();
            }
            catch(RemoteException unused_ex) {
                return -1;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public PendingIntent getSessionActivity() {
            try {
                return this.mBinder.getLaunchPendingIntent();
            }
            catch(RemoteException unused_ex) {
                return null;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public int getShuffleMode() {
            try {
                return this.mBinder.getShuffleMode();
            }
            catch(RemoteException unused_ex) {
                return -1;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public TransportControls getTransportControls() {
            if(this.mTransportControls == null) {
                this.mTransportControls = new TransportControlsBase(this.mBinder);
            }
            return this.mTransportControls;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public boolean isCaptioningEnabled() {
            try {
                return this.mBinder.isCaptioningEnabled();
            }
            catch(RemoteException unused_ex) {
                return false;
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public boolean isSessionReady() {
            return true;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void registerCallback(Callback mediaControllerCompat$Callback0, Handler handler0) {
            if(mediaControllerCompat$Callback0 == null) {
                throw new IllegalArgumentException("callback may not be null.");
            }
            try {
                this.mBinder.asBinder().linkToDeath(mediaControllerCompat$Callback0, 0);
                this.mBinder.registerCallbackListener(((IMediaControllerCallback)mediaControllerCompat$Callback0.mCallbackObj));
                mediaControllerCompat$Callback0.postToHandler(13, null, null);
            }
            catch(RemoteException unused_ex) {
                mediaControllerCompat$Callback0.postToHandler(8, null, null);
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void removeQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
            try {
                if((this.mBinder.getFlags() & 4L) == 0L) {
                    throw new UnsupportedOperationException("This session doesn\'t support queue management operations");
                }
                this.mBinder.removeQueueItem(mediaDescriptionCompat0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void sendCommand(String s, Bundle bundle0, ResultReceiver resultReceiver0) {
            try {
                this.mBinder.sendCommand(s, bundle0, new ResultReceiverWrapper(resultReceiver0));
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void setVolumeTo(int v, int v1) {
            try {
                this.mBinder.setVolumeTo(v, v1, null);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$MediaControllerImpl
        public void unregisterCallback(Callback mediaControllerCompat$Callback0) {
            if(mediaControllerCompat$Callback0 == null) {
                throw new IllegalArgumentException("callback may not be null.");
            }
            try {
                this.mBinder.unregisterCallbackListener(((IMediaControllerCallback)mediaControllerCompat$Callback0.mCallbackObj));
                this.mBinder.asBinder().unlinkToDeath(mediaControllerCompat$Callback0, 0);
            }
            catch(RemoteException unused_ex) {
            }
        }
    }

    public static final class PlaybackInfo {
        public static final int PLAYBACK_TYPE_LOCAL = 1;
        public static final int PLAYBACK_TYPE_REMOTE = 2;
        private final int mAudioStream;
        private final int mCurrentVolume;
        private final int mMaxVolume;
        private final int mPlaybackType;
        private final int mVolumeControl;

        public PlaybackInfo(int v, int v1, int v2, int v3, int v4) {
            this.mPlaybackType = v;
            this.mAudioStream = v1;
            this.mVolumeControl = v2;
            this.mMaxVolume = v3;
            this.mCurrentVolume = v4;
        }

        public int getAudioStream() {
            return this.mAudioStream;
        }

        public int getCurrentVolume() {
            return this.mCurrentVolume;
        }

        public int getMaxVolume() {
            return this.mMaxVolume;
        }

        public int getPlaybackType() {
            return this.mPlaybackType;
        }

        public int getVolumeControl() {
            return this.mVolumeControl;
        }
    }

    public static abstract class TransportControls {
        public static final String EXTRA_LEGACY_STREAM_TYPE = "android.media.session.extra.LEGACY_STREAM_TYPE";

        public abstract void fastForward();

        public abstract void pause();

        public abstract void play();

        public abstract void playFromMediaId(String arg1, Bundle arg2);

        public abstract void playFromSearch(String arg1, Bundle arg2);

        public abstract void playFromUri(Uri arg1, Bundle arg2);

        public abstract void prepare();

        public abstract void prepareFromMediaId(String arg1, Bundle arg2);

        public abstract void prepareFromSearch(String arg1, Bundle arg2);

        public abstract void prepareFromUri(Uri arg1, Bundle arg2);

        public abstract void rewind();

        public abstract void seekTo(long arg1);

        public abstract void sendCustomAction(CustomAction arg1, Bundle arg2);

        public abstract void sendCustomAction(String arg1, Bundle arg2);

        public abstract void setCaptioningEnabled(boolean arg1);

        public abstract void setRating(RatingCompat arg1);

        public abstract void setRating(RatingCompat arg1, Bundle arg2);

        public abstract void setRepeatMode(int arg1);

        public abstract void setShuffleMode(int arg1);

        public abstract void skipToNext();

        public abstract void skipToPrevious();

        public abstract void skipToQueueItem(long arg1);

        public abstract void stop();
    }

    static class TransportControlsApi21 extends TransportControls {
        protected final Object mControlsObj;

        public TransportControlsApi21(Object object0) {
            this.mControlsObj = object0;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void fastForward() {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.fastForward(this.mControlsObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void pause() {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.pause(this.mControlsObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void play() {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.play(this.mControlsObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void playFromMediaId(String s, Bundle bundle0) {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.playFromMediaId(this.mControlsObj, s, bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void playFromSearch(String s, Bundle bundle0) {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.playFromSearch(this.mControlsObj, s, bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void playFromUri(Uri uri0, Bundle bundle0) {
            if(uri0 == null || Uri.EMPTY.equals(uri0)) {
                throw new IllegalArgumentException("You must specify a non-empty Uri for playFromUri.");
            }
            Bundle bundle1 = new Bundle();
            bundle1.putParcelable("android.supportv1.v4.media.session.action.ARGUMENT_URI", uri0);
            bundle1.putBundle("android.supportv1.v4.media.session.action.ARGUMENT_EXTRAS", bundle0);
            this.sendCustomAction("android.supportv1.v4.media.session.action.PLAY_FROM_URI", bundle1);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void prepare() {
            this.sendCustomAction("android.supportv1.v4.media.session.action.PREPARE", null);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void prepareFromMediaId(String s, Bundle bundle0) {
            Bundle bundle1 = new Bundle();
            bundle1.putString("android.supportv1.v4.media.session.action.ARGUMENT_MEDIA_ID", s);
            bundle1.putBundle("android.supportv1.v4.media.session.action.ARGUMENT_EXTRAS", bundle0);
            this.sendCustomAction("android.supportv1.v4.media.session.action.PREPARE_FROM_MEDIA_ID", bundle1);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void prepareFromSearch(String s, Bundle bundle0) {
            Bundle bundle1 = new Bundle();
            bundle1.putString("android.supportv1.v4.media.session.action.ARGUMENT_QUERY", s);
            bundle1.putBundle("android.supportv1.v4.media.session.action.ARGUMENT_EXTRAS", bundle0);
            this.sendCustomAction("android.supportv1.v4.media.session.action.PREPARE_FROM_SEARCH", bundle1);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void prepareFromUri(Uri uri0, Bundle bundle0) {
            Bundle bundle1 = new Bundle();
            bundle1.putParcelable("android.supportv1.v4.media.session.action.ARGUMENT_URI", uri0);
            bundle1.putBundle("android.supportv1.v4.media.session.action.ARGUMENT_EXTRAS", bundle0);
            this.sendCustomAction("android.supportv1.v4.media.session.action.PREPARE_FROM_URI", bundle1);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void rewind() {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.rewind(this.mControlsObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void seekTo(long v) {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.seekTo(this.mControlsObj, v);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void sendCustomAction(CustomAction playbackStateCompat$CustomAction0, Bundle bundle0) {
            MediaControllerCompat.validateCustomAction(playbackStateCompat$CustomAction0.getAction(), bundle0);
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.sendCustomAction(this.mControlsObj, playbackStateCompat$CustomAction0.getAction(), bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void sendCustomAction(String s, Bundle bundle0) {
            MediaControllerCompat.validateCustomAction(s, bundle0);
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.sendCustomAction(this.mControlsObj, s, bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setCaptioningEnabled(boolean z) {
            Bundle bundle0 = new Bundle();
            bundle0.putBoolean("android.supportv1.v4.media.session.action.ARGUMENT_CAPTIONING_ENABLED", z);
            this.sendCustomAction("android.supportv1.v4.media.session.action.SET_CAPTIONING_ENABLED", bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setRating(RatingCompat ratingCompat0) {
            Object object0 = ratingCompat0 == null ? null : ratingCompat0.getRating();
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.setRating(this.mControlsObj, object0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setRating(RatingCompat ratingCompat0, Bundle bundle0) {
            Bundle bundle1 = new Bundle();
            bundle1.putParcelable("android.supportv1.v4.media.session.action.ARGUMENT_RATING", ratingCompat0);
            bundle1.putBundle("android.supportv1.v4.media.session.action.ARGUMENT_EXTRAS", bundle0);
            this.sendCustomAction("android.supportv1.v4.media.session.action.SET_RATING", bundle1);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setRepeatMode(int v) {
            Bundle bundle0 = new Bundle();
            bundle0.putInt("android.supportv1.v4.media.session.action.ARGUMENT_REPEAT_MODE", v);
            this.sendCustomAction("android.supportv1.v4.media.session.action.SET_REPEAT_MODE", bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setShuffleMode(int v) {
            Bundle bundle0 = new Bundle();
            bundle0.putInt("android.supportv1.v4.media.session.action.ARGUMENT_SHUFFLE_MODE", v);
            this.sendCustomAction("android.supportv1.v4.media.session.action.SET_SHUFFLE_MODE", bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void skipToNext() {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.skipToNext(this.mControlsObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void skipToPrevious() {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.skipToPrevious(this.mControlsObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void skipToQueueItem(long v) {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.skipToQueueItem(this.mControlsObj, v);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void stop() {
            android.supportv1.v4.media.session.MediaControllerCompatApi21.TransportControls.stop(this.mControlsObj);
        }
    }

    static class TransportControlsApi23 extends TransportControlsApi21 {
        public TransportControlsApi23(Object object0) {
            super(object0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControlsApi21
        public void playFromUri(Uri uri0, Bundle bundle0) {
            android.supportv1.v4.media.session.MediaControllerCompatApi23.TransportControls.playFromUri(this.mControlsObj, uri0, bundle0);
        }
    }

    static class TransportControlsApi24 extends TransportControlsApi23 {
        public TransportControlsApi24(Object object0) {
            super(object0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControlsApi21
        public void prepare() {
            android.supportv1.v4.media.session.MediaControllerCompatApi24.TransportControls.prepare(this.mControlsObj);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControlsApi21
        public void prepareFromMediaId(String s, Bundle bundle0) {
            android.supportv1.v4.media.session.MediaControllerCompatApi24.TransportControls.prepareFromMediaId(this.mControlsObj, s, bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControlsApi21
        public void prepareFromSearch(String s, Bundle bundle0) {
            android.supportv1.v4.media.session.MediaControllerCompatApi24.TransportControls.prepareFromSearch(this.mControlsObj, s, bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControlsApi21
        public void prepareFromUri(Uri uri0, Bundle bundle0) {
            android.supportv1.v4.media.session.MediaControllerCompatApi24.TransportControls.prepareFromUri(this.mControlsObj, uri0, bundle0);
        }
    }

    static class TransportControlsBase extends TransportControls {
        private IMediaSession mBinder;

        public TransportControlsBase(IMediaSession iMediaSession0) {
            this.mBinder = iMediaSession0;
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void fastForward() {
            try {
                this.mBinder.fastForward();
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void pause() {
            try {
                this.mBinder.pause();
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void play() {
            try {
                this.mBinder.play();
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void playFromMediaId(String s, Bundle bundle0) {
            try {
                this.mBinder.playFromMediaId(s, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void playFromSearch(String s, Bundle bundle0) {
            try {
                this.mBinder.playFromSearch(s, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void playFromUri(Uri uri0, Bundle bundle0) {
            try {
                this.mBinder.playFromUri(uri0, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void prepare() {
            try {
                this.mBinder.prepare();
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void prepareFromMediaId(String s, Bundle bundle0) {
            try {
                this.mBinder.prepareFromMediaId(s, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void prepareFromSearch(String s, Bundle bundle0) {
            try {
                this.mBinder.prepareFromSearch(s, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void prepareFromUri(Uri uri0, Bundle bundle0) {
            try {
                this.mBinder.prepareFromUri(uri0, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void rewind() {
            try {
                this.mBinder.rewind();
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void seekTo(long v) {
            try {
                this.mBinder.seekTo(v);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void sendCustomAction(CustomAction playbackStateCompat$CustomAction0, Bundle bundle0) {
            this.sendCustomAction(playbackStateCompat$CustomAction0.getAction(), bundle0);
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void sendCustomAction(String s, Bundle bundle0) {
            MediaControllerCompat.validateCustomAction(s, bundle0);
            try {
                this.mBinder.sendCustomAction(s, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setCaptioningEnabled(boolean z) {
            try {
                this.mBinder.setCaptioningEnabled(z);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setRating(RatingCompat ratingCompat0) {
            try {
                this.mBinder.rate(ratingCompat0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setRating(RatingCompat ratingCompat0, Bundle bundle0) {
            try {
                this.mBinder.rateWithExtras(ratingCompat0, bundle0);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setRepeatMode(int v) {
            try {
                this.mBinder.setRepeatMode(v);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void setShuffleMode(int v) {
            try {
                this.mBinder.setShuffleMode(v);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void skipToNext() {
            try {
                this.mBinder.next();
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void skipToPrevious() {
            try {
                this.mBinder.previous();
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void skipToQueueItem(long v) {
            try {
                this.mBinder.skipToQueueItem(v);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.session.MediaControllerCompat$TransportControls
        public void stop() {
            try {
                this.mBinder.stop();
            }
            catch(RemoteException unused_ex) {
            }
        }
    }

    public static final String COMMAND_ADD_QUEUE_ITEM = "android.supportv1.v4.media.session.command.ADD_QUEUE_ITEM";
    public static final String COMMAND_ADD_QUEUE_ITEM_AT = "android.supportv1.v4.media.session.command.ADD_QUEUE_ITEM_AT";
    public static final String COMMAND_ARGUMENT_INDEX = "android.supportv1.v4.media.session.command.ARGUMENT_INDEX";
    public static final String COMMAND_ARGUMENT_MEDIA_DESCRIPTION = "android.supportv1.v4.media.session.command.ARGUMENT_MEDIA_DESCRIPTION";
    public static final String COMMAND_GET_EXTRA_BINDER = "android.supportv1.v4.media.session.command.GET_EXTRA_BINDER";
    public static final String COMMAND_REMOVE_QUEUE_ITEM = "android.supportv1.v4.media.session.command.REMOVE_QUEUE_ITEM";
    public static final String COMMAND_REMOVE_QUEUE_ITEM_AT = "android.supportv1.v4.media.session.command.REMOVE_QUEUE_ITEM_AT";
    static final String TAG = "MediaControllerCompat";
    private final MediaControllerImpl mImpl;
    private final HashSet mRegisteredCallbacks;
    private final Token mToken;

    public MediaControllerCompat(Context context0, Token mediaSessionCompat$Token0) throws RemoteException {
        MediaControllerImplApi21 mediaControllerCompat$MediaControllerImplApi210;
        this.mRegisteredCallbacks = new HashSet();
        if(mediaSessionCompat$Token0 == null) {
            throw new IllegalArgumentException("sessionToken must not be null");
        }
        this.mToken = mediaSessionCompat$Token0;
        int v = Build.VERSION.SDK_INT;
        if(v >= 24) {
            mediaControllerCompat$MediaControllerImplApi210 = new MediaControllerImplApi24(context0, mediaSessionCompat$Token0);
        }
        else if(v >= 23) {
            mediaControllerCompat$MediaControllerImplApi210 = new MediaControllerImplApi23(context0, mediaSessionCompat$Token0);
        }
        else {
            mediaControllerCompat$MediaControllerImplApi210 = new MediaControllerImplApi21(context0, mediaSessionCompat$Token0);
        }
        this.mImpl = mediaControllerCompat$MediaControllerImplApi210;
    }

    public MediaControllerCompat(Context context0, MediaSessionCompat mediaSessionCompat0) {
        MediaControllerImplApi21 mediaControllerCompat$MediaControllerImplApi210;
        this.mRegisteredCallbacks = new HashSet();
        if(mediaSessionCompat0 == null) {
            throw new IllegalArgumentException("session must not be null");
        }
        Token mediaSessionCompat$Token0 = mediaSessionCompat0.getSessionToken();
        this.mToken = mediaSessionCompat$Token0;
        try {
            int v = Build.VERSION.SDK_INT;
            if(v >= 24) {
                mediaControllerCompat$MediaControllerImplApi210 = new MediaControllerImplApi24(context0, mediaSessionCompat$Token0);
            }
            else if(v >= 23) {
                mediaControllerCompat$MediaControllerImplApi210 = new MediaControllerImplApi23(context0, mediaSessionCompat$Token0);
            }
            else {
                mediaControllerCompat$MediaControllerImplApi210 = new MediaControllerImplApi21(context0, mediaSessionCompat$Token0);
            }
        }
        catch(RemoteException unused_ex) {
            mediaControllerCompat$MediaControllerImplApi210 = null;
        }
        this.mImpl = mediaControllerCompat$MediaControllerImplApi210;
    }

    public void addQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
        this.mImpl.addQueueItem(mediaDescriptionCompat0);
    }

    public void addQueueItem(MediaDescriptionCompat mediaDescriptionCompat0, int v) {
        this.mImpl.addQueueItem(mediaDescriptionCompat0, v);
    }

    public void adjustVolume(int v, int v1) {
        this.mImpl.adjustVolume(v, v1);
    }

    public boolean dispatchMediaButtonEvent(KeyEvent keyEvent0) {
        if(keyEvent0 == null) {
            throw new IllegalArgumentException("KeyEvent may not be null");
        }
        return this.mImpl.dispatchMediaButtonEvent(keyEvent0);
    }

    public Bundle getExtras() {
        return this.mImpl.getExtras();
    }

    public long getFlags() {
        return this.mImpl.getFlags();
    }

    public static MediaControllerCompat getMediaController(Activity activity0) {
        if(activity0 instanceof SupportActivity) {
            MediaControllerExtraData mediaControllerCompat$MediaControllerExtraData0 = (MediaControllerExtraData)((SupportActivity)activity0).getExtraData(MediaControllerExtraData.class);
            return mediaControllerCompat$MediaControllerExtraData0 == null ? null : mediaControllerCompat$MediaControllerExtraData0.getMediaController();
        }
        Object object0 = MediaControllerCompatApi21.getMediaController(activity0);
        if(object0 == null) {
            return null;
        }
        Object object1 = MediaControllerCompatApi21.getSessionToken(object0);
        try {
            return new MediaControllerCompat(activity0, Token.fromToken(object1));
        }
        catch(RemoteException unused_ex) {
            return null;
        }
    }

    public Object getMediaController() {
        return this.mImpl.getMediaController();
    }

    public MediaMetadataCompat getMetadata() {
        return this.mImpl.getMetadata();
    }

    public String getPackageName() {
        return this.mImpl.getPackageName();
    }

    public PlaybackInfo getPlaybackInfo() {
        return this.mImpl.getPlaybackInfo();
    }

    public PlaybackStateCompat getPlaybackState() {
        return this.mImpl.getPlaybackState();
    }

    public List getQueue() {
        return this.mImpl.getQueue();
    }

    public CharSequence getQueueTitle() {
        return this.mImpl.getQueueTitle();
    }

    public int getRatingType() {
        return this.mImpl.getRatingType();
    }

    public int getRepeatMode() {
        return this.mImpl.getRepeatMode();
    }

    public PendingIntent getSessionActivity() {
        return this.mImpl.getSessionActivity();
    }

    public Token getSessionToken() {
        return this.mToken;
    }

    public Bundle getSessionToken2Bundle() {
        return this.mToken.getSessionToken2Bundle();
    }

    public int getShuffleMode() {
        return this.mImpl.getShuffleMode();
    }

    public TransportControls getTransportControls() {
        return this.mImpl.getTransportControls();
    }

    public boolean isCaptioningEnabled() {
        return this.mImpl.isCaptioningEnabled();
    }

    public boolean isSessionReady() {
        return this.mImpl.isSessionReady();
    }

    public void registerCallback(Callback mediaControllerCompat$Callback0) {
        this.registerCallback(mediaControllerCompat$Callback0, null);
    }

    public void registerCallback(Callback mediaControllerCompat$Callback0, Handler handler0) {
        if(mediaControllerCompat$Callback0 == null) {
            throw new IllegalArgumentException("callback must not be null");
        }
        if(handler0 == null) {
            handler0 = new Handler();
        }
        mediaControllerCompat$Callback0.setHandler(handler0);
        this.mImpl.registerCallback(mediaControllerCompat$Callback0, handler0);
        this.mRegisteredCallbacks.add(mediaControllerCompat$Callback0);
    }

    public void removeQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
        this.mImpl.removeQueueItem(mediaDescriptionCompat0);
    }

    @Deprecated
    public void removeQueueItemAt(int v) {
        List list0 = this.getQueue();
        if(list0 != null && v >= 0 && v < list0.size()) {
            QueueItem mediaSessionCompat$QueueItem0 = (QueueItem)list0.get(v);
            if(mediaSessionCompat$QueueItem0 != null) {
                this.removeQueueItem(mediaSessionCompat$QueueItem0.getDescription());
            }
        }
    }

    public void sendCommand(String s, Bundle bundle0, ResultReceiver resultReceiver0) {
        if(TextUtils.isEmpty(s)) {
            throw new IllegalArgumentException("command must neither be null nor empty");
        }
        this.mImpl.sendCommand(s, bundle0, resultReceiver0);
    }

    public static void setMediaController(Activity activity0, MediaControllerCompat mediaControllerCompat0) {
        if(activity0 instanceof SupportActivity) {
            ((SupportActivity)activity0).putExtraData(new MediaControllerExtraData(mediaControllerCompat0));
        }
        MediaControllerCompatApi21.setMediaController(activity0, (mediaControllerCompat0 == null ? null : MediaControllerCompatApi21.fromToken(activity0, mediaControllerCompat0.getSessionToken().getToken())));
    }

    public void setVolumeTo(int v, int v1) {
        this.mImpl.setVolumeTo(v, v1);
    }

    public void unregisterCallback(Callback mediaControllerCompat$Callback0) {
        if(mediaControllerCompat$Callback0 == null) {
            throw new IllegalArgumentException("callback must not be null");
        }
        try {
            this.mRegisteredCallbacks.remove(mediaControllerCompat$Callback0);
            this.mImpl.unregisterCallback(mediaControllerCompat$Callback0);
        }
        finally {
            mediaControllerCompat$Callback0.setHandler(null);
        }
    }

    public static void validateCustomAction(String s, Bundle bundle0) {
        int v;
        if(s == null) {
            return;
        }
        switch(s.hashCode()) {
            case 0xAF9FC575: {
                v = s.equals("android.supportv1.v4.media.session.action.FOLLOW") ? 0 : -1;
                break;
            }
            case 503011406: {
                v = s.equals("android.supportv1.v4.media.session.action.UNFOLLOW") ? 1 : -1;
                break;
            }
            default: {
                v = -1;
            }
        }
        if((v == 0 || v == 1) && (bundle0 == null || !bundle0.containsKey("android.supportv1.v4.media.session.ARGUMENT_MEDIA_ATTRIBUTE"))) {
            throw new IllegalArgumentException(a.y("An extra field android.supportv1.v4.media.session.ARGUMENT_MEDIA_ATTRIBUTE is required for this action ", s, ".").toString());
        }
    }
}


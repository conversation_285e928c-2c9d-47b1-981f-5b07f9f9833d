package android.supportv1.v4.media.session;

import a.a;
import android.app.PendingIntent;
import android.content.BroadcastReceiver.PendingResult;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.pm.ServiceInfo;
import android.os.Build.VERSION;
import android.os.RemoteException;
import android.supportv1.v4.media.MediaBrowserCompat.ConnectionCallback;
import android.supportv1.v4.media.MediaBrowserCompat;
import android.view.KeyEvent;
import java.util.List;
import java.util.Objects;

public class MediaButtonReceiver extends BroadcastReceiver {
    static class MediaButtonConnectionCallback extends ConnectionCallback {
        private final Context mContext;
        private final Intent mIntent;
        private MediaBrowserCompat mMediaBrowser;
        private final BroadcastReceiver.PendingResult mPendingResult;

        public MediaButtonConnectionCallback(Context context0, Intent intent0, BroadcastReceiver.PendingResult broadcastReceiver$PendingResult0) {
            this.mContext = context0;
            this.mIntent = intent0;
            this.mPendingResult = broadcastReceiver$PendingResult0;
        }

        private void finish() {
            this.mMediaBrowser.disconnect();
            this.mPendingResult.finish();
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$ConnectionCallback
        public void onConnected() {
            try {
                Token mediaSessionCompat$Token0 = this.mMediaBrowser.getSessionToken();
                new MediaControllerCompat(this.mContext, mediaSessionCompat$Token0).dispatchMediaButtonEvent(((KeyEvent)this.mIntent.getParcelableExtra("android.intent.extra.KEY_EVENT")));
            }
            catch(RemoteException unused_ex) {
            }
            this.finish();
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$ConnectionCallback
        public void onConnectionFailed() {
            this.finish();
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$ConnectionCallback
        public void onConnectionSuspended() {
            this.finish();
        }

        public void setMediaBrowser(MediaBrowserCompat mediaBrowserCompat0) {
            this.mMediaBrowser = mediaBrowserCompat0;
        }
    }

    private static final String TAG = "MediaButtonReceiver";

    public static PendingIntent buildMediaButtonPendingIntent(Context context0, long v) {
        ComponentName componentName0 = MediaButtonReceiver.getMediaButtonReceiverComponent(context0);
        return componentName0 == null ? null : MediaButtonReceiver.buildMediaButtonPendingIntent(context0, componentName0, v);
    }

    public static PendingIntent buildMediaButtonPendingIntent(Context context0, ComponentName componentName0, long v) {
        if(componentName0 != null) {
            int v1 = PlaybackStateCompat.toKeyCode(v);
            if(v1 != 0) {
                Intent intent0 = new Intent("android.intent.action.MEDIA_BUTTON");
                intent0.setComponent(componentName0);
                intent0.putExtra("android.intent.extra.KEY_EVENT", new KeyEvent(0, v1));
                return PendingIntent.getBroadcast(context0, v1, intent0, 0);
            }
        }
        return null;
    }

    public static ComponentName getMediaButtonReceiverComponent(Context context0) {
        Intent intent0 = new Intent("android.intent.action.MEDIA_BUTTON");
        intent0.setPackage("com.pdf.editor.viewer.pdfreader.pdfviewer");
        List list0 = context0.getPackageManager().queryBroadcastReceivers(intent0, 0);
        if(list0.size() == 1) {
            ActivityInfo activityInfo0 = ((ResolveInfo)list0.get(0)).activityInfo;
            return new ComponentName(activityInfo0.packageName, activityInfo0.name);
        }
        return null;
    }

    private static ComponentName getServiceComponentByAction(Context context0, String s) {
        PackageManager packageManager0 = context0.getPackageManager();
        Intent intent0 = new Intent(s);
        intent0.setPackage("com.pdf.editor.viewer.pdfreader.pdfviewer");
        List list0 = packageManager0.queryIntentServices(intent0, 0);
        if(list0.size() == 1) {
            ServiceInfo serviceInfo0 = ((ResolveInfo)list0.get(0)).serviceInfo;
            return new ComponentName(serviceInfo0.packageName, serviceInfo0.name);
        }
        if(list0.isEmpty()) {
            return null;
        }
        StringBuilder stringBuilder0 = a.y("Expected 1 service that handles ", s, ", found ");
        stringBuilder0.append(list0.size());
        throw new IllegalStateException(stringBuilder0.toString());
    }

    public static KeyEvent handleIntent(MediaSessionCompat mediaSessionCompat0, Intent intent0) {
        if(mediaSessionCompat0 != null && intent0 != null && "android.intent.action.MEDIA_BUTTON".equals(intent0.getAction()) && intent0.hasExtra("android.intent.extra.KEY_EVENT")) {
            KeyEvent keyEvent0 = (KeyEvent)intent0.getParcelableExtra("android.intent.extra.KEY_EVENT");
            mediaSessionCompat0.getController().dispatchMediaButtonEvent(keyEvent0);
            return keyEvent0;
        }
        return null;
    }

    @Override  // android.content.BroadcastReceiver
    public void onReceive(Context context0, Intent intent0) {
        if(intent0 != null && "android.intent.action.MEDIA_BUTTON".equals(intent0.getAction()) && intent0.hasExtra("android.intent.extra.KEY_EVENT")) {
            ComponentName componentName0 = MediaButtonReceiver.getServiceComponentByAction(context0, "android.intent.action.MEDIA_BUTTON");
            if(componentName0 != null) {
                intent0.setComponent(componentName0);
                MediaButtonReceiver.startForegroundService(context0, intent0);
                return;
            }
            ComponentName componentName1 = MediaButtonReceiver.getServiceComponentByAction(context0, "android.media.browse.MediaBrowserService");
            if(componentName1 == null) {
                throw new IllegalStateException("Could not find any Service that handles android.intent.action.MEDIA_BUTTON or implements a media browser service.");
            }
            BroadcastReceiver.PendingResult broadcastReceiver$PendingResult0 = this.goAsync();
            Context context1 = context0.getApplicationContext();
            MediaButtonConnectionCallback mediaButtonReceiver$MediaButtonConnectionCallback0 = new MediaButtonConnectionCallback(context1, intent0, broadcastReceiver$PendingResult0);
            MediaBrowserCompat mediaBrowserCompat0 = new MediaBrowserCompat(context1, componentName1, mediaButtonReceiver$MediaButtonConnectionCallback0, null);
            mediaButtonReceiver$MediaButtonConnectionCallback0.setMediaBrowser(mediaBrowserCompat0);
            mediaBrowserCompat0.connect();
            return;
        }
        Objects.toString(intent0);
    }

    private static void startForegroundService(Context context0, Intent intent0) {
        if(Build.VERSION.SDK_INT >= 26) {
            context0.startForegroundService(intent0);
            return;
        }
        context0.startService(intent0);
    }
}


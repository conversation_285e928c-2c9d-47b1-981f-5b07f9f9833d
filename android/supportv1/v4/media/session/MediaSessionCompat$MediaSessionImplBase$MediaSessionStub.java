package android.supportv1.v4.media.session;

import android.app.PendingIntent;
import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.RemoteException;
import android.supportv1.v4.media.MediaDescriptionCompat;
import android.supportv1.v4.media.MediaMetadataCompat;
import android.supportv1.v4.media.MediaSessionManager.RemoteUserInfo;
import android.supportv1.v4.media.RatingCompat;
import android.supportv1.v4.media.VolumeProviderCompat;
import android.view.KeyEvent;
import java.util.List;

class MediaSessionCompat.MediaSessionImplBase.MediaSessionStub extends Stub {
    public MediaSessionCompat.MediaSessionImplBase.MediaSessionStub(MediaSessionImplBase mediaSessionCompat$MediaSessionImplBase0) {
        MediaSessionImplBase.this = mediaSessionCompat$MediaSessionImplBase0;
        super();
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void addQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
        this.postToHandler(25, mediaDescriptionCompat0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void addQueueItemAt(MediaDescriptionCompat mediaDescriptionCompat0, int v) {
        this.postToHandler(26, mediaDescriptionCompat0, v);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void adjustVolume(int v, int v1, String s) {
        MediaSessionImplBase.this.adjustVolume(v, v1);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void fastForward() throws RemoteException {
        this.postToHandler(16);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public Bundle getExtras() {
        synchronized(MediaSessionImplBase.this.mLock) {
        }
        return MediaSessionImplBase.this.mExtras;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public long getFlags() {
        synchronized(MediaSessionImplBase.this.mLock) {
        }
        return (long)MediaSessionImplBase.this.mFlags;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public PendingIntent getLaunchPendingIntent() {
        synchronized(MediaSessionImplBase.this.mLock) {
        }
        return MediaSessionImplBase.this.mSessionActivity;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public MediaMetadataCompat getMetadata() {
        return MediaSessionImplBase.this.mMetadata;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public String getPackageName() {
        return MediaSessionImplBase.this.mPackageName;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public PlaybackStateCompat getPlaybackState() {
        synchronized(MediaSessionImplBase.this.mLock) {
        }
        return MediaSessionCompat.getStateWithUpdatedPosition(MediaSessionImplBase.this.mState, MediaSessionImplBase.this.mMetadata);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public List getQueue() {
        synchronized(MediaSessionImplBase.this.mLock) {
        }
        return MediaSessionImplBase.this.mQueue;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public CharSequence getQueueTitle() {
        return MediaSessionImplBase.this.mQueueTitle;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public int getRatingType() {
        return MediaSessionImplBase.this.mRatingType;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public int getRepeatMode() {
        return MediaSessionImplBase.this.mRepeatMode;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public int getShuffleMode() {
        return MediaSessionImplBase.this.mShuffleMode;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public String getTag() {
        return MediaSessionImplBase.this.mTag;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public ParcelableVolumeInfo getVolumeAttributes() {
        int v3;
        int v2;
        int v1;
        int v5;
        int v4;
        synchronized(MediaSessionImplBase.this.mLock) {
            MediaSessionImplBase mediaSessionCompat$MediaSessionImplBase0 = MediaSessionImplBase.this;
            v1 = mediaSessionCompat$MediaSessionImplBase0.mVolumeType;
            v2 = mediaSessionCompat$MediaSessionImplBase0.mLocalStream;
            VolumeProviderCompat volumeProviderCompat0 = mediaSessionCompat$MediaSessionImplBase0.mVolumeProvider;
            v3 = 2;
            if(v1 == 2) {
                v4 = volumeProviderCompat0.getCurrentVolume();
                v5 = volumeProviderCompat0.getMaxVolume();
                v3 = volumeProviderCompat0.getVolumeControl();
            }
            else {
                v5 = mediaSessionCompat$MediaSessionImplBase0.mAudioManager.getStreamMaxVolume(v2);
                v4 = MediaSessionImplBase.this.mAudioManager.getStreamVolume(v2);
            }
        }
        return new ParcelableVolumeInfo(v1, v2, v3, v5, v4);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public boolean isCaptioningEnabled() {
        return MediaSessionImplBase.this.mCaptioningEnabled;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public boolean isShuffleModeEnabledRemoved() {
        return false;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public boolean isTransportControlEnabled() {
        return (MediaSessionImplBase.this.mFlags & 2) != 0;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void next() throws RemoteException {
        this.postToHandler(14);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void pause() throws RemoteException {
        this.postToHandler(12);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void play() throws RemoteException {
        this.postToHandler(7);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void playFromMediaId(String s, Bundle bundle0) throws RemoteException {
        this.postToHandler(8, s, bundle0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void playFromSearch(String s, Bundle bundle0) throws RemoteException {
        this.postToHandler(9, s, bundle0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void playFromUri(Uri uri0, Bundle bundle0) throws RemoteException {
        this.postToHandler(10, uri0, bundle0);
    }

    public void postToHandler(int v) {
        MediaSessionImplBase.this.postToHandler(v, 0, 0, null, null);
    }

    public void postToHandler(int v, int v1) {
        MediaSessionImplBase.this.postToHandler(v, v1, 0, null, null);
    }

    public void postToHandler(int v, Object object0) {
        MediaSessionImplBase.this.postToHandler(v, 0, 0, object0, null);
    }

    public void postToHandler(int v, Object object0, int v1) {
        MediaSessionImplBase.this.postToHandler(v, v1, 0, object0, null);
    }

    public void postToHandler(int v, Object object0, Bundle bundle0) {
        MediaSessionImplBase.this.postToHandler(v, 0, 0, object0, bundle0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void prepare() throws RemoteException {
        this.postToHandler(3);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void prepareFromMediaId(String s, Bundle bundle0) throws RemoteException {
        this.postToHandler(4, s, bundle0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void prepareFromSearch(String s, Bundle bundle0) throws RemoteException {
        this.postToHandler(5, s, bundle0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void prepareFromUri(Uri uri0, Bundle bundle0) throws RemoteException {
        this.postToHandler(6, uri0, bundle0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void previous() throws RemoteException {
        this.postToHandler(15);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void rate(RatingCompat ratingCompat0) throws RemoteException {
        this.postToHandler(19, ratingCompat0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void rateWithExtras(RatingCompat ratingCompat0, Bundle bundle0) throws RemoteException {
        this.postToHandler(0x1F, ratingCompat0, bundle0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void registerCallbackListener(IMediaControllerCallback iMediaControllerCallback0) {
        if(MediaSessionImplBase.this.mDestroyed) {
            try {
                iMediaControllerCallback0.onSessionDestroyed();
            }
            catch(Exception unused_ex) {
            }
            return;
        }
        RemoteUserInfo mediaSessionManager$RemoteUserInfo0 = new RemoteUserInfo("android.media.session.MediaController", Binder.getCallingPid(), Binder.getCallingUid());
        MediaSessionImplBase.this.mControllerCallbacks.register(iMediaControllerCallback0, mediaSessionManager$RemoteUserInfo0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void removeQueueItem(MediaDescriptionCompat mediaDescriptionCompat0) {
        this.postToHandler(27, mediaDescriptionCompat0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void removeQueueItemAt(int v) {
        this.postToHandler(28, v);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void rewind() throws RemoteException {
        this.postToHandler(17);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void seekTo(long v) throws RemoteException {
        this.postToHandler(18, v);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void sendCommand(String s, Bundle bundle0, ResultReceiverWrapper mediaSessionCompat$ResultReceiverWrapper0) {
        this.postToHandler(1, new MediaSessionCompat.MediaSessionImplBase.Command(s, bundle0, mediaSessionCompat$ResultReceiverWrapper0.mResultReceiver));
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void sendCustomAction(String s, Bundle bundle0) throws RemoteException {
        this.postToHandler(20, s, bundle0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public boolean sendMediaButton(KeyEvent keyEvent0) {
        if((MediaSessionImplBase.this.mFlags & 1) != 0) {
            this.postToHandler(21, keyEvent0);
        }
        return true;
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setCaptioningEnabled(boolean z) throws RemoteException {
        this.postToHandler(29, Boolean.valueOf(z));
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setRepeatMode(int v) throws RemoteException {
        this.postToHandler(23, v);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setShuffleMode(int v) throws RemoteException {
        this.postToHandler(30, v);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setShuffleModeEnabledRemoved(boolean z) throws RemoteException {
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void setVolumeTo(int v, int v1, String s) {
        MediaSessionImplBase.this.setVolumeTo(v, v1);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void skipToQueueItem(long v) {
        this.postToHandler(11, v);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void stop() throws RemoteException {
        this.postToHandler(13);
    }

    @Override  // android.supportv1.v4.media.session.IMediaSession
    public void unregisterCallbackListener(IMediaControllerCallback iMediaControllerCallback0) {
        MediaSessionImplBase.this.mControllerCallbacks.unregister(iMediaControllerCallback0);
    }
}


package android.supportv1.v4.media.session;

import android.os.Bundle;
import android.os.RemoteException;
import android.supportv1.v4.media.MediaMetadataCompat;
import java.lang.ref.WeakReference;
import java.util.List;

class MediaControllerCompat.Callback.StubCompat extends Stub {
    private final WeakReference mCallback;

    public MediaControllerCompat.Callback.StubCompat(Callback mediaControllerCompat$Callback0) {
        this.mCallback = new WeakReference(mediaControllerCompat$Callback0);
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onCaptioningEnabledChanged(boolean z) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(11, Boolean.valueOf(z), null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onEvent(String s, Bundle bundle0) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(1, s, bundle0);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onExtrasChanged(Bundle bundle0) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(7, bundle0, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onMetadataChanged(MediaMetadataCompat mediaMetadataCompat0) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(3, mediaMetadataCompat0, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onPlaybackStateChanged(PlaybackStateCompat playbackStateCompat0) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(2, playbackStateCompat0, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onQueueChanged(List list0) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(5, list0, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onQueueTitleChanged(CharSequence charSequence0) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(6, charSequence0, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onRepeatModeChanged(int v) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(9, v, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onSessionDestroyed() throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(8, null, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onSessionReady() throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(13, null, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onShuffleModeChanged(int v) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(12, v, null);
        }
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onShuffleModeChangedRemoved(boolean z) throws RemoteException {
    }

    @Override  // android.supportv1.v4.media.session.IMediaControllerCallback
    public void onVolumeInfoChanged(ParcelableVolumeInfo parcelableVolumeInfo0) throws RemoteException {
        Callback mediaControllerCompat$Callback0 = (Callback)this.mCallback.get();
        if(mediaControllerCompat$Callback0 != null) {
            mediaControllerCompat$Callback0.postToHandler(4, (parcelableVolumeInfo0 == null ? null : new PlaybackInfo(parcelableVolumeInfo0.volumeType, parcelableVolumeInfo0.audioStream, parcelableVolumeInfo0.controlType, parcelableVolumeInfo0.maxVolume, parcelableVolumeInfo0.currentVolume)), null);
        }
    }
}


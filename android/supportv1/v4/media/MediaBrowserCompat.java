package android.supportv1.v4.media;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.BadParcelableException;
import android.os.Binder;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.Parcel;
import android.os.Parcelable.Creator;
import android.os.Parcelable;
import android.os.RemoteException;
import android.supportv1.v4.app.BundleCompat;
import android.supportv1.v4.media.session.IMediaSession.Stub;
import android.supportv1.v4.media.session.IMediaSession;
import android.supportv1.v4.media.session.MediaSessionCompat.Token;
import android.supportv1.v4.media.session.MediaSessionCompat;
import android.supportv1.v4.os.ResultReceiver;
import android.supportv1.v4.util.ArrayMap;
import android.text.TextUtils;
import android.util.Log;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;

public final class MediaBrowserCompat {
    static class CallbackHandler extends Handler {
        private final WeakReference mCallbackImplRef;
        private WeakReference mCallbacksMessengerRef;

        public CallbackHandler(MediaBrowserServiceCallbackImpl mediaBrowserCompat$MediaBrowserServiceCallbackImpl0) {
            this.mCallbackImplRef = new WeakReference(mediaBrowserCompat$MediaBrowserServiceCallbackImpl0);
        }

        @Override  // android.os.Handler
        public void handleMessage(Message message0) {
            if(this.mCallbacksMessengerRef != null && this.mCallbacksMessengerRef.get() != null && this.mCallbackImplRef.get() != null) {
                Bundle bundle0 = message0.getData();
                MediaSessionCompat.ensureClassLoader(bundle0);
                MediaBrowserServiceCallbackImpl mediaBrowserCompat$MediaBrowserServiceCallbackImpl0 = (MediaBrowserServiceCallbackImpl)this.mCallbackImplRef.get();
                Messenger messenger0 = (Messenger)this.mCallbacksMessengerRef.get();
                try {
                    switch(message0.what) {
                        case 1: {
                            Bundle bundle1 = bundle0.getBundle("data_root_hints");
                            MediaSessionCompat.ensureClassLoader(bundle1);
                            mediaBrowserCompat$MediaBrowserServiceCallbackImpl0.onServiceConnected(messenger0, bundle0.getString("data_media_item_id"), ((Token)bundle0.getParcelable("data_media_session_token")), bundle1);
                            return;
                        }
                        case 2: {
                            mediaBrowserCompat$MediaBrowserServiceCallbackImpl0.onConnectionFailed(messenger0);
                            return;
                        }
                        case 3: {
                            Bundle bundle2 = bundle0.getBundle("data_options");
                            MediaSessionCompat.ensureClassLoader(bundle2);
                            Bundle bundle3 = bundle0.getBundle("data_notify_children_changed_options");
                            MediaSessionCompat.ensureClassLoader(bundle3);
                            mediaBrowserCompat$MediaBrowserServiceCallbackImpl0.onLoadChildren(messenger0, bundle0.getString("data_media_item_id"), bundle0.getParcelableArrayList("data_media_item_list"), bundle2, bundle3);
                        }
                    }
                }
                catch(BadParcelableException unused_ex) {
                    if(message0.what == 1) {
                        mediaBrowserCompat$MediaBrowserServiceCallbackImpl0.onConnectionFailed(messenger0);
                    }
                }
            }
        }

        public void setCallbacksMessenger(Messenger messenger0) {
            this.mCallbacksMessengerRef = new WeakReference(messenger0);
        }
    }

    public static class ConnectionCallback {
        MediaBrowserCompat.ConnectionCallback.ConnectionCallbackInternal mConnectionCallbackInternal;
        final Object mConnectionCallbackObj;

        public ConnectionCallback() {
            this.mConnectionCallbackObj = MediaBrowserCompatApi21.createConnectionCallback(new MediaBrowserCompat.ConnectionCallback.StubApi21(this));
        }

        public void onConnected() {
        }

        public void onConnectionFailed() {
        }

        public void onConnectionSuspended() {
        }

        public void setInternalConnectionCallback(MediaBrowserCompat.ConnectionCallback.ConnectionCallbackInternal mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0) {
            this.mConnectionCallbackInternal = mediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal0;
        }
    }

    public static abstract class CustomActionCallback {
        public void onError(String s, Bundle bundle0, Bundle bundle1) {
        }

        public void onProgressUpdate(String s, Bundle bundle0, Bundle bundle1) {
        }

        public void onResult(String s, Bundle bundle0, Bundle bundle1) {
        }
    }

    static class CustomActionResultReceiver extends ResultReceiver {
        private final String mAction;
        private final CustomActionCallback mCallback;
        private final Bundle mExtras;

        public CustomActionResultReceiver(String s, Bundle bundle0, CustomActionCallback mediaBrowserCompat$CustomActionCallback0, Handler handler0) {
            super(handler0);
            this.mAction = s;
            this.mExtras = bundle0;
            this.mCallback = mediaBrowserCompat$CustomActionCallback0;
        }

        @Override  // android.supportv1.v4.os.ResultReceiver
        public void onReceiveResult(int v, Bundle bundle0) {
            if(this.mCallback == null) {
                return;
            }
            MediaSessionCompat.ensureClassLoader(bundle0);
            switch(v) {
                case -1: {
                    return;
                }
                case 0: {
                    return;
                }
                default: {
                    if(v != 1) {
                        Objects.toString(this.mExtras);
                        Objects.toString(bundle0);
                    }
                }
            }
        }
    }

    public static abstract class ItemCallback {
        final Object mItemCallbackObj;

        public ItemCallback() {
            this.mItemCallbackObj = Build.VERSION.SDK_INT < 23 ? null : MediaBrowserCompatApi23.createItemCallback(new MediaBrowserCompat.ItemCallback.StubApi23(this));
        }

        public void onError(String s) {
        }

        public void onItemLoaded(MediaItem mediaBrowserCompat$MediaItem0) {
        }
    }

    static class ItemReceiver extends ResultReceiver {
        private final ItemCallback mCallback;
        private final String mMediaId;

        public ItemReceiver(String s, ItemCallback mediaBrowserCompat$ItemCallback0, Handler handler0) {
            super(handler0);
            this.mMediaId = s;
            this.mCallback = mediaBrowserCompat$ItemCallback0;
        }

        @Override  // android.supportv1.v4.os.ResultReceiver
        public void onReceiveResult(int v, Bundle bundle0) {
            MediaSessionCompat.ensureClassLoader(bundle0);
            if(v == 0 && bundle0 != null && bundle0.containsKey("media_item")) {
                Parcelable parcelable0 = bundle0.getParcelable("media_item");
                if(parcelable0 != null && !(parcelable0 instanceof MediaItem)) {
                }
            }
        }
    }

    interface MediaBrowserImpl {
        void connect();

        void disconnect();

        Bundle getExtras();

        void getItem(String arg1, ItemCallback arg2);

        Bundle getNotifyChildrenChangedOptions();

        String getRoot();

        ComponentName getServiceComponent();

        Token getSessionToken();

        boolean isConnected();

        void search(String arg1, Bundle arg2, SearchCallback arg3);

        void sendCustomAction(String arg1, Bundle arg2, CustomActionCallback arg3);

        void subscribe(String arg1, Bundle arg2, SubscriptionCallback arg3);

        void unsubscribe(String arg1, SubscriptionCallback arg2);
    }

    static class MediaBrowserImplApi21 implements MediaBrowserCompat.ConnectionCallback.ConnectionCallbackInternal, MediaBrowserImpl, MediaBrowserServiceCallbackImpl {
        protected final Object mBrowserObj;
        protected Messenger mCallbacksMessenger;
        final Context mContext;
        protected final CallbackHandler mHandler;
        private Token mMediaSessionToken;
        private Bundle mNotifyChildrenChangedOptions;
        protected final Bundle mRootHints;
        protected ServiceBinderWrapper mServiceBinderWrapper;
        protected int mServiceVersion;
        private final ArrayMap mSubscriptions;

        public MediaBrowserImplApi21(Context context0, ComponentName componentName0, ConnectionCallback mediaBrowserCompat$ConnectionCallback0, Bundle bundle0) {
            this.mHandler = new CallbackHandler(this);
            this.mSubscriptions = new ArrayMap();
            this.mContext = context0;
            Bundle bundle1 = bundle0 == null ? new Bundle() : new Bundle(bundle0);
            this.mRootHints = bundle1;
            bundle1.putInt("extra_client_version", 1);
            mediaBrowserCompat$ConnectionCallback0.setInternalConnectionCallback(this);
            this.mBrowserObj = MediaBrowserCompatApi21.createBrowser(context0, componentName0, mediaBrowserCompat$ConnectionCallback0.mConnectionCallbackObj, bundle1);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void connect() {
            MediaBrowserCompatApi21.connect(this.mBrowserObj);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void disconnect() {
            ServiceBinderWrapper mediaBrowserCompat$ServiceBinderWrapper0 = this.mServiceBinderWrapper;
            if(mediaBrowserCompat$ServiceBinderWrapper0 != null) {
                Messenger messenger0 = this.mCallbacksMessenger;
                if(messenger0 != null) {
                    try {
                        mediaBrowserCompat$ServiceBinderWrapper0.unregisterCallbackMessenger(messenger0);
                    }
                    catch(RemoteException unused_ex) {
                    }
                }
            }
            MediaBrowserCompatApi21.disconnect(this.mBrowserObj);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public Bundle getExtras() {
            return MediaBrowserCompatApi21.getExtras(this.mBrowserObj);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void getItem(String s, ItemCallback mediaBrowserCompat$ItemCallback0) {
            android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplApi21.1 mediaBrowserCompat$MediaBrowserImplApi21$10;
            CallbackHandler mediaBrowserCompat$CallbackHandler0;
            if(TextUtils.isEmpty(s)) {
                throw new IllegalArgumentException("mediaId is empty");
            }
            if(mediaBrowserCompat$ItemCallback0 == null) {
                throw new IllegalArgumentException("cb is null");
            }
            boolean z = false;
            if(!MediaBrowserCompatApi21.isConnected(this.mBrowserObj)) {
                z = true;
                mediaBrowserCompat$CallbackHandler0 = this.mHandler;
                mediaBrowserCompat$MediaBrowserImplApi21$10 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
            }
            else if(this.mServiceBinderWrapper == null) {
                z = true;
                mediaBrowserCompat$CallbackHandler0 = this.mHandler;
                mediaBrowserCompat$MediaBrowserImplApi21$10 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
            }
            if(z) {
                mediaBrowserCompat$CallbackHandler0.post(mediaBrowserCompat$MediaBrowserImplApi21$10);
                return;
            }
            ItemReceiver mediaBrowserCompat$ItemReceiver0 = new ItemReceiver(s, mediaBrowserCompat$ItemCallback0, this.mHandler);
            try {
                this.mServiceBinderWrapper.getMediaItem(s, mediaBrowserCompat$ItemReceiver0, this.mCallbacksMessenger);
            }
            catch(RemoteException unused_ex) {
                android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplApi21.3 mediaBrowserCompat$MediaBrowserImplApi21$30 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
                this.mHandler.post(mediaBrowserCompat$MediaBrowserImplApi21$30);
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public Bundle getNotifyChildrenChangedOptions() {
            return this.mNotifyChildrenChangedOptions;
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public String getRoot() {
            return MediaBrowserCompatApi21.getRoot(this.mBrowserObj);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public ComponentName getServiceComponent() {
            return MediaBrowserCompatApi21.getServiceComponent(this.mBrowserObj);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public Token getSessionToken() {
            if(this.mMediaSessionToken == null) {
                this.mMediaSessionToken = Token.fromToken(MediaBrowserCompatApi21.getSessionToken(this.mBrowserObj));
            }
            return this.mMediaSessionToken;
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public boolean isConnected() {
            return MediaBrowserCompatApi21.isConnected(this.mBrowserObj);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal
        public void onConnected() {
            Bundle bundle0 = MediaBrowserCompatApi21.getExtras(this.mBrowserObj);
            if(bundle0 == null) {
                return;
            }
            this.mServiceVersion = bundle0.getInt("extra_service_version", 0);
            IBinder iBinder0 = BundleCompat.getBinder(bundle0, "extra_messenger");
            if(iBinder0 != null) {
                this.mServiceBinderWrapper = new ServiceBinderWrapper(iBinder0, this.mRootHints);
                Messenger messenger0 = new Messenger(this.mHandler);
                this.mCallbacksMessenger = messenger0;
                this.mHandler.setCallbacksMessenger(messenger0);
                try {
                    this.mServiceBinderWrapper.registerCallbackMessenger(this.mContext, this.mCallbacksMessenger);
                }
                catch(RemoteException unused_ex) {
                }
            }
            IMediaSession iMediaSession0 = Stub.asInterface(BundleCompat.getBinder(bundle0, "extra_session_binder"));
            if(iMediaSession0 != null) {
                this.mMediaSessionToken = Token.fromToken(MediaBrowserCompatApi21.getSessionToken(this.mBrowserObj), iMediaSession0);
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal
        public void onConnectionFailed() {
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserServiceCallbackImpl
        public void onConnectionFailed(Messenger messenger0) {
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$ConnectionCallback$ConnectionCallbackInternal
        public void onConnectionSuspended() {
            this.mServiceBinderWrapper = null;
            this.mCallbacksMessenger = null;
            this.mMediaSessionToken = null;
            this.mHandler.setCallbacksMessenger(null);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserServiceCallbackImpl
        public void onLoadChildren(Messenger messenger0, String s, List list0, Bundle bundle0, Bundle bundle1) {
            if(this.mCallbacksMessenger != messenger0) {
                return;
            }
            Subscription mediaBrowserCompat$Subscription0 = (Subscription)this.mSubscriptions.get(s);
            if(mediaBrowserCompat$Subscription0 == null) {
                return;
            }
            if(mediaBrowserCompat$Subscription0.getCallback(bundle0) != null) {
                if(bundle0 == null) {
                    if(list0 == null) {
                        return;
                    }
                }
                else if(list0 == null) {
                    return;
                }
                this.mNotifyChildrenChangedOptions = bundle1;
                this.mNotifyChildrenChangedOptions = null;
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserServiceCallbackImpl
        public void onServiceConnected(Messenger messenger0, String s, Token mediaSessionCompat$Token0, Bundle bundle0) {
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void search(String s, Bundle bundle0, SearchCallback mediaBrowserCompat$SearchCallback0) {
            if(!this.isConnected()) {
                throw new IllegalStateException("search() called while not connected");
            }
            if(this.mServiceBinderWrapper == null) {
                android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplApi21.4 mediaBrowserCompat$MediaBrowserImplApi21$40 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
                this.mHandler.post(mediaBrowserCompat$MediaBrowserImplApi21$40);
                return;
            }
            SearchResultReceiver mediaBrowserCompat$SearchResultReceiver0 = new SearchResultReceiver(s, bundle0, mediaBrowserCompat$SearchCallback0, this.mHandler);
            try {
                this.mServiceBinderWrapper.search(s, bundle0, mediaBrowserCompat$SearchResultReceiver0, this.mCallbacksMessenger);
            }
            catch(RemoteException unused_ex) {
                android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplApi21.5 mediaBrowserCompat$MediaBrowserImplApi21$50 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
                this.mHandler.post(mediaBrowserCompat$MediaBrowserImplApi21$50);
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void sendCustomAction(String s, Bundle bundle0, CustomActionCallback mediaBrowserCompat$CustomActionCallback0) {
            if(!this.isConnected()) {
                throw new IllegalStateException("Cannot send a custom action (" + s + ") with extras " + bundle0 + " because the browser is not connected to the service.");
            }
            if(this.mServiceBinderWrapper == null && mediaBrowserCompat$CustomActionCallback0 != null) {
                android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplApi21.6 mediaBrowserCompat$MediaBrowserImplApi21$60 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
                this.mHandler.post(mediaBrowserCompat$MediaBrowserImplApi21$60);
            }
            CustomActionResultReceiver mediaBrowserCompat$CustomActionResultReceiver0 = new CustomActionResultReceiver(s, bundle0, mediaBrowserCompat$CustomActionCallback0, this.mHandler);
            try {
                this.mServiceBinderWrapper.sendCustomAction(s, bundle0, mediaBrowserCompat$CustomActionResultReceiver0, this.mCallbacksMessenger);
            }
            catch(RemoteException unused_ex) {
                Objects.toString(bundle0);
                if(mediaBrowserCompat$CustomActionCallback0 != null) {
                    android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplApi21.7 mediaBrowserCompat$MediaBrowserImplApi21$70 = new Runnable() {
                        @Override
                        public void run() {
                        }
                    };
                    this.mHandler.post(mediaBrowserCompat$MediaBrowserImplApi21$70);
                }
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void subscribe(String s, Bundle bundle0, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
            Subscription mediaBrowserCompat$Subscription0 = (Subscription)this.mSubscriptions.get(s);
            if(mediaBrowserCompat$Subscription0 == null) {
                mediaBrowserCompat$Subscription0 = new Subscription();
                this.mSubscriptions.put(s, mediaBrowserCompat$Subscription0);
            }
            mediaBrowserCompat$SubscriptionCallback0.setSubscription(mediaBrowserCompat$Subscription0);
            Bundle bundle1 = bundle0 == null ? null : new Bundle(bundle0);
            mediaBrowserCompat$Subscription0.putCallback(bundle1, mediaBrowserCompat$SubscriptionCallback0);
            ServiceBinderWrapper mediaBrowserCompat$ServiceBinderWrapper0 = this.mServiceBinderWrapper;
            if(mediaBrowserCompat$ServiceBinderWrapper0 == null) {
                MediaBrowserCompatApi21.subscribe(this.mBrowserObj, s, mediaBrowserCompat$SubscriptionCallback0.mSubscriptionCallbackObj);
                return;
            }
            try {
                mediaBrowserCompat$ServiceBinderWrapper0.addSubscription(s, mediaBrowserCompat$SubscriptionCallback0.mToken, bundle1, this.mCallbacksMessenger);
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void unsubscribe(String s, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
            Subscription mediaBrowserCompat$Subscription0 = (Subscription)this.mSubscriptions.get(s);
            if(mediaBrowserCompat$Subscription0 == null) {
                return;
            }
            ServiceBinderWrapper mediaBrowserCompat$ServiceBinderWrapper0 = this.mServiceBinderWrapper;
            if(mediaBrowserCompat$ServiceBinderWrapper0 != null) {
                try {
                    if(mediaBrowserCompat$SubscriptionCallback0 == null) {
                        mediaBrowserCompat$ServiceBinderWrapper0.removeSubscription(s, null, this.mCallbacksMessenger);
                    }
                    else {
                        List list2 = mediaBrowserCompat$Subscription0.getCallbacks();
                        List list3 = mediaBrowserCompat$Subscription0.getOptionsList();
                        for(int v1 = list2.size() - 1; v1 >= 0; --v1) {
                            if(list2.get(v1) == mediaBrowserCompat$SubscriptionCallback0) {
                                this.mServiceBinderWrapper.removeSubscription(s, mediaBrowserCompat$SubscriptionCallback0.mToken, this.mCallbacksMessenger);
                                list2.remove(v1);
                                list3.remove(v1);
                            }
                        }
                    }
                }
                catch(RemoteException unused_ex) {
                }
            }
            else if(mediaBrowserCompat$SubscriptionCallback0 != null) {
                List list0 = mediaBrowserCompat$Subscription0.getCallbacks();
                List list1 = mediaBrowserCompat$Subscription0.getOptionsList();
                for(int v = list0.size() - 1; v >= 0; --v) {
                    if(list0.get(v) == mediaBrowserCompat$SubscriptionCallback0) {
                        list0.remove(v);
                        list1.remove(v);
                    }
                }
                if(list0.size() == 0) {
                    MediaBrowserCompatApi21.unsubscribe(this.mBrowserObj, s);
                }
            }
            else {
                MediaBrowserCompatApi21.unsubscribe(this.mBrowserObj, s);
            }
            if(mediaBrowserCompat$Subscription0.isEmpty() || mediaBrowserCompat$SubscriptionCallback0 == null) {
                this.mSubscriptions.remove(s);
            }
        }
    }

    static class MediaBrowserImplApi23 extends MediaBrowserImplApi21 {
        public MediaBrowserImplApi23(Context context0, ComponentName componentName0, ConnectionCallback mediaBrowserCompat$ConnectionCallback0, Bundle bundle0) {
            super(context0, componentName0, mediaBrowserCompat$ConnectionCallback0, bundle0);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImplApi21
        public void getItem(String s, ItemCallback mediaBrowserCompat$ItemCallback0) {
            if(this.mServiceBinderWrapper == null) {
                MediaBrowserCompatApi23.getItem(this.mBrowserObj, s, mediaBrowserCompat$ItemCallback0.mItemCallbackObj);
                return;
            }
            super.getItem(s, mediaBrowserCompat$ItemCallback0);
        }
    }

    static class MediaBrowserImplApi26 extends MediaBrowserImplApi23 {
        public MediaBrowserImplApi26(Context context0, ComponentName componentName0, ConnectionCallback mediaBrowserCompat$ConnectionCallback0, Bundle bundle0) {
            super(context0, componentName0, mediaBrowserCompat$ConnectionCallback0, bundle0);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImplApi21
        public void subscribe(String s, Bundle bundle0, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
            if(this.mServiceBinderWrapper != null && this.mServiceVersion >= 2) {
                super.subscribe(s, bundle0, mediaBrowserCompat$SubscriptionCallback0);
                return;
            }
            if(bundle0 == null) {
                MediaBrowserCompatApi21.subscribe(this.mBrowserObj, s, mediaBrowserCompat$SubscriptionCallback0.mSubscriptionCallbackObj);
                return;
            }
            MediaBrowserCompatApi26.subscribe(this.mBrowserObj, s, bundle0, mediaBrowserCompat$SubscriptionCallback0.mSubscriptionCallbackObj);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImplApi21
        public void unsubscribe(String s, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
            if(this.mServiceBinderWrapper != null && this.mServiceVersion >= 2) {
                super.unsubscribe(s, mediaBrowserCompat$SubscriptionCallback0);
                return;
            }
            if(mediaBrowserCompat$SubscriptionCallback0 == null) {
                MediaBrowserCompatApi21.unsubscribe(this.mBrowserObj, s);
                return;
            }
            MediaBrowserCompatApi26.unsubscribe(this.mBrowserObj, s, mediaBrowserCompat$SubscriptionCallback0.mSubscriptionCallbackObj);
        }
    }

    static class MediaBrowserImplBase implements MediaBrowserImpl, MediaBrowserServiceCallbackImpl {
        static final int CONNECT_STATE_CONNECTED = 3;
        static final int CONNECT_STATE_CONNECTING = 2;
        static final int CONNECT_STATE_DISCONNECTED = 1;
        static final int CONNECT_STATE_DISCONNECTING = 0;
        static final int CONNECT_STATE_SUSPENDED = 4;
        final ConnectionCallback mCallback;
        Messenger mCallbacksMessenger;
        final Context mContext;
        private Bundle mExtras;
        final CallbackHandler mHandler;
        private Token mMediaSessionToken;
        private Bundle mNotifyChildrenChangedOptions;
        final Bundle mRootHints;
        private String mRootId;
        ServiceBinderWrapper mServiceBinderWrapper;
        final ComponentName mServiceComponent;
        MediaBrowserCompat.MediaBrowserImplBase.MediaServiceConnection mServiceConnection;
        int mState;
        private final ArrayMap mSubscriptions;

        public MediaBrowserImplBase(Context context0, ComponentName componentName0, ConnectionCallback mediaBrowserCompat$ConnectionCallback0, Bundle bundle0) {
            this.mHandler = new CallbackHandler(this);
            this.mSubscriptions = new ArrayMap();
            this.mState = 1;
            if(context0 == null) {
                throw new IllegalArgumentException("context must not be null");
            }
            if(componentName0 == null) {
                throw new IllegalArgumentException("service component must not be null");
            }
            if(mediaBrowserCompat$ConnectionCallback0 == null) {
                throw new IllegalArgumentException("connection callback must not be null");
            }
            this.mContext = context0;
            this.mServiceComponent = componentName0;
            this.mCallback = mediaBrowserCompat$ConnectionCallback0;
            this.mRootHints = bundle0 == null ? null : new Bundle(bundle0);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void connect() {
            if(this.mState != 0 && this.mState != 1) {
                throw new IllegalStateException("connect() called while neigther disconnecting nor disconnected (state=" + MediaBrowserImplBase.getStateLabel(this.mState) + ")");
            }
            this.mState = 2;
            android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplBase.1 mediaBrowserCompat$MediaBrowserImplBase$10 = new Runnable() {
                @Override
                public void run() {
                    MediaBrowserImplBase mediaBrowserCompat$MediaBrowserImplBase0 = MediaBrowserImplBase.this;
                    if(mediaBrowserCompat$MediaBrowserImplBase0.mState == 0) {
                        return;
                    }
                    mediaBrowserCompat$MediaBrowserImplBase0.mState = 2;
                    if(MediaBrowserCompat.DEBUG && mediaBrowserCompat$MediaBrowserImplBase0.mServiceConnection != null) {
                        throw new RuntimeException("mServiceConnection should be null. Instead it is " + MediaBrowserImplBase.this.mServiceConnection);
                    }
                    if(mediaBrowserCompat$MediaBrowserImplBase0.mServiceBinderWrapper != null) {
                        throw new RuntimeException("mServiceBinderWrapper should be null. Instead it is " + MediaBrowserImplBase.this.mServiceBinderWrapper);
                    }
                    if(mediaBrowserCompat$MediaBrowserImplBase0.mCallbacksMessenger != null) {
                        throw new RuntimeException("mCallbacksMessenger should be null. Instead it is " + MediaBrowserImplBase.this.mCallbacksMessenger);
                    }
                    Intent intent0 = new Intent("android.media.browse.MediaBrowserService");
                    intent0.setComponent(MediaBrowserImplBase.this.mServiceComponent);
                    MediaBrowserImplBase.this.mServiceConnection = new MediaBrowserCompat.MediaBrowserImplBase.MediaServiceConnection(MediaBrowserImplBase.this);
                    try {
                        if(!MediaBrowserImplBase.this.mContext.bindService(intent0, MediaBrowserImplBase.this.mServiceConnection, 1)) {
                            goto label_14;
                        }
                        goto label_16;
                    }
                    catch(Exception unused_ex) {
                        Objects.toString(MediaBrowserImplBase.this.mServiceComponent);
                    }
                label_14:
                    MediaBrowserImplBase.this.forceCloseConnection();
                    MediaBrowserImplBase.this.mCallback.onConnectionFailed();
                label_16:
                    if(MediaBrowserCompat.DEBUG) {
                        MediaBrowserImplBase.this.dump();
                    }
                }
            };
            this.mHandler.post(mediaBrowserCompat$MediaBrowserImplBase$10);
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void disconnect() {
            this.mState = 0;
            android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplBase.2 mediaBrowserCompat$MediaBrowserImplBase$20 = new Runnable() {
                @Override
                public void run() {
                    MediaBrowserImplBase mediaBrowserCompat$MediaBrowserImplBase0 = MediaBrowserImplBase.this;
                    Messenger messenger0 = mediaBrowserCompat$MediaBrowserImplBase0.mCallbacksMessenger;
                    if(messenger0 != null) {
                        try {
                            mediaBrowserCompat$MediaBrowserImplBase0.mServiceBinderWrapper.disconnect(messenger0);
                        }
                        catch(RemoteException unused_ex) {
                            Objects.toString(MediaBrowserImplBase.this.mServiceComponent);
                        }
                    }
                    int v = MediaBrowserImplBase.this.mState;
                    MediaBrowserImplBase.this.forceCloseConnection();
                    if(v != 0) {
                        MediaBrowserImplBase.this.mState = v;
                    }
                    if(MediaBrowserCompat.DEBUG) {
                        MediaBrowserImplBase.this.dump();
                    }
                }
            };
            this.mHandler.post(mediaBrowserCompat$MediaBrowserImplBase$20);
        }

        public void dump() {
            Objects.toString(this.mServiceComponent);
            Objects.toString(this.mCallback);
            Objects.toString(this.mRootHints);
            MediaBrowserImplBase.getStateLabel(this.mState);
            Objects.toString(this.mServiceConnection);
            Objects.toString(this.mServiceBinderWrapper);
            Objects.toString(this.mCallbacksMessenger);
            Objects.toString(this.mMediaSessionToken);
        }

        public void forceCloseConnection() {
            MediaBrowserCompat.MediaBrowserImplBase.MediaServiceConnection mediaBrowserCompat$MediaBrowserImplBase$MediaServiceConnection0 = this.mServiceConnection;
            if(mediaBrowserCompat$MediaBrowserImplBase$MediaServiceConnection0 != null) {
                this.mContext.unbindService(mediaBrowserCompat$MediaBrowserImplBase$MediaServiceConnection0);
            }
            this.mState = 1;
            this.mServiceConnection = null;
            this.mServiceBinderWrapper = null;
            this.mCallbacksMessenger = null;
            this.mHandler.setCallbacksMessenger(null);
            this.mRootId = null;
            this.mMediaSessionToken = null;
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public Bundle getExtras() {
            if(!this.isConnected()) {
                throw new IllegalStateException("getExtras() called while not connected (state=" + MediaBrowserImplBase.getStateLabel(this.mState) + ")");
            }
            return this.mExtras;
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void getItem(String s, ItemCallback mediaBrowserCompat$ItemCallback0) {
            if(TextUtils.isEmpty(s)) {
                throw new IllegalArgumentException("mediaId is empty");
            }
            if(mediaBrowserCompat$ItemCallback0 == null) {
                throw new IllegalArgumentException("cb is null");
            }
            if(!this.isConnected()) {
                android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplBase.3 mediaBrowserCompat$MediaBrowserImplBase$30 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
                this.mHandler.post(mediaBrowserCompat$MediaBrowserImplBase$30);
                return;
            }
            ItemReceiver mediaBrowserCompat$ItemReceiver0 = new ItemReceiver(s, mediaBrowserCompat$ItemCallback0, this.mHandler);
            try {
                this.mServiceBinderWrapper.getMediaItem(s, mediaBrowserCompat$ItemReceiver0, this.mCallbacksMessenger);
            }
            catch(RemoteException unused_ex) {
                android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplBase.4 mediaBrowserCompat$MediaBrowserImplBase$40 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
                this.mHandler.post(mediaBrowserCompat$MediaBrowserImplBase$40);
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public Bundle getNotifyChildrenChangedOptions() {
            return this.mNotifyChildrenChangedOptions;
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public String getRoot() {
            if(!this.isConnected()) {
                throw new IllegalStateException("getRoot() called while not connected(state=" + MediaBrowserImplBase.getStateLabel(this.mState) + ")");
            }
            return this.mRootId;
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public ComponentName getServiceComponent() {
            if(!this.isConnected()) {
                throw new IllegalStateException("getServiceComponent() called while not connected (state=" + this.mState + ")");
            }
            return this.mServiceComponent;
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public Token getSessionToken() {
            if(!this.isConnected()) {
                throw new IllegalStateException("getSessionToken() called while not connected(state=" + this.mState + ")");
            }
            return this.mMediaSessionToken;
        }

        private static String getStateLabel(int v) {
            switch(v) {
                case 0: {
                    return "CONNECT_STATE_DISCONNECTING";
                }
                case 1: {
                    return "CONNECT_STATE_DISCONNECTED";
                }
                case 2: {
                    return "CONNECT_STATE_CONNECTING";
                }
                case 3: {
                    return "CONNECT_STATE_CONNECTED";
                }
                case 4: {
                    return "CONNECT_STATE_SUSPENDED";
                }
                default: {
                    return "UNKNOWN/" + v;
                }
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public boolean isConnected() {
            return this.mState == 3;
        }

        private boolean isCurrent(Messenger messenger0, String s) {
            if(this.mCallbacksMessenger == messenger0 && (this.mState != 0 && this.mState != 1)) {
                return true;
            }
            if(this.mState != 0 && this.mState != 1) {
                Objects.toString(this.mServiceComponent);
                Objects.toString(this.mCallbacksMessenger);
            }
            return false;
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserServiceCallbackImpl
        public void onConnectionFailed(Messenger messenger0) {
            Objects.toString(this.mServiceComponent);
            if(!this.isCurrent(messenger0, "onConnectFailed")) {
                return;
            }
            int v = this.mState;
            if(v != 2) {
                MediaBrowserImplBase.getStateLabel(v);
                return;
            }
            this.forceCloseConnection();
            this.mCallback.onConnectionFailed();
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserServiceCallbackImpl
        public void onLoadChildren(Messenger messenger0, String s, List list0, Bundle bundle0, Bundle bundle1) {
            if(!this.isCurrent(messenger0, "onLoadChildren")) {
                return;
            }
            if(MediaBrowserCompat.DEBUG) {
                Objects.toString(this.mServiceComponent);
            }
            Subscription mediaBrowserCompat$Subscription0 = (Subscription)this.mSubscriptions.get(s);
            if(mediaBrowserCompat$Subscription0 == null) {
                return;
            }
            if(mediaBrowserCompat$Subscription0.getCallback(bundle0) != null) {
                if(bundle0 == null) {
                    if(list0 == null) {
                        return;
                    }
                }
                else if(list0 == null) {
                    return;
                }
                this.mNotifyChildrenChangedOptions = bundle1;
                this.mNotifyChildrenChangedOptions = null;
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserServiceCallbackImpl
        public void onServiceConnected(Messenger messenger0, String s, Token mediaSessionCompat$Token0, Bundle bundle0) {
            if(!this.isCurrent(messenger0, "onConnect")) {
                return;
            }
            int v = this.mState;
            if(v != 2) {
                MediaBrowserImplBase.getStateLabel(v);
                return;
            }
            this.mRootId = s;
            this.mMediaSessionToken = mediaSessionCompat$Token0;
            this.mExtras = bundle0;
            this.mState = 3;
            if(MediaBrowserCompat.DEBUG) {
                this.dump();
            }
            this.mCallback.onConnected();
            try {
                for(Object object0: this.mSubscriptions.entrySet()) {
                    String s1 = (String)((Map.Entry)object0).getKey();
                    Subscription mediaBrowserCompat$Subscription0 = (Subscription)((Map.Entry)object0).getValue();
                    List list0 = mediaBrowserCompat$Subscription0.getCallbacks();
                    List list1 = mediaBrowserCompat$Subscription0.getOptionsList();
                    for(int v1 = 0; v1 < list0.size(); ++v1) {
                        this.mServiceBinderWrapper.addSubscription(s1, ((SubscriptionCallback)list0.get(v1)).mToken, ((Bundle)list1.get(v1)), this.mCallbacksMessenger);
                    }
                }
            }
            catch(RemoteException unused_ex) {
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void search(String s, Bundle bundle0, SearchCallback mediaBrowserCompat$SearchCallback0) {
            if(!this.isConnected()) {
                throw new IllegalStateException("search() called while not connected (state=" + MediaBrowserImplBase.getStateLabel(this.mState) + ")");
            }
            SearchResultReceiver mediaBrowserCompat$SearchResultReceiver0 = new SearchResultReceiver(s, bundle0, mediaBrowserCompat$SearchCallback0, this.mHandler);
            try {
                this.mServiceBinderWrapper.search(s, bundle0, mediaBrowserCompat$SearchResultReceiver0, this.mCallbacksMessenger);
            }
            catch(RemoteException unused_ex) {
                android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplBase.5 mediaBrowserCompat$MediaBrowserImplBase$50 = new Runnable() {
                    @Override
                    public void run() {
                    }
                };
                this.mHandler.post(mediaBrowserCompat$MediaBrowserImplBase$50);
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void sendCustomAction(String s, Bundle bundle0, CustomActionCallback mediaBrowserCompat$CustomActionCallback0) {
            if(!this.isConnected()) {
                throw new IllegalStateException("Cannot send a custom action (" + s + ") with extras " + bundle0 + " because the browser is not connected to the service.");
            }
            CustomActionResultReceiver mediaBrowserCompat$CustomActionResultReceiver0 = new CustomActionResultReceiver(s, bundle0, mediaBrowserCompat$CustomActionCallback0, this.mHandler);
            try {
                this.mServiceBinderWrapper.sendCustomAction(s, bundle0, mediaBrowserCompat$CustomActionResultReceiver0, this.mCallbacksMessenger);
            }
            catch(RemoteException unused_ex) {
                Objects.toString(bundle0);
                if(mediaBrowserCompat$CustomActionCallback0 != null) {
                    android.supportv1.v4.media.MediaBrowserCompat.MediaBrowserImplBase.6 mediaBrowserCompat$MediaBrowserImplBase$60 = new Runnable() {
                        @Override
                        public void run() {
                        }
                    };
                    this.mHandler.post(mediaBrowserCompat$MediaBrowserImplBase$60);
                }
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void subscribe(String s, Bundle bundle0, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
            Subscription mediaBrowserCompat$Subscription0 = (Subscription)this.mSubscriptions.get(s);
            if(mediaBrowserCompat$Subscription0 == null) {
                mediaBrowserCompat$Subscription0 = new Subscription();
                this.mSubscriptions.put(s, mediaBrowserCompat$Subscription0);
            }
            Bundle bundle1 = bundle0 == null ? null : new Bundle(bundle0);
            mediaBrowserCompat$Subscription0.putCallback(bundle1, mediaBrowserCompat$SubscriptionCallback0);
            if(this.isConnected()) {
                try {
                    this.mServiceBinderWrapper.addSubscription(s, mediaBrowserCompat$SubscriptionCallback0.mToken, bundle1, this.mCallbacksMessenger);
                }
                catch(RemoteException unused_ex) {
                }
            }
        }

        @Override  // android.supportv1.v4.media.MediaBrowserCompat$MediaBrowserImpl
        public void unsubscribe(String s, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
            Subscription mediaBrowserCompat$Subscription0 = (Subscription)this.mSubscriptions.get(s);
            if(mediaBrowserCompat$Subscription0 == null) {
                return;
            }
            try {
                if(mediaBrowserCompat$SubscriptionCallback0 != null) {
                    List list0 = mediaBrowserCompat$Subscription0.getCallbacks();
                    List list1 = mediaBrowserCompat$Subscription0.getOptionsList();
                    for(int v = list0.size() - 1; v >= 0; --v) {
                        if(list0.get(v) == mediaBrowserCompat$SubscriptionCallback0) {
                            if(this.isConnected()) {
                                this.mServiceBinderWrapper.removeSubscription(s, mediaBrowserCompat$SubscriptionCallback0.mToken, this.mCallbacksMessenger);
                            }
                            list0.remove(v);
                            list1.remove(v);
                        }
                    }
                }
                else if(this.isConnected()) {
                    this.mServiceBinderWrapper.removeSubscription(s, null, this.mCallbacksMessenger);
                }
            }
            catch(RemoteException unused_ex) {
            }
            if(mediaBrowserCompat$Subscription0.isEmpty() || mediaBrowserCompat$SubscriptionCallback0 == null) {
                this.mSubscriptions.remove(s);
            }
        }
    }

    interface MediaBrowserServiceCallbackImpl {
        void onConnectionFailed(Messenger arg1);

        void onLoadChildren(Messenger arg1, String arg2, List arg3, Bundle arg4, Bundle arg5);

        void onServiceConnected(Messenger arg1, String arg2, Token arg3, Bundle arg4);
    }

    public static class MediaItem implements Parcelable {
        public static final Parcelable.Creator CREATOR = null;
        public static final int FLAG_BROWSABLE = 1;
        public static final int FLAG_PLAYABLE = 2;
        private final MediaDescriptionCompat mDescription;
        private final int mFlags;

        static {
            MediaItem.CREATOR = new Parcelable.Creator() {
                public MediaItem createFromParcel(Parcel parcel0) {
                    return new MediaItem(parcel0);
                }

                @Override  // android.os.Parcelable$Creator
                public Object createFromParcel(Parcel parcel0) {
                    return this.createFromParcel(parcel0);
                }

                public MediaItem[] newArray(int v) {
                    return new MediaItem[v];
                }

                @Override  // android.os.Parcelable$Creator
                public Object[] newArray(int v) {
                    return this.newArray(v);
                }
            };
        }

        public MediaItem(Parcel parcel0) {
            this.mFlags = parcel0.readInt();
            this.mDescription = (MediaDescriptionCompat)MediaDescriptionCompat.CREATOR.createFromParcel(parcel0);
        }

        public MediaItem(MediaDescriptionCompat mediaDescriptionCompat0, int v) {
            if(mediaDescriptionCompat0 == null) {
                throw new IllegalArgumentException("description cannot be null");
            }
            if(TextUtils.isEmpty(mediaDescriptionCompat0.getMediaId())) {
                throw new IllegalArgumentException("description must have a non-empty media id");
            }
            this.mFlags = v;
            this.mDescription = mediaDescriptionCompat0;
        }

        @Override  // android.os.Parcelable
        public int describeContents() {
            return 0;
        }

        public static MediaItem fromMediaItem(Object object0) {
            if(object0 != null) {
                int v = android.supportv1.v4.media.MediaBrowserCompatApi21.MediaItem.getFlags(object0);
                return new MediaItem(MediaDescriptionCompat.fromMediaDescription(android.supportv1.v4.media.MediaBrowserCompatApi21.MediaItem.getDescription(object0)), v);
            }
            return null;
        }

        public static List fromMediaItemList(List list0) {
            if(list0 != null) {
                List list1 = new ArrayList(list0.size());
                for(Object object0: list0) {
                    ((ArrayList)list1).add(MediaItem.fromMediaItem(object0));
                }
                return list1;
            }
            return null;
        }

        public MediaDescriptionCompat getDescription() {
            return this.mDescription;
        }

        public int getFlags() {
            return this.mFlags;
        }

        public String getMediaId() {
            return this.mDescription.getMediaId();
        }

        public boolean isBrowsable() {
            return (this.mFlags & 1) != 0;
        }

        public boolean isPlayable() {
            return (this.mFlags & 2) != 0;
        }

        @Override
        public String toString() {
            return "MediaItem{mFlags=" + this.mFlags + ", mDescription=" + this.mDescription + '}';
        }

        @Override  // android.os.Parcelable
        public void writeToParcel(Parcel parcel0, int v) {
            parcel0.writeInt(this.mFlags);
            this.mDescription.writeToParcel(parcel0, v);
        }
    }

    public static abstract class SearchCallback {
        public void onError(String s, Bundle bundle0) {
        }

        public void onSearchResult(String s, Bundle bundle0, List list0) {
        }
    }

    static class SearchResultReceiver extends ResultReceiver {
        private final SearchCallback mCallback;
        private final Bundle mExtras;
        private final String mQuery;

        public SearchResultReceiver(String s, Bundle bundle0, SearchCallback mediaBrowserCompat$SearchCallback0, Handler handler0) {
            super(handler0);
            this.mQuery = s;
            this.mExtras = bundle0;
            this.mCallback = mediaBrowserCompat$SearchCallback0;
        }

        @Override  // android.supportv1.v4.os.ResultReceiver
        public void onReceiveResult(int v, Bundle bundle0) {
            MediaSessionCompat.ensureClassLoader(bundle0);
            if(v == 0 && bundle0 != null && bundle0.containsKey("search_results")) {
                Parcelable[] arr_parcelable = bundle0.getParcelableArray("search_results");
                if(arr_parcelable != null) {
                    ArrayList arrayList0 = new ArrayList();
                    for(int v1 = 0; v1 < arr_parcelable.length; ++v1) {
                        arrayList0.add(((MediaItem)arr_parcelable[v1]));
                    }
                }
            }
        }
    }

    static class ServiceBinderWrapper {
        private Messenger mMessenger;
        private Bundle mRootHints;

        public ServiceBinderWrapper(IBinder iBinder0, Bundle bundle0) {
            this.mMessenger = new Messenger(iBinder0);
            this.mRootHints = bundle0;
        }

        public void addSubscription(String s, IBinder iBinder0, Bundle bundle0, Messenger messenger0) throws RemoteException {
            Bundle bundle1 = new Bundle();
            bundle1.putString("data_media_item_id", s);
            BundleCompat.putBinder(bundle1, "data_callback_token", iBinder0);
            bundle1.putBundle("data_options", bundle0);
            this.sendRequest(3, bundle1, messenger0);
        }

        public void connect(Context context0, Messenger messenger0) throws RemoteException {
            Bundle bundle0 = new Bundle();
            bundle0.putString("data_package_name", "com.pdf.editor.viewer.pdfreader.pdfviewer");
            bundle0.putBundle("data_root_hints", this.mRootHints);
            this.sendRequest(1, bundle0, messenger0);
        }

        public void disconnect(Messenger messenger0) throws RemoteException {
            this.sendRequest(2, null, messenger0);
        }

        public void getMediaItem(String s, ResultReceiver resultReceiver0, Messenger messenger0) throws RemoteException {
            Bundle bundle0 = new Bundle();
            bundle0.putString("data_media_item_id", s);
            bundle0.putParcelable("data_result_receiver", resultReceiver0);
            this.sendRequest(5, bundle0, messenger0);
        }

        public void registerCallbackMessenger(Context context0, Messenger messenger0) throws RemoteException {
            Bundle bundle0 = new Bundle();
            bundle0.putString("data_package_name", "com.pdf.editor.viewer.pdfreader.pdfviewer");
            bundle0.putBundle("data_root_hints", this.mRootHints);
            this.sendRequest(6, bundle0, messenger0);
        }

        public void removeSubscription(String s, IBinder iBinder0, Messenger messenger0) throws RemoteException {
            Bundle bundle0 = new Bundle();
            bundle0.putString("data_media_item_id", s);
            BundleCompat.putBinder(bundle0, "data_callback_token", iBinder0);
            this.sendRequest(4, bundle0, messenger0);
        }

        public void search(String s, Bundle bundle0, ResultReceiver resultReceiver0, Messenger messenger0) throws RemoteException {
            Bundle bundle1 = new Bundle();
            bundle1.putString("data_search_query", s);
            bundle1.putBundle("data_search_extras", bundle0);
            bundle1.putParcelable("data_result_receiver", resultReceiver0);
            this.sendRequest(8, bundle1, messenger0);
        }

        public void sendCustomAction(String s, Bundle bundle0, ResultReceiver resultReceiver0, Messenger messenger0) throws RemoteException {
            Bundle bundle1 = new Bundle();
            bundle1.putString("data_custom_action", s);
            bundle1.putBundle("data_custom_action_extras", bundle0);
            bundle1.putParcelable("data_result_receiver", resultReceiver0);
            this.sendRequest(9, bundle1, messenger0);
        }

        private void sendRequest(int v, Bundle bundle0, Messenger messenger0) throws RemoteException {
            Message message0 = Message.obtain();
            message0.what = v;
            message0.arg1 = 1;
            message0.setData(bundle0);
            message0.replyTo = messenger0;
            this.mMessenger.send(message0);
        }

        public void unregisterCallbackMessenger(Messenger messenger0) throws RemoteException {
            this.sendRequest(7, null, messenger0);
        }
    }

    static class Subscription {
        private final List mCallbacks;
        private final List mOptionsList;

        public Subscription() {
            this.mCallbacks = new ArrayList();
            this.mOptionsList = new ArrayList();
        }

        public SubscriptionCallback getCallback(Bundle bundle0) {
            for(int v = 0; v < this.mOptionsList.size(); ++v) {
                if(MediaBrowserCompatUtils.areSameOptions(((Bundle)this.mOptionsList.get(v)), bundle0)) {
                    return (SubscriptionCallback)this.mCallbacks.get(v);
                }
            }
            return null;
        }

        public List getCallbacks() {
            return this.mCallbacks;
        }

        public List getOptionsList() {
            return this.mOptionsList;
        }

        public boolean isEmpty() {
            return this.mCallbacks.isEmpty();
        }

        public void putCallback(Bundle bundle0, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
            for(int v = 0; v < this.mOptionsList.size(); ++v) {
                if(MediaBrowserCompatUtils.areSameOptions(((Bundle)this.mOptionsList.get(v)), bundle0)) {
                    this.mCallbacks.set(v, mediaBrowserCompat$SubscriptionCallback0);
                    return;
                }
            }
            this.mCallbacks.add(mediaBrowserCompat$SubscriptionCallback0);
            this.mOptionsList.add(bundle0);
        }
    }

    public static abstract class SubscriptionCallback {
        final Object mSubscriptionCallbackObj;
        WeakReference mSubscriptionRef;
        final IBinder mToken;

        public SubscriptionCallback() {
            this.mToken = new Binder();
            this.mSubscriptionCallbackObj = Build.VERSION.SDK_INT < 26 ? MediaBrowserCompatApi21.createSubscriptionCallback(new MediaBrowserCompat.SubscriptionCallback.StubApi21(this)) : MediaBrowserCompatApi26.createSubscriptionCallback(new MediaBrowserCompat.SubscriptionCallback.StubApi26(this));
        }

        public void onChildrenLoaded(String s, List list0) {
        }

        public void onChildrenLoaded(String s, List list0, Bundle bundle0) {
        }

        public void onError(String s) {
        }

        public void onError(String s, Bundle bundle0) {
        }

        public void setSubscription(Subscription mediaBrowserCompat$Subscription0) {
            this.mSubscriptionRef = new WeakReference(mediaBrowserCompat$Subscription0);
        }
    }

    public static final String CUSTOM_ACTION_DOWNLOAD = "android.supportv1.v4.media.action.DOWNLOAD";
    public static final String CUSTOM_ACTION_REMOVE_DOWNLOADED_FILE = "android.supportv1.v4.media.action.REMOVE_DOWNLOADED_FILE";
    static final boolean DEBUG = false;
    public static final String EXTRA_DOWNLOAD_PROGRESS = "android.media.browse.extra.DOWNLOAD_PROGRESS";
    public static final String EXTRA_MEDIA_ID = "android.media.browse.extra.MEDIA_ID";
    public static final String EXTRA_PAGE = "android.media.browse.extra.PAGE";
    public static final String EXTRA_PAGE_SIZE = "android.media.browse.extra.PAGE_SIZE";
    static final String TAG = "MediaBrowserCompat";
    private final MediaBrowserImpl mImpl;

    static {
        MediaBrowserCompat.DEBUG = Log.isLoggable("MediaBrowserCompat", 3);
    }

    public MediaBrowserCompat(Context context0, ComponentName componentName0, ConnectionCallback mediaBrowserCompat$ConnectionCallback0, Bundle bundle0) {
        MediaBrowserImplApi21 mediaBrowserCompat$MediaBrowserImplApi210;
        int v = Build.VERSION.SDK_INT;
        if(v >= 26) {
            mediaBrowserCompat$MediaBrowserImplApi210 = new MediaBrowserImplApi26(context0, componentName0, mediaBrowserCompat$ConnectionCallback0, bundle0);
        }
        else if(v >= 23) {
            mediaBrowserCompat$MediaBrowserImplApi210 = new MediaBrowserImplApi23(context0, componentName0, mediaBrowserCompat$ConnectionCallback0, bundle0);
        }
        else {
            mediaBrowserCompat$MediaBrowserImplApi210 = new MediaBrowserImplApi21(context0, componentName0, mediaBrowserCompat$ConnectionCallback0, bundle0);
        }
        this.mImpl = mediaBrowserCompat$MediaBrowserImplApi210;
    }

    public void connect() {
        this.mImpl.connect();
    }

    public void disconnect() {
        this.mImpl.disconnect();
    }

    public Bundle getExtras() {
        return this.mImpl.getExtras();
    }

    public void getItem(String s, ItemCallback mediaBrowserCompat$ItemCallback0) {
        this.mImpl.getItem(s, mediaBrowserCompat$ItemCallback0);
    }

    public Bundle getNotifyChildrenChangedOptions() {
        return this.mImpl.getNotifyChildrenChangedOptions();
    }

    public String getRoot() {
        return this.mImpl.getRoot();
    }

    public ComponentName getServiceComponent() {
        return this.mImpl.getServiceComponent();
    }

    public Token getSessionToken() {
        return this.mImpl.getSessionToken();
    }

    public boolean isConnected() {
        return this.mImpl.isConnected();
    }

    public void search(String s, Bundle bundle0, SearchCallback mediaBrowserCompat$SearchCallback0) {
        if(TextUtils.isEmpty(s)) {
            throw new IllegalArgumentException("query cannot be empty");
        }
        if(mediaBrowserCompat$SearchCallback0 == null) {
            throw new IllegalArgumentException("callback cannot be null");
        }
        this.mImpl.search(s, bundle0, mediaBrowserCompat$SearchCallback0);
    }

    public void sendCustomAction(String s, Bundle bundle0, CustomActionCallback mediaBrowserCompat$CustomActionCallback0) {
        if(TextUtils.isEmpty(s)) {
            throw new IllegalArgumentException("action cannot be empty");
        }
        this.mImpl.sendCustomAction(s, bundle0, mediaBrowserCompat$CustomActionCallback0);
    }

    public void subscribe(String s, Bundle bundle0, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
        if(TextUtils.isEmpty(s)) {
            throw new IllegalArgumentException("parentId is empty");
        }
        if(mediaBrowserCompat$SubscriptionCallback0 == null) {
            throw new IllegalArgumentException("callback is null");
        }
        if(bundle0 == null) {
            throw new IllegalArgumentException("options are null");
        }
        this.mImpl.subscribe(s, bundle0, mediaBrowserCompat$SubscriptionCallback0);
    }

    public void subscribe(String s, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
        if(TextUtils.isEmpty(s)) {
            throw new IllegalArgumentException("parentId is empty");
        }
        if(mediaBrowserCompat$SubscriptionCallback0 == null) {
            throw new IllegalArgumentException("callback is null");
        }
        this.mImpl.subscribe(s, null, mediaBrowserCompat$SubscriptionCallback0);
    }

    public void unsubscribe(String s) {
        if(TextUtils.isEmpty(s)) {
            throw new IllegalArgumentException("parentId is empty");
        }
        this.mImpl.unsubscribe(s, null);
    }

    public void unsubscribe(String s, SubscriptionCallback mediaBrowserCompat$SubscriptionCallback0) {
        if(TextUtils.isEmpty(s)) {
            throw new IllegalArgumentException("parentId is empty");
        }
        if(mediaBrowserCompat$SubscriptionCallback0 == null) {
            throw new IllegalArgumentException("callback is null");
        }
        this.mImpl.unsubscribe(s, mediaBrowserCompat$SubscriptionCallback0);
    }
}


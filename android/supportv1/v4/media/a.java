package android.supportv1.v4.media;

import android.media.session.MediaSessionManager.RemoteUserInfo;
import android.text.PrecomputedText.Params.Builder;
import android.text.PrecomputedText;
import android.text.Spannable;
import android.text.TextPaint;
import android.view.DisplayCutout;

public abstract class a {
    public static MediaSessionManager.RemoteUserInfo g(String s, int v, int v1) {
        return new MediaSessionManager.RemoteUserInfo(s, v, v1);
    }

    public static PrecomputedText.Params.Builder j(TextPaint textPaint0) {
        return new PrecomputedText.Params.Builder(textPaint0);
    }

    public static PrecomputedText l(Spannable spannable0) [...] // Inlined contents

    public static DisplayCutout p(Object object0) [...] // Inlined contents

    public static boolean x(Spannable spannable0) {
        return spannable0 instanceof PrecomputedText;
    }
}


package android.supportv1.v4.media;

import android.content.ComponentName;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.Messenger;
import android.os.RemoteException;
import java.util.Objects;

class MediaBrowserCompat.MediaBrowserImplBase.MediaServiceConnection implements ServiceConnection {
    public MediaBrowserCompat.MediaBrowserImplBase.MediaServiceConnection(MediaBrowserImplBase mediaBrowserCompat$MediaBrowserImplBase0) {
        MediaBrowserImplBase.this = mediaBrowserCompat$MediaBrowserImplBase0;
        super();
    }

    public boolean isCurrent(String s) {
        MediaBrowserImplBase mediaBrowserCompat$MediaBrowserImplBase0 = MediaBrowserImplBase.this;
        if(mediaBrowserCompat$MediaBrowserImplBase0.mServiceConnection == this && (mediaBrowserCompat$MediaBrowserImplBase0.mState != 0 && mediaBrowserCompat$MediaBrowserImplBase0.mState != 1)) {
            return true;
        }
        if(mediaBrowserCompat$MediaBrowserImplBase0.mState != 0 && mediaBrowserCompat$MediaBrowserImplBase0.mState != 1) {
            Objects.toString(mediaBrowserCompat$MediaBrowserImplBase0.mServiceComponent);
            Objects.toString(MediaBrowserImplBase.this.mServiceConnection);
        }
        return false;
    }

    @Override  // android.content.ServiceConnection
    public void onServiceConnected(ComponentName componentName0, IBinder iBinder0) {
        this.postOrRun(new Runnable() {
            @Override
            public void run() {
                boolean z = MediaBrowserCompat.DEBUG;
                if(z) {
                    Objects.toString(componentName0);
                    Objects.toString(iBinder0);
                    MediaBrowserImplBase.this.dump();
                }
                if(!MediaBrowserCompat.MediaBrowserImplBase.MediaServiceConnection.this.isCurrent("onServiceConnected")) {
                    return;
                }
                MediaBrowserImplBase.this.mServiceBinderWrapper = new ServiceBinderWrapper(iBinder0, MediaBrowserImplBase.this.mRootHints);
                MediaBrowserImplBase.this.mCallbacksMessenger = new Messenger(MediaBrowserImplBase.this.mHandler);
                MediaBrowserImplBase.this.mHandler.setCallbacksMessenger(MediaBrowserImplBase.this.mCallbacksMessenger);
                MediaBrowserImplBase mediaBrowserCompat$MediaBrowserImplBase0 = MediaBrowserImplBase.this;
                mediaBrowserCompat$MediaBrowserImplBase0.mState = 2;
                try {
                    if(z) {
                        mediaBrowserCompat$MediaBrowserImplBase0.dump();
                    }
                    MediaBrowserImplBase.this.mServiceBinderWrapper.connect(MediaBrowserImplBase.this.mContext, MediaBrowserImplBase.this.mCallbacksMessenger);
                }
                catch(RemoteException unused_ex) {
                    Objects.toString(MediaBrowserImplBase.this.mServiceComponent);
                    if(MediaBrowserCompat.DEBUG) {
                        MediaBrowserImplBase.this.dump();
                    }
                }
            }
        });
    }

    @Override  // android.content.ServiceConnection
    public void onServiceDisconnected(ComponentName componentName0) {
        this.postOrRun(new Runnable() {
            @Override
            public void run() {
                if(MediaBrowserCompat.DEBUG) {
                    Objects.toString(componentName0);
                    Objects.toString(MediaBrowserImplBase.this.mServiceConnection);
                    MediaBrowserImplBase.this.dump();
                }
                if(!MediaBrowserCompat.MediaBrowserImplBase.MediaServiceConnection.this.isCurrent("onServiceDisconnected")) {
                    return;
                }
                MediaBrowserImplBase.this.mServiceBinderWrapper = null;
                MediaBrowserImplBase.this.mCallbacksMessenger = null;
                MediaBrowserImplBase.this.mHandler.setCallbacksMessenger(null);
                MediaBrowserImplBase.this.mState = 4;
                MediaBrowserImplBase.this.mCallback.onConnectionSuspended();
            }
        });
    }

    private void postOrRun(Runnable runnable0) {
        if(Thread.currentThread() == MediaBrowserImplBase.this.mHandler.getLooper().getThread()) {
            runnable0.run();
            return;
        }
        MediaBrowserImplBase.this.mHandler.post(runnable0);
    }
}


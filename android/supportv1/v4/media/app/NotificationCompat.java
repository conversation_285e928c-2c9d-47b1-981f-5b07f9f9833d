package android.supportv1.v4.media.app;

import android.app.Notification.MediaStyle;
import android.app.Notification;
import android.app.PendingIntent;
import android.media.session.MediaSession.Token;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.Parcelable;
import android.supportv1.v4.app.NotificationBuilderWithBuilderAccessor;
import android.supportv1.v4.app.NotificationCompat.Action;
import android.supportv1.v4.app.NotificationCompat.Builder;
import android.supportv1.v4.app.NotificationCompat.Style;
import android.supportv1.v4.app.d;
import android.supportv1.v4.media.session.MediaSessionCompat.Token;
import android.widget.RemoteViews;

public class NotificationCompat {
    public static class DecoratedMediaCustomViewStyle extends MediaStyle {
        @Override  // android.supportv1.v4.media.app.NotificationCompat$MediaStyle
        public void apply(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            if(Build.VERSION.SDK_INT >= 24) {
                notificationBuilderWithBuilderAccessor0.getBuilder().setStyle(this.fillInMediaStyle(d.d()));
                return;
            }
            super.apply(notificationBuilderWithBuilderAccessor0);
        }

        @Override  // android.supportv1.v4.media.app.NotificationCompat$MediaStyle
        public int getBigContentViewLayoutResource(int v) {
            return v > 3 ? 0x7F0C0040 : 0x7F0C0042;
        }

        @Override  // android.supportv1.v4.media.app.NotificationCompat$MediaStyle
        public int getContentViewLayoutResource() {
            return this.mBuilder.getContentView() == null ? super.getContentViewLayoutResource() : 0x7F0C0047;
        }

        @Override  // android.supportv1.v4.media.app.NotificationCompat$MediaStyle
        public RemoteViews makeBigContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            if(Build.VERSION.SDK_INT >= 24) {
                return null;
            }
            RemoteViews remoteViews0 = this.mBuilder.getBigContentView() == null ? this.mBuilder.getContentView() : this.mBuilder.getBigContentView();
            if(remoteViews0 == null) {
                return null;
            }
            RemoteViews remoteViews1 = this.generateBigContentView();
            this.buildIntoRemoteViews(remoteViews1, remoteViews0);
            this.setBackgroundColor(remoteViews1);
            return remoteViews1;
        }

        @Override  // android.supportv1.v4.media.app.NotificationCompat$MediaStyle
        public RemoteViews makeContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            if(Build.VERSION.SDK_INT >= 24) {
                return null;
            }
            boolean z = this.mBuilder.getContentView() != null;
            if(!z && this.mBuilder.getBigContentView() == null) {
                return null;
            }
            RemoteViews remoteViews0 = this.generateContentView();
            if(z) {
                this.buildIntoRemoteViews(remoteViews0, this.mBuilder.getContentView());
            }
            this.setBackgroundColor(remoteViews0);
            return remoteViews0;
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public RemoteViews makeHeadsUpContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            if(Build.VERSION.SDK_INT >= 24) {
                return null;
            }
            RemoteViews remoteViews0 = this.mBuilder.getHeadsUpContentView() == null ? this.mBuilder.getContentView() : this.mBuilder.getHeadsUpContentView();
            if(remoteViews0 == null) {
                return null;
            }
            RemoteViews remoteViews1 = this.generateBigContentView();
            this.buildIntoRemoteViews(remoteViews1, remoteViews0);
            this.setBackgroundColor(remoteViews1);
            return remoteViews1;
        }

        private void setBackgroundColor(RemoteViews remoteViews0) {
            remoteViews0.setInt(0x7F090271, "setBackgroundColor", (this.mBuilder.getColor() == 0 ? this.mBuilder.mContext.getResources().getColor(0x7F060080) : this.mBuilder.getColor()));  // color:color_bgr_main
        }
    }

    public static class MediaStyle extends Style {
        private static final int MAX_MEDIA_BUTTONS = 5;
        private static final int MAX_MEDIA_BUTTONS_IN_COMPACT = 3;
        int[] mActionsToShowInCompact;
        PendingIntent mCancelButtonIntent;
        boolean mShowCancelButton;
        Token mToken;

        public MediaStyle() {
            this.mActionsToShowInCompact = null;
        }

        public MediaStyle(Builder notificationCompat$Builder0) {
            this.mActionsToShowInCompact = null;
            this.setBuilder(notificationCompat$Builder0);
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public void apply(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            notificationBuilderWithBuilderAccessor0.getBuilder().setStyle(this.fillInMediaStyle(new Notification.MediaStyle()));
        }

        public Notification.MediaStyle fillInMediaStyle(Notification.MediaStyle notification$MediaStyle0) {
            int[] arr_v = this.mActionsToShowInCompact;
            if(arr_v != null) {
                notification$MediaStyle0.setShowActionsInCompactView(arr_v);
            }
            Token mediaSessionCompat$Token0 = this.mToken;
            if(mediaSessionCompat$Token0 != null) {
                notification$MediaStyle0.setMediaSession(((MediaSession.Token)mediaSessionCompat$Token0.getToken()));
            }
            return notification$MediaStyle0;
        }

        public RemoteViews generateBigContentView() {
            int v = Math.min(this.mBuilder.mActions.size(), 5);
            RemoteViews remoteViews0 = this.applyStandardTemplate(false, this.getBigContentViewLayoutResource(v), false);
            remoteViews0.removeAllViews(0x7F090169);
            if(v > 0) {
                for(int v1 = 0; v1 < v; ++v1) {
                    remoteViews0.addView(0x7F090169, this.generateMediaActionButton(((Action)this.mBuilder.mActions.get(v1))));
                }
            }
            if(this.mShowCancelButton) {
                remoteViews0.setViewVisibility(0x7F090073, 0);
                remoteViews0.setInt(0x7F090073, "setAlpha", this.mBuilder.mContext.getResources().getInteger(0x7F0A0008));  // id:ChasingDots
                remoteViews0.setOnClickPendingIntent(0x7F090073, this.mCancelButtonIntent);
                return remoteViews0;
            }
            remoteViews0.setViewVisibility(0x7F090073, 8);
            return remoteViews0;
        }

        public RemoteViews generateContentView() {
            RemoteViews remoteViews0 = this.applyStandardTemplate(false, this.getContentViewLayoutResource(), true);
            int v = this.mBuilder.mActions.size();
            int v1 = this.mActionsToShowInCompact == null ? 0 : Math.min(this.mActionsToShowInCompact.length, 3);
            remoteViews0.removeAllViews(0x7F090169);
            if(v1 > 0) {
                for(int v2 = 0; v2 < v1; ++v2) {
                    if(v2 >= v) {
                        throw new IllegalArgumentException(String.format("setShowActionsInCompactView: action %d out of bounds (max %d)", v2, ((int)(v - 1))));
                    }
                    remoteViews0.addView(0x7F090169, this.generateMediaActionButton(((Action)this.mBuilder.mActions.get(this.mActionsToShowInCompact[v2]))));
                }
            }
            if(this.mShowCancelButton) {
                remoteViews0.setViewVisibility(0x7F0900D9, 8);
                remoteViews0.setViewVisibility(0x7F090073, 0);
                remoteViews0.setOnClickPendingIntent(0x7F090073, this.mCancelButtonIntent);
                remoteViews0.setInt(0x7F090073, "setAlpha", this.mBuilder.mContext.getResources().getInteger(0x7F0A0008));  // id:ChasingDots
                return remoteViews0;
            }
            remoteViews0.setViewVisibility(0x7F0900D9, 0);
            remoteViews0.setViewVisibility(0x7F090073, 8);
            return remoteViews0;
        }

        private RemoteViews generateMediaActionButton(Action notificationCompat$Action0) {
            RemoteViews remoteViews0 = new RemoteViews("com.pdf.editor.viewer.pdfreader.pdfviewer", 0x7F0C003D);
            remoteViews0.setImageViewResource(0x7F090008, notificationCompat$Action0.getIcon());  // font:gilroy_regular
            if(notificationCompat$Action0.getActionIntent() != null) {
                remoteViews0.setOnClickPendingIntent(0x7F090008, notificationCompat$Action0.getActionIntent());  // font:gilroy_regular
            }
            remoteViews0.setContentDescription(0x7F090008, notificationCompat$Action0.getTitle());  // font:gilroy_regular
            return remoteViews0;
        }

        public int getBigContentViewLayoutResource(int v) {
            return v > 3 ? 0x7F0C003F : 0x7F0C0041;
        }

        public int getContentViewLayoutResource() {
            return 0x7F0C0046;
        }

        public static Token getMediaSession(Notification notification0) {
            Bundle bundle0 = android.supportv1.v4.app.NotificationCompat.getExtras(notification0);
            if(bundle0 != null) {
                Parcelable parcelable0 = bundle0.getParcelable("android.mediaSession");
                return parcelable0 == null ? null : Token.fromToken(parcelable0);
            }
            return null;
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public RemoteViews makeBigContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            return null;
        }

        @Override  // android.supportv1.v4.app.NotificationCompat$Style
        public RemoteViews makeContentView(NotificationBuilderWithBuilderAccessor notificationBuilderWithBuilderAccessor0) {
            return null;
        }

        public MediaStyle setCancelButtonIntent(PendingIntent pendingIntent0) {
            this.mCancelButtonIntent = pendingIntent0;
            return this;
        }

        public MediaStyle setMediaSession(Token mediaSessionCompat$Token0) {
            this.mToken = mediaSessionCompat$Token0;
            return this;
        }

        public MediaStyle setShowActionsInCompactView(int[] arr_v) {
            this.mActionsToShowInCompact = arr_v;
            return this;
        }

        public MediaStyle setShowCancelButton(boolean z) {
            return this;
        }
    }

}


package android.supportv1.v4.os;

import android.content.Context;
import android.os.LocaleList;
import android.text.Html;
import android.text.Spanned;
import android.view.PointerIcon;
import java.util.Locale;

public abstract class a {
    public static LocaleList B(Locale[] arr_locale) {
        return new LocaleList(arr_locale);
    }

    public static LocaleList d(Locale[] arr_locale) {
        return new LocaleList(arr_locale);
    }

    public static Spanned e(int v, String s) {
        return Html.fromHtml(s, v);
    }

    public static PointerIcon g(int v, Context context0) {
        return PointerIcon.getSystemIcon(context0, v);
    }

    public static PointerIcon j(Object object0) [...] // Inlined contents

    public static void q() {
    }
}


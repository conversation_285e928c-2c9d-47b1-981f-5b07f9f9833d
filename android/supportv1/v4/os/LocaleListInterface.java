package android.supportv1.v4.os;

import java.util.Locale;

interface LocaleListInterface {
    @Override
    boolean equals(Object arg1);

    Locale get(int arg1);

    Locale getFirstMatch(String[] arg1);

    Object getLocaleList();

    @Override
    int hashCode();

    int indexOf(Locale arg1);

    boolean isEmpty();

    void setLocaleList(Locale[] arg1);

    int size();

    String toLanguageTags();

    @Override
    String toString();
}


package android.supportv1.v4.os;

import android.os.Build.VERSION;
import android.os.LocaleList;
import android.supportv1.v4.app.d;
import java.util.Locale;

public final class LocaleListCompat {
    static class LocaleListCompatApi24Impl implements LocaleListInterface {
        private LocaleList mLocaleList;

        public LocaleListCompatApi24Impl() {
            this.mLocaleList = a.B(new Locale[0]);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public boolean equals(Object object0) {
            LocaleList localeList0 = this.mLocaleList;
            return d.y(((LocaleListCompat)object0).unwrap(), localeList0);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public Locale get(int v) {
            return this.mLocaleList.get(v);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public Locale getFirstMatch(String[] arr_s) {
            return this.mLocaleList == null ? null : this.mLocaleList.getFirstMatch(arr_s);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public Object getLocaleList() {
            return this.mLocaleList;
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public int hashCode() {
            return this.mLocaleList.hashCode();
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public int indexOf(Locale locale0) {
            return this.mLocaleList.indexOf(locale0);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public boolean isEmpty() {
            return this.mLocaleList.isEmpty();
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public void setLocaleList(Locale[] arr_locale) {
            this.mLocaleList = a.d(arr_locale);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public int size() {
            return this.mLocaleList.size();
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public String toLanguageTags() {
            return this.mLocaleList.toLanguageTags();
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public String toString() {
            return this.mLocaleList.toString();
        }
    }

    static class LocaleListCompatBaseImpl implements LocaleListInterface {
        private LocaleListHelper mLocaleList;

        public LocaleListCompatBaseImpl() {
            this.mLocaleList = new LocaleListHelper(new Locale[0]);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public boolean equals(Object object0) {
            return this.mLocaleList.equals(((LocaleListCompat)object0).unwrap());
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public Locale get(int v) {
            return this.mLocaleList.get(v);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public Locale getFirstMatch(String[] arr_s) {
            return this.mLocaleList == null ? null : this.mLocaleList.getFirstMatch(arr_s);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public Object getLocaleList() {
            return this.mLocaleList;
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public int hashCode() {
            return this.mLocaleList.hashCode();
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public int indexOf(Locale locale0) {
            return this.mLocaleList.indexOf(locale0);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public boolean isEmpty() {
            return this.mLocaleList.isEmpty();
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public void setLocaleList(Locale[] arr_locale) {
            this.mLocaleList = new LocaleListHelper(arr_locale);
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public int size() {
            return this.mLocaleList.size();
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public String toLanguageTags() {
            return this.mLocaleList.toLanguageTags();
        }

        @Override  // android.supportv1.v4.os.LocaleListInterface
        public String toString() {
            return this.mLocaleList.toString();
        }
    }

    static final LocaleListInterface IMPL;
    private static final LocaleListCompat sEmptyLocaleList;

    static {
        LocaleListCompat.sEmptyLocaleList = new LocaleListCompat();
        LocaleListCompatApi24Impl localeListCompat$LocaleListCompatApi24Impl0 = Build.VERSION.SDK_INT >= 24 ? new LocaleListCompatApi24Impl() : new LocaleListCompatBaseImpl();
        LocaleListCompat.IMPL = localeListCompat$LocaleListCompatApi24Impl0;
    }

    public static LocaleListCompat create(Locale[] arr_locale) {
        LocaleListCompat localeListCompat0 = new LocaleListCompat();
        localeListCompat0.setLocaleListArray(arr_locale);
        return localeListCompat0;
    }

    @Override
    public boolean equals(Object object0) {
        return LocaleListCompat.IMPL.equals(object0);
    }

    public static LocaleListCompat forLanguageTags(String s) {
        if(s != null && !s.isEmpty()) {
            String[] arr_s = s.split(",", -1);
            Locale[] arr_locale = new Locale[arr_s.length];
            for(int v = 0; v < arr_s.length; ++v) {
                arr_locale[v] = Locale.forLanguageTag(arr_s[v]);
            }
            LocaleListCompat localeListCompat0 = new LocaleListCompat();
            localeListCompat0.setLocaleListArray(arr_locale);
            return localeListCompat0;
        }
        return LocaleListCompat.getEmptyLocaleList();
    }

    public Locale get(int v) {
        return LocaleListCompat.IMPL.get(v);
    }

    public static LocaleListCompat getAdjustedDefault() {
        return Build.VERSION.SDK_INT < 24 ? LocaleListCompat.create(new Locale[]{Locale.getDefault()}) : LocaleListCompat.wrap(LocaleList.getAdjustedDefault());
    }

    public static LocaleListCompat getDefault() {
        return Build.VERSION.SDK_INT < 24 ? LocaleListCompat.create(new Locale[]{Locale.getDefault()}) : LocaleListCompat.wrap(LocaleList.getDefault());
    }

    public static LocaleListCompat getEmptyLocaleList() {
        return LocaleListCompat.sEmptyLocaleList;
    }

    public Locale getFirstMatch(String[] arr_s) {
        return LocaleListCompat.IMPL.getFirstMatch(arr_s);
    }

    @Override
    public int hashCode() {
        return LocaleListCompat.IMPL.hashCode();
    }

    public int indexOf(Locale locale0) {
        return LocaleListCompat.IMPL.indexOf(locale0);
    }

    public boolean isEmpty() {
        return LocaleListCompat.IMPL.isEmpty();
    }

    private void setLocaleList(LocaleList localeList0) {
        int v = localeList0.size();
        if(v > 0) {
            Locale[] arr_locale = new Locale[v];
            for(int v1 = 0; v1 < v; ++v1) {
                arr_locale[v1] = localeList0.get(v1);
            }
            LocaleListCompat.IMPL.setLocaleList(arr_locale);
        }
    }

    private void setLocaleListArray(Locale[] arr_locale) {
        LocaleListCompat.IMPL.setLocaleList(arr_locale);
    }

    public int size() {
        return LocaleListCompat.IMPL.size();
    }

    public String toLanguageTags() {
        return LocaleListCompat.IMPL.toLanguageTags();
    }

    @Override
    public String toString() {
        return LocaleListCompat.IMPL.toString();
    }

    public Object unwrap() {
        return LocaleListCompat.IMPL.getLocaleList();
    }

    public static LocaleListCompat wrap(Object object0) {
        LocaleListCompat localeListCompat0 = new LocaleListCompat();
        if(d.x(object0)) {
            localeListCompat0.setLocaleList(((LocaleList)object0));
        }
        return localeListCompat0;
    }
}


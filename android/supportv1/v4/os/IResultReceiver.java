package android.supportv1.v4.os;

import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

public interface IResultReceiver extends IInterface {
    public static abstract class Stub extends Binder implements IResultReceiver {
        static class Proxy implements IResultReceiver {
            private IBinder mRemote;

            public Proxy(IBinder iBinder0) {
                this.mRemote = iBinder0;
            }

            @Override  // android.os.IInterface
            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "android.supportv1.v4.os.IResultReceiver";
            }

            @Override  // android.supportv1.v4.os.IResultReceiver
            public void send(int v, Bundle bundle0) throws RemoteException {
                Parcel parcel0 = Parcel.obtain();
                try {
                    parcel0.writeInterfaceToken("android.supportv1.v4.os.IResultReceiver");
                    parcel0.writeInt(v);
                    if(bundle0 == null) {
                        parcel0.writeInt(0);
                    }
                    else {
                        parcel0.writeInt(1);
                        bundle0.writeToParcel(parcel0, 0);
                    }
                    this.mRemote.transact(1, parcel0, null, 1);
                }
                finally {
                    parcel0.recycle();
                }
            }
        }

        private static final String DESCRIPTOR = "android.supportv1.v4.os.IResultReceiver";
        static final int TRANSACTION_send = 1;

        public Stub() {
            this.attachInterface(this, "android.supportv1.v4.os.IResultReceiver");
        }

        @Override  // android.os.IInterface
        public IBinder asBinder() {
            return this;
        }

        public static IResultReceiver asInterface(IBinder iBinder0) {
            if(iBinder0 == null) {
                return null;
            }
            IInterface iInterface0 = iBinder0.queryLocalInterface("android.supportv1.v4.os.IResultReceiver");
            return iInterface0 != null && iInterface0 instanceof IResultReceiver ? ((IResultReceiver)iInterface0) : new Proxy(iBinder0);
        }

        @Override  // android.os.Binder
        public boolean onTransact(int v, Parcel parcel0, Parcel parcel1, int v1) throws RemoteException {
            switch(v) {
                case 1: {
                    parcel0.enforceInterface("android.supportv1.v4.os.IResultReceiver");
                    this.send(parcel0.readInt(), (parcel0.readInt() == 0 ? null : ((Bundle)Bundle.CREATOR.createFromParcel(parcel0))));
                    return true;
                }
                case 0x5F4E5446: {
                    parcel1.writeString("android.supportv1.v4.os.IResultReceiver");
                    return true;
                }
                default: {
                    return super.onTransact(v, parcel0, parcel1, v1);
                }
            }
        }
    }

    void send(int arg1, Bundle arg2) throws RemoteException;
}


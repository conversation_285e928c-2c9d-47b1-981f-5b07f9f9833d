package android.supportv1.v4.text;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Locale;

public final class ICUCompat {
    private static final String TAG = "ICUCompat";
    private static Method sAddLikelySubtagsMethod;
    private static Method sGetScriptMethod;

    static {
        try {
            ICUCompat.sAddLikelySubtagsMethod = Class.forName("libcore.icu.ICU").getMethod("addLikelySubtags", Locale.class);
        }
        catch(Exception exception0) {
            throw new IllegalStateException(exception0);
        }
    }

    private static String addLikelySubtags(Locale locale0) {
        String s = locale0.toString();
        try {
            return ICUCompat.sAddLikelySubtagsMethod == null ? s : ((String)ICUCompat.sAddLikelySubtagsMethod.invoke(null, s));
        }
        catch(IllegalAccessException | InvocationTargetException unused_ex) {
        }
        return s;
    }

    private static String getScript(String s) {
        try {
            return ICUCompat.sGetScriptMethod == null ? null : ((String)ICUCompat.sGetScriptMethod.invoke(null, s));
        }
        catch(IllegalAccessException | InvocationTargetException unused_ex) {
        }
        return null;
    }

    public static String maximizeAndGetScript(Locale locale0) {
        try {
            return ((Locale)ICUCompat.sAddLikelySubtagsMethod.invoke(null, locale0)).getScript();
        }
        catch(InvocationTargetException | IllegalAccessException unused_ex) {
            return locale0.getScript();
        }
    }
}


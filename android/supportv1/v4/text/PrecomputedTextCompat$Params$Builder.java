package android.supportv1.v4.text;

import android.os.Build.VERSION;
import android.text.TextDirectionHeuristic;
import android.text.TextDirectionHeuristics;
import android.text.TextPaint;

public class PrecomputedTextCompat.Params.Builder {
    private int mBreakStrategy;
    private int mHyphenationFrequency;
    private final TextPaint mPaint;
    private TextDirectionHeuristic mTextDir;

    public PrecomputedTextCompat.Params.Builder(TextPaint textPaint0) {
        this.mPaint = textPaint0;
        if(Build.VERSION.SDK_INT >= 23) {
            this.mBreakStrategy = 1;
            this.mHyphenationFrequency = 1;
        }
        else {
            this.mHyphenationFrequency = 0;
            this.mBreakStrategy = 0;
        }
        this.mTextDir = TextDirectionHeuristics.FIRSTSTRONG_LTR;
    }

    public Params build() {
        return new Params(this.mPaint, this.mTextDir, this.mBreakStrategy, this.mHyphenationFrequency);
    }

    public PrecomputedTextCompat.Params.Builder setBreakStrategy(int v) {
        this.mBreakStrategy = v;
        return this;
    }

    public PrecomputedTextCompat.Params.Builder setHyphenationFrequency(int v) {
        this.mHyphenationFrequency = v;
        return this;
    }

    public PrecomputedTextCompat.Params.Builder setTextDirection(TextDirectionHeuristic textDirectionHeuristic0) {
        this.mTextDir = textDirectionHeuristic0;
        return this;
    }
}


package android.supportv1.v4.text.util;

import a.a;
import android.os.Build.VERSION;
import android.text.Spannable;
import android.text.method.LinkMovementMethod;
import android.text.method.MovementMethod;
import android.text.style.URLSpan;
import android.text.util.Linkify.MatchFilter;
import android.text.util.Linkify.TransformFilter;
import android.text.util.Linkify;
import android.webkit.WebView;
import android.widget.TextView;
import java.io.UnsupportedEncodingException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class LinkifyCompat {
    static class LinkSpec {
        int end;
        URLSpan frameworkAddedSpan;
        int start;
        String url;

    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface LinkifyMask {
    }

    private static final Comparator COMPARATOR;
    private static final String[] EMPTY_STRING;

    static {
        LinkifyCompat.EMPTY_STRING = new String[0];
        LinkifyCompat.COMPARATOR = new Comparator() {
            public int compare(LinkSpec linkifyCompat$LinkSpec0, LinkSpec linkifyCompat$LinkSpec1) {
                int v = linkifyCompat$LinkSpec0.start;
                int v1 = linkifyCompat$LinkSpec1.start;
                if(v < v1) {
                    return -1;
                }
                if(v > v1) {
                    return 1;
                }
                int v2 = linkifyCompat$LinkSpec0.end;
                int v3 = linkifyCompat$LinkSpec1.end;
                if(v2 < v3) {
                    return 1;
                }
                return v2 <= v3 ? 0 : -1;
            }

            @Override
            public int compare(Object object0, Object object1) {
                return this.compare(((LinkSpec)object0), ((LinkSpec)object1));
            }
        };
    }

    private static void addLinkMovementMethod(TextView textView0) {
        MovementMethod movementMethod0 = textView0.getMovementMethod();
        if((movementMethod0 == null || !(movementMethod0 instanceof LinkMovementMethod)) && textView0.getLinksClickable()) {
            textView0.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }

    public static void addLinks(TextView textView0, Pattern pattern0, String s) {
        Linkify.addLinks(textView0, pattern0, s);
    }

    public static void addLinks(TextView textView0, Pattern pattern0, String s, Linkify.MatchFilter linkify$MatchFilter0, Linkify.TransformFilter linkify$TransformFilter0) {
        Linkify.addLinks(textView0, pattern0, s, linkify$MatchFilter0, linkify$TransformFilter0);
    }

    // 去混淆评级： 低(20)
    public static void addLinks(TextView textView0, Pattern pattern0, String s, String[] arr_s, Linkify.MatchFilter linkify$MatchFilter0, Linkify.TransformFilter linkify$TransformFilter0) {
        Linkify.addLinks(textView0, pattern0, s, arr_s, linkify$MatchFilter0, linkify$TransformFilter0);
    }

    // 去混淆评级： 低(40)
    public static boolean addLinks(Spannable spannable0, int v) {
        return Linkify.addLinks(spannable0, v);
    }

    // 去混淆评级： 低(30)
    public static boolean addLinks(Spannable spannable0, Pattern pattern0, String s) {
        return Linkify.addLinks(spannable0, pattern0, s);
    }

    // 去混淆评级： 低(30)
    public static boolean addLinks(Spannable spannable0, Pattern pattern0, String s, Linkify.MatchFilter linkify$MatchFilter0, Linkify.TransformFilter linkify$TransformFilter0) {
        return Linkify.addLinks(spannable0, pattern0, s, linkify$MatchFilter0, linkify$TransformFilter0);
    }

    // 去混淆评级： 中等(70)
    public static boolean addLinks(Spannable spannable0, Pattern pattern0, String s, String[] arr_s, Linkify.MatchFilter linkify$MatchFilter0, Linkify.TransformFilter linkify$TransformFilter0) {
        return Linkify.addLinks(spannable0, pattern0, s, arr_s, linkify$MatchFilter0, linkify$TransformFilter0);
    }

    // 去混淆评级： 中等(60)
    public static boolean addLinks(TextView textView0, int v) {
        return Linkify.addLinks(textView0, v);
    }

    private static void applyLink(String s, int v, int v1, Spannable spannable0) {
        spannable0.setSpan(new URLSpan(s), v, v1, 33);
    }

    private static String findAddress(String s) {
        return Build.VERSION.SDK_INT < 28 ? FindAddress.findAddress(s) : WebView.findAddress(s);
    }

    private static void gatherLinks(ArrayList arrayList0, Spannable spannable0, Pattern pattern0, String[] arr_s, Linkify.MatchFilter linkify$MatchFilter0, Linkify.TransformFilter linkify$TransformFilter0) {
        Matcher matcher0 = pattern0.matcher(spannable0);
        while(matcher0.find()) {
            int v = matcher0.start();
            int v1 = matcher0.end();
            if(linkify$MatchFilter0 == null || linkify$MatchFilter0.acceptMatch(spannable0, v, v1)) {
                LinkSpec linkifyCompat$LinkSpec0 = new LinkSpec();
                linkifyCompat$LinkSpec0.url = LinkifyCompat.makeUrl(matcher0.group(0), arr_s, matcher0, linkify$TransformFilter0);
                linkifyCompat$LinkSpec0.start = v;
                linkifyCompat$LinkSpec0.end = v1;
                arrayList0.add(linkifyCompat$LinkSpec0);
            }
        }
    }

    private static void gatherMapLinks(ArrayList arrayList0, Spannable spannable0) {
        String s2;
        String s = spannable0.toString();
        int v = 0;
        try {
            String s1;
            while((s1 = LinkifyCompat.findAddress(s)) != null) {
                int v1 = s.indexOf(s1);
                if(v1 < 0) {
                    break;
                }
                LinkSpec linkifyCompat$LinkSpec0 = new LinkSpec();
                int v2 = s1.length() + v1;
                linkifyCompat$LinkSpec0.start = v1 + v;
                v += v2;
                linkifyCompat$LinkSpec0.end = v;
                s = s.substring(v2);
                try {
                    s2 = URLEncoder.encode(s1, "UTF-8");
                }
                catch(UnsupportedEncodingException unused_ex) {
                    continue;
                }
                linkifyCompat$LinkSpec0.url = "geo:0,0?q=" + s2;
                arrayList0.add(linkifyCompat$LinkSpec0);
            }
        }
        catch(UnsupportedOperationException unused_ex) {
        }
    }

    private static String makeUrl(String s, String[] arr_s, Matcher matcher0, Linkify.TransformFilter linkify$TransformFilter0) {
        boolean z;
        if(linkify$TransformFilter0 != null) {
            s = linkify$TransformFilter0.transformUrl(matcher0, s);
        }
        for(int v = 0; true; ++v) {
            z = false;
            if(v >= arr_s.length) {
                break;
            }
            String s1 = arr_s[v];
            if(s.regionMatches(true, 0, s1, 0, s1.length())) {
                String s2 = arr_s[v];
                z = true;
                if(s.regionMatches(false, 0, s2, 0, s2.length())) {
                    break;
                }
                return arr_s[v] + s.substring(arr_s[v].length());
            }
        }
        return z || arr_s.length <= 0 ? s : a.s(new StringBuilder(), arr_s[0], s);
    }

    private static void pruneOverlaps(ArrayList arrayList0, Spannable spannable0) {
        int v7;
        int v = 0;
        URLSpan[] arr_uRLSpan = (URLSpan[])spannable0.getSpans(0, spannable0.length(), URLSpan.class);
        for(int v1 = 0; v1 < arr_uRLSpan.length; ++v1) {
            LinkSpec linkifyCompat$LinkSpec0 = new LinkSpec();
            URLSpan uRLSpan0 = arr_uRLSpan[v1];
            linkifyCompat$LinkSpec0.frameworkAddedSpan = uRLSpan0;
            linkifyCompat$LinkSpec0.start = spannable0.getSpanStart(uRLSpan0);
            linkifyCompat$LinkSpec0.end = spannable0.getSpanEnd(arr_uRLSpan[v1]);
            arrayList0.add(linkifyCompat$LinkSpec0);
        }
        Collections.sort(arrayList0, LinkifyCompat.COMPARATOR);
        int v2 = arrayList0.size();
        while(v < v2 - 1) {
            LinkSpec linkifyCompat$LinkSpec1 = (LinkSpec)arrayList0.get(v);
            LinkSpec linkifyCompat$LinkSpec2 = (LinkSpec)arrayList0.get(v + 1);
            int v3 = linkifyCompat$LinkSpec1.start;
            int v4 = linkifyCompat$LinkSpec2.start;
            if(v3 <= v4) {
                int v5 = linkifyCompat$LinkSpec1.end;
                if(v5 > v4) {
                    int v6 = linkifyCompat$LinkSpec2.end;
                    if(v6 > v5 && v5 - v3 <= v6 - v4) {
                        v7 = v5 - v3 >= v6 - v4 ? -1 : v;
                    }
                    else {
                        v7 = v + 1;
                    }
                    if(v7 != -1) {
                        URLSpan uRLSpan1 = ((LinkSpec)arrayList0.get(v7)).frameworkAddedSpan;
                        if(uRLSpan1 != null) {
                            spannable0.removeSpan(uRLSpan1);
                        }
                        arrayList0.remove(v7);
                        --v2;
                        continue;
                    }
                }
            }
            ++v;
        }
    }

    private static boolean shouldAddLinksFallbackToFramework() [...] // 潜在的解密器
}


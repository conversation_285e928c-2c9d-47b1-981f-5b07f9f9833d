package android.supportv1.v4.text;

import java.util.concurrent.Callable;

class PrecomputedTextCompat.PrecomputedTextFutureTask.PrecomputedTextCallback implements Callable {
    private Params mParams;
    private CharSequence mText;

    public PrecomputedTextCompat.PrecomputedTextFutureTask.PrecomputedTextCallback(Params precomputedTextCompat$Params0, CharSequence charSequence0) {
        this.mParams = precomputedTextCompat$Params0;
        this.mText = charSequence0;
    }

    public PrecomputedTextCompat call() throws Exception {
        return PrecomputedTextCompat.create(this.mText, this.mParams);
    }

    @Override
    public Object call() throws Exception {
        return this.call();
    }
}


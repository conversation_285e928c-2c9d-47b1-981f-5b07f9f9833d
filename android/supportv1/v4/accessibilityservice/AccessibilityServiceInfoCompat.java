package android.supportv1.v4.accessibilityservice;

import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.pm.PackageManager;

public final class AccessibilityServiceInfoCompat {
    public static final int CAPABILITY_CAN_FILTER_KEY_EVENTS = 8;
    public static final int CAPABILITY_CAN_REQUEST_ENHANCED_WEB_ACCESSIBILITY = 4;
    public static final int CAPABILITY_CAN_REQUEST_TOUCH_EXPLORATION = 2;
    public static final int CAPABILITY_CAN_RETRIEVE_WINDOW_CONTENT = 1;
    public static final int FEEDBACK_ALL_MASK = -1;
    public static final int FEEDBACK_BRAILLE = 0x20;
    public static final int FLAG_INCLUDE_NOT_IMPORTANT_VIEWS = 2;
    public static final int FLAG_REPORT_VIEW_IDS = 16;
    public static final int FLAG_REQUEST_ENHANCED_WEB_ACCESSIBILITY = 8;
    public static final int FLAG_REQUEST_FILTER_KEY_EVENTS = 0x20;
    public static final int FLAG_REQUEST_TOUCH_EXPLORATION_MODE = 4;

    public static String capabilityToString(int v) {
        switch(v) {
            case 1: {
                return "CAPABILITY_CAN_RETRIEVE_WINDOW_CONTENT";
            }
            case 2: {
                return "CAPABILITY_CAN_REQUEST_TOUCH_EXPLORATION";
            }
            case 4: {
                return "CAPABILITY_CAN_REQUEST_ENHANCED_WEB_ACCESSIBILITY";
            }
            case 8: {
                return "CAPABILITY_CAN_FILTER_KEY_EVENTS";
            }
            default: {
                return "UNKNOWN";
            }
        }
    }

    public static String feedbackTypeToString(int v) {
        StringBuilder stringBuilder0 = new StringBuilder();
        String s = "[";
    alab1:
        while(true) {
            stringBuilder0.append(s);
        alab2:
            while(true) {
                if(v <= 0) {
                    break alab1;
                }
                int v1 = 1 << Integer.numberOfTrailingZeros(v);
                v &= v1;
                if(stringBuilder0.length() > 1) {
                    stringBuilder0.append(", ");
                }
                switch(v1) {
                    case 1: {
                        s = "FEEDBACK_SPOKEN";
                        break alab2;
                    }
                    case 4: {
                        s = "FEEDBACK_AUDIBLE";
                        break alab2;
                    }
                    case 8: {
                        s = "FEEDBACK_VISUAL";
                        break alab2;
                    }
                    case 16: {
                        break;
                    }
                    default: {
                        if(v1 != 2) {
                            continue;
                        }
                        s = "FEEDBACK_HAPTIC";
                        break alab2;
                    }
                }
                s = "FEEDBACK_GENERIC";
                break;
            }
        }
        stringBuilder0.append("]");
        return stringBuilder0.toString();
    }

    public static String flagToString(int v) {
        switch(v) {
            case 1: {
                return "DEFAULT";
            }
            case 2: {
                return "FLAG_INCLUDE_NOT_IMPORTANT_VIEWS";
            }
            case 4: {
                return "FLAG_REQUEST_TOUCH_EXPLORATION_MODE";
            }
            case 8: {
                return "FLAG_REQUEST_ENHANCED_WEB_ACCESSIBILITY";
            }
            case 16: {
                return "FLAG_REPORT_VIEW_IDS";
            }
            case 0x20: {
                return "FLAG_REQUEST_FILTER_KEY_EVENTS";
            }
            default: {
                return null;
            }
        }
    }

    public static int getCapabilities(AccessibilityServiceInfo accessibilityServiceInfo0) {
        return accessibilityServiceInfo0.getCapabilities();
    }

    public static String loadDescription(AccessibilityServiceInfo accessibilityServiceInfo0, PackageManager packageManager0) {
        return accessibilityServiceInfo0.loadDescription(packageManager0);
    }
}


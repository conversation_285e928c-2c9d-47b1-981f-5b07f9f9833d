package a0;

import android.app.ActivityManager;
import android.app.ApplicationExitInfo;
import android.os.ext.SdkExtensions;
import android.view.Window;
import java.util.List;

public abstract class a {
    public static int B() {
        return 8;
    }

    public static int a() {
        return SdkExtensions.getExtensionVersion(1000000);
    }

    public static ApplicationExitInfo e(Object object0) [...] // Inlined contents

    public static List l(ActivityManager activityManager0) {
        return activityManager0.getHistoricalProcessExitReasons(null, 0, 0);
    }

    public static void p(Window window0) {
        window0.setDecorFitsSystemWindows(true);
    }

    public static int s() {
        return SdkExtensions.getExtensionVersion(30);
    }

    public static int x() {
        return SdkExtensions.getExtensionVersion(0x1F);
    }
}


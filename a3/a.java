package a3;

import android.app.Notification.Builder;
import android.app.Notification.DecoratedCustomViewStyle;
import android.app.Notification.MessagingStyle.Message;
import android.app.Notification.MessagingStyle;
import android.os.LocaleList;
import android.view.FrameMetrics;
import android.widget.RemoteViews;
import java.util.Locale;

public abstract class a {
    public static long A(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(7);
    }

    public static long B(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(6);
    }

    public static long C(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(0);
    }

    public static long D(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(2);
    }

    public static long b(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(8);
    }

    public static Notification.DecoratedCustomViewStyle d() {
        return new Notification.DecoratedCustomViewStyle();
    }

    public static Notification.MessagingStyle.Message e(CharSequence charSequence0, long v, CharSequence charSequence1) {
        return new Notification.MessagingStyle.Message(charSequence0, v, charSequence1);
    }

    public static Notification.MessagingStyle f(CharSequence charSequence0) {
        return new Notification.MessagingStyle(charSequence0);
    }

    public static Locale h(LocaleList localeList0) {
        return localeList0.get(0);
    }

    public static void i() {
    }

    public static void k(Notification.Builder notification$Builder0, RemoteViews remoteViews0) {
        notification$Builder0.setCustomContentView(remoteViews0);
    }

    public static void l(Notification.Builder notification$Builder0, CharSequence[] arr_charSequence) {
        notification$Builder0.setRemoteInputHistory(arr_charSequence);
    }

    public static long t(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(1);
    }

    public static void u() {
    }

    public static void v(Notification.Builder notification$Builder0, RemoteViews remoteViews0) {
        notification$Builder0.setCustomBigContentView(remoteViews0);
    }

    public static long w(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(3);
    }

    public static void x(Notification.Builder notification$Builder0, RemoteViews remoteViews0) {
        notification$Builder0.setCustomHeadsUpContentView(remoteViews0);
    }

    public static long y(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(4);
    }

    public static long z(FrameMetrics frameMetrics0) {
        return frameMetrics0.getMetric(5);
    }
}


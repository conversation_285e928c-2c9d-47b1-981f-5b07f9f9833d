package a2;

import android.view.View;
import android.view.ViewParent;
import androidx.core.view.accessibility.AccessibilityViewCommand;
import com.google.android.material.sidesheet.SideSheetBehavior;
import q.b;

public final class a implements AccessibilityViewCommand {
    public final SideSheetBehavior a;
    public final int b;

    public a(SideSheetBehavior sideSheetBehavior0, int v) {
        this.a = sideSheetBehavior0;
        this.b = v;
    }

    @Override  // androidx.core.view.accessibility.AccessibilityViewCommand
    public final boolean a(View view0) {
        SideSheetBehavior sideSheetBehavior0 = this.a;
        sideSheetBehavior0.getClass();
        int v = this.b;
        if(v == 1 || v == 2) {
            throw new IllegalArgumentException(a.a.s(new StringBuilder("STATE_"), (v == 1 ? "DRAGGING" : "SETTLING"), " should not be set externally."));
        }
        if(sideSheetBehavior0.p != null && sideSheetBehavior0.p.get() != null) {
            View view1 = (View)sideSheetBehavior0.p.get();
            b b0 = new b(v, 1, sideSheetBehavior0);
            ViewParent viewParent0 = view1.getParent();
            if(viewParent0 != null && viewParent0.isLayoutRequested() && view1.isAttachedToWindow()) {
                view1.post(b0);
                return true;
            }
            b0.run();
            return true;
        }
        sideSheetBehavior0.s(v);
        return true;
    }
}

